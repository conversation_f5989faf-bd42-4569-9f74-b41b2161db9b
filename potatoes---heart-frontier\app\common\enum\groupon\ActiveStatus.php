<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------

declare (strict_types=1);

namespace app\common\enum\groupon;

use app\common\enum\EnumBasics;

/**
 * 枚举类：拼团活动状态
 * Class ActiveStatus
 * @package app\common\enum\groupon
 */
class ActiveStatus extends EnumBasics
{
    // 已开始
    const STATE_BEGIN = 10;

    // 未开始
    const STATE_SOON = 20;

    // 已结束
    const STATE_END = 30;

    /**
     * 获取枚举类型值
     * @return array
     */
    public static function data(): array
    {
        return [
            self::STATE_BEGIN => [
                'name' => '已开始',
                'value' => self::STATE_BEGIN
            ],
            self::STATE_SOON => [
                'name' => '未开始',
                'value' => self::STATE_SOON
            ],
            self::STATE_END => [
                'name' => '已结束',
                'value' => self::STATE_END
            ]
        ];
    }
}