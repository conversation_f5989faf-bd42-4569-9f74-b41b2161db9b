<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\admin\service\store;

use app\common\service\store\User as Basics;
use app\admin\model\store\User as StoreUserModel;

/**
 * 超管用户服务类
 * Class User
 */
class User extends Basics
{
    /**
     * 获取指定商城的管理员用户信息
     * @param int $storeId
     * @return StoreUserModel|array|null
     */
    public static function getUserInfoByStoreId(int $storeId)
    {
        return StoreUserModel::detail([
            ['store_id', '=', $storeId],
            ['is_super', '=', 1]
        ]);
    }
}
