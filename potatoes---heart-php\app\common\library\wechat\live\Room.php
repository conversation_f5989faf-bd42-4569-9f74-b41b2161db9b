<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\library\wechat\live;

use app\common\library\wechat\WxBase;
use cores\exception\BaseException;

/**
 * 微信小程序直播接口
 * Class Room
 * @package app\common\library\wechat\live
 */
class Room extends WxBase
{
    /**
     * 微信小程序直播-获取直播房间列表接口
     * api文档: https://developers.weixin.qq.com/miniprogram/dev/framework/liveplayer/live-player-plugin.html
     * @throws BaseException
     */
    public function getLiveRoomList()
    {
        // 微信接口url
        $accessToken = $this->getAccessToken();
        $apiUrl = "https://api.weixin.qq.com/wxa/business/getliveinfo?access_token={$accessToken}";
        // 请求参数
        $params = $this->jsonEncode(['start' => 0, 'limit' => 100]);
        // 执行请求
        $result = $this->post($apiUrl, $params);
        // 记录日志
        log_record(['name' => '微信小程序直播-获取直播房间列表接口', 'url' => $apiUrl, 'params' => $params, 'result' => $result]);
        // 返回结果
        $response = $this->jsonDecode($result);
        if (!isset($response['errcode'])) {
            $this->error = 'not found errcode';
            return false;
        }
        // 容错: empty room list
        if ($response['errcode'] > 1 && $response['errcode'] != 9410000) {
            $this->error = $response['errmsg'];
            return false;
        }
        return $response;
    }
}