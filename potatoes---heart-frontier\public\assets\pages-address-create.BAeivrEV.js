import{a as e,_ as a,b as o}from"./u-form.B7HHrbys.js";import{a1 as t,u as l,c as r,w as s,n,i,o as d,a as m,f as u,b as p,k as f}from"./index-BI5vpG2u.js";import{r as c}from"./uni-app.es.CVyVeKL7.js";import{_ as g}from"./select-region.BBgVGens.js";import{a as h}from"./verify.CkNGx45Y.js";import{a as b,b as _}from"./address.BjxyamVC.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./u-icon.D0JFGKqy.js";import"./emitter.DrjJCwnj.js";import"./u-loading.DmE6S-wz.js";import"./u-popup.bJE3OMB4.js";import"./u-mask.DE5a1hRC.js";const V={name:[{required:!0,message:"请输入姓名",trigger:["blur","change"]}],phone:[{required:!0,message:"请输入手机号",trigger:["blur","change"]},{validator:(e,a,o)=>h(a),message:"手机号码不正确",trigger:["blur"]}],region:[{required:!0,message:"请选择省市区",trigger:["blur","change"],type:"array"}],detail:[{required:!0,message:"请输入详细地址",trigger:["blur","change"]}]};const j=y({components:{SelectRegion:g},data:()=>({form:{content:"",name:"",phone:"",region:[],detail:""},rules:V,disabled:!1}),onLoad(e){},onReady(){this.$refs.uForm.setRules(this.rules)},methods:{handleAnalysis(){const e=this;b(e.form.content).then((a=>{const o=a.data.detail;e.createFormData(o)}))},openAddress(){const{form:e,$refs:a}=this;t.openAddress().then((o=>{alert(JSON.stringify(o));const t=a.sRegion.getOptionItemByNames(o);e.name=o.userName,e.phone=o.telNumber,e.detail=o.detailInfo,e.region=t.length>0?t:[]}))},createFormData(e){const{form:a}=this;a.name=e.name,a.phone=e.phone,a.detail=e.detail,a.region=this.createRegion(e)},createRegion(e){return 0==e.province_id||0==e.city_id||0==e.region_id?(this.$toast("很抱歉，地区未能识别请手动选择",2e3),[]):[{label:e.region.province,value:e.province_id},{label:e.region.city,value:e.city_id},{label:e.region.region,value:e.region_id}]},handleSubmit(){const e=this;if(e.disabled)return!1;e.$refs.uForm.validate((a=>{a&&(e.disabled=!0,_(e.form).then((a=>{e.$toast(a.message),uni.navigateBack()})).finally((()=>e.disabled=!1)))}))}}},[["render",function(t,h,b,_,y,V){const j=c(l("u-input"),e),v=c(l("u-form-item"),a),k=i,x=c(l("select-region"),g),w=c(l("u-form"),o);return d(),r(k,{class:"container",style:n(t.appThemeStyle)},{default:s((()=>[m(k,{class:"form-analysis form-wrapper"},{default:s((()=>[m(v,{prop:"name","border-bottom":!1},{default:s((()=>[m(j,{modelValue:y.form.content,"onUpdate:modelValue":h[0]||(h[0]=e=>y.form.content=e),type:"textarea",placeholder:"粘贴地址信息，自动解析姓名、电话和地址","custom-style":{height:"150rpx"},"auto-height":!1},null,8,["modelValue"])])),_:1}),m(k,{class:"analysis-foot clearfix"},{default:s((()=>[m(k,{class:"analysis-btn",onClick:h[1]||(h[1]=e=>V.handleAnalysis())},{default:s((()=>[u("智能识别")])),_:1})])),_:1})])),_:1}),m(k,{class:"page-title"},{default:s((()=>[u("收货地址")])),_:1}),m(k,{class:"form-wrapper"},{default:s((()=>[m(w,{model:y.form,ref:"uForm","label-width":"140rpx"},{default:s((()=>[m(v,{label:"姓名",prop:"name"},{default:s((()=>[m(j,{modelValue:y.form.name,"onUpdate:modelValue":h[2]||(h[2]=e=>y.form.name=e),placeholder:"请输入收货人姓名"},null,8,["modelValue"])])),_:1}),m(v,{label:"电话",prop:"phone"},{default:s((()=>[m(j,{modelValue:y.form.phone,"onUpdate:modelValue":h[3]||(h[3]=e=>y.form.phone=e),placeholder:"请输入收货人手机号"},null,8,["modelValue"])])),_:1}),m(v,{label:"地区",prop:"region"},{default:s((()=>[m(x,{ref:"sRegion",modelValue:y.form.region,"onUpdate:modelValue":h[4]||(h[4]=e=>y.form.region=e)},null,8,["modelValue"])])),_:1}),m(v,{label:"详细地址",prop:"detail","border-bottom":!1},{default:s((()=>[m(j,{modelValue:y.form.detail,"onUpdate:modelValue":h[5]||(h[5]=e=>y.form.detail=e),placeholder:"街道门牌、楼层等信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1}),m(k,{class:"footer"},{default:s((()=>[m(k,{class:"btn-wrapper"},{default:s((()=>["WXOFFICIAL"===t.platform?(d(),r(k,{key:0,class:"btn-item btn-item-wechat",onClick:h[6]||(h[6]=e=>V.openAddress())},{default:s((()=>[u("选择微信收货地址")])),_:1})):p("",!0),m(k,{class:f(["btn-item btn-item-main",{disabled:y.disabled}]),onClick:h[7]||(h[7]=e=>V.handleSubmit())},{default:s((()=>[u("保存")])),_:1},8,["class"])])),_:1})])),_:1})])),_:1},8,["style"])}],["__scopeId","data-v-6fa04682"]]);export{j as default};
