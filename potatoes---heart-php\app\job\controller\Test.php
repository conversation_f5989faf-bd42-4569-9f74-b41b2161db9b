<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\job\controller;

use cores\BaseJob;
use cores\traits\QueueTrait;

/**
 * 队列任务：测试队列服务
 * Class Test
 * @package app\job\controller
 */
class Test extends BaseJob
{
    use QueueTrait;

    /**
     * 消费队列任务：输出测试信息
     * @param array $data 参数
     * @return bool 返回结果
     */
    public function handle(array $data): bool
    {
        echo "---- test ---- \n";
        return true;
    }
}