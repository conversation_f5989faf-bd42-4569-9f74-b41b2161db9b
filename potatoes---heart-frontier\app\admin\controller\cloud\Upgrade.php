<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\admin\controller\cloud;

use think\response\Json;
use app\admin\controller\Controller;
use app\admin\service\cloud\Upgrade as UpgradeService;
use cores\exception\BaseException;

/**
 * 在线更新升级
 */
class Upgrade extends Controller
{
    /**
     * 获取更新信息
     * @return Json
     * @throws BaseException
     */
    public function info(): Json
    {
        $service = new UpgradeService;
        return $this->renderSuccess($service->getInfo());
    }

    /**
     * 下载指定的版本模块
     * @param int $versionId 版本ID
     * @param string $module 模块key
     * @return Json
     * @throws BaseException
     */
    public function download(int $versionId, string $module): Json
    {
        $service = new UpgradeService;
        $url = $service->download($versionId, $module);
        return $this->renderSuccess(compact('url'));
    }

    /**
     * 下载指定的版本模块
     * @param int $versionId 版本ID
     * @return Json
     * @throws BaseException
     */
    public function update(int $versionId): Json
    {
        $service = new UpgradeService;
        if (!$service->update($versionId)) {
            return $this->renderError('一键更新失败');
        }
        return $this->renderSuccess('一键更新成功');
    }
}