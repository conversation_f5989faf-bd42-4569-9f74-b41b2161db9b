<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\admin\model;

use app\admin\model\Page as PageModel;
use app\common\model\Store as StoreModel;
use app\admin\model\store\User as StoreUserModel;

/**
 * 商家记录表模型
 * Class Store
 * @package app\admin\model
 */
class Store extends StoreModel
{
    /**
     * 获取列表数据
     * @param bool $isRecycle
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getList(bool $isRecycle = false): \think\Paginator
    {
        return $this->where('is_recycle', '=', (int)$isRecycle)
            ->where('is_delete', '=', 0)
            ->order(['sort' => 'asc', 'create_time' => 'desc'])
            ->paginate(15);
    }

    /**
     * 新增记录
     * @param array $data
     * @return bool|mixed
     */
    public function add(array $data)
    {
        if ($data['password'] !== $data['password_confirm']) {
            $this->error = '确认密码不正确';
            return false;
        }
        if (StoreUserModel::checkExist($data['user_name'])) {
            $this->error = '商家用户名已存在';
            return false;
        }
        return $this->transaction(function () use ($data) {
            // 添加小程序记录
            $data['is_recycle'] = 0;
            if ($status = $this->save($data)) {
                // 新增商家用户信息
                (new StoreUserModel)->add((int)$this['store_id'], $data);
                // 新增默认首页数据
                (new PageModel)->insertDefault((int)$this['store_id']);
            }
            return $status;
        });
    }

    /**
     * 移入移出回收站
     * @param bool $isRecycle
     * @return bool|false
     */
    public function recycle(bool $isRecycle = true): bool
    {
        return $this->save(['is_recycle' => (int)$isRecycle]);
    }

    /**
     * 软删除
     * @return false|int
     */
    public function setDelete()
    {
        return $this->transaction(function () {
            // 删除商家用户信息
            StoreUserModel::setDelete($this['store_id']);
            // 设置当前商城为已删除
            return $this->save(['is_delete' => 1]);
        });
    }
}
