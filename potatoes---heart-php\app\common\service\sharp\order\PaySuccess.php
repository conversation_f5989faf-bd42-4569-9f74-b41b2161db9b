<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\service\sharp\order;

use app\common\model\Order as OrderModel;
use app\common\model\sharp\ActiveGoods as ActiveGoodsModel;
use app\common\service\BaseService;

/**
 * 砍价订单支付成功后的回调
 * Class PaySuccess
 * @package app\common\service\sharp\order
 */
class PaySuccess extends BaseService
{
    /**
     * 回调方法
     * @param OrderModel $order
     * @return bool
     */
    public function onPaySuccess(OrderModel $order): bool
    {
        // 更新活动会场的商品实际销量
        $activeTimeId = $order['order_source_id'];
        return $this->updateActiveGoodsAales($activeTimeId, $order['goods']);
    }

    /**
     * 更新活动会场的商品实际销量
     * @param int $activeTimeId
     * @param $goodsList
     * @return bool
     */
    private function updateActiveGoodsAales(int $activeTimeId, $goodsList): bool
    {
        // 整理更新数据
        $data = [];
        foreach ($goodsList as $goods) {
            $data[] = [
                'data' => ['sales_actual' => ['inc', $goods['total_num']]],
                'where' => [
                    'active_time_id' => $activeTimeId,
                    'sharp_goods_id' => $goods['goods_source_id'],
                ],
            ];
        }
        // 执行批量更新
        (new ActiveGoodsModel)->updateAll($data);
        return true;
    }
}