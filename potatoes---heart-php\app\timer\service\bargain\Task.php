<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\timer\service\bargain;

use app\common\library\helper;
use app\timer\library\Tools;
use app\timer\model\bargain\Task as TaskModel;
use app\common\service\BaseService;

/**
 * 服务类：砍价任务
 * Class Task
 * @package app\timer\service\bargain
 */
class Task extends BaseService
{
    /**
     * 标记已过期的砍价任务
     * @param int $storeId
     * @return bool
     */
    public function overdueEvent(int $storeId): bool
    {
        // 获取已失效的订单ID集
        $model = new TaskModel;
        $endTaskIds = $model->getEndTaskIds($storeId);
        // 记录日志
        Tools::taskLogs('BargainTask', 'overdueEvent', [
            'storeId' => $storeId,
            'endTaskIds' => helper::jsonEncode($endTaskIds)
        ]);
        // 标记订单失效状态
        return empty($endTaskIds) || $model->setIsEnd($endTaskIds);
    }
}