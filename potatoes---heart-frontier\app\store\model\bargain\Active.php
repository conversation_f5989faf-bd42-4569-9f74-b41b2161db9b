<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\bargain;

use app\common\model\bargain\Active as ActiveModel;

/**
 * 砍价活动模型
 * Class Active
 * @package app\store\model\bargain
 */
class Active extends ActiveModel
{
    /**
     * 获取列表数据
     * @param string $search
     * @return mixed|\think\Paginator
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getList(string $search = '')
    {
        // 设置基础查询条件
        $query = $this->setBaseQuery($this->alias, [
            ['goods', 'goods_id'],
        ]);
        // 检索查询条件
        !empty($search) && $query->where('goods.goods_name', 'like', "%{$search}%");
        // 获取活动列表
        $list = $query->where("{$this->alias}.is_delete", '=', 0)
            ->order(["{$this->alias}.sort" => 'asc', "{$this->alias}.create_time" => 'desc'])
            ->paginate(15);
        // 设置商品数据
        return !$list->isEmpty() ? $this->setGoodsListData($list, true) : $list;
    }

    /**
     * 新增记录
     * @param array $data
     * @return bool
     */
    public function add(array $data): bool
    {
        if (!$this->onValidate($data, 'add')) {
            return false;
        }
        $data = $this->createData($data);
        return $this->save($data) !== false;
    }

    /**
     * 更新记录
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
        if (!$this->onValidate($data, 'edit')) {
            return false;
        }
        $data = $this->createData($data);
        return $this->save($data) !== false;
    }

    /**
     * 生成欲写入的数据
     * @param array $data
     * @return array
     */
    private function createData(array $data): array
    {
        // 格式化活动时间
        $times = between_time($data['betweenTime'], true);
        $data['start_time'] = $times['start_time'];
        $data['end_time'] = $times['end_time'];
        $data['store_id'] = static::$storeId;
        return $data;
    }

    /**
     * 表单验证
     * @param array $data
     * @param string $scene
     * @return bool
     */
    private function onValidate(array $data, string $scene = 'add'): bool
    {
        if ($scene === 'add') {
            if (!isset($data['goods_id']) || empty($data['goods_id'])) {
                $this->error = '请选择商品';
                return false;
            }
        }
        // 验证活动时间
        if (empty($data['betweenTime']) || !count($data['betweenTime'])) {
            $this->error = '请选择活动的开始时间与截止时间';
            return false;
        }
        // 格式化活动时间
        $times = between_time($data['betweenTime']);
        if ($times['end_time'] <= $times['start_time']) {
            $this->error = '活动结束时间必须大于开始时间';
            return false;
        }
        return true;
    }

    /**
     * 软删除
     * @return bool|false
     */
    public function setDelete(): bool
    {
        return $this->save(['is_delete' => 1]);
    }

    /**
     * 商品ID是否存在
     * @param int $goodsId
     * @return bool
     */
    public static function isExistGoodsId(int $goodsId): bool
    {
        return (bool)(new static)->where('goods_id', '=', $goodsId)
            ->where('is_delete', '=', 0)
            ->value('active_id');
    }
}