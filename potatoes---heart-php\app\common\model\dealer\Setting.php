<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\dealer;

use cores\BaseModel;
use think\facade\Cache;
use app\common\library\helper;
use app\common\enum\dealer\withdraw\PayType as PayTypeEnum;
use app\common\enum\dealer\apply\ApplyType as ApplyTypeEnum;

/**
 * 分销商设置模型
 * Class Apply
 * @package app\common\model\dealer
 */
class Setting extends BaseModel
{
    // 定义表名
    protected $name = 'dealer_setting';

    protected $createTime = false;

    /**
     * 获取器: 转义数组格式
     * @param $value
     * @return array
     */
    public function getValuesAttr($value): array
    {
        return helper::jsonDecode($value);
    }

    /**
     * 修改器: 转义成json格式
     * @param $value
     * @return string
     */
    public function setValuesAttr($value): string
    {
        return helper::jsonEncode($value);
    }

    /**
     * 获取指定项设置
     * @param string $key
     * @param int|null $storeId
     * @return array|mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getItem(string $key, int $storeId = null)
    {
        $data = static::getAll($storeId);
        return $data[$key] ?? [];
    }

    /**
     * 获取分销商设置
     * @param int|null $storeId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getAll(int $storeId = null): array
    {
        $model = new static;
        is_null($storeId) && $storeId = 10001;
        if (!$data = Cache::get("dealer_setting_{$storeId}")) {
            // 获取全部设置列表
            $setting = $model->where('store_id', '=', $storeId)->select();
            // 格式化
            $data = $setting->isEmpty() ? [] : helper::arrayColumn2Key($setting->toArray(), 'key');
            // 写入缓存
            Cache::tag('cache')->set("dealer_setting_{$storeId}", $data);
        }
        // 重组setting缓存数据 (多维)
        $mixed = static::reorganize($model->defaultData(), $data, $type = 'app', true);
        return static::getValues($mixed, true);
    }

    /**
     * 获取设置项信息
     * @param string $key
     * @return static|array|null
     */
    public static function detail(string $key)
    {
        return static::get(compact('key'));
    }

    /**
     * 是否已开启分销功能
     * @param int|null $storeId
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function isEnabled(?int $storeId = null): bool
    {
        return (bool)self::getBasic($storeId)['is_open'];
    }

    /**
     * 获取基础设置
     * @param int|null $storeId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getBasic(?int $storeId = null): array
    {
        return (array)static::getItem('basic', $storeId);
    }

    /**
     * 分销中心页面名称
     * @param int|null $storeId
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getDealerTitle(int $storeId = null)
    {
        return static::getItem('words', $storeId)['index']['title']['value'];
    }

    /**
     * 默认配置
     * @return array
     */
    public function defaultData(): array
    {
        return [
            'basic' => [
                'key' => 'basic',
                'describe' => '基础设置',
                'values' => [
                    // 是否开启分销功能
                    'is_open' => 0,   // 参数值：1开启 0关闭
                    // 分销层级
                    'level' => 3, // 参数值：1一级 2二级 3三级
                    // 分销商内购
                    'self_buy' => 0   // 参数值：1开启 0关闭
                ],
            ],
            'condition' => [
                'key' => 'condition',
                'describe' => '分销商条件',
                'values' => [
                    // 成为分销商条件
                    'become' => ApplyTypeEnum::AUDIT,   // 参数值：10填写申请信息(需后台审核) 20填写申请信息(无需审核)
                    // 购买指定商品成为分销商 0关闭 1开启
                    'becomeBuyGoods' => 0,
                    // 购买指定商品的id集
                    'becomeBuyGoodsIds' => [],
                    // 成为下线条件
                    'downline' => 10,  // 参数值：10首次点击分享链接
                ]
            ],
            'commission' => [
                'key' => 'commission',
                'describe' => '佣金设置',
                'values' => [
                    // 一级佣金
                    'first_money' => 0,
                    // 二级佣金
                    'second_money' => 0,
                    // 三级佣金
                    'third_money' => 0,
                ]
            ],
            'settlement' => [
                'key' => 'settlement',
                'describe' => '结算',
                'values' => [
                    // 提现方式
                    'pay_type' => [PayTypeEnum::ALIPAY, PayTypeEnum::BANK_CARD],   // 参数值：10微信支付 20支付宝支付 30银行卡支付
                    // 最低提现额度
                    'min_money' => 10.00,
                    // 佣金结算天数
                    'settle_days' => 10,
                ]
            ],
            'words' => [
                'key' => 'words',
                'describe' => '自定义文字',
                'values' => [
                    'index' => [
                        'title' => [
                            'default' => '分销中心',
                            'value' => '分销中心'
                        ],
                        'words' => [
                            'not_dealer' => [
                                'default' => '您还不是分销商，请先提交申请',
                                'value' => '您还不是分销商，请先提交申请'
                            ],
                            'apply_now' => [
                                'default' => '立即加入',
                                'value' => '立即加入'
                            ],
                            'referee' => [
                                'default' => '推荐人',
                                'value' => '推荐人'
                            ],
                            'money' => [
                                'default' => '可提现佣金',
                                'value' => '可提现'
                            ],
                            'freeze_money' => [
                                'default' => '待提现佣金',
                                'value' => '待提现'
                            ],
                            'total_money' => [
                                'default' => '已提现金额',
                                'value' => '已提现金额'
                            ],
                            'withdraw' => [
                                'default' => '去提现',
                                'value' => '去提现'
                            ],
                        ]
                    ],
                    'apply' => [
                        'title' => [
                            'default' => '申请成为分销商',
                            'value' => '申请成为分销商'
                        ],
                        'words' => [
                            'title' => [
                                'default' => '请填写申请信息',
                                'value' => '请填写申请信息'
                            ],
                            'license' => [
                                'default' => '分销商申请协议',
                                'value' => '分销商申请协议'
                            ],
                            'submit' => [
                                'default' => '申请成为经销商',
                                'value' => '申请成为经销商'
                            ],
                            'wait_audit' => [
                                'default' => '您的申请已受理，正在进行信息核验，请耐心等待。',
                                'value' => '您的申请已受理，正在进行信息核验，请耐心等待。'
                            ],
                            'goto_mall' => [
                                'default' => '去商城逛逛',
                                'value' => '去商城逛逛'
                            ],
                        ]
                    ],
                    'order' => [
                        'title' => [
                            'default' => '分销订单',
                            'value' => '分销订单'
                        ],
                        'words' => [
                            'all' => [
                                'default' => '全部',
                                'value' => '全部'
                            ],
                            'unsettled' => [
                                'default' => '未结算',
                                'value' => '未结算'
                            ],
                            'settled' => [
                                'default' => '已结算',
                                'value' => '已结算'
                            ],
                        ]
                    ],
                    'team' => [
                        'title' => [
                            'default' => '我的团队',
                            'value' => '我的团队'
                        ],
                        'words' => [
                            'total_team' => [
                                'default' => '团队总人数',
                                'value' => '团队总人数'
                            ],
                            'first' => [
                                'default' => '一级团队',
                                'value' => '一级团队'
                            ],
                            'second' => [
                                'default' => '二级团队',
                                'value' => '二级团队'
                            ],
                            'third' => [
                                'default' => '三级团队',
                                'value' => '三级团队'
                            ],
                        ]
                    ],
                    'withdraw_list' => [
                        'title' => [
                            'default' => '提现明细',
                            'value' => '提现明细'
                        ],
                        'words' => [
                            'all' => [
                                'default' => '全部',
                                'value' => '全部'
                            ],
                            'apply_10' => [
                                'default' => '审核中',
                                'value' => '审核中'
                            ],
                            'apply_20' => [
                                'default' => '审核通过',
                                'value' => '审核通过'
                            ],
                            'apply_40' => [
                                'default' => '已打款',
                                'value' => '已打款'
                            ],
                            'apply_30' => [
                                'default' => '驳回',
                                'value' => '驳回'
                            ],
                        ]
                    ],
                    'withdraw_apply' => [
                        'title' => [
                            'default' => '申请提现',
                            'value' => '申请提现'
                        ],
                        'words' => [
                            'capital' => [
                                'default' => '可提现佣金',
                                'value' => '可提现佣金'
                            ],
                            'money' => [
                                'default' => '提现金额',
                                'value' => '提现金额'
                            ],
                            'money_placeholder' => [
                                'default' => '请输入要提取的金额',
                                'value' => '请输入要提取的金额'
                            ],
                            'min_money' => [
                                'default' => '最低提现佣金',
                                'value' => '最低提现佣金'
                            ],
                            'submit' => [
                                'default' => '提交申请',
                                'value' => '提交申请'
                            ],
                        ]
                    ],
                    'poster' => [
                        'title' => [
                            'default' => '推广二维码',
                            'value' => '推广二维码'
                        ]
                    ],
                ]
            ],
            'license' => [
                'key' => 'license',
                'describe' => '申请协议',
                'values' => [
                    'license' => "1. xxxxxxx\r\n2. xxxxxxx\r\n3. xxxxxxx"
                ]
            ],
            'background' => [
                'key' => 'background',
                'describe' => '页面背景图',
                'values' => [
                    // 分销中心首页
                    'index' => base_url() . 'assets/store/img/dealer/dealer-bg.png',
                    // 申请成为分销商页
                    'apply' => base_url() . 'assets/store/img/dealer/dealer-bg.png',
                    // 申请提现页
                    'withdraw_apply' => base_url() . 'assets/store/img/dealer/dealer-bg.png',
                ],
            ],
            'poster' => [
                'key' => 'poster',
                'describe' => '分销海报',
                'values' => [
                    'backdrop' => [
                        'src' => base_url() . 'assets/store/img/dealer/backdrop.png',
                    ],
                    'nickName' => [
                        'fontSize' => 12,
                        'color' => '#000000',
                        'left' => 123,
                        'top' => 476,
                    ],
                    'avatar' => [
                        'width' => 50,
                        'style' => 'circle',
                        'left' => 62,
                        'top' => 466,
                        'src' => base_url() . 'assets/store/img/dealer/avatar.png',
                    ],
                    'qrcode' => [
                        'width' => 66,
                        // 'style' => 'circle',
                        'left' => 249,
                        'top' => 456,
                        'src' => base_url() . 'assets/store/img/dealer/qrcode.png',
                    ]
                ],
            ]
        ];
    }
}