<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\dealer\withdraw;

use app\common\enum\EnumBasics;

/**
 * 枚举类：分销商体现打款方式
 * Class PayType
 * @package app\common\enum\dealer\withdraw
 */
class PayType extends EnumBasics
{
    // 微信
    const WECHAT = 10;

    // 支付宝
    const ALIPAY = 20;

    // 银行卡
    const BANK_CARD = 30;

    /**
     * 获取枚举类型值
     * @return array
     */
    public static function data(): array
    {
        return [
            self::WECHAT => [
                'name' => '待审核',
                'value' => self::WECHAT,
            ],
            self::ALIPAY => [
                'name' => '审核通过',
                'value' => self::ALIPAY,
            ],
            self::BANK_CARD => [
                'name' => '驳回',
                'value' => self::BANK_CARD,
            ]
        ];
    }
}