import{p as e,N as r}from"./index-BI5vpG2u.js";import{C as t,P as o}from"./Client.Dpks1FFV.js";const a=e=>{const t={formHtml:"",...e};return r.set("tempUnifyData_"+t.orderKey,{method:o.ALIPAY.value,outTradeNo:t.out_trade_no},3600),new Promise(((e,r)=>{if(t.formHtml){const e=document.createElement("div");e.innerHTML=t.formHtml,document.body.appendChild(e),document.forms[0].submit()}}))},n=e=>new Promise(((r,t)=>{uni.requestPayment({provider:"alipay",orderInfo:e.orderInfo,success(t){const o={isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"alipay"};r({res:t,option:o})},fail:e=>t(e)})})),i=e=>new Promise(((r,t)=>{uni.requestPayment({provider:"alipay",orderInfo:e.orderInfo,success(t){const o={isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"alipay"};r({res:t,option:o})},fail:e=>t(e)})})),s=r=>({[t.H5.value]:a,[t.APP.value]:n,[t.MP_ALIPAY.value]:i}[e](r)),p=()=>{const e={};return e.returnUrl=window.location.href,e},u=e=>{const r={timeStamp:"",nonceStr:"",package:"",signType:"",paySign:"",...e};return new Promise(((e,t)=>{uni.requestPayment({provider:"wxpay",timeStamp:r.timeStamp,nonceStr:r.nonceStr,package:r.package,signType:r.signType,paySign:r.paySign,success(t){const o={isRequireQuery:!0,outTradeNo:r.out_trade_no,method:"wechat"};e({res:t,option:o})},fail:e=>t(e)})}))},d=e=>{const t={orderKey:null,mweb_url:"",h5_url:"",...e};return r.set("tempUnifyData_"+t.orderKey,{method:o.WECHAT.value,outTradeNo:t.out_trade_no},3600),new Promise(((e,r)=>{const o=t.mweb_url||t.h5_url;o&&(window.location.href=o)}))},m=e=>{const r={appId:"",timeStamp:"",nonceStr:"",package:"",signType:"",paySign:"",...e};return y(r)},c=e=>new Promise(((r,t)=>{uni.requestPayment({provider:"wxpay",orderInfo:{partnerid:e.partnerid,appid:e.appid,package:"Sign=WXPay",noncestr:e.noncestr,sign:e.sign,prepayid:e.prepayid,timestamp:e.timestamp},success(t){r({res:t,option:{isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"wechat"}})},fail:e=>t(e)})})),y=e=>new Promise(((r,t)=>{WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:e.appId,timeStamp:e.timeStamp,nonceStr:e.nonceStr,package:e.package,signType:e.signType,paySign:e.paySign},(o=>{if("get_brand_wcpay_request:ok"==o.err_msg){const t={isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"wechat"};r({res:o,option:t})}else t(o)}))})),l=r=>({[t.H5.value]:d,[t.MP_WEIXIN.value]:u,[t.WXOFFICIAL.value]:m,[t.APP.value]:c}[e](r)),g=()=>({});export{g as a,l as b,p as e,s as p};
