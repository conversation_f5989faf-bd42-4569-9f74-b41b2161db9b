<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\store\shop;

use app\common\model\store\shop\Order as OrderModel;

/**
 * 商家门店核销订单记录模型
 * Class Order
 * @package app\store\model\store\shop
 */
class Order extends OrderModel
{
    /**
     * 获取列表数据
     * @param array $param
     * @return iterable|\think\model\Collection|\think\Paginator
     */
    public function getList(array $param = [])
    {
        // 设置默认查询条件
        $params = $this->setQueryDefaultValue($param, [
            'shop_id' => 0,     // 门店ID
            'search' => '',     // 搜索关键词: 店员姓名/手机号
        ]);
        // 检索查询条件
        $filter = [];
        // 门店ID
        $params['shop_id'] > 0 && $filter[] = ['main.shop_id', '=', (int)$params['shop_id']];
        // 搜索关键词
        !empty($params['search']) && $filter[] = ['clerk.real_name', 'like', "%{$params['search']}%"];
        // 查询列表数据
        $list = $this->alias('main')
            ->field(['main.*'])
            ->where($filter)
            ->join('store_shop_clerk clerk', 'clerk.clerk_id = main.clerk_id')
            ->order(['main.create_time' => 'desc'])
            ->paginate(15);
        // 加载订单关联数据
        return static::preload($list, ['shop', 'clerk', 'order']);
    }
}
