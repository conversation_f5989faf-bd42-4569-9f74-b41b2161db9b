<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\api\controller;

use app\api\model\User as UserModel;
use app\common\library\payment\Facade as PaymentFacade;
use app\common\model\dealer\Setting as SettingModel;
use app\common\model\user\PointsLog as PointsLogModel;
use app\store\model\Payment as PaymentModel;
use think\facade\Db;

/**
 * 默认控制器
 * Class Index
 * @package app\api\controller
 */
class Index
{
    public function index()
    {
        echo '当前访问的index.php，请将index.html设为默认站点入口';
    }

    //会员到期提醒
    public function message()
    {
        $UserModel = new UserModel;

        $users = $UserModel->getVipList();

        foreach ($users as $key => $value) {
            $data['user_id'] = $value['user_id'];

            $data['create_time'] = time();
            $data['store_id']    = 10001;
            $data['title']       = '会员续费通知';
            $data['describe']    = '您的' . $value['grade']['name'] . '将于' . $value['end_time_text'] . '结束，请及时续费';

            Db::name('xj_message')->insert($data);
        }
        $ids = array_column($users, 'user_id');
        Db::name('user')->where('user_id', 'in', $ids)->update(['is_send' => 1]);

        echo '发送成功';
    }

    public function settle_points()
    {
        $month = date('m');
        if ($month != 12) {
            echo '当前' . $month . '月,不结算';exit;
        }
        // 分销商基本设置
        $setting = SettingModel::getItem('commission');

        $dealers = Db::name('dealer_user')->where('user_id', 10043)->where('is_delete', 0)->field('user_id')->select()->toArray();

        foreach ($dealers as $key => $value) {
            $userId = $value['user_id'];

            $refereeUser = Db::name('dealer_referee')->where('level', 1)->where('dealer_id', $userId)->select()->toArray();

            $ids = array_column($refereeUser, 'user_id');

            $time = strtotime(Date('Y-01-01'));

            $points = Db::name('user_points_log')->where('user_id', 'in', $ids)
                ->where('create_time', '>=', $time)->where('type', 0)->where('is_settled', 0)->where('value', '>', 0)->sum('value');
            $money = $setting['points_money'] / 100;

            $points = round($points * $money);

            Db::name('user_points_log')->where('user_id', 'in', $ids)
                ->where('create_time', '>=', $time)->where('type', 0)->where('is_settled', 0)->where('value', '>', 0)->update(['is_settled' => 1, 'settled_time' => time()]);

            // 更新账户积分
            if ($points > 0) {

                Db::name('user')->where('user_id', $userId)->inc('points', $points)->update();

                // 新增积分变动记录
                PointsLogModel::add([
                    'user_id'  => $userId,
                    'value'    => $points,
                    'store_id' => 10001,
                    'type'     => 0,
                    'describe' => "积分分成结算",
                    'remark'   => '',
                ]);
            }

        }
        echo '积分结算成功';
    }

    /**
     * 微信转账结果
     * @return bool
     * @throws BaseException
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function transfer()
    {

        // 获取支付方式的配置信息
        $options = $this->getPaymentConfig('MP-WEIXIN');

        // 构建支付模块
        $Payment = PaymentFacade::store('wechat')->setOptions($options, 'MP-WEIXIN');

        $lists = Db::name('dealer_withdraw')
            ->where('apply_status', 40)
            ->where('pay_type', 10)
            ->field('out_trade_no,state')->select()->toArray();
        $ids = [];
        if (count($lists) > 0) {
            foreach ($lists as $key => $value) {
                $tradeNo = $value['out_trade_no'];

                // 执行第三方支付下单API
                $result = $Payment->transfersQuery($tradeNo);
                if (isset($result['result']['state'])) {
                    $status=40;
                    if($result['result']['state'] == 'SUCCESS'){
                        $status=50;
                    }elseif ($result['result']['state'] == 'FAIL') {
                       $status=60;
                    }

              

                    Db::name('dealer_withdraw')->where('out_trade_no', $tradeNo)
                        ->update(['state' =>$result['result']['state'], 'apply_status' => $status]);
                }
                // if ($result['state'] == 'SUCCESS') {
                //     $ids[] = $tradeNo;
                // }
            }

        }

        // print_r($result);
        echo 2;
    }

    /**
     * 获取支付方式的配置信息
     * @param string $client 客户端
     * @return mixed
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getPaymentConfig(string $client)
    {
        $PaymentModel = new PaymentModel;
        $templateInfo = $PaymentModel->getPaymentInfo('wechat', $client, 10001);
        $options      = $templateInfo['template']['config']['wechat'];

        return $options;
    }
}
