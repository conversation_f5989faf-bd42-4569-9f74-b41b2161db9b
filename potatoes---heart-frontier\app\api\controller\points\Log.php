<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\api\controller\points;

use app\api\controller\Controller;
use app\api\model\user\PointsLog as PointsLogModel;

use think\response\Json;
use app\api\model\dealer\Withdraw as WithdrawModel;

use app\api\model\User as UserModel;

use app\api\service\User as UserService;
/**
 * 积分明细
 * Class Log
 * @package app\api\controller\balance
 */
class Log extends Controller
{
    /**
     * 积分明细列表
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DbException
     */
    public function list(): Json
    {
        $model = new PointsLogModel;
        $list  = $model->getList();
        return $this->renderSuccess(compact('list'));
    }
    public function getData(): Json
    {
        $model = new PointsLogModel;
        $list  = $model->getPoints();
        return $this->renderSuccess(compact('list'));
    }

    //积分兑现页面

     public function center(): Json
    {
        $model = new PointsLogModel;
        $info  = $model->getDuihuan();
  $PointsModel   = new PointsLogModel;
        $info['fencheng_points']= $PointsModel->getDealerPoints();

        return $this->renderSuccess(compact('info'));
    }
    /**
     * 提交提现申请
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
     public function submit(): Json
    {
        // 当前用户ID
        $userId = UserService::getCurrentLoginUserId();
        // 分销商用户详情
        $dealer = UserModel::detail($userId);
        // 提交提现申请
        $model = new WithdrawModel;
        if ($model->submitPoints($dealer,$this->postForm())) {
            return $this->renderSuccess([], '提现申请已提交成功，请等待审核');
        }
        return $this->renderError($model->getError() ?: '提交失败');
    }


}
