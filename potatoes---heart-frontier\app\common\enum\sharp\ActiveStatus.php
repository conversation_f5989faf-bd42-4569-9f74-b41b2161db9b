<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\sharp;

use app\common\enum\EnumBasics;

/**
 * 整点秒杀-活动会场状态
 * Class ActiveStatus
 * @package app\common\enum\sharp
 */
class ActiveStatus extends EnumBasics
{
    // 活动状态：已开始
    const STATE_BEGIN = 10;

    // 活动状态：即将开始
    const STATE_SOON = 20;

    // 活动状态：预告
    const STATE_NOTICE = 30;

    /**
     * 获取枚举数据
     * @return array
     */
    public static function data(): array
    {
        return [
            self::STATE_BEGIN => [
                'name' => '已开始',
                'value' => self::STATE_BEGIN,
            ],
            self::STATE_SOON => [
                'name' => '即将开始',
                'value' => self::STATE_SOON,
            ],
            self::STATE_NOTICE => [
                'name' => '预告',
                'value' => self::STATE_SOON,
            ]
        ];
    }
}