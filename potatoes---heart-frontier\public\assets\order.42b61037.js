import{$ as r}from"./index-4ddb689d.js";const e="order/todoCounts",t="order/list",o="order/detail",n="order/express",d="order/cancel",s="order/extractQrcode",u="order/receipt";function c(t,o){return r.get(e,t,o)}function a(e,o){return r.get(t,e,o)}function i(e,t){return r.get(o,{orderId:e,...t})}function f(e,t){return r.get(n,{orderId:e,...t})}function p(e,t){return r.post(d,{orderId:e,...t})}function g(e,t){return r.post(u,{orderId:e,...t})}function I(e,t){return r.get(s,{orderId:e,...t})}export{f as a,p as c,i as d,I as e,a as l,g as r,c as t};
