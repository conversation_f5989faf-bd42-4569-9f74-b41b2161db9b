<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\service\bargain\order;

use app\common\model\bargain\Task as TaskModel;
use app\common\model\bargain\Active as ActiveModel;
use app\common\service\BaseService;

/**
 * 砍价订单支付成功后的回调
 * Class PaySuccess
 * @package app\common\service\bargain\order
 */
class PaySuccess extends BaseService
{
    /**
     * 回调方法
     * @param $order
     * @return bool
     */
    public function onPaySuccess($order): bool
    {
        // 砍价任务详情
        $task = TaskModel::detail($order['order_source_id']);
        if (empty($task)) {
            $this->error = '未找到砍价任务信息';
            return false;
        }
        // 标记为已购买
        TaskModel::setIsBuy($task['task_id']);
        // 砍价活动详情
        $active = ActiveModel::detail($task['active_id']);
        if (empty($active)) {
            $this->error = '未找到砍价活动信息';
            return false;
        }
        // 累计活动销量
        ActiveModel::setIncSales($active['active_id']);
        return true;
    }
}