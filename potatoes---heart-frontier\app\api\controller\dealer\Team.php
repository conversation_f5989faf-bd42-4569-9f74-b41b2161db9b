<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\dealer;

use think\response\Json;
use app\api\controller\Controller;
use app\api\model\dealer\Referee as RefereeModel;
use app\api\service\User as UserService;

/**
 * 我的团队
 * Class Team
 * @package app\api\controller\user\dealer
 */
class Team extends Controller
{
    /**
     * 我的团队列表
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(): Json
    {
        // 当前用户ID
        $userId = UserService::getCurrentLoginUserId();
        // 我的团队列表
        $model = new RefereeModel;
        $list = $model->getList($userId, $this->request->param());
        // 返回数据
        return $this->renderSuccess(compact('list'));
    }
}