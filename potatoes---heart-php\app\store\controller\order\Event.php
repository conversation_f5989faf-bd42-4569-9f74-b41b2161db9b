<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\order;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\Order as OrderModel;

/**
 * 订单事件控制器
 * Class Event
 * @package app\store\controller\order
 */
class Event extends Controller
{
    /**
     * 修改订单价格
     * @param int $orderId
     * @return Json
     */
    public function updatePrice(int $orderId): Json
    {
        // 订单详情
        $model = OrderModel::detail($orderId);
        if ($model->updatePrice($this->postForm())) {
            return $this->renderSuccess('操作成功');
        }
        return $this->renderError($model->getError() ?: '操作失败');
    }

    /**
     * 修改商家备注
     * @param int $orderId
     * @return Json
     */
    public function updateRemark(int $orderId): Json
    {
        // 订单详情
        $model = OrderModel::detail($orderId);
        if ($model->updateRemark($this->postForm())) {
            return $this->renderSuccess('操作成功');
        }
        return $this->renderError($model->getError() ?: '操作失败');
    }

    /**
     * 小票打印
     * @param int $orderId
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function printer(int $orderId): Json
    {
        // 订单详情
        $model = OrderModel::detail($orderId);
        if ($model->printer($this->postForm())) {
            return $this->renderSuccess('操作成功');
        }
        return $this->renderError($model->getError() ?: '操作失败');
    }

    /**
     * 审核：用户取消订单
     * @param int $orderId
     * @return Json
     */
    public function confirmCancel(int $orderId): Json
    {
        // 订单详情
        $model = OrderModel::detail($orderId);
        if ($model->confirmCancel($this->postForm())) {
            return $this->renderSuccess('操作成功');
        }
        return $this->renderError($model->getError() ?: '操作失败');
    }

    /**
     * 门店自提核销
     * @param int $orderId
     * @return Json
     */
    public function extract(int $orderId): Json
    {
        // 订单详情
        $model = OrderModel::detail($orderId);
        if ($model->extractEvent($this->postForm())) {
            return $this->renderSuccess('核销成功');
        }
        return $this->renderError($model->getError() ?: '核销失败');
    }

    /**
     * 确认支付 (线下支付)
     * @param int $orderId
     * @return Json
     * @throws \cores\exception\BaseException
     */
    public function payment(int $orderId): Json
    {
        // 订单详情
        $model = OrderModel::detail($orderId);
        // 确认支付
        if ($model->paymentEvent()) {
            return $this->renderSuccess('操作成功');
        }
        return $this->renderError($model->getError() ?: '操作失败');
    }

    /**
     * 删除订单记录
     * @param int $orderId
     * @return Json
     */
    public function delete(int $orderId): Json
    {
        // 订单详情
        $model = OrderModel::detail($orderId);
        // 确认核销
        if ($model->setDelete()) {
            return $this->renderSuccess('删除成功');
        }
        return $this->renderError($model->getError() ?: '操作失败');
    }
}
