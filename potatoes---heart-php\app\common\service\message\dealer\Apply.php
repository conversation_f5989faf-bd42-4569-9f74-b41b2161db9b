<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\service\message\dealer;

use app\common\service\message\Basics;

/**
 * 消息通知服务 [分销商入驻]
 * Class Apply
 * @package app\common\service\message\dealer
 */
class Apply extends Basics
{
    /**
     * 参数列表
     * @var array
     */
    protected $param = [
        'apply' => [],   // 申请记录
        'user' => [],    // 用户信息
    ];

    /**
     * 发送消息通知
     * @param array $param
     * @return mixed|void
     * @throws \think\Exception
     */
    public function send(array $param)
    {
        // 记录参数
        $this->param = $param;
    }
}