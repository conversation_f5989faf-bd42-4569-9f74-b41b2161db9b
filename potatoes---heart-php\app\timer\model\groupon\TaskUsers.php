<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\timer\model\groupon;

use app\common\model\groupon\TaskUsers as TaskUsersModel;

/**
 * 拼团拼单成员模型
 * Class TaskUsers
 * @package app\timer\model\groupon
 */
class TaskUsers extends TaskUsersModel
{
    /**
     * 批量加入机器人成员
     * @param array $robotIds
     * @param $task
     * @param $storeId
     * @return bool
     */
    public static function mockJoin(array $robotIds, $task, $storeId): bool
    {
        // 批量加入拼团
        $data = [];
        foreach ($robotIds as $robotId) {
            $data[] = [
                'task_id' => $task['task_id'],
                'is_robot' => 1,
                'robot_id' => $robotId,
                'store_id' => $storeId,
            ];
        }
        !empty($data) && (new static)->addAll($data);
        return true;
    }
}