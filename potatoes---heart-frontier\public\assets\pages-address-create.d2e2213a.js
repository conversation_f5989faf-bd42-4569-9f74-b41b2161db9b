import{a as e,_ as a,b as o}from"./u-form.096d05f1.js";import{o as l,c as r,w as t,n as s,i as n,a as i,f as d,k as m,y as u}from"./index-97ace627.js";import{r as p}from"./uni-app.es.095c4ce1.js";import{_ as f}from"./select-region.e7ebd6ac.js";import{a as c}from"./verify.5c522bdb.js";import{a as g,b as h}from"./address.2a112cc0.js";import{_ as b}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.c9477b7b.js";import"./u-loading.11ce6644.js";import"./u-popup.4d8643d1.js";const _={name:[{required:!0,message:"请输入姓名",trigger:["blur","change"]}],phone:[{required:!0,message:"请输入手机号",trigger:["blur","change"]},{validator:(e,a,o)=>c(a),message:"手机号码不正确",trigger:["blur"]}],region:[{required:!0,message:"请选择省市区",trigger:["blur","change"],type:"array"}],detail:[{required:!0,message:"请输入详细地址",trigger:["blur","change"]}]};const y=b({components:{SelectRegion:f},data:()=>({form:{content:"",name:"",phone:"",region:[],detail:""},rules:_,disabled:!1}),onLoad(e){},onReady(){this.$refs.uForm.setRules(this.rules)},methods:{handleAnalysis(){const e=this;g(e.form.content).then((a=>{const o=a.data.detail;e.createFormData(o)}))},createFormData(e){const{form:a}=this;a.name=e.name,a.phone=e.phone,a.detail=e.detail,a.region=this.createRegion(e)},createRegion(e){return 0==e.province_id||0==e.city_id||0==e.region_id?(this.$toast("很抱歉，地区未能识别请手动选择",2e3),[]):[{label:e.region.province,value:e.province_id},{label:e.region.city,value:e.city_id},{label:e.region.region,value:e.region_id}]},handleSubmit(){const e=this;if(e.disabled)return!1;e.$refs.uForm.validate((a=>{a&&(e.disabled=!0,h(e.form).then((a=>{e.$toast(a.message),uni.navigateBack()})).finally((()=>e.disabled=!1)))}))}}},[["render",function(c,g,h,b,_,y){const V=p(u("u-input"),e),v=p(u("u-form-item"),a),j=n,x=p(u("select-region"),f),R=p(u("u-form"),o);return l(),r(j,{class:"container",style:s(c.appThemeStyle)},{default:t((()=>[i(j,{class:"form-analysis form-wrapper"},{default:t((()=>[i(v,{prop:"name","border-bottom":!1},{default:t((()=>[i(V,{modelValue:_.form.content,"onUpdate:modelValue":g[0]||(g[0]=e=>_.form.content=e),type:"textarea",placeholder:"粘贴地址信息，自动解析姓名、电话和地址","custom-style":{height:"150rpx"},"auto-height":!1},null,8,["modelValue"])])),_:1}),i(j,{class:"analysis-foot clearfix"},{default:t((()=>[i(j,{class:"analysis-btn",onClick:g[1]||(g[1]=e=>y.handleAnalysis())},{default:t((()=>[d("智能识别")])),_:1})])),_:1})])),_:1}),i(j,{class:"page-title"},{default:t((()=>[d("收货地址")])),_:1}),i(j,{class:"form-wrapper"},{default:t((()=>[i(R,{model:_.form,ref:"uForm","label-width":"140rpx"},{default:t((()=>[i(v,{label:"姓名",prop:"name"},{default:t((()=>[i(V,{modelValue:_.form.name,"onUpdate:modelValue":g[2]||(g[2]=e=>_.form.name=e),placeholder:"请输入收货人姓名"},null,8,["modelValue"])])),_:1}),i(v,{label:"电话",prop:"phone"},{default:t((()=>[i(V,{modelValue:_.form.phone,"onUpdate:modelValue":g[3]||(g[3]=e=>_.form.phone=e),placeholder:"请输入收货人手机号"},null,8,["modelValue"])])),_:1}),i(v,{label:"地区",prop:"region"},{default:t((()=>[i(x,{ref:"sRegion",modelValue:_.form.region,"onUpdate:modelValue":g[4]||(g[4]=e=>_.form.region=e)},null,8,["modelValue"])])),_:1}),i(v,{label:"详细地址",prop:"detail","border-bottom":!1},{default:t((()=>[i(V,{modelValue:_.form.detail,"onUpdate:modelValue":g[5]||(g[5]=e=>_.form.detail=e),placeholder:"街道门牌、楼层等信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1}),i(j,{class:"footer"},{default:t((()=>[i(j,{class:"btn-wrapper"},{default:t((()=>[i(j,{class:m(["btn-item btn-item-main",{disabled:_.disabled}]),onClick:g[6]||(g[6]=e=>y.handleSubmit())},{default:t((()=>[d("保存")])),_:1},8,["class"])])),_:1})])),_:1})])),_:1},8,["style"])}],["__scopeId","data-v-9f6f943c"]]);export{y as default};
