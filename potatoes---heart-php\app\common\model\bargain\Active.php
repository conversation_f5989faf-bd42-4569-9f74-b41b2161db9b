<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\bargain;

use cores\BaseModel;
use app\common\service\Goods as GoodsService;

/**
 * 砍价活动模型
 * Class Active
 * @package app\common\model\bargain
 */
class Active extends BaseModel
{
    // 定义表名
    protected $name = 'bargain_active';

    // 定义主键
    protected $pk = 'active_id';

    // 定义别名
    protected string $alias = 'active';

    // 强制类型转换
    protected $type = [
        'is_self_cut' => 'integer',
        'is_floor_buy' => 'integer',
        'status' => 'integer',
    ];

    /**
     * 追加的字段
     * @var array $append
     */
    protected $append = [
        'is_start',   // 活动已开启
        'is_end',   // 活动已结束
        'active_sales', // 活动销量
    ];

    /**
     * 获取器：活动开始时间
     * @param $value
     * @return false|string
     */
    public function getStartTimeAttr($value)
    {
        return format_time($value);
    }

    /**
     * 获取器：活动结束时间
     * @param $value
     * @return false|string
     */
    public function getEndTimeAttr($value)
    {
        return format_time($value);
    }

    /**
     * 获取器：分享标题
     * @param $value
     * @return string
     */
    public function getShareTitleAttr($value): string
    {
        return htmlspecialchars_decode($value);
    }

    /**
     * 获取器：砍价助力语
     * @param $value
     * @return string
     */
    public function getPromptWordsAttr($value): string
    {
        return htmlspecialchars_decode($value);
    }

    /**
     * 获取器：活动是否已开启
     * @param $value
     * @param $data
     * @return bool|false
     */
    public function getIsStartAttr($value, $data): bool
    {
        return $value ?: $data['start_time'] <= time();
    }

    /**
     * 获取器：活动是否已结束
     * @param $value
     * @param $data
     * @return bool|false
     */
    public function getIsEndAttr($value, $data): bool
    {
        return $value ?: $data['end_time'] <= time();
    }

    /**
     * 获取器：活动销量（用户端显示）
     * @param $value
     * @param $data
     * @return false|string
     */
    public function getActiveSalesAttr($value, $data)
    {
        return $value ?: $data['actual_sales'] + $data['initial_sales'];
    }

    /**
     * 砍价活动详情
     * @param $activeId
     * @param array $with
     * @return static|array|null
     */
    public static function detail($activeId, array $with = [])
    {
        return static::get($activeId, $with);
    }

    /**
     * 设置商品展示的数据
     * @param $data
     * @param bool $isMultiple
     * @param array $hidden 隐藏的属性
     * @param callable|null $callback
     * @return mixed
     */
    protected function setGoodsListData($data, bool $isMultiple = true, array $hidden = [], callable $callback = null)
    {
        // 设置主商品数据
        $data = GoodsService::setGoodsData($data, $isMultiple, $hidden);
        if (!$isMultiple) $dataSource = [&$data]; else $dataSource = &$data;
        // 整理商品数据
        foreach ($dataSource as &$item) {
            // 商品名称
            $item['goods_name'] = $item['goods']['goods_name'];
            // 商品图片
            $item['goods_image'] = $item['goods']['goods_image'];
            // 秒杀商品原价 (获取主商品价格)
            $item['original_price'] = $item['goods']['goods_price_min'];
            // 回调函数
            is_callable($callback) && call_user_func($callback, $item);
        }
        return $data;
    }

    /**
     * 累计活动销量(实际)
     * @param int $activeId
     * @return mixed
     */
    public static function setIncSales(int $activeId)
    {
        return (new static())->setInc($activeId, 'actual_sales', 1);
    }
}