import{o as e,c as s,w as a,n as t,i as d,a as l,b as n,d as i,F as o,e as c,f as r,r as u,t as f,l as h,k as _,_ as m,H as p}from"./index-ae8bbb19.js";import{l as g,d as k,r as v,s as I}from"./address.66e1d271.js";import{E as y}from"./index.97c7afee.js";import{_ as C}from"./_plugin-vue_export-helper.1b428a4d.js";const L=C({components:{Empty:y},data:()=>({options:{},isLoading:!0,list:[],defaultId:null}),onLoad(e){this.options=e},onShow(){this.getPageData()},methods:{getPageData(){const e=this;e.isLoading=!0,Promise.all([e.getDefaultId(),e.getAddressList()]).then((()=>{e.onReorder()})).finally((()=>e.isLoading=!1))},getAddressList(){const e=this;return new Promise(((s,a)=>{g().then((a=>{e.list=a.data.list,s(a)})).catch(a)}))},getDefaultId(){return new Promise(((e,s)=>{const a=this;k().then((s=>{a.defaultId=s.data.defaultId,e(s)})).catch(s)}))},onReorder(){const e=this;e.list.sort((s=>s.address_id==e.defaultId?-1:1))},handleCreate(){this.$navTo("pages/address/create")},handleUpdate(e){this.$navTo("pages/address/update",{addressId:e})},handleRemove(e){const s=this;uni.showModal({title:"提示",content:"您确定要删除当前收货地址吗?",success({confirm:a}){a&&s.onRemove(e)}})},onRemove(e){const s=this;v(e).then((e=>{s.getPageData()}))},handleSetDefault(e){const s=this;I(e).then((a=>{s.defaultId=e,"checkout"===s.options.from&&uni.navigateBack()}))}}},[["render",function(g,k,v,I,y,C){const L=_,D=d,w=m,x=p,P=u("empty");return e(),s(D,{class:"container",style:t(g.appThemeStyle)},{default:a((()=>[l(D,{class:"addres-list"},{default:a((()=>[(e(!0),n(o,null,i(y.list,((t,d)=>(e(),s(D,{class:"address-item",key:d},{default:a((()=>[l(D,{class:"contacts"},{default:a((()=>[l(L,{class:"name"},{default:a((()=>[r(f(t.name),1)])),_:2},1024),l(L,{class:"phone"},{default:a((()=>[r(f(t.phone),1)])),_:2},1024)])),_:2},1024),l(D,{class:"address"},{default:a((()=>[(e(!0),n(o,null,i(t.region,((t,d)=>(e(),s(L,{class:"region",key:d},{default:a((()=>[r(f(t),1)])),_:2},1024)))),128)),l(L,{class:"detail"},{default:a((()=>[r(f(t.detail),1)])),_:2},1024)])),_:2},1024),l(D,{class:"line"}),l(D,{class:"item-option"},{default:a((()=>[l(D,{class:"_left"},{default:a((()=>[l(x,{class:"item-radio",onClick:h((e=>C.handleSetDefault(t.address_id)),["stop"])},{default:a((()=>[l(w,{class:"radio",color:g.appTheme.mainBg,checked:t.address_id==y.defaultId},null,8,["color","checked"]),l(L,{class:"text"},{default:a((()=>[r(f(t.address_id==y.defaultId?"默认":"选择"),1)])),_:2},1024)])),_:2},1032,["onClick"])])),_:2},1024),l(D,{class:"_right"},{default:a((()=>[l(D,{class:"events"},{default:a((()=>[l(D,{class:"event-item",onClick:e=>C.handleUpdate(t.address_id)},{default:a((()=>[l(L,{class:"iconfont icon-edit"}),l(L,{class:"title"},{default:a((()=>[r("编辑")])),_:1})])),_:2},1032,["onClick"]),l(D,{class:"event-item",onClick:e=>C.handleRemove(t.address_id)},{default:a((()=>[l(L,{class:"iconfont icon-delete"}),l(L,{class:"title"},{default:a((()=>[r("删除")])),_:1})])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1}),y.list.length?c("",!0):(e(),s(P,{key:0,isLoading:y.isLoading,tips:"亲，暂无收货地址"},null,8,["isLoading"])),l(D,{class:"footer-fixed"},{default:a((()=>[l(D,{class:"btn-wrapper"},{default:a((()=>[l(D,{class:"btn-item btn-item-main",onClick:k[0]||(k[0]=e=>C.handleCreate())},{default:a((()=>[r("添加新地址")])),_:1})])),_:1})])),_:1})])),_:1},8,["style"])}],["__scopeId","data-v-926690e9"]]);export{L as default};
