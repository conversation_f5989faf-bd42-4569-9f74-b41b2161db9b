<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\live;

use app\common\enum\EnumBasics;

/**
 * 枚举类：微信小程序直播间状态
 * Class Room
 * @package app\common\enum\live
 */
class LiveStatus extends EnumBasics
{
    /**
     * 获取枚举数据
     * @return array
     */
    public static function data(): array
    {
        return [
            101 => [
                'name' => '直播中',
                'value' => 101,
            ],
            102 => [
                'name' => '未开始',
                'value' => 102,
            ],
            103 => [
                'name' => '已结束',
                'value' => 103,
            ],
            104 => [
                'name' => '禁播',
                'value' => 104,
            ],
            105 => [
                'name' => '暂停中',
                'value' => 105,
            ],
            106 => [
                'name' => '异常',
                'value' => 106,
            ],
            107 => [
                'name' => '已过期',
                'value' => 107,
            ]
        ];
    }
}