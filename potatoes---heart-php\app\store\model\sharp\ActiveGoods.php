<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\sharp;

use app\store\model\sharp\Goods as SharpGoodsModel;
use app\common\model\sharp\ActiveGoods as ActiveGoodsModel;

/**
 * 整点秒杀-活动会场与商品关联模型
 * Class ActiveGoods
 * @package app\store\model\sharp
 */
class ActiveGoods extends ActiveGoodsModel
{
    /**
     * 同步删除活动会场与商品关联记录
     * @param int $sharpGoodsId 秒杀商品ID
     * @return bool|int
     */
    public static function deleteSharpGoods(int $sharpGoodsId)
    {
        return static::deleteAll(['sharp_goods_id' => $sharpGoodsId]);
    }

    /**
     * 根据商品ID集获取商品列表
     * @param array $sharpGoodsIds 秒杀商品ID集
     * @param array $param
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected function getGoodsListByIds(array $sharpGoodsIds, array $param = [])
    {
        return (new SharpGoodsModel)->getListByIds($sharpGoodsIds, $param);
    }
}