import{$ as e,o as s,c as a,w as l,n as d,b as t,i as u,a as o,f as i,t as f,d as r,e as n,F as c,k as _,g as x,l as m,af as p,I as g,X as b,a5 as A}from"./index-86a01735.js";import{A as h,R as v,a as E}from"./RefundType.86b0e4a8.js";import{d as w,a as y}from"./refund.3e4b01a8.js";import{_ as I}from"./_plugin-vue_export-helper.1b428a4d.js";const k="express/list";const R=I({data:()=>({AuditStatusEnum:h,RefundStatusEnum:v,RefundTypeEnum:E,isLoading:!0,orderRefundId:null,detail:{},expressList:[],formData:{expressId:null,expressNo:""},expressIndex:-1,disabled:!1}),onLoad({orderRefundId:e}){this.orderRefundId=e,this.getPageData()},methods:{getPageData(){const e=this;e.isLoading=!0,Promise.all([e.getRefundDetail(),e.getExpressList()]).then((s=>e.isLoading=!1))},getRefundDetail(){const e=this;return new Promise(((s,a)=>{w(e.orderRefundId).then((a=>{e.detail=a.data.detail,s()})).catch(a)}))},getExpressList(){const s=this;return new Promise(((a,l)=>{var d;e.get(k,d).then((e=>{s.expressList=e.data.list,a()})).catch(l)}))},onGoodsDetail(e){this.$navTo("pages/goods/detail",{goodsId:e})},handlePreviewImages(e){const{detail:{images:s}}=this,a=s.map((e=>e.image_url));uni.previewImage({current:a[e],urls:a})},onChangeExpress(e){const s=e.detail.value,{expressList:a}=this;this.expressIndex=s,this.formData.expressId=a[s].express_id},onSubmit(){const e=this;if(!0===e.disabled)return!1;e.disabled=!0,y(e.orderRefundId,e.formData).then((s=>{e.$toast(s.message),setTimeout((()=>{e.disabled=!1,uni.navigateBack()}),1500)})).catch((s=>e.disabled=!1))}}},[["render",function(e,h,v,E,w,y){const I=x,k=u,R=m,D=p,P=g,j=b,L=A;return w.isLoading?t("",!0):(s(),a(k,{key:0,class:"container p-bottom",style:d(e.appThemeStyle)},{default:l((()=>[o(k,{class:"detail-header dis-flex flex-y-center"},{default:l((()=>[o(k,{class:"header-backdrop"},{default:l((()=>[o(I,{class:"image",src:"data:image/png;base64,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"})])),_:1}),o(k,{class:"header-state"},{default:l((()=>[o(R,{class:"f-32 col-f"},{default:l((()=>[i(f(w.detail.state_text),1)])),_:1})])),_:1})])),_:1}),o(k,{class:"detail-goods b-f m-top20 dis-flex flex-dir-row",onClick:h[0]||(h[0]=e=>y.onGoodsDetail(w.detail.orderGoods.goods_id))},{default:l((()=>[o(k,{class:"left"},{default:l((()=>[o(I,{class:"goods-image",src:w.detail.orderGoods.goods_image},null,8,["src"])])),_:1}),o(k,{class:"right dis-flex flex-box flex-dir-column flex-x-around"},{default:l((()=>[o(k,{class:"goods-name"},{default:l((()=>[o(R,{class:"twoline-hide"},{default:l((()=>[i(f(w.detail.orderGoods.goods_name),1)])),_:1})])),_:1}),o(k,{class:"dis-flex col-9 f-24"},{default:l((()=>[o(k,{class:"flex-box"},{default:l((()=>[o(k,{class:"goods-props clearfix"},{default:l((()=>[(s(!0),r(c,null,n(w.detail.orderGoods.goods_props,((e,d)=>(s(),a(k,{class:"goods-props-item",key:d},{default:l((()=>[o(R,null,{default:l((()=>[i(f(e.value.name),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1}),o(R,{class:"t-r"},{default:l((()=>[i("×"+f(w.detail.orderGoods.total_num),1)])),_:1})])),_:1})])),_:1})])),_:1}),o(k,{class:"detail-order b-f row-block"},{default:l((()=>[o(k,{class:"item dis-flex flex-x-end flex-y-center"},{default:l((()=>[o(R,{class:""},{default:l((()=>[i("商品金额：")])),_:1}),o(R,{class:"col-m"},{default:l((()=>[i("￥"+f(w.detail.orderGoods.total_pay_price),1)])),_:1})])),_:1})])),_:1}),w.detail.status==w.RefundStatusEnum.COMPLETED.value&&10==w.detail.type?(s(),a(k,{key:0,class:"detail-order b-f row-block dis-flex flex-x-end flex-y-center"},{default:l((()=>[o(R,{class:""},{default:l((()=>[i("已退款金额：")])),_:1}),o(R,{class:"col-m"},{default:l((()=>[i("￥"+f(w.detail.refund_money),1)])),_:1})])),_:1})):t("",!0),w.detail.status==w.RefundStatusEnum.REJECTED.value?(s(),a(k,{key:1,class:"detail-refund b-f m-top20"},{default:l((()=>[o(k,{class:"detail-refund__row dis-flex"},{default:l((()=>[o(k,{class:"text"},{default:l((()=>[o(R,null,{default:l((()=>[i("售后类型：")])),_:1})])),_:1}),o(k,{class:"flex-box"},{default:l((()=>[o(R,null,{default:l((()=>[i(f(w.RefundTypeEnum[w.detail.type].name),1)])),_:1})])),_:1})])),_:1}),o(k,{class:"detail-refund__row dis-flex"},{default:l((()=>[o(k,{class:"text"},{default:l((()=>[o(R,null,{default:l((()=>[i("申请原因：")])),_:1})])),_:1}),o(k,{class:"flex-box"},{default:l((()=>[o(R,null,{default:l((()=>[i(f(w.detail.apply_desc),1)])),_:1})])),_:1})])),_:1}),w.detail.images.length>0?(s(),a(k,{key:0,class:"detail-refund__row dis-flex"},{default:l((()=>[o(k,{class:"text"},{default:l((()=>[o(R,null,{default:l((()=>[i("申请凭证：")])),_:1})])),_:1}),o(k,{class:"image-list flex-box"},{default:l((()=>[(s(!0),r(c,null,n(w.detail.images,((e,d)=>(s(),a(k,{class:"image-preview",key:d},{default:l((()=>[o(I,{class:"image",mode:"aspectFill",src:e.image_url,onClick:e=>y.handlePreviewImages(d)},null,8,["src","onClick"])])),_:2},1024)))),128))])),_:1})])),_:1})):t("",!0)])),_:1})):t("",!0),10==w.detail.status.value?(s(),a(k,{key:2,class:"detail-refund b-f m-top20"},{default:l((()=>[o(k,{class:"detail-refund__row dis-flex"},{default:l((()=>[o(k,{class:"text"},{default:l((()=>[o(R,{class:"col-m"},{default:l((()=>[i("拒绝原因：")])),_:1})])),_:1}),o(k,{class:"flex-box"},{default:l((()=>[o(R,null,{default:l((()=>[i(f(w.detail.refuse_desc),1)])),_:1})])),_:1})])),_:1})])),_:1})):t("",!0),w.detail.audit_status==w.AuditStatusEnum.REVIEWED.value&&w.detail.is_user_send?(s(),a(k,{key:3,class:"detail-address b-f m-top20"},{default:l((()=>[o(k,{class:"detail-address__row address-title"},{default:l((()=>[o(R,{class:"col-m"},{default:l((()=>[i("退货物流信息")])),_:1})])),_:1}),o(k,{class:"detail-address__row address-details"},{default:l((()=>[o(k,{class:"address-details__row"},{default:l((()=>[o(R,null,{default:l((()=>[i("物流公司："+f(w.detail.express.express_name),1)])),_:1})])),_:1}),o(k,{class:"address-details__row"},{default:l((()=>[o(R,null,{default:l((()=>[i("物流单号："+f(w.detail.express_no),1)])),_:1})])),_:1}),o(k,{class:"address-details__row"},{default:l((()=>[o(R,null,{default:l((()=>[i("发货时间："+f(w.detail.send_time),1)])),_:1})])),_:1})])),_:1})])),_:1})):t("",!0),w.detail.audit_status==w.AuditStatusEnum.REVIEWED.value?(s(),a(k,{key:4,class:"detail-address b-f m-top20"},{default:l((()=>[o(k,{class:"detail-address__row address-title"},{default:l((()=>[o(R,{class:"col-m"},{default:l((()=>[i("商家退货地址")])),_:1})])),_:1}),o(k,{class:"detail-address__row address-details"},{default:l((()=>[o(k,{class:"address-details__row"},{default:l((()=>[o(R,null,{default:l((()=>[i("收货人："+f(w.detail.address.name),1)])),_:1})])),_:1}),o(k,{class:"address-details__row"},{default:l((()=>[o(R,null,{default:l((()=>[i("联系电话："+f(w.detail.address.phone),1)])),_:1})])),_:1}),o(k,{class:"address-details__row dis-flex"},{default:l((()=>[o(k,{class:"text"},{default:l((()=>[o(R,null,{default:l((()=>[i("详细地址：")])),_:1})])),_:1}),o(k,{class:"address flex-box"},{default:l((()=>[(s(!0),r(c,null,n(w.detail.address.region,((e,d)=>(s(),a(R,{class:"region",key:d},{default:l((()=>[i(f(e),1)])),_:2},1024)))),128)),o(R,{class:"detail"},{default:l((()=>[i(f(w.detail.address.detail),1)])),_:1})])),_:1})])),_:1})])),_:1}),o(k,{class:"detail-address__row address-tips"},{default:l((()=>[o(k,{class:"f-26 col-9"},{default:l((()=>[o(R,null,{default:l((()=>[i("· 未与卖家协商一致情况下，请勿寄到付或平邮")])),_:1})])),_:1}),o(k,{class:"f-26 col-9"},{default:l((()=>[o(R,null,{default:l((()=>[i("· 请填写真实有效物流信息")])),_:1})])),_:1})])),_:1})])),_:1})):t("",!0),w.detail.type!=w.RefundTypeEnum.RETURN.value||w.detail.audit_status!=w.AuditStatusEnum.REVIEWED.value||w.detail.is_user_send?t("",!0):(s(),a(L,{key:5,onSubmit:h[2]||(h[2]=e=>y.onSubmit())},{default:l((()=>[o(k,{class:"detail-express b-f m-top20"},{default:l((()=>[o(k,{class:"form-group dis-flex flex-y-center"},{default:l((()=>[o(k,{class:"field"},{default:l((()=>[i("物流公司：")])),_:1}),o(k,{class:"flex-box"},{default:l((()=>[o(D,{mode:"selector",range:w.expressList,"range-key":"express_name",value:w.expressIndex,onChange:y.onChangeExpress},{default:l((()=>[w.expressIndex>-1?(s(),a(R,{key:0},{default:l((()=>[i(f(w.expressList[w.expressIndex].express_name),1)])),_:1})):(s(),a(R,{key:1,class:"col-80"},{default:l((()=>[i("请选择物流公司")])),_:1}))])),_:1},8,["range","value","onChange"])])),_:1})])),_:1}),o(k,{class:"form-group dis-flex flex-y-center"},{default:l((()=>[o(k,{class:"field"},{default:l((()=>[i("物流单号：")])),_:1}),o(k,{class:"flex-box"},{default:l((()=>[o(P,{class:"input",modelValue:w.formData.expressNo,"onUpdate:modelValue":h[1]||(h[1]=e=>w.formData.expressNo=e),placeholder:"请填写物流单号"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(k,{class:"footer"},{default:l((()=>[o(k,{class:"btn-wrapper"},{default:l((()=>[o(j,{class:_(["btn-item btn-item-main btn-normal",{disabled:w.disabled}]),formType:"submit"},{default:l((()=>[i("确认发货")])),_:1},8,["class"])])),_:1})])),_:1})])),_:1}))])),_:1},8,["style"]))}],["__scopeId","data-v-57c9304f"]]);export{R as default};
