import{q as a,u as t,o as s,c as e,w as l,n as o,i,a as c,d as n,e as d,F as u,b as r,k as f,f as _,l as m,r as g,y as p,t as h,g as b}from"./index-2c18571c.js";import{M as x,_ as y}from"./mescroll-mixins.fd5f02d1.js";import{r as k}from"./uni-app.es.f042447b.js";import{W as v}from"./wxofficial.12eafe1c.js";import{A as T}from"./index.9242fe53.js";import{C as L}from"./index.fc1e6256.js";import{l as C,a as w}from"./task.c9913963.js";import{_ as S}from"./_plugin-vue_export-helper.1b428a4d.js";const j=S({components:{AvatarImage:T,CountDown:L},mixins:[x,v],data:()=>({isLoading:!0,curTab:0,activeList:a(),myList:a(),upOption:{auto:!0,page:{size:15},noMoreSize:3}}),watch:{curTab(a){uni.setNavigationBarTitle({title:0==a?"砍价会场":"我的砍价"})}},onLoad(a){a.tab&&(this.curTab=a.tab),this.setWxofficialShareData()},methods:{upCallback(a){const t=this;t.getListData(a.num).then((a=>{const s=a.data.length,e=a.data.total;t.mescroll.endBySize(s,e)})).catch((()=>t.mescroll.endErr()))},getListData(a){return{0:this.getActiveList,1:this.getMyList}[this.curTab](a)},getActiveList(a){const s=this;return new Promise(((e,l)=>{C({page:a}).then((l=>{const o=l.data.list;s.activeList.data=t(o,s.activeList,a),e(o)})).catch(l)}))},getMyList(a){const s=this;return new Promise(((e,l)=>{w({page:a}).then((l=>{const o=l.data.list;s.myList.data=t(o,s.myList,a),e(o)})).catch(l)}))},onChangeTab(t=0){const s=this;s.curTab=t,s.activeList=a(),s.myList=a(),s.mescroll.resetUpScroll()},onTargetActive(a){this.$navTo("pages/bargain/goods/index",{activeId:a.active_id,goodsId:a.goods_id})},onTargetTask(a){this.$navTo("pages/bargain/task",{taskId:a})},setWxofficialShareData(){this.updateShareCardData({title:"砍价专区"})}},onShareAppMessage(){return{title:"砍价专区",path:`/pages/bargain/index?${this.$getShareUrlParams()}`}},onShareTimeline(){return{title:"砍价专区",path:`/pages/bargain/index?${this.$getShareUrlParams()}`}}},[["render",function(a,t,x,v,T,L){const C=b,w=i,S=m,j=g("avatar-image"),I=g("count-down"),A=k(p("mescroll-body"),y);return s(),e(w,{class:"container",style:o(a.appThemeStyle)},{default:l((()=>[c(A,{ref:"mescrollRef",sticky:!0,onInit:a.mescrollInit,down:{use:!1},up:T.upOption,onUp:L.upCallback},{default:l((()=>[0==T.curTab?(s(),e(w,{key:0,class:"bargain-hall"},{default:l((()=>[(s(!0),n(u,null,d(T.activeList.data,((a,t)=>(s(),e(w,{class:"goods-item",key:t},{default:l((()=>[c(w,{class:"goods-item--container dis-flex",onClick:t=>L.onTargetActive(a)},{default:l((()=>[c(w,{class:"goods-image"},{default:l((()=>[c(C,{class:"image",src:a.goods.goods_image},null,8,["src"])])),_:2},1024),c(w,{class:"goods-info"},{default:l((()=>[c(w,{class:"goods-name"},{default:l((()=>[c(S,{class:"twoline-hide"},{default:l((()=>[_(h(a.goods.goods_name),1)])),_:2},1024)])),_:2},1024),a.helpsCount>0?(s(),e(w,{key:0,class:"peoples dis-flex"},{default:l((()=>[c(w,{class:"user-list dis-flex"},{default:l((()=>[(s(!0),n(u,null,d(a.helpList,((a,t)=>(s(),e(w,{class:"user-item-avatar",key:t},{default:l((()=>[c(j,{url:a.user.avatar_url,width:36},null,8,["url"])])),_:2},1024)))),128))])),_:2},1024),c(w,{class:"people__text"},{default:l((()=>[c(S,null,{default:l((()=>[_(h(a.helpsCount)+"人正在砍价",1)])),_:2},1024)])),_:2},1024)])),_:2},1024)):r("",!0),c(w,{class:"goods-price"},{default:l((()=>[c(S,null,{default:l((()=>[_("￥"+h(a.goods.goods_price_min),1)])),_:2},1024)])),_:2},1024),c(w,{class:"floor-price"},{default:l((()=>[c(S,{class:"small"},{default:l((()=>[_("最低￥")])),_:1}),c(S,{class:"big"},{default:l((()=>[_(h(a.floor_price),1)])),_:2},1024)])),_:2},1024),c(w,{class:"opt-touch"},{default:l((()=>[c(w,{class:"touch-btn"},{default:l((()=>[c(S,null,{default:l((()=>[_("立即参加")])),_:1})])),_:1})])),_:1})])),_:2},1024)])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1})):r("",!0),1==T.curTab?(s(),e(w,{key:1,class:"bargain-hall"},{default:l((()=>[(s(!0),n(u,null,d(T.myList.data,((a,t)=>(s(),e(w,{class:"goods-item",key:t},{default:l((()=>[c(w,{class:"goods-item--container dis-flex",onClick:t=>L.onTargetTask(a.task_id)},{default:l((()=>[c(w,{class:"goods-image"},{default:l((()=>[c(C,{class:"image",src:a.goods.goods_image},null,8,["src"])])),_:2},1024),c(w,{class:"goods-info"},{default:l((()=>[c(w,{class:"goods-name"},{default:l((()=>[c(S,{class:"twoline-hide"},{default:l((()=>[_(h(a.goods.goods_name),1)])),_:2},1024)])),_:2},1024),c(w,{class:"task-rate"},{default:l((()=>[1==a.status?(s(),n(u,{key:0},[c(S,null,{default:l((()=>[_("已砍")])),_:1}),c(S,{class:"col-m"},{default:l((()=>[_(h(a.cut_money),1)])),_:2},1024),c(S,null,{default:l((()=>[_("元，")])),_:1}),c(S,null,{default:l((()=>[_("只差")])),_:1}),c(S,{class:"col-m"},{default:l((()=>[_(h(a.surplus_money),1)])),_:2},1024),c(S,null,{default:l((()=>[_("元")])),_:1})],64)):r("",!0),a.is_floor?(s(),n(u,{key:1},[c(S,null,{default:l((()=>[_("已砍至最低")])),_:1}),c(S,{class:"col-m"},{default:l((()=>[_(h(a.floor_price),1)])),_:2},1024),c(S,null,{default:l((()=>[_("元")])),_:1})],64)):r("",!0)])),_:2},1024),c(w,{class:"task-status dis-flex flex-y-center"},{default:l((()=>[1==a.status?(s(),e(w,{key:0,class:"count-down dis-flex flex-y-center"},{default:l((()=>[c(S,{class:"m-r-6"},{default:l((()=>[_("剩余")])),_:1}),c(I,{date:a.end_time,separator:"colon",theme:"custom"},null,8,["date"])])),_:2},1024)):r("",!0),0==a.status?(s(),e(w,{key:1,class:"task-status__text"},{default:l((()=>[c(S,{class:"col-m"},{default:l((()=>[_(h(a.is_buy?"砍价成功":"已结束"),1)])),_:2},1024)])),_:2},1024)):r("",!0)])),_:2},1024),1==a.status?(s(),e(w,{key:0,class:"opt-touch"},{default:l((()=>[c(w,{class:"touch-btn"},{default:l((()=>[c(S,null,{default:l((()=>[_("继续砍价")])),_:1})])),_:1})])),_:1})):r("",!0)])),_:2},1024)])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1})):r("",!0),c(w,{class:"footer-fixed"},{default:l((()=>[c(w,{class:"footer-container"},{default:l((()=>[c(w,{class:f(["tabbar-item flex-box",{active:0==T.curTab}])},{default:l((()=>[c(w,{class:"tabbar-item-content dis-flex flex-x-center flex-y-center",onClick:t[0]||(t[0]=a=>L.onChangeTab(0))},{default:l((()=>[c(w,{class:"tabbar-item-icon"},{default:l((()=>[c(S,{class:"iconfont icon-shangcheng"})])),_:1}),c(w,{class:"tabbar-item-name"},{default:l((()=>[c(S,null,{default:l((()=>[_("砍价会场")])),_:1})])),_:1})])),_:1})])),_:1},8,["class"]),c(w,{class:"tabbar-item__divider"},{default:l((()=>[c(w,{class:"divider-line"})])),_:1}),c(w,{class:f(["tabbar-item flex-box",{active:1==T.curTab}])},{default:l((()=>[c(w,{class:"tabbar-item-content dis-flex flex-x-center flex-y-center",onClick:t[1]||(t[1]=a=>L.onChangeTab(1))},{default:l((()=>[c(w,{class:"tabbar-item-icon"},{default:l((()=>[c(S,{class:"iconfont icon-sy-yh"})])),_:1}),c(w,{class:"tabbar-item-name"},{default:l((()=>[c(S,null,{default:l((()=>[_("我的砍价")])),_:1})])),_:1})])),_:1})])),_:1},8,["class"])])),_:1})])),_:1})])),_:1},8,["onInit","up","onUp"])])),_:1},8,["style"])}],["__scopeId","data-v-6bb4a67a"]]);export{j as default};
