<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2023 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\service\wxapp;

use app\common\service\BaseService;
use app\store\model\Setting as SettingModel;
use app\store\model\wxapp\Setting as WxappSettingModel;
use app\common\library\helper;
use app\common\library\wechat\WxSubMsg;
use cores\exception\BaseException;
 
/**
 * 小程序订阅消息服务类
 * Class SubMsg
 * @package app\store\service\wxapp
 */
class SubMsg extends BaseService
{
    /* @var $WxSubMsg WxSubMsg 小程序订阅消息api类 */
    private WxSubMsg $WxSubMsg;

    /**
     * 构造方法
     * @param null $storeId
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function __construct($storeId = null)
    {
        parent::__construct();
        // 小程序订阅消息api类
        $wxConfig = WxappSettingModel::getConfigBasic($storeId);
        $this->WxSubMsg = new WxSubMsg($wxConfig['app_id'], $wxConfig['app_secret']);
    }
    public function sendTemplateMessage($params) {
      

        $result = $this->WxSubMsg->sendTemplateMessage($params);
    

        return true;
    }
    /**
     * 一键添加订阅消息
     * @return bool
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function shuttle(): bool
    {
        // 拉取我的模板列表
        $myList = $this->getMyTemplateList();
        // 筛选出未添加的模板
        $addedList = $this->getNotAddedTemplates($myList);
        // 批量添加订阅消息模板
        $newList = $this->onBatchAdd($addedList);
        // 全部模板列表
        $tplList = array_merge($newList, $myList);
        // 保存全部模板id
        return $this->saveAll($tplList);
    }

    /**
     * 保存全部模板id
     * @param $tplList
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function saveAll($tplList): bool
    {
        // 整理模板id
        $data = SettingModel::getItem('submsg');
        foreach ($data as &$group) {
            foreach ($group as &$item) {
                if (!isset($item['title'])) continue;
                $tpl = helper::arraySearch($tplList, 'title', $item['title']);
                $tpl != false && $item['template_id'] = $tpl['priTmplId'];
            }
        }
        // 保存数据
        return (new SettingModel)->edit('submsg', $data);
    }

    /**
     * 批量添加订阅消息模板
     * [并且记录返回的priTmplId]
     * @param $newList
     * @return array
     * @throws BaseException
     */
    private function onBatchAdd($newList): array
    {
        foreach ($newList as &$item) {
            // 请求微信api, 添加模板记录
            $response = $this->WxSubMsg->addTemplate($item['tid'], $item['kidList'], $item['sceneDesc']);
            if ($response === false) {
                throwError("添加模板[{$item['sceneDesc']}]失败：" . $this->WxSubMsg->getError());
            }
            // 记录template_id
            $item['priTmplId'] = $response['priTmplId'];
        }
        return $newList;
    }

    /**
     * 筛选出未添加的模板
     * @param $myList
     * @return array
     */
    private function getNotAddedTemplates($myList): array
    {
        // 所有订阅消息模板列表
        $templateLists = $this->getTemplates();
        if (empty($myList)) return $templateLists;
        // 整理未添加的
        $data = [];
        foreach ($templateLists as $item) {
            if (helper::arraySearch($myList, 'title', $item['title']) === false) {
                $data[] = $item;
            }
        }
        return $data;
    }

    /**
     * 所有订阅消息模板列表
     * @return array
     */
    private function getTemplates(): array
    {
        return [
            // 支付成功通知
            [
                'tid' => 9344,
                'title' => '新订单提醒',
                'kidList' => [1, 2, 4, 3],
                'sceneDesc' => '新订单提醒',
            ],
            // 订单发货通知
            [
                'tid' => 855,
                'title' => '订单发货通知',
                'kidList' => [1, 2, 12, 11, 17],
                'sceneDesc' => '订单发货通知',
            ],
            // 售后状态通知
            [
                'tid' => 5049,
                'title' => '售后状态通知',
                'kidList' => [1, 6, 2, 3, 4],
                'sceneDesc' => '售后状态通知',
            ],
            // 分销商入驻审核通知
            [
                'tid' => 4050,
                'title' => '代理商入驻审核通知',
                'kidList' => [1, 2, 3, 4],
                'sceneDesc' => '分销商入驻审核通知',
            ],
            // 提现成功通知
            [
                'tid' => 2001,
                'title' => '提现成功通知',
                'kidList' => [1, 3, 4],
                'sceneDesc' => '提现成功通知',
            ],
            // 提现失败通知
            [
                'tid' => 3173,
                'title' => '提现失败通知',
                'kidList' => [1, 3, 4],
                'sceneDesc' => '提现失败通知',
            ],

        ];
    }

    /**
     * 拉取我的模板列表
     * @return mixed
     * @throws BaseException
     */
    private function getMyTemplateList()
    {
        $response = $this->WxSubMsg->getTemplateList();
        if ($response === false) {
            throwError('拉取模板列表失败：' . $this->WxSubMsg->getError());
        }
        return $response['data'];
    }
}