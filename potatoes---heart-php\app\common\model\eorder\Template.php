<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\eorder;

use cores\BaseModel;
use app\common\library\helper;
use cores\exception\BaseException;
use think\model\relation\BelongsTo;

/**
 * 模型类：电子面单模板
 * Class Template
 * @package app\common\model\eorder
 */
class Template extends BaseModel
{
    // 定义表名
    protected $name = 'eorder_template';

    // 定义主键
    protected $pk = 'template_id';

    /**
     * 关联物流公司模型
     * @return BelongsTo
     */
    public function express(): BelongsTo
    {
        $module = self::getCalledModule();
        return $this->belongsTo("app\\{$module}\\model\\Express", 'express_id');
    }

    /**
     * 获取器：电子面单配置
     * @param $value
     * @return array
     */
    public function getConfigAttr($value): array
    {
        return helper::jsonDecode($value);
    }

    /**
     * 修改器：电子面单配置
     * @param array $data
     * @return string
     */
    public function setConfigAttr(array $data): string
    {
        return helper::jsonEncode(array_merge([
            'customerName' => '',
            'customerPwd' => '',
            'monthCode' => '',
            'stationName' => '',
            'sendSite' => '',
            'payType' => '',
        ], $data));
    }

    /**
     * 电子面单模板详情
     * @param int $templateId
     * @param array $with
     * @return static|array|null
     */
    public static function detail(int $templateId, array $with = []): ?self
    {
        return self::get($templateId, $with);
    }

    /**
     * 获取电子面单模板
     * @param int $templateId 电子面单模板ID
     * @return Template
     * @throws BaseException
     */
    public function getTemplateInfo(int $templateId): Template
    {
        // 电子面单模板记录
        $template = static::detail($templateId);
        if (empty($template) || $template['is_delete']) {
            throwError('很抱歉，当前不存在电子面单模板');
        }
        return $template;
    }
}