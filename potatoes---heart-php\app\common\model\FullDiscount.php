<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model;

use cores\BaseModel;
use app\common\library\helper;

/**
 * 满额立减活动模型
 * Class FullDiscount
 * @package app\common\model
 */
class FullDiscount extends BaseModel
{
    // 定义表名
    protected $name = 'full_discount';

    // 定义主键
    protected $pk = 'full_discount_id';

    // 支持的优惠叠加
    protected static array $stackingData = ['coupon', 'points', 'full-free'];

    /**
     * 详情记录
     * @param int $fullDiscountId
     * @param array $with
     * @return static|array|null
     */
    public static function detail(int $fullDiscountId, array $with = [])
    {
        return static::get($fullDiscountId, $with);
    }

    /**
     * 获取器：减免折扣率
     * @param $value
     * @return float|int
     */
    public function getDiscountRateAttr($value)
    {
        return !empty($value) ? $value / 10 : '';
    }

    /**
     * 获取器：活动开始时间
     * @param $value
     * @return false|string
     */
    public function getStartTimeAttr($value)
    {
        return format_time($value);
    }

    /**
     * 获取器：活动结束时间
     * @param $value
     * @return false|string
     */
    public function getEndTimeAttr($value)
    {
        return format_time($value);
    }

    /**
     * 获取器：适用商品配置
     * @param $value
     * @return array
     */
    public function getUsableConfigAttr($value): array
    {
        return helper::jsonDecode($value);
    }

    /**
     * 获取器：可叠加的优惠方式
     * @param $value
     * @return array
     */
    public function getStackingAttr($value): array
    {
        return !empty($value) ? helper::jsonDecode($value) : [];
    }

    /**
     * 修改器：减免折扣率
     * @param $value
     * @return float|int
     */
    public function setDiscountRateAttr($value)
    {
        return !empty($value) ? $value * 10 : 0;
    }

    /**
     * 修改器：适用商品配置
     * @param $value
     * @return string
     */
    public function setUsableConfigAttr($value): string
    {
        return helper::jsonEncode($value);
    }

    /**
     * 修改器：可叠加的优惠方式
     * @param $value
     * @return string
     */
    public function setStackingAttr($value): string
    {
        return helper::jsonEncode($value);
    }

    /**
     * 获取禁用的叠加优惠
     * @param array $stacking 满额立减设置的叠加优惠
     * @return array|string[]
     */
    public static function getDisabledStacking(array $stacking): array
    {
        return \array_diff(self::$stackingData, $stacking);
    }

    /**
     * 判断是否支持订单结算中的叠加优惠
     * @param array $stacking 满额立减设置的叠加优惠
     * @param array $enabledStacking 订单中启用的叠加优惠
     * @return bool
     */
    public static function checkOrderStacking(array $stacking, array $enabledStacking): bool
    {
        // 禁用的叠加优惠方式
        $disabled = self::getDisabledStacking($stacking);
        // 判断是否支持订单结算中的叠加优惠
        return empty(\array_intersect($disabled, $enabledStacking));
    }
}
