<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace cores\cloud;

use cores\exception\BaseException;

/**
 * 云服务：收货地址智能解析
 * Class Address
 * @package cores\cloud
 */
class Address
{
    /**
     * 收货地址智能解析
     * @param string $content
     * @return array
     * @throws BaseException
     */
    public function analysis(string $content): array
    {
        $Http = new Http;
        return $Http->request('address/analysis', compact('content'));
    }
}