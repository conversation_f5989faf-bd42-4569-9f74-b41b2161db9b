<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace cores\cloud;

use app\common\library\helper;
use cores\cloud\enum\HttpStatus;
use cores\exception\BaseException;
use Exception;

/**
 * HTTP服务类
 * Class Http
 * @package cores\cloud
 */
class Http
{
    // API地址
    private const BASE_URL = 'https://cloud.yiovo.com/';

    /**
     * 请求云服务API
     * @param string $url
     * @param array $param
     * @return array
     * @throws BaseException
     */
    public function request(string $url, array $param): array
    {
        $result = $this->get(self::BASE_URL . $url, $param);
        $data = helper::jsonDecode($result);
        if ($data['status'] != HttpStatus::SUCCESS) {
            throwError($data['message']);
        }
        return $data['data'];
    }

    /**
     * curl实现get请求
     * @param string $url 请求地址
     * @param array $param
     * @return string $result
     * @throws BaseException
     * @throws Exception
     */
    private function get(string $url, array $param = [])
    {
        // 处理query参数
        if (!empty($param)) {
            $url = $url . '?' . http_build_query($param);
        }
        // 构建curl
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->header());
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $result = curl_exec($ch);
        if ($result === false) {
            throwError('很抱歉，请求云服务失败 ' . curl_error($ch));
        }
        curl_close($ch);
        return $result;
    }

    /**
     * 构建header参数
     * @return string[]
     * @throws BaseException
     */
    private function header(): array
    {
        return [
            'Content-Type: ' . 'application/json;charset=UTF-8',
            'AppID: ' . AuthFile::getAppID(),
            'UserKey: ' . AuthFile::getUserKey(),
            'version: ' . get_version(),
        ];
    }
}