<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\service\message\dealer;

use app\common\service\message\Basics;

/**
 * 消息通知服务 [分销商提现]
 * Class Withdraw
 * @package app\common\service\message\dealer
 */
class Withdraw extends Basics
{
    /**
     * 参数列表
     * @var array
     */
    protected $param = [
        'withdraw' => [],   // 提现记录
    ];

    /**
     * 发送消息通知
     * @param array $param
     * @return mixed|void
     * @throws \cores\exception\BaseException
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function send(array $param)
    {
        // 记录参数
        $this->param = $param;
    }
}