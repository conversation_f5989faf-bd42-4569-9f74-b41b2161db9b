<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\timer\controller\groupon;

use app\timer\controller\Controller;
use app\timer\service\groupon\Task as TaskService;

/**
 * 定时任务: 拼团拼单任务
 * Class Task
 * @package app\timer\controller\groupon
 */
class Task extends Controller
{
    // 当前任务唯一标识
    private string $taskKey = 'GrouponTask';

    // 任务执行间隔时长 (单位:秒)
    protected int $taskExpire = 60 * 10;

    // 当前商城ID
    private int $storeId;

    /**
     * 任务处理
     * @param array $param
     */
    public function handle(array $param)
    {
        ['storeId' => $this->storeId] = $param;
        $this->setInterval($this->storeId, $this->taskKey, $this->taskExpire, function () {
            echo $this->taskKey . PHP_EOL;
            // 拼单模拟成团事件
            $this->taskMockEvent();
            // 拼单失败事件
            $this->taskFailEvent();
            // 拼单完成事件
            $this->taskCompleteEvent();
        });
    }

    /**
     * 拼单模拟成团事件
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function taskMockEvent()
    {
        $service = new TaskService;
        $service->taskMockEvent($this->storeId);
    }

    /**
     * 拼单失败事件 (将拼团失败的订单退款处理)
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function taskFailEvent()
    {
        $service = new TaskService;
        $service->taskFailEvent($this->storeId);
    }

    /**
     * 拼单完成事件 (将已完成拼单的未付款订单取消)
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function taskCompleteEvent()
    {
        $service = new TaskService;
        $service->taskCompleteEvent($this->storeId);
    }
}