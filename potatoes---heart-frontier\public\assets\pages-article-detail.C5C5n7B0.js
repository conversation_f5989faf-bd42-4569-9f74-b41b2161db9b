import{o as t,c as a,w as e,a as s,k as i,n as l,_ as o,l as n,i as c,u as r,r as d,b as h,f,t as m}from"./index-BrSKFjFf.js";import{_}from"./mp-html.DhDdIpoD.js";import{r as p}from"./uni-app.es.BT6Htq7o.js";import{W as u}from"./wxofficial.BsQrFOat.js";import{_ as w}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{d as g}from"./index.CDkYrlzg.js";const S=w({components:{Shortcut:w({props:{right:{type:Number,default:30},bottom:{type:Number,default:100}},data:()=>({isShow:!1,transparent:!0}),methods:{handleToggleShow(){const t=this;t.isShow=!t.isShow,t.transparent=!1},handleItem(t=0){const a=o();this.$navTo(a[t])}}},[["render",function(o,r,d,h,f,m){const _=n,p=c;return t(),a(p,{class:"shortcut",style:l({"--right":`${o.rightPx}rpx`,"--bottom":`${o.bottomPx}rpx`})},{default:e((()=>[s(p,{class:i(["nav-item",[f.isShow?"show_80":f.transparent?"":"hide_80"]]),onClick:r[0]||(r[0]=t=>m.handleItem(0))},{default:e((()=>[s(_,{class:"iconfont icon-home"})])),_:1},8,["class"]),s(p,{class:i(["nav-item",[f.isShow?"show_60":f.transparent?"":"hide_60"]]),onClick:r[1]||(r[1]=t=>m.handleItem(1))},{default:e((()=>[s(_,{class:"iconfont icon-cate"})])),_:1},8,["class"]),s(p,{class:i(["nav-item",[f.isShow?"show_40":f.transparent?"":"hide_40"]]),onClick:r[2]||(r[2]=t=>m.handleItem(2))},{default:e((()=>[s(_,{class:"iconfont icon-cart"})])),_:1},8,["class"]),s(p,{class:i(["nav-item",[f.isShow?"show_20":f.transparent?"":"hide_20"]]),onClick:r[3]||(r[3]=t=>m.handleItem(3))},{default:e((()=>[s(_,{class:"iconfont icon-profile"})])),_:1},8,["class"]),s(p,{class:i(["nav-item nav-item__switch",{shortcut_click_show:f.isShow}]),onClick:r[4]||(r[4]=t=>m.handleToggleShow())},{default:e((()=>[s(_,{class:"iconfont icon-daohang"})])),_:1},8,["class"])])),_:1},8,["style"])}],["__scopeId","data-v-2d199b90"]])},mixins:[u],data:()=>({articleId:null,isLoading:!0,detail:null}),onLoad(t){this.articleId=t.articleId,this.getArticleDetail()},methods:{getArticleDetail(){const t=this;t.isLoading=!0,g(t.articleId).then((a=>{t.detail=a.data.detail,t.setWxofficialShareData()})).finally((()=>t.isLoading=!1))},setWxofficialShareData(){this.updateShareCardData({title:this.detail.title})}},onShareAppMessage(){const t=this,a=t.$getShareUrlParams({articleId:t.articleId});return{title:t.detail.title,path:"/pages/article/detail?"+a}},onShareTimeline(){const t=this,a=t.$getShareUrlParams({articleId:t.articleId});return{title:t.detail.title,path:"/pages/article/detail?"+a}}},[["render",function(i,l,o,u,w,g){const S=n,x=c,I=p(r("mp-html"),_),v=d("shortcut");return w.isLoading?h("",!0):(t(),a(x,{key:0,class:"container b-f p-b"},{default:e((()=>[s(x,{class:"article-title"},{default:e((()=>[s(S,{class:"f-32"},{default:e((()=>[f(m(w.detail.title),1)])),_:1})])),_:1}),s(x,{class:"article-little dis-flex flex-x-between m-top10"},{default:e((()=>[s(x,{class:"article-little__left"},{default:e((()=>[s(S,{class:"article-views f-24 col-8"},{default:e((()=>[f(m(w.detail.show_views)+"次浏览",1)])),_:1})])),_:1}),s(x,{class:"article-little__right"},{default:e((()=>[s(S,{class:"article-views f-24 col-8"},{default:e((()=>[f(m(w.detail.view_time),1)])),_:1})])),_:1})])),_:1}),s(x,{class:"article-content m-top20"},{default:e((()=>[s(I,{content:w.detail.content},null,8,["content"])])),_:1}),s(v)])),_:1}))}],["__scopeId","data-v-821db831"]]);export{S as default};
