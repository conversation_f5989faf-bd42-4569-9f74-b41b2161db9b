(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["market"],{"052a":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form-model",{ref:"myForm",staticClass:"my-form",attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-form-model-item",{attrs:{label:"开启商品推荐",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-radio-group",{model:{value:e.record.enabled,callback:function(t){e.$set(e.record,"enabled",t)},expression:"record.enabled"}},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("p",{staticClass:"form-item-help"},[t("small",[e._v("开启后将在用户端购物车页底部、个人中心页底部显示推荐的商品列表")])])],1),t("a-form-model-item",{attrs:{label:"商品来源",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-radio-group",{model:{value:e.record.params.source,callback:function(t){e.$set(e.record.params,"source",t)},expression:"record.params.source"}},[t("a-radio",{attrs:{value:"auto"}},[e._v("自动获取")]),t("a-radio",{attrs:{value:"choice"}},[e._v("手动选择")])],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"auto"===e.record.params.source,expression:"record.params.source === 'auto'"}]},[t("a-form-model-item",{attrs:{label:"商品分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("SelectCategory",{model:{value:e.record.params.auto.category,callback:function(t){e.$set(e.record.params.auto,"category",t)},expression:"record.params.auto.category"}})],1),t("a-form-model-item",{attrs:{label:"商品排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-radio-group",{model:{value:e.record.params.auto.goodsSort,callback:function(t){e.$set(e.record.params.auto,"goodsSort",t)},expression:"record.params.auto.goodsSort"}},[t("a-radio",{attrs:{value:"all"}},[e._v("默认")]),t("a-radio",{attrs:{value:"sales"}},[e._v("销量")]),t("a-radio",{attrs:{value:"price"}},[e._v("价格")])],1)],1),t("a-form-model-item",{attrs:{label:"显示数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input-number",{attrs:{min:0,max:50,autocomplete:"off"},model:{value:e.record.params.auto.showNum,callback:function(t){e.$set(e.record.params.auto,"showNum",t)},expression:"record.params.auto.showNum"}}),t("span",{staticClass:"ml-10"},[e._v("件")])],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"choice"===e.record.params.source,expression:"record.params.source === 'choice'"}]},[t("a-form-model-item",{attrs:{label:"选择的商品",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("SelectGoods",{attrs:{defaultList:e.choiceGoodsList},model:{value:e.record.params.goodsIds,callback:function(t){e.$set(e.record.params,"goodsIds",t)},expression:"record.params.goodsIds"}})],1)],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("样式设置")]),t("a-form-model-item",{attrs:{label:"标题内容",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{model:{value:e.record.style.title,callback:function(t){e.$set(e.record.style,"title",t)},expression:"record.style.title"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("例如：商品推荐、精选好物、为你推荐")])])],1),t("a-form-model-item",{attrs:{label:"商品分列",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-radio-group",{model:{value:e.record.style.column,callback:function(t){e.$set(e.record.style,"column",t)},expression:"record.style.column"}},[t("a-radio",{attrs:{value:1}},[e._v("单列")]),t("a-radio",{attrs:{value:2}},[e._v("两列")]),t("a-radio",{attrs:{value:3}},[e._v("三列")])],1)],1),t("a-form-model-item",{attrs:{label:"商品内容",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-checkbox-group",{model:{value:e.record.style.show,callback:function(t){e.$set(e.record.style,"show",t)},expression:"record.style.show"}},[t("a-checkbox",{attrs:{value:"goodsName"}},[e._v("商品名称")]),t("a-checkbox",{attrs:{value:"goodsPrice"}},[e._v("商品价格")]),t("a-checkbox",{attrs:{value:"linePrice"}},[e._v("划线价格")]),t("a-checkbox",{directives:[{name:"show",rawName:"v-show",value:1===e.record.style.column,expression:"record.style.column === 1"}],attrs:{value:"sellingPoint"}},[e._v("商品卖点")]),t("a-checkbox",{directives:[{name:"show",rawName:"v-show",value:1===e.record.style.column,expression:"record.style.column === 1"}],attrs:{value:"goodsSales"}},[e._v("商品销量")])],1)],1),t("a-form-model-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary",loading:e.confirmLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)],1)},o=[],i=r("c7eb"),n=r("1da1"),s=(r("d3b7"),r("ddb0"),r("2ef0")),l=r("f585"),u=r("d084"),c=r("2af9"),d=r("35c4"),m={enabled:1,params:{source:"auto",auto:{category:0,goodsSort:"all",showNum:6},goodsIds:[]},style:{title:"",column:2,show:["goodsName","goodsPrice","linePrice","sellingPoint","goodsSales"]}},p={components:{SelectGoods:c["g"],SelectCategory:c["e"]},data:function(){return{key:d["a"].RECOMMENDED.value,labelCol:{span:4},wrapperCol:{span:12},isLoading:!1,confirmLoading:!1,record:Object(s["cloneDeep"])(m),choiceGoodsList:[]}},created:function(){var e=this;return Object(n["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getDetail();case 2:return t.next=4,e.getChoiceGoodsList();case 4:case"end":return t.stop()}}),t)})))()},methods:{getDetail:function(){var e=this;return Object(n["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,l["a"](e.key).then((function(t){return e.record=t.data.values})).finally((function(){return e.isLoading=!1}));case 3:case"end":return t.stop()}}),t)})))()},getChoiceGoodsList:function(){var e=this;return Object(n["a"])(Object(i["a"])().mark((function t(){var r;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r=e.record.params.goodsIds,!(r.length>0)){t.next=5;break}return e.isLoading=!0,t.next=5,u["g"](r).then((function(t){return e.choiceGoodsList=t.data.list})).finally((function(){return e.isLoading=!1}));case 5:case"end":return t.stop()}}),t)})))()},handleSubmit:function(e){var t=this;this.confirmLoading=!0,l["b"](this.key,{form:this.record}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(e){return t.confirmLoading=!1}))}}},f=p,v=(r("7e12f"),r("2877")),h=Object(v["a"])(f,a,o,!1,null,"58595866",null);t["default"]=h.exports},"053e":function(e,t,r){},"0ab7":function(e,t,r){},"0b60":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"优惠券名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入优惠券名称"}})],1),t("a-form-item",{attrs:{label:"优惠券类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["coupon_type",{initialValue:10,rules:[{required:!0}]}],expression:"['coupon_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("满减券")]),t("a-radio",{attrs:{value:20}},[e._v("折扣券")])],1)],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("coupon_type")==e.CouponTypeEnum.FULL_DISCOUNT.value,expression:"form.getFieldValue('coupon_type') ==  CouponTypeEnum.FULL_DISCOUNT.value"}],attrs:{label:"减免金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["reduce_price",{rules:[{required:!0,message:"请输入减免金额"}]}],expression:"['reduce_price', { rules: [{ required: true, message: '请输入减免金额' }] }]"}],attrs:{min:.01,precision:2}}),t("span",{staticClass:"ml-5"},[e._v("元")])],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("coupon_type")==e.CouponTypeEnum.DISCOUNT.value,expression:"form.getFieldValue('coupon_type') == CouponTypeEnum.DISCOUNT.value"}],attrs:{label:"折扣率",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["discount",{initialValue:9.9,rules:[{required:!0,message:"请输入折扣率"}]}],expression:"['discount', { initialValue: 9.9, rules: [{ required: true, message: '请输入折扣率' }] }]"}],attrs:{min:0,max:9.9,precision:1}}),t("span",{staticClass:"ml-5"},[e._v("%")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("折扣率范围 0-9.9，8代表打8折，0代表不折扣")])])],1),t("a-form-item",{attrs:{label:"最低消费金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["min_price",{rules:[{required:!0,message:"请输入最低消费金额"}]}],expression:"['min_price', { rules: [{ required: true, message: '请输入最低消费金额' }] }]"}],attrs:{min:1,precision:2}}),t("span",{staticClass:"ml-5"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"到期类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["expire_type",{initialValue:10,rules:[{required:!0}]}],expression:"['expire_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("领取后生效")]),t("a-radio",{attrs:{value:20}},[e._v("固定时间")])],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:10==e.form.getFieldValue("expire_type"),expression:"form.getFieldValue('expire_type') == 10"}],staticClass:"expire_type-10"},[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["expire_day",{initialValue:7,rules:[{required:!0,message:"请输入有效期天数"}]}],expression:"['expire_day', { initialValue: 7, rules: [{ required: true, message: '请输入有效期天数' }] }]"}],attrs:{addonBefore:"有效期",addonAfter:"天",inputProps:{min:1,precision:0}}})],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:20==e.form.getFieldValue("expire_type"),expression:"form.getFieldValue('expire_type') == 20"}],staticClass:"expire_type-20"},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime",{initialValue:e.defaultDate,rules:[{required:!0,message:"请选择有效期范围"}]}],expression:"['betweenTime', { initialValue: defaultDate, rules: [{ required: true, message: '请选择有效期范围' }] }]"}],attrs:{format:"YYYY-MM-DD"}})],1)],1),t("a-form-item",{attrs:{label:"券适用范围",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["apply_range",{initialValue:10,rules:[{required:!0}]}],expression:"['apply_range', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("全场通用")]),t("a-radio",{attrs:{value:20}},[e._v("指定商品")])],1),20==e.form.getFieldValue("apply_range")?t("a-form-item",[t("SelectGoods",{directives:[{name:"decorator",rawName:"v-decorator",value:["apply_range_config.applyGoodsIds",{rules:[{required:!0,message:"请选择指定的商品"}]}],expression:"['apply_range_config.applyGoodsIds', { rules: [{ required: true, message: '请选择指定的商品' }] }]"}],attrs:{defaultList:e.containGoodsList}})],1):e._e()],1),t("a-form-item",{attrs:{label:"发放总数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["total_num",{initialValue:-1,rules:[{required:!0,message:"请输入发放总数量"}]}],expression:"['total_num', { initialValue: -1, rules: [{ required: true, message: '请输入发放总数量' }] }]"}],attrs:{min:-1,precision:0}}),t("span",{staticClass:"ml-5"},[e._v("张")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("发放的优惠券总数量，-1为不限制")])])],1),t("a-form-item",{attrs:{label:"显示状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("显示")]),t("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1),t("p",{staticClass:"form-item-help"},[t("small",[e._v("如果设为隐藏将不会展示在用户端页面")])])],1),t("a-form-item",{attrs:{label:"优惠券描述",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["describe"],expression:"['describe']"}],attrs:{autoSize:{minRows:4}}})],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1),t("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1)],1)],1)},o=[],i=r("c7eb"),n=r("1da1"),s=(r("d3b7"),r("c1df")),l=r.n(s),u=r("2ef0"),c=r("39ad9"),d=r("d084"),m=r("ca00"),p=r("2af9"),f=r("8fa3"),v={components:{SelectGoods:p["g"],InputNumberGroup:p["c"]},data:function(){return{ApplyRangeEnum:f["a"],CouponTypeEnum:f["b"],ExpireTypeEnum:f["c"],isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},form:this.$form.createForm(this),defaultDate:[l()(),l()()],couponId:null,record:{},containGoodsList:[]}},created:function(){var e=this;return Object(n["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.couponId=e.$route.query.couponId,t.next=3,e.getDetail();case 3:return t.next=5,e.getContainGoodsList();case 5:case"end":return t.stop()}}),t)})))()},methods:{getDetail:function(){var e=this;return Object(n["a"])(Object(i["a"])().mark((function t(){var r;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=e.couponId,e.isLoading=!0,t.next=4,c["c"]({couponId:r}).then((function(t){e.record=t.data.detail,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}));case 4:case"end":return t.stop()}}),t)})))()},getContainGoodsList:function(){var e=this;return Object(n["a"])(Object(i["a"])().mark((function t(){var r,a;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r=e.record,a=Object(u["get"])(r,"apply_range_config.applyGoodsIds"),void 0===a||!a.length){t.next=6;break}return e.isLoading=!0,t.next=6,d["g"](a).then((function(t){e.containGoodsList=t.data.list})).finally((function(t){e.isLoading=!1}));case 6:case"end":return t.stop()}}),t)})))()},setFieldsValue:function(){var e=this,t=this.record,r=this.form,a=this.$nextTick;!Object(m["f"])(r.getFieldsValue())&&a((function(){var a=Object(u["pick"])(t,["name","coupon_type","reduce_price","discount","min_price","status","expire_type","expire_day","apply_range","total_num","describe","sort"]);a.betweenTime=e.getBetweenTime(t),r.setFieldsValue(a)}))},getBetweenTime:function(e){return e.expire_type===f["c"].FIXED_TIME.value?[l()(new Date(e.start_time)),l()(new Date(e.end_time))]:this.defaultDate},handleSubmit:function(e){e.preventDefault();var t=this.form.validateFields,r=this.onFormSubmit;t((function(e,t){!e&&r(t)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,this.isBtnLoading=!0,c["d"]({couponId:this.couponId,form:e}).then((function(e){t.$message.success(e.message,1.5),setTimeout((function(){t.$router.push("./index")}),1500)})).catch((function(){t.isBtnLoading=!1})).finally((function(){return t.isLoading=!1}))}}},h=v,b=(r("5a40"),r("2877")),g=Object(b["a"])(h,a,o,!1,null,"5cf7d6db",null);t["default"]=g.exports},"0e9c":function(e,t,r){"use strict";r.r(t);r("b0c0");var a=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"是否开启满额包邮",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_open",{rules:[{required:!0}]}],expression:"['is_open', { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),t("a-form-item",{attrs:{label:"单笔订单满",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["money",{rules:[{required:!0,message:"请输入包邮的订单额度"}]}],expression:"['money', { rules: [{ required: true, message: '请输入包邮的订单额度' }] }]"}],attrs:{min:0,precision:2}}),t("span",{staticClass:"ml-10"},[e._v("元包邮")]),t("div",{staticClass:"form-item-help"},[t("small",[e._v("如设置0为全场包邮")])])],1),t("a-form-item",{attrs:{label:"不参与包邮的商品",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("SelectGoods",{directives:[{name:"decorator",rawName:"v-decorator",value:["excludedGoodsIds"],expression:"['excludedGoodsIds']"}],attrs:{defaultList:e.excludedGoodsList}})],1),t("a-form-item",{attrs:{label:"不参与包邮的地区",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-button",{on:{click:e.handleAreasModal}},[e._v("选择地区")]),t("p",{staticClass:"content"},e._l(e.excludedRegions.selectedText,(function(r,a){return t("span",{key:a},[t("span",[e._v(e._s(r.name))]),r.citys.length?[t("span",[e._v("[")]),e._l(r.citys,(function(a,o){return t("span",{key:o,staticClass:"city-name"},[e._v(e._s(a.name)+e._s(r.citys.length>o+1?"、":""))])})),t("span",[e._v("]")])]:e._e(),t("span",{staticClass:"mr-5"})],2)})),0)],1),t("a-form-item",{attrs:{label:"满额包邮说明",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["describe",{rules:[{required:!0,message:"请输入满额包邮说明"}]}],expression:"['describe', { rules: [{ required: true, message: '请输入满额包邮说明' }] }]"}],attrs:{autoSize:{minRows:4}}})],1),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1),t("AreasModal",{ref:"AreasModal",on:{handleSubmit:e.handleAreaSubmit}})],1)],1)},o=[],i=r("c7eb"),n=r("1da1"),s=(r("d3b7"),r("ddb0"),r("88bc")),l=r.n(s),u=r("f585"),c=r("d084"),d=r("2af9"),m=r("fd0d"),p={components:{SelectGoods:d["g"],AreasModal:m["a"]},data:function(){return{key:"full_free",labelCol:{span:4},wrapperCol:{span:12},isLoading:!1,form:this.$form.createForm(this),record:{},excludedRegions:{cityIds:[],selectedText:[]},excludedGoodsList:[]}},created:function(){var e=this;return Object(n["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getDetail();case 2:return t.next=4,e.getExcludedGoodsList();case 4:case"end":return t.stop()}}),t)})))()},methods:{getDetail:function(){var e=this;return Object(n["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,u["a"](e.key).then((function(t){e.record=t.data.values,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}));case 3:case"end":return t.stop()}}),t)})))()},getExcludedGoodsList:function(){var e=this;return Object(n["a"])(Object(i["a"])().mark((function t(){var r;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r=e.record.excludedGoodsIds,!(r.length>0)){t.next=5;break}return e.isLoading=!0,t.next=5,c["g"](r).then((function(t){e.excludedGoodsList=t.data.list})).finally((function(t){e.isLoading=!1}));case 5:case"end":return t.stop()}}),t)})))()},setFieldsValue:function(){var e=this,t=this.record,r=this.$nextTick,a=this.form.setFieldsValue;r((function(){e.excludedRegions=t.excludedRegions,a(l()(t,["is_open","money","describe"]))}))},handleAreasModal:function(){this.$refs.AreasModal.handle({},this.excludedRegions.cityIds)},handleAreaSubmit:function(e){this.excludedRegions={cityIds:e.selectedCityIds,selectedText:e.selectedText}},handleSubmit:function(e){var t=this;e.preventDefault();var r=this.form.validateFields,a=this.excludedRegions;r((function(e,r){e||(r.excludedRegions=a,t.onFormSubmit(r))}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,u["b"](this.key,{form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},f=p,v=(r("e0ac"),r("2877")),h=Object(v["a"])(f,a,o,!1,null,"c094d56a",null);t["default"]=h.exports},"164a":function(e,t,r){"use strict";r.r(t);r("ac1f"),r("841c"),r("b0c0");var a=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-alert",{attrs:{message:"注：优惠券只能抵扣商品金额，最多优惠到0.01元，不能抵扣运费",banner:""}}),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{attrs:{span:6}},[e.$auth("/market/coupon/create")?t("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]):e._e()],1),t("a-col",{attrs:{span:8,offset:10}},[t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px",float:"right"},attrs:{placeholder:"请输入优惠券名称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"coupon_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"coupon_type",fn:function(r){return[t("a-tag",[e._v(e._s(e.CouponTypeEnum[r].name))])]}},{key:"min_price",fn:function(r){return[t("p",{staticClass:"c-p"},[e._v(e._s(r))])]}},{key:"discount",fn:function(r){return[10==r.coupon_type?[t("span",[e._v("立减")]),t("span",{staticClass:"c-p mlr-2"},[e._v(e._s(r.reduce_price))]),t("span",[e._v("元")])]:e._e(),20==r.coupon_type?[t("span",[e._v("打")]),t("span",{staticClass:"c-p mlr-2"},[e._v(e._s(r.discount))]),t("span",[e._v("折")])]:e._e()]}},{key:"duetime",fn:function(r){return[10==r.expire_type?[t("span",[e._v("领取")]),t("span",{staticClass:"c-p mlr-2"},[e._v(e._s(r.expire_day))]),t("span",[e._v("天内有效")])]:e._e(),20==r.expire_type?[t("span",[e._v(e._s(r.start_time)+" ~ "+e._s(r.end_time))])]:e._e()]}},{key:"status",fn:function(r){return[t("a-tag",{attrs:{color:r?"green":""}},[e._v(e._s(r?"显示":"隐藏"))])]}},{key:"action",fn:function(r){return t("span",{staticClass:"actions"},[e.$auth("/market/coupon/update")?t("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]):e._e(),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")])])}}])})],1)},o=[],i=r("5530"),n=(r("d3b7"),r("39ad9")),s=r("2af9"),l=r("8fa3"),u={name:"Index",components:{STable:s["d"]},data:function(){var e=this;return{queryParam:{},ApplyRangeEnum:l["a"],CouponTypeEnum:l["b"],ExpireTypeEnum:l["c"],isLoading:!1,columns:[{title:"优惠券ID",dataIndex:"coupon_id"},{title:"优惠券名称",dataIndex:"name"},{title:"优惠券类型",dataIndex:"coupon_type",scopedSlots:{customRender:"coupon_type"}},{title:"最低消费金额 (元)",dataIndex:"min_price",scopedSlots:{customRender:"min_price"}},{title:"优惠方式",scopedSlots:{customRender:"discount"}},{title:"已发放/领取数量",dataIndex:"receive_num"},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"操作",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(t){return n["f"](Object(i["a"])(Object(i["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$router.push("./create")},handleEdit:function(e){this.$router.push({path:"./update",query:{couponId:e.coupon_id}})},handleDelete:function(e){var t=this,r=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return n["b"]({couponId:e.coupon_id}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return r.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},c=u,d=r("2877"),m=Object(d["a"])(c,a,o,!1,null,null,null);t["default"]=m.exports},"1da1":function(e,t,r){"use strict";r.d(t,"a",(function(){return o}));r("d3b7");function a(e,t,r,a,o,i,n){try{var s=e[i](n),l=s.value}catch(u){return void r(u)}s.done?t(l):Promise.resolve(l).then(a,o)}function o(e){return function(){var t=this,r=arguments;return new Promise((function(o,i){var n=e.apply(t,r);function s(e){a(n,o,i,s,l,"next",e)}function l(e){a(n,o,i,s,l,"throw",e)}s(void 0)}))}}},2640:function(e,t,r){"use strict";r("9aea")},3095:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"是否开启会员充值",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_entrance",{rules:[{required:!0}]}],expression:"['is_entrance', { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("如设置关闭则用户端不显示充值按钮")])])],1),t("a-form-item",{attrs:{label:"充值自定义金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_custom",{rules:[{required:!0}]}],expression:"['is_custom', { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("允许")]),t("a-radio",{attrs:{value:0}},[e._v("不允许")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("是否允许用户填写自定义的充值金额")])])],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:1==e.form.getFieldValue("is_custom"),expression:"form.getFieldValue('is_custom') == 1"}],attrs:{label:"最低充值金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["lowest_money",{rules:[{required:!0,message:"请输入最低充值金额"}]}],expression:"['lowest_money', { rules: [{ required: true, message: '请输入最低充值金额' }] }]"}],attrs:{min:.01,precision:2}}),t("span",{staticClass:"ml-10"},[e._v("元")]),t("div",{staticClass:"form-item-help"},[t("small",[e._v("低于该设定金额时不允许充值")])])],1),t("a-form-item",{attrs:{label:"自动匹配套餐",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_match_plan",{rules:[{required:!0}]}],expression:"['is_match_plan', { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("充值自定义金额时 是否自动匹配充值套餐，如不开启则不参与套餐金额赠送")])])],1),t("a-form-item",{attrs:{label:"充值说明",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["describe",{rules:[{required:!0,message:"请输入充值说明"}]}],expression:"['describe', { rules: [{ required: true, message: '请输入充值说明' }] }]"}],attrs:{autoSize:{minRows:4}}})],1),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},o=[],i=(r("d3b7"),r("ddb0"),r("88bc")),n=r.n(i),s=r("f585"),l={components:{},data:function(){return{key:"recharge",labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),record:{}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,s["a"](this.key).then((function(t){e.record=t.data.values,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,t=this.$nextTick,r=this.form.setFieldsValue;t((function(){r(n()(e,["is_entrance","is_custom","lowest_money","is_match_plan","describe"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var r=this.form.validateFields;r((function(e,r){!e&&t.onFormSubmit(r)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,s["b"](this.key,{form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},u=l,c=(r("d36d"),r("2877")),d=Object(c["a"])(u,a,o,!1,null,"6c768cd3",null);t["default"]=d.exports},"35c4":function(e,t,r){"use strict";r.d(t,"a",(function(){return o}));var a=r("5c06"),o=new a["a"]([{key:"DELIVERY",name:"配送设置",value:"delivery"},{key:"TRADE",name:"交易设置",value:"trade"},{key:"STORAGE",name:"上传设置",value:"storage"},{key:"PRINTER",name:"小票打印",value:"printer"},{key:"FULL_FREE",name:"满额包邮设置",value:"full_free"},{key:"RECHARGE",name:"充值设置",value:"recharge"},{key:"POINTS",name:"积分设置",value:"points"},{key:"SUBMSG",name:"订阅消息设置",value:"submsg"},{key:"APP_THEME",name:"店铺页面风格",value:"app_theme"},{key:"PAGE_CATEGORY_TEMPLATE",name:"分类页模板",value:"page_category_template"},{key:"RECOMMENDED",name:"商品推荐设置",value:"recommended"},{key:"CUSTOMER",name:"商城客服设置",value:"customer"}])},"374b":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",{staticClass:"row-item-search"},[t("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[t("a-form-item",{attrs:{label:"会员昵称"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["search"],expression:"['search']"}],attrs:{placeholder:"请输入会员昵称"}})],1),t("a-form-item",{attrs:{label:"变动时间"}},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime"],expression:"['betweenTime']"}],attrs:{format:"YYYY-MM-DD"}})],1),t("a-form-item",{staticClass:"search-btn"},[t("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"log_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"user",fn:function(e){return t("span",{},[t("UserItem",{attrs:{user:e}})],1)}},{key:"value",fn:function(r){return t("span",{},[t("p",{staticClass:"c-p"},[e._v(e._s(r>0?"+":"")+e._s(r))])])}}])})],1)},o=[],i=r("5530"),n=r("b775"),s={log:"/market.points/log"};function l(e){return Object(n["b"])({url:s.log,method:"get",params:e})}var u=r("ab09"),c=r("fe7e"),d={name:"Index",components:{STable:u["b"],UserItem:u["c"]},data:function(){var e=this;return{SceneEnum:c["a"],searchForm:this.$form.createForm(this),queryParam:{},isLoading:!1,columns:[{title:"ID",dataIndex:"log_id"},{title:"会员信息",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"变动数量",dataIndex:"value",scopedSlots:{customRender:"value"}},{title:"描述/说明",dataIndex:"describe"},{title:"管理员备注",dataIndex:"remark"},{title:"变动时间",dataIndex:"create_time"}],loadData:function(t){return l(Object(i["a"])(Object(i["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},handleSearch:function(e){var t=this;e.preventDefault(),this.searchForm.validateFields((function(e,r){e||(t.queryParam=Object(i["a"])(Object(i["a"])({},t.queryParam),r),t.handleRefresh(!0))}))}}},m=d,p=(r("701c"),r("2877")),f=Object(p["a"])(m,a,o,!1,null,"0d4db7ff",null);t["default"]=f.exports},"3a16":function(e,t,r){},"3d2f":function(e,t,r){"use strict";r.r(t);r("b0c0");var a=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",{staticClass:"row-item-search"},[t("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[t("a-form-item",{attrs:{label:"优惠券名称"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["couponName"],expression:"['couponName']"}],attrs:{placeholder:"请输入优惠券名称"}})],1),t("a-form-item",{attrs:{label:"会员昵称"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["nickName"],expression:"['nickName']"}],attrs:{placeholder:"请输入会员昵称"}})],1),t("a-form-item",{attrs:{label:"领取时间"}},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime"],expression:"['betweenTime']"}],attrs:{format:"YYYY-MM-DD"}})],1),t("a-form-item",{staticClass:"search-btn"},[t("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"user_coupon_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"user",fn:function(e){return[t("UserItem",{attrs:{user:e}})]}},{key:"coupon_type",fn:function(r){return[t("a-tag",[e._v(e._s(e.CouponTypeEnum[r].name))])]}},{key:"min_price",fn:function(r){return[t("p",{staticClass:"c-p"},[e._v(e._s(r))])]}},{key:"discount",fn:function(r){return[r.coupon_type==e.CouponTypeEnum.FULL_DISCOUNT.value?[t("span",[e._v("立减")]),t("span",{staticClass:"c-p mlr-2"},[e._v(e._s(r.reduce_price))]),t("span",[e._v("元")])]:e._e(),r.coupon_type==e.CouponTypeEnum.DISCOUNT.value?[t("span",[e._v("打")]),t("span",{staticClass:"c-p mlr-2"},[e._v(e._s(r.discount))]),t("span",[e._v("折")])]:e._e()]}},{key:"duetime",fn:function(r){return[10==r.expire_type?[t("span",[e._v("领取")]),t("span",{staticClass:"c-p mlr-2"},[e._v(e._s(r.expire_day))]),t("span",[e._v("天内有效")])]:e._e(),20==r.expire_type?[t("span",[e._v(e._s(r.start_time)+" ~ "+e._s(r.end_time))])]:e._e()]}}])})],1)},o=[],i=r("5530"),n=r("39ad9"),s=r("ab09"),l=r("8fa3"),u={name:"Index",components:{STable:s["b"],UserItem:s["c"]},data:function(){var e=this;return{searchForm:this.$form.createForm(this),queryParam:{},CouponTypeEnum:l["b"],isLoading:!1,columns:[{title:"ID",dataIndex:"user_coupon_id"},{title:"会员信息",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"优惠券名称",dataIndex:"name"},{title:"优惠券类型",dataIndex:"coupon_type",scopedSlots:{customRender:"coupon_type"}},{title:"最低消费金额 (元)",dataIndex:"min_price",scopedSlots:{customRender:"min_price"}},{title:"优惠方式",scopedSlots:{customRender:"discount"}},{title:"有效期",scopedSlots:{customRender:"duetime"}},{title:"领取时间",dataIndex:"create_time"}],loadData:function(t){return n["g"](Object(i["a"])(Object(i["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},handleSearch:function(e){var t=this;e.preventDefault(),this.searchForm.validateFields((function(e,r){e||(t.queryParam=Object(i["a"])(Object(i["a"])({},t.queryParam),r),t.handleRefresh(!0))}))}}},c=u,d=r("2877"),m=Object(d["a"])(c,a,o,!1,null,null,null);t["default"]=m.exports},"577f":function(e,t,r){},"5a40":function(e,t,r){"use strict";r("053e")},"6ad7":function(e,t,r){"use strict";r.r(t);r("ac1f"),r("841c");var a=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{attrs:{span:6}},[t("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),t("a-col",{attrs:{span:8,offset:10}},[t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px",float:"right"},attrs:{placeholder:"请输入套餐名称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"plan_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"money",fn:function(r){return t("span",{},[t("p",{staticClass:"c-p"},[e._v(e._s(r))])])}},{key:"gift_money",fn:function(r){return t("span",{},[t("p",{staticClass:"c-p"},[e._v(e._s(r))])])}},{key:"action",fn:function(r,a){return t("span",{},[t("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(t){return e.handleEdit(a)}}},[e._v("编辑")]),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(a)}}},[e._v("删除")])])}}])}),t("AddForm",{ref:"AddForm",on:{handleSubmit:e.handleRefresh}}),t("EditForm",{ref:"EditForm",on:{handleSubmit:e.handleRefresh}})],1)},o=[],i=r("5530"),n=(r("d3b7"),r("b775")),s={list:"/market.recharge.plan/list",add:"/market.recharge.plan/add",edit:"/market.recharge.plan/edit",delete:"/market.recharge.plan/delete"};function l(e){return Object(n["b"])({url:s.list,method:"get",params:e})}function u(e){return Object(n["b"])({url:s.add,method:"post",data:e})}function c(e){return Object(n["b"])({url:s.edit,method:"post",data:e})}function d(e){return Object(n["b"])({url:s.delete,method:"post",data:e})}var m=r("2af9"),p=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"套餐名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"便于后台查找，例如：充100元送10元"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["plan_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['plan_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),t("a-form-item",{attrs:{label:"充值金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"会员充值并支付的金额"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["money",{rules:[{required:!0,message:"请输入充值的金额"}]}],expression:"['money', { rules: [{ required: true, message: '请输入充值的金额' }] }]"}],attrs:{min:.01}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"赠送金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"充值成功后赠送的金额，不能大于充值金额"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["gift_money",{rules:[{required:!0,message:"请输入赠送的金额"}]}],expression:"['gift_money', { rules: [{ required: true, message: '请输入赠送的金额' }] }]"}],attrs:{min:0}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},f=[],v={data:function(){return{title:"新增充值套餐",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{add:function(){this.visible=!0},handleSubmit:function(e){var t=this;e.preventDefault();var r=this.form.validateFields;r((function(e,r){!e&&t.onFormSubmit(r)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,u({form:e}).then((function(r){t.$message.success(r.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){return t.confirmLoading=!1}))}}},h=v,b=r("2877"),g=Object(b["a"])(h,p,f,!1,null,null,null),_=g.exports,C=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"套餐名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"便于后台查找，例如：充100元送10元"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["plan_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['plan_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),t("a-form-item",{attrs:{label:"充值金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"会员充值并支付的金额"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["money",{rules:[{required:!0,message:"请输入充值的金额"}]}],expression:"['money', { rules: [{ required: true, message: '请输入充值的金额' }] }]"}],attrs:{min:.01}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"赠送金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"充值成功后赠送的金额，不能大于充值金额"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["gift_money",{rules:[{required:!0,message:"请输入赠送的金额"}]}],expression:"['gift_money', { rules: [{ required: true, message: '请输入赠送的金额' }] }]"}],attrs:{min:0}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},w=[],y=r("88bc"),x=r.n(y),L={data:function(){return{title:"编辑充值套餐",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{edit:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this,t=this.form.setFieldsValue;this.$nextTick((function(){t(x()(e.record,["plan_name","money","gift_money","sort"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var r=this.form.validateFields;r((function(e,r){!e&&t.onFormSubmit(r)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,c({planId:this.record.plan_id,form:e}).then((function(r){t.$message.success(r.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){return t.confirmLoading=!1}))}}},q=L,k=Object(b["a"])(q,C,w,!1,null,null,null),S=k.exports,N={name:"Index",components:{STable:m["d"],AddForm:_,EditForm:S},data:function(){var e=this;return{queryParam:{},isLoading:!1,columns:[{title:"套餐ID",dataIndex:"plan_id"},{title:"套餐名称",dataIndex:"plan_name"},{title:"充值金额 (元)",dataIndex:"money",scopedSlots:{customRender:"money"}},{title:"赠送金额 (元)",dataIndex:"gift_money",scopedSlots:{customRender:"gift_money"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(t){return l(Object(i["a"])(Object(i["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleDelete:function(e){var t=this,r=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return d({planId:e.plan_id}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return r.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},F=N,O=Object(b["a"])(F,a,o,!1,null,null,null);t["default"]=O.exports},"701c":function(e,t,r){"use strict";r("0ab7")},"7b6b":function(e,t,r){},"7e12f":function(e,t,r){"use strict";r("3a16")},"85d6":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v("优惠券 - 手动发放")]),t("a-alert",{staticClass:"mb-30",attrs:{message:"注：选择指定的优惠券（仅一张）发放给指定的用户；发放成功后无法撤销，请谨慎操作",banner:""}}),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form-model",{ref:"myForm",staticClass:"my-form",attrs:{model:e.form,labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-form-model-item",{attrs:{label:"选择指定优惠券",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"couponId",rules:{required:!0,message:"请选择优惠券"}}},[t("SelectCoupon",{attrs:{multiple:!1},model:{value:e.form.couponId,callback:function(t){e.$set(e.form,"couponId",t)},expression:"form.couponId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("请先确保优惠券剩余数量充足，否则将会导致发送失败")])])],1),t("a-form-model-item",{attrs:{label:"发送类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"type",rules:{required:!0,message:"请选择发送类型"}}},[t("a-radio-group",{model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},[t("a-radio",{attrs:{value:10}},[e._v("指定的会员")])],1)],1),t("a-form-model-item",{attrs:{label:"选择会员",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"target.userIds",rules:{required:!0,message:"请选择会员"}}},[t("SelectUser",{attrs:{multiple:!0},model:{value:e.form.target.userIds,callback:function(t){e.$set(e.form.target,"userIds",t)},expression:"form.target.userIds"}})],1),t("a-form-model-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary",loading:e.confirmLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)],1)},o=[],i=(r("d3b7"),r("2ef0")),n=r("2af9"),s=r("39ad9"),l={couponId:null,type:10,target:{userIds:[]}},u={components:{SelectCoupon:n["f"],SelectUser:n["l"]},data:function(){return{labelCol:{span:4},wrapperCol:{span:12},isLoading:!1,confirmLoading:!1,form:Object(i["cloneDeep"])(l)}},created:function(){},methods:{handleSubmit:function(e){var t=this,r=this;r.$refs.myForm.validate((function(e){e&&(t.confirmLoading=!0,s["e"]({form:r.form}).then((function(e){r.$message.success(e.message,1.5),r.form=Object(i["cloneDeep"])(l),setTimeout((function(){return t.$router.push("./receive/index")}),1200)})).finally((function(e){return r.confirmLoading=!1})))}))}}},c=u,d=(r("a40f"),r("2877")),m=Object(d["a"])(c,a,o,!1,null,"1f61d62d",null);t["default"]=m.exports},"88bc":function(e,t,r){(function(t){var r=1/0,a=9007199254740991,o="[object Arguments]",i="[object Function]",n="[object GeneratorFunction]",s="[object Symbol]",l="object"==typeof t&&t&&t.Object===Object&&t,u="object"==typeof self&&self&&self.Object===Object&&self,c=l||u||Function("return this")();function d(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}function m(e,t){var r=-1,a=e?e.length:0,o=Array(a);while(++r<a)o[r]=t(e[r],r,e);return o}function p(e,t){var r=-1,a=t.length,o=e.length;while(++r<a)e[o+r]=t[r];return e}var f=Object.prototype,v=f.hasOwnProperty,h=f.toString,b=c.Symbol,g=f.propertyIsEnumerable,_=b?b.isConcatSpreadable:void 0,C=Math.max;function w(e,t,r,a,o){var i=-1,n=e.length;r||(r=q),o||(o=[]);while(++i<n){var s=e[i];t>0&&r(s)?t>1?w(s,t-1,r,a,o):p(o,s):a||(o[o.length]=s)}return o}function y(e,t){return e=Object(e),x(e,t,(function(t,r){return r in e}))}function x(e,t,r){var a=-1,o=t.length,i={};while(++a<o){var n=t[a],s=e[n];r(s,n)&&(i[n]=s)}return i}function L(e,t){return t=C(void 0===t?e.length-1:t,0),function(){var r=arguments,a=-1,o=C(r.length-t,0),i=Array(o);while(++a<o)i[a]=r[t+a];a=-1;var n=Array(t+1);while(++a<t)n[a]=r[a];return n[t]=i,d(e,this,n)}}function q(e){return N(e)||S(e)||!!(_&&e&&e[_])}function k(e){if("string"==typeof e||$(e))return e;var t=e+"";return"0"==t&&1/e==-r?"-0":t}function S(e){return O(e)&&v.call(e,"callee")&&(!g.call(e,"callee")||h.call(e)==o)}var N=Array.isArray;function F(e){return null!=e&&j(e.length)&&!I(e)}function O(e){return E(e)&&F(e)}function I(e){var t=V(e)?h.call(e):"";return t==i||t==n}function j(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=a}function V(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function E(e){return!!e&&"object"==typeof e}function $(e){return"symbol"==typeof e||E(e)&&h.call(e)==s}var D=L((function(e,t){return null==e?{}:y(e,m(w(t,1),k))}));e.exports=D}).call(this,r("c8ba"))},"9aea":function(e,t,r){},"9ed8":function(e,t,r){"use strict";r("577f")},a40f:function(e,t,r){"use strict";r("7b6b")},ae32:function(e,t,r){},c10a:function(e,t,r){},c24e:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"优惠券名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入优惠券名称"}})],1),t("a-form-item",{attrs:{label:"优惠券类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["coupon_type",{initialValue:10,rules:[{required:!0}]}],expression:"['coupon_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("满减券")]),t("a-radio",{attrs:{value:20}},[e._v("折扣券")])],1)],1),10==e.form.getFieldValue("coupon_type")?t("a-form-item",{attrs:{label:"减免金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["reduce_price",{rules:[{required:!0,message:"请输入减免金额"}]}],expression:"['reduce_price', { rules: [{ required: true, message: '请输入减免金额' }] }]"}],attrs:{min:.01,precision:2}}),t("span",{staticClass:"ml-5"},[e._v("元")])],1):e._e(),20==e.form.getFieldValue("coupon_type")?t("a-form-item",{attrs:{label:"折扣率",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["discount",{initialValue:9.9,rules:[{required:!0,message:"请输入折扣率"}]}],expression:"['discount', { initialValue: 9.9, rules: [{ required: true, message: '请输入折扣率' }] }]"}],attrs:{min:0,max:9.9,precision:1}}),t("span",{staticClass:"ml-5"},[e._v("%")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("折扣率范围 0-9.9，8代表打8折，0代表不折扣")])])],1):e._e(),t("a-form-item",{attrs:{label:"最低消费金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["min_price",{rules:[{required:!0,message:"请输入最低消费金额"}]}],expression:"['min_price', { rules: [{ required: true, message: '请输入最低消费金额' }] }]"}],attrs:{min:1,precision:2}}),t("span",{staticClass:"ml-5"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"到期类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["expire_type",{initialValue:10,rules:[{required:!0}]}],expression:"['expire_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("领取后生效")]),t("a-radio",{attrs:{value:20}},[e._v("固定时间")])],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:10==e.form.getFieldValue("expire_type"),expression:"form.getFieldValue('expire_type') == 10"}],staticClass:"expire_type-10"},[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["expire_day",{initialValue:7,rules:[{required:!0,message:"请输入有效期天数"}]}],expression:"['expire_day', { initialValue: 7, rules: [{ required: true, message: '请输入有效期天数' }] }]"}],attrs:{addonBefore:"有效期",addonAfter:"天",inputProps:{min:1,precision:0}}})],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:20==e.form.getFieldValue("expire_type"),expression:"form.getFieldValue('expire_type') == 20"}],staticClass:"expire_type-20"},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime",{initialValue:e.defaultDate,rules:[{required:!0,message:"请选择有效期范围"}]}],expression:"['betweenTime', { initialValue: defaultDate, rules: [{ required: true, message: '请选择有效期范围' }] }]"}],attrs:{format:"YYYY-MM-DD"}})],1)],1),t("a-form-item",{attrs:{label:"券适用范围",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["apply_range",{initialValue:10,rules:[{required:!0}]}],expression:"['apply_range', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("全场通用")]),t("a-radio",{attrs:{value:20}},[e._v("指定商品")])],1),20==e.form.getFieldValue("apply_range")?t("a-form-item",[t("SelectGoods",{directives:[{name:"decorator",rawName:"v-decorator",value:["apply_range_config.applyGoodsIds",{initialValue:[],rules:[{required:!0,message:"请选择指定的商品"}]}],expression:"['apply_range_config.applyGoodsIds', { initialValue: [], rules: [{ required: true, message: '请选择指定的商品' }] }]"}],attrs:{defaultList:e.containGoodsList}})],1):e._e()],1),t("a-form-item",{attrs:{label:"发放总数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["total_num",{initialValue:-1,rules:[{required:!0,message:"请输入发放总数量"}]}],expression:"['total_num', { initialValue: -1, rules: [{ required: true, message: '请输入发放总数量' }] }]"}],attrs:{min:-1,precision:0}}),t("span",{staticClass:"ml-5"},[e._v("张")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("发放的优惠券总数量，-1为不限制")])])],1),t("a-form-item",{attrs:{label:"显示状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("显示")]),t("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1),t("p",{staticClass:"form-item-help"},[t("small",[e._v("如果设为隐藏将不会展示在用户端页面")])])],1),t("a-form-item",{attrs:{label:"优惠券描述",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["describe"],expression:"['describe']"}],attrs:{autoSize:{minRows:4}}})],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1),t("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1)],1)],1)},o=[],i=(r("d3b7"),r("c1df")),n=r.n(i),s=r("39ad9"),l=r("2af9"),u=r("8fa3"),c={components:{SelectGoods:l["g"],InputNumberGroup:l["c"]},data:function(){return{ApplyRangeEnum:u["a"],CouponTypeEnum:u["b"],ExpireTypeEnum:u["c"],isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},form:this.$form.createForm(this),defaultDate:[n()(),n()()],containGoodsList:[]}},created:function(){var e=this;this.$nextTick((function(){e.$forceUpdate()}))},methods:{handleSubmit:function(e){e.preventDefault();var t=this.form.validateFields,r=this.onFormSubmit;t((function(e,t){!e&&r(t)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,this.isBtnLoading=!0,s["a"]({form:e}).then((function(e){t.$message.success(e.message,1.5),setTimeout((function(){t.$router.push("./index")}),1500)})).catch((function(){t.isBtnLoading=!1})).finally((function(){return t.isLoading=!1}))}}},d=c,m=(r("2640"),r("2877")),p=Object(m["a"])(d,a,o,!1,null,"6c9a6694",null);t["default"]=p.exports},c7eb:function(e,t,r){"use strict";r.d(t,"a",(function(){return o}));r("a4d3"),r("e01a"),r("d3b7"),r("d28b"),r("3ca3"),r("ddb0"),r("b636"),r("944a"),r("0c47"),r("23dc"),r("3410"),r("159b"),r("b0c0"),r("131a"),r("fb6a");var a=r("53ca");function o(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
o=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",l=n.asyncIterator||"@@asyncIterator",u=n.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(O){c=function(e,t,r){return e[t]=r}}function d(e,t,r,a){var o=t&&t.prototype instanceof f?t:f,n=Object.create(o.prototype),s=new S(a||[]);return i(n,"_invoke",{value:x(e,r,s)}),n}function m(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(O){return{type:"throw",arg:O}}}e.wrap=d;var p={};function f(){}function v(){}function h(){}var b={};c(b,s,(function(){return this}));var g=Object.getPrototypeOf,_=g&&g(g(N([])));_&&_!==t&&r.call(_,s)&&(b=_);var C=h.prototype=f.prototype=Object.create(b);function w(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function y(e,t){function o(i,n,s,l){var u=m(e[i],e,n);if("throw"!==u.type){var c=u.arg,d=c.value;return d&&"object"==Object(a["a"])(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,s,l)}),(function(e){o("throw",e,s,l)})):t.resolve(d).then((function(e){c.value=e,s(c)}),(function(e){return o("throw",e,s,l)}))}l(u.arg)}var n;i(this,"_invoke",{value:function(e,r){function a(){return new t((function(t,a){o(e,r,t,a)}))}return n=n?n.then(a,a):a()}})}function x(e,t,r){var a="suspendedStart";return function(o,i){if("executing"===a)throw new Error("Generator is already running");if("completed"===a){if("throw"===o)throw i;return F()}for(r.method=o,r.arg=i;;){var n=r.delegate;if(n){var s=L(n,r);if(s){if(s===p)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===a)throw a="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a="executing";var l=m(e,t,r);if("normal"===l.type){if(a=r.done?"completed":"suspendedYield",l.arg===p)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(a="completed",r.method="throw",r.arg=l.arg)}}}function L(e,t){var r=t.method,a=e.iterator[r];if(void 0===a)return t.delegate=null,"throw"===r&&e.iterator["return"]&&(t.method="return",t.arg=void 0,L(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),p;var o=m(a,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,p;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,p):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function q(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(q,this),this.reset(!0)}function N(e){if(e){var t=e[s];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function t(){for(;++a<e.length;)if(r.call(e,a))return t.value=e[a],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:F}}function F(){return{value:void 0,done:!0}}return v.prototype=h,i(C,"constructor",{value:h,configurable:!0}),i(h,"constructor",{value:v,configurable:!0}),v.displayName=c(h,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,c(e,u,"GeneratorFunction")),e.prototype=Object.create(C),e},e.awrap=function(e){return{__await:e}},w(y.prototype),c(y.prototype,l,(function(){return this})),e.AsyncIterator=y,e.async=function(t,r,a,o,i){void 0===i&&(i=Promise);var n=new y(d(t,r,a,o),i);return e.isGeneratorFunction(r)?n:n.next().then((function(e){return e.done?e.value:n.next()}))},w(C),c(C,u,"Generator"),c(C,s,(function(){return this})),c(C,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var a in t)r.push(a);return r.reverse(),function e(){for(;r.length;){var a=r.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},e.values=N,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function a(r,a){return n.type="throw",n.arg=e,t.next=r,a&&(t.method="next",t.arg=void 0),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],n=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var s=r.call(i,"catchLoc"),l=r.call(i,"finallyLoc");if(s&&l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var n=i?i.completion:{};return n.type=e,n.arg=t,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(n)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),k(r),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var a=r.completion;if("throw"===a.type){var o=a.arg;k(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:N(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),p}},e}},d36d:function(e,t,r){"use strict";r("c10a")},da13:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"积分名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points_name",{rules:[{required:!0,message:"请输入积分名称"}]}],expression:"['points_name', { rules: [{ required: true, message: '请输入积分名称' }] }]"}]}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("注：商家使用自定义的积分名称来做品牌运营。如京东把积分称为“京豆”，淘宝把积分称为“淘金币”")])])],1),t("a-form-item",{attrs:{label:"积分说明",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["describe",{rules:[{required:!0,message:"请输入积分说明"}]}],expression:"['describe', { rules: [{ required: true, message: '请输入积分说明' }] }]"}],attrs:{autoSize:{minRows:4}}})],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("积分赠送")]),t("a-form-item",{attrs:{label:"购物送积分",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_shopping_gift",{initialValue:1,rules:[{required:!0}]}],expression:"['is_shopping_gift', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("积分赠送规则：1. 订单确认收货已完成； 2. 已完成订单超出后台设置的申请售后期限")])])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:1==e.form.getFieldValue("is_shopping_gift"),expression:"form.getFieldValue('is_shopping_gift') == 1"}]},[t("a-form-item",{attrs:{label:"积分赠送比例",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["gift_ratio",{rules:[{required:!0,message:"请输入积分赠送比例"}]}],expression:"['gift_ratio', { rules: [{ required: true, message: '请输入积分赠送比例' }] }]"}],attrs:{min:.1,max:100,precision:1}}),t("span",{staticClass:"ml-10"},[e._v("%")]),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("注：赠送比例请填写数字0~100；订单的运费不参与积分赠送")]),t("p",{staticClass:"extra"},[e._v("例：订单付款金额(100.00元) * 积分赠送比例(100%) = 实际赠送的积分(100积分)")])])],1)],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("积分抵扣")]),t("a-form-item",{attrs:{label:"下单使用积分抵扣",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_shopping_discount",{initialValue:1,rules:[{required:!0}]}],expression:"['is_shopping_discount', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("注：如开启则用户下单时可选择使用积分抵扣订单金额")])])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:1==e.form.getFieldValue("is_shopping_discount"),expression:"form.getFieldValue('is_shopping_discount') == 1"}]},[t("a-form-item",{attrs:{label:"积分抵扣比例",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["discount.discount_ratio",{rules:[{required:!0,message:"积分抵扣比例不能为空"}]}],expression:"['discount.discount_ratio', { rules: [{ required: true, message: '积分抵扣比例不能为空' }] }]"}],attrs:{addonBefore:"1个积分可抵扣",addonAfter:"元",inputProps:{min:.01,precision:2}}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("例如：设置1个积分可抵扣0.1元，则10积分可抵扣1元，100积分可抵扣10元")])])],1),t("a-form-item",{attrs:{label:"最高可抵扣金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-form-item",[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["discount.full_order_price",{rules:[{required:!0,message:"抵扣条件不能为空"}]}],expression:"['discount.full_order_price', { rules: [{ required: true, message: '抵扣条件不能为空' }] }]"}],attrs:{addonBefore:"订单满",addonAfter:"元",inputProps:{min:.01,precision:2}}})],1),t("a-form-item",[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["discount.max_money_ratio",{rules:[{required:!0,message:"最高可抵扣金额不能为空"}]}],expression:"['discount.max_money_ratio', { rules: [{ required: true, message: '最高可抵扣金额不能为空' }] }]"}],attrs:{addonBefore:"最高可抵扣金额",addonAfter:"%",inputProps:{min:.1,max:100,precision:1}}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("例如：设置最高可抵扣10%，订单金额100元，此时用户可抵扣10元")])])],1)],1)],1),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},o=[],i=(r("d3b7"),r("ddb0"),r("88bc")),n=r.n(i),s=r("f585"),l=r("2af9"),u={components:{InputNumberGroup:l["c"]},data:function(){return{key:"points",labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),record:{}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,s["a"](this.key).then((function(t){e.record=t.data.values,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,t=this.$nextTick,r=this.form.setFieldsValue;t((function(){r(n()(e,["points_name","describe","is_shopping_gift","gift_ratio","is_shopping_discount","discount"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var r=this.form.validateFields;r((function(e,r){!e&&t.onFormSubmit(r)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,s["b"](this.key,{form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},c=u,d=(r("9ed8"),r("2877")),m=Object(d["a"])(c,a,o,!1,null,"e26ed306",null);t["default"]=m.exports},e0ac:function(e,t,r){"use strict";r("ae32")},f585:function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return s}));var a=r("5530"),o=r("b775"),i={detail:"/setting/detail",update:"/setting/update"};function n(e){return Object(o["b"])({url:i.detail,method:"get",params:{key:e}})}function s(e,t){return Object(o["b"])({url:i.update,method:"post",data:Object(a["a"])({key:e},t)})}},fe7e:function(e,t,r){"use strict";var a=r("5c06");t["a"]=new a["a"]([{key:"RECHARGE",name:"用户充值",value:10},{key:"CONSUME",name:"用户消费",value:20},{key:"ADMIN",name:"管理员操作",value:30},{key:"REFUND",name:"订单退款",value:40}])}}]);