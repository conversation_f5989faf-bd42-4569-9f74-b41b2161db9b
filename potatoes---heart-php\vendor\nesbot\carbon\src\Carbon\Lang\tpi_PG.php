<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return array_replace_recursive(require __DIR__.'/en.php', [
    'formats' => [
        'L' => 'DD/MM/YYYY',
    ],
    'months' => ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Mas', '<PERSON>pril', 'Me', 'Jun', '<PERSON><PERSON>', 'Ogas', 'Septemba', '<PERSON><PERSON><PERSON>', '<PERSON>em<PERSON>', '<PERSON><PERSON><PERSON>'],
    'months_short' => ['Jan', 'Feb', 'Mas', 'Epr', 'Me', 'Jun', 'Jul', 'Oga', 'Sep', 'Okt', 'Nov', 'Des'],
    'weekdays' => ['Sande', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>onde', '<PERSON>aide', '<PERSON><PERSON>e'],
    'weekdays_short' => ['San', 'Man', 'Tun', '<PERSON>', '<PERSON>on', 'Fra', 'Sar'],
    'weekdays_min' => ['San', '<PERSON>', 'Tun', 'Tri', 'Fon', 'Fra', 'Sar'],
    'first_day_of_week' => 0,
    'day_of_first_week_of_year' => 1,
    'meridiem' => ['biknait', 'apinun'],

    'year' => 'yia :count',
    'y' => 'yia :count',
    'a_year' => 'yia :count',

    'month' => ':count mun',
    'm' => ':count mun',
    'a_month' => ':count mun',

    'week' => ':count wik',
    'w' => ':count wik',
    'a_week' => ':count wik',

    'day' => ':count de',
    'd' => ':count de',
    'a_day' => ':count de',

    'hour' => ':count aua',
    'h' => ':count aua',
    'a_hour' => ':count aua',

    'minute' => ':count minit',
    'min' => ':count minit',
    'a_minute' => ':count minit',

    'second' => ':count namba tu',
    's' => ':count namba tu',
    'a_second' => ':count namba tu',
]);
