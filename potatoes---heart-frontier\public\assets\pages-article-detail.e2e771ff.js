import{o as t,c as a,w as e,a as s,k as i,n as l,Y as o,l as n,i as r,e as c,f as d,t as h,y as f,r as p}from"./index-b996d08a.js";import{_ as u}from"./mp-html.a2824d73.js";import{r as m}from"./uni-app.es.e82e6f02.js";import{W as _}from"./wxofficial.0e36c140.js";import{_ as g}from"./_plugin-vue_export-helper.1b428a4d.js";import{d as w}from"./index.c4013507.js";const x=g({components:{Shortcut:g({props:{right:{type:Number,default:30},bottom:{type:Number,default:100}},data:()=>({isShow:!1,transparent:!0}),computed:{rightPx(){return uni.upx2px(this.right)},bottomPx(){return uni.upx2px(this.bottom)}},methods:{onToggleShow(){const t=this;t.isShow=!t.isShow,t.transparent=!1},onTargetPage(t=0){const a=o();this.$navTo(a[t])}}},[["render",function(o,c,d,h,f,p){const u=n,m=r;return t(),a(m,{class:"shortcut",style:l({"--right":`${p.rightPx}px`,"--bottom":`${p.bottomPx}px`})},{default:e((()=>[s(m,{class:i(["nav-item",[f.isShow?"show_80":f.transparent?"":"hide_80"]]),onClick:c[0]||(c[0]=t=>p.onTargetPage(0))},{default:e((()=>[s(u,{class:"iconfont icon-home"})])),_:1},8,["class"]),s(m,{class:i(["nav-item",[f.isShow?"show_60":f.transparent?"":"hide_60"]]),onClick:c[1]||(c[1]=t=>p.onTargetPage(1))},{default:e((()=>[s(u,{class:"iconfont icon-cate"})])),_:1},8,["class"]),s(m,{class:i(["nav-item",[f.isShow?"show_40":f.transparent?"":"hide_40"]]),onClick:c[2]||(c[2]=t=>p.onTargetPage(2))},{default:e((()=>[s(u,{class:"iconfont icon-cart"})])),_:1},8,["class"]),s(m,{class:i(["nav-item",[f.isShow?"show_20":f.transparent?"":"hide_20"]]),onClick:c[3]||(c[3]=t=>p.onTargetPage(3))},{default:e((()=>[s(u,{class:"iconfont icon-profile"})])),_:1},8,["class"]),s(m,{class:i(["nav-item nav-item__switch",{shortcut_click_show:f.isShow}]),onClick:c[4]||(c[4]=t=>p.onToggleShow())},{default:e((()=>[s(u,{class:"iconfont icon-daohang"})])),_:1},8,["class"])])),_:1},8,["style"])}],["__scopeId","data-v-beb2efe2"]])},mixins:[_],data:()=>({articleId:null,isLoading:!0,detail:null}),onLoad(t){this.articleId=t.articleId,this.getArticleDetail()},methods:{getArticleDetail(){const t=this;t.isLoading=!0,w(t.articleId).then((a=>{t.detail=a.data.detail,t.setWxofficialShareData()})).finally((()=>t.isLoading=!1))},setWxofficialShareData(){this.updateShareCardData({title:this.detail.title})}},onShareAppMessage(){const t=this,a=t.$getShareUrlParams({articleId:t.articleId});return{title:t.detail.title,path:"/pages/article/detail?"+a}},onShareTimeline(){const t=this,a=t.$getShareUrlParams({articleId:t.articleId});return{title:t.detail.title,path:"/pages/article/detail?"+a}}},[["render",function(i,l,o,_,g,w){const x=n,S=r,v=m(f("mp-html"),u),b=p("shortcut");return g.isLoading?c("",!0):(t(),a(S,{key:0,class:"container b-f p-b"},{default:e((()=>[s(S,{class:"article-title"},{default:e((()=>[s(x,{class:"f-32"},{default:e((()=>[d(h(g.detail.title),1)])),_:1})])),_:1}),s(S,{class:"article-little dis-flex flex-x-between m-top10"},{default:e((()=>[s(S,{class:"article-little__left"},{default:e((()=>[s(x,{class:"article-views f-24 col-8"},{default:e((()=>[d(h(g.detail.show_views)+"次浏览",1)])),_:1})])),_:1}),s(S,{class:"article-little__right"},{default:e((()=>[s(x,{class:"article-views f-24 col-8"},{default:e((()=>[d(h(g.detail.view_time),1)])),_:1})])),_:1})])),_:1}),s(S,{class:"article-content m-top20"},{default:e((()=>[s(v,{content:g.detail.content},null,8,["content"])])),_:1}),s(b)])),_:1}))}],["__scopeId","data-v-821db831"]]);export{x as default};
