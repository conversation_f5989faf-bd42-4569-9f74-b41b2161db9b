import{o as e,c as t,w as o,a as s,k as l,n as a,h as i,f as n,i as r,I as u,r as c,t as d,v as m,b as p,x as h,d as f,F as g,e as y,g as b,l as C,j as k}from"./index-BI5vpG2u.js";import{_ as S}from"./_plugin-vue_export-helper.BCo6x5W8.js";const N={name:"NumberBox",emits:["update:modelValue","input","change","blur","plus","minus"],props:{value:{type:Number,default:1},modelValue:{type:Number,default:1},bgColor:{type:String,default:"#FFFFFF"},min:{type:Number,default:0},max:{type:Number,default:99999},step:{type:Number,default:1},stepFirst:{type:Number,default:0},stepStrictly:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},size:{type:[Number,String],default:26},color:{type:String,default:"#323233"},inputWidth:{type:[Number,String],default:80},inputHeight:{type:[Number,String],default:50},index:{type:[Number,String],default:""},disabledInput:{type:Boolean,default:!1},cursorSpacing:{type:[Number,String],default:100},longPress:{type:Boolean,default:!0},pressTime:{type:[Number,String],default:250},positiveInteger:{type:Boolean,default:!0}},watch:{valueCom(e,t){this.changeFromInner||(this.inputVal=e,this.$nextTick((function(){this.changeFromInner=!1})))},inputVal(e,t){if(""==e)return;let o=0;o=this.isNumber(e)&&e>=this.min&&e<=this.max?e:t,this.positiveInteger&&(e<0||-1!==String(e).indexOf("."))&&(o=t,this.$nextTick((()=>{this.inputVal=t}))),this.handleChange(o,"change")},min(e){void 0!==e&&""!=e&&this.valueCom<e&&(this.$emit("input",e),this.$emit("update:modelValue",e))},max(e){void 0!==e&&""!=e&&this.valueCom>e&&(this.$emit("input",e),this.$emit("update:modelValue",e))}},data:()=>({inputVal:1,timer:null,changeFromInner:!1,innerChangeTimer:null,showInput:!1}),created(){this.inputVal=Number(this.valueCom)},computed:{valueCom(){return this.modelValue},getCursorSpacing(){return Number(uni.upx2px(this.cursorSpacing))}},methods:{emptyClick(){},btnTouchStart(e){this[e](),this.longPress&&(clearInterval(this.timer),this.timer=null,this.timer=setInterval((()=>{this[e]()}),this.pressTime))},clearTimer(){this.$nextTick((()=>{clearInterval(this.timer),this.timer=null}))},minus(){this.computeVal("minus")},plus(){this.computeVal("plus")},calcPlus(e,t){let o,s,l;try{s=e.toString().split(".")[1].length}catch(a){s=0}try{l=t.toString().split(".")[1].length}catch(a){l=0}return o=Math.pow(10,Math.max(s,l)),((e*o+t*o)/o).toFixed(s>=l?s:l)},calcMinus(e,t){let o,s,l;try{s=e.toString().split(".")[1].length}catch(a){s=0}try{l=t.toString().split(".")[1].length}catch(a){l=0}return o=Math.pow(10,Math.max(s,l)),((e*o-t*o)/o).toFixed(s>=l?s:l)},computeVal(e){if(uni.hideKeyboard(),this.disabled)return;let t=0;if("minus"===e?t=this.stepFirst>0&&this.inputVal==this.stepFirst?this.min:this.calcMinus(this.inputVal,this.step):"plus"===e&&(t=this.stepFirst>0&&this.inputVal<this.stepFirst?this.stepFirst:this.calcPlus(this.inputVal,this.step)),this.stepStrictly){let e=t%this.step;e>0&&(t-=e)}t>this.max?t=this.max:t<this.min&&(t=this.min),this.inputVal=t,this.handleChange(t,e)},onBlur(e){let t=0,o=e.detail.value;if(/(^\d+$)/.test(o)&&0!=o[0]||(t=this.min),t=+o,this.stepFirst>0&&this.inputVal<this.stepFirst&&this.inputVal>0&&(t=this.stepFirst),this.stepStrictly){let e=t%this.step;e>0&&(t-=e)}t>this.max?t=this.max:t<this.min&&(t=this.min),this.$nextTick((()=>{this.inputVal=t})),this.handleChange(t,"blur")},handleChange(e,t){this.disabled||(this.innerChangeTimer&&(clearTimeout(this.innerChangeTimer),this.innerChangeTimer=null),this.changeFromInner=!0,this.innerChangeTimer=setTimeout((()=>{this.changeFromInner=!1}),150),this.$emit("input",Number(e)),this.$emit("update:modelValue",Number(e)),this.$emit(t,{value:Number(e),index:this.index}))},isNumber:e=>/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(e)}};var T,I={};const x=S({name:"GoodsSkuPopup",components:{NumberBox:S(N,[["render",function(c,d,m,p,h,f){const g=r,y=u;return e(),t(g,{class:"number-box"},{default:o((()=>[s(g,{class:l(["u-icon-minus",{"u-icon-disabled":m.disabled||h.inputVal<=m.min}]),style:a({background:m.bgColor,height:m.inputHeight+"rpx",color:m.color,fontSize:m.size+"rpx",minHeight:"1.4em"}),onClick:f.emptyClick,onTouchstart:d[0]||(d[0]=i((e=>f.btnTouchStart("minus")),["prevent"])),onTouchend:i(f.clearTimer,["stop","prevent"])},{default:o((()=>[s(g,{style:a("font-size:"+(Number(m.size)+10)+"rpx"),class:"num-btn"},{default:o((()=>[n("－")])),_:1},8,["style"])])),_:1},8,["class","style","onClick","onTouchend"]),s(y,{modelValue:h.inputVal,"onUpdate:modelValue":d[1]||(d[1]=e=>h.inputVal=e),disabled:m.disabledInput||m.disabled,"cursor-spacing":f.getCursorSpacing,class:l([{"u-input-disabled":m.disabled},"u-number-input"]),type:"number",style:a({color:m.color,fontSize:m.size+"rpx",background:m.bgColor,height:m.inputHeight+"rpx",width:m.inputWidth+"rpx"}),onBlur:f.onBlur,onClick:d[2]||(d[2]=e=>h.showInput=!0)},null,8,["modelValue","disabled","cursor-spacing","class","style","onBlur"]),s(g,{class:l(["u-icon-plus",{"u-icon-disabled":m.disabled||h.inputVal>=m.max}]),style:a({background:m.bgColor,height:m.inputHeight+"rpx",color:m.color,fontSize:m.size+"rpx",minHeight:"1.4em"}),onClick:f.emptyClick,onTouchstart:d[3]||(d[3]=i((e=>f.btnTouchStart("plus")),["prevent"])),onTouchend:i(f.clearTimer,["stop","prevent"])},{default:o((()=>[s(g,{style:a("font-size:"+(Number(m.size)+10)+"rpx"),class:"num-btn"},{default:o((()=>[n("＋")])),_:1},8,["style"])])),_:1},8,["class","style","onClick","onTouchend"])])),_:1})}],["__scopeId","data-v-f78177f9"]])},emits:["update:modelValue","input","update-goods","open","close","add-cart","buy-now","cart","buy","num-change"],props:{value:{Type:Boolean,default:!1},modelValue:{Type:Boolean,default:!1},goodsId:{Type:String,default:""},action:{Type:String,default:""},noStockText:{Type:String,default:"该商品已抢完"},stockText:{Type:String,default:"库存"},goodsIdName:{Type:String,default:"_id"},skuIdName:{Type:String,default:"_id"},skuListName:{Type:String,default:"sku_list"},specListName:{Type:String,default:"spec_list"},stockName:{Type:String,default:"stock"},skuArrName:{Type:String,default:"sku_name_arr"},defaultSingleSkuName:{Type:String,default:"默认"},mode:{Type:Number,default:1},maskCloseAble:{Type:Boolean,default:!0},borderRadius:{Type:[String,Number],default:0},goodsThumbName:{Type:[String],default:"goods_thumb"},goodsThumbBackgroundColor:{Type:String,default:"#999999"},minBuyNum:{Type:[Number,String],default:1},maxBuyNum:{Type:[Number,String],default:1e5},stepBuyNum:{Type:[Number,String],default:1},stepStrictly:{Type:Boolean,default:!1},customAction:{Type:[Function],default:null},localdata:{type:Object},priceColor:{Type:String},buyNowText:{Type:String,default:"立即购买"},buyNowColor:{Type:String},buyNowBackgroundColor:{Type:String},addCartText:{Type:String,default:"加入购物车"},addCartColor:{Type:String},addCartBackgroundColor:{Type:String},disableStyle:{Type:Object,default:null},activedStyle:{Type:Object,default:null},btnStyle:{Type:Object,default:null},showClose:{Type:Boolean,default:!0},closeImage:{Type:String,default:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAYAAADFw8lbAAAEyUlEQVR42sSZeWwNURTGp4OqtBo7sSXELragdkpQsRRJ1Zr4hyJiJ9YgxNIg1qANiT+E1i5IY0kVVWtQEbuEKLFGUSH27/ANN5PXmTvzupzkl/tm8t6b7517lnvvC0lKSjJ8WmnQAUSDFqABqALKgl8gD7wE90E2SAeXwFf1SxISErQeVtKHwCgwFsSDSIf3hYFKoCkYDBaDdyAViHdueHmoF6FtwDLQ23b/E7gM7oIcejIERIDaoBFoC8qA8mA8SQNz6W1XC9GY+nCQCCYAk/c+gF0gBZwH312+IxR0BCPBUIaH2A+wHsxHCHxx+gLT5QGN6a2JfG8uvVCDws9oiDQYlxkMGfHyQvARlADTwcXk5OT6foV2kS8ATXidymlcyen1a/Jjl9IJh3hPkjELYqO8Cu0KjjNZvtETw5jFBWXPmGSTGQKSeOn5iQ0kVLL0CINfPNcPbDMKyRCbGzEMBJ+ZD8cChYFdqGTqfsWT8otPGoVsEHsMwxDFs3shNsxJ6BrQ0Po8OGUUkVHsNCVml+cntB1jUWwn2GEUsTEMrASbDK+2CCQ0kYX6nfLLisMmKqUr0S60M+jG10vAm+JSCa8+x7CKlzHwaktV6DiObzUzPJIxFO1BQ12wGtTReO9GetVgY/kjNJzZbcWmTjHfxw51AsRqvL8eOAtmsJuFu3g1l+1ZLB5eDTVZ3K0P7tL0TkWOpSg61kVkBtuuNRthGs+wtJST5aQI7cEbkkRXNYVKgX6kIdYuUhYzMQwxN8tiExCLFqHNeSF9/aem0BzGp5PYQCJ7c/Gsk1RfuSD6U1dNpcDf9ZigTmKbMRZ9iVTsHscGJluW2FMf1SSQWGnBmaB6kCJVTVVNJZE++Cx9drEllS1KMCINpURFmEbBWA63Fz9s95cGIdJgp/zXmT4pZcOvSUzuZttTbblmnc3PIjjmidDXvKgdhMh0JdbzuCjWrbNOVovjS5P7bkPJ/mBESkz2BO0166ybNeJ431S2q+01NntuIq3E0amzjiZtk9tssWyTDzO4525bACK9NAUn68TtkNhpEXpOSagRml+S6iLSSeweHv242Qhl13rRyvoDvDlKyTQny/ZQJ+1iH7vVbEx7OR5UiKVIO7VicgvHCtwrudloMIV7/0uadVYW57O4Wvvi8v4pymlKkrpwvsDeLLZAY2pkwbAB3PSQfC+4cH7l4k1ZH8zkZRq8ecO+Z5rN40JJqnXFuGfaxPCTLjcn0OZOpnArXw8HY4paIbw5CcMgXq6HN2/mt6+XGLrN15tBryIUGavMpCTrfKcDCKkAceA9S8nhAOehhSUyhXpkBxxnP4YM1InugP7cBkjBPcqVUWFYCEROxXiQz5JlXV+IfKh7mpfJac+lZ6V87QXVClBkTc7YWsWTPSDyitfzUTlJlj8TbvE6jluDOdwZ+jX57GLO3ADeuyZrDYi86vV81FD2UVGsmT+5Zl0BnkhoseOEaogL46pqO4v/IqUEyalIR4h85BgjHv6+aUWRMbb7EstX6O0cpT1Gco0ry8fWygLDMjmDnQeBt3Qe7uVfkeugDwVLcsVzGsuwLXbV+I63XNAkG5r/hvgRqgqWs6pJPKrsbvz/Q6yyun0w/h6lP+BnzrCpfPMT2L8FGAA7k1GZ/vnaqAAAAABJRU5ErkJggg=="},hideStock:{Type:Boolean,default:!1},theme:{Type:String,default:"default"},actionTips:{Type:String,default:"请求中..."},defaultSelect:{Type:Object},useCache:{Type:Boolean,default:!0},defaultGoods:{Type:Object},amountType:{Type:Number,default:1},selectedInit:{Type:Boolean,default:!1},safeAreaInsetBottom:{Type:Boolean,default:!0}},data(){return{complete:!1,goodsInfo:{},isShow:!1,initKey:!0,shopItemInfo:{},selectArr:[],subIndex:[],selectShop:{},selectNum:this.minBuyNum||1,outFoStock:!1,openTime:0,themeColor:{default:{priceColor:"rgb(254, 86, 10)",buyNowColor:"#ffffff",buyNowBackgroundColor:"rgb(254, 86, 10)",addCartColor:"#ffffff",addCartBackgroundColor:"rgb(255, 148, 2)",btnStyle:{color:"#333333",borderColor:"#f4f4f4",backgroundColor:"#ffffff"},activedStyle:{color:"rgb(254, 86, 10)",borderColor:"rgb(254, 86, 10)",backgroundColor:"rgba(254,86,10,0.1)"},disableStyle:{color:"#c3c3c3",borderColor:"#f6f6f6",backgroundColor:"#f6f6f6"}},"red-black":{priceColor:"rgb(255, 68, 68)",buyNowColor:"#ffffff",buyNowBackgroundColor:"rgb(255, 68, 68)",addCartColor:"#ffffff",addCartBackgroundColor:"rgb(85, 85, 85)",activedStyle:{color:"rgb(255, 68, 68)",borderColor:"rgb(255, 68, 68)",backgroundColor:"rgba(255,68,68,0.1)"}},"black-white":{priceColor:"rgb(47, 47, 52)",buyNowColor:"#ffffff",buyNowBackgroundColor:"rgb(47, 47, 52)",addCartColor:"rgb(47, 47, 52)",addCartBackgroundColor:"rgb(235, 236, 242)",activedStyle:{color:"rgb(47, 47, 52)",borderColor:"rgba(47,47,52,0.12)",backgroundColor:"rgba(47,47,52,0.12)"}},coffee:{priceColor:"rgb(195, 167, 105)",buyNowColor:"#ffffff",buyNowBackgroundColor:"rgb(195, 167, 105)",addCartColor:"rgb(195, 167, 105)",addCartBackgroundColor:"rgb(243, 238, 225)",activedStyle:{color:"rgb(195, 167, 105)",borderColor:"rgb(195, 167, 105)",backgroundColor:"rgba(195, 167, 105,0.1)"}},green:{priceColor:"rgb(99, 190, 114)",buyNowColor:"#ffffff",buyNowBackgroundColor:"rgb(99, 190, 114)",addCartColor:"rgb(99, 190, 114)",addCartBackgroundColor:"rgb(225, 244, 227)",activedStyle:{color:"rgb(99, 190, 114)",borderColor:"rgb(99, 190, 114)",backgroundColor:"rgba(99, 190, 114,0.1)"}}}}},created(){let e=this;T=e.vk,e.valueCom&&e.open()},mounted(){},methods:{init(e){let t=this;t.selectArr=[],t.subIndex=[],t.selectShop={},t.selectNum=t.minBuyNum||1,t.outFoStock=!1,t.shopItemInfo={};let o=t.specListName;t.goodsInfo[o].map((e=>{t.selectArr.push(""),t.subIndex.push(-1)})),t.checkItem(),t.checkInpath(-1),e||t.autoClickSku()},findGoodsInfo(e={}){let t=this,{useCache:o}=e;if(void 0===T)return t.toast("custom-action必须是function","none"),!1;let{actionTips:s}=t,l="",a=!1;"custom"!==s?l=o?"":"请求中...":a=!o,T.callFunction({url:t.action,title:l,loading:a,data:{goods_id:t.goodsId},success(e){t.updateGoodsInfo(e.goodsInfo),I[t.goodsId]=e.goodsInfo,t.$emit("update-goods",e.goodsInfo)},fail(){t.updateValue(!1)}})},updateValue(e){let t=this;e?(t.$emit("open",!0),t.$emit("input",!0),t.$emit("update:modelValue",!0)):(t.$emit("input",!1),t.$emit("close","close"),t.$emit("update:modelValue",!1))},updateGoodsInfo(e){let t=this,{skuListName:o}=t;if("{}"===JSON.stringify(t.goodsInfo)||t.goodsInfo[t.goodsIdName]!==e[t.goodsIdName]?(t.goodsInfo=e,t.initKey=!0):t.goodsInfo[o]=e[o],t.initKey){t.initKey=!1;const e=t.goodsInfo[t.skuListName],o=!t.isManyCom||1===e.length;t.init(!o)}let s=t.getListItem(t.goodsInfo[o],t.skuIdName,t.selectShop[t.skuIdName]);Object.assign(t.selectShop,s),t.defaultSelectSku(),t.complete=!0},async open(){let e=this;e.openTime=(new Date).getTime();let t=!0;e.skuListName;let o=!1,s=I[e.goodsId];if(s&&e.useCache?(o=!0,e.updateGoodsInfo(s)):e.complete=!1,e.customAction&&"function"==typeof e.customAction){try{s=await e.customAction({useCache:o,goodsId:e.goodsId,goodsInfo:s,close:function(){setTimeout((function(){e.close()}),500)}}).catch((t=>{setTimeout((function(){e.close()}),500)}))}catch(l){let{message:t=""}=l;if(t.indexOf(".catch is not a function")>-1)return e.toast("custom-action必须返回一个Promise","none"),setTimeout((function(){e.close()}),500),!1}if(I[e.goodsId]=s,!s||"object"!=typeof s||"{}"==JSON.stringify(s))return e.toast("未获取到商品信息","none"),e.$emit("input",!1),!1;t=!1,e.updateGoodsInfo(s),e.updateValue(!0)}else if(void 0!==e.localdata&&null!==e.localdata){if(s=e.localdata,!s||"object"!=typeof s||"{}"==JSON.stringify(s))return e.toast("未获取到商品信息","none"),e.$emit("input",!1),!1;t=!1,e.updateGoodsInfo(s),e.updateValue(!0)}else t&&e.findGoodsInfo({useCache:o})},close(e){let t=this;if((new Date).getTime()-t.openTime<400)return!1;"mask"==e?!1!==t.maskCloseAble&&(t.$emit("input",!1),t.$emit("close","mask"),t.$emit("update:modelValue",!1)):(t.$emit("input",!1),t.$emit("close","close"),t.$emit("update:modelValue",!1))},moveHandle(){},skuClick(e,t,o){let s=this;e.ishow&&(s.selectArr[t]!=e.name?(s.$set(s.selectArr,t,e.name),s.$set(s.subIndex,t,o)):(s.$set(s.selectArr,t,""),s.$set(s.subIndex,t,-1)),s.checkInpath(t),s.checkSelectShop())},checkSelectShop(){let e=this;if(e.selectArr.every((e=>""!=e))){e.selectShop=e.shopItemInfo[e.getArrayToSting(e.selectArr)];let t=e.selectShop[e.stockName];void 0!==t&&e.selectNum>t&&(e.selectNum=t),e.selectNum>e.maxBuyNum&&(e.selectNum=e.maxBuyNum),e.selectNum<e.minBuyNum&&(e.selectNum=e.minBuyNum),e.selectedInit&&(e.selectNum=e.minBuyNum||1)}else e.selectShop={}},checkInpath(e){let t=this,o=t.specListName,s=t.goodsInfo[o];for(let l=0,a=s.length;l<a;l++){if(l==e)continue;let o=s[l].list.length;for(let e=0;e<o;e++){if(-1!=t.subIndex[l]&&e==t.subIndex[l])continue;let o=[...t.selectArr];t.$set(o,l,s[l].list[e].name);let a=o.filter((e=>""!==e&&void 0!==e));t.shopItemInfo.hasOwnProperty(t.getArrayToSting(a))?s[l].list[e].ishow=!0:s[l].list[e].ishow=!1}}t.$set(t.goodsInfo,o,s)},checkItem(){let e=this,{stockName:t}=e,o=e.skuListName,s=e.goodsInfo[o],l=[],a=0;s.map(((e,o)=>{e[t]>0&&(l.push(e),a+=e[t])})),a<=0&&(e.outFoStock=!0),l.reduce(((t,o)=>t.concat(o[e.skuArrName].reduce(((t,s)=>t.concat(t.map((t=>(e.shopItemInfo.hasOwnProperty(e.getArrayToSting([...t,s]))||(e.shopItemInfo[e.getArrayToSting([...t,s])]=o),[...t,s]))))),[[]]))),[[]])},getArrayToSting(e){let t="";return e.map(((e,o)=>{e=e.replace(/\./g,"。"),t+=0==o?e:","+e})),t},checkSelectComplete(e={}){let t=this,o=(new Date).getTime();if(t.clickTime&&o-t.clickTime<400)return!1;t.clickTime=o;let{selectShop:s,selectNum:l,stockText:a,stockName:i}=t;return s&&s[t.skuIdName]?l<=0?(t.toast("购买数量必须>0","none"),!1):l>s[i]?(t.toast(a+"不足","none"),!1):void("function"==typeof e.success&&e.success(s)):(t.toast("请先选择对应规格","none"),!1)},addCart(){let e=this;e.checkSelectComplete({success:function(t){t.buy_num=e.selectNum,e.$emit("add-cart",t),e.$emit("cart",t)}})},buyNow(){let e=this;e.checkSelectComplete({success:function(t){t.buy_num=e.selectNum,e.$emit("buy-now",t),e.$emit("buy",t)}})},toast(e,t){uni.showToast({title:e,icon:t})},getListItem(e,t,o){let s;for(let l in e)if("object"==typeof o){if(JSON.stringify(e[l][t])===JSON.stringify(o)){s=e[l];break}}else if(e[l][t]===o){s=e[l];break}return s},getListIndex(e,t,o){let s=-1;for(let l=0;l<e.length;l++)if(e[l][t]===o){s=l;break}return s},autoClickSku(){let e=this,{stockName:t}=e,o=e.goodsInfo[e.skuListName],s=e.goodsInfo[e.specListName];if(1==s.length){let l=s[0].list;for(let s=0;s<l.length;s++){let a=e.getListItem(o,e.skuArrName,[l[s].name]);if(a&&a[t]>0){e.skuClick(l[s],0,s);break}}}},themeColorFn(e){let t=this,{theme:o,themeColor:s}=t;return t[e]?t[e]:s[o][e]},defaultSelectSku(){let e=this,{defaultSelect:t}=e;t&&t.sku&&t.sku.length>0&&e.selectSku(t)},selectSku(e={}){let t=this,{sku:o,num:s}=e,l=t.goodsInfo[t.specListName];if(o&&l.length===o.length){let e=[],s=!0;for(let a=0;a<o.length;a++){let i=o[a],n=l[a].list,r=a,u=t.getListIndex(n,"name",i);if(-1==u){s=!1;break}e.push({spec:n[u],index1:r,index2:u})}s&&(t.init(!0),e.map((e=>{t.skuClick(e.spec,e.index1,e.index2)})))}s>0&&(t.selectNum=s)},priceFilter(e=0){return"string"==typeof e&&(e=parseFloat(e)),0===this.amountType?e.toFixed(2):(e/100).toFixed(2)},pushGoodsCache(e){let{goodsIdName:t}=this;I[e[t]]=e},stop(){},previewImage(){let{selectShop:e,goodsInfo:t,goodsThumbName:o}=this,s=e.image?e.image:t[o];s&&uni.previewImage({urls:[s]})},getMaxStock(){let e=0,{selectShop:t={},goodsInfo:o={},skuListName:s,stockName:l}=this;if(t[l])e=t[l];else{let t=o[s];if(t&&t.length>0){let o=[];t.map(((e,t)=>{o.push(e[l])})),e=Math.max(...o)}}return e},numChange(e){this.$emit("num-change",e.value)}},computed:{valueCom(){return this.modelValue},maxBuyNumCom(){let e=this.getMaxStock(),t=this.maxBuyNum||1e5;return t>e&&(t=e),t},isManyCom(){let{goodsInfo:e,defaultSingleSkuName:t,specListName:o}=this,s=!0;return e[o]&&1===e[o].length&&1===e[o][0].list.length&&e[o][0].name===t&&(s=!1),s},priceCom(){let e="",t=this,{selectShop:o={},goodsInfo:s={},skuListName:l,skuIdName:a}=t;if(o[a])e=t.priceFilter(o.price);else{let o=s[l];if(o&&o.length>0){let s=[];o.map(((e,t)=>{s.push(e.price)}));let l=t.priceFilter(Math.min(...s)),a=t.priceFilter(Math.max(...s));e=l===a?l+"":`${l} - ${a}`}}return e},stockCom(){let e="",{selectShop:t={},goodsInfo:o={},skuListName:s,stockName:l}=this;if(t[l])e=t[l];else{let t=o[s];if(t&&t.length>0){let o=[];t.map(((e,t)=>{o.push(e[l])}));let s=Math.min(...o),a=Math.max(...o);e=s===a?s:`${s} - ${a}`}}return e}},watch:{valueCom(e,t){e&&this.open()},defaultGoods:{immediate:!0,handler:function(e,t){let o=this,{goodsIdName:s}=o;"object"==typeof e&&e&&e[s]&&!I[e[s]]&&o.pushGoodsCache(e)}}}},[["render",function(u,S,N,T,I,x){const w=r,v=b,B=C,A=c("number-box"),V=k;return e(),t(w,{class:l(["goods-sku-popup",x.valueCom&&I.complete?"show":"none"]),catchtouchmove:"true",onTouchmove:i(x.moveHandle,["stop","prevent"]),onClick:i(x.stop,["stop"])},{default:o((()=>[s(w,{class:"mask",onClick:S[0]||(S[0]=e=>x.close("mask"))}),s(w,{class:l(["layer attr-content",{"safe-area-inset-bottom":N.safeAreaInsetBottom}]),style:a({borderRadius:N.borderRadius+"rpx "+N.borderRadius+"rpx 0 0"})},{default:o((()=>[s(w,{class:"specification-wrapper"},{default:o((()=>[s(V,{class:"specification-wrapper-content","scroll-y":"true"},{default:o((()=>[s(w,{class:"specification-header"},{default:o((()=>[s(w,{class:"specification-left"},{default:o((()=>[s(v,{class:"product-img",src:I.selectShop.image?I.selectShop.image:I.goodsInfo[N.goodsThumbName],style:a({backgroundColor:N.goodsThumbBackgroundColor}),mode:"aspectFill",onClick:x.previewImage},null,8,["src","style","onClick"])])),_:1}),s(w,{class:"specification-right"},{default:o((()=>[s(w,{class:"price-content",style:a({color:x.themeColorFn("priceColor")})},{default:o((()=>[s(B,{class:"sign"},{default:o((()=>[n("¥")])),_:1}),s(B,{class:l(["price",x.priceCom.length>16?"price2":""])},{default:o((()=>[n(d(x.priceCom),1)])),_:1},8,["class"])])),_:1},8,["style"]),N.hideStock?(e(),t(w,{key:1,class:"inventory"})):(e(),t(w,{key:0,class:"inventory"},{default:o((()=>[n(d(N.stockText)+"："+d(x.stockCom),1)])),_:1})),m(s(w,{class:"choose"},{default:o((()=>[I.selectArr.every((e=>""==e))?p("",!0):(e(),t(B,{key:0},{default:o((()=>[n("已选："+d(I.selectArr.join(" ")),1)])),_:1}))])),_:1},512),[[h,x.isManyCom]])])),_:1})])),_:1}),s(w,{class:"specification-content"},{default:o((()=>[(e(!0),f(g,null,y(I.goodsInfo[N.specListName],((i,r)=>m((e(),t(w,{class:"specification-item",key:r},{default:o((()=>[s(w,{class:"item-title"},{default:o((()=>[n(d(i.name),1)])),_:2},1024),s(w,{class:"item-wrapper"},{default:o((()=>[(e(!0),f(g,null,y(i.list,((s,i)=>(e(),t(w,{class:l(["item-content",[s.ishow?"":"noactived",I.subIndex[r]==i?"actived":""]]),key:i,style:a([s.ishow?"":x.themeColorFn("disableStyle"),s.ishow?x.themeColorFn("btnStyle"):"",I.subIndex[r]==i?x.themeColorFn("activedStyle"):""]),onClick:e=>x.skuClick(s,r,i)},{default:o((()=>[n(d(s.name),1)])),_:2},1032,["class","style","onClick"])))),128))])),_:2},1024)])),_:2},1024)),[[h,x.isManyCom]]))),128)),s(w,{class:"number-box-view"},{default:o((()=>[s(w,{style:{flex:"1"}},{default:o((()=>[n("数量")])),_:1}),s(w,{style:{flex:"4","text-align":"right"}},{default:o((()=>[s(A,{modelValue:I.selectNum,"onUpdate:modelValue":S[1]||(S[1]=e=>I.selectNum=e),min:N.minBuyNum||1,max:x.maxBuyNumCom,step:N.stepBuyNum||1,"step-strictly":N.stepStrictly,"positive-integer":!0,onChange:x.numChange},null,8,["modelValue","min","max","step","step-strictly","onChange"])])),_:1})])),_:1})])),_:1})])),_:1}),0!=N.showClose?(e(),t(w,{key:0,class:"close",onClick:S[2]||(S[2]=e=>x.close("close"))},{default:o((()=>[s(v,{class:"close-item",src:N.closeImage},null,8,["src"])])),_:1})):p("",!0)])),_:1}),I.outFoStock||4==N.mode?(e(),t(w,{key:0,class:"btn-wrapper"},{default:o((()=>[s(w,{class:"sure",style:{color:"#ffffff","background-color":"#cccccc"}},{default:o((()=>[n(d(N.noStockText),1)])),_:1})])),_:1})):1==N.mode?(e(),t(w,{key:1,class:"btn-wrapper"},{default:o((()=>[s(w,{class:"sure add-cart",style:a([{"border-radius":"38rpx 0rpx 0rpx 38rpx"},{color:x.themeColorFn("addCartColor"),backgroundColor:x.themeColorFn("addCartBackgroundColor")}]),onClick:x.addCart},{default:o((()=>[n(d(N.addCartText),1)])),_:1},8,["style","onClick"]),s(w,{class:"sure",style:a([{"border-radius":"0rpx 38rpx 38rpx 0rpx"},{color:x.themeColorFn("buyNowColor"),backgroundColor:x.themeColorFn("buyNowBackgroundColor")}]),onClick:x.buyNow},{default:o((()=>[n(d(N.buyNowText),1)])),_:1},8,["style","onClick"])])),_:1})):2==N.mode?(e(),t(w,{key:2,class:"btn-wrapper"},{default:o((()=>[s(w,{class:"sure add-cart",style:a({color:x.themeColorFn("addCartColor"),backgroundColor:x.themeColorFn("addCartBackgroundColor")}),onClick:x.addCart},{default:o((()=>[n(d(N.addCartText),1)])),_:1},8,["style","onClick"])])),_:1})):3==N.mode?(e(),t(w,{key:3,class:"btn-wrapper"},{default:o((()=>[s(w,{class:"sure",style:a({color:x.themeColorFn("buyNowColor"),backgroundColor:x.themeColorFn("buyNowBackgroundColor")}),onClick:x.buyNow},{default:o((()=>[n(d(N.buyNowText),1)])),_:1},8,["style","onClick"])])),_:1})):p("",!0)])),_:1},8,["class","style"])])),_:1},8,["class","onTouchmove","onClick"])}],["__scopeId","data-v-4a3d8de5"]]);export{x as G};
