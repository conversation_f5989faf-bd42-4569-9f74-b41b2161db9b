<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\sharp;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\sharp\Active as ActiveModel;

/**
 * 秒杀活动会场管理
 * Class Active
 * @package app\store\controller\apps\sharp
 */
class Active extends Controller
{
    /**
     * 活动会场列表
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function list(): Json
    {
        $model = new ActiveModel;
        $list = $model->getList();
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 获取活动会场详情
     * @param int $activeId
     * @return Json
     */
    public function detail(int $activeId): Json
    {
        $detail = ActiveModel::detail($activeId);
        return $this->renderSuccess(compact('detail'));
    }

    /**
     * 修改活动状态
     * @param int $activeId 活动会场ID
     * @param int $state 活动状态
     * @return Json
     */
    public function state(int $activeId, int $state): Json
    {
        // 活动详情
        $model = ActiveModel::detail($activeId);
        if (!$model->setStatus($state)) {
            return $this->renderError('操作失败');
        }
        return $this->renderSuccess('操作成功');
    }

    /**
     * 新增活动会场
     * @return Json
     */
    public function add(): Json
    {
        // 新增记录
        $model = new ActiveModel;
        if ($model->add($this->postForm())) {
            return $this->renderSuccess('添加成功');
        }
        return $this->renderError($model->getError() ?: '添加失败');
    }

    /**
     * 删除活动会场
     * @param int $activeId 活动会场ID
     * @return Json
     */
    public function delete(int $activeId): Json
    {
        // 活动会场详情
        $model = ActiveModel::detail($activeId);
        if (!$model->setDelete()) {
            return $this->renderError($model->getError() ?: '删除失败');
        }
        return $this->renderSuccess('删除成功');
    }
}