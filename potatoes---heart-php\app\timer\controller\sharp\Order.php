<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\timer\controller\sharp;

use app\timer\controller\Controller;
use app\timer\model\sharp\Setting as SettingModel;
use app\timer\service\sharp\Order as OrderService;

/**
 * 定时任务：整点秒杀订单
 * Class Order
 * @package app\timer\controller\sharp
 */
class Order extends Controller
{
    // 当前任务唯一标识
    private string $taskKey = 'SharpOrder';

    // 任务执行间隔时长 (单位:秒)
    protected int $taskExpire = 10;

    // 当前商城ID
    private int $storeId;

    /**
     * 任务处理
     * @param array $param
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function handle(array $param)
    {
        ['storeId' => $this->storeId] = $param;
        $this->setInterval($this->storeId, $this->taskKey, $this->taskExpire, function () {
            echo $this->taskKey . PHP_EOL;
            // 未支付订单自动关闭
            $this->closeEvent();
        });
    }

    /**
     * 未支付订单自动关闭
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function closeEvent()
    {
        // 自动关闭订单的分钟数
        $closeMinute = (int)$this->getSetting()['orderClose'];
        // 执行自动关闭
        if ($closeMinute > 0) {
            $service = new OrderService;
            $service->closeEvent($this->storeId, $closeMinute);
        }
    }

    /**
     * 获取整点秒杀基本设置
     * @return array|mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getSetting()
    {
        return SettingModel::getItem('basic', $this->storeId)['order'];
    }
}