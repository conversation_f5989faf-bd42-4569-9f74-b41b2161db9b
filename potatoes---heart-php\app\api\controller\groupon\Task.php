<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\groupon;

use think\response\Json;
use app\api\controller\Controller;
use app\api\model\groupon\Task as TaskModel;
use app\api\model\groupon\Goods as GoodsModel;

/**
 * 拼团商品管理
 * Class Task
 * @package app\api\controller\groupon
 */
class Task extends Controller
{
    /**
     * 我的拼团列表
     * @return Json
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function myList(): Json
    {
        $service = new TaskModel;
        $list = $service->getMyList();
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 获取指定拼团商品的凑团列表
     * @param int $grouponGoodsId
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function listByGoods(int $grouponGoodsId): Json
    {
        $service = new TaskModel;
        $list = $service->getQuickJoinList($grouponGoodsId, 10);
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 拼团拼单详情
     * @param int $taskId 拼单ID
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(int $taskId): Json
    {
        // 拼单详情
        $model = new TaskModel;
        $detail = $model->getDetail($taskId);
        // 拼团商品详情
        $GoodsModel = new GoodsModel;
        $goods = $GoodsModel->getGoodsDetail($detail['groupon_goods_id']);
        return $this->renderSuccess(compact('detail', 'goods'));
    }
}