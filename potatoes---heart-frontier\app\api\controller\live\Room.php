<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\live;

use think\response\Json;
use app\api\controller\Controller;
use app\api\model\wxapp\LiveRoom as LiveRoomModel;

/**
 * 微信小程序直播列表
 * Class Room
 * @package app\api\controller\live
 */
class Room extends Controller
{
    /**
     * 获取直播间列表
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function list(): Json
    {
        $model = new LiveRoomModel;
        $list = $model->getList();
        return $this->renderSuccess(compact('list'));
    }
}