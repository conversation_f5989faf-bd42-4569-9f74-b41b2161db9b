<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace cores\traits;

use think\facade\Queue;

/**
 * 快捷发布队列任务
 * 仅限job层下的类use
 * Trait QueueTrait
 * @package cores\traits
 */
trait QueueTrait
{
    // 队列服务名称
    static private string $serveName = 'serve';

    /**
     * 快捷发布队列任务
     * 使用方法: \app\job\Test::dispatch(数据, 延迟);
     * @param array $data
     * @param int $secs 延迟几秒后执行
     * @return void
     */
    public static function dispatch(array $data, int $secs = 0)
    {
        // 发送发布：任务名，参数，队列名
        if ($secs == 0) {
            Queue::push(static::getJobName(), $data, static::$serveName);
        } else {
            Queue::later($secs, static::getJobName(), $data, static::$serveName);
        }
    }

    /**
     * 获取队列任务
     * @return string
     */
    private static function getJobName(): string
    {
        return get_class();
    }
}