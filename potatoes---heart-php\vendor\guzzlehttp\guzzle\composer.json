{"name": "guzzlehttp/guzzle", "description": "Guzzle is a PHP HTTP client library", "keywords": ["framework", "http", "rest", "web service", "curl", "client", "HTTP client", "PSR-7", "PSR-18"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "repositories": [{"type": "package", "package": {"name": "guzzle/client-integration-tests", "version": "v3.0.2", "dist": {"url": "https://codeload.github.com/guzzle/client-integration-tests/zip/2c025848417c1135031fdf9c728ee53d0a7ceaee", "type": "zip"}, "require": {"php": "^7.2.5 || ^8.0", "phpunit/phpunit": "^7.5.20 || ^8.5.8 || ^9.3.11", "php-http/message": "^1.0 || ^2.0", "guzzlehttp/psr7": "^1.7 || ^2.0", "th3n3rd/cartesian-product": "^0.3"}, "autoload": {"psr-4": {"Http\\Client\\Tests\\": "src/"}}, "bin": ["bin/http_test_server"]}}], "require": {"php": "^7.2.5 || ^8.0", "ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"ext-curl": "*", "bamarni/composer-bin-plugin": "^1.8.2", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "config": {"allow-plugins": {"bamarni/composer-bin-plugin": true}, "preferred-install": "dist", "sort-packages": true}, "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "autoload-dev": {"psr-4": {"GuzzleHttp\\Tests\\": "tests/"}}}