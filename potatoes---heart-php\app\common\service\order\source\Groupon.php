<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\service\order\source;

use cores\exception\BaseException;
use app\common\model\groupon\Task as TaskModel;

/**
 * 订单来源-拼团订单
 * Class Groupon
 * @package app\common\service\order\source
 */
class Groupon extends Basics
{
    /**
     * 判断订单是否允许付款
     * @param $order
     * @return bool
     * @throws BaseException
     */
    public function checkOrderStatusOnPay($order): bool
    {
        // 判断订单状态
        if (!$this->checkOrderStatusOnPayCommon($order)) {
            return false;
        }
        // 判断商品状态、库存
        if (!$this->checkGoodsStatusOnPayCommon($order['goods'], false)) {
            return false;
        }
        // 验证当前拼单是否允许加入新成员
        if (!$this->checkGrouponTaskJoin($order)) {
            return false;
        }
        return true;
    }

    /**
     * 判断订单是否允许取消
     * @param $order
     * @return bool
     * @throws BaseException
     */
    public function checkOrderByCancel($order): bool
    {
        // 判断订单是否允许取消
        if (!$this->checkOrderByCancelCommon($order)) {
            return false;
        }
        // 判断拼单是否完成 ( 拼单未完成时不允许取消 )
        if (!$this->checkGrouponTaskComplete($order)) {
            return false;
        }
        return true;
    }

    /**
     * 判断订单是否允许发货
     * @param $order
     * @return bool
     * @throws BaseException
     */
    public function checkOrderByDelivery($order): bool
    {
        // 判断订单是否允许发货
        if (!$this->checkOrderByDeliveryCommon($order)) {
            return false;
        }
        // 判断拼单是否成功 ( 拼单未成功时不允许发货 )
        if (!$this->checkGrouponTaskSuccess($order)) {
            return false;
        }
        return true;
    }

    /**
     * 验证当前拼单是否已完成
     * @param $order
     * @return bool
     * @throws BaseException
     */
    private function checkGrouponTaskComplete($order): bool
    {
        $taskId = $order['order_source_id'];
        $model = new TaskModel;
        if ($taskId > 0 && !$model->checkTaskComplete($taskId)) {
            $this->error = '很抱歉，该拼团任务尚未结束 不可以取消订单';
            return false;
        }
        return true;
    }

    /**
     * 验证当前拼单是否已成功
     * @param $order
     * @return bool
     * @throws BaseException
     */
    private function checkGrouponTaskSuccess($order): bool
    {
        $taskId = $order['order_source_id'];
        $model = new TaskModel;
        if ($taskId > 0 && !$model->checkTaskSuccess($taskId)) {
            $this->error = '很抱歉，该拼团任务未成功 不能进行发货；' . "订单号：{$order['order_no']}";
            return false;
        }
        return true;
    }

    /**
     * 验证当前拼单是否允许加入新成员
     * @param $order
     * @return bool
     * @throws BaseException
     */
    private function checkGrouponTaskJoin($order): bool
    {
        $taskId = $order['order_source_id'];
        $model = new TaskModel;
        if ($taskId > 0 && !$model->checkToJoinByTaskId($taskId, $order['user_id'])) {
            $this->error = $model->getError();
            return false;
        }
        return true;
    }
}