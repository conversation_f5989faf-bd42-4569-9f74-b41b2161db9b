import{ac as s,K as a,ad as t,o as e,c as l,w as o,n,e as i,i as d,a as r,f as c,t as u,b as f,F as _,j as h,d as m,k as g,r as k,g as p,V as b,S as w,x as y}from"./index-ae8bbb19.js";import{_ as v}from"./u-modal.b5d074c6.js";import{r as x}from"./uni-app.es.ee1d5b08.js";import{W as C}from"./wxofficial.cfa745b7.js";import{A as I}from"./index.0c0be40a.js";import{C as S}from"./index.002ef29d.js";import{b as B,a as L}from"./index.c0835ba6.js";import{c as j,d as $,h as P,e as D}from"./task.dfabb3d7.js";import{_ as T}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-loading.01f91c7b.js";import"./u-popup.d74d5514.js";import"./u-icon.b463ad8a.js";const R=T({components:{AvatarImage:I,CountDown:S},mixins:[C],data:()=>({isLoading:!0,taskId:void 0,activeId:void 0,task:{},active:{},goods:{},goodsSkuInfo:{},helpList:[],isCreater:!1,isCut:!1,setting:{},showRules:!1,disabled:!1,showBuyBtn:!1,showShareBtn:!1,showCatBtn:!1,showOtherBtn:!1}),onLoad(s){this.taskId=s.taskId,this.onRefreshPage()},methods:{onRefreshPage(){const s=this;s.isLoading=!0,s.getTaskDetail().then((a=>{Promise.all([s.getActiveDetail(),s.getGoodsBasic(),s.getGoodsSku(),s.getHelpList()]).then((()=>{s.initShowBtn(),s.setWxofficialShareData()})).finally((()=>s.isLoading=!1))}))},getTaskDetail(){const s=this;return new Promise(((a,t)=>{j(s.taskId).then((t=>{s.task=t.data.taskInfo,s.activeId=s.task.active_id,s.isCreater=t.data.isCreater,s.isCut=t.data.isCut,s.setting=t.data.setting,a(t)})).catch(t)}))},getActiveDetail(){const s=this;return new Promise(((a,t)=>{$(s.activeId).then((t=>{s.active=t.data.active,a(t)})).catch(t)}))},getGoodsBasic(){const s=this,a=s.task.goods_id;return new Promise(((t,e)=>{B(a,!1).then((a=>{s.goods=a.data.detail,t(a)})).catch(e)}))},getGoodsSku(){const s=this,a=s.task.goods_id,t=s.task.goods_sku_id;return new Promise(((e,l)=>{L(a,t).then((a=>{s.goodsSkuInfo=a.data.skuInfo,e(a)})).catch(l)}))},getHelpList(){const s=this;return new Promise(((a,t)=>{P(s.taskId).then((t=>{s.helpList=t.data.list,a(t)})).catch(t)}))},initShowBtn(){const s=this,a=s.isCreater&&!s.task.is_buy&&s.task.status&&(!s.active.is_floor_buy||s.task.is_floor),t=!s.isCreater&&!s.isCut&&!s.task.is_floor&&s.task.status,e=!t&&!s.task.is_floor&&s.task.status,l=!a&&!e&&!t;s.showBuyBtn=a,s.showCatBtn=t,s.showShareBtn=e,s.showOtherBtn=l},handleShowRules(){this.showRules=!0},handleBuyNow(){this.$navTo("pages/checkout/index",{mode:"bargain",taskId:this.taskId})},handleHelpCut(){const s=this;s.disabled=!0,D(s.taskId).then((a=>{s.$toast(a.message),setTimeout((()=>s.onRefreshPage()),1800)})).finally((()=>s.disabled=!1))},handleShareBtn(){this.handleCopyLink()},handleCopyLink(){const s=this;s.getShareUrl().then((a=>{uni.setClipboardData({data:a,success:()=>s.$toast("复制链接成功，快去发送给朋友吧"),fail:a=>s.$toast("复制失败")})}))},getShareUrl(){const{path:e,query:l}=s();return new Promise(((s,o)=>{a.h5Url().then((a=>{const o=t(a,e,l);s(o)}))}))},setWxofficialShareData(){const{active:s,goods:a}=this;this.updateShareCardData({title:s.share_title,desc:s.prompt_words,imgUrl:a.goods_image})}},onShareAppMessage(){const s=this,a=s.$getShareUrlParams({taskId:s.taskId});return{title:s.active.share_title,path:`/pages/bargain/task?${a}`}},onShareTimeline(){const s=this,a=s.$getShareUrlParams({taskId:s.taskId});return{title:s.active.share_title,path:`/pages/bargain/task?${a}`}}},[["render",function(s,a,t,C,I,S){const B=g,L=d,j=k("avatar-image"),$=p,P=b,D=k("count-down"),T=w,R=x(y("u-modal"),v);return I.isLoading?i("",!0):(e(),l(L,{key:0,class:"container",style:n(s.appThemeStyle)},{default:o((()=>[r(L,{class:"header dis-flex flex-x-between"},{default:o((()=>[r(L,{class:"item-touch",onClick:a[0]||(a[0]=a=>s.$navTo("pages/index/index"))},{default:o((()=>[r(B,null,{default:o((()=>[c("返回首页")])),_:1})])),_:1}),r(L,{class:"item-touch",onClick:a[1]||(a[1]=s=>S.handleShowRules())},{default:o((()=>[r(B,null,{default:o((()=>[c("玩法详情")])),_:1})])),_:1})])),_:1}),r(L,{class:"content"},{default:o((()=>[r(L,{class:"infos-wrap"},{default:o((()=>[r(L,{class:"infos-top"},{default:o((()=>[r(L,{class:"infos-img"},{default:o((()=>[r(j,{url:I.task.user.avatar_url,width:104},null,8,["url"])])),_:1}),r(L,{class:"infos-name"},{default:o((()=>[r(B,null,{default:o((()=>[c(u(I.task.user.nick_name),1)])),_:1})])),_:1})])),_:1}),r(L,{class:"infos-mask"},{default:o((()=>[I.active.prompt_words?(e(),l(L,{key:0,class:"infos-prompt"},{default:o((()=>[r(B,null,{default:o((()=>[c(u(I.active.prompt_words),1)])),_:1})])),_:1})):i("",!0),r(L,{class:"infos-item",onClick:a[2]||(a[2]=a=>s.$navTo("pages/bargain/goods/index",{activeId:I.activeId,goodsId:I.goods.goods_id}))},{default:o((()=>[r(L,{class:"infos-item-img"},{default:o((()=>[r($,{class:"image",src:I.goodsSkuInfo.goods_image?I.goodsSkuInfo.goods_image:I.goods.goods_image},null,8,["src"])])),_:1}),r(L,{class:"infos-item-info"},{default:o((()=>[r(L,{class:"infos-item-name"},{default:o((()=>[r(B,{class:"twoline-hide"},{default:o((()=>[c(u(I.goods.goods_name),1)])),_:1})])),_:1}),r(L,{class:"infos-item-stock"},{default:o((()=>[r(L,{class:"stock-widget"},{default:o((()=>[r(B,null,{default:o((()=>[c("仅剩")])),_:1}),r(B,{class:"stock-num"},{default:o((()=>[c(u(I.goodsSkuInfo.stock_num),1)])),_:1}),r(B,null,{default:o((()=>[c("件")])),_:1})])),_:1})])),_:1}),r(L,{class:"infos-item-price dis-flex flex-y-end"},{default:o((()=>[r(B,{class:"price1 col-m"},{default:o((()=>[c("底价¥")])),_:1}),r(B,{class:"price2 col-m"},{default:o((()=>[c(u(I.task.floor_price),1)])),_:1}),r(B,{class:"price3"},{default:o((()=>[c("¥"+u(I.task.goods_price),1)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),r(L,{class:"connect"},{default:o((()=>[r(L,{class:"connect-ring bgf-ring--left"},{default:o((()=>[r(B,{class:"line"})])),_:1}),r(L,{class:"connect-ring bgf-ring--right"},{default:o((()=>[r(B,{class:"line"})])),_:1})])),_:1}),r(L,{class:"bargain-wrap"},{default:o((()=>[r(L,{class:"bargain-info"},{default:o((()=>[I.task.status?(e(),l(L,{key:0,class:"bargain-ing"},{default:o((()=>[I.task.is_floor?(e(),f(_,{key:1},[r(B,null,{default:o((()=>[c("已砍至最低")])),_:1}),r(B,{class:"focal col-m"},{default:o((()=>[c(u(I.task.floor_price),1)])),_:1}),r(B,null,{default:o((()=>[c("元，砍价成功！")])),_:1})],64)):(e(),f(_,{key:0},[r(B,null,{default:o((()=>[c("已砍")])),_:1}),r(B,{class:"focal col-m"},{default:o((()=>[c(u(I.task.cut_money),1)])),_:1}),r(B,null,{default:o((()=>[c("元，还差")])),_:1}),r(B,{class:"focal col-m"},{default:o((()=>[c(u(I.task.surplus_money),1)])),_:1}),r(B,null,{default:o((()=>[c("元")])),_:1})],64))])),_:1})):(e(),l(L,{key:1,class:"bargain-ing"},{default:o((()=>[r(B,{class:"col-9"},{default:o((()=>[c("该砍价任务已结束～")])),_:1})])),_:1}))])),_:1}),r(L,{class:"bgn__process m-top30"},{default:o((()=>[r(L,{class:"bgn__process-bottom"},{default:o((()=>[r(L,{class:"bgn__process-process process--ani",style:n({width:`${I.task.bargain_rate}%`})},null,8,["style"])])),_:1})])),_:1}),r(L,{class:"btn-container m-top30 dis-flex flex-x-center"},{default:o((()=>[I.showBuyBtn?(e(),l(L,{key:0,class:h(["btn-item btn-item__buy",{complete:I.task.is_floor}]),onClick:a[3]||(a[3]=s=>S.handleBuyNow())},{default:o((()=>[r(B,null,{default:o((()=>[c("立即购买")])),_:1})])),_:1},8,["class"])):i("",!0),I.showShareBtn?(e(),l(P,{key:1,"open-type":"share",class:"btn-normal",onClick:a[4]||(a[4]=s=>S.handleShareBtn())},{default:o((()=>[r(L,{class:"btn-item btn-item__main"},{default:o((()=>[r(B,null,{default:o((()=>[c("邀请好友砍价")])),_:1})])),_:1})])),_:1})):i("",!0),I.showCatBtn?(e(),l(L,{key:2,class:"btn-item btn-item__main btn-item-long",onClick:a[5]||(a[5]=s=>S.handleHelpCut())},{default:o((()=>[r(B,null,{default:o((()=>[c("帮TA砍一刀")])),_:1})])),_:1})):i("",!0),I.showOtherBtn?(e(),l(L,{key:3,class:"btn-item btn-item__main btn-item-long",onClick:a[6]||(a[6]=a=>s.$navTo("pages/bargain/index"))},{default:o((()=>[r(B,null,{default:o((()=>[c("查看其他砍价活动")])),_:1})])),_:1})):i("",!0)])),_:1}),I.task.status?(e(),l(L,{key:0,class:"bargain-p"},{default:o((()=>[r(L,{class:"bargain-people dis-flex flex-x-center flex-y-center"},{default:o((()=>[r(B,null,{default:o((()=>[c("活动还剩")])),_:1}),r(D,{date:I.active.end_time,separator:"zh",theme:"text"},null,8,["date"]),r(B,null,{default:o((()=>[c("结束，快来砍价吧~")])),_:1})])),_:1})])),_:1})):i("",!0)])),_:1}),I.helpList.length?(e(),l(L,{key:0,class:"records-container"},{default:o((()=>[r(L,{class:"records"},{default:o((()=>[r(L,{class:"records-back"}),r(L,{class:"records-content"},{default:o((()=>[r(L,{class:"records-h2"},{default:o((()=>[r(B,null,{default:o((()=>[c("好友助力榜")])),_:1})])),_:1}),r(L,{class:"friend-help"},{default:o((()=>[(e(!0),f(_,null,m(I.helpList,((s,a)=>(e(),l(L,{class:"records-item",key:a},{default:o((()=>[r(L,{class:"records-left"},{default:o((()=>[r(j,{url:s.user.avatar_url,width:70},null,8,["url"]),r(B,{class:"nick-name"},{default:o((()=>[c(u(s.user.nick_name),1)])),_:2},1024)])),_:2},1024),r(L,{class:"records-right"},{default:o((()=>[r(B,{class:"bold m-r-6"},{default:o((()=>[c("帮砍了")])),_:1}),r(B,{class:"red"},{default:o((()=>[c("¥"+u(s.cut_money),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})])),_:1})):i("",!0)])),_:1}),I.isLoading?i("",!0):(e(),l(R,{key:0,modelValue:I.showRules,"onUpdate:modelValue":a[7]||(a[7]=s=>I.showRules=s),title:"砍价规则"},{default:o((()=>[r(T,{style:{height:"610rpx"},"scroll-y":!0},{default:o((()=>[r(L,{class:"pops-content"},{default:o((()=>[r(B,null,{default:o((()=>[c(u(I.setting.rulesDesc),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"]))])),_:1},8,["style"]))}],["__scopeId","data-v-1b415d66"]]);export{R as default};
