<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\job\controller\goods;

use app\job\service\goods\Import as GoodsImportService;
use cores\BaseJob;
use cores\traits\QueueTrait;
use cores\exception\BaseException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 队列任务：商品批量导入
 * Class Import
 * @package app\job\controller
 */
class Import extends BaseJob
{
    use QueueTrait;

    /**
     * 消费队列任务：商品导入
     * @param array $data 参数 [index队列顺序；totalCount商品总数量；list商品列表；storeId商城ID]
     * @return bool 返回结果
     * @throws BaseException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function handle(array $data): bool
    {
        $time = date('H:i:s');
        echo "\n  ---- Import ----  {$time} ----   \n";

        $service = new GoodsImportService;
        return $service->batch($data['list'], $data['recordId'], $data['storeId']);
    }
}