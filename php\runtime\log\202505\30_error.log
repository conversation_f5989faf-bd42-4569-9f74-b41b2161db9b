[2025-05-30 23:39:31] [error] ********** GET http://***************:8094/nmaplowercheck1748619571
[ message ] [0] 控制器不存在:app\controller\Nmaplowercheck1748619571
[ file ] /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/route/dispatch/Controller.php:76
[ header ] Array
(
    [user-agent] => Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)
    [host] => ***************:8094
    [connection] => close
    [content-length] => 
    [content-type] => 
)
[ param ] Array
(
)

#0 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/route/Dispatch.php(90): think\route\dispatch\Controller->exec()
#1 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Route.php(785): think\route\Dispatch->run()
#2 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(59): think\Route->think\{closure}()
#3 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(66): think\Pipeline->think\{closure}()
#4 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Route.php(786): think\Pipeline->then()
#5 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(216): think\Route->dispatch()
#6 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(206): think\Http->dispatchToRoute()
#7 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(59): think\Http->think\{closure}()
#8 /www/wwwroot/xinjiang.kj/vendor/topthink/think-multi-app/src/MultiApp.php(65): think\Pipeline->think\{closure}()
#9 [internal function]: think\app\MultiApp->handle()
#10 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#11 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#12 /www/wwwroot/xinjiang.kj/cores/middleware/AppLog.php(43): think\Pipeline->think\{closure}()
#13 [internal function]: cores\middleware\AppLog->handle()
#14 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#15 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#16 /www/wwwroot/xinjiang.kj/cores/middleware/AllowCrossDomain.php(98): think\Pipeline->think\{closure}()
#17 [internal function]: cores\middleware\AllowCrossDomain->handle()
#18 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#19 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#20 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(66): think\Pipeline->think\{closure}()
#21 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(207): think\Pipeline->then()
#22 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(170): think\Http->runWithRequest()
#23 /www/wwwroot/xinjiang.kj/public/index.php(15): think\Http->run()
#24 {main}
--------------------------------------------------------------------------------------------
[2025-05-30 23:39:31] [error] ********** POST http://***************:8094/sdk
[ message ] [0] 控制器不存在:app\controller\Sdk
[ file ] /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/route/dispatch/Controller.php:76
[ header ] Array
(
    [user-agent] => Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)
    [content-length] => 441
    [connection] => close
    [host] => ***************:8094
    [content-type] => 
)
[ param ] Array
(
)

#0 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/route/Dispatch.php(90): think\route\dispatch\Controller->exec()
#1 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Route.php(785): think\route\Dispatch->run()
#2 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(59): think\Route->think\{closure}()
#3 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(66): think\Pipeline->think\{closure}()
#4 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Route.php(786): think\Pipeline->then()
#5 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(216): think\Route->dispatch()
#6 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(206): think\Http->dispatchToRoute()
#7 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(59): think\Http->think\{closure}()
#8 /www/wwwroot/xinjiang.kj/vendor/topthink/think-multi-app/src/MultiApp.php(65): think\Pipeline->think\{closure}()
#9 [internal function]: think\app\MultiApp->handle()
#10 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#11 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#12 /www/wwwroot/xinjiang.kj/cores/middleware/AppLog.php(43): think\Pipeline->think\{closure}()
#13 [internal function]: cores\middleware\AppLog->handle()
#14 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#15 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#16 /www/wwwroot/xinjiang.kj/cores/middleware/AllowCrossDomain.php(98): think\Pipeline->think\{closure}()
#17 [internal function]: cores\middleware\AllowCrossDomain->handle()
#18 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#19 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#20 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(66): think\Pipeline->think\{closure}()
#21 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(207): think\Pipeline->then()
#22 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(170): think\Http->runWithRequest()
#23 /www/wwwroot/xinjiang.kj/public/index.php(15): think\Http->run()
#24 {main}
--------------------------------------------------------------------------------------------
[2025-05-30 23:39:31] [error] ********** GET http://***************:8094/NmapUpperCheck1748619571
[ message ] [0] 控制器不存在:app\controller\NmapUpperCheck1748619571
[ file ] /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/route/dispatch/Controller.php:76
[ header ] Array
(
    [user-agent] => Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)
    [host] => ***************:8094
    [connection] => close
    [content-length] => 
    [content-type] => 
)
[ param ] Array
(
)

#0 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/route/Dispatch.php(90): think\route\dispatch\Controller->exec()
#1 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Route.php(785): think\route\Dispatch->run()
#2 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(59): think\Route->think\{closure}()
#3 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(66): think\Pipeline->think\{closure}()
#4 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Route.php(786): think\Pipeline->then()
#5 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(216): think\Route->dispatch()
#6 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(206): think\Http->dispatchToRoute()
#7 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(59): think\Http->think\{closure}()
#8 /www/wwwroot/xinjiang.kj/vendor/topthink/think-multi-app/src/MultiApp.php(65): think\Pipeline->think\{closure}()
#9 [internal function]: think\app\MultiApp->handle()
#10 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#11 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#12 /www/wwwroot/xinjiang.kj/cores/middleware/AppLog.php(43): think\Pipeline->think\{closure}()
#13 [internal function]: cores\middleware\AppLog->handle()
#14 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#15 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#16 /www/wwwroot/xinjiang.kj/cores/middleware/AllowCrossDomain.php(98): think\Pipeline->think\{closure}()
#17 [internal function]: cores\middleware\AllowCrossDomain->handle()
#18 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#19 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#20 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(66): think\Pipeline->think\{closure}()
#21 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(207): think\Pipeline->then()
#22 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(170): think\Http->runWithRequest()
#23 /www/wwwroot/xinjiang.kj/public/index.php(15): think\Http->run()
#24 {main}
--------------------------------------------------------------------------------------------
[2025-05-30 23:39:31] [error] ********** GET http://***************:8094/Nmap/folder/check1748619571
[ message ] [0] 控制器不存在:app\controller\Nmap
[ file ] /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/route/dispatch/Controller.php:76
[ header ] Array
(
    [user-agent] => Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)
    [host] => ***************:8094
    [connection] => close
    [content-length] => 
    [content-type] => 
)
[ param ] Array
(
)

#0 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/route/Dispatch.php(90): think\route\dispatch\Controller->exec()
#1 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Route.php(785): think\route\Dispatch->run()
#2 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(59): think\Route->think\{closure}()
#3 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(66): think\Pipeline->think\{closure}()
#4 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Route.php(786): think\Pipeline->then()
#5 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(216): think\Route->dispatch()
#6 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(206): think\Http->dispatchToRoute()
#7 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(59): think\Http->think\{closure}()
#8 /www/wwwroot/xinjiang.kj/vendor/topthink/think-multi-app/src/MultiApp.php(65): think\Pipeline->think\{closure}()
#9 [internal function]: think\app\MultiApp->handle()
#10 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#11 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#12 /www/wwwroot/xinjiang.kj/cores/middleware/AppLog.php(43): think\Pipeline->think\{closure}()
#13 [internal function]: cores\middleware\AppLog->handle()
#14 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#15 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#16 /www/wwwroot/xinjiang.kj/cores/middleware/AllowCrossDomain.php(98): think\Pipeline->think\{closure}()
#17 [internal function]: cores\middleware\AllowCrossDomain->handle()
#18 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#19 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#20 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(66): think\Pipeline->think\{closure}()
#21 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(207): think\Pipeline->then()
#22 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(170): think\Http->runWithRequest()
#23 /www/wwwroot/xinjiang.kj/public/index.php(15): think\Http->run()
#24 {main}
--------------------------------------------------------------------------------------------
[2025-05-30 23:39:32] [error] ********** GET http://***************:8094/evox/about
[ message ] [0] 控制器不存在:app\controller\Evox
[ file ] /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/route/dispatch/Controller.php:76
[ header ] Array
(
    [user-agent] => Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)
    [host] => ***************:8094
    [connection] => close
    [content-length] => 
    [content-type] => 
)
[ param ] Array
(
)

#0 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/route/Dispatch.php(90): think\route\dispatch\Controller->exec()
#1 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Route.php(785): think\route\Dispatch->run()
#2 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(59): think\Route->think\{closure}()
#3 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(66): think\Pipeline->think\{closure}()
#4 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Route.php(786): think\Pipeline->then()
#5 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(216): think\Route->dispatch()
#6 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(206): think\Http->dispatchToRoute()
#7 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(59): think\Http->think\{closure}()
#8 /www/wwwroot/xinjiang.kj/vendor/topthink/think-multi-app/src/MultiApp.php(65): think\Pipeline->think\{closure}()
#9 [internal function]: think\app\MultiApp->handle()
#10 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#11 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#12 /www/wwwroot/xinjiang.kj/cores/middleware/AppLog.php(43): think\Pipeline->think\{closure}()
#13 [internal function]: cores\middleware\AppLog->handle()
#14 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#15 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#16 /www/wwwroot/xinjiang.kj/cores/middleware/AllowCrossDomain.php(98): think\Pipeline->think\{closure}()
#17 [internal function]: cores\middleware\AllowCrossDomain->handle()
#18 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#19 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#20 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(66): think\Pipeline->think\{closure}()
#21 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(207): think\Pipeline->then()
#22 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(170): think\Http->runWithRequest()
#23 /www/wwwroot/xinjiang.kj/public/index.php(15): think\Http->run()
#24 {main}
--------------------------------------------------------------------------------------------
[2025-05-30 23:39:32] [error] ********** GET http://***************:8094/HNAP1
[ message ] [0] 控制器不存在:app\controller\HNAP1
[ file ] /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/route/dispatch/Controller.php:76
[ header ] Array
(
    [user-agent] => Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)
    [host] => ***************:8094
    [connection] => close
    [content-length] => 
    [content-type] => 
)
[ param ] Array
(
)

#0 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/route/Dispatch.php(90): think\route\dispatch\Controller->exec()
#1 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Route.php(785): think\route\Dispatch->run()
#2 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(59): think\Route->think\{closure}()
#3 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(66): think\Pipeline->think\{closure}()
#4 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Route.php(786): think\Pipeline->then()
#5 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(216): think\Route->dispatch()
#6 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(206): think\Http->dispatchToRoute()
#7 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(59): think\Http->think\{closure}()
#8 /www/wwwroot/xinjiang.kj/vendor/topthink/think-multi-app/src/MultiApp.php(65): think\Pipeline->think\{closure}()
#9 [internal function]: think\app\MultiApp->handle()
#10 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#11 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#12 /www/wwwroot/xinjiang.kj/cores/middleware/AppLog.php(43): think\Pipeline->think\{closure}()
#13 [internal function]: cores\middleware\AppLog->handle()
#14 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#15 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#16 /www/wwwroot/xinjiang.kj/cores/middleware/AllowCrossDomain.php(98): think\Pipeline->think\{closure}()
#17 [internal function]: cores\middleware\AllowCrossDomain->handle()
#18 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Middleware.php(142): call_user_func()
#19 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(85): think\Middleware->think\{closure}()
#20 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Pipeline.php(66): think\Pipeline->think\{closure}()
#21 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(207): think\Pipeline->then()
#22 /www/wwwroot/xinjiang.kj/vendor/topthink/framework/src/think/Http.php(170): think\Http->runWithRequest()
#23 /www/wwwroot/xinjiang.kj/public/index.php(15): think\Http->run()
#24 {main}
--------------------------------------------------------------------------------------------
