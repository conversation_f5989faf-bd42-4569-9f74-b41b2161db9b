<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\service;

use cores\cloud\Address as AddressCloud;
use app\api\model\Region as RegionModel;
use cores\exception\BaseException;

/**
 * 服务类: 收货地址
 * Class Address
 * @package app\api\service
 */
class Address
{
    /**
     * 收货地址智能解析
     * @param string $content
     * @return array
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function analysis(string $content): array
    {
        // 请求云API解析收货地址
        $AddressCloud = new AddressCloud;
        $detail = $AddressCloud->analysis($content);
        // 格式化数据
        $data = [
            'name' => $detail['detail']['person'],
            'phone' => $detail['detail']['mobile'],
            'detail' => $detail['detail']['address'],
            'region' => [
                'province' => $detail['detail']['province'],
                'city' => $detail['detail']['city'],
                'region' => $detail['detail']['district'],
            ],
        ];
        // 格式化地区ID
        $data['province_id'] = RegionModel::getIdByName($detail['detail']['province'] ?? '', 1);
        $data['city_id'] = RegionModel::getIdByName($detail['detail']['city'] ?? '', 2, $data['province_id']);
        $data['region_id'] = RegionModel::getIdByName($detail['detail']['district'] ?? '', 3, $data['city_id']);
        return $data;
    }
}