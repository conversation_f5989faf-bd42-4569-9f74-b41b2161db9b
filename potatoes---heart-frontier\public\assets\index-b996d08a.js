!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const e={},t=function(t,n,o){if(!n||0===n.length)return t();const r=document.getElementsByTagName("link");return Promise.all(n.map((t=>{if(t=function(e,t){return new URL(e,t).href}(t,o),t in e)return;e[t]=!0;const n=t.endsWith(".css"),i=n?'[rel="stylesheet"]':"";if(!!o)for(let e=r.length-1;e>=0;e--){const o=r[e];if(o.href===t&&(!n||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${t}"]${i}`))return;const a=document.createElement("link");return a.rel=n?"stylesheet":"modulepreload",n||(a.as="script",a.crossOrigin=""),a.href=t,document.head.appendChild(a),n?new Promise(((e,n)=>{a.addEventListener("load",e),a.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${t}`))))})):void 0}))).then((()=>t()))};function n(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}function o(e){if(C(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],i=I(r)?s(r):o(r);if(i)for(const e in i)t[e]=i[e]}return t}return I(e)||L(e)?e:void 0}const r=/;(?![^(]*\))/g,i=/:([^]+)/,a=/\/\*.*?\*\//gs;function s(e){const t={};return e.replace(a,"").split(r).forEach((e=>{if(e){const n=e.split(i);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function l(e){let t="";if(I(e))t=e;else if(C(e))for(let n=0;n<e.length;n++){const o=l(e[n]);o&&(t+=o+" ")}else if(L(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const c=n("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function u(e){return!!e||""===e}function d(e,t){if(e===t)return!0;let n=M(e),o=M(t);if(n||o)return!(!n||!o)&&e.getTime()===t.getTime();if(n=O(e),o=O(t),n||o)return e===t;if(n=C(e),o=C(t),n||o)return!(!n||!o)&&function(e,t){if(e.length!==t.length)return!1;let n=!0;for(let o=0;n&&o<e.length;o++)n=d(e[o],t[o]);return n}(e,t);if(n=L(e),o=L(t),n||o){if(!n||!o)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e){const o=e.hasOwnProperty(n),r=t.hasOwnProperty(n);if(o&&!r||!o&&r||!d(e[n],t[n]))return!1}}return String(e)===String(t)}function p(e,t){return e.findIndex((e=>d(e,t)))}const f=e=>I(e)?e:null==e?"":C(e)||L(e)&&(e.toString===D||!P(e.toString))?JSON.stringify(e,h,2):String(e),h=(e,t)=>t&&t.__v_isRef?h(e,t.value):E(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n])=>(e[`${t} =>`]=n,e)),{})}:A(t)?{[`Set(${t.size})`]:[...t.values()]}:!L(t)||C(t)||B(t)?t:String(t),g={},m=[],v=()=>{},y=()=>!1,b=/^on[^a-z]/,_=e=>b.test(e),w=e=>e.startsWith("onUpdate:"),x=Object.assign,T=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},S=Object.prototype.hasOwnProperty,k=(e,t)=>S.call(e,t),C=Array.isArray,E=e=>"[object Map]"===R(e),A=e=>"[object Set]"===R(e),M=e=>"[object Date]"===R(e),P=e=>"function"==typeof e,I=e=>"string"==typeof e,O=e=>"symbol"==typeof e,L=e=>null!==e&&"object"==typeof e,$=e=>L(e)&&P(e.then)&&P(e.catch),D=Object.prototype.toString,R=e=>D.call(e),B=e=>"[object Object]"===R(e),N=e=>I(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,q=n(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),j=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},z=/-(\w)/g,V=j((e=>e.replace(z,((e,t)=>t?t.toUpperCase():"")))),F=/\B([A-Z])/g,U=j((e=>e.replace(F,"-$1").toLowerCase())),W=j((e=>e.charAt(0).toUpperCase()+e.slice(1))),H=j((e=>e?`on${W(e)}`:"")),Y=(e,t)=>!Object.is(e,t),X=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},G=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},J=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Q;const K=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view"].map((e=>"uni-"+e));const Z=["%","%"],ee=/^([a-z-]+:)?\/\//i,te=/^data:.*,.*/;function ne(e){return e&&(e.appContext?e.proxy:e)}function oe(e){if(!e)return;let t=e.type.name;for(;t&&(n=U(t),-1!==K.indexOf("uni-"+n.replace("v-uni-","")));)t=(e=e.parent).type.name;var n;return e.proxy}function re(e){return 1===e.nodeType}function ie(e){return 0===e.indexOf("/")}function ae(e){return ie(e)?e:"/"+e}function se(e){return ie(e)?e.slice(1):e}function le(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}const ce=e=>e>9?e:"0"+e;function ue({date:e=new Date,mode:t="date"}){return"time"===t?ce(e.getHours())+":"+ce(e.getMinutes()):e.getFullYear()+"-"+ce(e.getMonth()+1)+"-"+ce(e.getDate())}function de(e,t){e=e||{},I(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?P(e.success)&&e.success(t):P(e.fail)&&e.fail(t),P(e.complete)&&e.complete(t)}function pe(e){return V(e.substring(5))}const fe=le((()=>{const e=HTMLElement.prototype,t=e.setAttribute;e.setAttribute=function(e,n){if(e.startsWith("data-")&&this.tagName.startsWith("UNI-")){(this.__uniDataset||(this.__uniDataset={}))[pe(e)]=n}t.call(this,e,n)};const n=e.removeAttribute;e.removeAttribute=function(e){this.__uniDataset&&e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&delete this.__uniDataset[pe(e)],n.call(this,e)}}));function he(e){return x({},e.dataset,e.__uniDataset)}const ge=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function me(e){return{passive:e}}function ve(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:he(e),offsetTop:n,offsetLeft:o}}function ye(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function be(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=ye(e[n])}catch(kC){t[n]=e[n]}})),t}const _e=/\+/g;function we(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(_e," ");let r=e.indexOf("="),i=ye(r<0?e:e.slice(0,r)),a=r<0?null:ye(e.slice(r+1));if(i in t){let e=t[i];C(e)||(e=t[i]=[e]),e.push(a)}else t[i]=a}return t}function xe(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);const i=()=>e.apply(this,arguments);r=o(i,t)};return i.cancel=function(){n(r)},i}class Te{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const Se=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"],ke=["onLoad","onShow"];const Ce=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];let Ee;const Ae=[];const Me=le(((e,t)=>{if(P(e._component.onError))return t(e)})),Pe=function(){};Pe.prototype={on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t)for(var i=0,a=o.length;i<a;i++)o[i].fn!==t&&o[i].fn._!==t&&r.push(o[i]);return r.length?n[e]=r:delete n[e],this}};var Ie=Pe;const Oe={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function Le(e,t={},n="light"){const o=t[n],r={};return o?(Object.keys(e).forEach((i=>{let a=e[i];r[i]=(()=>{if(B(a))return Le(a,t,n);if(C(a))return a.map((e=>B(e)?Le(e,t,n):e));if(I(a)&&a.startsWith("@")){const t=a.replace("@","");let n=o[t]||a;switch(i){case"titleColor":n="black"===n?"#000000":"#ffffff";break;case"borderStyle":n=(e=n)&&e in Oe?Oe[e]:e}return n}var e;return a})()})),r):e}let $e;class De{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=$e,!e&&$e&&(this.index=($e.scopes||($e.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=$e;try{return $e=this,e()}finally{$e=t}}}on(){$e=this}off(){$e=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function Re(e){return new De(e)}const Be=e=>{const t=new Set(e);return t.w=0,t.n=0,t},Ne=e=>(e.w&Ve)>0,qe=e=>(e.n&Ve)>0,je=new WeakMap;let ze=0,Ve=1;let Fe;const Ue=Symbol(""),We=Symbol("");class He{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,function(e,t=$e){t&&t.active&&t.effects.push(e)}(this,n)}run(){if(!this.active)return this.fn();let e=Fe,t=Xe;for(;e;){if(e===this)return;e=e.parent}try{return this.parent=Fe,Fe=this,Xe=!0,Ve=1<<++ze,ze<=30?(({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=Ve})(this):Ye(this),this.fn()}finally{ze<=30&&(e=>{const{deps:t}=e;if(t.length){let n=0;for(let o=0;o<t.length;o++){const r=t[o];Ne(r)&&!qe(r)?r.delete(e):t[n++]=r,r.w&=~Ve,r.n&=~Ve}t.length=n}})(this),Ve=1<<--ze,Fe=this.parent,Xe=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){Fe===this?this.deferStop=!0:this.active&&(Ye(this),this.onStop&&this.onStop(),this.active=!1)}}function Ye(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let Xe=!0;const Ge=[];function Je(){Ge.push(Xe),Xe=!1}function Qe(){const e=Ge.pop();Xe=void 0===e||e}function Ke(e,t,n){if(Xe&&Fe){let t=je.get(e);t||je.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=Be()),Ze(o)}}function Ze(e,t){let n=!1;ze<=30?qe(e)||(e.n|=Ve,n=!Ne(e)):n=!e.has(Fe),n&&(e.add(Fe),Fe.deps.push(e))}function et(e,t,n,o,r,i){const a=je.get(e);if(!a)return;let s=[];if("clear"===t)s=[...a.values()];else if("length"===n&&C(e)){const e=Number(o);a.forEach(((t,n)=>{("length"===n||n>=e)&&s.push(t)}))}else switch(void 0!==n&&s.push(a.get(n)),t){case"add":C(e)?N(n)&&s.push(a.get("length")):(s.push(a.get(Ue)),E(e)&&s.push(a.get(We)));break;case"delete":C(e)||(s.push(a.get(Ue)),E(e)&&s.push(a.get(We)));break;case"set":E(e)&&s.push(a.get(Ue))}if(1===s.length)s[0]&&tt(s[0]);else{const e=[];for(const t of s)t&&e.push(...t);tt(Be(e))}}function tt(e,t){const n=C(e)?e:[...e];for(const o of n)o.computed&&nt(o);for(const o of n)o.computed||nt(o)}function nt(e,t){(e!==Fe||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const ot=n("__proto__,__v_isRef,__isVue"),rt=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(O)),it=dt(),at=dt(!1,!0),st=dt(!0),lt=ct();function ct(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=Gt(this);for(let t=0,r=this.length;t<r;t++)Ke(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(Gt)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){Je();const n=Gt(this)[t].apply(this,e);return Qe(),n}})),e}function ut(e){const t=Gt(this);return Ke(t,0,e),t.hasOwnProperty(e)}function dt(e=!1,t=!1){return function(n,o,r){if("__v_isReactive"===o)return!e;if("__v_isReadonly"===o)return e;if("__v_isShallow"===o)return t;if("__v_raw"===o&&r===(e?t?jt:qt:t?Nt:Bt).get(n))return n;const i=C(n);if(!e){if(i&&k(lt,o))return Reflect.get(lt,o,r);if("hasOwnProperty"===o)return ut}const a=Reflect.get(n,o,r);return(O(o)?rt.has(o):ot(o))?a:(e||Ke(n,0,o),t?a:tn(a)?i&&N(o)?a:a.value:L(a)?e?Ft(a):Vt(a):a)}}function pt(e=!1){return function(t,n,o,r){let i=t[n];if(Ht(i)&&tn(i)&&!tn(o))return!1;if(!e&&(Yt(o)||Ht(o)||(i=Gt(i),o=Gt(o)),!C(t)&&tn(i)&&!tn(o)))return i.value=o,!0;const a=C(t)&&N(n)?Number(n)<t.length:k(t,n),s=Reflect.set(t,n,o,r);return t===Gt(r)&&(a?Y(o,i)&&et(t,"set",n,o):et(t,"add",n,o)),s}}const ft={get:it,set:pt(),deleteProperty:function(e,t){const n=k(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&et(e,"delete",t,void 0),o},has:function(e,t){const n=Reflect.has(e,t);return O(t)&&rt.has(t)||Ke(e,0,t),n},ownKeys:function(e){return Ke(e,0,C(e)?"length":Ue),Reflect.ownKeys(e)}},ht={get:st,set:(e,t)=>!0,deleteProperty:(e,t)=>!0},gt=x({},ft,{get:at,set:pt(!0)}),mt=e=>e,vt=e=>Reflect.getPrototypeOf(e);function yt(e,t,n=!1,o=!1){const r=Gt(e=e.__v_raw),i=Gt(t);n||(t!==i&&Ke(r,0,t),Ke(r,0,i));const{has:a}=vt(r),s=o?mt:n?Kt:Qt;return a.call(r,t)?s(e.get(t)):a.call(r,i)?s(e.get(i)):void(e!==r&&e.get(t))}function bt(e,t=!1){const n=this.__v_raw,o=Gt(n),r=Gt(e);return t||(e!==r&&Ke(o,0,e),Ke(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function _t(e,t=!1){return e=e.__v_raw,!t&&Ke(Gt(e),0,Ue),Reflect.get(e,"size",e)}function wt(e){e=Gt(e);const t=Gt(this);return vt(t).has.call(t,e)||(t.add(e),et(t,"add",e,e)),this}function xt(e,t){t=Gt(t);const n=Gt(this),{has:o,get:r}=vt(n);let i=o.call(n,e);i||(e=Gt(e),i=o.call(n,e));const a=r.call(n,e);return n.set(e,t),i?Y(t,a)&&et(n,"set",e,t):et(n,"add",e,t),this}function Tt(e){const t=Gt(this),{has:n,get:o}=vt(t);let r=n.call(t,e);r||(e=Gt(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&et(t,"delete",e,void 0),i}function St(){const e=Gt(this),t=0!==e.size,n=e.clear();return t&&et(e,"clear",void 0,void 0),n}function kt(e,t){return function(n,o){const r=this,i=r.__v_raw,a=Gt(i),s=t?mt:e?Kt:Qt;return!e&&Ke(a,0,Ue),i.forEach(((e,t)=>n.call(o,s(e),s(t),r)))}}function Ct(e,t,n){return function(...o){const r=this.__v_raw,i=Gt(r),a=E(i),s="entries"===e||e===Symbol.iterator&&a,l="keys"===e&&a,c=r[e](...o),u=n?mt:t?Kt:Qt;return!t&&Ke(i,0,l?We:Ue),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:s?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Et(e){return function(...t){return"delete"!==e&&this}}function At(){const e={get(e){return yt(this,e)},get size(){return _t(this)},has:bt,add:wt,set:xt,delete:Tt,clear:St,forEach:kt(!1,!1)},t={get(e){return yt(this,e,!1,!0)},get size(){return _t(this)},has:bt,add:wt,set:xt,delete:Tt,clear:St,forEach:kt(!1,!0)},n={get(e){return yt(this,e,!0)},get size(){return _t(this,!0)},has(e){return bt.call(this,e,!0)},add:Et("add"),set:Et("set"),delete:Et("delete"),clear:Et("clear"),forEach:kt(!0,!1)},o={get(e){return yt(this,e,!0,!0)},get size(){return _t(this,!0)},has(e){return bt.call(this,e,!0)},add:Et("add"),set:Et("set"),delete:Et("delete"),clear:Et("clear"),forEach:kt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=Ct(r,!1,!1),n[r]=Ct(r,!0,!1),t[r]=Ct(r,!1,!0),o[r]=Ct(r,!0,!0)})),[e,n,t,o]}const[Mt,Pt,It,Ot]=At();function Lt(e,t){const n=t?e?Ot:It:e?Pt:Mt;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(k(n,o)&&o in t?n:t,o,r)}const $t={get:Lt(!1,!1)},Dt={get:Lt(!1,!0)},Rt={get:Lt(!0,!1)},Bt=new WeakMap,Nt=new WeakMap,qt=new WeakMap,jt=new WeakMap;function zt(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>R(e).slice(8,-1))(e))}function Vt(e){return Ht(e)?e:Ut(e,!1,ft,$t,Bt)}function Ft(e){return Ut(e,!0,ht,Rt,qt)}function Ut(e,t,n,o,r){if(!L(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const a=zt(e);if(0===a)return e;const s=new Proxy(e,2===a?o:n);return r.set(e,s),s}function Wt(e){return Ht(e)?Wt(e.__v_raw):!(!e||!e.__v_isReactive)}function Ht(e){return!(!e||!e.__v_isReadonly)}function Yt(e){return!(!e||!e.__v_isShallow)}function Xt(e){return Wt(e)||Ht(e)}function Gt(e){const t=e&&e.__v_raw;return t?Gt(t):e}function Jt(e){return G(e,"__v_skip",!0),e}const Qt=e=>L(e)?Vt(e):e,Kt=e=>L(e)?Ft(e):e;function Zt(e){Xe&&Fe&&Ze((e=Gt(e)).dep||(e.dep=Be()))}function en(e,t){const n=(e=Gt(e)).dep;n&&tt(n)}function tn(e){return!(!e||!0!==e.__v_isRef)}function nn(e){return rn(e,!1)}function on(e){return rn(e,!0)}function rn(e,t){return tn(e)?e:new an(e,t)}class an{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Gt(e),this._value=t?e:Qt(e)}get value(){return Zt(this),this._value}set value(e){const t=this.__v_isShallow||Yt(e)||Ht(e);e=t?e:Gt(e),Y(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Qt(e),en(this))}}function sn(e){return tn(e)?e.value:e}const ln={get:(e,t,n)=>sn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return tn(r)&&!tn(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function cn(e){return Wt(e)?e:new Proxy(e,ln)}var un;class dn{constructor(e,t,n,o){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this[un]=!1,this._dirty=!0,this.effect=new He(e,(()=>{this._dirty||(this._dirty=!0,en(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=Gt(this);return Zt(e),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function pn(e,t,n,o){let r;try{r=o?e(...o):e()}catch(i){hn(i,t,n)}return r}function fn(e,t,n,o){if(P(e)){const r=pn(e,t,n,o);return r&&$(r)&&r.catch((e=>{hn(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(fn(e[i],t,n,o));return r}function hn(e,t,n,o=!0){t&&t.vnode;if(t){let o=t.parent;const r=t.proxy,i=n;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const a=t.appContext.config.errorHandler;if(a)return void pn(a,null,10,[e,r,i])}!function(e,t,n,o=!0){console.error(e)}(e,0,0,o)}un="__v_isReadonly";let gn=!1,mn=!1;const vn=[];let yn=0;const bn=[];let _n=null,wn=0;const xn=Promise.resolve();let Tn=null;function Sn(e){const t=Tn||xn;return e?t.then(this?e.bind(this):e):t}function kn(e){vn.length&&vn.includes(e,gn&&e.allowRecurse?yn+1:yn)||(null==e.id?vn.push(e):vn.splice(function(e){let t=yn+1,n=vn.length;for(;t<n;){const o=t+n>>>1;Mn(vn[o])<e?t=o+1:n=o}return t}(e.id),0,e),Cn())}function Cn(){gn||mn||(mn=!0,Tn=xn.then(In))}function En(e,t=(gn?yn+1:0)){for(;t<vn.length;t++){const e=vn[t];e&&e.pre&&(vn.splice(t,1),t--,e())}}function An(e){if(bn.length){const e=[...new Set(bn)];if(bn.length=0,_n)return void _n.push(...e);for(_n=e,_n.sort(((e,t)=>Mn(e)-Mn(t))),wn=0;wn<_n.length;wn++)_n[wn]();_n=null,wn=0}}const Mn=e=>null==e.id?1/0:e.id,Pn=(e,t)=>{const n=Mn(e)-Mn(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function In(e){mn=!1,gn=!0,vn.sort(Pn);try{for(yn=0;yn<vn.length;yn++){const e=vn[yn];e&&!1!==e.active&&pn(e,null,14)}}finally{yn=0,vn.length=0,An(),gn=!1,Tn=null,(vn.length||bn.length)&&In()}}function On(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||g;let r=n;const i=t.startsWith("update:"),a=i&&t.slice(7);if(a&&a in o){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:t,trim:i}=o[e]||g;i&&(r=n.map((e=>I(e)?e.trim():e))),t&&(r=n.map(J))}let s,l=o[s=H(t)]||o[s=H(V(t))];!l&&i&&(l=o[s=H(U(t))]),l&&fn(l,e,6,Ln(e,l,r));const c=o[s+"Once"];if(c){if(e.emitted){if(e.emitted[s])return}else e.emitted={};e.emitted[s]=!0,fn(c,e,6,Ln(e,c,r))}}function Ln(e,t,n){if(1!==n.length)return n;if(P(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&k(o,"type")&&k(o,"timeStamp")&&k(o,"target")&&k(o,"currentTarget")&&k(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function $n(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let a={},s=!1;if(!P(e)){const o=e=>{const n=$n(e,t,!0);n&&(s=!0,x(a,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||s?(C(i)?i.forEach((e=>a[e]=null)):x(a,i),L(e)&&o.set(e,a),a):(L(e)&&o.set(e,null),null)}function Dn(e,t){return!(!e||!_(t))&&(t=t.slice(2).replace(/Once$/,""),k(e,t[0].toLowerCase()+t.slice(1))||k(e,U(t))||k(e,t))}let Rn=null,Bn=null;function Nn(e){const t=Rn;return Rn=e,Bn=e&&e.type.__scopeId||null,t}function qn(e,t=Rn,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&jr(-1);const r=Nn(t);let i;try{i=e(...n)}finally{Nn(r),o._d&&jr(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function jn(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[a],slots:s,attrs:l,emit:c,render:u,renderCache:d,data:p,setupState:f,ctx:h,inheritAttrs:g}=e;let m,v;const y=Nn(e);try{if(4&n.shapeFlag){const e=r||o;m=ei(u.call(e,e,d,i,f,p,h)),v=l}else{const e=t;0,m=ei(e.length>1?e(i,{attrs:l,slots:s,emit:c}):e(i,null)),v=t.props?l:zn(l)}}catch(_){Rr.length=0,hn(_,e,1),m=Jr($r)}let b=m;if(v&&!1!==g){const e=Object.keys(v),{shapeFlag:t}=b;e.length&&7&t&&(a&&e.some(w)&&(v=Vn(v,a)),b=Qr(b,v))}return n.dirs&&(b=Qr(b),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&(b.transition=n.transition),m=b,Nn(y),m}const zn=e=>{let t;for(const n in e)("class"===n||"style"===n||_(n))&&((t||(t={}))[n]=e[n]);return t},Vn=(e,t)=>{const n={};for(const o in e)w(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function Fn(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!Dn(n,i))return!0}return!1}const Un=e=>e.__isSuspense;function Wn(e,t){if(si){let n=si.provides;const o=si.parent&&si.parent.provides;o===n&&(n=si.provides=Object.create(o)),n[e]=t,"app"===si.type.mpType&&si.appContext.app.provide(e,t)}else;}function Hn(e,t,n=!1){const o=si||Rn;if(o){const r=null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&P(t)?t.call(o.proxy):t}}function Yn(e,t){return Jn(e,null,t)}const Xn={};function Gn(e,t,n){return Jn(e,t,n)}function Jn(e,t,{immediate:n,deep:o,flush:r,onTrack:i,onTrigger:a}=g){const s=$e===(null==si?void 0:si.scope)?si:null;let l,c,u=!1,d=!1;if(tn(e)?(l=()=>e.value,u=Yt(e)):Wt(e)?(l=()=>e,o=!0):C(e)?(d=!0,u=e.some((e=>Wt(e)||Yt(e))),l=()=>e.map((e=>tn(e)?e.value:Wt(e)?Zn(e):P(e)?pn(e,s,2):void 0))):l=P(e)?t?()=>pn(e,s,2):()=>{if(!s||!s.isUnmounted)return c&&c(),fn(e,s,3,[f])}:v,t&&o){const e=l;l=()=>Zn(e())}let p,f=e=>{c=b.onStop=()=>{pn(e,s,4)}};if(pi){if(f=v,t?n&&fn(t,s,3,[l(),d?[]:void 0,f]):l(),"sync"!==r)return v;{const e=_i();p=e.__watcherHandles||(e.__watcherHandles=[])}}let h=d?new Array(e.length).fill(Xn):Xn;const m=()=>{if(b.active)if(t){const e=b.run();(o||u||(d?e.some(((e,t)=>Y(e,h[t]))):Y(e,h)))&&(c&&c(),fn(t,s,3,[e,h===Xn?void 0:d&&h[0]===Xn?[]:h,f]),h=e)}else b.run()};let y;m.allowRecurse=!!t,"sync"===r?y=m:"post"===r?y=()=>Ar(m,s&&s.suspense):(m.pre=!0,s&&(m.id=s.uid),y=()=>kn(m));const b=new He(l,y);t?n?m():h=b.run():"post"===r?Ar(b.run.bind(b),s&&s.suspense):b.run();const _=()=>{b.stop(),s&&s.scope&&T(s.scope.effects,b)};return p&&p.push(_),_}function Qn(e,t,n){const o=this.proxy,r=I(e)?e.includes(".")?Kn(o,e):()=>o[e]:e.bind(o,o);let i;P(t)?i=t:(i=t.handler,n=t);const a=si;ci(this);const s=Jn(r,i.bind(o),n);return a?ci(a):ui(),s}function Kn(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function Zn(e,t){if(!L(e)||e.__v_skip)return e;if((t=t||new Set).has(e))return e;if(t.add(e),tn(e))Zn(e.value,t);else if(C(e))for(let n=0;n<e.length;n++)Zn(e[n],t);else if(A(e)||E(e))e.forEach((e=>{Zn(e,t)}));else if(B(e))for(const n in e)Zn(e[n],t);return e}const eo=[Function,Array],to={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:eo,onEnter:eo,onAfterEnter:eo,onEnterCancelled:eo,onBeforeLeave:eo,onLeave:eo,onAfterLeave:eo,onLeaveCancelled:eo,onBeforeAppear:eo,onAppear:eo,onAfterAppear:eo,onAppearCancelled:eo},no={name:"BaseTransition",props:to,setup(e,{slots:t}){const n=li(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Io((()=>{e.isMounted=!0})),$o((()=>{e.isUnmounting=!0})),e}();let r;return()=>{const i=t.default&&lo(t.default(),!0);if(!i||!i.length)return;let a=i[0];if(i.length>1)for(const e of i)if(e.type!==$r){a=e;break}const s=Gt(e),{mode:l}=s;if(o.isLeaving)return io(a);const c=ao(a);if(!c)return io(a);const u=ro(c,s,o,n);so(c,u);const d=n.subTree,p=d&&ao(d);let f=!1;const{getTransitionKey:h}=c.type;if(h){const e=h();void 0===r?r=e:e!==r&&(r=e,f=!0)}if(p&&p.type!==$r&&(!Wr(c,p)||f)){const e=ro(p,s,o,n);if(so(p,e),"out-in"===l)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&n.update()},io(a);"in-out"===l&&c.type!==$r&&(e.delayLeave=(e,t,n)=>{oo(o,p)[String(p.key)]=p,e._leaveCb=()=>{t(),e._leaveCb=void 0,delete u.delayedLeave},u.delayedLeave=n})}return a}}};function oo(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function ro(e,t,n,o){const{appear:r,mode:i,persisted:a=!1,onBeforeEnter:s,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:p,onAfterLeave:f,onLeaveCancelled:h,onBeforeAppear:g,onAppear:m,onAfterAppear:v,onAppearCancelled:y}=t,b=String(e.key),_=oo(n,e),w=(e,t)=>{e&&fn(e,o,9,t)},x=(e,t)=>{const n=t[1];w(e,t),C(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},T={mode:i,persisted:a,beforeEnter(t){let o=s;if(!n.isMounted){if(!r)return;o=g||s}t._leaveCb&&t._leaveCb(!0);const i=_[b];i&&Wr(e,i)&&i.el._leaveCb&&i.el._leaveCb(),w(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=m||l,o=v||c,i=y||u}let a=!1;const s=e._enterCb=t=>{a||(a=!0,w(t?i:o,[e]),T.delayedLeave&&T.delayedLeave(),e._enterCb=void 0)};t?x(t,[e,s]):s()},leave(t,o){const r=String(e.key);if(t._enterCb&&t._enterCb(!0),n.isUnmounting)return o();w(d,[t]);let i=!1;const a=t._leaveCb=n=>{i||(i=!0,o(),w(n?h:f,[t]),t._leaveCb=void 0,_[r]===e&&delete _[r])};_[r]=e,p?x(p,[t,a]):a()},clone:e=>ro(e,t,n,o)};return T}function io(e){if(ho(e))return(e=Qr(e)).children=null,e}function ao(e){return ho(e)?e.children?e.children[0]:void 0:e}function so(e,t){6&e.shapeFlag&&e.component?so(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function lo(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let a=e[i];const s=null==n?a.key:String(n)+String(null!=a.key?a.key:i);a.type===Or?(128&a.patchFlag&&r++,o=o.concat(lo(a.children,t,s))):(t||a.type!==$r)&&o.push(null!=s?Qr(a,{key:s}):a)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}function co(e){return P(e)?{setup:e,name:e.name}:e}const uo=e=>!!e.type.__asyncLoader;function po(e){P(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:a=!0,onError:s}=e;let l,c=null,u=0;const d=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),s)return new Promise(((t,n)=>{s(e,(()=>t((u++,c=null,d()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return co({name:"AsyncComponentWrapper",__asyncLoader:d,get __asyncResolved(){return l},setup(){const e=si;if(l)return()=>fo(l,e);const t=t=>{c=null,hn(t,e,13,!o)};if(a&&e.suspense||pi)return d().then((t=>()=>fo(t,e))).catch((e=>(t(e),()=>o?Jr(o,{error:e}):null)));const s=nn(!1),u=nn(),p=nn(!!r);return r&&setTimeout((()=>{p.value=!1}),r),null!=i&&setTimeout((()=>{if(!s.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),d().then((()=>{s.value=!0,e.parent&&ho(e.parent.vnode)&&kn(e.parent.update)})).catch((e=>{t(e),u.value=e})),()=>s.value&&l?fo(l,e):u.value&&o?Jr(o,{error:u.value}):n&&!p.value?Jr(n):void 0}})}function fo(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,a=Jr(e,o,r);return a.ref=n,a.ce=i,delete t.vnode.ce,a}const ho=e=>e.type.__isKeepAlive;class go{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,r=t.get(e);if(r)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return r}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const mo={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=li(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=e.cache||new go(e.max);r.pruneCacheEntry=a;let i=null;function a(t){var o;!i||!Wr(t,i)||"key"===e.matchBy&&t.key!==i.key?(To(o=t),u(o,n,s,!0)):i&&To(i)}const s=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:d}}}=o,p=d("div");function f(t){r.forEach(((n,o)=>{const i=ko(n,e.matchBy);!i||t&&t(i)||(r.delete(o),a(n))}))}o.activate=(e,t,n,o,r)=>{const i=e.component;if(i.ba){const e=i.isDeactivated;i.isDeactivated=!1,X(i.ba),i.isDeactivated=e}c(e,t,n,0,s),l(i.vnode,e,t,n,i,s,o,e.slotScopeIds,r),Ar((()=>{i.isDeactivated=!1,i.a&&X(i.a);const t=e.props&&e.props.onVnodeMounted;t&&ri(t,i.parent,e)}),s)},o.deactivate=e=>{const t=e.component;t.bda&&Co(t.bda),c(e,p,null,1,s),Ar((()=>{t.bda&&Eo(t.bda),t.da&&X(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&ri(n,t.parent,e),t.isDeactivated=!0}),s)},Gn((()=>[e.include,e.exclude,e.matchBy]),(([e,t])=>{e&&f((t=>yo(e,t))),t&&f((e=>!yo(t,e)))}),{flush:"post",deep:!0});let h=null;const g=()=>{null!=h&&r.set(h,So(n.subTree))};return Io(g),Lo(g),$o((()=>{r.forEach(((t,o)=>{r.delete(o),a(t);const{subTree:i,suspense:s}=n,l=So(i);if(t.type!==l.type||"key"===e.matchBy&&t.key!==l.key);else{l.component.bda&&X(l.component.bda),To(l);const e=l.component.da;e&&Ar(e,s)}}))})),()=>{if(h=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return i=null,n;if(!Ur(o)||!(4&o.shapeFlag)&&!Un(o.type))return i=null,o;let a=So(o);const s=a.type,l=ko(a,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!yo(c,l))||u&&l&&yo(u,l))return i=a,o;const d=null==a.key?s:a.key,p=r.get(d);return a.el&&(a=Qr(a),Un(o.type)&&(o.ssContent=a)),h=d,p&&(a.el=p.el,a.component=p.component,a.transition&&so(a,a.transition),a.shapeFlag|=512),a.shapeFlag|=256,i=a,Un(o.type)?o:a}}},vo=mo;function yo(e,t){return C(e)?e.some((e=>yo(e,t))):I(e)?e.split(",").includes(t):!!e.test&&e.test(t)}function bo(e,t){wo(e,"a",t)}function _o(e,t){wo(e,"da",t)}function wo(e,t,n=si){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,Ao(t,o,n),n){let e=n.parent;for(;e&&e.parent;)ho(e.parent.vnode)&&xo(o,t,n,e),e=e.parent}}function xo(e,t,n,o){const r=Ao(t,e,o,!0);Do((()=>{T(o[t],r)}),n)}function To(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function So(e){return Un(e.type)?e.ssContent:e}function ko(e,t){if("name"===t){const t=e.type;return mi(uo(e)?t.__asyncResolved||{}:t)}return String(e.key)}function Co(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function Eo(e){e.forEach((e=>e.__called=!1))}function Ao(e,t,n=si,o=!1){if(n){if(r=e,Se.indexOf(r)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return ke.indexOf(e)>-1}(e))){const o=n.proxy;fn(t.bind(o),n,e,"onLoad"===e?[o.$page.options]:[])}}const i=n[e]||(n[e]=[]),a=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;Je(),ci(n);const r=fn(t,n,e,o);return ui(),Qe(),r});return o?i.unshift(a):i.push(a),a}var r}const Mo=e=>(t,n=si)=>(!pi||"sp"===e)&&Ao(e,((...e)=>t(...e)),n),Po=Mo("bm"),Io=Mo("m"),Oo=Mo("bu"),Lo=Mo("u"),$o=Mo("bum"),Do=Mo("um"),Ro=Mo("sp"),Bo=Mo("rtg"),No=Mo("rtc");function qo(e,t=si){Ao("ec",e,t)}function jo(e,t){const n=Rn;if(null===n)return e;const o=gi(n)||n.proxy,r=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[e,n,a,s=g]=t[i];e&&(P(e)&&(e={mounted:e,updated:e}),e.deep&&Zn(n),r.push({dir:e,instance:o,value:n,oldValue:void 0,arg:a,modifiers:s}))}return e}function zo(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let a=0;a<r.length;a++){const s=r[a];i&&(s.oldValue=i[a].value);let l=s.dir[o];l&&(Je(),fn(l,n,8,[e.el,s,e,t]),Qe())}}function Vo(e,t){return Wo("components",e,!0,t)||e}const Fo=Symbol();function Uo(e){return I(e)?Wo("components",e,!1)||e:e||Fo}function Wo(e,t,n=!0,o=!1){const r=Rn||si;if(r){const n=r.type;if("components"===e){const e=mi(n,!1);if(e&&(e===t||e===V(t)||e===W(V(t))))return n}const i=Ho(r[e]||n[e],t)||Ho(r.appContext[e],t);return!i&&o?n:i}}function Ho(e,t){return e&&(e[t]||e[V(t)]||e[W(V(t))])}function Yo(e,t,n,o){let r;const i=n&&n[o];if(C(e)||I(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i&&i[n])}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i&&i[n])}else if(L(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,a=n.length;o<a;o++){const a=n[o];r[o]=t(e[a],a,o,i&&i[o])}}else r=[];return n&&(n[o]=r),r}function Xo(e,t,n={},o,r){if(Rn.isCE||Rn.parent&&uo(Rn.parent)&&Rn.parent.isCE)return"default"!==t&&(n.name=t),Jr("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),Nr();const a=i&&Go(i(n)),s=Fr(Or,{key:n.key||a&&a.key||`_${t}`},a||(o?o():[]),a&&1===e._?64:-2);return!r&&s.scopeId&&(s.slotScopeIds=[s.scopeId+"-s"]),i&&i._c&&(i._d=!0),s}function Go(e){return e.some((e=>!Ur(e)||e.type!==$r&&!(e.type===Or&&!Go(e.children))))?e:null}const Jo=e=>e?di(e)?gi(e)||e.proxy:Jo(e.parent):null,Qo=x(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Jo(e.parent),$root:e=>Jo(e.root),$emit:e=>e.emit,$options:e=>rr(e),$forceUpdate:e=>e.f||(e.f=()=>kn(e.update)),$nextTick:e=>e.n||(e.n=Sn.bind(e.proxy)),$watch:e=>Qn.bind(e)}),Ko=(e,t)=>e!==g&&!e.__isScriptSetup&&k(e,t),Zo={get({_:e},t){const{ctx:n,setupState:o,data:r,props:i,accessCache:a,type:s,appContext:l}=e;let c;if("$"!==t[0]){const s=a[t];if(void 0!==s)switch(s){case 1:return o[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Ko(o,t))return a[t]=1,o[t];if(r!==g&&k(r,t))return a[t]=2,r[t];if((c=e.propsOptions[0])&&k(c,t))return a[t]=3,i[t];if(n!==g&&k(n,t))return a[t]=4,n[t];er&&(a[t]=0)}}const u=Qo[t];let d,p;return u?("$attrs"===t&&Ke(e,0,t),u(e)):(d=s.__cssModules)&&(d=d[t])?d:n!==g&&k(n,t)?(a[t]=4,n[t]):(p=l.config.globalProperties,k(p,t)?p[t]:void 0)},set({_:e},t,n){const{data:o,setupState:r,ctx:i}=e;return Ko(r,t)?(r[t]=n,!0):o!==g&&k(o,t)?(o[t]=n,!0):!k(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(i[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:i}},a){let s;return!!n[a]||e!==g&&k(e,a)||Ko(t,a)||(s=i[0])&&k(s,a)||k(o,a)||k(Qo,a)||k(r.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:k(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};let er=!0;function tr(e){const t=rr(e),n=e.proxy,o=e.ctx;er=!1,t.beforeCreate&&nr(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:a,watch:s,provide:l,inject:c,created:u,beforeMount:d,mounted:p,beforeUpdate:f,updated:h,activated:g,deactivated:m,beforeDestroy:y,beforeUnmount:b,destroyed:_,unmounted:w,render:x,renderTracked:T,renderTriggered:S,errorCaptured:k,serverPrefetch:E,expose:A,inheritAttrs:M,components:I,directives:O,filters:$}=t;if(c&&function(e,t,n=v,o=!1){C(e)&&(e=lr(e));for(const r in e){const n=e[r];let i;i=L(n)?"default"in n?Hn(n.from||r,n.default,!0):Hn(n.from||r):Hn(n),tn(i)&&o?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e}):t[r]=i}}(c,o,null,e.appContext.config.unwrapInjectedRef),a)for(const v in a){const e=a[v];P(e)&&(o[v]=e.bind(n))}if(r){const t=r.call(n,n);L(t)&&(e.data=Vt(t))}if(er=!0,i)for(const C in i){const e=i[C],t=P(e)?e.bind(n,n):P(e.get)?e.get.bind(n,n):v,r=!P(e)&&P(e.set)?e.set.bind(n):v,a=vi({get:t,set:r});Object.defineProperty(o,C,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(s)for(const v in s)or(s[v],o,n,v);if(l){const e=P(l)?l.call(n):l;Reflect.ownKeys(e).forEach((t=>{Wn(t,e[t])}))}function D(e,t){C(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(u&&nr(u,e,"c"),D(Po,d),D(Io,p),D(Oo,f),D(Lo,h),D(bo,g),D(_o,m),D(qo,k),D(No,T),D(Bo,S),D($o,b),D(Do,w),D(Ro,E),C(A))if(A.length){const t=e.exposed||(e.exposed={});A.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});x&&e.render===v&&(e.render=x),null!=M&&(e.inheritAttrs=M),I&&(e.components=I),O&&(e.directives=O);const R=e.appContext.config.globalProperties.$applyOptions;R&&R(t,e,n)}function nr(e,t,n){fn(C(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function or(e,t,n,o){const r=o.includes(".")?Kn(n,o):()=>n[o];if(I(e)){const n=t[e];P(n)&&Gn(r,n)}else if(P(e))Gn(r,e.bind(n));else if(L(e))if(C(e))e.forEach((e=>or(e,t,n,o)));else{const o=P(e.handler)?e.handler.bind(n):t[e.handler];P(o)&&Gn(r,o,e)}}function rr(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,s=i.get(t);let l;return s?l=s:r.length||n||o?(l={},r.length&&r.forEach((e=>ir(l,e,a,!0))),ir(l,t,a)):l=t,L(t)&&i.set(t,l),l}function ir(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&ir(e,i,n,!0),r&&r.forEach((t=>ir(e,t,n,!0)));for(const a in t)if(o&&"expose"===a);else{const o=ar[a]||n&&n[a];e[a]=o?o(e[a],t[a]):t[a]}return e}const ar={data:sr,props:ur,emits:ur,methods:ur,computed:ur,beforeCreate:cr,created:cr,beforeMount:cr,mounted:cr,beforeUpdate:cr,updated:cr,beforeDestroy:cr,beforeUnmount:cr,destroyed:cr,unmounted:cr,activated:cr,deactivated:cr,errorCaptured:cr,serverPrefetch:cr,components:ur,directives:ur,watch:function(e,t){if(!e)return t;if(!t)return e;const n=x(Object.create(null),e);for(const o in t)n[o]=cr(e[o],t[o]);return n},provide:sr,inject:function(e,t){return ur(lr(e),lr(t))}};function sr(e,t){return t?e?function(){return x(P(e)?e.call(this,this):e,P(t)?t.call(this,this):t)}:t:e}function lr(e){if(C(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function cr(e,t){return e?[...new Set([].concat(e,t))]:t}function ur(e,t){return e?x(x(Object.create(null),e),t):t}function dr(e,t,n,o=!1){const r={},i={};G(i,Hr,1),e.propsDefaults=Object.create(null),pr(e,t,r,i);for(const a in e.propsOptions[0])a in r||(r[a]=void 0);n?e.props=o?r:Ut(r,!1,gt,Dt,Nt):e.type.props?e.props=r:e.props=i,e.attrs=i}function pr(e,t,n,o){const[r,i]=e.propsOptions;let a,s=!1;if(t)for(let l in t){if(q(l))continue;const c=t[l];let u;r&&k(r,u=V(l))?i&&i.includes(u)?(a||(a={}))[u]=c:n[u]=c:Dn(e.emitsOptions,l)||l in o&&c===o[l]||(o[l]=c,s=!0)}if(i){const t=Gt(n),o=a||g;for(let a=0;a<i.length;a++){const s=i[a];n[s]=fr(r,t,s,o[s],e,!k(o,s))}}return s}function fr(e,t,n,o,r,i){const a=e[n];if(null!=a){const e=k(a,"default");if(e&&void 0===o){const e=a.default;if(a.type!==Function&&P(e)){const{propsDefaults:i}=r;n in i?o=i[n]:(ci(r),o=i[n]=e.call(null,t),ui())}else o=e}a[0]&&(i&&!e?o=!1:!a[1]||""!==o&&o!==U(n)||(o=!0))}return o}function hr(e,t,n=!1){const o=t.propsCache,r=o.get(e);if(r)return r;const i=e.props,a={},s=[];let l=!1;if(!P(e)){const o=e=>{l=!0;const[n,o]=hr(e,t,!0);x(a,n),o&&s.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!i&&!l)return L(e)&&o.set(e,m),m;if(C(i))for(let u=0;u<i.length;u++){const e=V(i[u]);gr(e)&&(a[e]=g)}else if(i)for(const u in i){const e=V(u);if(gr(e)){const t=i[u],n=a[e]=C(t)||P(t)?{type:t}:Object.assign({},t);if(n){const t=yr(Boolean,n.type),o=yr(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||k(n,"default"))&&s.push(e)}}}const c=[a,s];return L(e)&&o.set(e,c),c}function gr(e){return"$"!==e[0]}function mr(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function vr(e,t){return mr(e)===mr(t)}function yr(e,t){return C(t)?t.findIndex((t=>vr(t,e))):P(t)&&vr(t,e)?0:-1}const br=e=>"_"===e[0]||"$stable"===e,_r=e=>C(e)?e.map(ei):[ei(e)],wr=(e,t,n)=>{if(t._n)return t;const o=qn(((...e)=>_r(t(...e))),n);return o._c=!1,o},xr=(e,t,n)=>{const o=e._ctx;for(const r in e){if(br(r))continue;const n=e[r];if(P(n))t[r]=wr(0,n,o);else if(null!=n){const e=_r(n);t[r]=()=>e}}},Tr=(e,t)=>{const n=_r(t);e.slots.default=()=>n};function Sr(){return{app:null,config:{isNativeTag:y,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let kr=0;function Cr(e,t){return function(n,o=null){P(n)||(n=Object.assign({},n)),null==o||L(o)||(o=null);const r=Sr(),i=new Set;let a=!1;const s=r.app={_uid:kr++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:wi,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&P(e.install)?(i.add(e),e.install(s,...t)):P(e)&&(i.add(e),e(s,...t))),s),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),s),component:(e,t)=>t?(r.components[e]=t,s):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,s):r.directives[e],mount(i,l,c){if(!a){const u=Jr(n,o);return u.appContext=r,l&&t?t(u,i):e(u,i,c),a=!0,s._container=i,i.__vue_app__=s,s._instance=u.component,gi(u.component)||u.component.proxy}},unmount(){a&&(e(null,s._container),delete s._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,s)};return s}}function Er(e,t,n,o,r=!1){if(C(e))return void e.forEach(((e,i)=>Er(e,t&&(C(t)?t[i]:t),n,o,r)));if(uo(o)&&!r)return;const i=4&o.shapeFlag?gi(o.component)||o.component.proxy:o.el,a=r?null:i,{i:s,r:l}=e,c=t&&t.r,u=s.refs===g?s.refs={}:s.refs,d=s.setupState;if(null!=c&&c!==l&&(I(c)?(u[c]=null,k(d,c)&&(d[c]=null)):tn(c)&&(c.value=null)),P(l))pn(l,s,12,[a,u]);else{const t=I(l),o=tn(l);if(t||o){const s=()=>{if(e.f){const n=t?k(d,l)?d[l]:u[l]:l.value;r?C(n)&&T(n,i):C(n)?n.includes(i)||n.push(i):t?(u[l]=[i],k(d,l)&&(d[l]=u[l])):(l.value=[i],e.k&&(u[e.k]=l.value))}else t?(u[l]=a,k(d,l)&&(d[l]=a)):o&&(l.value=a,e.k&&(u[e.k]=a))};a?(s.id=-1,Ar(s,n)):s()}}}const Ar=function(e,t){var n;t&&t.pendingBranch?C(e)?t.effects.push(...e):t.effects.push(e):(C(n=e)?bn.push(...n):_n&&_n.includes(n,n.allowRecurse?wn+1:wn)||bn.push(n),Cn())};function Mr(e){return function(e,t){(Q||(Q="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})).__VUE__=!0;const{insert:n,remove:o,patchProp:r,forcePatchProp:i,createElement:a,createText:s,createComment:l,setText:c,setElementText:u,parentNode:d,nextSibling:p,setScopeId:f=v,insertStaticContent:h}=e,y=(e,t,n,o=null,r=null,i=null,a=!1,s=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!Wr(e,t)&&(o=te(e),Y(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case Lr:b(e,t,n,o);break;case $r:_(e,t,n,o);break;case Dr:null==e&&w(t,n,o,a);break;case Or:L(e,t,n,o,r,i,a,s,l);break;default:1&d?C(e,t,n,o,r,i,a,s,l):6&d?D(e,t,n,o,r,i,a,s,l):(64&d||128&d)&&c.process(e,t,n,o,r,i,a,s,l,oe)}null!=u&&r&&Er(u,e&&e.ref,i,t||e,!t)},b=(e,t,o,r)=>{if(null==e)n(t.el=s(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&c(n,t.children)}},_=(e,t,o,r)=>{null==e?n(t.el=l(t.children||""),o,r):t.el=e.el},w=(e,t,n,o)=>{[e.el,e.anchor]=h(e.children,t,n,o,e.el,e.anchor)},T=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=p(e),n(e,o,r),e=i;n(t,o,r)},S=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=p(e),o(e),e=n;o(t)},C=(e,t,n,o,r,i,a,s,l)=>{a=a||"svg"===t.type,null==e?E(t,n,o,r,i,a,s,l):P(e,t,r,i,a,s,l)},E=(e,t,o,i,s,l,c,d)=>{let p,f;const{type:h,props:g,shapeFlag:m,transition:v,dirs:y}=e;if(p=e.el=a(e.type,l,g&&g.is,g),8&m?u(p,e.children):16&m&&M(e.children,p,null,i,s,l&&"foreignObject"!==h,c,d),y&&zo(e,null,i,"created"),A(p,e,e.scopeId,c,i),g){for(const t in g)"value"===t||q(t)||r(p,t,null,g[t],l,e.children,i,s,ee);"value"in g&&r(p,"value",null,g.value),(f=g.onVnodeBeforeMount)&&ri(f,i,e)}Object.defineProperty(p,"__vueParentComponent",{value:i,enumerable:!1}),y&&zo(e,null,i,"beforeMount");const b=(!s||s&&!s.pendingBranch)&&v&&!v.persisted;b&&v.beforeEnter(p),n(p,t,o),((f=g&&g.onVnodeMounted)||b||y)&&Ar((()=>{f&&ri(f,i,e),b&&v.enter(p),y&&zo(e,null,i,"mounted")}),s)},A=(e,t,n,o,r)=>{if(n&&f(e,n),o)for(let i=0;i<o.length;i++)f(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;A(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},M=(e,t,n,o,r,i,a,s,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=s?ti(e[c]):ei(e[c]);y(null,l,t,n,o,r,i,a,s)}},P=(e,t,n,o,a,s,l)=>{const c=t.el=e.el;let{patchFlag:d,dynamicChildren:p,dirs:f}=t;d|=16&e.patchFlag;const h=e.props||g,m=t.props||g;let v;n&&Pr(n,!1),(v=m.onVnodeBeforeUpdate)&&ri(v,n,t,e),f&&zo(t,e,n,"beforeUpdate"),n&&Pr(n,!0);const y=a&&"foreignObject"!==t.type;if(p?I(e.dynamicChildren,p,c,n,o,y,s):l||z(e,t,c,null,n,o,y,s,!1),d>0){if(16&d)O(c,t,h,m,n,o,a);else if(2&d&&h.class!==m.class&&r(c,"class",null,m.class,a),4&d&&r(c,"style",h.style,m.style,a),8&d){const s=t.dynamicProps;for(let t=0;t<s.length;t++){const l=s[t],u=h[l],d=m[l];(d!==u||"value"===l||i&&i(c,l))&&r(c,l,u,d,a,e.children,n,o,ee)}}1&d&&e.children!==t.children&&u(c,t.children)}else l||null!=p||O(c,t,h,m,n,o,a);((v=m.onVnodeUpdated)||f)&&Ar((()=>{v&&ri(v,n,t,e),f&&zo(t,e,n,"updated")}),o)},I=(e,t,n,o,r,i,a)=>{for(let s=0;s<t.length;s++){const l=e[s],c=t[s],u=l.el&&(l.type===Or||!Wr(l,c)||70&l.shapeFlag)?d(l.el):n;y(l,c,u,null,o,r,i,a,!0)}},O=(e,t,n,o,a,s,l)=>{if(n!==o){if(n!==g)for(const i in n)q(i)||i in o||r(e,i,n[i],null,l,t.children,a,s,ee);for(const c in o){if(q(c))continue;const u=o[c],d=n[c];(u!==d&&"value"!==c||i&&i(e,c))&&r(e,c,d,u,l,t.children,a,s,ee)}"value"in o&&r(e,"value",n.value,o.value)}},L=(e,t,o,r,i,a,l,c,u)=>{const d=t.el=e?e.el:s(""),p=t.anchor=e?e.anchor:s("");let{patchFlag:f,dynamicChildren:h,slotScopeIds:g}=t;g&&(c=c?c.concat(g):g),null==e?(n(d,o,r),n(p,o,r),M(t.children,o,p,i,a,l,c,u)):f>0&&64&f&&h&&e.dynamicChildren?(I(e.dynamicChildren,h,o,i,a,l,c),(null!=t.key||i&&t===i.subTree)&&Ir(e,t,!0)):z(e,t,o,p,i,a,l,c,u)},D=(e,t,n,o,r,i,a,s,l)=>{t.slotScopeIds=s,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,a,l):R(t,n,o,r,i,a,l):B(e,t,l)},R=(e,t,n,o,r,i,a)=>{const s=e.component=function(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||ii,i={uid:ai++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new De(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:hr(o,r),emitsOptions:$n(o,r),emit:null,emitted:null,propsDefaults:g,inheritAttrs:o.inheritAttrs,ctx:g,data:g,props:g,attrs:g,slots:g,refs:g,setupState:g,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};i.ctx={_:i},i.root=t?t.root:i,i.emit=On.bind(null,i),i.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(i);return i}(e,o,r);if(ho(e)&&(s.ctx.renderer=oe),function(e,t=!1){pi=t;const{props:n,children:o}=e.vnode,r=di(e);dr(e,n,r,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=Gt(t),G(t,"_",n)):xr(t,e.slots={})}else e.slots={},t&&Tr(e,t);G(e.slots,Hr,1)})(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Jt(new Proxy(e.ctx,Zo));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};let n;return{get attrs(){return n||(n=function(e){return new Proxy(e.attrs,{get:(t,n)=>(Ke(e,0,"$attrs"),t[n])})}(e))},slots:e.slots,emit:e.emit,expose:t}}(e):null;ci(e),Je();const r=pn(o,e,0,[e.props,n]);if(Qe(),ui(),$(r)){if(r.then(ui,ui),t)return r.then((n=>{fi(e,n,t)})).catch((t=>{hn(t,e,0)}));e.asyncDep=r}else fi(e,r,t)}else hi(e,t)}(e,t):void 0;pi=!1}(s),s.asyncDep){if(r&&r.registerDep(s,N),!e.el){const e=s.subTree=Jr($r);_(null,e,t,n)}}else N(s,e,t,n,r,i,a)},B=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:a,children:s,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!s||s&&s.$stable)||o!==a&&(o?!a||Fn(o,a,c):!!a);if(1024&l)return!0;if(16&l)return o?Fn(o,a,c):!!a;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(a[n]!==o[n]&&!Dn(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void j(o,t,n);o.next=t,function(e){const t=vn.indexOf(e);t>yn&&vn.splice(t,1)}(o.update),o.update()}else t.el=e.el,o.vnode=t},N=(e,t,n,o,r,i,a)=>{const s=()=>{if(e.isMounted){let t,{next:n,bu:o,u:s,parent:l,vnode:c}=e,u=n;Pr(e,!1),n?(n.el=c.el,j(e,n,a)):n=c,o&&X(o),(t=n.props&&n.props.onVnodeBeforeUpdate)&&ri(t,l,n,c),Pr(e,!0);const p=jn(e),f=e.subTree;e.subTree=p,y(f,p,d(f.el),te(f),e,r,i),n.el=p.el,null===u&&function({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}(e,p.el),s&&Ar(s,r),(t=n.props&&n.props.onVnodeUpdated)&&Ar((()=>ri(t,l,n,c)),r)}else{let a;const{el:s,props:l}=t,{bm:c,m:u,parent:d}=e,p=uo(t);if(Pr(e,!1),c&&X(c),!p&&(a=l&&l.onVnodeBeforeMount)&&ri(a,d,t),Pr(e,!0),s&&ie){const n=()=>{e.subTree=jn(e),ie(s,e.subTree,e,r,null)};p?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const a=e.subTree=jn(e);y(null,a,n,o,e,r,i),t.el=a.el}if(u&&Ar(u,r),!p&&(a=l&&l.onVnodeMounted)){const e=t;Ar((()=>ri(a,d,e)),r)}const{ba:f,a:h}=e;(256&t.shapeFlag||d&&uo(d.vnode)&&256&d.vnode.shapeFlag)&&(f&&Co(f),h&&Ar(h,r),f&&Ar((()=>Eo(f)),r)),e.isMounted=!0,t=n=o=null}},l=e.effect=new He(s,(()=>kn(c)),e.scope),c=e.update=()=>l.run();c.id=e.uid,Pr(e,!0),c()},j=(e,t,n)=>{t.component=e;const o=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:a}}=e,s=Gt(r),[l]=e.propsOptions;let c=!1;if(!(o||a>0)||16&a){let o;pr(e,t,r,i)&&(c=!0);for(const i in s)t&&(k(t,i)||(o=U(i))!==i&&k(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=fr(l,s,i,void 0,e,!0)):delete r[i]);if(i!==s)for(const e in i)t&&k(t,e)||(delete i[e],c=!0)}else if(8&a){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let a=n[o];if(Dn(e.emitsOptions,a))continue;const u=t[a];if(l)if(k(i,a))u!==i[a]&&(i[a]=u,c=!0);else{const t=V(a);r[t]=fr(l,s,t,u,e,!1)}else u!==i[a]&&(i[a]=u,c=!0)}}c&&et(e,"set","$attrs")}(e,t.props,o,n),((e,t,n)=>{const{vnode:o,slots:r}=e;let i=!0,a=g;if(32&o.shapeFlag){const e=t._;e?n&&1===e?i=!1:(x(r,t),n||1!==e||delete r._):(i=!t.$stable,xr(t,r)),a=t}else t&&(Tr(e,t),a={default:1});if(i)for(const s in r)br(s)||s in a||delete r[s]})(e,t.children,n),Je(),En(),Qe()},z=(e,t,n,o,r,i,a,s,l=!1)=>{const c=e&&e.children,d=e?e.shapeFlag:0,p=t.children,{patchFlag:f,shapeFlag:h}=t;if(f>0){if(128&f)return void W(c,p,n,o,r,i,a,s,l);if(256&f)return void F(c,p,n,o,r,i,a,s,l)}8&h?(16&d&&ee(c,r,i),p!==c&&u(n,p)):16&d?16&h?W(c,p,n,o,r,i,a,s,l):ee(c,r,i,!0):(8&d&&u(n,""),16&h&&M(p,n,o,r,i,a,s,l))},F=(e,t,n,o,r,i,a,s,l)=>{t=t||m;const c=(e=e||m).length,u=t.length,d=Math.min(c,u);let p;for(p=0;p<d;p++){const o=t[p]=l?ti(t[p]):ei(t[p]);y(e[p],o,n,null,r,i,a,s,l)}c>u?ee(e,r,i,!0,!1,d):M(t,n,o,r,i,a,s,l,d)},W=(e,t,n,o,r,i,a,s,l)=>{let c=0;const u=t.length;let d=e.length-1,p=u-1;for(;c<=d&&c<=p;){const o=e[c],u=t[c]=l?ti(t[c]):ei(t[c]);if(!Wr(o,u))break;y(o,u,n,null,r,i,a,s,l),c++}for(;c<=d&&c<=p;){const o=e[d],c=t[p]=l?ti(t[p]):ei(t[p]);if(!Wr(o,c))break;y(o,c,n,null,r,i,a,s,l),d--,p--}if(c>d){if(c<=p){const e=p+1,d=e<u?t[e].el:o;for(;c<=p;)y(null,t[c]=l?ti(t[c]):ei(t[c]),n,d,r,i,a,s,l),c++}}else if(c>p)for(;c<=d;)Y(e[c],r,i,!0),c++;else{const f=c,h=c,g=new Map;for(c=h;c<=p;c++){const e=t[c]=l?ti(t[c]):ei(t[c]);null!=e.key&&g.set(e.key,c)}let v,b=0;const _=p-h+1;let w=!1,x=0;const T=new Array(_);for(c=0;c<_;c++)T[c]=0;for(c=f;c<=d;c++){const o=e[c];if(b>=_){Y(o,r,i,!0);continue}let u;if(null!=o.key)u=g.get(o.key);else for(v=h;v<=p;v++)if(0===T[v-h]&&Wr(o,t[v])){u=v;break}void 0===u?Y(o,r,i,!0):(T[u-h]=c+1,u>=x?x=u:w=!0,y(o,t[u],n,null,r,i,a,s,l),b++)}const S=w?function(e){const t=e.slice(),n=[0];let o,r,i,a,s;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,a=n.length-1;i<a;)s=i+a>>1,e[n[s]]<l?i=s+1:a=s;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,a=n[i-1];for(;i-- >0;)n[i]=a,a=t[a];return n}(T):m;for(v=S.length-1,c=_-1;c>=0;c--){const e=h+c,d=t[e],p=e+1<u?t[e+1].el:o;0===T[c]?y(null,d,n,p,r,i,a,s,l):w&&(v<0||c!==S[v]?H(d,n,p,2):v--)}}},H=(e,t,o,r,i=null)=>{const{el:a,type:s,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void H(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void s.move(e,t,o,oe);if(s===Or){n(a,t,o);for(let e=0;e<c.length;e++)H(c[e],t,o,r);return void n(e.anchor,t,o)}if(s===Dr)return void T(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(a),n(a,t,o),Ar((()=>l.enter(a)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,s=()=>n(a,t,o),c=()=>{e(a,(()=>{s(),i&&i()}))};r?r(a,s,c):c()}else n(a,t,o)},Y=(e,t,n,o=!1,r=!1)=>{const{type:i,props:a,ref:s,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:p}=e;if(null!=s&&Er(s,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const f=1&u&&p,h=!uo(e);let g;if(h&&(g=a&&a.onVnodeBeforeUnmount)&&ri(g,t,e),6&u)Z(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);f&&zo(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,oe,o):c&&(i!==Or||d>0&&64&d)?ee(c,t,n,!1,!0):(i===Or&&384&d||!r&&16&u)&&ee(l,t,n),o&&J(e)}(h&&(g=a&&a.onVnodeUnmounted)||f)&&Ar((()=>{g&&ri(g,t,e),f&&zo(e,null,t,"unmounted")}),n)},J=e=>{const{type:t,el:n,anchor:r,transition:i}=e;if(t===Or)return void K(n,r);if(t===Dr)return void S(e);const a=()=>{o(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(1&e.shapeFlag&&i&&!i.persisted){const{leave:t,delayLeave:o}=i,r=()=>t(n,a);o?o(e.el,a,r):r()}else a()},K=(e,t)=>{let n;for(;e!==t;)n=p(e),o(e),e=n;o(t)},Z=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:a,um:s}=e;o&&X(o),r.stop(),i&&(i.active=!1,Y(a,e,t,n)),s&&Ar(s,t),Ar((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,r=!1,i=0)=>{for(let a=i;a<e.length;a++)Y(e[a],t,n,o,r)},te=e=>6&e.shapeFlag?te(e.component.subTree):128&e.shapeFlag?e.suspense.next():p(e.anchor||e.el),ne=(e,t,n)=>{null==e?t._vnode&&Y(t._vnode,null,null,!0):y(t._vnode||null,e,t,null,null,null,n),En(),An(),t._vnode=e},oe={p:y,um:Y,m:H,r:J,mt:R,mc:M,pc:z,pbc:I,n:te,o:e};let re,ie;t&&([re,ie]=t(oe));return{render:ne,hydrate:re,createApp:Cr(ne,re)}}(e)}function Pr({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Ir(e,t,n=!1){const o=e.children,r=t.children;if(C(o)&&C(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=ti(r[i]),t.el=e.el),n||Ir(e,t)),t.type===Lr&&(t.el=e.el)}}const Or=Symbol(void 0),Lr=Symbol(void 0),$r=Symbol(void 0),Dr=Symbol(void 0),Rr=[];let Br=null;function Nr(e=!1){Rr.push(Br=e?null:[])}let qr=1;function jr(e){qr+=e}function zr(e){return e.dynamicChildren=qr>0?Br||m:null,Rr.pop(),Br=Rr[Rr.length-1]||null,qr>0&&Br&&Br.push(e),e}function Vr(e,t,n,o,r,i){return zr(Gr(e,t,n,o,r,i,!0))}function Fr(e,t,n,o,r){return zr(Jr(e,t,n,o,r,!0))}function Ur(e){return!!e&&!0===e.__v_isVNode}function Wr(e,t){return e.type===t.type&&e.key===t.key}const Hr="__vInternal",Yr=({key:e})=>null!=e?e:null,Xr=({ref:e,ref_key:t,ref_for:n})=>null!=e?I(e)||tn(e)||P(e)?{i:Rn,r:e,k:t,f:!!n}:e:null;function Gr(e,t=null,n=null,o=0,r=null,i=(e===Or?0:1),a=!1,s=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Yr(t),ref:t&&Xr(t),scopeId:Bn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Rn};return s?(ni(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=I(n)?8:16),qr>0&&!a&&Br&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&Br.push(l),l}const Jr=function(e,t=null,n=null,r=0,i=null,a=!1){e&&e!==Fo||(e=$r);if(Ur(e)){const o=Qr(e,t,!0);return n&&ni(o,n),qr>0&&!a&&Br&&(6&o.shapeFlag?Br[Br.indexOf(e)]=o:Br.push(o)),o.patchFlag|=-2,o}s=e,P(s)&&"__vccOpts"in s&&(e=e.__vccOpts);var s;if(t){t=function(e){return e?Xt(e)||Hr in e?x({},e):e:null}(t);let{class:e,style:n}=t;e&&!I(e)&&(t.class=l(e)),L(n)&&(Xt(n)&&!C(n)&&(n=x({},n)),t.style=o(n))}const c=I(e)?1:Un(e)?128:(e=>e.__isTeleport)(e)?64:L(e)?4:P(e)?2:0;return Gr(e,t,n,r,i,c,a,!0)};function Qr(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:a}=e,s=t?oi(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:s,key:s&&Yr(s),ref:t&&t.ref?n&&r?C(r)?r.concat(Xr(t)):[r,Xr(t)]:Xr(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Or?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Qr(e.ssContent),ssFallback:e.ssFallback&&Qr(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function Kr(e=" ",t=0){return Jr(Lr,null,e,t)}function Zr(e="",t=!1){return t?(Nr(),Fr($r,null,e)):Jr($r,null,e)}function ei(e){return null==e||"boolean"==typeof e?Jr($r):C(e)?Jr(Or,null,e.slice()):"object"==typeof e?ti(e):Jr(Lr,null,String(e))}function ti(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Qr(e)}function ni(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(C(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),ni(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||Hr in t?3===o&&Rn&&(1===Rn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Rn}}else P(t)?(t={default:t,_ctx:Rn},n=32):(t=String(t),64&o?(n=16,t=[Kr(t)]):n=8);e.children=t,e.shapeFlag|=n}function oi(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const e in r)if("class"===e)t.class!==r.class&&(t.class=l([t.class,r.class]));else if("style"===e)t.style=o([t.style,r.style]);else if(_(e)){const n=t[e],o=r[e];!o||n===o||C(n)&&n.includes(o)||(t[e]=n?[].concat(n,o):o)}else""!==e&&(t[e]=r[e])}return t}function ri(e,t,n,o=null){fn(e,t,7,[n,o])}const ii=Sr();let ai=0;let si=null;const li=()=>si||Rn,ci=e=>{si=e,e.scope.on()},ui=()=>{si&&si.scope.off(),si=null};function di(e){return 4&e.vnode.shapeFlag}let pi=!1;function fi(e,t,n){P(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:L(t)&&(e.setupState=cn(t)),hi(e,n)}function hi(e,t,n){const o=e.type;e.render||(e.render=o.render||v),ci(e),Je(),tr(e),Qe(),ui()}function gi(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(cn(Jt(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Qo?Qo[n](e):void 0,has:(e,t)=>t in e||t in Qo}))}function mi(e,t=!0){return P(e)?e.displayName||e.name:e.name||t&&e.__name}const vi=(e,t)=>function(e,t,n=!1){let o,r;const i=P(e);return i?(o=e,r=v):(o=e.get,r=e.set),new dn(o,r,i||!r,n)}(e,0,pi);function yi(e,t,n){const o=arguments.length;return 2===o?L(t)&&!C(t)?Ur(t)?Jr(e,null,[t]):Jr(e,t):Jr(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Ur(n)&&(n=[n]),Jr(e,t,n))}const bi=Symbol(""),_i=()=>Hn(bi),wi="3.2.47",xi="undefined"!=typeof document?document:null,Ti=xi&&xi.createElement("template"),Si={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r=t?xi.createElementNS("http://www.w3.org/2000/svg",e):xi.createElement(e,n?{is:n}:void 0);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>xi.createTextNode(e),createComment:e=>xi.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>xi.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const a=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{Ti.innerHTML=o?`<svg>${e}</svg>`:e;const r=Ti.content;if(o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[a?a.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};const ki=/\s*!important$/;function Ci(e,t,n){if(C(n))n.forEach((n=>Ci(e,t,n)));else if(null==n&&(n=""),n=Ri(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=Ai[t];if(n)return n;let o=V(t);if("filter"!==o&&o in e)return Ai[t]=o;o=W(o);for(let r=0;r<Ei.length;r++){const n=Ei[r]+o;if(n in e)return Ai[t]=n}return t}(e,t);ki.test(n)?e.setProperty(U(o),n.replace(ki,""),"important"):e[o]=n}}const Ei=["Webkit","Moz","ms"],Ai={};const{unit:Mi,unitRatio:Pi,unitPrecision:Ii}={unit:"rem",unitRatio:10/320,unitPrecision:5},Oi=(Li=Mi,$i=Pi,Di=Ii,e=>e.replace(ge,((e,t)=>{if(!t)return e;if(1===$i)return`${t}${Li}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*$i,Di);return 0===n?"0":`${n}${Li}`})));var Li,$i,Di;const Ri=e=>I(e)?Oi(e):e,Bi="http://www.w3.org/1999/xlink";function Ni(e,t,n,o){e.addEventListener(t,n,o)}function qi(e,t,n,o,r=null){const i=e._vei||(e._vei={}),a=i[t];if(o&&a)a.value=o;else{const[n,s]=function(e){let t;if(ji.test(e)){let n;for(t={};n=e.match(ji);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):U(e.slice(2)),t]}(t);if(o){const a=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&C(i)){const n=Fi(e,i);for(let o=0;o<n.length;o++){const i=n[o];fn(i,t,5,i.__wwe?[e]:r(e))}}else fn(Fi(e,i),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=(()=>zi||(Vi.then((()=>zi=0)),zi=Date.now()))(),n}(o,r);Ni(e,n,a,s)}else a&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,a,s),i[t]=void 0)}}const ji=/(?:Once|Passive|Capture)$/;let zi=0;const Vi=Promise.resolve();function Fi(e,t){if(C(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const Ui=/^on[a-z]/;const Wi="transition",Hi=(e,{slots:t})=>yi(no,function(e){const t={};for(const x in e)x in Yi||(t[x]=e[x]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:a=`${n}-enter-active`,enterToClass:s=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:c=a,appearToClass:u=s,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:f=`${n}-leave-to`}=e,h=function(e){if(null==e)return null;if(L(e))return[Ji(e.enter),Ji(e.leave)];{const t=Ji(e);return[t,t]}}(r),g=h&&h[0],m=h&&h[1],{onBeforeEnter:v,onEnter:y,onEnterCancelled:b,onLeave:_,onLeaveCancelled:w,onBeforeAppear:T=v,onAppear:S=y,onAppearCancelled:k=b}=t,C=(e,t,n)=>{Ki(e,t?u:s),Ki(e,t?c:a),n&&n()},E=(e,t)=>{e._isLeaving=!1,Ki(e,d),Ki(e,f),Ki(e,p),t&&t()},A=e=>(t,n)=>{const r=e?S:y,a=()=>C(t,e,n);Xi(r,[t,a]),Zi((()=>{Ki(t,e?l:i),Qi(t,e?u:s),Gi(r)||ta(t,o,g,a)}))};return x(t,{onBeforeEnter(e){Xi(v,[e]),Qi(e,i),Qi(e,a)},onBeforeAppear(e){Xi(T,[e]),Qi(e,l),Qi(e,c)},onEnter:A(!1),onAppear:A(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>E(e,t);Qi(e,d),document.body.offsetHeight,Qi(e,p),Zi((()=>{e._isLeaving&&(Ki(e,d),Qi(e,f),Gi(_)||ta(e,o,m,n))})),Xi(_,[e,n])},onEnterCancelled(e){C(e,!1),Xi(b,[e])},onAppearCancelled(e){C(e,!0),Xi(k,[e])},onLeaveCancelled(e){E(e),Xi(w,[e])}})}(e),t);Hi.displayName="Transition";const Yi={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};Hi.props=x({},to,Yi);const Xi=(e,t=[])=>{C(e)?e.forEach((e=>e(...t))):e&&e(...t)},Gi=e=>!!e&&(C(e)?e.some((e=>e.length>1)):e.length>1);function Ji(e){const t=(e=>{const t=I(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function Qi(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e._vtc||(e._vtc=new Set)).add(t)}function Ki(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function Zi(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let ea=0;function ta(e,t,n,o){const r=e._endId=++ea,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:a,timeout:s,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o("transitionDelay"),i=o("transitionDuration"),a=na(r,i),s=o("animationDelay"),l=o("animationDuration"),c=na(s,l);let u=null,d=0,p=0;t===Wi?a>0&&(u=Wi,d=a,p=i.length):"animation"===t?c>0&&(u="animation",d=c,p=l.length):(d=Math.max(a,c),u=d>0?a>c?Wi:"animation":null,p=u?u===Wi?i.length:l.length:0);const f=u===Wi&&/\b(transform|all)(,|$)/.test(o("transitionProperty").toString());return{type:u,timeout:d,propCount:p,hasTransform:f}}(e,t);if(!a)return o();const c=a+"end";let u=0;const d=()=>{e.removeEventListener(c,p),i()},p=t=>{t.target===e&&++u>=l&&d()};setTimeout((()=>{u<l&&d()}),s+1),e.addEventListener(c,p)}function na(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>oa(t)+oa(e[n]))))}function oa(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}const ra=e=>{const t=e.props["onUpdate:modelValue"]||!1;return C(t)?e=>X(t,e):t};function ia(e){e.target.composing=!0}function aa(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const sa={created(e,{modifiers:{lazy:t,trim:n,number:o}},r){e._assign=ra(r);const i=o||r.props&&"number"===r.props.type;Ni(e,t?"change":"input",(t=>{if(t.target.composing)return;let o=e.value;n&&(o=o.trim()),i&&(o=J(o)),e._assign(o)})),n&&Ni(e,"change",(()=>{e.value=e.value.trim()})),t||(Ni(e,"compositionstart",ia),Ni(e,"compositionend",aa),Ni(e,"change",aa))},mounted(e,{value:t}){e.value=null==t?"":t},beforeUpdate(e,{value:t,modifiers:{lazy:n,trim:o,number:r}},i){if(e._assign=ra(i),e.composing)return;if(document.activeElement===e&&"range"!==e.type){if(n)return;if(o&&e.value.trim()===t)return;if((r||"number"===e.type)&&J(e.value)===t)return}const a=null==t?"":t;e.value!==a&&(e.value=a)}},la={deep:!0,created(e,t,n){e._assign=ra(n),Ni(e,"change",(()=>{const t=e._modelValue,n=fa(e),o=e.checked,r=e._assign;if(C(t)){const e=p(t,n),i=-1!==e;if(o&&!i)r(t.concat(n));else if(!o&&i){const n=[...t];n.splice(e,1),r(n)}}else if(A(t)){const e=new Set(t);o?e.add(n):e.delete(n),r(e)}else r(ha(e,o))}))},mounted:ca,beforeUpdate(e,t,n){e._assign=ra(n),ca(e,t,n)}};function ca(e,{value:t,oldValue:n},o){e._modelValue=t,C(t)?e.checked=p(t,o.props.value)>-1:A(t)?e.checked=t.has(o.props.value):t!==n&&(e.checked=d(t,ha(e,!0)))}const ua={created(e,{value:t},n){e.checked=d(t,n.props.value),e._assign=ra(n),Ni(e,"change",(()=>{e._assign(fa(e))}))},beforeUpdate(e,{value:t,oldValue:n},o){e._assign=ra(o),t!==n&&(e.checked=d(t,o.props.value))}},da={deep:!0,created(e,{value:t,modifiers:{number:n}},o){const r=A(t);Ni(e,"change",(()=>{const t=Array.prototype.filter.call(e.options,(e=>e.selected)).map((e=>n?J(fa(e)):fa(e)));e._assign(e.multiple?r?new Set(t):t:t[0])})),e._assign=ra(o)},mounted(e,{value:t}){pa(e,t)},beforeUpdate(e,t,n){e._assign=ra(n)},updated(e,{value:t}){pa(e,t)}};function pa(e,t){const n=e.multiple;if(!n||C(t)||A(t)){for(let o=0,r=e.options.length;o<r;o++){const r=e.options[o],i=fa(r);if(n)C(t)?r.selected=p(t,i)>-1:r.selected=t.has(i);else if(d(fa(r),t))return void(e.selectedIndex!==o&&(e.selectedIndex=o))}n||-1===e.selectedIndex||(e.selectedIndex=-1)}}function fa(e){return"_value"in e?e._value:e.value}function ha(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const ga={created(e,t,n){ma(e,t,n,null,"created")},mounted(e,t,n){ma(e,t,n,null,"mounted")},beforeUpdate(e,t,n,o){ma(e,t,n,o,"beforeUpdate")},updated(e,t,n,o){ma(e,t,n,o,"updated")}};function ma(e,t,n,o,r){const i=function(e,t){switch(e){case"SELECT":return da;case"TEXTAREA":return sa;default:switch(t){case"checkbox":return la;case"radio":return ua;default:return sa}}}(e.tagName,n.props&&n.props.type)[r];i&&i(e,t,n,o)}const va=["ctrl","shift","alt","meta"],ya={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>va.some((n=>e[`${n}Key`]&&!t.includes(n)))},ba=(e,t)=>(n,...o)=>{for(let e=0;e<t.length;e++){const o=ya[t[e]];if(o&&o(n,t))return}return e(n,...o)},_a={beforeMount(e,{value:t},{transition:n}){e._vod="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):wa(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),wa(e,!0),o.enter(e)):o.leave(e,(()=>{wa(e,!1)})):wa(e,t))},beforeUnmount(e,{value:t}){wa(e,t)}};function wa(e,t){e.style.display=t?e._vod:"none"}const xa=x({patchProp:(e,t,n,o,r=!1,i,a,s,l)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,a=i[r],s=(e.__wxsProps||(e.__wxsProps={}))[r];if(s===a)return;e.__wxsProps[r]=a;const l=o.proxy;Sn((()=>{n(a,s,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,a);"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e._vtc;i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,r):"style"===t?function(e,t,n){const o=e.style,r=I(n);if(n&&!r){if(t&&!I(t))for(const e in t)null==n[e]&&Ci(o,e,"");for(const e in n)Ci(o,e,n[e])}else{const i=o.display;r?t!==n&&(o.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(o.display=i)}const{__wxsStyle:i}=e;if(i)for(const a in i)Ci(o,a,i[a])}(e,n,o):_(t)?w(t)||qi(e,t,0,o,a):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&Ui.test(t)&&P(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if(Ui.test(t)&&I(n))return!1;return t in e}(e,t,o,r))?function(e,t,n,o,r,i,a){if("innerHTML"===t||"textContent"===t)return o&&a(o,r,i),void(e[t]=null==n?"":n);if("value"===t&&"PROGRESS"!==e.tagName&&!e.tagName.includes("-")){e._value=n;const o=null==n?"":n;return e.value===o&&"OPTION"!==e.tagName||(e.value=o),void(null==n&&e.removeAttribute(t))}let s=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=u(n):null==n&&"string"===o?(n="",s=!0):"number"===o&&(n=0,s=!0)}try{e[t]=n}catch(kC){}s&&e.removeAttribute(t)}(e,t,o,i,a,s,l):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o,r){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(Bi,t.slice(6,t.length)):e.setAttributeNS(Bi,t,n);else{const o=c(t);null==n||o&&!u(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,r))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},Si);let Ta;const Sa=(...e)=>{const t=(Ta||(Ta=Mr(xa))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(I(e)){return document.querySelector(e)}return e}(e);if(!o)return;const r=t._component;P(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,o instanceof SVGElement);return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const ka=["{","}"];const Ca=/^(?:\d)+/,Ea=/^(?:\w)+/;const Aa=Object.prototype.hasOwnProperty,Ma=(e,t)=>Aa.call(e,t),Pa=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=ka){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let a=e[r++];if(a===t){i&&o.push({type:"text",value:i}),i="";let t="";for(a=e[r++];void 0!==a&&a!==n;)t+=a,a=e[r++];const s=a===n,l=Ca.test(t)?"list":s&&Ea.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=a}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function Ia(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class Oa{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||Pa,this.messages=n||{},this.setLocale(e||"en"),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=Ia(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{Ma(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=Ia(t,this.messages))&&(o=this.messages[t]):n=t,Ma(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function La(e,t={},n,o){"string"!=typeof e&&([e,t]=[t,e]),"string"!=typeof e&&(e="undefined"!=typeof uni&&uni.getLocale?uni.getLocale():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const r=new Oa({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=yv().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}function $a(e,t){return e.indexOf(t[0])>-1}
/*!
  * vue-router v4.1.6
  * (c) 2022 Eduardo San Martin Morote
  * @license MIT
  */const Da="undefined"!=typeof window;const Ra=Object.assign;function Ba(e,t){const n={};for(const o in t){const r=t[o];n[o]=qa(r)?r.map(e):e(r)}return n}const Na=()=>{},qa=Array.isArray,ja=/\/$/;function za(e,t,n="/"){let o,r={},i="",a="";const s=t.indexOf("#");let l=t.indexOf("?");return s<l&&s>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,s>-1?s:t.length),r=e(i)),s>-1&&(o=o||t.slice(0,s),a=t.slice(s,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/");let r,i,a=n.length-1;for(r=0;r<o.length;r++)if(i=o[r],"."!==i){if(".."!==i)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+o.slice(r-(r===o.length?1:0)).join("/")}(null!=o?o:t,n),{fullPath:o+(i&&"?")+i+a,path:o,query:r,hash:a}}function Va(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Fa(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ua(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Wa(e[n],t[n]))return!1;return!0}function Wa(e,t){return qa(e)?Ha(e,t):qa(t)?Ha(t,e):e===t}function Ha(e,t){return qa(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}var Ya,Xa,Ga,Ja;function Qa(e){if(!e)if(Da){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(ja,"")}(Xa=Ya||(Ya={})).pop="pop",Xa.push="push",(Ja=Ga||(Ga={})).back="back",Ja.forward="forward",Ja.unknown="";const Ka=/^[^#]+#/;function Za(e,t){return e.replace(Ka,"#")+t}const es=()=>({left:window.pageXOffset,top:window.pageYOffset});function ts(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.pageXOffset,null!=t.top?t.top:window.pageYOffset)}function ns(e,t){return(history.state?history.state.position-t:-1)+e}const os=new Map;function rs(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let t=r.includes(e.slice(i))?e.slice(i).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),Va(n,"")}return Va(n,e)+o+r}function is(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?es():null}}function as(e){const{history:t,location:n}=window,o={value:rs(e,n)},r={value:t.state};function i(o,i,a){const s=e.indexOf("#"),l=s>-1?(n.host&&document.querySelector("base")?e:e.slice(s))+o:location.protocol+"//"+location.host+e+o;try{t[a?"replaceState":"pushState"](i,"",l),r.value=i}catch(c){console.error(c),n[a?"replace":"assign"](l)}}return r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const a=Ra({},r.value,t.state,{forward:e,scroll:es()});i(a.current,a,!0),i(e,Ra({},is(o.value,e,null),{position:a.position+1},n),!1),o.value=e},replace:function(e,n){i(e,Ra({},t.state,is(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}function ss(e){const t=as(e=Qa(e)),n=function(e,t,n,o){let r=[],i=[],a=null;const s=({state:i})=>{const s=rs(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=s,t.value=i,a&&a===l)return void(a=null);u=c?i.position-c.position:0}else o(s);r.forEach((e=>{e(n.value,l,{delta:u,type:Ya.pop,direction:u?u>0?Ga.forward:Ga.back:Ga.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(Ra({},e.state,{scroll:es()}),"")}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",l),{pauseListeners:function(){a=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=Ra({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:Za.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function ls(e){return"string"==typeof e||"symbol"==typeof e}const cs={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},us=Symbol("");var ds,ps;function fs(e,t){return Ra(new Error,{type:e,[us]:!0},t)}function hs(e,t){return e instanceof Error&&us in e&&(null==t||!!(e.type&t))}(ps=ds||(ds={}))[ps.aborted=4]="aborted",ps[ps.cancelled=8]="cancelled",ps[ps.duplicated=16]="duplicated";const gs={sensitive:!1,strict:!1,start:!0,end:!0},ms=/[.+*?^${}()[\]/\\]/g;function vs(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function ys(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=vs(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(bs(o))return 1;if(bs(r))return-1}return r.length-o.length}function bs(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const _s={type:0,value:""},ws=/[a-zA-Z0-9_]/;function xs(e,t,n){const o=function(e,t){const n=Ra({},gs,t),o=[];let r=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let a=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(ms,"\\$&"),a+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;i.push({name:e,repeatable:n,optional:c});const d=u||"[^/]+?";if("[^/]+?"!==d){a+=10;try{new RegExp(`(${d})`)}catch(s){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+s.message)}}let p=n?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(p=c&&l.length<2?`(?:/${p})`:"/"+p),c&&(p+="?"),r+=p,a+=20,c&&(a+=-8),n&&(a+=-20),".*"===d&&(a+=-50)}e.push(a)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const a=new RegExp(r,n.sensitive?"":"i");return{re:a,score:o,keys:i,parse:function(e){const t=e.match(a),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=i[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:a,optional:s}=e,l=i in t?t[i]:"";if(qa(l)&&!a)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=qa(l)?l.join("/"):l;if(!c){if(!s)throw new Error(`Missing required param "${i}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[_s]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let i;function a(){i&&r.push(i),i=[]}let s,l=0,c="",u="";function d(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===s||"+"===s)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===s||"+"===s,optional:"*"===s||"?"===s})):t("Invalid state to consume buffer"),c="")}function p(){c+=s}for(;l<e.length;)if(s=e[l++],"\\"!==s||2===n)switch(n){case 0:"/"===s?(c&&d(),a()):":"===s?(d(),n=1):p();break;case 4:p(),n=o;break;case 1:"("===s?n=2:ws.test(s)?p():(d(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--);break;case 2:")"===s?"\\"==u[u.length-1]?u=u.slice(0,-1)+s:n=3:u+=s;break;case 3:d(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),d(),a(),r}(e.path),n),r=Ra(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Ts(e,t){const n=[],o=new Map;function r(e,n,o){const s=!o,l=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:ks(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}(e);l.aliasOf=o&&o.record;const c=As(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(Ra({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l}))}let d,p;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(d=xs(t,n,c),o?o.alias.push(d):(p=p||d,p!==d&&p.alias.push(d),s&&e.name&&!Cs(d)&&i(e.name)),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],d,o&&o.children[t])}o=o||d,(d.record.components&&Object.keys(d.record.components).length||d.record.name||d.record.redirect)&&a(d)}return p?()=>{i(p)}:Na}function i(e){if(ls(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function a(e){let t=0;for(;t<n.length&&ys(e,n[t])>=0&&(e.record.path!==n[t].record.path||!Ms(e,n[t]));)t++;n.splice(t,0,e),e.record.name&&!Cs(e)&&o.set(e.record.name,e)}return t=As({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,i,a,s={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw fs(1,{location:e});a=r.record.name,s=Ra(Ss(t.params,r.keys.filter((e=>!e.optional)).map((e=>e.name))),e.params&&Ss(e.params,r.keys.map((e=>e.name)))),i=r.stringify(s)}else if("path"in e)i=e.path,r=n.find((e=>e.re.test(i))),r&&(s=r.parse(i),a=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw fs(1,{location:e,currentLocation:t});a=r.record.name,s=Ra({},t.params,e.params),i=r.stringify(s)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:a,path:i,params:s,matched:l,meta:Es(l)}},removeRoute:i,getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function Ss(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function ks(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="boolean"==typeof n?n:n[o];return t}function Cs(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Es(e){return e.reduce(((e,t)=>Ra(e,t.meta)),{})}function As(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function Ms(e,t){return t.children.some((t=>t===e||Ms(e,t)))}const Ps=/#/g,Is=/&/g,Os=/\//g,Ls=/=/g,$s=/\?/g,Ds=/\+/g,Rs=/%5B/g,Bs=/%5D/g,Ns=/%5E/g,qs=/%60/g,js=/%7B/g,zs=/%7C/g,Vs=/%7D/g,Fs=/%20/g;function Us(e){return encodeURI(""+e).replace(zs,"|").replace(Rs,"[").replace(Bs,"]")}function Ws(e){return Us(e).replace(Ds,"%2B").replace(Fs,"+").replace(Ps,"%23").replace(Is,"%26").replace(qs,"`").replace(js,"{").replace(Vs,"}").replace(Ns,"^")}function Hs(e){return null==e?"":function(e){return Us(e).replace(Ps,"%23").replace($s,"%3F")}(e).replace(Os,"%2F")}function Ys(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function Xs(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Ds," "),r=e.indexOf("="),i=Ys(r<0?e:e.slice(0,r)),a=r<0?null:Ys(e.slice(r+1));if(i in t){let e=t[i];qa(e)||(e=t[i]=[e]),e.push(a)}else t[i]=a}return t}function Gs(e){let t="";for(let n in e){const o=e[n];if(n=Ws(n).replace(Ls,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(qa(o)?o.map((e=>e&&Ws(e))):[o&&Ws(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function Js(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=qa(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const Qs=Symbol(""),Ks=Symbol(""),Zs=Symbol(""),el=Symbol(""),tl=Symbol("");function nl(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e,reset:function(){e=[]}}}function ol(e,t,n,o,r){const i=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((a,s)=>{const l=e=>{var l;!1===e?s(fs(4,{from:n,to:t})):e instanceof Error?s(e):"string"==typeof(l=e)||l&&"object"==typeof l?s(fs(2,{from:t,to:e})):(i&&o.enterCallbacks[r]===i&&"function"==typeof e&&i.push(e),a())},c=e.call(o&&o.instances[r],t,n,l);let u=Promise.resolve(c);e.length<3&&(u=u.then(l)),u.catch((e=>s(e)))}))}function rl(e,t,n,o){const r=[];for(const a of e)for(const e in a.components){let s=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if("object"==typeof(i=s)||"displayName"in i||"props"in i||"__vccOpts"in i){const i=(s.__vccOpts||s)[t];i&&r.push(ol(i,n,o,a,e))}else{let i=s();r.push((()=>i.then((r=>{if(!r)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${a.path}"`));const i=(s=r).__esModule||"Module"===s[Symbol.toStringTag]?r.default:r;var s;a.components[e]=i;const l=(i.__vccOpts||i)[t];return l&&ol(l,n,o,a,e)()}))))}}var i;return r}function il(e){const t=Hn(Zs),n=Hn(el),o=vi((()=>t.resolve(sn(e.to)))),r=vi((()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;const a=i.findIndex(Fa.bind(null,r));if(a>-1)return a;const s=ll(e[t-2]);return t>1&&ll(r)===s&&i[i.length-1].path!==s?i.findIndex(Fa.bind(null,e[t-2])):a})),i=vi((()=>r.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!qa(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}(n.params,o.value.params))),a=vi((()=>r.value>-1&&r.value===n.matched.length-1&&Ua(n.params,o.value.params)));return{route:o,href:vi((()=>o.value.href)),isActive:i,isExactActive:a,navigate:function(n={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)?t[sn(e.replace)?"replace":"push"](sn(e.to)).catch(Na):Promise.resolve()}}}const al=co({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:il,setup(e,{slots:t}){const n=Vt(il(e)),{options:o}=Hn(Zs),r=vi((()=>({[cl(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[cl(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:yi("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),sl=al;function ll(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const cl=(e,t,n)=>null!=e?e:null!=t?t:n;function ul(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const dl=co({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=Hn(tl),r=vi((()=>e.route||o.value)),i=Hn(Ks,0),a=vi((()=>{let e=sn(i);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),s=vi((()=>r.value.matched[a.value]));Wn(Ks,vi((()=>a.value+1))),Wn(Qs,s),Wn(tl,r);const l=nn();return Gn((()=>[l.value,s.value,e.name]),(([e,t,n],[o,r,i])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&Fa(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,i=e.name,a=s.value,c=a&&a.components[i];if(!c)return ul(n.default,{Component:c,route:o});const u=a.props[i],d=u?!0===u?o.params:"function"==typeof u?u(o):u:null,p=yi(c,Ra({},d,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(a.instances[i]=null)},ref:l}));return ul(n.default,{Component:p,route:o})||p}}});function pl(e){const t=Ts(e.routes,e),n=e.parseQuery||Xs,o=e.stringifyQuery||Gs,r=e.history,i=nl(),a=nl(),s=nl(),l=on(cs);let c=cs;Da&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Ba.bind(null,(e=>""+e)),d=Ba.bind(null,Hs),p=Ba.bind(null,Ys);function f(e,i){if(i=Ra({},i||l.value),"string"==typeof e){const o=za(n,e,i.path),a=t.resolve({path:o.path},i),s=r.createHref(o.fullPath);return Ra(o,a,{params:p(a.params),hash:Ys(o.hash),redirectedFrom:void 0,href:s})}let a;if("path"in e)a=Ra({},e,{path:za(n,e.path,i.path).path});else{const t=Ra({},e.params);for(const e in t)null==t[e]&&delete t[e];a=Ra({},e,{params:d(e.params)}),i.params=d(i.params)}const s=t.resolve(a,i),c=e.hash||"";s.params=u(p(s.params));const f=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,Ra({},e,{hash:(h=c,Us(h).replace(js,"{").replace(Vs,"}").replace(Ns,"^")),path:s.path}));var h;const g=r.createHref(f);return Ra({fullPath:f,hash:c,query:o===Gs?Js(e.query):e.query||{}},s,{redirectedFrom:void 0,href:g})}function h(e){return"string"==typeof e?za(n,e,l.value.path):Ra({},e)}function g(e,t){if(c!==e)return fs(8,{from:t,to:e})}function m(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=h(o):{path:o},o.params={}),Ra({query:e.query,hash:e.hash,params:"path"in o?{}:e.params},o)}}function y(e,t){const n=c=f(e),r=l.value,i=e.state,a=e.force,s=!0===e.replace,u=v(n);if(u)return y(Ra(h(u),{state:"object"==typeof u?Ra({},i,u.state):i,force:a,replace:s}),t||n);const d=n;let p;return d.redirectedFrom=t,!a&&function(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&Fa(t.matched[o],n.matched[r])&&Ua(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(p=fs(16,{to:d,from:r}),P(r,r,!0,!1)),(p?Promise.resolve(p):_(d,r)).catch((e=>hs(e)?hs(e,2)?e:M(e):A(e,d,r))).then((e=>{if(e){if(hs(e,2))return y(Ra({replace:s},h(e.to),{state:"object"==typeof e.to?Ra({},i,e.to.state):i,force:a}),t||d)}else e=x(d,r,!0,s,i);return w(d,r,e),e}))}function b(e,t){const n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function _(e,t){let n;const[o,r,s]=function(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let a=0;a<i;a++){const i=t.matched[a];i&&(e.matched.find((e=>Fa(e,i)))?o.push(i):n.push(i));const s=e.matched[a];s&&(t.matched.find((e=>Fa(e,s)))||r.push(s))}return[n,o,r]}(e,t);n=rl(o.reverse(),"beforeRouteLeave",e,t);for(const i of o)i.leaveGuards.forEach((o=>{n.push(ol(o,e,t))}));const l=b.bind(null,e,t);return n.push(l),fl(n).then((()=>{n=[];for(const o of i.list())n.push(ol(o,e,t));return n.push(l),fl(n)})).then((()=>{n=rl(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(ol(o,e,t))}));return n.push(l),fl(n)})).then((()=>{n=[];for(const o of e.matched)if(o.beforeEnter&&!t.matched.includes(o))if(qa(o.beforeEnter))for(const r of o.beforeEnter)n.push(ol(r,e,t));else n.push(ol(o.beforeEnter,e,t));return n.push(l),fl(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=rl(s,"beforeRouteEnter",e,t),n.push(l),fl(n)))).then((()=>{n=[];for(const o of a.list())n.push(ol(o,e,t));return n.push(l),fl(n)})).catch((e=>hs(e,8)?e:Promise.reject(e)))}function w(e,t,n){for(const o of s.list())o(e,t,n)}function x(e,t,n,o,i){const a=g(e,t);if(a)return a;const s=t===cs,c=Da?history.state:{};n&&(o||s?r.replace(e.fullPath,Ra({scroll:s&&c&&c.scroll},i)):r.push(e.fullPath,i)),l.value=e,P(e,t,n,s),M()}let T;function S(){T||(T=r.listen(((e,t,n)=>{if(!$.listening)return;const o=f(e),i=v(o);if(i)return void y(Ra(i,{replace:!0}),o).catch(Na);c=o;const a=l.value;var s,u;Da&&(s=ns(a.fullPath,n.delta),u=es(),os.set(s,u)),_(o,a).catch((e=>hs(e,12)?e:hs(e,2)?(y(e.to,o).then((e=>{hs(e,20)&&!n.delta&&n.type===Ya.pop&&r.go(-1,!1)})).catch(Na),Promise.reject()):(n.delta&&r.go(-n.delta,!1),A(e,o,a)))).then((e=>{(e=e||x(o,a,!1))&&(n.delta&&!hs(e,8)?r.go(-n.delta,!1):n.type===Ya.pop&&hs(e,20)&&r.go(-1,!1)),w(o,a,e)})).catch(Na)})))}let k,C=nl(),E=nl();function A(e,t,n){M(e);const o=E.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function M(e){return k||(k=!e,S(),C.list().forEach((([t,n])=>e?n(e):t())),C.reset()),e}function P(t,n,o,r){const{scrollBehavior:i}=e;if(!Da||!i)return Promise.resolve();const a=!o&&function(e){const t=os.get(e);return os.delete(e),t}(ns(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return Sn().then((()=>i(t,n,a))).then((e=>e&&ts(e))).catch((e=>A(e,t,n)))}const I=e=>r.go(e);let O;const L=new Set,$={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return ls(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:f,options:e,push:m,replace:function(e){return m(Ra(h(e),{replace:!0}))},go:I,back:()=>I(-1),forward:()=>I(1),beforeEach:i.add,beforeResolve:a.add,afterEach:s.add,onError:E.add,isReady:function(){return k&&l.value!==cs?Promise.resolve():new Promise(((e,t)=>{C.add([e,t])}))},install(e){e.component("RouterLink",sl),e.component("RouterView",dl),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>sn(l)}),Da&&!O&&l.value===cs&&(O=!0,m(r.location).catch((e=>{})));const t={};for(const o in cs)t[o]=vi((()=>l.value[o]));e.provide(Zs,this),e.provide(el,Vt(t)),e.provide(tl,l);const n=e.unmount;L.add(e),e.unmount=function(){L.delete(e),L.size<1&&(c=cs,T&&T(),T=null,l.value=cs,O=!1,k=!1),n()}}};return $}function fl(e){return e.reduce(((e,t)=>e.then((()=>t()))),Promise.resolve())}function hl(){return Hn(el)}const gl=le((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let ml;function vl(e){return $a(e,Z)?_l().f(e,function(){const e=uni.getLocale(),t=__uniConfig.locales;return t[e]||t[__uniConfig.fallbackLocale]||t.en||{}}(),Z):e}function yl(e,t){if(1===t.length){if(e){const n=e=>I(e)&&$a(e,Z),o=t[0];let r=[];if(C(e)&&(r=e.filter((e=>n(e[o])))).length)return r;const i=e[t[0]];if(n(i))return e}return}const n=t.shift();return yl(e&&e[n],t)}function bl(e,t){const n=yl(e,t);if(!n)return!1;const o=t[t.length-1];if(C(n))n.forEach((e=>bl(e,[o])));else{let e=n[o];Object.defineProperty(n,o,{get:()=>vl(e),set(t){e=t}})}return!0}function _l(){if(!ml){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,ml=La(e),gl()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>ml.add(e,__uniConfig.locales[e]))),ml.setLocale(e)}}return ml}function wl(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const xl=le((()=>{const e="uni.async.",t=["error"];_l().add("en",wl(e,t,["The connection timed out, click the screen to try again."]),!1),_l().add("es",wl(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),_l().add("fr",wl(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),_l().add("zh-Hans",wl(e,t,["连接服务器超时，点击屏幕重试"]),!1),_l().add("zh-Hant",wl(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),Tl=le((()=>{const e="uni.showActionSheet.",t=["cancel"];_l().add("en",wl(e,t,["Cancel"]),!1),_l().add("es",wl(e,t,["Cancelar"]),!1),_l().add("fr",wl(e,t,["Annuler"]),!1),_l().add("zh-Hans",wl(e,t,["取消"]),!1),_l().add("zh-Hant",wl(e,t,["取消"]),!1)})),Sl=le((()=>{const e="uni.showToast.",t=["unpaired"];_l().add("en",wl(e,t,["Please note showToast must be paired with hideToast"]),!1),_l().add("es",wl(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),_l().add("fr",wl(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),_l().add("zh-Hans",wl(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),_l().add("zh-Hant",wl(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),kl=le((()=>{const e="uni.showLoading.",t=["unpaired"];_l().add("en",wl(e,t,["Please note showLoading must be paired with hideLoading"]),!1),_l().add("es",wl(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),_l().add("fr",wl(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),_l().add("zh-Hans",wl(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),_l().add("zh-Hant",wl(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),Cl=le((()=>{const e="uni.showModal.",t=["cancel","confirm"];_l().add("en",wl(e,t,["Cancel","OK"]),!1),_l().add("es",wl(e,t,["Cancelar","OK"]),!1),_l().add("fr",wl(e,t,["Annuler","OK"]),!1),_l().add("zh-Hans",wl(e,t,["取消","确定"]),!1),_l().add("zh-Hant",wl(e,t,["取消","確定"]),!1)})),El=le((()=>{const e="uni.chooseFile.",t=["notUserActivation"];_l().add("en",wl(e,t,["File chooser dialog can only be shown with a user activation"]),!1),_l().add("es",wl(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),_l().add("fr",wl(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),_l().add("zh-Hans",wl(e,t,["文件选择器对话框只能在由用户激活时显示"]),!1),_l().add("zh-Hant",wl(e,t,["文件選擇器對話框只能在由用戶激活時顯示"]),!1)})),Al=le((()=>{const e="uni.setClipboardData.",t=["success","fail"];_l().add("en",wl(e,t,["Content copied","Copy failed, please copy manually"]),!1),_l().add("es",wl(e,t,["Contenido copiado","Error al copiar, copie manualmente"]),!1),_l().add("fr",wl(e,t,["Contenu copié","Échec de la copie, copiez manuellement"]),!1),_l().add("zh-Hans",wl(e,t,["内容已复制","复制失败，请手动复制"]),!1),_l().add("zh-Hant",wl(e,t,["內容已復制","復制失敗，請手動復製"]),!1)})),Ml=le((()=>{const e="uni.getClipboardData.",t=["fail"];_l().add("en",wl(e,t,["Reading failed, please paste manually"]),!1),_l().add("es",wl(e,t,["Error de lectura, pegue manualmente"]),!1),_l().add("fr",wl(e,t,["Échec de la lecture, veuillez coller manuellement"]),!1),_l().add("zh-Hans",wl(e,t,["读取失败，请手动粘贴"]),!1),_l().add("zh-Hant",wl(e,t,["讀取失敗，請手動粘貼"]),!1)})),Pl=le((()=>{const e="uni.picker.",t=["done","cancel"];_l().add("en",wl(e,t,["Done","Cancel"]),!1),_l().add("es",wl(e,t,["OK","Cancelar"]),!1),_l().add("fr",wl(e,t,["OK","Annuler"]),!1),_l().add("zh-Hans",wl(e,t,["完成","取消"]),!1),_l().add("zh-Hant",wl(e,t,["完成","取消"]),!1)})),Il=le((()=>{const e="uni.video.",t=["danmu","volume"];_l().add("en",wl(e,t,["Danmu","Volume"]),!1),_l().add("es",wl(e,t,["Danmu","Volumen"]),!1),_l().add("fr",wl(e,t,["Danmu","Le Volume"]),!1),_l().add("zh-Hans",wl(e,t,["弹幕","音量"]),!1),_l().add("zh-Hant",wl(e,t,["彈幕","音量"]),!1)})),Ol=le((()=>{const e="uni.chooseLocation.",t=["search","cancel"];_l().add("en",wl(e,t,["Find Place","Cancel"]),!1),_l().add("es",wl(e,t,["Encontrar","Cancelar"]),!1),_l().add("fr",wl(e,t,["Trouve","Annuler"]),!1),_l().add("zh-Hans",wl(e,t,["搜索地点","取消"]),!1),_l().add("zh-Hant",wl(e,t,["搜索地點","取消"]),!1)}));function Ll(e){const t=new Ie;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}let $l=1;const Dl=Object.create(null);function Rl(e,t){return e+"."+t}function Bl(e,t,n){t=Rl(e,t),Dl[t]||(Dl[t]=n)}function Nl({id:e,name:t,args:n},o){t=Rl(o,t);const r=t=>{e&&Ix.publishHandler("invokeViewApi."+e,t)},i=Dl[t];i?i(n,r):r({})}const ql=x(Ll("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=Ix,i=n?$l++:0;n&&o("invokeServiceApi."+i,n,!0),r("invokeServiceApi",{id:i,name:e,args:t})}}),jl=me(!0);let zl;function Vl(){zl&&(clearTimeout(zl),zl=null)}let Fl=0,Ul=0;function Wl(e){if(Vl(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];Fl=t,Ul=n,zl=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}function Hl(e){if(!zl)return;if(1!==e.touches.length)return Vl();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Fl)>10||Math.abs(n-Ul)>10?Vl():void 0}function Yl(e,t){const n=Number(e);return isNaN(n)?t:n}function Xl(){const e=__uniConfig.globalStyle||{},t=Yl(e.rpxCalcMaxDeviceWidth,960),n=Yl(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function Gl(){Xl(),fe(),window.addEventListener("touchstart",Wl,jl),window.addEventListener("touchmove",Hl,jl),window.addEventListener("touchend",Vl,jl),window.addEventListener("touchcancel",Vl,jl)}function Jl(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ql,Kl,Zl=["top","left","right","bottom"],ec={};function tc(){return Kl="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function nc(){if(Kl="string"==typeof Kl?Kl:tc()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(kC){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),Zl.forEach((function(e){a(o,e)})),document.body.appendChild(o),i(),Ql=!0}else Zl.forEach((function(e){ec[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function a(e,n){var o=document.createElement("div"),a=document.createElement("div"),s=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Kl+"(safe-area-inset-"+n+")"};r(o,c),r(a,c),r(s,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(s),a.appendChild(l),e.appendChild(o),e.appendChild(a),i((function(){o.scrollTop=a.scrollTop=1e4;var e=o.scrollTop,r=a.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=a.scrollTop=1e4,e=o.scrollTop,r=a.scrollTop,function(e){rc.length||setTimeout((function(){var e={};rc.forEach((function(t){e[t]=ec[t]})),rc.length=0,ic.forEach((function(t){t(e)}))}),0);rc.push(e)}(n))}o.addEventListener("scroll",i,t),a.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(ec,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function oc(e){return Ql||nc(),ec[e]}var rc=[];var ic=[];const ac=Jl({get support(){return 0!=("string"==typeof Kl?Kl:tc()).length},get top(){return oc("top")},get left(){return oc("left")},get right(){return oc("right")},get bottom(){return oc("bottom")},onChange:function(e){tc()&&(Ql||nc(),"function"==typeof e&&ic.push(e))},offChange:function(e){var t=ic.indexOf(e);t>=0&&ic.splice(t,1)}}),sc=ba((()=>{}),["prevent"]),lc=ba((()=>{}),["stop"]);function cc(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function uc(){const e=cc(document.documentElement.style,"--window-top");return e?e+ac.top:0}function dc(){const e=document.documentElement.style,t=uc(),n=cc(e,"--window-bottom"),o=cc(e,"--window-left"),r=cc(e,"--window-right"),i=cc(e,"--top-window-height");return{top:t,bottom:n?n+ac.bottom:0,left:o?o+ac.left:0,right:r?r+ac.right:0,topWindowHeight:i||0}}function pc(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function fc(e){return pc(e)}function hc(e){return Symbol(e)}function gc(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function mc(e,t=!1){if(t)return function(e){if(!gc(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>uni.upx2px(parseFloat(t))+"px"))}(e);if(I(e)){const t=parseInt(e)||0;return gc(e)?uni.upx2px(t):t}return e}const vc="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",yc="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z",bc="M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z",_c="M17.25 16.156l7.375-7.313q0.281-0.281 0.281-0.641t-0.281-0.641q-0.25-0.25-0.625-0.25t-0.625 0.25l-7.375 7.344-7.313-7.344q-0.25-0.25-0.625-0.25t-0.625 0.25q-0.281 0.25-0.281 0.625t0.281 0.625l7.313 7.344-7.375 7.344q-0.281 0.25-0.281 0.625t0.281 0.625q0.125 0.125 0.281 0.188t0.344 0.063q0.156 0 0.328-0.063t0.297-0.188l7.375-7.344 7.375 7.406q0.125 0.156 0.297 0.219t0.328 0.063q0.188 0 0.344-0.078t0.281-0.203q0.281-0.25 0.281-0.609t-0.281-0.641l-7.375-7.406z",wc="M31.562 4.9966666659375q0.435 0.399 0.435 0.87 0.036 0.58-0.399 0.98l-18.61 19.917q-0.145 0.145-0.327 0.217-0.073 0.037-0.145 0.11-0.254 0.035-0.472 0.035-0.29 0-0.544-0.036l-0.145-0.072q-0.109-0.073-0.217-0.182l-0.11-0.072L0.363 16.2786666659375q-0.327-0.399-0.363-0.907 0-0.544 0.363-1.016 0.435-0.326 0.961-0.362 0.527-0.036 0.962 0.362l9.722 9.542L29.712 5.0326666659375q0.399-0.363 0.943-0.363 0.544-0.036 0.907 0.327z";function xc(e,t="#000",n=27){return Jr("svg",{width:n,height:n,viewBox:"0 0 32 32"},[Jr("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function Tc(){{const{$pageInstance:e}=li();return e&&e.proxy.$page.id}}function Sc(e){const t=ne(e);if(t.$page)return t.$page.id;if(t.$){const{$pageInstance:e}=t.$;return e&&e.proxy.$page.id}}function kc(){const e=Xm(),t=e.length;if(t)return e[t-1]}function Cc(){const e=kc();if(e)return e.$page.meta}function Ec(){const e=Cc();return e?e.id:-1}function Ac(){const e=kc();if(e)return e.$vm}const Mc=["navigationBar","pullToRefresh"];function Pc(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=x({id:t},n,e);Mc.forEach((t=>{o[t]=x({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function Ic(e,t,n){if(I(e))n=t,t=e,e=Ac();else if("number"==typeof e){const t=Xm().find((t=>t.$page.id===e));e=t?t.$vm:Ac()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function Oc(e){e.preventDefault()}let Lc,$c=0;function Dc({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const a=()=>{function a(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,a=Math.abs(e-$c)>n;return!i||r&&!a?(!i&&r&&(r=!1),!1):($c=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(a()||(Lc=setTimeout(a,300))),o=!1};return function(){clearTimeout(Lc),o||requestAnimationFrame(a),o=!0}}function Rc(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return Rc(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),ae(i.concat(n).join("/"))}function Bc(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}class Nc{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(re(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&re(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=Vc(this.$el.querySelector(e));return t?qc(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=Vc(n[o]);e&&t.push(qc(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||I(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:U(n);(I(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(I(e)&&(e=s(e)),B(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];P(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&Ix.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function qc(e,t=!0){if(t&&e&&(e=oe(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Nc(e)),e.$el.__wxsComponentDescriptor}function jc(e,t){return qc(e,t)}function zc(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>jc(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=oe(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,jc(r,!1)]}}function Vc(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function Fc(e){for(;e&&0!==e.tagName.indexOf("UNI-");)e=e.parentElement;return e}function Uc(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e,a={type:n,timeStamp:o,target:ve(t?r:Fc(r)),detail:{},currentTarget:ve(i)};return e._stopped&&(a._stopped=!0),e.type.startsWith("touch")&&(a.touches=e.touches,a.changedTouches=e.changedTouches),function(e,t){x(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(a,e),a}function Wc(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function Hc(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:a,clientX:s,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:a-t,clientX:s,clientY:l-t,force:c||0})}return n}const Yc=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=0!==o.tagName.indexOf("UNI-");if(r)return zc(e,t,n,!1)||[e];const i=Uc(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=uc();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[Wc(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=uc();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Wc(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=uc();i.touches=Hc(e.touches,t),i.changedTouches=Hc(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(i,t,{get:()=>e[t]})}))}return zc(i,t,n)||[i]},createNativeEvent:Uc},Symbol.toStringTag,{value:"Module"});function Xc(e){!function(e){const t=e.globalProperties;x(t,Yc),t.$gcd=jc}(e._context.config)}let Gc=1;function Jc(e){return(e||Ec())+".invokeViewApi"}const Qc=x(Ll("view"),{invokeOnCallback:(e,t)=>Lx.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=Lx,a=o?Gc++:0;o&&r("invokeViewApi."+a,o,!0),i(Jc(n),{id:a,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:a}=Lx,s=Gc++,l="invokeViewApi."+s;return r(l,n),a(Jc(o),{id:s,name:e,args:t},o),()=>{i(l)}}});function Kc(e){Ic(kc(),"onResize",e),Lx.invokeOnCallback("onWindowResize",e)}function Zc(e){const t=kc();Ic(yv(),"onShow",e),Ic(t,"onShow")}function eu(){Ic(yv(),"onHide"),Ic(kc(),"onHide")}const tu=["onPageScroll","onReachBottom"];function nu(){tu.forEach((e=>Lx.subscribe(e,function(e){return(t,n)=>{Ic(parseInt(n),e,t)}}(e))))}function ou(){!function(){const{on:e}=Lx;e("onResize",Kc),e("onAppEnterForeground",Zc),e("onAppEnterBackground",eu)}(),nu()}function ru(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new Te(this.$page.id)),e.eventChannel}}function iu(e){e._context.config.globalProperties.getOpenerEventChannel=ru}function au(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function su(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${uni.upx2px(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function lu(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],a=t.option.transition,s=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,a=e.option,s=a.transition,l={},c=[];return i.forEach((e=>{let i=e.type,a=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?a=a.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(a=a.map(su)),n.indexOf(i)>=0&&(a.length=1),c.push(`${i}(${a.join(",")})`);else if(o.concat(r).includes(a[0])){i=a[0];const e=a[1];l[i]=r.includes(i)?su(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${s.duration}ms ${s.timingFunction} ${s.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=a.transformOrigin,l}(t);Object.keys(s).forEach((t=>{e.$el.style[t]=s[t]})),n+=1,n<r&&setTimeout(i,a.duration+a.delay)}setTimeout((()=>{i()}),0)}const cu={props:["animation"],watch:{animation:{deep:!0,handler(){lu(this)}}},mounted(){lu(this)}},uu=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(cu),du(e)},du=e=>(e.__reserved=!0,e.compatConfig={MODE:3},co(e)),pu={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function fu(e){const t=nn(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function a(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function s(){r=!1,t.value&&i()}function l(){s(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:function(e){e.touches.length>1||a(e)},onMousedown:function(e){r||(a(e),window.addEventListener("mouseup",l))},onTouchend:function(){s()},onMouseup:function(){r&&l()},onTouchcancel:function(){r=!1,t.value=!1,clearTimeout(n)}}}}function hu(e,t){return I(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}function gu(e){return e.__wwe=!0,e}function mu(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){const r=ve(n);return{type:o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const vu=hc("uf"),yu=uu({name:"Form",emits:["submit","reset"],setup(e,{slots:t,emit:n}){const o=nn(null);return function(e){const t=[];Wn(vu,{addField(e){t.push(e)},removeField(e){t.splice(t.indexOf(e),1)},submit(n){e("submit",n,{value:t.reduce(((e,t)=>{if(t.submit){const[n,o]=t.submit();n&&(e[n]=o)}return e}),Object.create(null))})},reset(n){t.forEach((e=>e.reset&&e.reset())),e("reset",n)}})}(mu(o,n)),()=>Jr("uni-form",{ref:o},[Jr("span",null,[t.default&&t.default()])],512)}});const bu=hc("ul");function _u(e,t){wu(e.id,t),Gn((()=>e.id),((e,n)=>{xu(n,t,!0),wu(e,t,!0)})),Do((()=>{xu(e.id,t)}))}function wu(e,t,n){const o=Tc();n&&!e||B(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&Ix.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?Ix.on(r,t[r]):e&&Ix.on(`uni-${r}-${o}-${e}`,t[r])}))}function xu(e,t,n){const o=Tc();n&&!e||B(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&Ix.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?Ix.off(r,t[r]):e&&Ix.off(`uni-${r}-${o}-${e}`,t[r])}))}const Tu=uu({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=nn(null),o=Hn(vu,!1),{hovering:r,binding:i}=fu(e);_l();const a=gu(((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;})),s=Hn(bu,!1);return s&&(s.addHandler(a),$o((()=>{s.removeHandler(a)}))),_u(e,{"label-click":a}),()=>{const o=e.hoverClass,s=hu(e,"disabled"),l=hu(e,"loading"),c=hu(e,"plain"),u=o&&"none"!==o;return Jr("uni-button",oi({ref:n,onClick:a,class:u&&r.value?o:""},u&&i,s,l,c),[t.default&&t.default()],16,["onClick"])}}});function Su(e){return e.$el}function ku(e){const{base:t}=__uniConfig.router;return 0===ae(e).indexOf(t)?ae(e):t+e}function Cu(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0===e.indexOf("./static/")||n&&0===e.indexOf("./"+n+"/"))&&(e=e.slice(1)),0===e.indexOf("/")){if(0!==e.indexOf("//"))return ku(e.slice(1));e="https:"+e}if(ee.test(e)||te.test(e)||0===e.indexOf("blob:"))return e;const o=Xm();return o.length?ku(Rc(o[o.length-1].$page.route,e).slice(1)):e}const Eu=navigator.userAgent,Au=/android/i.test(Eu),Mu=/iphone|ipad|ipod/i.test(Eu),Pu=Eu.match(/Windows NT ([\d|\d.\d]*)/i),Iu=/Macintosh|Mac/i.test(Eu),Ou=/Linux|X11/i.test(Eu),Lu=Iu&&navigator.maxTouchPoints>0;function $u(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function Du(e){return e&&90===Math.abs(window.orientation)}function Ru(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function Bu(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function Nu(e,t,n,o){Lx.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function qu(e,t){const n={},{top:o,topWindowHeight:r}=dc();if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=he(e)),t.rect||t.size){const i=e.getBoundingClientRect();t.rect&&(n.left=i.left,n.right=i.right,n.top=i.top-o-r,n.bottom=i.bottom-o-r),t.size&&(n.width=i.width,n.height=i.height)}if(C(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){const t=e.children[0].children[0];n.scrollLeft=t.scrollLeft,n.scrollTop=t.scrollTop,n.scrollHeight=t.scrollHeight,n.scrollWidth=t.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(C(t.computedStyle)){const o=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=o[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function ju(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){const t=this.parentElement.querySelectorAll(e);let n=t.length;for(;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}function zu(e,t,n){const o=[];t.forEach((({component:t,selector:n,single:r,fields:i})=>{null===t?o.push(function(e){const t={};if(e.id&&(t.id=""),e.dataset&&(t.dataset={}),e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0),e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight),e.scrollOffset){const e=document.documentElement,n=document.body;t.scrollLeft=e.scrollLeft||n.scrollLeft||0,t.scrollTop=e.scrollTop||n.scrollTop||0,t.scrollHeight=e.scrollHeight||n.scrollHeight||0,t.scrollWidth=e.scrollWidth||n.scrollWidth||0}return t}(i)):o.push(function(e,t,n,o,r){const i=function(e,t){return e?e.$el:t.$el}(t,e),a=i.parentElement;if(!a)return o?null:[];const{nodeType:s}=i,l=3===s||8===s;if(o){const e=l?a.querySelector(n):ju(i,n)?i:i.querySelector(n);return e?qu(e,r):null}{let e=[];const t=(l?a:i).querySelectorAll(n);return t&&t.length&&[].forEach.call(t,(t=>{e.push(qu(t,r))})),!l&&ju(i,n)&&e.unshift(qu(i,r)),e}}(e,t,n,r,i))})),n(o)}var Vu="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Fu=function(){const e=new Uint8Array(256);for(var t=0;t<Vu.length;t++)e[Vu.charCodeAt(t)]=t;return e}();const Uu=["original","compressed"],Wu=["album","camera"],Hu=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function Yu(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function Xu(e,t){return!C(e)||0===e.length||e.find((e=>-1===t.indexOf(e)))?t:e}function Gu(e){return function(){try{return e.apply(e,arguments)}catch(kC){console.error(kC)}}}let Ju=1;const Qu={};function Ku(e,t,n,o=!1){return Qu[e]={name:t,keepAlive:o,callback:n},e}function Zu(e,t,n){if("number"==typeof e){const o=Qu[e];if(o)return o.keepAlive||delete Qu[e],o.callback(t,n)}return t}function ed(e){for(const t in Qu)if(Qu[t].name===e)return!0;return!1}const td="success",nd="fail",od="complete";function rd(e,t={},{beforeAll:n,beforeSuccess:o}={}){B(t)||(t={});const{success:r,fail:i,complete:a}=function(e){const t={};for(const n in e){const o=e[n];P(o)&&(t[n]=Gu(o),delete e[n])}return t}(t),s=P(r),l=P(i),c=P(a),u=Ju++;return Ku(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),P(n)&&n(u),u.errMsg===e+":ok"?(P(o)&&o(u,t),s&&r(u)):l&&i(u),c&&a(u)})),u}const id="success",ad="fail",sd="complete",ld={},cd={};function ud(e,t){return function(n){return e(n,t)||n}}function dd(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(ud(i,n));else{const e=i(t,n);if($(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function pd(e,t={}){return[id,ad,sd].forEach((n=>{const o=e[n];if(!C(o))return;const r=t[n];t[n]=function(e){dd(o,e,t).then((e=>P(r)&&r(e)||e))}})),t}function fd(e,t){const n=[];C(ld.returnValue)&&n.push(...ld.returnValue);const o=cd[e];return o&&C(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function hd(e){const t=Object.create(null);Object.keys(ld).forEach((e=>{"returnValue"!==e&&(t[e]=ld[e].slice())}));const n=cd[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function gd(e,t,n,o){const r=hd(e);if(r&&Object.keys(r).length){if(C(r.invoke)){return dd(r.invoke,n).then((n=>t(pd(hd(e),n),...o)))}return t(pd(r,n),...o)}return t(n,...o)}function md(e,t){return(n={},...o)=>function(e){return!(!B(e)||![td,nd,od].find((t=>P(e[t]))))}(n)?fd(e,gd(e,t,n,o)):fd(e,new Promise(((r,i)=>{gd(e,t,x(n,{success:r,fail:i}),o)})))}function vd(e,t,n,o){return Zu(e,x({errMsg:t+":fail"+(n?" "+n:"")},o))}function yd(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(I(e))return e}const r=function(e,t){const n=e[0];if(!t||!B(t.formatArgs)&&B(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],a=o[t];if(P(a)){const o=a(e[0][t],n);if(I(o))return o}else k(n,t)||(n[t]=a)}}(t,o);if(r)return r}function bd(e){if(!P(e))throw new Error('Invalid args: type check failed for args "callback". Expected Function')}function _d(e,t,n){return o=>{bd(o);const r=yd(0,[o],0,n);if(r)throw new Error(r);const i=!ed(e);!function(e,t){Ku(Ju++,e,t,!0)}(e,o),i&&(!function(e){Lx.on("api."+e,(t=>{for(const n in Qu){const o=Qu[n];o.name===e&&o.callback(t)}}))}(e),t())}}function wd(e,t,n){return o=>{bd(o);const r=yd(0,[o],0,n);if(r)throw new Error(r);!function(e,t){for(const n in Qu){const o=Qu[n];o.callback===t&&o.name===e&&delete Qu[n]}}(e=e.replace("off","on"),o);ed(e)||(!function(e){Lx.off("api."+e)}(e),t())}}function xd(e,t,n,o){return n=>{const r=rd(e,n,o),i=yd(0,[n],0,o);return i?vd(r,e,i):t(n,{resolve:t=>function(e,t,n){return Zu(e,x(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>vd(r,e,function(e){return!e||I(e)?e:e.stack?(console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function Td(e,t,n){return _d(e,t,n)}function Sd(e,t,n){return wd(e,t,n)}function kd(e,t,n,o){return md(e,xd(e,t,0,o))}function Cd(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=yd(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function Ed(e,t,n,o){return md(e,function(e,t,n,o){return xd(e,t,0,o)}(e,t,0,o))}function Ad(e){return`method 'uni.${e}' not supported`}function Md(e){return()=>{console.error(Ad(e))}}const Pd=Md;function Id(e){return(t,{reject:n})=>n(Ad(e))}const Od=Cd(0,(e=>function(e){var t,n,o,r,i,a=.75*e.length,s=e.length,l=0;"="===e[e.length-1]&&(a--,"="===e[e.length-2]&&a--);var c=new ArrayBuffer(a),u=new Uint8Array(c);for(t=0;t<s;t+=4)n=Fu[e.charCodeAt(t)],o=Fu[e.charCodeAt(t+1)],r=Fu[e.charCodeAt(t+2)],i=Fu[e.charCodeAt(t+3)],u[l++]=n<<2|o>>4,u[l++]=(15&o)<<4|r>>2,u[l++]=(3&r)<<6|63&i;return c}(e))),Ld=Cd(0,(e=>function(e){var t,n=new Uint8Array(e),o=n.length,r="";for(t=0;t<o;t+=3)r+=Vu[n[t]>>2],r+=Vu[(3&n[t])<<4|n[t+1]>>4],r+=Vu[(15&n[t+1])<<2|n[t+2]>>6],r+=Vu[63&n[t+2]];return o%3==2?r=r.substring(0,r.length-1)+"=":o%3==1&&(r=r.substring(0,r.length-2)+"=="),r}(e)));let $d=!1,Dd=0,Rd=0,Bd=960,Nd=375,qd=750;function jd(){const{platform:e,pixelRatio:t,windowWidth:n}=function(){const e=$u(),t=Bu(Ru(e,Du(e)));return{platform:Mu?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();Dd=n,Rd=t,$d="ios"===e}function zd(e,t){const n=Number(e);return isNaN(n)?t:n}const Vd=Cd(0,((e,t)=>{if(0===Dd&&(jd(),function(){const e=__uniConfig.globalStyle||{};Bd=zd(e.rpxCalcMaxDeviceWidth,960),Nd=zd(e.rpxCalcBaseDeviceWidth,375),qd=zd(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||Dd;n=e===qd||n<=Bd?n:Nd;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==Rd&&$d?.5:1),e<0?-o:o}));function Fd(e,t){Object.keys(t).forEach((n=>{P(t[n])&&(e[n]=function(e,t){const n=t?e?e.concat(t):C(t)?t:[t]:e;return n?function(e){const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))}))}function Ud(e,t){e&&t&&Object.keys(t).forEach((n=>{const o=e[n],r=t[n];C(o)&&P(r)&&T(o,r)}))}const Wd=Cd(0,((e,t)=>{I(e)&&B(t)?Fd(cd[e]||(cd[e]={}),t):B(e)&&Fd(ld,e)})),Hd=Cd(0,((e,t)=>{I(e)?B(t)?Ud(cd[e],t):delete cd[e]:B(e)&&Ud(ld,e)})),Yd=new Ie,Xd=Cd(0,((e,t)=>(Yd.on(e,t),()=>Yd.off(e,t)))),Gd=Cd(0,((e,t)=>(Yd.once(e,t),()=>Yd.off(e,t)))),Jd=Cd(0,((e,t)=>{e?(C(e)||(e=[e]),e.forEach((e=>Yd.off(e,t)))):Yd.e={}})),Qd=Cd(0,((e,...t)=>{Yd.emit(e,...t)})),Kd=[.5,.8,1,1.25,1.5,2];class Zd{constructor(e,t){this.id=e,this.pageId=t}play(){Nu(this.id,this.pageId,"play")}pause(){Nu(this.id,this.pageId,"pause")}stop(){Nu(this.id,this.pageId,"stop")}seek(e){Nu(this.id,this.pageId,"seek",{position:e})}sendDanmu(e){Nu(this.id,this.pageId,"sendDanmu",e)}playbackRate(e){~Kd.indexOf(e)||(e=1),Nu(this.id,this.pageId,"playbackRate",{rate:e})}requestFullScreen(e={}){Nu(this.id,this.pageId,"requestFullScreen",e)}exitFullScreen(){Nu(this.id,this.pageId,"exitFullScreen")}showStatusBar(){Nu(this.id,this.pageId,"showStatusBar")}hideStatusBar(){Nu(this.id,this.pageId,"hideStatusBar")}}const ep=Cd(0,((e,t)=>new Zd(e,Sc(t||Ac())))),tp=(e,t,n,o)=>{!function(e,t,n,o,r){Lx.invokeViewMethod("map."+e,{type:n,data:o},t,r)}(e,t,n,o,(e=>{o&&((e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)})(o,e)}))};class np{constructor(e,t){this.id=e,this.pageId=t}getCenterLocation(e){tp(this.id,this.pageId,"getCenterLocation",e)}moveToLocation(e){tp(this.id,this.pageId,"moveToLocation",e)}getScale(e){tp(this.id,this.pageId,"getScale",e)}getRegion(e){tp(this.id,this.pageId,"getRegion",e)}includePoints(e){tp(this.id,this.pageId,"includePoints",e)}translateMarker(e){tp(this.id,this.pageId,"translateMarker",e)}$getAppMap(){}addCustomLayer(e){tp(this.id,this.pageId,"addCustomLayer",e)}removeCustomLayer(e){tp(this.id,this.pageId,"removeCustomLayer",e)}addGroundOverlay(e){tp(this.id,this.pageId,"addGroundOverlay",e)}removeGroundOverlay(e){tp(this.id,this.pageId,"removeGroundOverlay",e)}updateGroundOverlay(e){tp(this.id,this.pageId,"updateGroundOverlay",e)}initMarkerCluster(e){tp(this.id,this.pageId,"initMarkerCluster",e)}addMarkers(e){tp(this.id,this.pageId,"addMarkers",e)}removeMarkers(e){tp(this.id,this.pageId,"removeMarkers",e)}moveAlong(e){tp(this.id,this.pageId,"moveAlong",e)}setLocMarkerIcon(e){tp(this.id,this.pageId,"setLocMarkerIcon",e)}openMapApp(e){tp(this.id,this.pageId,"openMapApp",e)}on(e,t){tp(this.id,this.pageId,"on",{name:e,callback:t})}}const op=Cd(0,((e,t)=>new np(e,Sc(t||Ac()))));function rp(e,t){return function(n,o){n?o[e]=Math.round(n):void 0!==t&&(o[e]=t)}}const ip=rp("width"),ap=rp("height"),sp={formatArgs:{x:rp("x"),y:rp("y"),width:ip,height:ap}},lp={canvasId:{type:String,required:!0},x:{type:Number,required:!0},y:{type:Number,required:!0},width:{type:Number,required:!0},height:{type:Number,required:!0}},cp=sp,up=(Uint8ClampedArray,{PNG:"png",JPG:"jpg",JPEG:"jpg"}),dp={formatArgs:{x:rp("x",0),y:rp("y",0),width:ip,height:ap,destWidth:rp("destWidth"),destHeight:rp("destHeight"),fileType(e,t){e=(e||"").toUpperCase();let n=up[e];n||(n=up.PNG),t.fileType=n},quality(e,t){t.quality=e&&e>0&&e<1?e:1}}};function pp(e,t,n,o,r){Lx.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,(e=>{r&&r(e)}))}var fp=["scale","rotate","translate","setTransform","transform"],hp=["drawImage","fillText","fill","stroke","fillRect","strokeRect","clearRect","strokeText"],gp=["setFillStyle","setTextAlign","setStrokeStyle","setGlobalAlpha","setShadow","setFontSize","setLineCap","setLineJoin","setLineWidth","setMiterLimit","setTextBaseline","setLineDash"];const mp={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function vp(e){var t=null;if(null!=(t=/^#([0-9|A-F|a-f]{6})$/.exec(e=e||"#000000"))){return[parseInt(t[1].slice(0,2),16),parseInt(t[1].slice(2,4),16),parseInt(t[1].slice(4),16),255]}if(null!=(t=/^#([0-9|A-F|a-f]{3})$/.exec(e))){let e=t[1].slice(0,1),n=t[1].slice(1,2),o=t[1].slice(2,3);return e=parseInt(e+e,16),n=parseInt(n+n,16),o=parseInt(o+o,16),[e,n,o,255]}if(null!=(t=/^rgb\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e){return Math.min(255,parseInt(e.trim()))})).concat(255);if(null!=(t=/^rgba\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e,t){return 3===t?Math.floor(255*parseFloat(e.trim())):Math.min(255,parseInt(e.trim()))}));var n=e.toLowerCase();if(k(mp,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(mp[n]);const e=parseInt(t[1].slice(0,2),16),o=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4,6),16);let i=parseInt(t[1].slice(6,8),16);return i=i>=0?i:255,[e,o,r,i]}return console.error("unsupported color:"+e),[0,0,0,255]}class yp{constructor(e,t){this.type=e,this.data=t,this.colorStop=[]}addColorStop(e,t){this.colorStop.push([e,vp(t)])}}class bp{constructor(e,t){this.type="pattern",this.data=e,this.colorStop=t}}class _p{constructor(e){this.width=e}}class wp{constructor(e,t){this.id=e,this.pageId=t,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}draw(e=!1,t){var n=[...this.actions];this.actions=[],this.path=[],pp(this.id,this.pageId,"actionsChanged",{actions:n,reserve:e},t)}createLinearGradient(e,t,n,o){return new yp("linear",[e,t,n,o])}createCircularGradient(e,t,n){return new yp("radial",[e,t,n])}createPattern(e,t){if(void 0===t)console.error("Failed to execute 'createPattern' on 'CanvasContext': 2 arguments required, but only 1 present.");else{if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(t)<0))return new bp(e,t);console.error("Failed to execute 'createPattern' on 'CanvasContext': The provided type ('"+t+"') is not one of 'repeat', 'no-repeat', 'repeat-x', or 'repeat-y'.")}}measureText(e){let t=0;return t=function(e,t){const n=document.createElement("canvas").getContext("2d");return n.font=t,n.measureText(e).width||0}(e,this.state.font),new _p(t)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(e,t){this.path.push({method:"moveTo",data:[e,t]}),this.subpath=[[e,t]]}lineTo(e,t){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[e,t]}):this.path.push({method:"lineTo",data:[e,t]}),this.subpath.push([e,t])}quadraticCurveTo(e,t,n,o){this.path.push({method:"quadraticCurveTo",data:[e,t,n,o]}),this.subpath.push([n,o])}bezierCurveTo(e,t,n,o,r,i){this.path.push({method:"bezierCurveTo",data:[e,t,n,o,r,i]}),this.subpath.push([r,i])}arc(e,t,n,o,r,i=!1){this.path.push({method:"arc",data:[e,t,n,o,r,i]}),this.subpath.push([e,t])}rect(e,t,n,o){this.path.push({method:"rect",data:[e,t,n,o]}),this.subpath=[[e,t]]}arcTo(e,t,n,o,r){this.path.push({method:"arcTo",data:[e,t,n,o,r]}),this.subpath.push([n,o])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var e=[...this.actions];return this.clearActions(),e}set lineDashOffset(e){this.actions.push({method:"setLineDashOffset",data:[e]})}set globalCompositeOperation(e){this.actions.push({method:"setGlobalCompositeOperation",data:[e]})}set shadowBlur(e){this.actions.push({method:"setShadowBlur",data:[e]})}set shadowColor(e){this.actions.push({method:"setShadowColor",data:[e]})}set shadowOffsetX(e){this.actions.push({method:"setShadowOffsetX",data:[e]})}set shadowOffsetY(e){this.actions.push({method:"setShadowOffsetY",data:[e]})}set font(e){var t=this;this.state.font=e;var n=e.match(/^(([\w\-]+\s)*)(\d+r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var o=n[1].trim().split(/\s/),r=parseFloat(n[3]),i=n[7],a=[];o.forEach((function(e,n){["italic","oblique","normal"].indexOf(e)>-1?(a.push({method:"setFontStyle",data:[e]}),t.state.fontStyle=e):["bold","normal"].indexOf(e)>-1?(a.push({method:"setFontWeight",data:[e]}),t.state.fontWeight=e):0===n?(a.push({method:"setFontStyle",data:["normal"]}),t.state.fontStyle="normal"):1===n&&s()})),1===o.length&&s(),o=a.map((function(e){return e.data[0]})).join(" "),this.state.fontSize=r,this.state.fontFamily=i,this.actions.push({method:"setFont",data:[`${o} ${r}px ${i}`]})}else console.warn("Failed to set 'font' on 'CanvasContext': invalid format.");function s(){a.push({method:"setFontWeight",data:["normal"]}),t.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(e){this.setFillStyle(e)}set strokeStyle(e){this.setStrokeStyle(e)}set globalAlpha(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:"setGlobalAlpha",data:[e]})}set textAlign(e){this.actions.push({method:"setTextAlign",data:[e]})}set lineCap(e){this.actions.push({method:"setLineCap",data:[e]})}set lineJoin(e){this.actions.push({method:"setLineJoin",data:[e]})}set lineWidth(e){this.actions.push({method:"setLineWidth",data:[e]})}set miterLimit(e){this.actions.push({method:"setMiterLimit",data:[e]})}set textBaseline(e){this.actions.push({method:"setTextBaseline",data:[e]})}}const xp=le((()=>{[...fp,...hp].forEach((function(e){wp.prototype[e]=function(e){switch(e){case"fill":case"stroke":return function(){this.actions.push({method:e+"Path",data:[...this.path]})};case"fillRect":return function(e,t,n,o){this.actions.push({method:"fillPath",data:[{method:"rect",data:[e,t,n,o]}]})};case"strokeRect":return function(e,t,n,o){this.actions.push({method:"strokePath",data:[{method:"rect",data:[e,t,n,o]}]})};case"fillText":case"strokeText":return function(t,n,o,r){var i=[t.toString(),n,o];"number"==typeof r&&i.push(r),this.actions.push({method:e,data:i})};case"drawImage":return function(t,n,o,r,i,a,s,l,c){var u;function d(e){return"number"==typeof e}void 0===c&&(a=n,s=o,l=r,c=i,n=void 0,o=void 0,r=void 0,i=void 0),u=d(n)&&d(o)&&d(r)&&d(i)?[t,a,s,l,c,n,o,r,i]:d(l)&&d(c)?[t,a,s,l,c]:[t,a,s],this.actions.push({method:e,data:u})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)})),gp.forEach((function(e){wp.prototype[e]=function(e){switch(e){case"setFillStyle":case"setStrokeStyle":return function(t){"object"!=typeof t?this.actions.push({method:e,data:["normal",vp(t)]}):this.actions.push({method:e,data:[t.type,t.data,t.colorStop]})};case"setGlobalAlpha":return function(t){t=Math.floor(255*parseFloat(t)),this.actions.push({method:e,data:[t]})};case"setShadow":return function(t,n,o,r){r=vp(r),this.actions.push({method:e,data:[t,n,o,r]}),this.state.shadowBlur=o,this.state.shadowColor=r,this.state.shadowOffsetX=t,this.state.shadowOffsetY=n};case"setLineDash":return function(t,n){t=t||[0,0],n=n||0,this.actions.push({method:e,data:[t,n]}),this.state.lineDash=t};case"setFontSize":return function(t){this.state.font=this.state.font.replace(/\d+\.?\d*px/,t+"px"),this.state.fontSize=t,this.actions.push({method:e,data:[t]})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)}))})),Tp=Cd(0,((e,t)=>{if(xp(),t)return new wp(e,Sc(t));const n=Sc(Ac());if(n)return new wp(e,n);Lx.emit("onError","createCanvasContext:fail")})),Sp=Ed("canvasGetImageData",(({canvasId:e,x:t,y:n,width:o,height:r},{resolve:i,reject:a})=>{const s=Sc(Ac());s?pp(e,s,"getImageData",{x:t,y:n,width:o,height:r},(function(e){if(e.errMsg&&-1!==e.errMsg.indexOf("fail"))return void a("",e);let t=e.data;t&&t.length&&(e.data=new Uint8ClampedArray(t)),delete e.compressed,i(e)})):a()}),0,sp),kp=Ed("canvasPutImageData",(({canvasId:e,data:t,x:n,y:o,width:r,height:i},{resolve:a,reject:s})=>{var l=Sc(Ac());if(!l)return void s();t=Array.prototype.slice.call(t),pp(e,l,"putImageData",{data:t,x:n,y:o,width:r,height:i,compressed:void 0},(e=>{e.errMsg&&-1!==e.errMsg.indexOf("fail")?s():a(e)}))}),0,cp),Cp=Ed("canvasToTempFilePath",(({x:e=0,y:t=0,width:n,height:o,destWidth:r,destHeight:i,canvasId:a,fileType:s,quality:l},{resolve:c,reject:u})=>{var d=Sc(Ac());if(!d)return void u();pp(a,d,"toTempFilePath",{x:e,y:t,width:n,height:o,destWidth:r,destHeight:i,fileType:s,quality:l,dirname:`${yh}/canvas`},(e=>{e.errMsg&&-1!==e.errMsg.indexOf("fail")?u("",e):c(e)}))}),0,dp),Ep=["onCanplay","onPlay","onPause","onStop","onEnded","onTimeUpdate","onError","onWaiting","onSeeking","onSeeked"],Ap=["offCanplay","offPlay","offPause","offStop","offEnded","offTimeUpdate","offError","offWaiting","offSeeking","offSeeked"],Mp={thresholds:[0],initialRatio:0,observeAll:!1},Pp=["top","right","bottom","left"];let Ip=1;function Op(e={}){return Pp.map((t=>`${Number(e[t])||0}px`)).join(" ")}class Lp{constructor(e,t){this._pageId=Sc(e),this._component=e,this._options=x({},Mp,t)}relativeTo(e,t){return this._options.relativeToSelector=e,this._options.rootMargin=Op(t),this}relativeToViewport(e){return this._options.relativeToSelector=void 0,this._options.rootMargin=Op(e),this}observe(e,t){P(t)&&(this._options.selector=e,this._reqId=Ip++,function({reqId:e,component:t,options:n,callback:o},r){const i=Su(t);(i.__io||(i.__io={}))[e]=function(e,t,n){!function(){if("object"!=typeof window)return;if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)return void("isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}}));function e(e){try{return e.defaultView&&e.defaultView.frameElement||null}catch(kC){return null}}var t=function(t){for(var n=window.document,o=e(n);o;)o=e(n=o.ownerDocument);return n}(),n=[],o=null,r=null;function i(e){this.time=e.time,this.target=e.target,this.rootBounds=h(e.rootBounds),this.boundingClientRect=h(e.boundingClientRect),this.intersectionRect=h(e.intersectionRect||f()),this.isIntersecting=!!e.intersectionRect;var t=this.boundingClientRect,n=t.width*t.height,o=this.intersectionRect,r=o.width*o.height;this.intersectionRatio=n?Number((r/n).toFixed(4)):this.isIntersecting?1:0}function a(e,t){var n=t||{};if("function"!=typeof e)throw new Error("callback must be a function");if(n.root&&1!=n.root.nodeType&&9!=n.root.nodeType)throw new Error("root must be a Document or Element");this._checkForIntersections=l(this._checkForIntersections.bind(this),this.THROTTLE_TIMEOUT),this._callback=e,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(n.rootMargin),this.thresholds=this._initThresholds(n.threshold),this.root=n.root||null,this.rootMargin=this._rootMarginValues.map((function(e){return e.value+e.unit})).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}function s(){return window.performance&&performance.now&&performance.now()}function l(e,t){var n=null;return function(){n||(n=setTimeout((function(){e(),n=null}),t))}}function c(e,t,n,o){"function"==typeof e.addEventListener?e.addEventListener(t,n,o||!1):"function"==typeof e.attachEvent&&e.attachEvent("on"+t,n)}function u(e,t,n,o){"function"==typeof e.removeEventListener?e.removeEventListener(t,n,o||!1):"function"==typeof e.detatchEvent&&e.detatchEvent("on"+t,n)}function d(e,t){var n=Math.max(e.top,t.top),o=Math.min(e.bottom,t.bottom),r=Math.max(e.left,t.left),i=Math.min(e.right,t.right),a=i-r,s=o-n;return a>=0&&s>=0&&{top:n,bottom:o,left:r,right:i,width:a,height:s}||null}function p(e){var t;try{t=e.getBoundingClientRect()}catch(n){}return t?(t.width&&t.height||(t={top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:t.right-t.left,height:t.bottom-t.top}),t):f()}function f(){return{top:0,bottom:0,left:0,right:0,width:0,height:0}}function h(e){return!e||"x"in e?e:{top:e.top,y:e.top,bottom:e.bottom,left:e.left,x:e.left,right:e.right,width:e.width,height:e.height}}function g(e,t){var n=t.top-e.top,o=t.left-e.left;return{top:n,left:o,height:t.height,width:t.width,bottom:n+t.height,right:o+t.width}}function m(e,t){for(var n=t;n;){if(n==e)return!0;n=v(n)}return!1}function v(n){var o=n.parentNode;return 9==n.nodeType&&n!=t?e(n):(o&&o.assignedSlot&&(o=o.assignedSlot.parentNode),o&&11==o.nodeType&&o.host?o.host:o)}function y(e){return e&&9===e.nodeType}a.prototype.THROTTLE_TIMEOUT=100,a.prototype.POLL_INTERVAL=null,a.prototype.USE_MUTATION_OBSERVER=!0,a._setupCrossOriginUpdater=function(){return o||(o=function(e,t){r=e&&t?g(e,t):f(),n.forEach((function(e){e._checkForIntersections()}))}),o},a._resetCrossOriginUpdater=function(){o=null,r=null},a.prototype.observe=function(e){if(!this._observationTargets.some((function(t){return t.element==e}))){if(!e||1!=e.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:e,entry:null}),this._monitorIntersections(e.ownerDocument),this._checkForIntersections()}},a.prototype.unobserve=function(e){this._observationTargets=this._observationTargets.filter((function(t){return t.element!=e})),this._unmonitorIntersections(e.ownerDocument),0==this._observationTargets.length&&this._unregisterInstance()},a.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},a.prototype.takeRecords=function(){var e=this._queuedEntries.slice();return this._queuedEntries=[],e},a.prototype._initThresholds=function(e){var t=e||[0];return Array.isArray(t)||(t=[t]),t.sort().filter((function(e,t,n){if("number"!=typeof e||isNaN(e)||e<0||e>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return e!==n[t-1]}))},a.prototype._parseRootMargin=function(e){var t=(e||"0px").split(/\s+/).map((function(e){var t=/^(-?\d*\.?\d+)(px|%)$/.exec(e);if(!t)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(t[1]),unit:t[2]}}));return t[1]=t[1]||t[0],t[2]=t[2]||t[0],t[3]=t[3]||t[1],t},a.prototype._monitorIntersections=function(n){var o=n.defaultView;if(o&&-1==this._monitoringDocuments.indexOf(n)){var r=this._checkForIntersections,i=null,a=null;this.POLL_INTERVAL?i=o.setInterval(r,this.POLL_INTERVAL):(c(o,"resize",r,!0),c(n,"scroll",r,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in o&&(a=new o.MutationObserver(r)).observe(n,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this._monitoringDocuments.push(n),this._monitoringUnsubscribes.push((function(){var e=n.defaultView;e&&(i&&e.clearInterval(i),u(e,"resize",r,!0)),u(n,"scroll",r,!0),a&&a.disconnect()}));var s=this.root&&(this.root.ownerDocument||this.root)||t;if(n!=s){var l=e(n);l&&this._monitorIntersections(l.ownerDocument)}}},a.prototype._unmonitorIntersections=function(n){var o=this._monitoringDocuments.indexOf(n);if(-1!=o){var r=this.root&&(this.root.ownerDocument||this.root)||t;if(!this._observationTargets.some((function(t){var o=t.element.ownerDocument;if(o==n)return!0;for(;o&&o!=r;){var i=e(o);if((o=i&&i.ownerDocument)==n)return!0}return!1}))){var i=this._monitoringUnsubscribes[o];if(this._monitoringDocuments.splice(o,1),this._monitoringUnsubscribes.splice(o,1),i(),n!=r){var a=e(n);a&&this._unmonitorIntersections(a.ownerDocument)}}}},a.prototype._unmonitorAllIntersections=function(){var e=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0,this._monitoringUnsubscribes.length=0;for(var t=0;t<e.length;t++)e[t]()},a.prototype._checkForIntersections=function(){if(this.root||!o||r){var e=this._rootIsInDom(),t=e?this._getRootRect():f();this._observationTargets.forEach((function(n){var r=n.element,a=p(r),l=this._rootContainsTarget(r),c=n.entry,u=e&&l&&this._computeTargetAndRootIntersection(r,a,t),d=null;this._rootContainsTarget(r)?o&&!this.root||(d=t):d=f();var h=n.entry=new i({time:s(),target:r,boundingClientRect:a,rootBounds:d,intersectionRect:u});c?e&&l?this._hasCrossedThreshold(c,h)&&this._queuedEntries.push(h):c&&c.isIntersecting&&this._queuedEntries.push(h):this._queuedEntries.push(h)}),this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)}},a.prototype._computeTargetAndRootIntersection=function(e,n,i){if("none"!=window.getComputedStyle(e).display){for(var a=n,s=v(e),l=!1;!l&&s;){var c=null,u=1==s.nodeType?window.getComputedStyle(s):{};if("none"==u.display)return null;if(s==this.root||9==s.nodeType)if(l=!0,s==this.root||s==t)o&&!this.root?!r||0==r.width&&0==r.height?(s=null,c=null,a=null):c=r:c=i;else{var f=v(s),h=f&&p(f),m=f&&this._computeTargetAndRootIntersection(f,h,i);h&&m?(s=f,c=g(h,m)):(s=null,a=null)}else{var y=s.ownerDocument;s!=y.body&&s!=y.documentElement&&"visible"!=u.overflow&&(c=p(s))}if(c&&(a=d(c,a)),!a)break;s=s&&v(s)}return a}},a.prototype._getRootRect=function(){var e;if(this.root&&!y(this.root))e=p(this.root);else{var n=y(this.root)?this.root:t,o=n.documentElement,r=n.body;e={top:0,left:0,right:o.clientWidth||r.clientWidth,width:o.clientWidth||r.clientWidth,bottom:o.clientHeight||r.clientHeight,height:o.clientHeight||r.clientHeight}}return this._expandRectByRootMargin(e)},a.prototype._expandRectByRootMargin=function(e){var t=this._rootMarginValues.map((function(t,n){return"px"==t.unit?t.value:t.value*(n%2?e.width:e.height)/100})),n={top:e.top-t[0],right:e.right+t[1],bottom:e.bottom+t[2],left:e.left-t[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},a.prototype._hasCrossedThreshold=function(e,t){var n=e&&e.isIntersecting?e.intersectionRatio||0:-1,o=t.isIntersecting?t.intersectionRatio||0:-1;if(n!==o)for(var r=0;r<this.thresholds.length;r++){var i=this.thresholds[r];if(i==n||i==o||i<n!=i<o)return!0}},a.prototype._rootIsInDom=function(){return!this.root||m(t,this.root)},a.prototype._rootContainsTarget=function(e){var n=this.root&&(this.root.ownerDocument||this.root)||t;return m(n,e)&&(!this.root||n==e.ownerDocument)},a.prototype._registerInstance=function(){n.indexOf(this)<0&&n.push(this)},a.prototype._unregisterInstance=function(){var e=n.indexOf(this);-1!=e&&n.splice(e,1)},window.IntersectionObserver=a,window.IntersectionObserverEntry=i}();const o=t.relativeToSelector?e.querySelector(t.relativeToSelector):null,r=new IntersectionObserver((e=>{e.forEach((e=>{n({intersectionRatio:hh(e),intersectionRect:fh(e.intersectionRect),boundingClientRect:fh(e.boundingClientRect),relativeRect:fh(e.rootBounds),time:Date.now(),dataset:he(e.target),id:e.target.id})}))}),{root:o,rootMargin:t.rootMargin,threshold:t.thresholds});if(t.observeAll){r.USE_MUTATION_OBSERVER=!0;const n=e.querySelectorAll(t.selector);for(let e=0;e<n.length;e++)r.observe(n[e])}else{r.USE_MUTATION_OBSERVER=!1;const n=e.querySelector(t.selector);n?r.observe(n):console.warn(`Node ${t.selector} is not found. Intersection observer will not trigger.`)}return r}(i,n,o)}({reqId:this._reqId,component:this._component,options:this._options,callback:t},this._pageId))}disconnect(){this._reqId&&function({reqId:e,component:t},n){const o=Su(t),r=o.__io&&o.__io[e];r&&(r.disconnect(),delete o.__io[e])}({reqId:this._reqId,component:this._component},this._pageId)}}const $p=Cd(0,((e,t)=>((e=ne(e))&&!Sc(e)&&(t=e,e=null),new Lp(e||Ac(),t))));let Dp=1;class Rp{constructor(e){this._pageId=e.$page&&e.$page.id,this._component=e}observe(e,t){P(t)&&(this._reqId=Dp++,function({reqId:e,component:t,options:n,callback:o},r){const i=gh[e]=window.matchMedia(function(e){const t=[],n=["width","minWidth","maxWidth","height","minHeight","maxHeight","orientation"];for(const o of n)"orientation"!==o&&e[o]&&Number(e[o]>=0)&&t.push(`(${vh(o)}: ${Number(e[o])}px)`),"orientation"===o&&e[o]&&t.push(`(${vh(o)}: ${e[o]})`);return t.join(" and ")}(n)),a=mh[e]=e=>o(e.matches);a(i),i.addListener(a)}({reqId:this._reqId,component:this._component,options:e,callback:t},this._pageId))}disconnect(){this._reqId&&function({reqId:e,component:t},n){const o=mh[e],r=gh[e];r&&(r.removeListener(o),delete mh[e],delete gh[e])}({reqId:this._reqId,component:this._component},this._pageId)}}const Bp=Cd(0,(e=>((e=ne(e))&&!Sc(e)&&(e=null),new Rp(e||Ac()))));let Np=0,qp={};const jp={canvas:wp,map:np,video:Zd,editor:class{constructor(e,t){this.id=e,this.pageId=t}format(e,t){this._exec("format",{name:e,value:t})}insertDivider(){this._exec("insertDivider")}insertImage(e){this._exec("insertImage",e)}insertText(e){this._exec("insertText",e)}setContents(e){this._exec("setContents",e)}getContents(e){this._exec("getContents",e)}clear(e){this._exec("clear",e)}removeFormat(e){this._exec("removeFormat",e)}undo(e){this._exec("undo",e)}redo(e){this._exec("redo",e)}blur(e){this._exec("blur",e)}getSelectionText(e){this._exec("getSelectionText",e)}scrollIntoView(e){this._exec("scrollIntoView",e)}_exec(e,t){!function(e,t,n,o){const r={options:o},i=o&&("success"in o||"fail"in o||"complete"in o);if(i){const e=String(Np++);r.callbackId=e,qp[e]=o}Lx.invokeViewMethod(`editor.${e}`,{type:n,data:r},t,(({callbackId:e,data:t})=>{i&&(de(qp[e],t),delete qp[e])}))}(this.id,this.pageId,e,t)}}};function zp(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,r=jp[n];e.context=new r(t,o),delete e.contextInfo}}class Vp{constructor(e,t,n,o){this._selectorQuery=e,this._component=t,this._selector=n,this._single=o}boundingClientRect(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},e),this._selectorQuery}fields(e,t){return this._selectorQuery._push(this._selector,this._component,this._single,e,t),this._selectorQuery}scrollOffset(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},e),this._selectorQuery}context(e){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},e),this._selectorQuery}node(e){return this._selectorQuery}}class Fp{constructor(e){this._component=void 0,this._page=e,this._queue=[],this._queueCb=[]}exec(e){return zu(this._page,this._queue,(t=>{const n=this._queueCb;t.forEach(((e,t)=>{C(e)?e.forEach(zp):zp(e);const o=n[t];P(o)&&o.call(this,e)})),P(e)&&e.call(this,t)})),this._nodesRef}in(e){return this._component=ne(e),this}select(e){return this._nodesRef=new Vp(this,this._component,e,!0)}selectAll(e){return this._nodesRef=new Vp(this,this._component,e,!1)}selectViewport(){return this._nodesRef=new Vp(this,null,"",!0)}_push(e,t,n,o,r){this._queue.push({component:t,selector:e,single:n,fields:o}),this._queueCb.push(r)}}const Up=Cd(0,(e=>((e=ne(e))&&!Sc(e)&&(e=null),new Fp(e||Ac())))),Wp={formatArgs:{}},Hp={duration:400,timingFunction:"linear",delay:0,transformOrigin:"50% 50% 0"};class Yp{constructor(e){this.actions=[],this.currentTransform={},this.currentStepAnimates=[],this.option=x({},Hp,e)}_getOption(e){const t={transition:x({},this.option,e),transformOrigin:""};return t.transformOrigin=t.transition.transformOrigin,delete t.transition.transformOrigin,t}_pushAnimates(e,t){this.currentStepAnimates.push({type:e,args:t})}_converType(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}_getValue(e){return"number"==typeof e?`${e}px`:e}export(){const e=this.actions;return this.actions=[],{actions:e}}step(e){return this.currentStepAnimates.forEach((e=>{"style"!==e.type?this.currentTransform[e.type]=e:this.currentTransform[`${e.type}.${e.args[0]}`]=e})),this.actions.push({animates:Object.values(this.currentTransform),option:this._getOption(e)}),this.currentStepAnimates=[],this}}const Xp=le((()=>{const e=["opacity","backgroundColor"],t=["width","height","left","right","top","bottom"];["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"].concat(e,t).forEach((n=>{Yp.prototype[n]=function(...o){return e.concat(t).includes(n)?this._pushAnimates("style",[this._converType(n),t.includes(n)?this._getValue(o[0]):o[0]]):this._pushAnimates(n,o),this}}))})),Gp=Cd(0,(e=>(Xp(),new Yp(e))),0,Wp),Jp=Td("onTabBarMidButtonTap",(()=>{})),Qp=Td("onWindowResize",(()=>{})),Kp=Sd("offWindowResize",(()=>{})),Zp=Cd(0,(()=>{const e=yv();return e&&e.$vm?e.$vm.$locale:_l().getLocale()})),ef=Td("onLocaleChange",(()=>{})),tf=Cd(0,(e=>{const t=yv();if(!t)return!1;return t.$vm.$locale!==e&&(t.$vm.$locale=e,navigator.cookieEnabled&&window.localStorage&&(localStorage.UNI_LOCALE=e),Lx.invokeOnCallback("onLocaleChange",{locale:e}),!0)})),nf=Ed("setPageMeta",((e,{resolve:t})=>{t(function(e,{pageStyle:t,rootFontSize:n}){t&&(document.querySelector("uni-page-body")||document.body).setAttribute("style",t);n&&document.documentElement.style.fontSize!==n&&(document.documentElement.style.fontSize=n)}(Ac(),e))})),of=Ed("getSelectedTextRange",((e,{resolve:t,reject:n})=>{Lx.invokeViewMethod("getSelectedTextRange",{},Ec(),(e=>{void 0===e.end&&void 0===e.start?n("no focused"):t(e)}))})),rf={onUnhandledRejection:[],onPageNotFound:[],onError:[],onShow:[],onHide:[]};function af(e,t){const n=yv();if(n&&n.$vm)return Ao(e,t,n.$vm.$);rf[e].push(t)}function sf(e,t){const n=yv();if(n&&n.$vm)return function(e,t,n){const o=e.$[t];C(o)&&n.__weh&&T(o,n.__weh)}(n.$vm,e,t);T(rf[e],t)}const lf=Cd(0,(()=>Ch())),cf=Cd(0,(()=>x({},Sh)));let uf,df,pf;function ff(e){try{return JSON.parse(e)}catch(kC){}return e}const hf=[];function gf(e,t){hf.forEach((n=>{n(e,t)})),hf.length=0}const mf=Ed("getPushClientId",((e,{resolve:t,reject:n})=>{Promise.resolve().then((()=>{void 0===pf&&(pf=!1,uf="",df="uniPush is not enabled"),hf.push(((e,o)=>{e?t({cid:e}):n(o)})),void 0!==uf&&gf(uf,df)}))})),vf=[],yf={formatArgs:{showToast:!0},beforeInvoke(){Al()},beforeSuccess(e,t){if(!t.showToast)return;const{t:n}=_l(),o=n("uni.setClipboardData.success");o&&uni.showToast({title:o,icon:"success",mask:!1})}},bf=(Boolean,{formatArgs:{filePath(e,t){t.filePath=Cu(e)}}}),_f={formatArgs:{filePath(e,t){t.filePath=Cu(e)}}},wf=["wgs84","gcj02"],xf={formatArgs:{type(e,t){e=(e||"").toLowerCase(),-1===wf.indexOf(e)?t.type=wf[0]:t.type=e},altitude(e,t){t.altitude=e||!1}}},Tf=(Boolean,(e,t)=>{if(void 0===t)return`${e} should not be empty.`;if("number"!=typeof t){let e=typeof t;return e=e[0].toUpperCase()+e.substring(1),`Expected Number, got ${e} with value ${JSON.stringify(t)}.`}}),Sf={formatArgs:{latitude(e,t){const n=Tf("latitude",e);if(n)return n;t.latitude=e},longitude(e,t){const n=Tf("longitude",e);if(n)return n;t.longitude=e},scale(e,t){e=Math.floor(e),t.scale=e>=5&&e<=18?e:18}}},kf={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=Xu(e,Uu)},sourceType(e,t){t.sourceType=Xu(e,Wu)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Cf={formatArgs:{sourceType(e,t){t.sourceType=Xu(e,Wu)},compressed:!0,maxDuration:60,camera:"back",extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Ef=(Boolean,["all","image","video"]),Af={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=100)},sourceType(e,t){t.sourceType=Xu(e,Wu)},type(e,t){t.type=Yu(e,Ef)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=[""])}}},Mf={formatArgs:{src(e,t){t.src=Cu(e)}}},Pf={formatArgs:{urls(e,t){t.urls=e.map((e=>I(e)&&e?Cu(e):""))},current(e,t){"number"==typeof e?t.current=e>0&&e<t.urls.length?e:0:I(e)&&e&&(t.current=Cu(e))}}},If={formatArgs:{src(e,t){t.src=Cu(e)}}},Of="json",Lf=["text","arraybuffer"],$f=encodeURIComponent;ArrayBuffer,Boolean;const Df={formatArgs:{method(e,t){t.method=Yu((e||"").toUpperCase(),Hu)},data(e,t){t.data=e||""},url(e,t){t.method===Hu[0]&&B(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),a={};i.forEach((e=>{const t=e.split("=");a[t[0]]=t[1]}));for(const s in t)if(k(t,s)){let e=t[s];null==e?e="":B(e)&&(e=JSON.stringify(e)),a[$f(s)]=$f(e)}return r=Object.keys(a).map((e=>`${e}=${a[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==Hu[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||Of).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===Lf.indexOf(t.responseType)&&(t.responseType="text")}}},Rf={formatArgs:{header(e,t){t.header=e||{}}}},Bf={formatArgs:{filePath(e,t){e&&(t.filePath=Cu(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}},Nf={formatArgs:{header(e,t){t.header=e||{}},method(e,t){t.method=Yu((e||"").toUpperCase(),Hu)},protocols(e,t){I(e)&&(t.protocols=[e])}}},qf=["wgs84","gcj02"],jf={formatArgs:{type(e,t){e=(e||"").toLowerCase(),-1===qf.indexOf(e)?t.type=qf[1]:t.type=e}}};const zf={url:{type:String,required:!0}},Vf=(Yf(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),Yf(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),Jf("navigateTo")),Ff=Jf("redirectTo"),Uf=Jf("reLaunch"),Wf=Jf("switchTab"),Hf={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(Xm().length-1,e)}}};function Yf(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let Xf;function Gf(){Xf=""}function Jf(e){return{formatArgs:{url:Qf(e)},beforeAll:Gf}}function Qf(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/"))return e;let t="";const n=Xm();return n.length&&(t=n[n.length-1].$page.route),Rc(t,e)}(t)).split("?")[0],r=Bc(o,!0);if(!r)return"page `"+t+"` is not found";if("navigateTo"===e||"redirectTo"===e){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if("switchTab"===e&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if("switchTab"!==e&&"preloadPage"!==e||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!I(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))})),o.length?e+"?"+o.join("&"):e}(t),"unPreloadPage"!==e)if("preloadPage"!==e){if(Xf===t&&"appLaunch"!==n.openType)return`${Xf} locked`;__uniConfig.ready&&(Xf=t)}else if(r.meta.isTabBar){const e=Xm(),t=r.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}const Kf={formatArgs:{animation(e,t){e||(e={duration:0,timingFunc:"linear"}),t.animation={duration:e.duration||0,timingFunc:e.timingFunc||"linear"}}}},Zf={formatArgs:{duration:300}},eh={formatArgs:{itemColor:"#000"}},th=(Boolean,{formatArgs:{title:"",mask:!1}}),nh=(Boolean,{beforeInvoke(){Cl()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!k(t,"cancelText")){const{t:e}=_l();t.cancelText=e("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!k(t,"confirmText")){const{t:e}=_l();t.confirmText=e("uni.showModal.confirm")}},confirmColor:"#007aff"}}),oh=["success","loading","none","error"],rh=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=Yu(e,oh)},image(e,t){t.image=e?Cu(e):""},duration:1500,mask:!1}}),ih={beforeInvoke(){const e=Cc();if(e&&!e.isTabBar)return"not TabBar page"},formatArgs:{index(e){if(!__uniConfig.tabBar.list[e])return"tabbar item not found"}}},ah={beforeInvoke:ih.beforeInvoke,formatArgs:x({pagePath(e,t){e&&(t.pagePath=se(e))}},ih.formatArgs)},sh=/^(linear|radial)-gradient\(.+?\);?$/,lh={beforeInvoke:ih.beforeInvoke,formatArgs:{backgroundImage(e,t){e&&!sh.test(e)&&(t.backgroundImage=Cu(e))},borderStyle(e,t){e&&(t.borderStyle="white"===e?"white":"black")}}},ch=ih,uh=ih,dh=ih,ph={beforeInvoke:ih.beforeInvoke,formatArgs:x({text(e,t){(function(e=""){return(""+e).replace(/[^\x00-\xff]/g,"**").length})(e)>=4&&(t.text="...")}},ih.formatArgs)};function fh(e){const{bottom:t,height:n,left:o,right:r,top:i,width:a}=e||{};return{bottom:t,height:n,left:o,right:r,top:i,width:a}}function hh(e){const{intersectionRatio:t,boundingClientRect:{height:n,width:o},intersectionRect:{height:r,width:i}}=e;return 0!==t?t:r===n?i/o:r/n}let gh={},mh={};function vh(e){return e.replace(/([A-Z])/g,"-$1").toLowerCase()}const yh="",bh={};function _h(e,t){const n=bh[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(function(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",r=atob(t[1]);let i=r.length;const a=new Uint8Array(i);for(;i--;)a[i]=r.charCodeAt(i);return wh(a,o)}(e)):t?Promise.reject(new Error("not find")):new Promise(((t,n)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){t(this.response)},o.onerror=n,o.send()}))}function wh(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const r=`${Date.now()}${function(e){const t=e.split("/")[1];return t?`.${t}`:""}(t)}`;try{n=new File([e],r,{type:t})}catch(o){n=e=e instanceof Blob?e:new Blob([e],{type:t}),n.name=n.name||r}}return n}function xh(e){for(const n in bh)if(k(bh,n)){if(bh[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return bh[t]=e,t}function Th(e){(window.URL||window.webkitURL).revokeObjectURL(e),delete bh[e]}const Sh=au(),kh=au();function Ch(){return x({},kh)}const Eh=uu({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=nn(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=Vt({width:-1,height:-1});return Gn((()=>x({},o)),(e=>t("resize",e))),()=>{const t=e.value;o.width=t.offsetWidth,o.height=t.offsetHeight,n()}}(n,t,o);return function(e,t,n,o){bo(o),Io((()=>{t.initial&&Sn(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,r,o),()=>Jr("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[Jr("div",{onScroll:r},[Jr("div",null,null)],40,["onScroll"]),Jr("div",{onScroll:r},[Jr("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});function Ah(){}const Mh={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function Ph(e,t,n){function o(e){const t=vi((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(undefined),document.addEventListener("click",Ah,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",Ah,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}Gn((()=>t.value),(e=>e&&o(e)))}var Ih=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,Oh=/^<\/([-A-Za-z0-9_]+)[^>]*>/,Lh=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,$h=jh("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),Dh=jh("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),Rh=jh("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),Bh=jh("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),Nh=jh("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),qh=jh("script,style");function jh(e){for(var t={},n=e.split(","),o=0;o<n.length;o++)t[n[o]]=!0;return t}const zh={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},Vh={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},Fh={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},Uh=uu({name:"Image",props:zh,setup(e,{emit:t}){const n=nn(null),o=function(e,t){const n=nn(""),o=vi((()=>{let e="auto",o="";const r=Fh[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),r=Vt({rootEl:e,src:vi((()=>t.src?Cu(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return Io((()=>{const t=e.value.style;r.origWidth=Number(t.width)||0,r.origHeight=Number(t.height)||0})),r}(n,e),r=mu(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=Vh[o];if(!r)return;const{origWidth:i,origHeight:a}=n,s=i&&a?i/a:0;if(!s)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){Wh&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,s))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return Gn((()=>t.mode),((e,t)=>{Vh[t]&&r(),Vh[e]&&o()})),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,a;const s=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void s();i=i||new Image,i.onload=e=>{const{width:u,height:d}=i;s(u,d,l),o(),i.draggable=t.draggable,a&&a.remove(),a=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:d})},i.onerror=t=>{s(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};Gn((()=>e.src),(e=>l(e))),Gn((()=>e.imgSrc),(e=>{!e&&a&&(a.remove(),a=null)})),Io((()=>l(e.src))),$o((()=>c()))}(o,e,n,i,r),()=>Jr("uni-image",{ref:n},[Jr("div",{style:o.modeStyle},null,4),Vh[e.mode]?Jr(Eh,{onResize:i},null,8,["onResize"]):Jr("span",null,null)],512)}});const Wh="Google Inc."===navigator.vendor;const Hh=me(!0),Yh=[];let Xh,Gh=0;const Jh=e=>Yh.forEach((t=>t.userAction=e));function Qh(e={userAction:!1}){if(!Xh){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!Gh&&Jh(!0),Gh++,setTimeout((()=>{!--Gh&&Jh(!1)}),0)}),Hh)})),Xh=!0}Yh.push(e)}const Kh=()=>!!Gh;function Zh(){const e=Vt({userAction:!1});return Io((()=>{Qh(e)})),$o((()=>{!function(e){const t=Yh.indexOf(e);t>=0&&Yh.splice(t,1)}(e)})),{state:e}}function eg(){const e=Vt({attrs:{}});return Io((()=>{let t=li();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}function tg(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}function ng(e,t){return"number"===t&&isNaN(Number(e))&&(e=""),null===e?"":String(e)}const og=["none","text","decimal","numeric","tel","search","email","url"],rg=x({},{name:{type:String,default:""},modelValue:{type:[String,Number],default:""},value:{type:[String,Number],default:""},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~og.indexOf(e)}},Mh),ig=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function ag(e,t,n,o){const r=xe((n=>{t.value=ng(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout});Gn((()=>e.modelValue),r),Gn((()=>e.value),r);const i=function(e,t){let n,o,r=0;const i=function(...i){const a=Date.now();clearTimeout(n),o=()=>{o=null,r=a,e.apply(this,i)},a-r<t?n=setTimeout(o,t-(a-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}(((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return Po((()=>{r.cancel(),i.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function sg(e,t){Zh();const n=vi((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}Gn((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),Io((()=>{n.value&&Sn(o)}))}function lg(e,t,n,o){Bl(Ec(),"getSelectedTextRange",tg);const{fieldRef:r,state:i,trigger:a}=function(e,t,n){const o=nn(null),r=mu(t,n),i=vi((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),a=vi((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),s=vi((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=vi((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t})),c=ng(e.modelValue,e.type)||ng(e.value,e.type),u=Vt({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:a,cursor:s});return Gn((()=>u.focus),(e=>n("update:focus",e))),Gn((()=>u.maxlength),(e=>u.value=u.value.slice(0,e))),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:s}=ag(e,i,n,a);sg(e,r),Ph(0,r);const{state:l}=eg();!function(e,t){const n=Hn(vu,!1);if(!n)return;const o=li(),r={submit(){const n=o.proxy;return[n[e],I(t)?n[t]:t.value]},reset(){I(t)?o.proxy[t]="":t.value=""}};n.addField(r),$o((()=>{n.removeField(r)}))}("name",i),function(e,t,n,o,r,i){function a(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function s(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}Gn([()=>t.selectionStart,()=>t.selectionEnd],a),Gn((()=>t.cursor),s),Gn((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),P(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function d(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),a(),s()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,d(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),d(e)})),c.addEventListener("compositionupdate",d)}))}(r,i,e,a,s,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:a}}const cg=uu({name:"Input",props:x({},rg,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),emits:["confirm",...ig],setup(e,{emit:t}){const n=["text","number","idcard","digit","password","tel"],o=["off","one-time-code"],r=vi((()=>{let t="";switch(e.type){case"text":"search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=~n.includes(e.type)?e.type:"text"}return e.password?"password":t})),i=vi((()=>{const t=o.indexOf(e.textContentType),n=o.indexOf(U(e.textContentType));return o[-1!==t?t:-1!==n?n:0]}));let a,s=nn("");const l=nn(null),{fieldRef:c,state:u,scopedAttrsState:d,fixDisabledColor:p,trigger:f}=lg(e,l,t,((e,t)=>{const n=e.target;if("number"===r.value){if(a&&(n.removeEventListener("blur",a),a=null),n.validity&&!n.validity.valid){if((!s.value||!n.value)&&"-"===e.data||"-"===s.value[0]&&"deleteContentBackward"===e.inputType)return s.value="-",t.value="",a=()=>{s.value=n.value=""},n.addEventListener("blur",a),!1;if(s.value)if(-1!==s.value.indexOf(".")){if("."!==e.data&&"deleteContentBackward"===e.inputType){const e=s.value.indexOf(".");return s.value=n.value=t.value=s.value.slice(0,e),!0}}else if("."===e.data)return s.value+=".",a=()=>{s.value=n.value=s.value.slice(0,-1)},n.addEventListener("blur",a),!1;return s.value=t.value=n.value="-"===s.value?"":s.value,!1}s.value=n.value;const o=t.maxlength;if(o>0&&n.value.length>o)return n.value=n.value.slice(0,o),t.value=n.value,!1}}));Gn((()=>u.value),(t=>{"number"!==e.type||"-"===s.value&&""===t||(s.value=t)}));const h=["number","digit"],g=vi((()=>h.includes(e.type)?e.step:""));function m(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),f("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return()=>{let t=e.disabled&&p?Jr("input",{key:"disabled-input",ref:c,value:u.value,tabindex:"-1",readonly:!!e.disabled,type:r.value,maxlength:u.maxlength,step:g.value,class:"uni-input-input",onFocus:e=>e.target.blur()},null,40,["value","readonly","type","maxlength","step","onFocus"]):jo(Jr("input",{key:"input",ref:c,"onUpdate:modelValue":e=>u.value=e,disabled:!!e.disabled,type:r.value,maxlength:u.maxlength,step:g.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",autocomplete:i.value,onKeyup:m,inputmode:e.inputmode},null,40,["onUpdate:modelValue","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]),[[ga,u.value]]);return Jr("uni-input",{ref:l},[Jr("div",{class:"uni-input-wrapper"},[jo(Jr("div",oi(d.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[_a,!(u.value.length||"-"===s.value)]]),"search"===e.confirmType?Jr("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});const ug=["class","style"],dg=/^on[A-Z]+/,pg=(e={})=>{const{excludeListeners:t=!1,excludeKeys:n=[]}=e,o=li(),r=on({}),i=on({}),a=on({}),s=n.concat(ug);return o.attrs=Vt(o.attrs),Yn((()=>{const e=(n=o.attrs,Object.keys(n).map((e=>[e,n[e]]))).reduce(((e,[n,o])=>(s.includes(n)?e.exclude[n]=o:dg.test(n)?(t||(e.attrs[n]=o),e.listeners[n]=o):e.attrs[n]=o,e)),{exclude:{},attrs:{},listeners:{}});var n;r.value=e.attrs,i.value=e.listeners,a.value=e.exclude})),{$attrs:r,$listeners:i,$excludeAttrs:a}};function fg(e){const t=[];return C(e)&&e.forEach((e=>{Ur(e)?e.type===Or?t.push(...fg(e.children)):t.push(e):C(e)&&t.push(...fg(e))})),t}const hg=uu({inheritAttrs:!1,name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},setup(e,{slots:t}){const n=nn(null),o=nn(!1);let{setContexts:r,events:i}=function(e,t){const n=nn(0),o=nn(0),r=Vt({x:null,y:null}),i=nn(null);let a=null,s=[];function l(t){t&&1!==t&&(e.scaleArea?s.forEach((function(e){e._setScale(t)})):a&&a._setScale(t))}function c(e,n=s){let o=t.value;function r(e){for(let t=0;t<n.length;t++){const o=n[t];if(e===o.rootRef.value)return o}return e===o||e===document.body||e===document?null:r(e.parentNode)}return r(e)}const u=gu((t=>{let n=t.touches;if(n&&n.length>1){let t={x:n[1].pageX-n[0].pageX,y:n[1].pageY-n[0].pageY};if(i.value=gg(t),r.x=t.x,r.y=t.y,!e.scaleArea){let e=c(n[0].target),t=c(n[1].target);a=e&&e===t?e:null}}})),d=gu((e=>{let t=e.touches;if(t&&t.length>1){e.preventDefault();let n={x:t[1].pageX-t[0].pageX,y:t[1].pageY-t[0].pageY};if(null!==r.x&&i.value&&i.value>0){l(gg(n)/i.value)}r.x=n.x,r.y=n.y}})),p=gu((t=>{let n=t.touches;n&&n.length||t.changedTouches&&(r.x=0,r.y=0,i.value=null,e.scaleArea?s.forEach((function(e){e._endScale()})):a&&a._endScale())}));function f(){h(),s.forEach((function(e,t){e.setParent()}))}function h(){let e=window.getComputedStyle(t.value),r=t.value.getBoundingClientRect();n.value=r.width-["Left","Right"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0),o.value=r.height-["Top","Bottom"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0)}return Wn("movableAreaWidth",n),Wn("movableAreaHeight",o),{setContexts(e){s=e},events:{_onTouchstart:u,_onTouchmove:d,_onTouchend:p,_resize:f}}}(e,n);const{$listeners:a,$attrs:s,$excludeAttrs:l}=pg(),c=a.value;["onTouchstart","onTouchmove","onTouchend"].forEach((e=>{let t=c[e],n=i[`_${e}`];c[e]=t?[].concat(t,n):n})),Io((()=>{i._resize(),o.value=!0}));let u=[];const d=[];function p(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n=n.el;const o=d.find((e=>n===e.rootRef.value));o&&e.push(Jt(o))}r(e)}return Wn("_isMounted",o),Wn("movableAreaRootRef",n),Wn("addMovableViewContext",(e=>{d.push(e),p()})),Wn("removeMovableViewContext",(e=>{const t=d.indexOf(e);t>=0&&(d.splice(t,1),p())})),()=>{const e=t.default&&t.default();return u=fg(e),Jr("uni-movable-area",oi({ref:n},s.value,l.value,c),[Jr(Eh,{onResize:i._resize},null,8,["onResize"]),u],16)}}});function gg(e){return Math.sqrt(e.x*e.x+e.y*e.y)}const mg=function(e,t,n,o){e.addEventListener(t,(e=>{P(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};let vg,yg;function bg(e,t,n){$o((()=>{document.removeEventListener("mousemove",vg),document.removeEventListener("mouseup",yg)}));let o=0,r=0,i=0,a=0;const s=function(e,n,s,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:s,y:l,dx:s-o,dy:l-r,ddx:s-i,ddy:l-a,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;mg(e,"touchstart",(function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=i=e.touches[0].pageX,r=a=e.touches[0].pageY,s(e,"start",o,r)})),mg(e,"mousedown",(function(e){if(c=!0,!l&&!u)return u=e,o=i=e.pageX,r=a=e.pageY,s(e,"start",o,r)})),mg(e,"touchmove",(function(e){if(1===e.touches.length&&u){const t=s(e,"move",e.touches[0].pageX,e.touches[0].pageY);return i=e.touches[0].pageX,a=e.touches[0].pageY,t}}));const d=vg=function(e){if(!l&&c&&u){const t=s(e,"move",e.pageX,e.pageY);return i=e.pageX,a=e.pageY,t}};document.addEventListener("mousemove",d),mg(e,"touchend",(function(e){if(0===e.touches.length&&u)return l=!1,u=null,s(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));const p=yg=function(e){if(c=!1,!l&&u)return u=null,s(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",p),mg(e,"touchcancel",(function(e){if(u){l=!1;const t=u;return u=null,s(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}function _g(e,t,n){return e>t-n&&e<t+n}function wg(e,t){return _g(e,0,t)}function xg(){}function Tg(e,t){this._m=e,this._f=1e3*t,this._startTime=0,this._v=0}function Sg(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}function kg(e,t,n){this._springX=new Sg(e,t,n),this._springY=new Sg(e,t,n),this._springScale=new Sg(e,t,n),this._startTime=0}xg.prototype.x=function(e){return Math.sqrt(e)},Tg.prototype.setV=function(e,t){const n=Math.pow(Math.pow(e,2)+Math.pow(t,2),.5);this._x_v=e,this._y_v=t,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(e/this._x_a)||Math.abs(t/this._y_a),this._lastDt=null,this._startTime=(new Date).getTime()},Tg.prototype.setS=function(e,t){this._x_s=e,this._y_s=t},Tg.prototype.s=function(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t,this._lastDt=e);let t=this._x_v*e+.5*this._x_a*Math.pow(e,2)+this._x_s,n=this._y_v*e+.5*this._y_a*Math.pow(e,2)+this._y_s;return(this._x_a>0&&t<this._endPositionX||this._x_a<0&&t>this._endPositionX)&&(t=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:t,y:n}},Tg.prototype.ds=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t),{dx:this._x_v+this._x_a*e,dy:this._y_v+this._y_a*e}},Tg.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},Tg.prototype.dt=function(){return-this._x_v/this._x_a},Tg.prototype.done=function(){const e=_g(this.s().x,this._endPositionX)||_g(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,e},Tg.prototype.setEnd=function(e,t){this._endPositionX=e,this._endPositionY=t},Tg.prototype.reconfigure=function(e,t){this._m=e,this._f=1e3*t},Sg.prototype._solve=function(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,a=t/(r*e);return{x:function(e){return(i+a*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+a*e)*t+a*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),a=(-n+Math.sqrt(i))/(2*o),s=(t-r*e)/(a-r),l=e-s;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*t+s*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*r*t+s*a*n}}}const a=Math.sqrt(4*o*r-n*n)/(2*o),s=-n/2*o,l=e,c=(t-s*e)/a;return{x:function(e){return Math.pow(Math.E,s*e)*(l*Math.cos(a*e)+c*Math.sin(a*e))},dx:function(e){const t=Math.pow(Math.E,s*e),n=Math.cos(a*e),o=Math.sin(a*e);return t*(c*a*n-l*a*o)+s*t*(c*o+l*n)}}},Sg.prototype.x=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0},Sg.prototype.dx=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0},Sg.prototype.setEnd=function(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!wg(t,.1)){t=t||0;let o=this._endPosition;this._solution&&(wg(t,.1)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),wg(t,.1)&&(t=0),wg(o,.1)&&(o=0),o+=this._endPosition),this._solution&&wg(o-e,.1)&&wg(t,.1)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}},Sg.prototype.snap=function(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}},Sg.prototype.done=function(e){return e||(e=(new Date).getTime()),_g(this.x(),this._endPosition,.1)&&wg(this.dx(),.1)},Sg.prototype.reconfigure=function(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},Sg.prototype.springConstant=function(){return this._k},Sg.prototype.damping=function(){return this._c},Sg.prototype.configuration=function(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]},kg.prototype.setEnd=function(e,t,n,o){const r=(new Date).getTime();this._springX.setEnd(e,o,r),this._springY.setEnd(t,o,r),this._springScale.setEnd(n,o,r),this._startTime=r},kg.prototype.x=function(){const e=((new Date).getTime()-this._startTime)/1e3;return{x:this._springX.x(e),y:this._springY.x(e),scale:this._springScale.x(e)}},kg.prototype.done=function(){const e=(new Date).getTime();return this._springX.done(e)&&this._springY.done(e)&&this._springScale.done(e)},kg.prototype.reconfigure=function(e,t,n){this._springX.reconfigure(e,t,n),this._springY.reconfigure(e,t,n),this._springScale.reconfigure(e,t,n)};function Cg(e,t){return+((1e3*e-1e3*t)/1e3).toFixed(1)}const Eg=uu({name:"MovableView",props:{direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.5},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}},emits:["change","scale"],setup(e,{slots:t,emit:n}){const o=nn(null),r=mu(o,n),{setParent:i}=function(e,t,n){const o=Hn("_isMounted",nn(!1)),r=Hn("addMovableViewContext",(()=>{})),i=Hn("removeMovableViewContext",(()=>{}));let a,s,l=nn(1),c=nn(1),u=nn(!1),d=nn(0),p=nn(0),f=null,h=null,g=!1,m=null,v=null;const y=new xg,b=new xg,_={historyX:[0,0],historyY:[0,0],historyT:[0,0]},w=vi((()=>{let t=Number(e.friction);return isNaN(t)||t<=0?2:t})),x=new Tg(1,w.value);Gn((()=>e.disabled),(()=>{W()}));const{_updateOldScale:T,_endScale:S,_setScale:k,scaleValueSync:C,_updateBoundary:E,_updateOffset:A,_updateWH:M,_scaleOffset:P,minX:I,minY:O,maxX:L,maxY:$,FAandSFACancel:D,_getLimitXY:R,_setTransform:B,_revise:N,dampingNumber:q,xMove:j,yMove:z,xSync:V,ySync:F,_STD:U}=function(e,t,n,o,r,i,a,s,l,c){const u=vi((()=>{let t=Number(e.scaleMin);return isNaN(t)?.5:t})),d=vi((()=>{let t=Number(e.scaleMax);return isNaN(t)?10:t})),p=nn(Number(e.scaleValue)||1);Gn(p,(e=>{B(e)})),Gn(u,(()=>{R()})),Gn(d,(()=>{R()})),Gn((()=>e.scaleValue),(e=>{p.value=Number(e)||0}));const{_updateBoundary:f,_updateOffset:h,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:b,maxY:_}=function(e,t,n){const o=Hn("movableAreaWidth",nn(0)),r=Hn("movableAreaHeight",nn(0)),i=Hn("movableAreaRootRef"),a={x:0,y:0},s={x:0,y:0},l=nn(0),c=nn(0),u=nn(0),d=nn(0),p=nn(0),f=nn(0);function h(){let e=0-a.x+s.x,t=o.value-l.value-a.x-s.x;u.value=Math.min(e,t),p.value=Math.max(e,t);let n=0-a.y+s.y,i=r.value-c.value-a.y-s.y;d.value=Math.min(n,i),f.value=Math.max(n,i)}function g(){a.x=Pg(e.value,i.value),a.y=Ig(e.value,i.value)}function m(o){o=o||t.value,o=n(o);let r=e.value.getBoundingClientRect();c.value=r.height/t.value,l.value=r.width/t.value;let i=c.value*o,a=l.value*o;s.x=(a-l.value)/2,s.y=(i-c.value)/2}return{_updateBoundary:h,_updateOffset:g,_updateWH:m,_scaleOffset:s,minX:u,minY:d,maxX:p,maxY:f}}(t,o,D),{FAandSFACancel:w,_getLimitXY:x,_animationTo:T,_setTransform:S,_revise:k,dampingNumber:C,xMove:E,yMove:A,xSync:M,ySync:P,_STD:I}=function(e,t,n,o,r,i,a,s,l,c,u,d,p,f){const h=vi((()=>{let e=Number(t.damping);return isNaN(e)?20:e})),g=vi((()=>"all"===t.direction||"horizontal"===t.direction)),m=vi((()=>"all"===t.direction||"vertical"===t.direction)),v=nn(Lg(t.x)),y=nn(Lg(t.y));Gn((()=>t.x),(e=>{v.value=Lg(e)})),Gn((()=>t.y),(e=>{y.value=Lg(e)})),Gn(v,(e=>{k(e)})),Gn(y,(e=>{C(e)}));const b=new kg(1,9*Math.pow(h.value,2)/40,h.value);function _(e,t){let n=!1;return e>r.value?(e=r.value,n=!0):e<a.value&&(e=a.value,n=!0),t>i.value?(t=i.value,n=!0):t<s.value&&(t=s.value,n=!0),{x:e,y:t,outOfBounds:n}}function w(){d&&d.cancel(),u&&u.cancel()}function x(e,n,r,i,a,s){w(),g.value||(e=l.value),m.value||(n=c.value),t.scale||(r=o.value);let d=_(e,n);e=d.x,n=d.y,t.animation?(b._springX._solution=null,b._springY._solution=null,b._springScale._solution=null,b._springX._endPosition=l.value,b._springY._endPosition=c.value,b._springScale._endPosition=o.value,b.setEnd(e,n,r,1),u=Og(b,(function(){let e=b.x();T(e.x,e.y,e.scale,i,a,s)}),(function(){u.cancel()}))):T(e,n,r,i,a,s)}function T(r,i,a,s="",u,d){null!==r&&"NaN"!==r.toString()&&"number"==typeof r||(r=l.value||0),null!==i&&"NaN"!==i.toString()&&"number"==typeof i||(i=c.value||0),r=Number(r.toFixed(1)),i=Number(i.toFixed(1)),a=Number(a.toFixed(1)),l.value===r&&c.value===i||u||f("change",{},{x:Cg(r,n.x),y:Cg(i,n.y),source:s}),t.scale||(a=o.value),a=+(a=p(a)).toFixed(3),d&&a!==o.value&&f("scale",{},{x:r,y:i,scale:a});let h="translateX("+r+"px) translateY("+i+"px) translateZ(0px) scale("+a+")";e.value&&(e.value.style.transform=h,e.value.style.webkitTransform=h,l.value=r,c.value=i,o.value=a)}function S(e){let t=_(l.value,c.value),n=t.x,r=t.y,i=t.outOfBounds;return i&&x(n,r,o.value,e),i}function k(e){if(g.value){if(e+n.x===l.value)return l;u&&u.cancel(),x(e+n.x,y.value+n.y,o.value)}return e}function C(e){if(m.value){if(e+n.y===c.value)return c;u&&u.cancel(),x(v.value+n.x,e+n.y,o.value)}return e}return{FAandSFACancel:w,_getLimitXY:_,_animationTo:x,_setTransform:T,_revise:S,dampingNumber:h,xMove:g,yMove:m,xSync:v,ySync:y,_STD:b}}(t,e,m,o,b,_,v,y,a,s,l,c,D,n);function O(t,n){if(e.scale){t=D(t),g(t),f();const e=x(a.value,s.value),o=e.x,r=e.y;n?T(o,r,t,"",!0,!0):Mg((function(){S(o,r,t,"",!0,!0)}))}}function L(){i.value=!0}function $(e){r.value=e}function D(e){return e=Math.max(.5,u.value,e),e=Math.min(10,d.value,e)}function R(){if(!e.scale)return!1;O(o.value,!0),$(o.value)}function B(t){return!!e.scale&&(O(t=D(t),!0),$(t),t)}function N(){i.value=!1,$(o.value)}function q(e){e&&(e=r.value*e,L(),O(e))}return{_updateOldScale:$,_endScale:N,_setScale:q,scaleValueSync:p,_updateBoundary:f,_updateOffset:h,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:b,maxY:_,FAandSFACancel:w,_getLimitXY:x,_animationTo:T,_setTransform:S,_revise:k,dampingNumber:C,xMove:E,yMove:A,xSync:M,ySync:P,_STD:I}}(e,n,t,l,c,u,d,p,f,h);function W(){u.value||e.disabled||(D(),_.historyX=[0,0],_.historyY=[0,0],_.historyT=[0,0],j.value&&(a=d.value),z.value&&(s=p.value),n.value.style.willChange="transform",m=null,v=null,g=!0)}function H(t){if(!u.value&&!e.disabled&&g){let n=d.value,o=p.value;if(null===v&&(v=Math.abs(t.detail.dx/t.detail.dy)>1?"htouchmove":"vtouchmove"),j.value&&(n=t.detail.dx+a,_.historyX.shift(),_.historyX.push(n),z.value||null!==m||(m=Math.abs(t.detail.dx/t.detail.dy)<1)),z.value&&(o=t.detail.dy+s,_.historyY.shift(),_.historyY.push(o),j.value||null!==m||(m=Math.abs(t.detail.dy/t.detail.dx)<1)),_.historyT.shift(),_.historyT.push(t.detail.timeStamp),!m){t.preventDefault();let r="touch";n<I.value?e.outOfBounds?(r="touch-out-of-bounds",n=I.value-y.x(I.value-n)):n=I.value:n>L.value&&(e.outOfBounds?(r="touch-out-of-bounds",n=L.value+y.x(n-L.value)):n=L.value),o<O.value?e.outOfBounds?(r="touch-out-of-bounds",o=O.value-b.x(O.value-o)):o=O.value:o>$.value&&(e.outOfBounds?(r="touch-out-of-bounds",o=$.value+b.x(o-$.value)):o=$.value),Mg((function(){B(n,o,l.value,r)}))}}}function Y(){if(!u.value&&!e.disabled&&g&&(n.value.style.willChange="auto",g=!1,!m&&!N("out-of-bounds")&&e.inertia)){const e=1e3*(_.historyX[1]-_.historyX[0])/(_.historyT[1]-_.historyT[0]),t=1e3*(_.historyY[1]-_.historyY[0])/(_.historyT[1]-_.historyT[0]),n=d.value,o=p.value;x.setV(e,t),x.setS(n,o);const r=x.delta().x,i=x.delta().y;let a=r+n,s=i+o;a<I.value?(a=I.value,s=o+(I.value-n)*i/r):a>L.value&&(a=L.value,s=o+(L.value-n)*i/r),s<O.value?(s=O.value,a=n+(O.value-o)*r/i):s>$.value&&(s=$.value,a=n+($.value-o)*r/i),x.setEnd(a,s),h=Og(x,(function(){let e=x.s(),t=e.x,n=e.y;B(t,n,l.value,"friction")}),(function(){h.cancel()}))}e.outOfBounds||e.inertia||D()}function X(){if(!o.value)return;D();let t=e.scale?C.value:1;A(),M(t),E();let n=R(V.value+P.x,F.value+P.y),r=n.x,i=n.y;B(r,i,t,"",!0),T(t)}return Io((()=>{bg(n.value,(e=>{switch(e.detail.state){case"start":W();break;case"move":H(e);break;case"end":Y()}})),X(),x.reconfigure(1,w.value),U.reconfigure(1,9*Math.pow(q.value,2)/40,q.value),n.value.style.transformOrigin="center";const e={rootRef:n,setParent:X,_endScale:S,_setScale:k};r(e),Do((()=>{i(e)}))})),Do((()=>{D()})),{setParent:X}}(e,r,o);return()=>Jr("uni-movable-view",{ref:o},[Jr(Eh,{onResize:i},null,8,["onResize"]),t.default&&t.default()],512)}});let Ag=!1;function Mg(e){Ag||(Ag=!0,requestAnimationFrame((function(){e(),Ag=!1})))}function Pg(e,t){if(e===t)return 0;let n=e.offsetLeft;return e.offsetParent?n+=Pg(e.offsetParent,t):0}function Ig(e,t){if(e===t)return 0;let n=e.offsetTop;return e.offsetParent?n+=Ig(e.offsetParent,t):0}function Og(e,t,n){let o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);let i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}function Lg(e){return/\d+[ur]px$/i.test(e)?uni.upx2px(parseFloat(e)):Number(e)||0}const $g=["navigate","redirect","switchTab","reLaunch","navigateBack"],Dg=["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"],Rg=["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"],Bg={hoverClass:{type:String,default:"navigator-hover"},url:{type:String,default:""},openType:{type:String,default:"navigate",validator:e=>Boolean(~$g.indexOf(e))},delta:{type:Number,default:1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:600},exists:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},animationType:{type:String,default:"",validator:e=>!e||Dg.concat(Rg).includes(e)},animationDuration:{type:[String,Number],default:300}};x({},Bg,{renderLink:{type:Boolean,default:!0}});const Ng=uu({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return C(e)&&e.filter((e=>"number"==typeof e)).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,{slots:t,emit:n}){const o=nn(null),r=nn(null),i=mu(o,n),a=function(e){const t=Vt([...e.value]),n=Vt({value:t,height:34});return Gn((()=>e.value),((e,t)=>{n.value.length=e.length,e.forEach(((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)}))})),n}(e),s=nn(null);Io((()=>{const e=s.value;e&&(a.height=e.$el.offsetHeight)}));let l=nn([]),c=nn([]);function u(e){let t=c.value;t=t.filter((e=>e.type!==$r));let n=t.indexOf(e);return-1!==n?n:l.value.indexOf(e)}return Wn("getPickerViewColumn",(function(e){return vi({get(){const t=u(e.vnode);return a.value[t]||0},set(t){const o=u(e.vnode);if(o<0)return;if(a.value[o]!==t){a.value[o]=t;const e=a.value.map((e=>e));n("update:value",e),i("change",{},{value:e})}}})})),Wn("pickerViewProps",e),Wn("pickerViewState",a),()=>{const e=t.default&&t.default();{const t=fg(e);l.value=t,Sn((()=>{c.value=t}))}return Jr("uni-picker-view",{ref:o},[Jr(Eh,{ref:s,onResize:({height:e})=>a.height=e},null,8,["onResize"]),Jr("div",{ref:r,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class qg{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){const t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){const e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function jg(e,t,n){return e>t-n&&e<t+n}function zg(e,t){return jg(e,0,t)}class Vg{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,a=t/(r*e);return{x:function(e){return(i+a*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+a*e)*t+a*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),a=(-n+Math.sqrt(i))/(2*o),s=(t-r*e)/(a-r),l=e-s;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*t+s*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*r*t+s*a*n}}}const a=Math.sqrt(4*o*r-n*n)/(2*o),s=-n/2*o,l=e,c=(t-s*e)/a;return{x:function(e){return Math.pow(Math.E,s*e)*(l*Math.cos(a*e)+c*Math.sin(a*e))},dx:function(e){const t=Math.pow(Math.E,s*e),n=Math.cos(a*e),o=Math.sin(a*e);return t*(c*a*n-l*a*o)+s*t*(c*o+l*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!zg(t,.4)){t=t||0;let o=this._endPosition;this._solution&&(zg(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),zg(t,.4)&&(t=0),zg(o,.4)&&(o=0),o+=this._endPosition),this._solution&&zg(o-e,.4)&&zg(t,.4)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),jg(this.x(),this._endPosition,.4)&&zg(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class Fg{constructor(e,t,n){this._extent=e,this._friction=t||new qg(.01),this._spring=n||new Vg(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){let t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){const e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class Ug{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new Fg(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){let n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}let o;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){const e=this._scroll._friction.x(100),t=e%this._itemSize;o=Math.abs(t)>this._itemSize/2?e-(this._itemSize-Math.abs(t)):e-t,o<=0&&o>=-this._extent&&this._scroll.setVelocityByEnd(o)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=function(e,t,n){const o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);const i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}(this._scroll,(()=>{const e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();const o=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/o),this._lastTime=e)}),(()=>{this._enableSnap&&(o<=0&&o>=-this._extent&&(this._position=o,this.updatePosition()),P(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1}))}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),P(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(P(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){let o=0;const r=this._position;this._enableX?(o=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(o=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-o?this._position=-o:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),r!==this._position&&(this.dispatchScroll(),P(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=o,this._scroll._extent=o}updatePosition(){let e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}function Wg(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},o=new Ug(e,t);function r(e){const t=e,o=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:o.screenX-n.x,y:o.screenY-n.y}}return{scroller:o,handleTouchStart:function(e){const t=e,r=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=r.screenX,n.y=r.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||r.timeStamp],n.listener=o,o.onTouchStart&&o.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){const t=e,o=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();const i=r(e);if(i){for(n.maxDy=Math.max(n.maxDy,Math.abs(i.y)),n.maxDx=Math.max(n.maxDx,Math.abs(i.x)),n.historyX.push(i.x),n.historyY.push(i.y),n.historyTime.push(t.detail.timeStamp||o.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(i.x,i.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();const t=r(e);if(t){const e=n.listener;n.trackingID=-1,n.listener=null;const o={x:0,y:0};if(n.historyTime.length>2)for(let t=n.historyTime.length-1,r=n.historyTime[t],i=n.historyX[t],a=n.historyY[t];t>0;){t--;const e=r-n.historyTime[t];if(e>30&&e<50){o.x=(i-n.historyX[t])/(e/1e3),o.y=(a-n.historyY[t])/(e/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],e&&e.onTouchEnd&&e.onTouchEnd(t.x,t.y,o)}}}}}let Hg=0;const Yg=uu({name:"PickerViewColumn",setup(e,{slots:t,emit:n}){const o=nn(null),r=nn(null),i=Hn("getPickerViewColumn"),a=li(),s=i?i(a):nn(0),l=Hn("pickerViewProps"),c=Hn("pickerViewState"),u=nn(34),d=nn(null);Io((()=>{const e=d.value;u.value=e.$el.offsetHeight}));const p=vi((()=>(c.height-u.value)/2)),{state:f}=eg(),h=function(e){const t="uni-picker-view-content-"+Hg++;return Gn((()=>e.value),(function(){const n=document.createElement("style");n.innerText=`.uni-picker-view-content.${t}>*{height: ${e.value}px;overflow: hidden;}`,document.head.appendChild(n)})),t}(u);let g;const m=Vt({current:s.value,length:0});let v;function y(){g&&!v&&(v=!0,Sn((()=>{v=!1;let e=Math.min(m.current,m.length-1);e=Math.max(e,0),g.update(e*u.value,void 0,u.value)})))}Gn((()=>s.value),(e=>{e!==m.current&&(m.current=e,y())})),Gn((()=>m.current),(e=>s.value=e)),Gn([()=>u.value,()=>m.length,()=>c.height],y);let b=0;function _(e){const t=b+e.deltaY;if(Math.abs(t)>10){b=0;let e=Math.min(m.current+(t<0?-1:1),m.length-1);m.current=e=Math.max(e,0),g.scrollTo(e*u.value)}else b=t;e.preventDefault()}function w({clientY:e}){const t=o.value;if(!g.isScrolling()){const n=e-t.getBoundingClientRect().top-c.height/2,o=u.value/2;if(!(Math.abs(n)<=o)){const e=Math.ceil((Math.abs(n)-o)/u.value),t=n<0?-e:e;let r=Math.min(m.current+t,m.length-1);m.current=r=Math.max(r,0),g.scrollTo(r*u.value)}}}return Io((()=>{const e=o.value,t=r.value,{scroller:n,handleTouchStart:i,handleTouchMove:a,handleTouchEnd:s}=Wg(t,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:u.value,friction:new qg(1e-4),spring:new Vg(2,90,20),onSnap:e=>{isNaN(e)||e===m.current||(m.current=e)}});g=n,bg(e,(e=>{switch(e.detail.state){case"start":i(e);break;case"move":a(e),e.stopPropagation();break;case"end":case"cancel":s(e)}}),!0),function(e){let t=0,n=0;e.addEventListener("touchstart",(e=>{const o=e.changedTouches[0];t=o.clientX,n=o.clientY})),e.addEventListener("touchend",(e=>{const o=e.changedTouches[0];if(Math.abs(o.clientX-t)<20&&Math.abs(o.clientY-n)<20){const t={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},n=new CustomEvent("click",t);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{n[e]=o[e]})),e.target.dispatchEvent(n)}}))}(e),y()})),()=>{const e=t.default&&t.default();m.length=fg(e).length;const n=`${p.value}px 0`;return Jr("uni-picker-view-column",{ref:o},[Jr("div",{onWheel:_,onClick:w,class:"uni-picker-view-group"},[Jr("div",oi(f.attrs,{class:["uni-picker-view-mask",l.maskClass],style:`background-size: 100% ${p.value}px;${l.maskStyle}`}),null,16),Jr("div",oi(f.attrs,{class:["uni-picker-view-indicator",l.indicatorClass],style:l.indicatorStyle}),[Jr(Eh,{ref:d,onResize:({height:e})=>u.value=e},null,8,["onResize"])],16),Jr("div",{ref:r,class:["uni-picker-view-content",h],style:{padding:n}},[e],6)],40,["onWheel","onClick"])],512)}}}),Xg=hc("ucg"),Gg=uu({name:"Radio",props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},value:{type:String,default:""},color:{type:String,default:"#007aff"},backgroundColor:{type:String,default:""},borderColor:{type:String,default:""},activeBackgroundColor:{type:String,default:""},activeBorderColor:{type:String,default:""},iconColor:{type:String,default:"#ffffff"}},setup(e,{slots:t}){const n=nn(e.checked),o=nn(e.value),r=vi((()=>{if(e.disabled)return{backgroundColor:"#E1E1E1",borderColor:"#D1D1D1"};const t={};return n.value?(t.backgroundColor=e.activeBackgroundColor||e.color,t.borderColor=e.activeBorderColor||t.backgroundColor):(e.borderColor&&(t.borderColor=e.borderColor),e.backgroundColor&&(t.backgroundColor=e.backgroundColor)),t}));Gn([()=>e.checked,()=>e.value],(([e,t])=>{n.value=e,o.value=t}));const{uniCheckGroup:i,uniLabel:a,field:s}=function(e,t,n){const o=vi({get:()=>({radioChecked:Boolean(e.value),value:t.value}),set:({radioChecked:t})=>{e.value=t}}),r={reset:n},i=Hn(Xg,!1);i&&i.addField(o);const a=Hn(vu,!1);a&&a.addField(r);const s=Hn(bu,!1);return $o((()=>{i&&i.removeField(o),a&&a.removeField(r)})),{uniCheckGroup:i,uniForm:a,uniLabel:s,field:o}}(n,o,(()=>{n.value=!1})),l=t=>{e.disabled||n.value||(n.value=!0,i&&i.radioChange(t,s),t.stopPropagation())};return a&&(a.addHandler(l),$o((()=>{a.removeHandler(l)}))),_u(e,{"label-click":l}),()=>{const o=hu(e,"disabled");return Jr("uni-radio",oi(o,{onClick:l}),[Jr("div",{class:"uni-radio-wrapper",style:{"--HOVER-BD-COLOR":n.value?r.value.borderColor:e.activeBorderColor}},[Jr("div",{class:["uni-radio-input",{"uni-radio-input-disabled":e.disabled}],style:r.value},[n.value?xc(vc,e.disabled?"#ADADAD":e.iconColor,18):""],6),t.default&&t.default()],4)],16,["onClick"])}}});const Jg={a:"",abbr:"",address:"",article:"",aside:"",b:"",bdi:"",bdo:["dir"],big:"",blockquote:"",br:"",caption:"",center:"",cite:"",code:"",col:["span","width"],colgroup:["span","width"],dd:"",del:"",div:"",dl:"",dt:"",em:"",fieldset:"",font:"",footer:"",h1:"",h2:"",h3:"",h4:"",h5:"",h6:"",header:"",hr:"",i:"",img:["alt","src","height","width"],ins:"",label:"",legend:"",li:"",mark:"",nav:"",ol:["start","type"],p:"",pre:"",q:"",rt:"",ruby:"",s:"",section:"",small:"",span:"",strong:"",sub:"",sup:"",table:["width"],tbody:"",td:["colspan","height","rowspan","width"],tfoot:"",th:["colspan","height","rowspan","width"],thead:"",tr:["colspan","height","rowspan","width"],tt:"",u:"",ul:""},Qg={amp:"&",gt:">",lt:"<",nbsp:" ",quot:'"',apos:"'",ldquo:"“",rdquo:"”",yen:"￥",radic:"√",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",hellip:"…"};const Kg=(e,t,n)=>!n||C(n)&&!n.length?[]:n.map((n=>{if(B(n)){if(!k(n,"type")||"node"===n.type){let o={[e]:""};const r=n.name.toLowerCase();if(!k(Jg,r))return;return function(e,t){if(B(t))for(const n in t)if(k(t,n)){const o=t[n];"img"===e&&"src"===n&&(t[n]=Cu(o))}}(r,n.attrs),o=x(o,function(e,t){if(["a","img"].includes(e.name)&&t)return{onClick:n=>{t(n,{node:e}),n.stopPropagation(),n.preventDefault(),n.returnValue=!1}}}(n,t),n.attrs),yi(n.name,o,Kg(e,t,n.children))}return"text"===n.type&&I(n.text)&&""!==n.text?Kr((n.text||"").replace(/&(([a-zA-Z]+)|(#x{0,1}[\da-zA-Z]+));/gi,(function(e,t){return k(Qg,t)&&Qg[t]?Qg[t]:/^#[0-9]{1,4}$/.test(t)?String.fromCharCode(t.slice(1)):/^#x[0-9a-f]{1,4}$/i.test(t)?String.fromCharCode(0+t.slice(1)):e}))):void 0}}));function Zg(e){e=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(e);const t=[],n={node:"root",children:[]};return function(e,t){var n,o,r,i=[],a=e;for(i.last=function(){return this[this.length-1]};e;){if(o=!0,i.last()&&qh[i.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+i.last()+"[^>]*>"),(function(e,n){return n=n.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(n),""})),c("",i.last());else if(0==e.indexOf("\x3c!--")?(n=e.indexOf("--\x3e"))>=0&&(t.comment&&t.comment(e.substring(4,n)),e=e.substring(n+3),o=!1):0==e.indexOf("</")?(r=e.match(Oh))&&(e=e.substring(r[0].length),r[0].replace(Oh,c),o=!1):0==e.indexOf("<")&&(r=e.match(Ih))&&(e=e.substring(r[0].length),r[0].replace(Ih,l),o=!1),o){var s=(n=e.indexOf("<"))<0?e:e.substring(0,n);e=n<0?"":e.substring(n),t.chars&&t.chars(s)}if(e==a)throw"Parse Error: "+e;a=e}function l(e,n,o,r){if(n=n.toLowerCase(),Dh[n])for(;i.last()&&Rh[i.last()];)c("",i.last());if(Bh[n]&&i.last()==n&&c("",n),(r=$h[n]||!!r)||i.push(n),t.start){var a=[];o.replace(Lh,(function(e,t){var n=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:Nh[t]?t:"";a.push({name:t,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})})),t.start&&t.start(n,a,r)}}function c(e,n){if(n)for(o=i.length-1;o>=0&&i[o]!=n;o--);else var o=0;if(o>=0){for(var r=i.length-1;r>=o;r--)t.end&&t.end(i[r]);i.length=o}}c()}(e,{start:function(e,o,r){const i={name:e};if(0!==o.length&&(i.attrs=function(e){return e.reduce((function(e,t){let n=t.value;const o=t.name;return n.match(/ /)&&-1===["style","src"].indexOf(o)&&(n=n.split(" ")),e[o]?Array.isArray(e[o])?e[o].push(n):e[o]=[e[o],n]:e[o]=n,e}),{})}(o)),r){const e=t[0]||n;e.children||(e.children=[]),e.children.push(i)}else t.unshift(i)},end:function(e){const o=t.shift();if(o.name!==e&&console.error("invalid state: mismatch end tag"),0===t.length)n.children.push(o);else{const e=t[0];e.children||(e.children=[]),e.children.push(o)}},chars:function(e){const o={type:"text",text:e};if(0===t.length)n.children.push(o);else{const e=t[0];e.children||(e.children=[]),e.children.push(o)}},comment:function(e){const n={node:"comment",text:e},o=t[0];o.children||(o.children=[]),o.children.push(n)}}),n.children}const em=uu({name:"RichText",compatConfig:{MODE:3},props:{nodes:{type:[Array,String],default:function(){return[]}}},emits:["click","touchstart","touchmove","touchcancel","touchend","longpress","itemclick"],setup(e,{emit:t}){const n=li(),o=n&&n.vnode.scopeId||"",r=nn(null),i=nn([]),a=mu(r,t);function s(e,t={}){a("itemclick",e,t)}return Gn((()=>e.nodes),(function(){let t=e.nodes;I(t)&&(t=Zg(e.nodes)),i.value=Kg(o,s,t)}),{immediate:!0}),()=>yi("uni-rich-text",{ref:r},yi("div",{},i.value))}}),tm=me(!0),nm=uu({name:"ScrollView",compatConfig:{MODE:3},props:{scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"back"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n}){const o=nn(null),r=nn(null),i=nn(null),a=nn(null),s=nn(null),l=mu(o,t),{state:c,scrollTopNumber:u,scrollLeftNumber:d}=function(e){const t=vi((()=>Number(e.scrollTop)||0)),n=vi((()=>Number(e.scrollLeft)||0));return{state:Vt({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshRotate:0,refreshState:""}),scrollTopNumber:t,scrollLeftNumber:n}}(e);!function(e,t,n,o,r,i,a,s,l){let c=!1,u=0,d=!1,p=()=>{};const f=vi((()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t})),h=vi((()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function g(e,t){const n=a.value;let o=0,r="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?o=n.scrollLeft-e:"y"===t&&(o=n.scrollTop-e),0===o)return;let i=s.value;i.style.transition="transform .3s ease-out",i.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?r="translateX("+o+"px) translateZ(0)":"y"===t&&(r="translateY("+o+"px) translateZ(0)"),i.removeEventListener("transitionend",p),i.removeEventListener("webkitTransitionEnd",p),p=()=>_(e,t),i.addEventListener("transitionend",p),i.addEventListener("webkitTransitionEnd",p),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),i.style.transform=r,i.style.webkitTransform=r}function m(n){const o=n.target;r("scroll",n,{scrollLeft:o.scrollLeft,scrollTop:o.scrollTop,scrollHeight:o.scrollHeight,scrollWidth:o.scrollWidth,deltaX:t.lastScrollLeft-o.scrollLeft,deltaY:t.lastScrollTop-o.scrollTop}),e.scrollY&&(o.scrollTop<=f.value&&t.lastScrollTop-o.scrollTop>0&&n.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",n,{direction:"top"}),t.lastScrollToUpperTime=n.timeStamp),o.scrollTop+o.offsetHeight+h.value>=o.scrollHeight&&t.lastScrollTop-o.scrollTop<0&&n.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",n,{direction:"bottom"}),t.lastScrollToLowerTime=n.timeStamp)),e.scrollX&&(o.scrollLeft<=f.value&&t.lastScrollLeft-o.scrollLeft>0&&n.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",n,{direction:"left"}),t.lastScrollToUpperTime=n.timeStamp),o.scrollLeft+o.offsetWidth+h.value>=o.scrollWidth&&t.lastScrollLeft-o.scrollLeft<0&&n.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",n,{direction:"right"}),t.lastScrollToLowerTime=n.timeStamp)),t.lastScrollTop=o.scrollTop,t.lastScrollLeft=o.scrollLeft}function v(t){e.scrollY&&(e.scrollWithAnimation?g(t,"y"):a.value.scrollTop=t)}function y(t){e.scrollX&&(e.scrollWithAnimation?g(t,"x"):a.value.scrollLeft=t)}function b(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error(`id error: scroll-into-view=${t}`);let n=i.value.querySelector("#"+t);if(n){let t=a.value.getBoundingClientRect(),o=n.getBoundingClientRect();if(e.scrollX){let n=o.left-t.left,r=a.value.scrollLeft+n;e.scrollWithAnimation?g(r,"x"):a.value.scrollLeft=r}if(e.scrollY){let n=o.top-t.top,r=a.value.scrollTop+n;e.scrollWithAnimation?g(r,"y"):a.value.scrollTop=r}}}}function _(t,n){s.value.style.transition="",s.value.style.webkitTransition="",s.value.style.transform="",s.value.style.webkitTransform="";let o=a.value;"x"===n?(o.style.overflowX=e.scrollX?"auto":"hidden",o.scrollLeft=t):"y"===n&&(o.style.overflowY=e.scrollY?"auto":"hidden",o.scrollTop=t),s.value.removeEventListener("transitionend",p),s.value.removeEventListener("webkitTransitionEnd",p)}function w(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,r("refresherrefresh",{},{}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(d=!1,r("refresherrestore",{},{})),"refresherabort"===n&&d&&(d=!1,r("refresherabort",{},{}))}t.refreshState=n}}Io((()=>{Sn((()=>{v(n.value),y(o.value)})),b(e.scrollIntoView);let i=function(e){e.preventDefault(),e.stopPropagation(),m(e)},s={x:0,y:0},l=null,p=function(n){if(null===s)return;let o=n.touches[0].pageX,i=n.touches[0].pageY,p=a.value;if(Math.abs(o-s.x)>Math.abs(i-s.y))if(e.scrollX){if(0===p.scrollLeft&&o>s.x)return void(l=!1);if(p.scrollWidth===p.offsetWidth+p.scrollLeft&&o<s.x)return void(l=!1);l=!0}else l=!1;else if(e.scrollY)if(0===p.scrollTop&&i>s.y)l=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(p.scrollHeight===p.offsetHeight+p.scrollTop&&i<s.y)return void(l=!1);l=!0}else l=!1;if(l&&n.stopPropagation(),0===p.scrollTop&&1===n.touches.length&&w("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const o=i-s.y;0===u&&(u=i),c?(t.refresherHeight=o+e.refresherThreshold,d=!1):(t.refresherHeight=i-u,t.refresherHeight>0&&(d=!0,r("refresherpulling",n,{deltaY:o})));const a=t.refresherHeight/e.refresherThreshold;t.refreshRotate=360*(a>1?1:a)}},f=function(e){1===e.touches.length&&(s={x:e.touches[0].pageX,y:e.touches[0].pageY})},h=function(n){s=null,t.refresherHeight>=e.refresherThreshold?w("refreshing"):w("refresherabort")};a.value.addEventListener("touchstart",f,tm),a.value.addEventListener("touchmove",p,me(!1)),a.value.addEventListener("scroll",i,me(!1)),a.value.addEventListener("touchend",h,tm),$o((()=>{a.value.removeEventListener("touchstart",f),a.value.removeEventListener("touchmove",p),a.value.removeEventListener("scroll",i),a.value.removeEventListener("touchend",h)}))})),bo((()=>{e.scrollY&&(a.value.scrollTop=t.lastScrollTop),e.scrollX&&(a.value.scrollLeft=t.lastScrollLeft)})),Gn(n,(e=>{v(e)})),Gn(o,(e=>{y(e)})),Gn((()=>e.scrollIntoView),(e=>{b(e)})),Gn((()=>e.refresherTriggered),(e=>{!0===e?w("refreshing"):!1===e&&w("restore")}))}(e,c,u,d,l,o,r,a,t);const p=vi((()=>{let t="";return e.scrollX?t+="overflow-x:auto;":t+="overflow-x:hidden;",e.scrollY?t+="overflow-y:auto;":t+="overflow-y:hidden;",t}));return()=>{const{refresherEnabled:t,refresherBackground:l,refresherDefaultStyle:u}=e,{refresherHeight:d,refreshState:f,refreshRotate:h}=c;return Jr("uni-scroll-view",{ref:o},[Jr("div",{ref:i,class:"uni-scroll-view"},[Jr("div",{ref:r,style:p.value,class:"uni-scroll-view"},[Jr("div",{ref:a,class:"uni-scroll-view-content"},[t?Jr("div",{ref:s,style:{backgroundColor:l,height:d+"px"},class:"uni-scroll-view-refresher"},["none"!==u?Jr("div",{class:"uni-scroll-view-refresh"},[Jr("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==f?Jr("svg",{key:"refresh__icon",style:{transform:"rotate("+h+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[Jr("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),Jr("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==f?Jr("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[Jr("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"==u?n.refresher&&n.refresher():null],4):null,n.default&&n.default()],512)],4)],512)],512)}}});function om(e,t,n,o,r,i){function a(){c&&(clearTimeout(c),c=null)}let s,l,c=null,u=!0,d=0,p=1,f=null,h=!1,g=0,m="";const v=vi((()=>n.value.length>t.displayMultipleItems)),y=vi((()=>e.circular&&v.value));function b(r){Math.floor(2*d)===Math.floor(2*r)&&Math.ceil(2*d)===Math.ceil(2*r)||y.value&&function(o){if(!u)for(let r=n.value,i=r.length,a=o+t.displayMultipleItems,s=0;s<i;s++){const t=r[s],n=Math.floor(o/i)*i+s,l=n+i,c=n-i,u=Math.max(o-(n+1),n-a,0),d=Math.max(o-(l+1),l-a,0),p=Math.max(o-(c+1),c-a,0),f=Math.min(u,d,p),h=[n,l,c][[u,d,p].indexOf(f)];t.updatePosition(h,e.vertical)}}(r);const a="translate("+(e.vertical?"0":100*-r*p+"%")+", "+(e.vertical?100*-r*p+"%":"0")+") translateZ(0)",l=o.value;if(l&&(l.style.webkitTransform=a,l.style.transform=a),d=r,!s){if(r%1==0)return;s=r}r-=Math.floor(s);const c=n.value;r<=-(c.length-1)?r+=c.length:r>=c.length&&(r-=c.length),r=s%1>.5||s<0?r-1:r,i("transition",{},{dx:e.vertical?0:r*l.offsetWidth,dy:e.vertical?r*l.offsetHeight:0})}function _(e){const o=n.value.length;if(!o)return-1;const r=(Math.round(e)%o+o)%o;if(y.value){if(o<=t.displayMultipleItems)return 0}else if(r>o-t.displayMultipleItems)return o-t.displayMultipleItems;return r}function w(){f=null}function x(){if(!f)return void(h=!1);const e=f,o=e.toPos,r=e.acc,a=e.endTime,c=e.source,u=a-Date.now();if(u<=0){b(o),f=null,h=!1,s=null;const e=n.value[t.current];if(e){const n=e.getItemId();i("animationfinish",{},{current:t.current,currentItemId:n,source:c})}return}b(o+r*u*u/2),l=requestAnimationFrame(x)}function T(e,o,r){w();const i=t.duration,a=n.value.length;let s=d;if(y.value)if(r<0){for(;s<e;)s+=a;for(;s-a>e;)s-=a}else if(r>0){for(;s>e;)s-=a;for(;s+a<e;)s+=a;s+a-e<e-s&&(s+=a)}else{for(;s+a<e;)s+=a;for(;s-a>e;)s-=a;s+a-e<e-s&&(s+=a)}else"click"===o&&(e=e+t.displayMultipleItems-1<a?e:0);f={toPos:e,acc:2*(s-e)/(i*i),endTime:Date.now()+i,source:o},h||(h=!0,l=requestAnimationFrame(x))}function S(){a();const e=n.value,o=function(){c=null,m="autoplay",y.value?t.current=_(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,T(t.current,"autoplay",y.value?1:0),c=setTimeout(o,t.interval)};u||e.length<=t.displayMultipleItems||(c=setTimeout(o,t.interval))}function k(e){e?S():a()}return Gn([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{let o=-1;if(e.currentItemId)for(let t=0,r=n.value;t<r.length;t++){if(r[t].getItemId()===e.currentItemId){o=t;break}}o<0&&(o=Math.round(e.current)||0),o=o<0?0:o,t.current!==o&&(m="",t.current=o)})),Gn([()=>e.vertical,()=>y.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){a(),f&&(b(f.toPos),f=null);const r=n.value;for(let t=0;t<r.length;t++)r[t].updatePosition(t,e.vertical);p=1;const i=o.value;if(1===t.displayMultipleItems&&r.length){const e=r[0].getBoundingClientRect(),t=i.getBoundingClientRect();p=e.width/t.width,p>0&&p<1||(p=1)}const s=d;d=-2;const l=t.current;l>=0?(u=!1,t.userTracking?(b(s+l-g),g=l):(b(l),e.autoplay&&S())):(u=!0,b(-t.displayMultipleItems-1))})),Gn((()=>t.interval),(()=>{c&&(a(),S())})),Gn((()=>t.current),((e,o)=>{!function(e,o){const r=m;m="";const a=n.value;if(!r){const t=a.length;T(e,"",y.value&&o+(t-e)%t>t/2?1:0)}const s=a[e];if(s){const e=t.currentItemId=s.getItemId();i("change",{},{current:t.current,currentItemId:e,source:r})}}(e,o),r("update:current",e)})),Gn((()=>t.currentItemId),(e=>{r("update:currentItemId",e)})),Gn((()=>e.autoplay&&!t.userTracking),k),k(e.autoplay&&!t.userTracking),Io((()=>{let r=!1,i=0,s=0;function l(e){t.userTracking=!1;const n=i/Math.abs(i);let o=0;!e&&Math.abs(i)>.2&&(o=.5*n);const r=_(d+o);e?b(g):(m="touch",t.current=r,T(r,"touch",0!==o?o:0===r&&y.value&&d>=1?1:0))}bg(o.value,(c=>{if(!e.disableTouch&&!u){if("start"===c.detail.state)return t.userTracking=!0,r=!1,a(),g=d,i=0,s=Date.now(),void w();if("end"===c.detail.state)return l(!1);if("cancel"===c.detail.state)return l(!0);if(t.userTracking){if(!r){r=!0;const n=Math.abs(c.detail.dx),o=Math.abs(c.detail.dy);if((n>=o&&e.vertical||n<=o&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&S())}return function(r){const a=s;s=Date.now();const l=n.value.length-t.displayMultipleItems;function c(e){return.5-.25/(e+.5)}function u(e,t){let n=g+e;i=.6*i+.4*t,y.value||(n<0||n>l)&&(n<0?n=-c(-n):n>l&&(n=l+c(n-l)),i=0),b(n)}const d=s-a||1,p=o.value;e.vertical?u(-r.dy/p.offsetHeight,-r.ddy/d):u(-r.dx/p.offsetWidth,-r.ddx/d)}(c.detail),!1}}}))})),Do((()=>{a(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){T(t.current=e,m="click",y.value?1:0)},circularEnabled:y,swiperEnabled:v}}const rm=uu({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const o=nn(null),r=mu(o,n),i=nn(null),a=nn(null),s=function(e){return Vt({interval:vi((()=>{const t=Number(e.interval);return isNaN(t)?5e3:t})),duration:vi((()=>{const t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:vi((()=>{const t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),l=vi((()=>{let t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:mc(e.previousMargin,!0),bottom:mc(e.nextMargin,!0)}:{top:0,bottom:0,left:mc(e.previousMargin,!0),right:mc(e.nextMargin,!0)}),t})),c=vi((()=>{const t=Math.abs(100/s.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}}));let u=[];const d=[],p=nn([]);function f(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n instanceof Element||(n=n.el);const o=d.find((e=>n===e.rootRef.value));o&&e.push(Jt(o))}p.value=e}Wn("addSwiperContext",(function(e){d.push(e),f()}));Wn("removeSwiperContext",(function(e){const t=d.indexOf(e);t>=0&&(d.splice(t,1),f())}));const{onSwiperDotClick:h,circularEnabled:g,swiperEnabled:m}=om(e,s,p,a,n,r);let v=()=>null;return v=im(o,e,s,h,p,g,m),()=>{const n=t.default&&t.default();return u=fg(n),Jr("uni-swiper",{ref:o},[Jr("div",{ref:i,class:"uni-swiper-wrapper"},[Jr("div",{class:"uni-swiper-slides",style:l.value},[Jr("div",{ref:a,class:"uni-swiper-slide-frame",style:c.value},[n],4)],4),e.indicatorDots&&Jr("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[p.value.map(((t,n,o)=>Jr("div",{onClick:()=>h(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<s.current+s.displayMultipleItems&&n>=s.current||n<s.current+s.displayMultipleItems-o.length},style:{background:n===s.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),v()],512)],512)}}}),im=(e,t,n,o,r,i,a)=>{let s=!1,l=!1,c=!1,u=nn(!1);function d(e,n){const o=e.currentTarget;o&&(o.style.backgroundColor="over"===n?t.navigationActiveColor:"")}Yn((()=>{s="auto"===t.navigation,u.value=!0!==t.navigation||s,y()})),Yn((()=>{const e=r.value.length,t=!i.value;l=0===n.current&&t,c=n.current===e-1&&t||t&&n.current+n.displayMultipleItems>=e,a.value||(l=!0,c=!0,s&&(u.value=!0))}));const p={onMouseover:e=>d(e,"over"),onMouseout:e=>d(e,"out")};function f(e,t,a){if(e.stopPropagation(),a)return;const s=r.value.length;let l=n.current;switch(t){case"prev":l--,l<0&&i.value&&(l=s-1);break;case"next":l++,l>=s&&i.value&&(l=0)}o(l)}const h=()=>xc(bc,t.navigationColor,26);let g;const m=n=>{clearTimeout(g);const{clientX:o,clientY:r}=n,{left:i,right:a,top:s,bottom:l,width:c,height:d}=e.value.getBoundingClientRect();let p=!1;if(p=t.vertical?!(r-s<d/3||l-r<d/3):!(o-i<c/3||a-o<c/3),p)return g=setTimeout((()=>{u.value=p}),300);u.value=p},v=()=>{u.value=!0};function y(){e.value&&(e.value.removeEventListener("mousemove",m),e.value.removeEventListener("mouseleave",v),s&&(e.value.addEventListener("mousemove",m),e.value.addEventListener("mouseleave",v)))}return Io(y),function(){const e={"uni-swiper-navigation-hide":u.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?Jr(Or,null,[Jr("div",oi({class:["uni-swiper-navigation uni-swiper-navigation-prev",x({"uni-swiper-navigation-disabled":l},e)],onClick:e=>f(e,"prev",l)},p),[h()],16,["onClick"]),Jr("div",oi({class:["uni-swiper-navigation uni-swiper-navigation-next",x({"uni-swiper-navigation-disabled":c},e)],onClick:e=>f(e,"next",c)},p),[h()],16,["onClick"])]):null}},am=uu({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=nn(null),o={rootRef:n,getItemId:()=>e.itemId,getBoundingClientRect:()=>n.value.getBoundingClientRect(),updatePosition(e,t){const o=t?"0":100*e+"%",r=t?100*e+"%":"0",i=n.value,a=`translate(${o},${r}) translateZ(0)`;i&&(i.style.webkitTransform=a,i.style.transform=a)}};return Io((()=>{const e=Hn("addSwiperContext");e&&e(o)})),Do((()=>{const e=Hn("removeSwiperContext");e&&e(o)})),()=>Jr("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),sm={ensp:" ",emsp:" ",nbsp:" "};function lm(e,t){return e.replace(/\\n/g,"\n").split("\n").map((e=>function(e,{space:t,decode:n}){if(!e)return e;t&&sm[t]&&(e=e.replace(/ /g,sm[t]));if(!n)return e;return e.replace(/&nbsp;/g,sm.nbsp).replace(/&ensp;/g,sm.ensp).replace(/&emsp;/g,sm.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'")}(e,t)))}const cm=uu({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup:(e,{slots:t})=>()=>{const n=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==$r){const o=lm(t.children,{space:e.space,decode:e.decode}),r=o.length-1;o.forEach(((e,t)=>{(0!==t||e)&&n.push(Kr(e)),t!==r&&n.push(Jr("br"))}))}else n.push(t)})),Jr("uni-text",{selectable:!!e.selectable||null},[Jr("span",null,n)],8,["selectable"])}}),um=x({},rg,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator:e=>pm.concat("return").includes(e)}});let dm=!1;const pm=["done","go","next","search","send"];const fm=uu({name:"Textarea",props:um,emits:["confirm","linechange",...ig],setup(e,{emit:t}){const n=nn(null),o=nn(null),{fieldRef:r,state:i,scopedAttrsState:a,fixDisabledColor:s,trigger:l}=lg(e,n,t),c=vi((()=>i.value.split("\n"))),u=vi((()=>pm.includes(e.confirmType))),d=nn(0),p=nn(null);function f({height:e}){d.value=e}function h(e){"Enter"===e.key&&u.value&&e.preventDefault()}function g(t){if("Enter"===t.key&&u.value){!function(e){l("confirm",e,{value:i.value})}(t);const n=t.target;!e.confirmHold&&n.blur()}}return Gn((()=>d.value),(t=>{const r=n.value,i=p.value,a=o.value;let s=parseFloat(getComputedStyle(r).lineHeight);isNaN(s)&&(s=i.offsetHeight);var c=Math.round(t/s);l("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:c}),e.autoHeight&&(r.style.height="auto",a.style.height=t+"px")})),function(){const e="(prefers-color-scheme: dark)";dm=0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(e).media!==e}(),()=>{let t=e.disabled&&s?Jr("textarea",{key:"disabled-textarea",ref:r,value:i.value,tabindex:"-1",readonly:!!e.disabled,maxlength:i.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":dm},style:{overflowY:e.autoHeight?"hidden":"auto"},onFocus:e=>e.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):Jr("textarea",{key:"textarea",ref:r,value:i.value,disabled:!!e.disabled,maxlength:i.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":dm},style:{overflowY:e.autoHeight?"hidden":"auto"},onKeydown:h,onKeyup:g},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return Jr("uni-textarea",{ref:n},[Jr("div",{ref:o,class:"uni-textarea-wrapper"},[jo(Jr("div",oi(a.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[_a,!i.value.length]]),Jr("div",{ref:p,class:"uni-textarea-line"},[" "],512),Jr("div",{class:"uni-textarea-compute"},[c.value.map((e=>Jr("div",null,[e.trim()?e:"."]))),Jr(Eh,{initial:!0,onResize:f},null,8,["initial","onResize"])]),"search"===e.confirmType?Jr("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[t],40,["onSubmit"]):t],512)],512)}}}),hm=uu({name:"View",props:x({},pu),setup(e,{slots:t}){const{hovering:n,binding:o}=fu(e);return()=>{const r=e.hoverClass;return r&&"none"!==r?Jr("uni-view",oi({class:n.value?r:""},o),[t.default&&t.default()],16):Jr("uni-view",null,[t.default&&t.default()])}}});function gm(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function mm(e,t,n){e&&Bl(n||Ec(),e,(({type:e,data:n},o)=>{t(e,n,o)}))}function vm(e,t){e&&function(e,t){t=Rl(e,t),delete Dl[t]}(t||Ec(),e)}function ym(e,t,n,o){const r=li().proxy;Io((()=>{mm(t||gm(r),e,o),!n&&t||Gn((()=>r.id),((t,n)=>{mm(gm(r,t),e,o),vm(n&&gm(r,n))}))})),$o((()=>{vm(t||gm(r),o)}))}let bm=0;function _m(e){const t=Tc(),n=li().proxy,o=n.$options.name.toLowerCase(),r=e||n.id||"context"+bm++;return Io((()=>{n.$el.__uniContextInfo={id:r,type:o,page:t}})),`${o}.${r}`}function wm(e,t,n,o){P(t)&&Ao(e,t.bind(n),o)}function xm(e,t,n){var o;const r=e.mpType||n.$mpType;if(r&&"component"!==r&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!P(t))&&(Ce.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];C(r)?r.forEach((e=>wm(o,e,n,t))):wm(o,r,n,t)}})),"page"===r)){t.__isVisible=!0;try{Ic(n,"onLoad",t.attrs.__pageQuery),delete t.attrs.__pageQuery,"preloadPage"!==(null==(o=n.$page)?void 0:o.openType)&&Ic(n,"onShow")}catch(kC){console.error(kC.message+"\n"+kC.stack)}}}function Tm(e,t,n){xm(e,t,n)}function Sm(e,t,n){return e[t]=n}function km(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function Cm(e){return function(t,n,o){if(!n)throw t;const r=e._instance;if(!r||!r.proxy)throw t;Ic(r.proxy,"onError",t)}}function Em(e,t){return e?[...new Set([].concat(e,t))]:t}function Am(e){const t=e._context.config;var n;t.errorHandler=Me(e,Cm),n=t.optionMergeStrategies,Ce.forEach((e=>{n[e]=Em}));const o=t.globalProperties;o.$set=Sm,o.$applyOptions=Tm,o.$callMethod=km,function(e){Ee=e,Ae.forEach((t=>t(e)))}(e)}const Mm=hc("upm");function Pm(){return Hn(Mm)}function Im(e){const t=function(e){return Vt(function(e){{const{enablePullDownRefresh:t,navigationBar:n}=e;if(t){const t=function(e){return e.offset&&(e.offset=mc(e.offset)),e.height&&(e.height=mc(e.height)),e.range&&(e.range=mc(e.range)),e}(x({support:!0,color:"#2BD009",style:"circle",height:70,range:150,offset:0},e.pullToRefresh)),{type:o,style:r}=n;"custom"!==r&&"transparent"!==o&&(t.offset+=44+ac.top),e.pullToRefresh=t}}{const{navigationBar:t}=e,{titleSize:n,titleColor:o,backgroundColor:r}=t;t.titleText=t.titleText||"",t.type=t.type||"default",t.titleSize=n||"16px",t.titleColor=o||"#000000",t.backgroundColor=r||"#F8F8F8"}if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==Xm().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(Pc(hl().meta,e)))))}(e);return Wn(Mm,t),t}function Om(){return hl()}function Lm(){return history.state&&history.state.__id__||1}let $m;function Dm(){var e;return $m||($m=__uniConfig.tabBar&&Vt((e=__uniConfig.tabBar,gl()&&e.list&&e.list.forEach((e=>{bl(e,["text"])})),e))),$m}const Rm=window.CSS&&window.CSS.supports;function Bm(e){return Rm&&(Rm(e)||Rm.apply(window.CSS,e.split(":")))}const Nm=Bm("--a:0"),qm=Bm("top:env(a)"),jm=Bm("top:constant(a)"),zm=Bm("backdrop-filter:blur(10px)"),Vm={"css.var":Nm,"css.env":qm,"css.constant":jm,"css.backdrop-filter":zm},Fm=Cd(0,(e=>!k(Vm,e)||Vm[e])),Um=(()=>qm?"env":jm?"constant":"")();function Wm(e){return Um?`calc(${e}px + ${Um}(safe-area-inset-bottom))`:`${e}px`}const Hm=new Map;function Ym(){return Hm}function Xm(){const e=[],t=Hm.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function Gm(e,t=!0){const n=Hm.get(e);n.$.__isUnload=!0,Ic(n,"onUnload"),Hm.delete(e),t&&function(e){const t=ev.get(e);t&&(ev.delete(e),tv.pruneCacheEntry(t))}(e)}let Jm=Lm();function Qm(e){const t=Pm();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),function(e,t,n,o,r,i){const{id:a,route:s}=o,l=Le(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:a,path:ae(s),route:s,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#ffffff"===l?"light":"dark"}}("navigateTo",n,{},t)}function Km(e){const t=Qm(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),Hm.set(Zm(t.path,t.id),e)}function Zm(e,t){return e+"$$"+t}const ev=new Map,tv={get:e=>ev.get(e),set(e,t){!function(e){const t=parseInt(e.split("$$")[1]);if(!t)return;tv.forEach(((e,n)=>{const o=parseInt(n.split("$$")[1]);if(o&&o>t){if(function(e){return"tabBar"===e.props.type}(e))return;tv.delete(n),tv.pruneCacheEntry(e),Sn((()=>{Hm.forEach(((e,t)=>{e.$.isUnmounted&&Hm.delete(t)}))}))}}))}(e),ev.set(e,t)},delete(e){ev.get(e)&&ev.delete(e)},forEach(e){ev.forEach(e)}};function nv(e,t){!function(e){const t=rv(e),{body:n}=document;iv&&n.removeAttribute(iv),t&&n.setAttribute(t,""),iv=t}(e),function(e){let t=0,n=0;if("custom"!==e.navigationBar.style&&["default","float"].indexOf(e.navigationBar.type)>-1&&(t=44),e.isTabBar){const e=Dm();e.shown&&(n=parseInt(e.height))}var o;fc({"--window-top":(o=t,Um?`calc(${o}px + ${Um}(safe-area-inset-top))`:`${o}px`),"--window-bottom":Wm(n)})}(t),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),function(e,t){document.removeEventListener("touchmove",Oc),av&&document.removeEventListener("scroll",av);if(t.disableScroll)return document.addEventListener("touchmove",Oc);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!n&&!o&&!r)return;const i={},a=e.proxy.$page.id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&Ix.publishHandler("onPageScroll",{scrollTop:o},e),n&&Ix.emit(e+".onPageScroll",{scrollTop:o})}}(a,n,r));o&&(i.onReachBottomDistance=t.onReachBottomDistance||50,i.onReachBottom=()=>Ix.publishHandler("onReachBottom",{},a));av=Dc(i),requestAnimationFrame((()=>document.addEventListener("scroll",av)))}(e,t)}function ov(e){const t=rv(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function rv(e){return e.type.__scopeId}let iv,av;function sv(e){const t=pl({history:cv(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:lv});e.router=t,e.use(t)}const lv=(e,t,n)=>{if(n)return n};function cv(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=(n=e,(n=location.host?n||location.pathname+location.search:"").includes("#")||(n+="#"),ss(n));var n;return t.listen(((e,t,n)=>{"back"===n.direction&&function(e=1){const t=Xm(),n=t.length-1,o=n-e;for(let r=n;r>o;r--){const e=t[r].$page;Gm(Zm(e.path,e.id),!1)}}(Math.abs(n.delta))})),t}const uv={install(e){Am(e),Xc(e),iu(e),e.config.warnHandler||(e.config.warnHandler=dv),sv(e)}};function dv(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n),console.warn(...o)}const pv={class:"uni-async-loading"},fv=Jr("i",{class:"uni-loading"},null,-1),hv=du({name:"AsyncLoading",render:()=>(Nr(),Fr("div",pv,[fv]))});function gv(){window.location.reload()}const mv=du({name:"AsyncError",setup(){xl();const{t:e}=_l();return()=>Jr("div",{class:"uni-async-error",onClick:gv},[e("uni.async.error")],8,["onClick"])}});let vv;function yv(){return vv}function bv(e){vv=e,Object.defineProperty(vv.$.ctx,"$children",{get:()=>Xm().map((e=>e.$vm))});const t=vv.$.appContext.app;t.component(hv.name)||t.component(hv.name,hv),t.component(mv.name)||t.component(mv.name,mv),function(e){e.$vm=e,e.$mpType="app";const t=nn(_l().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(vv),function(e,t){const n=e.$options||{};n.globalData=x(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(vv),ou(),Gl()}function _v(e,{clone:t,init:n,setup:o,before:r}){t&&(e=x({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=li();n(r.proxy);const a=o(r);if(i)return i(a||e,t)},e}function wv(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?_v(e.default,t):_v(e,t)}function xv(e){return wv(e,{clone:!0,init:Km,setup(e){e.$pageInstance=e;const t=Om(),n=be(t.query);e.attrs.__pageQuery=n,e.proxy.$page.options=n;const o=Pm();var r,i,a;return Po((()=>{nv(e,o)})),Io((()=>{ov(e);const{onReady:n}=e;n&&X(n),Cv(t)})),wo((()=>{if(!e.__isVisible){nv(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&X(n),Sn((()=>{Cv(t)}))}}),"ba",r),function(e,t){wo(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;const{onHide:t}=e;t&&X(t)}})),i=o.id,Ix.subscribe(Rl(i,"invokeViewApi"),a?a(Nl):Nl),$o((()=>{!function(e){Ix.unsubscribe(Rl(e,"invokeViewApi")),Object.keys(Dl).forEach((t=>{0===t.indexOf(e+".")&&delete Dl[t]}))}(o.id)})),n}})}function Tv(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=uni.getSystemInfoSync(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";Lx.emit("onResize",{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function Sv(e){B(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&Lx.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function kv(){const{emit:e}=Lx;"visible"===document.visibilityState?e("onAppEnterForeground",Ch()):e("onAppEnterBackground")}function Cv(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&Ic("onTabItemTap",{index:n,text:t,pagePath:o})}function Ev(e){e=e>0&&e<1/0?e:0;const t=Math.floor(e/3600),n=Math.floor(e%3600/60),o=Math.floor(e%3600%60),r=(t<10?"0":"")+t;let i=(n<10?"0":"")+n+":"+((o<10?"0":"")+o);return"00"!==r&&(i=r+":"+i),i}function Av(e,t,n){const o=Vt({gestureType:"none",volumeOld:0,volumeNew:0,currentTimeOld:0,currentTimeNew:0}),r={x:0,y:0};return{state:o,onTouchstart:function(e){const t=e.targetTouches[0];r.x=t.pageX,r.y=t.pageY,o.gestureType="none",o.volumeOld=0,o.currentTimeOld=o.currentTimeNew=0},onTouchmove:function(i){function a(){i.stopPropagation(),i.preventDefault()}n.fullscreen&&a();const s=o.gestureType;if("stop"===s)return;const l=i.targetTouches[0],c=l.pageX,u=l.pageY,d=r,p=t.value;if("progress"===s?function(e){const n=t.value.duration;let r=e/600*n+o.currentTimeOld;r<0?r=0:r>n&&(r=n);o.currentTimeNew=r}(c-d.x):"volume"===s&&function(e){const n=t.value,r=o.volumeOld;let i;"number"==typeof r&&(i=r-e/200,i<0?i=0:i>1&&(i=1),n.volume=i,o.volumeNew=i)}(u-d.y),"none"===s)if(Math.abs(c-d.x)>Math.abs(u-d.y)){if(!e.enableProgressGesture)return void(o.gestureType="stop");o.gestureType="progress",o.currentTimeOld=o.currentTimeNew=p.currentTime,n.fullscreen||a()}else{if(!e.pageGesture)return void(o.gestureType="stop");o.gestureType="volume",o.volumeOld=p.volume,n.fullscreen||a()}},onTouchend:function(e){const n=t.value;"none"!==o.gestureType&&"stop"!==o.gestureType&&(e.stopPropagation(),e.preventDefault()),"progress"===o.gestureType&&o.currentTimeOld!==o.currentTimeNew&&(n.currentTime=o.currentTimeNew),o.gestureType="none"}}}const Mv=uu({name:"Video",props:{id:{type:String,default:""},src:{type:String,default:""},duration:{type:[Number,String],default:""},controls:{type:[Boolean,String],default:!0},danmuList:{type:Array,default:()=>[]},danmuBtn:{type:[Boolean,String],default:!1},enableDanmu:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},loop:{type:[Boolean,String],default:!1},muted:{type:[Boolean,String],default:!1},objectFit:{type:String,default:"contain"},poster:{type:String,default:""},direction:{type:[String,Number],default:""},showProgress:{type:Boolean,default:!0},initialTime:{type:[String,Number],default:0},showFullscreenBtn:{type:[Boolean,String],default:!0},pageGesture:{type:[Boolean,String],default:!1},enableProgressGesture:{type:[Boolean,String],default:!0},showPlayBtn:{type:[Boolean,String],default:!0},showCenterPlayBtn:{type:[Boolean,String],default:!0}},emits:["fullscreenchange","progress","loadedmetadata","waiting","error","play","pause","ended","timeupdate"],setup(e,{emit:t,attrs:n,slots:o}){const r=nn(null),i=nn(null),a=mu(r,t),{state:s}=Zh(),{$attrs:l}=pg({excludeListeners:!0}),{t:c}=_l();Il();const{videoRef:u,state:d,play:p,pause:f,seek:h,playbackRate:g,toggle:m,onDurationChange:v,onLoadedMetadata:y,onProgress:b,onWaiting:_,onVideoError:w,onPlay:x,onPause:T,onEnded:S,onTimeUpdate:k}=function(e,t,n){const o=nn(null),r=vi((()=>Cu(e.src))),i=Vt({start:!1,src:r,playing:!1,currentTime:0,duration:0,progress:0,buffered:0});function a(e){const t=e.target,n=t.buffered;n.length&&(i.buffered=n.end(n.length-1)/t.duration*100)}return Gn((()=>r.value),(()=>{i.playing=!1,i.currentTime=0})),Gn((()=>i.buffered),(e=>{n("progress",{},{buffered:e})})),{videoRef:o,state:i,play:function(){const e=o.value;i.start=!0,e.play()},pause:function(){o.value.pause()},seek:function(e){const t=o.value;"number"!=typeof(e=Number(e))||isNaN(e)||(t.currentTime=e)},playbackRate:function(e){o.value.playbackRate=e},toggle:function(){const e=o.value;i.playing?e.pause():e.play()},onDurationChange:function({target:e}){i.duration=e.duration},onLoadedMetadata:function(t){const o=Number(e.initialTime)||0,r=t.target;o>0&&(r.currentTime=o),n("loadedmetadata",t,{width:r.videoWidth,height:r.videoHeight,duration:r.duration}),a(t)},onProgress:a,onWaiting:function(e){n("waiting",e,{})},onVideoError:function(e){i.playing=!1,n("error",e,{})},onPlay:function(e){i.start=!0,i.playing=!0,n("play",e,{})},onPause:function(e){i.playing=!1,n("pause",e,{})},onEnded:function(e){i.playing=!1,n("ended",e,{})},onTimeUpdate:function(e){const t=e.target,o=i.currentTime=t.currentTime;n("timeupdate",e,{currentTime:o,duration:t.duration})}}}(e,0,a),{state:E,danmuRef:A,updateDanmu:M,toggleDanmu:P,sendDanmu:I}=function(e,t){const n=nn(null),o=Vt({enable:Boolean(e.enableDanmu)});let r={time:0,index:-1};const i=C(e.danmuList)?JSON.parse(JSON.stringify(e.danmuList)):[];function a(e){const t=document.createElement("p");t.className="uni-video-danmu-item",t.innerText=e.text;let o=`bottom: ${100*Math.random()}%;color: ${e.color};`;t.setAttribute("style",o),n.value.appendChild(t),setTimeout((function(){o+="left: 0;-webkit-transform: translateX(-100%);transform: translateX(-100%);",t.setAttribute("style",o),setTimeout((function(){t.remove()}),4e3)}),17)}return i.sort((function(e,t){return(e.time||0)-(t.time||0)})),{state:o,danmuRef:n,updateDanmu:function(e){const n=e.target.currentTime,s=r,l={time:n,index:s.index};if(n>s.time)for(let r=s.index+1;r<i.length;r++){const e=i[r];if(!(n>=(e.time||0)))break;l.index=r,t.playing&&o.enable&&a(e)}else if(n<s.time)for(let t=s.index-1;t>-1&&n<=(i[t].time||0);t--)l.index=t-1;r=l},toggleDanmu:function(){o.enable=!o.enable},sendDanmu:function(e){i.splice(r.index+1,0,{text:String(e.text),color:e.color,time:t.currentTime||0})}}}(e,d),{state:O,onFullscreenChange:L,emitFullscreenChange:$,toggleFullscreen:D,requestFullScreen:R,exitFullScreen:B}=function(e,t,n,o,r){const i=Vt({fullscreen:!1}),a=/^Apple/.test(navigator.vendor);function s(t){i.fullscreen=t,e("fullscreenchange",{},{fullScreen:t,direction:"vertical"})}function l(e){const i=r.value,l=t.value,c=n.value;let u;e?!document.fullscreenEnabled&&!document.webkitFullscreenEnabled||a&&!o.userAction?c.webkitEnterFullScreen?c.webkitEnterFullScreen():(u=!0,l.remove(),l.classList.add("uni-video-type-fullscreen"),document.body.appendChild(l)):l[document.fullscreenEnabled?"requestFullscreen":"webkitRequestFullscreen"]():document.fullscreenEnabled||document.webkitFullscreenEnabled?document.fullscreenElement?document.exitFullscreen():document.webkitFullscreenElement&&document.webkitExitFullscreen():c.webkitExitFullScreen?c.webkitExitFullScreen():(u=!0,l.remove(),l.classList.remove("uni-video-type-fullscreen"),i.appendChild(l)),u&&s(e)}function c(){l(!1)}return $o(c),{state:i,onFullscreenChange:function(e,t){t&&document.fullscreenEnabled||s(!(!document.fullscreenElement&&!document.webkitFullscreenElement))},emitFullscreenChange:s,toggleFullscreen:l,requestFullScreen:function(){l(!0)},exitFullScreen:c}}(a,i,u,s,r),{state:N,onTouchstart:q,onTouchend:j,onTouchmove:z}=Av(e,u,O),{state:V,progressRef:F,ballRef:U,clickProgress:W,toggleControls:H}=function(e,t,n){const o=nn(null),r=nn(null),i=vi((()=>e.showCenterPlayBtn&&!t.start)),a=nn(!0),s=vi((()=>!i.value&&e.controls&&a.value)),l=Vt({touching:!1,controlsTouching:!1,centerPlayBtnShow:i,controlsShow:s,controlsVisible:a});let c;function u(){c=setTimeout((()=>{l.controlsVisible=!1}),3e3)}function d(){c&&(clearTimeout(c),c=null)}return $o((()=>{c&&clearTimeout(c)})),Gn((()=>l.controlsShow&&t.playing&&!l.controlsTouching),(e=>{e?u():d()})),Gn([()=>t.currentTime,()=>{e.duration}],(function(){l.touching||(t.progress=t.currentTime/t.duration*100)})),Io((()=>{const e=me(!1);let i,a,s,c=!0;const u=r.value;function d(e){const n=e.targetTouches[0],r=n.pageX,l=n.pageY;if(c&&Math.abs(r-i)<Math.abs(l-a))return void p(e);c=!1;const u=o.value.offsetWidth;let d=s+(r-i)/u*100;d<0?d=0:d>100&&(d=100),t.progress=d,e.preventDefault(),e.stopPropagation()}function p(o){l.controlsTouching=!1,l.touching&&(u.removeEventListener("touchmove",d,e),c||(o.preventDefault(),o.stopPropagation(),n(t.duration*t.progress/100)),l.touching=!1)}u.addEventListener("touchstart",(n=>{l.controlsTouching=!0;const o=n.targetTouches[0];i=o.pageX,a=o.pageY,s=t.progress,c=!0,l.touching=!0,u.addEventListener("touchmove",d,e)})),u.addEventListener("touchend",p),u.addEventListener("touchcancel",p)})),{state:l,progressRef:o,ballRef:r,clickProgress:function(e){const r=o.value;let i=e.target,a=e.offsetX;for(;i&&i!==r;)a+=i.offsetLeft,i=i.parentNode;const s=r.offsetWidth;let l=0;a>=0&&a<=s&&(l=a/s,n(t.duration*l))},toggleControls:function(){l.controlsVisible=!l.controlsVisible},autoHideStart:u,autoHideEnd:d}}(e,d,h);return function(e,t,n,o,r,i,a){const s={play:e,pause:t,seek:n,sendDanmu:o,playbackRate:r,requestFullScreen:i,exitFullScreen:a};ym(((e,t)=>{let n;switch(e){case"seek":n=t.position;break;case"sendDanmu":n=t;break;case"playbackRate":n=t.rate}e in s&&s[e](n)}),_m(),!0)}(p,f,h,I,g,R,B),()=>Jr("uni-video",{ref:r,id:e.id},[Jr("div",{ref:i,class:"uni-video-container",onTouchstart:q,onTouchend:j,onTouchmove:z,onFullscreenchange:ba(L,["stop"]),onWebkitfullscreenchange:ba((e=>L(e,!0)),["stop"])},[Jr("video",oi({ref:u,style:{"object-fit":e.objectFit},muted:!!e.muted,loop:!!e.loop,src:d.src,poster:e.poster,autoplay:!!e.autoplay},l.value,{class:"uni-video-video","webkit-playsinline":!0,playsinline:!0,onClick:H,onDurationchange:v,onLoadedmetadata:y,onProgress:b,onWaiting:_,onError:w,onPlay:x,onPause:T,onEnded:S,onTimeupdate:e=>{k(e),M(e)},onWebkitbeginfullscreen:()=>$(!0),onX5videoenterfullscreen:()=>$(!0),onWebkitendfullscreen:()=>$(!1),onX5videoexitfullscreen:()=>$(!1)}),null,16,["muted","loop","src","poster","autoplay","webkit-playsinline","playsinline","onClick","onDurationchange","onLoadedmetadata","onProgress","onWaiting","onError","onPlay","onPause","onEnded","onTimeupdate","onWebkitbeginfullscreen","onX5videoenterfullscreen","onWebkitendfullscreen","onX5videoexitfullscreen"]),jo(Jr("div",{class:"uni-video-bar uni-video-bar-full",onClick:ba((()=>{}),["stop"])},[Jr("div",{class:"uni-video-controls"},[jo(Jr("div",{class:{"uni-video-control-button":!0,"uni-video-control-button-play":!d.playing,"uni-video-control-button-pause":d.playing},onClick:ba(m,["stop"])},null,10,["onClick"]),[[_a,e.showPlayBtn]]),jo(Jr("div",{class:"uni-video-current-time"},[Ev(d.currentTime)],512),[[_a,e.showProgress]]),jo(Jr("div",{ref:F,class:"uni-video-progress-container",onClick:ba(W,["stop"])},[Jr("div",{class:"uni-video-progress"},[Jr("div",{style:{width:d.buffered+"%"},class:"uni-video-progress-buffered"},null,4),Jr("div",{ref:U,style:{left:d.progress+"%"},class:"uni-video-ball"},[Jr("div",{class:"uni-video-inner"},null)],4)])],8,["onClick"]),[[_a,e.showProgress]]),jo(Jr("div",{class:"uni-video-duration"},[Ev(Number(e.duration)||d.duration)],512),[[_a,e.showProgress]])]),jo(Jr("div",{class:{"uni-video-danmu-button":!0,"uni-video-danmu-button-active":E.enable},onClick:ba(P,["stop"])},[c("uni.video.danmu")],10,["onClick"]),[[_a,e.danmuBtn]]),jo(Jr("div",{class:{"uni-video-fullscreen":!0,"uni-video-type-fullscreen":O.fullscreen},onClick:ba((()=>D(!O.fullscreen)),["stop"])},null,10,["onClick"]),[[_a,e.showFullscreenBtn]])],8,["onClick"]),[[_a,V.controlsShow]]),jo(Jr("div",{ref:A,style:"z-index: 0;",class:"uni-video-danmu"},null,512),[[_a,d.start&&E.enable]]),V.centerPlayBtnShow&&Jr("div",{class:"uni-video-cover",onClick:ba((()=>{}),["stop"])},[Jr("div",{class:"uni-video-cover-play-button",onClick:ba(p,["stop"])},null,8,["onClick"]),Jr("p",{class:"uni-video-cover-duration"},[Ev(Number(e.duration)||d.duration)])],8,["onClick"]),Jr("div",{class:{"uni-video-toast":!0,"uni-video-toast-volume":"volume"===N.gestureType}},[Jr("div",{class:"uni-video-toast-title"},[c("uni.video.volume")]),Jr("svg",{class:"uni-video-toast-icon",width:"200px",height:"200px",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},[Jr("path",{d:"M475.400704 201.19552l0 621.674496q0 14.856192-10.856448 25.71264t-25.71264 10.856448-25.71264-10.856448l-190.273536-190.273536-149.704704 0q-14.856192 0-25.71264-10.856448t-10.856448-25.71264l0-219.414528q0-14.856192 10.856448-25.71264t25.71264-10.856448l149.704704 0 190.273536-190.273536q10.856448-10.856448 25.71264-10.856448t25.71264 10.856448 10.856448 25.71264zm219.414528 310.837248q0 43.425792-24.28416 80.851968t-64.2816 53.425152q-5.71392 2.85696-14.2848 2.85696-14.856192 0-25.71264-10.570752t-10.856448-25.998336q0-11.999232 6.856704-20.284416t16.570368-14.2848 19.427328-13.142016 16.570368-20.284416 6.856704-32.569344-6.856704-32.569344-16.570368-20.284416-19.427328-13.142016-16.570368-14.2848-6.856704-20.284416q0-15.427584 10.856448-25.998336t25.71264-10.570752q8.57088 0 14.2848 2.85696 39.99744 15.427584 64.2816 53.139456t24.28416 81.137664zm146.276352 0q0 87.422976-48.56832 161.41824t-128.5632 107.707392q-7.428096 2.85696-14.2848 2.85696-15.427584 0-26.284032-10.856448t-10.856448-25.71264q0-22.284288 22.284288-33.712128 31.997952-16.570368 43.425792-25.141248 42.283008-30.855168 65.995776-77.423616t23.712768-99.136512-23.712768-99.136512-65.995776-77.423616q-11.42784-8.57088-43.425792-25.141248-22.284288-11.42784-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 79.99488 33.712128 128.5632 107.707392t48.56832 161.41824zm146.276352 0q0 131.42016-72.566784 241.41312t-193.130496 161.989632q-7.428096 2.85696-14.856192 2.85696-14.856192 0-25.71264-10.856448t-10.856448-25.71264q0-20.570112 22.284288-33.712128 3.999744-2.285568 12.85632-5.999616t12.85632-5.999616q26.284032-14.2848 46.854144-29.140992 70.281216-51.996672 109.707264-129.705984t39.426048-165.132288-39.426048-165.132288-109.707264-129.705984q-20.570112-14.856192-46.854144-29.140992-3.999744-2.285568-12.85632-5.999616t-12.85632-5.999616q-22.284288-13.142016-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 120.563712 51.996672 193.130496 161.989632t72.566784 241.41312z"},null)]),Jr("div",{class:"uni-video-toast-value"},[Jr("div",{style:{width:100*N.volumeNew+"%"},class:"uni-video-toast-value-content"},[Jr("div",{class:"uni-video-toast-volume-grids"},[Yo(10,(()=>Jr("div",{class:"uni-video-toast-volume-grids-item"},null)))])],4)])],2),Jr("div",{class:{"uni-video-toast":!0,"uni-video-toast-progress":"progress"===N.gestureType}},[Jr("div",{class:"uni-video-toast-title"},[Ev(N.currentTimeNew)," / ",Ev(d.duration)])],2),Jr("div",{class:"uni-video-slots"},[o.default&&o.default()])],40,["onTouchstart","onTouchend","onTouchmove","onFullscreenchange","onWebkitfullscreenchange"])],8,["id"])}});let Pv,Iv=0;function Ov(e,t,n,o){var r,i=document.createElement("script"),a=t.callback||"callback",s="__uni_jsonp_callback_"+Iv++,l=t.timeout||3e4;function c(){clearTimeout(r),delete window[s],i.remove()}window[s]=e=>{P(n)&&n(e),c()},i.onerror=()=>{P(o)&&o(),c()},r=setTimeout((function(){P(o)&&o(),c()}),l),i.src=e+(e.indexOf("?")>=0?"&":"?")+a+"="+s,document.body.appendChild(i)}function Lv(e){function t(){const e=this.div;this.getPanes().floatPane.appendChild(e)}function n(){const e=this.div.parentNode;e&&e.removeChild(this.div)}function o(){const t=this.option;this.Text=new e.Text({text:t.content,anchor:"bottom-center",offset:new e.Pixel(0,t.offsetY-16),style:{padding:(t.padding||8)+"px","line-height":(t.fontSize||14)+"px","border-radius":(t.borderRadius||0)+"px","border-color":`${t.bgColor||"#fff"} transparent transparent`,"background-color":t.bgColor||"#fff","box-shadow":"0 2px 6px 0 rgba(114, 124, 245, .5)","text-align":"center","font-size":(t.fontSize||14)+"px",color:t.color||"#000"},position:t.position});(e.event||e.Event).addListener(this.Text,"click",(()=>{this.callback()})),this.Text.setMap(t.map)}function r(){}function i(){this.Text&&this.option.map.remove(this.Text)}function a(){this.Text&&this.option.map.remove(this.Text)}class s{constructor(e={},s){this.createAMapText=o,this.removeAMapText=i,this.createBMapText=r,this.removeBMapText=a,this.onAdd=t,this.construct=t,this.onRemove=n,this.destroy=n,this.option=e||{};const l=this.visible=this.alwaysVisible="ALWAYS"===e.display;if(Uv())this.callback=s,this.visible&&this.createAMapText();else if(Wv())this.visible&&this.createBMapText();else{const t=e.map;this.position=e.position,this.index=1;const n=this.div=document.createElement("div"),o=n.style;o.position="absolute",o.whiteSpace="nowrap",o.transform="translateX(-50%) translateY(-100%)",o.zIndex="1",o.boxShadow=e.boxShadow||"none",o.display=l?"block":"none";const r=this.triangle=document.createElement("div");r.setAttribute("style","position: absolute;white-space: nowrap;border-width: 4px;border-style: solid;border-color: #fff transparent transparent;border-image: initial;font-size: 12px;padding: 0px;background-color: transparent;width: 0px;height: 0px;transform: translate(-50%, 100%);left: 50%;bottom: 0;"),this.setStyle(e),n.appendChild(r),t&&this.setMap(t)}}set onclick(e){this.div.onclick=e}get onclick(){return this.div.onclick}setOption(e){this.option=e,"ALWAYS"===e.display?this.alwaysVisible=this.visible=!0:this.alwaysVisible=!1,Uv()?this.visible&&this.createAMapText():Wv()?this.visible&&this.createBMapText():(this.setPosition(e.position),this.setStyle(e))}setStyle(e){const t=this.div,n=t.style;t.innerText=e.content||"",n.lineHeight=(e.fontSize||14)+"px",n.fontSize=(e.fontSize||14)+"px",n.padding=(e.padding||8)+"px",n.color=e.color||"#000",n.borderRadius=(e.borderRadius||0)+"px",n.backgroundColor=e.bgColor||"#fff",n.marginTop="-"+((e.top||0)+5)+"px",this.triangle.style.borderColor=`${e.bgColor||"#fff"} transparent transparent`}setPosition(e){this.position=e,this.draw()}draw(){const e=this.getProjection();if(!this.position||!this.div||!e)return;const t=e.fromLatLngToDivPixel(this.position),n=this.div.style;n.left=t.x+"px",n.top=t.y+"px"}changed(){this.div.style.display=this.visible?"block":"none"}}if(!Uv()&&!Wv()){const t=new(e.OverlayView||e.Overlay);s.prototype.setMap=t.setMap,s.prototype.getMap=t.getMap,s.prototype.getPanes=t.getPanes,s.prototype.getProjection=t.getProjection,s.prototype.map_changed=t.map_changed,s.prototype.set=t.set,s.prototype.get=t.get,s.prototype.setOptions=t.setValues,s.prototype.bindTo=t.bindTo,s.prototype.bindsTo=t.bindsTo,s.prototype.notify=t.notify,s.prototype.setValues=t.setValues,s.prototype.unbind=t.unbind,s.prototype.unbindAll=t.unbindAll,s.prototype.addListener=t.addListener}return s}const $v={};function Dv(e,t){const n=zv();if(!n.key)return void console.error("Map key not configured.");const o=$v[n.type]=$v[n.type]||[];if(Pv)t(Pv);else if(window[n.type]&&window[n.type].maps)Pv=Uv()||Wv()?window[n.type]:window[n.type].maps,Pv.Callout=Pv.Callout||Lv(Pv),t(Pv);else if(o.length)o.push(t);else{o.push(t);const r=window,i="__map_callback__"+n.type;r[i]=function(){delete r[i],Pv=Uv()||Wv()?window[n.type]:window[n.type].maps,Pv.Callout=Lv(Pv),o.forEach((e=>e(Pv))),o.length=0},Uv()&&function(e){window._AMapSecurityConfig={securityJsCode:e.securityJsCode||"",serviceHost:e.serviceHost||""}}(n);const a=document.createElement("script");let s=Rv(n.type);n.type===jv.QQ&&e.push("geometry"),e.length&&(s+=`libraries=${e.join("%2C")}&`),n.type===jv.BMAP?a.src=`${s}ak=${n.key}&callback=${i}`:a.src=`${s}key=${n.key}&callback=${i}`,a.onerror=function(){console.error("Map load failed.")},document.body.appendChild(a)}}const Rv=e=>({qq:"https://map.qq.com/api/js?v=2.exp&",google:"https://maps.googleapis.com/maps/api/js?",AMap:"https://webapi.amap.com/maps?v=2.0&",BMapGL:"https://api.map.baidu.com/api?type=webgl&v=1.0&"}[e]);const Bv="M13.3334375 16 q0.033125 1.1334375 0.783125 1.8834375 q0.75 0.75 1.8834375 0.75 q1.1334375 0 1.8834375 -0.75 q0.75 -0.75 0.75 -1.8834375 q0 -1.1334375 -0.75 -1.8834375 q-0.75 -0.75 -1.8834375 -0.75 q-1.1334375 0 -1.8834375 0.75 q-0.75 0.75 -0.783125 1.8834375 ZM30.9334375 14.9334375 l-1.1334375 0 q-0.5 -5.2 -4.0165625 -8.716875 q-3.516875 -3.5165625 -8.716875 -4.0165625 l0 -1.1334375 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 l0 1.1334375 q-5.2 0.5 -8.716875 4.0165625 q-3.5165625 3.516875 -4.0165625 8.716875 l-1.1334375 0 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 l1.1334375 0 q0.5 5.2 4.0165625 8.716875 q3.516875 3.5165625 8.716875 4.0165625 l0 1.1334375 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 l0 -1.1334375 q5.2 -0.5 8.716875 -4.0165625 q3.5165625 -3.516875 4.0165625 -8.716875 l1.1334375 0 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 ZM17.0665625 27.6665625 l0 -2.0665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 l0 2.0665625 q-4.3 -0.4665625 -7.216875 -3.383125 q-2.916875 -2.916875 -3.3834375 -7.216875 l2.0665625 0 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 l-2.0665625 0 q0.4665625 -4.3 3.3834375 -7.216875 q2.9165625 -2.916875 7.216875 -3.3834375 l0 2.0665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 l0 -2.0665625 q4.3 0.4665625 7.216875 3.3834375 q2.9165625 2.9165625 3.383125 7.216875 l-2.0665625 0 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 l2.0665625 0 q-0.4665625 4.3 -3.383125 7.216875 q-2.916875 2.9165625 -7.216875 3.383125 Z",Nv="data:image/png;base64,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",qv="data:image/png;base64,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";var jv=(e=>(e.QQ="qq",e.GOOGLE="google",e.AMAP="AMap",e.BMAP="BMapGL",e.UNKNOWN="",e))(jv||{});function zv(){return __uniConfig.bMapKey?{type:"BMapGL",key:__uniConfig.bMapKey}:__uniConfig.qqMapKey?{type:"qq",key:__uniConfig.qqMapKey}:__uniConfig.googleMapKey?{type:"google",key:__uniConfig.googleMapKey}:__uniConfig.aMapKey?{type:"AMap",key:__uniConfig.aMapKey,securityJsCode:__uniConfig.aMapSecurityJsCode,serviceHost:__uniConfig.aMapServiceHost}:{type:"",key:""}}let Vv=!1,Fv=!1;const Uv=()=>Fv?Vv:(Fv=!0,Vv="AMap"===zv().type),Wv=()=>"BMapGL"===zv().type;function Hv(e,t,n){const o=zv();return e&&"WGS84"===e.toUpperCase()||["google"].includes(o.type)||n?Promise.resolve(t):"qq"===o.type?new Promise((e=>{Ov(`https://apis.map.qq.com/jsapi?qt=translate&type=1&points=${t.longitude},${t.latitude}&key=${o.key}&output=jsonp&pf=jsapi&ref=jsapi`,{callback:"cb"},(n=>{if("detail"in n&&"points"in n.detail&&n.detail.points.length){const{lng:o,lat:r}=n.detail.points[0];e({longitude:o,latitude:r,altitude:t.altitude,accuracy:t.accuracy,altitudeAccuracy:t.altitudeAccuracy,heading:t.heading,speed:t.speed})}else e(t)}),(()=>e(t)))})):"AMap"===o.type?new Promise((e=>{Dv([],(()=>{window.AMap.convertFrom([t.longitude,t.latitude],"gps",((n,o)=>{if("ok"===o.info&&o.locations.length){const{lat:n,lng:r}=o.locations[0];e({longitude:r,latitude:n,altitude:t.altitude,accuracy:t.accuracy,altitudeAccuracy:t.altitudeAccuracy,heading:t.heading,speed:t.speed})}else e(t)}))}))})):Promise.reject(new Error("translate coordinate system faild"))}const Yv=du({name:"MapMarker",props:{id:{type:[Number,String],default:""},latitude:{type:[Number,String],require:!0},longitude:{type:[Number,String],require:!0},title:{type:String,default:""},iconPath:{type:String,require:!0},rotate:{type:[Number,String],default:0},alpha:{type:[Number,String],default:1},width:{type:[Number,String],default:""},height:{type:[Number,String],default:""},callout:{type:Object,default:null},label:{type:Object,default:null},anchor:{type:Object,default:null},clusterId:{type:[Number,String],default:""},customCallout:{type:Object,default:null},ariaLabel:{type:String,default:""}},setup(e){const t=String(isNaN(Number(e.id))?"":e.id),n=Hn("onMapReady"),o=function(e){const t="uni-map-marker-label-"+e,n=document.createElement("style");return n.id=t,document.head.appendChild(n),Do((()=>{n.remove()})),function(e){const o=Object.assign({},e,{position:"absolute",top:"70px",borderStyle:"solid"}),r=document.createElement("div");return Object.keys(o).forEach((e=>{r.style[e]=o[e]||""})),n.innerText=`.${t}{${r.getAttribute("style")}}`,t}}(t);let r;function i(e){Uv()?e.removeAMapText():e.setMap(null)}if(n(((n,a,s)=>{function l(e){const l=e.title;let c;c=Uv()?new a.LngLat(e.longitude,e.latitude):Wv()?new a.Point(e.longitude,e.latitude):new a.LatLng(e.latitude,e.longitude);const u=new Image;let d=0;u.onload=()=>{const p=e.anchor||{};let f,h,g,m,v="number"==typeof p.x?p.x:.5,y="number"==typeof p.y?p.y:1;e.iconPath&&(e.width||e.height)?(h=e.width||u.width/u.height*e.height,g=e.height||u.height/u.width*e.width):(h=u.width/2,g=u.height/2),d=g,m=g-(g-y*g),f="MarkerImage"in a?new a.MarkerImage(u.src,null,null,new a.Point(v*h,y*g),new a.Size(h,g)):"Icon"in a?new a.Icon({image:u.src,size:new a.Size(h,g),imageSize:new a.Size(h,g),imageOffset:new a.Pixel(v*h,y*g)}):{url:u.src,anchor:new a.Point(v,y),size:new a.Size(h,g)},Wv()?(r=new a.Marker(new a.Point(c.lng,c.lat)),n.addOverlay(r)):(r.setPosition(c),r.setIcon(f)),"setRotation"in r&&r.setRotation(e.rotate||0);const b=e.label||{};let _;if("label"in r&&(r.label.setMap(null),delete r.label),b.content){const e={borderColor:b.borderColor,borderWidth:(Number(b.borderWidth)||0)+"px",padding:(Number(b.padding)||0)+"px",borderRadius:(Number(b.borderRadius)||0)+"px",backgroundColor:b.bgColor,color:b.color,fontSize:(b.fontSize||14)+"px",lineHeight:(b.fontSize||14)+"px",marginLeft:(Number(b.anchorX||b.x)||0)+"px",marginTop:(Number(b.anchorY||b.y)||0)+"px"};if("Label"in a)_=new a.Label({position:c,map:n,clickable:!1,content:b.content,style:e}),r.label=_;else if("setLabel"in r)if(Uv()){const t=`<div style="\n                  margin-left:${e.marginLeft};\n                  margin-top:${e.marginTop};\n                  padding:${e.padding};\n                  background-color:${e.backgroundColor};\n                  border-radius:${e.borderRadius};\n                  line-height:${e.lineHeight};\n                  color:${e.color};\n                  font-size:${e.fontSize};\n\n                  ">\n                  ${b.content}\n                <div>`;r.setLabel({content:t,direction:"bottom-right"})}else{const t=o(e);r.setLabel({text:b.content,color:e.color,fontSize:e.fontSize,className:t})}}const w=e.callout||{};let x,T=r.callout;if(w.content||l){Uv()&&w.content&&(w.content=w.content.replaceAll("\n","<br/>"));const o="0px 0px 3px 1px rgba(0,0,0,0.5)";let i=-d/2;if((e.width||e.height)&&(i+=14-d/2),x=w.content?{position:c,map:n,top:m,offsetY:i,content:w.content,color:w.color,fontSize:w.fontSize,borderRadius:w.borderRadius,bgColor:w.bgColor,padding:w.padding,boxShadow:w.boxShadow||o,display:w.display}:{position:c,map:n,top:m,offsetY:i,content:l,boxShadow:o},T)T.setOption(x);else if(Uv()){const e=e=>{""!==e&&s("callouttap",{},{markerId:Number(e)})};T=r.callout=new a.Callout(x,e)}else T=r.callout=new a.Callout(x),T.div.onclick=function(e){""!==t&&s("callouttap",e,{markerId:Number(t)}),e.stopPropagation(),e.preventDefault()},zv().type===jv.GOOGLE&&(T.div.ontouchstart=function(e){e.stopPropagation()},T.div.onpointerdown=function(e){e.stopPropagation()})}else T&&(i(T),delete r.callout)},e.iconPath?u.src=Cu(e.iconPath):console.error("Marker.iconPath is required.")}!function(e){Wv()||(r=new a.Marker({map:n,flat:!0,autoRotation:!1})),l(e);const o=a.event||a.Event;Wv()||o.addListener(r,"click",(()=>{const n=r.callout;if(n&&!n.alwaysVisible)if(Uv())n.visible=!n.visible,n.visible?r.callout.createAMapText():r.callout.removeAMapText();else if(n.set("visible",!n.visible),n.visible){const e=n.div,t=e.parentNode;t.removeChild(e),t.appendChild(e)}t&&s("markertap",{},{markerId:Number(t),latitude:e.latitude,longitude:e.longitude})}))}(e),Gn(e,l)})),t){const e=Hn("addMapChidlContext"),o=Hn("removeMapChidlContext"),i={id:t,translate(e){n(((t,n,o)=>{const i=e.destination,a=e.duration,s=!!e.autoRotate;let l=Number(e.rotate)||0,c=0;"getRotation"in r&&(c=r.getRotation());const u=r.getPosition(),d=new n.LatLng(i.latitude,i.longitude),p=n.geometry.spherical.computeDistanceBetween(u,d)/1e3/(("number"==typeof a?a:1e3)/36e5),f=n.event||n.Event,h=f.addListener(r,"moving",(e=>{const t=e.latLng,n=r.label;n&&n.setPosition(t);const o=r.callout;o&&o.setPosition(t)})),g=f.addListener(r,"moveend",(()=>{g.remove(),h.remove(),r.lastPosition=u,r.setPosition(d);const t=r.label;t&&t.setPosition(d);const n=r.callout;n&&n.setPosition(d);const o=e.animationEnd;P(o)&&o()}));let m=0;s&&(r.lastPosition&&(m=n.geometry.spherical.computeHeading(r.lastPosition,u)),l=n.geometry.spherical.computeHeading(u,d)-m),"setRotation"in r&&r.setRotation(c+l),"moveTo"in r?r.moveTo(d,p):(r.setPosition(d),f.trigger(r,"moveend",{}))}))}};e(i),Do((()=>o(i)))}return Do((function(){r&&(r.label&&"setMap"in r.label&&r.label.setMap(null),r.callout&&i(r.callout),r.setMap(null))})),()=>null}});function Xv(e){if(!e)return{r:0,g:0,b:0,a:0};let t=e.slice(1);const n=t.length;if(![3,4,6,8].includes(n))return{r:0,g:0,b:0,a:0};3!==n&&4!==n||(t=t.replace(/(\w{1})/g,"$1$1"));let[o,r,i,a]=t.match(/(\w{2})/g);const s=parseInt(o,16),l=parseInt(r,16),c=parseInt(i,16);return a?{r:s,g:l,b:c,a:(`0x100${a}`-65536)/255}:{r:s,g:l,b:c,a:1}}const Gv={points:{type:Array,require:!0},color:{type:String,default:"#000000"},width:{type:[Number,String],default:""},dottedLine:{type:[Boolean,String],default:!1},arrowLine:{type:[Boolean,String],default:!1},arrowIconPath:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderWidth:{type:[Number,String],default:""},colorList:{type:Array,default:()=>[]},level:{type:String,default:""}},Jv=du({name:"MapPolyline",props:Gv,setup(e){let t,n;function o(){t&&t.setMap(null),n&&n.setMap(null)}return Hn("onMapReady")(((r,i)=>{function a(e){const o=[];e.points.forEach((e=>{let t;t=Uv()?[e.longitude,e.latitude]:Wv()?new i.Point(e.longitude,e.latitude):new i.LatLng(e.latitude,e.longitude),o.push(t)}));const a=Number(e.width)||1,{r:s,g:l,b:c,a:u}=Xv(e.color),{r:d,g:p,b:f,a:h}=Xv(e.borderColor),g={map:r,clickable:!1,path:o,strokeWeight:a,strokeColor:e.color||void 0,strokeDashStyle:e.dottedLine?"dash":"solid"},m=Number(e.borderWidth)||0,v={map:r,clickable:!1,path:o,strokeWeight:a+2*m,strokeColor:e.borderColor||void 0,strokeDashStyle:e.dottedLine?"dash":"solid"};"Color"in i?(g.strokeColor=new i.Color(s,l,c,u),v.strokeColor=new i.Color(d,p,f,h)):(g.strokeColor=`rgb(${s}, ${l}, ${c})`,g.strokeOpacity=u,v.strokeColor=`rgb(${d}, ${p}, ${f})`,v.strokeOpacity=h),m&&(n=new i.Polyline(v)),Wv()?(t=new i.Polyline(g.path,g),r.addOverlay(t)):t=new i.Polyline(g)}a(e),Gn(e,(function(e){o(),a(e)}))})),Do(o),()=>null}}),Qv=du({name:"MapCircle",props:{latitude:{type:[Number,String],require:!0},longitude:{type:[Number,String],require:!0},color:{type:String,default:"#000000"},fillColor:{type:String,default:"#00000000"},radius:{type:[Number,String],require:!0},strokeWidth:{type:[Number,String],default:""},level:{type:String,default:""}},setup(e){let t;function n(){t&&t.setMap(null)}return Hn("onMapReady")(((o,r)=>{function i(e){const n=Uv()||Wv()?[e.longitude,e.latitude]:new r.LatLng(e.latitude,e.longitude),i={map:o,center:n,clickable:!1,radius:e.radius,strokeWeight:Number(e.strokeWidth)||1,strokeDashStyle:"solid"};if(Wv())i.strokeColor=e.color,i.fillColor=e.fillColor||"#000",i.fillOpacity=1;else{const{r:t,g:n,b:o,a:a}=Xv(e.fillColor),{r:s,g:l,b:c,a:u}=Xv(e.color);"Color"in r?(i.fillColor=new r.Color(t,n,o,a),i.strokeColor=new r.Color(s,l,c,u)):(i.fillColor=`rgb(${t}, ${n}, ${o})`,i.fillOpacity=a,i.strokeColor=`rgb(${s}, ${l}, ${c})`,i.strokeOpacity=u)}if(Wv()){let e=new r.Point(i.center[0],i.center[1]);t=new r.Circle(e,i.radius,i),o.addOverlay(t)}else t=new r.Circle(i),Uv()&&o.add(t)}i(e),Gn(e,(function(e){n(),i(e)}))})),Do(n),()=>null}}),Kv={id:{type:[Number,String],default:""},position:{type:Object,required:!0},iconPath:{type:String,required:!0},clickable:{type:[Boolean,String],default:""},trigger:{type:Function,required:!0}},Zv=du({name:"MapControl",props:Kv,setup(e){const t=vi((()=>Cu(e.iconPath))),n=vi((()=>{let t=`top:${e.position.top||0}px;left:${e.position.left||0}px;`;return e.position.width&&(t+=`width:${e.position.width}px;`),e.position.height&&(t+=`height:${e.position.height}px;`),t})),o=t=>{e.clickable&&e.trigger("controltap",t,{controlId:e.id})};return()=>Jr("div",{class:"uni-map-control"},[Jr("img",{src:t.value,style:n.value,class:"uni-map-control-icon",onClick:o},null,12,["src","onClick"])])}}),ey=le((()=>{Ep.forEach((e=>{ty.prototype[e]=function(t){P(t)&&this._events[e].push(t)}})),Ap.forEach((e=>{ty.prototype[e]=function(t){var n=this._events[e.replace("off","on")],o=n.indexOf(t);o>=0&&n.splice(o,1)}}))}));class ty{constructor(){this._src="";var e=this._audio=new Audio;this._stoping=!1;["src","autoplay","loop","duration","currentTime","paused","volume"].forEach((t=>{Object.defineProperty(this,t,{set:"src"===t?t=>(e.src=Cu(t),this._src=t,t):n=>(e[t]=n,n),get:"src"===t?()=>this._src:()=>e[t]})})),this.startTime=0,Object.defineProperty(this,"obeyMuteSwitch",{set:()=>!1,get:()=>!1}),Object.defineProperty(this,"buffered",{get(){var t=e.buffered;return t.length?t.end(t.length-1):0}}),this._events={},Ep.forEach((e=>{this._events[e]=[]})),e.addEventListener("loadedmetadata",(()=>{var t=Number(this.startTime)||0;t>0&&(e.currentTime=t)}));var t=["canplay","pause","seeking","seeked","timeUpdate"];t.concat(["play","ended","error","waiting"]).forEach((n=>{e.addEventListener(n.toLowerCase(),(()=>{if(this._stoping&&t.indexOf(n)>=0)return;const e=`on${n.slice(0,1).toUpperCase()}${n.slice(1)}`;this._events[e].forEach((e=>{e()}))}),!1)})),ey()}play(){this._stoping=!1,this._audio.play()}pause(){this._audio.pause()}stop(){this._stoping=!0,this._audio.pause(),this._audio.currentTime=0,this._events.onStop.forEach((e=>{e()}))}seek(e){this._stoping=!1,"number"!=typeof(e=Number(e))||isNaN(e)||(this._audio.currentTime=e)}destroy(){this.stop()}}const ny=Cd(0,(()=>new ty)),oy=Ed("makePhoneCall",(({phoneNumber:e},{resolve:t})=>(window.location.href=`tel:${e}`,t()))),ry=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let iy;function ay(){if(iy=iy||ry.__DC_STAT_UUID,!iy){iy=Date.now()+""+Math.floor(1e7*Math.random());try{ry.__DC_STAT_UUID=iy}catch(e){}}return iy}function sy(){if(!0!==__uniConfig.darkmode)return I(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function ly(){let e,t="0",n="",o="phone";const r=navigator.language;if(Mu){e="iOS";const o=Eu.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=Eu.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(Au){e="Android";const o=Eu.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=Eu.match(/\((.+?)\)/),i=r?r[1].split(";"):Eu.split(" "),a=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<a.length;e++)if(a[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(Lu)n="iPad",e="iOS",o="pad",t=P(window.BigInt)?"14.0":"13.0";else if(Pu||Iu||Ou){n="PC",e="PC",o="pc",t="0";let r=Eu.match(/\((.+?)\)/)[1];if(Pu){switch(e="Windows",Pu[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(Iu){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(Ou){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,a=e.toLocaleLowerCase();let s="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)s="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(Eu)&&(s=t[n],l=Eu.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:a,browserName:s.toLocaleLowerCase(),browserVersion:l,language:r,deviceType:o,ua:Eu,osname:e,osversion:t,theme:sy()}}const cy=Cd(0,(()=>{const e=window.devicePixelRatio,t=$u(),n=Du(t),o=Ru(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=Bu(o);let a=window.innerHeight;const s=ac.top,l={left:ac.left,right:i-ac.right,top:ac.top,bottom:a-ac.bottom,width:i-ac.left-ac.right,height:a-ac.top-ac.bottom},{top:c,bottom:u}=dc();return a-=c,a-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:a,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:s,safeArea:l,safeAreaInsets:{top:ac.top,right:ac.right,bottom:ac.bottom,left:ac.left},screenTop:r-a}}));let uy,dy=!0;function py(){dy&&(uy=ly())}const fy=Cd(0,(()=>{py();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:a,deviceType:s}=uy;return{brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:ay(),deviceOrientation:a,deviceType:s,model:o,platform:r,system:i}})),hy=Cd(0,(()=>{py();const{theme:e,language:t,browserName:n,browserVersion:o}=uy;return{appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:Zp?Zp():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:""}})),gy=Cd(0,(()=>{dy=!0,py(),dy=!1;const e=cy(),t=fy(),n=hy();dy=!0;const{ua:o,browserName:r,browserVersion:i,osname:a,osversion:s}=uy,l=x(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:a.toLocaleLowerCase(),osVersion:s,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return B(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)})),my=Ed("getSystemInfo",((e,{resolve:t})=>t(gy())));function vy(){wy().then((({networkType:e})=>{Lx.invokeOnCallback("onNetworkStatusChange",{isConnected:"none"!==e,networkType:e})}))}function yy(){return navigator.connection||navigator.webkitConnection||navigator.mozConnection}const by=Td("onNetworkStatusChange",(()=>{const e=yy();e?e.addEventListener("change",vy):(window.addEventListener("offline",vy),window.addEventListener("online",vy))})),_y=Sd("offNetworkStatusChange",(()=>{const e=yy();e?e.removeEventListener("change",vy):(window.removeEventListener("offline",vy),window.removeEventListener("online",vy))})),wy=Ed("getNetworkType",((e,{resolve:t})=>{const n=yy();let o="unknown";return n?(o=n.type,"cellular"===o&&n.effectiveType?o=n.effectiveType.replace("slow-",""):["none","wifi"].includes(o)||(o="unknown")):!1===navigator.onLine&&(o="none"),t({networkType:o})}));let xy=null;const Ty=Td("onAccelerometer",(()=>{ky()})),Sy=Sd("offAccelerometer",(()=>{Cy()})),ky=Ed("startAccelerometer",((e,{resolve:t,reject:n})=>{if(window.DeviceMotionEvent){if(!xy){if(DeviceMotionEvent.requestPermission)return void DeviceMotionEvent.requestPermission().then((e=>{"granted"===e?(o(),t()):n(`${e}`)})).catch((e=>{n(`${e}`)}));o()}t()}else n();function o(){xy=function(e){const t=e.acceleration||e.accelerationIncludingGravity;Lx.invokeOnCallback("onAccelerometer",{x:t&&t.x||0,y:t&&t.y||0,z:t&&t.z||0})},window.addEventListener("devicemotion",xy,!1)}})),Cy=Ed("stopAccelerometer",((e,{resolve:t})=>{xy&&(window.removeEventListener("devicemotion",xy,!1),xy=null),t()}));let Ey=null;const Ay=Td("onCompass",(()=>{Py()})),My=Sd("offCompass",(()=>{Iy()})),Py=Ed("startCompass",((e,{resolve:t,reject:n})=>{if(window.DeviceOrientationEvent){if(!Ey){if(DeviceOrientationEvent.requestPermission)return void DeviceOrientationEvent.requestPermission().then((e=>{"granted"===e?(o(),t()):n(`${e}`)})).catch((e=>{n(`${e}`)}));o()}t()}else n();function o(){Ey=function(e){const t=360-(null!==e.alpha?e.alpha:360);Lx.invokeOnCallback("onCompass",{direction:t})},window.addEventListener("deviceorientation",Ey,!1)}})),Iy=Ed("stopCompass",((e,{resolve:t})=>{Ey&&(window.removeEventListener("deviceorientation",Ey,!1),Ey=null),t()})),Oy=!!window.navigator.vibrate,Ly=Ed("vibrateShort",((e,{resolve:t,reject:n})=>{Oy&&window.navigator.vibrate(15)?t():n("vibrateLong:fail")})),$y=Ed("vibrateLong",((e,{resolve:t,reject:n})=>{Oy&&window.navigator.vibrate(400)?t():n("vibrateLong:fail")}));var Dy=(e,t,n)=>new Promise(((o,r)=>{var i=e=>{try{s(n.next(e))}catch(kC){r(kC)}},a=e=>{try{s(n.throw(e))}catch(kC){r(kC)}},s=e=>e.done?o(e.value):Promise.resolve(e.value).then(i,a);s((n=n.apply(e,t)).next())}));const Ry=Ed("getClipboardData",((e,t)=>Dy(void 0,[e,t],(function*(e,{resolve:t,reject:n}){Ml();const{t:o}=_l();try{t({data:yield navigator.clipboard.readText()})}catch(r){!function(e,t){const n=document.getElementById("#clipboard"),o=n?n.value:void 0;o?e({data:o}):t()}(t,(()=>{n(`${r} ${o("uni.getClipboardData.fail")}`)}))}})))),By=Ed("setClipboardData",((e,t)=>Dy(void 0,[e,t],(function*({data:e},{resolve:t,reject:n}){try{yield navigator.clipboard.writeText(e),t()}catch(o){!function(e,t,n){const o=document.getElementById("#clipboard");o&&o.remove();const r=document.createElement("textarea");r.id="#clipboard",r.style.position="fixed",r.style.top="-9999px",r.style.zIndex="-9999",document.body.appendChild(r),r.value=e,r.select(),r.setSelectionRange(0,r.value.length);const i=document.execCommand("Copy",!1);r.blur(),i?t():n()}(e,t,n)}}))),0,yf);const Ny=e=>{Lx.invokeOnCallback("onThemeChange",e)},qy=Td("onThemeChange",(()=>{Lx.on("onThemeChange",Ny)})),jy=Sd("offThemeChange",(()=>{Lx.off("onThemeChange",Ny)}));const zy=Cd(0,((e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)})),Vy=Ed("setStorage",(({key:e,data:t},{resolve:n,reject:o})=>{try{zy(e,t),n()}catch(r){o(r.message)}}));function Fy(e){const t=localStorage&&localStorage.getItem(e);if(!I(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=I(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const Uy=Cd(0,(e=>{try{return Fy(e)}catch(t){return""}})),Wy=Ed("getStorage",(({key:e},{resolve:t,reject:n})=>{try{t({data:Fy(e)})}catch(o){n(o.message)}})),Hy=Cd(0,(e=>{localStorage&&localStorage.removeItem(e)})),Yy=Ed("removeStorage",(({key:e},{resolve:t})=>{Hy(e),t()})),Xy=Cd(0,(()=>{localStorage&&localStorage.clear()})),Gy=Ed("clearStorage",((e,{resolve:t})=>{Xy(),t()})),Jy=Cd(0,(()=>{const e=localStorage&&localStorage.length||0,t=[];let n=0;for(let o=0;o<e;o++){const e=localStorage.key(o),r=localStorage.getItem(e)||"";n+=e.length+r.length,"uni-storage-keys"!==e&&t.push(e)}return{keys:t,currentSize:Math.ceil(2*n/1024),limitSize:Number.MAX_VALUE}})),Qy=Ed("getStorageInfo",((e,{resolve:t})=>{t(Jy())})),Ky=Ed("getFileInfo",(({filePath:e},{resolve:t,reject:n})=>{_h(e).then((e=>{t({size:e.size})})).catch((e=>{n(String(e))}))}),0,bf),Zy=Ed("openDocument",(({filePath:e},{resolve:t})=>(window.open(e),t())),0,_f),eb=Ed("hideKeyboard",((e,{resolve:t,reject:n})=>{const o=document.activeElement;!o||"TEXTAREA"!==o.tagName&&"INPUT"!==o.tagName||(o.blur(),t())}));const tb=Ed("getImageInfo",(({src:e},{resolve:t,reject:n})=>{const o=new Image;o.onload=function(){t({width:o.naturalWidth,height:o.naturalHeight,path:0===e.indexOf("/")?window.location.protocol+"//"+window.location.host+e:e})},o.onerror=function(){n()},o.src=e}),0,Mf),nb=Ed("getVideoInfo",(({src:e},{resolve:t,reject:n})=>{_h(e,!0).then((e=>e)).catch((()=>null)).then((o=>{const r=document.createElement("video");if(void 0!==r.onloadedmetadata){const i=setTimeout((()=>{r.onloadedmetadata=null,r.onerror=null,n()}),e.startsWith("data:")||e.startsWith("blob:")?300:3e3);r.onloadedmetadata=function(){clearTimeout(i),r.onerror=null,t({size:o?o.size:0,duration:r.duration||0,width:r.videoWidth||0,height:r.videoHeight||0})},r.onerror=function(){clearTimeout(i),r.onloadedmetadata=null,n()},r.src=e}else n()}))}),0,If),ob={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};function rb({count:e,sourceType:t,type:n,extension:o}){const r=document.createElement("input");return r.type="file",function(e,t){for(const n in t)e.style[n]=t[n]}(r,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),r.accept=o.map((e=>{if("all"!==n){const t=e.replace(".","");return`${n}/${ob[n][t]||t}`}return function(){const e=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!(!e||"micromessenger"!==e[0])}()?".":0===e.indexOf(".")?e:`.${e}`})).join(","),e&&e>1&&(r.multiple=!0),"all"!==n&&t instanceof Array&&1===t.length&&"camera"===t[0]&&r.setAttribute("capture","camera"),r}Qh();let ib=null;const ab=Ed("chooseFile",(({count:e,sourceType:t,type:n,extension:o},{resolve:r,reject:i})=>{El();const{t:a}=_l();ib&&(document.body.removeChild(ib),ib=null),ib=rb({count:e,sourceType:t,type:n,extension:o}),document.body.appendChild(ib),ib.addEventListener("change",(function(t){const n=t.target,o=[];if(n&&n.files){const t=n.files.length;for(let r=0;r<t;r++){const t=n.files[r];let i;Object.defineProperty(t,"path",{get:()=>(i=i||xh(t),i)}),r<e&&o.push(t)}}r({get tempFilePaths(){return o.map((({path:e})=>e))},tempFiles:o})})),ib.click(),Kh()||console.warn(a("uni.chooseFile.notUserActivation"))}),0,Af);let sb=null;const lb=Ed("chooseImage",(({count:e,sourceType:t,extension:n},{resolve:o,reject:r})=>{El();const{t:i}=_l();sb&&(document.body.removeChild(sb),sb=null),sb=rb({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(sb),sb.addEventListener("change",(function(t){const n=t.target,r=[];if(n&&n.files){const t=n.files.length;for(let o=0;o<t;o++){const t=n.files[o];let i;Object.defineProperty(t,"path",{get:()=>(i=i||xh(t),i)}),o<e&&r.push(t)}}o({get tempFilePaths(){return r.map((({path:e})=>e))},tempFiles:r})})),sb.click(),Kh()||console.warn(i("uni.chooseFile.notUserActivation"))}),0,kf),cb={esc:["Esc","Escape"],enter:["Enter"]},ub=Object.keys(cb);function db(){const e=nn(""),t=nn(!1),n=n=>{if(t.value)return;const o=ub.find((e=>-1!==cb[e].indexOf(n.key)));o&&(e.value=o),Sn((()=>e.value=""))};return Io((()=>{document.addEventListener("keyup",n)})),$o((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}const pb=Jr("div",{class:"uni-mask"},null,-1);function fb(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),Sa(co({setup:()=>()=>(Nr(),Fr(e,t,null,16))}))}function hb(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function gb(e,{onEsc:t,onEnter:n}){const o=nn(e.visible),{key:r,disable:i}=db();return Gn((()=>e.visible),(e=>o.value=e)),Gn((()=>o.value),(e=>i.value=!e)),Yn((()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}let mb=0,vb="";function yb(e){let t=mb;mb+=e?1:-1,mb=Math.max(0,mb),mb>0?0===t&&(vb=document.body.style.overflow,document.body.style.overflow="hidden"):(document.body.style.overflow=vb,vb="")}function bb(){Io((()=>yb(!0))),Do((()=>yb(!1)))}const _b=du({name:"ImageView",props:{src:{type:String,default:""}},setup(e){const t=Vt({direction:"none"});let n=1,o=0,r=0,i=0,a=0;function s({detail:e}){n=e.scale}function l(e){const t=e.target.getBoundingClientRect();o=t.width,r=t.height}function c(e){const t=e.target.getBoundingClientRect();i=t.width,a=t.height,d(e)}function u(e){const s=n*o>i,l=n*r>a;t.direction=s&&l?"all":s?"horizontal":l?"vertical":"none",d(e)}function d(e){"all"!==t.direction&&"horizontal"!==t.direction||e.stopPropagation()}return()=>{const n={position:"absolute",left:"0",top:"0",width:"100%",height:"100%"};return Jr(hg,{style:n,onTouchstart:gu(c),onTouchmove:gu(d),onTouchend:gu(u)},{default:()=>[Jr(Eg,{style:n,direction:t.direction,inertia:!0,scale:!0,"scale-min":"1","scale-max":"4",onScale:s},{default:()=>[Jr("img",{src:e.src,style:{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)",maxHeight:"100%",maxWidth:"100%"},onLoad:l},null,40,["src","onLoad"])]},8,["style","direction","inertia","scale","onScale"])]},8,["style","onTouchstart","onTouchmove","onTouchend"])}}});function wb(e){let t="number"==typeof e.current?e.current:e.urls.indexOf(e.current);return t=t<0?0:t,t}const xb=du({name:"ImagePreview",props:{urls:{type:Array,default:()=>[]},current:{type:[Number,String],default:0}},emits:["close"],setup(e,{emit:t}){bb();const n=nn(null),o=nn(wb(e));let r;function i(){r||Sn((()=>{t("close")}))}function a(e){o.value=e.detail.current}Gn((()=>e.current),(()=>o.value=wb(e))),Io((()=>{const e=n.value;let t=0,o=0;e.addEventListener("mousedown",(e=>{r=!1,t=e.clientX,o=e.clientY})),e.addEventListener("mouseup",(e=>{(Math.abs(e.clientX-t)>20||Math.abs(e.clientY-o)>20)&&(r=!0)}))}));const s={position:"absolute","box-sizing":"border-box",top:"0",right:"0",width:"60px",height:"44px",padding:"6px","line-height":"32px","font-size":"26px",color:"white","text-align":"center",cursor:"pointer"};return()=>{let t;return Jr("div",{ref:n,style:{display:"block",position:"fixed",left:"0",top:"0",width:"100%",height:"100%",zIndex:999,background:"rgba(0,0,0,0.8)"},onClick:i},[Jr(rm,{navigation:"auto",current:o.value,onChange:a,"indicator-dots":!1,autoplay:!1,style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%"}},(r=t=e.urls.map((e=>Jr(am,null,{default:()=>[Jr(_b,{src:e},null,8,["src"])]}))),"function"==typeof r||"[object Object]"===Object.prototype.toString.call(r)&&!Ur(r)?t:{default:()=>[t],_:1}),8,["current","onChange"]),Jr("div",{style:s},[xc(_c,"#ffffff",26)],4)],8,["onClick"]);var r}}});let Tb,Sb=null;const kb=()=>{Sb=null,Sn((()=>{null==Tb||Tb.unmount(),Tb=null}))},Cb=Ed("previewImage",((e,{resolve:t})=>{Sb?x(Sb,e):(Sb=Vt(e),Sn((()=>{Tb=fb(xb,Sb,kb),Tb.mount(hb("u-a-p"))}))),t()}),0,Pf),Eb=Ed("closePreviewImage",((e,{resolve:t,reject:n})=>{Tb?(kb(),t()):n()}));let Ab=null;const Mb=Ed("chooseVideo",(({sourceType:e,extension:t},{resolve:n,reject:o})=>{El();const{t:r}=_l();Ab&&(document.body.removeChild(Ab),Ab=null),Ab=rb({sourceType:e,extension:t,type:"video"}),document.body.appendChild(Ab),Ab.addEventListener("change",(function(e){const t=e.target.files[0];let o="";const r={tempFilePath:o,tempFile:t,size:t.size,duration:0,width:0,height:0,name:t.name};Object.defineProperty(r,"tempFilePath",{get(){return o=o||xh(this.tempFile),o}});const i=document.createElement("video");if(void 0!==i.onloadedmetadata){const e=xh(t);i.onloadedmetadata=function(){Th(e),n(x(r,{duration:i.duration||0,width:i.videoWidth||0,height:i.videoHeight||0}))},setTimeout((()=>{i.onloadedmetadata=null,Th(e),n(r)}),300),i.src=e}else n(r)})),Ab.click(),Kh()||console.warn(r("uni.chooseFile.notUserActivation"))}),0,Cf),Pb=kd("request",(({url:e,data:t,header:n,method:o,dataType:r,responseType:i,withCredentials:a,timeout:s=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const d=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(I(t)||t instanceof ArrayBuffer)u=t;else if("json"===d)try{u=JSON.stringify(t)}catch(g){u=t.toString()}else if("urlencoded"===d){const e=[];for(const n in t)k(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));u=e.join("&")}else u=t.toString();const p=new XMLHttpRequest,f=new Ib(p);p.open(o,e);for(const m in n)k(n,m)&&p.setRequestHeader(m,n[m]);const h=setTimeout((function(){p.onload=p.onabort=p.onerror=null,f.abort(),c("timeout")}),s);return p.responseType=i,p.onload=function(){clearTimeout(h);const e=p.status;let t="text"===i?p.responseText:p.response;if("text"===i&&"json"===r)try{t=JSON.parse(t)}catch(g){}l({data:t,statusCode:e,header:Ob(p.getAllResponseHeaders()),cookies:[]})},p.onabort=function(){clearTimeout(h),c("abort")},p.onerror=function(){clearTimeout(h),c()},p.withCredentials=a,p.send(u),f}),0,Df);class Ib{constructor(e){this._xhr=e}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}function Ob(e){const t={};return e.split("\n").forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}class Lb{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){P(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const $b=kd("downloadFile",(({url:e,header:t,timeout:n=__uniConfig.networkTimeout.downloadFile},{resolve:o,reject:r})=>{var i,a=new XMLHttpRequest,s=new Lb(a);return a.open("GET",e,!0),Object.keys(t).forEach((e=>{a.setRequestHeader(e,t[e])})),a.responseType="blob",a.onload=function(){clearTimeout(i);const t=a.status,n=this.response;let r;const s=a.getResponseHeader("content-disposition");if(s){const e=s.match(/filename="?(\S+)"?\b/);e&&(r=e[1])}n.name=r||function(e){const t=(e=e.split("#")[0].split("?")[0]).split("/");return t[t.length-1]}(e),o({statusCode:t,tempFilePath:xh(n)})},a.onabort=function(){clearTimeout(i),r("abort")},a.onerror=function(){clearTimeout(i),r()},a.onprogress=function(e){s._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesWritten:n,totalBytesExpectedToWrite:o})}))},a.send(),i=setTimeout((function(){a.onprogress=a.onload=a.onabort=a.onerror=null,s.abort(),r("timeout")}),n),s}),0,Rf);class Db{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){P(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const Rb=kd("uploadFile",(({url:e,file:t,filePath:n,name:o,files:r,header:i,formData:a,timeout:s=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new Db;return C(r)&&r.length||(r=[{name:o,file:t,uri:n}]),Promise.all(r.map((({file:e,uri:t})=>e instanceof Blob?Promise.resolve(wh(e)):_h(t)))).then((function(t){var n,o=new XMLHttpRequest,d=new FormData;Object.keys(a).forEach((e=>{d.append(e,a[e])})),Object.values(r).forEach((({name:e},n)=>{const o=t[n];d.append(e||"file",o,o.name||`file-${Date.now()}`)})),o.open("POST",e),Object.keys(i).forEach((e=>{o.setRequestHeader(e,i[e])})),o.upload.onprogress=function(e){u._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesSent:n,totalBytesExpectedToSend:o})}))},o.onerror=function(){clearTimeout(n),c()},o.onabort=function(){clearTimeout(n),c("abort")},o.onload=function(){clearTimeout(n);const e=o.status;l({statusCode:e,data:o.responseText||o.response})},u._isAbort?c("abort"):(n=setTimeout((function(){o.upload.onprogress=o.onload=o.onabort=o.onerror=null,u.abort(),c("timeout")}),s),o.send(d),u._xhr=o)})).catch((()=>{setTimeout((()=>{c("file error")}),0)})),u}),0,Bf),Bb=[],Nb={open:"",close:"",error:"",message:""};class qb{constructor(e,t,n){let o;this._callbacks={open:[],close:[],error:[],message:[]};try{const n=this._webSocket=new WebSocket(e,t);n.binaryType="arraybuffer";["open","close","error","message"].forEach((e=>{this._callbacks[e]=[],n.addEventListener(e,(t=>{const{data:n,code:o,reason:r}=t,i="message"===e?{data:n}:"close"===e?{code:o,reason:r}:{};if(this._callbacks[e].forEach((t=>{try{t(i)}catch(kC){console.error(`thirdScriptError\n${kC};at socketTask.on${W(e)} callback function\n`,kC)}})),this===Bb[0]&&Nb[e]&&Lx.invokeOnCallback(Nb[e],i),"error"===e||"close"===e){const e=Bb.indexOf(this);e>=0&&Bb.splice(e,1)}}))}));["CLOSED","CLOSING","CONNECTING","OPEN","readyState"].forEach((e=>{Object.defineProperty(this,e,{get:()=>n[e]})}))}catch(kC){o=kC}n&&n(o,this)}send(e){const t=(e||{}).data,n=this._webSocket;try{if(n.readyState!==n.OPEN)throw new Error("SocketTask.readyState is not OPEN");n.send(t),de(e,"sendSocketMessage:ok")}catch(o){de(e,`sendSocketMessage:fail ${o}`)}}close(e={}){const t=this._webSocket;try{const n=e.code||1e3,o=e.reason;I(o)?t.close(n,o):t.close(n),de(e,"closeSocket:ok")}catch(n){de(e,`closeSocket:fail ${n}`)}}onOpen(e){this._callbacks.open.push(e)}onMessage(e){this._callbacks.message.push(e)}onError(e){this._callbacks.error.push(e)}onClose(e){this._callbacks.close.push(e)}}const jb=kd("connectSocket",(({url:e,protocols:t},{resolve:n,reject:o})=>new qb(e,t,((e,t)=>{e?o(e.toString()):(Bb.push(t),n())}))),0,Nf);function zb(e,t,n,o,r){const i=e[t];P(i)&&i.call(e,x({},n,{success(){o()},fail({errMsg:e}){r(e.replace("sendSocketMessage:fail ",""))},complete:void 0}))}const Vb=Ed("sendSocketMessage",((e,{resolve:t,reject:n})=>{const o=Bb[0];o&&o.readyState===o.OPEN?zb(o,"send",e,t,n):n("WebSocket is not connected")})),Fb=Ed("closeSocket",((e,{resolve:t,reject:n})=>{const o=Bb[0];o?zb(o,"close",e,t,n):n("WebSocket is not connected")}));function Ub(e){const t=`onSocket${W(e)}`;return Td(t,(()=>{Nb[e]=t}))}const Wb=Ub("open"),Hb=Ub("error"),Yb=Ub("message"),Xb=Ub("close"),Gb=Ed("getLocation",(({type:e,altitude:t,highAccuracyExpireTime:n,isHighAccuracy:o},{resolve:r,reject:i})=>{const a=zv();new Promise(((e,r)=>{navigator.geolocation?navigator.geolocation.getCurrentPosition((t=>e({coords:t.coords})),r,{enableHighAccuracy:o||t,timeout:n||1e5}):r(new Error("device nonsupport geolocation"))})).catch((e=>new Promise(((t,n)=>{a.type===jv.QQ?Ov(`https://apis.map.qq.com/ws/location/v1/ip?output=jsonp&key=${a.key}`,{callback:"callback"},(e=>{if("result"in e&&e.result.location){const n=e.result.location;t({coords:{latitude:n.lat,longitude:n.lng},skip:!0})}else n(new Error(e.message||JSON.stringify(e)))}),(()=>n(new Error("network error")))):a.type===jv.GOOGLE?Pb({method:"POST",url:`https://www.googleapis.com/geolocation/v1/geolocate?key=${a.key}`,success(e){const o=e.data;"location"in o?t({coords:{latitude:o.location.lat,longitude:o.location.lng,accuracy:o.accuracy},skip:!0}):n(new Error(o.error&&o.error.message||JSON.stringify(e)))},fail(){n(new Error("network error"))}}):a.type===jv.AMAP?Dv([],(()=>{window.AMap.plugin("AMap.Geolocation",(()=>{new window.AMap.Geolocation({enableHighAccuracy:!0,timeout:1e4}).getCurrentPosition(((e,o)=>{"complete"===e?t({coords:{latitude:o.position.lat,longitude:o.position.lng,accuracy:o.accuracy},skip:!0}):n(new Error(o.message))}))}))})):n(e)})))).then((({coords:t,skip:n})=>{Hv(e,t,n).then((e=>{r({latitude:e.latitude,longitude:e.longitude,accuracy:e.accuracy,speed:e.altitude||0,altitude:e.altitude||0,verticalAccuracy:e.altitudeAccuracy||0,horizontalAccuracy:e.accuracy||0})})).catch((e=>{i(e.message)}))})).catch((e=>{i(e.message||JSON.stringify(e))}))}),0,xf);const Jb=du({name:"LocationView",props:{latitude:{type:Number},longitude:{type:Number},scale:{type:Number,default:18},name:{type:String,default:""},address:{type:String,default:""}},emits:["close"],setup(e,{emit:t}){const n=function(e){const t=Vt({center:{latitude:0,longitude:0},marker:{id:1,latitude:0,longitude:0,iconPath:qv,width:32,height:52},location:{id:2,latitude:0,longitude:0,iconPath:Nv,width:44,height:44}});function n(){e.latitude&&e.longitude&&(t.center.latitude=e.latitude,t.center.longitude=e.longitude,t.marker.latitude=e.latitude,t.marker.longitude=e.longitude)}return Gn([()=>e.latitude,()=>e.longitude],n),n(),t}(e);function o(e){const t=e.detail.centerLocation;t&&(n.center.latitude=t.latitude,n.center.longitude=t.longitude)}function r(){const t=zv();let o="";if(t.type===jv.GOOGLE){o=`https://www.google.com/maps/dir/?api=1${n.location.latitude?`&origin=${n.location.latitude}%2C${n.location.longitude}`:""}&destination=${e.latitude}%2C${e.longitude}`}else if(t.type===jv.QQ){o=`https://apis.map.qq.com/uri/v1/routeplan?type=drive${n.location.latitude?`&fromcoord=${n.location.latitude}%2C${n.location.longitude}&from=${encodeURIComponent("我的位置")}`:""}&tocoord=${e.latitude}%2C${e.longitude}&to=${encodeURIComponent(e.name||"目的地")}&ref=${t.key}`}else if(t.type===jv.AMAP){o=`https://uri.amap.com/navigation?${n.location.latitude?`from=${n.location.longitude},${n.location.latitude},${encodeURIComponent("我的位置")}&`:""}to=${e.longitude},${e.latitude},${encodeURIComponent(e.name||"目的地")}`}window.open(o)}function i(){t("close")}function a({latitude:e,longitude:t}){n.center.latitude=e,n.center.longitude=t}return bb(),Gb({type:"gcj02",success:({latitude:e,longitude:t})=>{n.location.latitude=e,n.location.longitude=t}}),()=>Jr("div",{class:"uni-system-open-location"},[Jr(_x,{latitude:n.center.latitude,longitude:n.center.longitude,class:"map",markers:[n.marker,n.location],onRegionchange:o},{default:()=>[Jr("div",{class:"map-move",onClick:()=>a(n.location)},[xc(Bv,"#000000",24)],8,["onClick"])]},8,["latitude","longitude","markers","onRegionchange"]),Jr("div",{class:"info"},[Jr("div",{class:"name",onClick:()=>a(n.marker)},[e.name],8,["onClick"]),Jr("div",{class:"address",onClick:()=>a(n.marker)},[e.address],8,["onClick"]),Jr("div",{class:"nav",onClick:r},[xc("M28 17c-6.49396875 0-12.13721875 2.57040625-15 6.34840625V5.4105l6.29859375 6.29859375c0.387875 0.387875 1.02259375 0.387875 1.4105 0 0.387875-0.387875 0.387875-1.02259375 0-1.4105L12.77853125 2.36803125a0.9978125 0.9978125 0 0 0-0.0694375-0.077125c-0.1944375-0.1944375-0.45090625-0.291375-0.70721875-0.290875l-0.00184375-0.0000625-0.00184375 0.0000625c-0.2563125-0.0005-0.51278125 0.09640625-0.70721875 0.290875a0.9978125 0.9978125 0 0 0-0.0694375 0.077125l-7.930625 7.9305625c-0.387875 0.387875-0.387875 1.02259375 0 1.4105 0.387875 0.387875 1.02259375 0.387875 1.4105 0L11 5.4105V29c0 0.55 0.45 1 1 1s1-0.45 1-1c0-5.52284375 6.71571875-10 15-10 0.55228125 0 1-0.44771875 1-1 0-0.55228125-0.44771875-1-1-1z","#ffffff",26)],8,["onClick"])]),Jr("div",{class:"nav-btn-back",onClick:i},[xc(bc,"#ffffff",26)],8,["onClick"])])}});let Qb=null;const Kb=Ed("openLocation",((e,{resolve:t})=>{Qb?x(Qb,e):(Qb=Vt(e),Sn((()=>{const e=fb(Jb,Qb,(()=>{Qb=null,Sn((()=>{e.unmount()}))}));e.mount(hb("u-a-o"))}))),t()}),0,Sf);const Zb=du({name:"LoctaionPicker",props:{latitude:{type:Number},longitude:{type:Number}},emits:["close"],setup(e,{emit:t}){bb(),Ol();const{t:n}=_l(),o=function(e){const t=Vt({latitude:0,longitude:0,keyword:"",searching:!1});function n(){e.latitude&&e.longitude&&(t.latitude=e.latitude,t.longitude=e.longitude)}return Gn([()=>e.latitude,()=>e.longitude],n),n(),t}(e),{list:r,listState:i,loadMore:a,reset:s,getList:l}=function(e){const t=__uniConfig.qqMapKey,n=Vt([]),o=nn(-1),r=vi((()=>n[o.value])),i=Vt({loading:!0,pageSize:20,pageIndex:1,hasNextPage:!0,nextPage:null,selectedIndex:o,selected:r}),a=nn(""),s=vi((()=>a.value?`region(${a.value},1,${e.latitude},${e.longitude})`:`nearby(${e.latitude},${e.longitude},5000)`));function l(e){e.forEach((e=>{n.push({name:e.title||e.name,address:e.address,distance:e._distance||e.distance,latitude:e.location.lat,longitude:e.location.lng})}))}function c(){i.loading=!0;const o=zv();if(o.type===jv.GOOGLE){if(i.pageIndex>1&&i.nextPage)return void i.nextPage();new google.maps.places.PlacesService(document.createElement("div"))[e.searching?"textSearch":"nearbySearch"]({location:{lat:e.latitude,lng:e.longitude},query:e.keyword,radius:5e3},((e,t,o)=>{i.loading=!1,e&&e.length&&e.forEach((e=>{n.push({name:e.name||"",address:e.vicinity||e.formatted_address||"",distance:0,latitude:e.geometry.location.lat(),longitude:e.geometry.location.lng()})})),o&&(o.hasNextPage?i.nextPage=()=>{o.nextPage()}:i.hasNextPage=!1)}))}else o.type===jv.QQ?Ov(e.searching?`https://apis.map.qq.com/ws/place/v1/search?output=jsonp&key=${t}&boundary=${s.value}&keyword=${e.keyword}&page_size=${i.pageSize}&page_index=${i.pageIndex}`:`https://apis.map.qq.com/ws/geocoder/v1/?output=jsonp&key=${t}&location=${e.latitude},${e.longitude}&get_poi=1&poi_options=page_size=${i.pageSize};page_index=${i.pageIndex}`,{callback:"callback"},(t=>{if(i.loading=!1,e.searching&&"data"in t&&t.data.length)l(t.data);else if("result"in t){const e=t.result;a.value=e.ad_info?e.ad_info.adcode:"",e.pois&&l(e.pois)}n.length===i.pageSize*i.pageIndex&&(i.hasNextPage=!1)}),(()=>{i.loading=!1})):o.type===jv.AMAP&&window.AMap.plugin("AMap.PlaceSearch",(function(){const t=new window.AMap.PlaceSearch({city:"全国",pageSize:10,pageIndex:i.pageIndex}),n=e.searching?e.keyword:"",o=e.searching?5e4:5e3;t.searchNearBy(n,[e.longitude,e.latitude],o,(function(e,t){"error"===e?console.error(t):"no_data"===e?i.hasNextPage=!1:l(t.poiList.pois)})),i.loading=!1}))}return{listState:i,list:n,loadMore:function(){!i.loading&&i.hasNextPage&&(i.pageIndex++,c())},reset:function(){i.selectedIndex=-1,i.pageIndex=1,i.hasNextPage=!0,i.nextPage=null,n.splice(0,n.length)},getList:c}}(o),c=xe((()=>{s(),o.keyword&&l()}),1e3,{setTimeout:setTimeout,clearTimeout:clearTimeout});function u(e){o.keyword=e.detail.value,c()}function d(){t("close",x({},i.selected))}function p(){t("close")}function f(e){const t=e.detail.centerLocation;t&&g(t)}function h(){Gb({type:"gcj02",success:g,fail:()=>{}})}function g({latitude:e,longitude:t}){o.latitude=e,o.longitude=t,o.searching||(s(),l())}return Gn((()=>o.searching),(e=>{s(),e||l()})),o.latitude&&o.longitude||h(),()=>{const e=r.map(((e,t)=>{return Jr("div",{key:t,class:{"list-item":!0,selected:i.selectedIndex===t},onClick:()=>{i.selectedIndex=t,o.latitude=e.latitude,o.longitude=e.longitude}},[xc(wc,"#007aff",24),Jr("div",{class:"list-item-title"},[e.name]),Jr("div",{class:"list-item-detail"},[(n=e.distance,n>100?`${n>1e3?(n/1e3).toFixed(1)+"k":n.toFixed(0)}m | `:n>0?"<100m | ":""),e.address])],10,["onClick"]);var n}));return i.loading&&e.unshift(Jr("div",{class:"list-loading"},[Jr("i",{class:"uni-loading"},null)])),Jr("div",{class:"uni-system-choose-location"},[Jr(_x,{latitude:o.latitude,longitude:o.longitude,class:"map","show-location":!0,libraries:["places"],onUpdated:l,onRegionchange:f},{default:()=>[Jr("div",{class:"map-location",style:`background-image: url("${qv}")`},null),Jr("div",{class:"map-move",onClick:h},[xc(Bv,"#000000",24)],8,["onClick"])],_:1},8,["latitude","longitude","show-location","onUpdated","onRegionchange"]),Jr("div",{class:"nav"},[Jr("div",{class:"nav-btn back",onClick:p},[xc(_c,"#ffffff",26)],8,["onClick"]),Jr("div",{class:{"nav-btn":!0,confirm:!0,disable:!i.selected},onClick:d},[xc(wc,"#ffffff",26)],10,["onClick"])]),Jr("div",{class:"menu"},[Jr("div",{class:"search"},[Jr(cg,{value:o.keyword,class:"search-input",placeholder:n("uni.chooseLocation.search"),onFocus:()=>o.searching=!0,onInput:u},null,8,["value","placeholder","onFocus","onInput"]),o.searching&&Jr("div",{class:"search-btn",onClick:()=>{o.searching=!1,o.keyword=""}},[n("uni.chooseLocation.cancel")],8,["onClick"])]),Jr(nm,{"scroll-y":!0,class:"list",onScrolltolower:a},(t=e,"function"==typeof t||"[object Object]"===Object.prototype.toString.call(t)&&!Ur(t)?e:{default:()=>[e],_:2}),8,["scroll-y","onScrolltolower"])])]);var t}}});let e_=null;const t_=Ed("chooseLocation",((e,{resolve:t,reject:n})=>{e_?n("cancel"):(e_=Vt(e),Sn((()=>{const e=fb(Zb,e_,(o=>{e_=null,Sn((()=>{e.unmount()})),o?t(o):n("cancel")}));e.mount(hb("u-a-c"))})))}));let n_=!1,o_=0;const r_=Ed("startLocationUpdate",((e,{resolve:t,reject:n})=>{navigator.geolocation?(o_=o_||navigator.geolocation.watchPosition((n=>{n_=!0,Hv(null==e?void 0:e.type,n.coords).then((e=>{Lx.invokeOnCallback("onLocationChange",e),t()})).catch((e=>{Lx.invokeOnCallback("onLocationChangeError",{errMsg:`onLocationChange:fail ${e.message}`})}))}),(e=>{n_||(n(e.message),n_=!0),Lx.invokeOnCallback("onLocationChangeError",{errMsg:`onLocationChange:fail ${e.message}`})})),setTimeout(t,100)):n()}),0,jf),i_=Ed("stopLocationUpdate",((e,{resolve:t})=>{o_&&(navigator.geolocation.clearWatch(o_),n_=!1,o_=0),t()})),a_=Td("onLocationChange",(()=>{})),s_=Sd("offLocationChange",(()=>{})),l_=Td("onLocationChangeError",(()=>{})),c_=Sd("offLocationChangeError",(()=>{})),u_=Ed("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===Ic("onBackPress",{from:e.from||"navigateBack"})&&(o=!1),o?(yv().$router.go(-e.delta),t()):n("onBackPress")}),0,Hf);function d_({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:r},i){const a=yv().$router,{path:s,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:we(n||"")}}(t);return new Promise(((t,c)=>{const u=function(e,t){return{__id__:t||++Jm,__type__:e}}(e,i);a["navigateTo"===e?"push":"replace"]({path:s,query:l,state:u,force:!0}).then((i=>{if(hs(i))return c(i.message);if("switchTab"===e&&(a.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=a.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new Te(u.__id__,o),t(r?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return r?t({__id__:u.__id__}):t()}))}))}const p_=Ed("navigateTo",(({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:r})=>d_({type:"navigateTo",url:e,events:t,isAutomatedTesting:n}).then(o).catch(r)),0,Vf);const f_=Ed("redirectTo",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>(function(){const e=kc();if(!e)return;const t=e.$page;Gm(Zm(t.path,t.id))}(),d_({type:"redirectTo",url:e,isAutomatedTesting:t}).then(n).catch(o))),0,Ff);const h_=Ed("reLaunch",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>(function(){const e=Ym().keys();for(const t of e)Gm(t)}(),d_({type:"reLaunch",url:e,isAutomatedTesting:t}).then(n).catch(o))),0,Uf);function g_(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}const m_=Ed("switchTab",(({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:o,reject:r})=>(function(){const e=Ac();if(!e)return;const t=Ym(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:Gm(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,Ic(e,"onHide"))}(),d_({type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},function(e){const t=Ym().values();for(const n of t){const t=n.$page;if(g_(e,t))return n.$.__isActive=!0,t.id}}(e)).then(o).catch(r))),0,Wf),v_=Ed("preloadPage",(({url:e},{resolve:t,reject:n})=>{const o=Bc(e.split("?")[0]);o?o.loader&&o.loader().then((()=>{t({url:e,errMsg:"preloadPage:ok"})})).catch((t=>{n(`${e} ${String(t)}`)})):n(`${e}}`)}));function y_(e){__uniConfig.darkmode&&Lx.on("onThemeChange",e)}function b_(e){Lx.off("onThemeChange",e)}function __(e){let t={};return __uniConfig.darkmode&&(t=Le(e,__uniConfig.themeConfig,sy())),__uniConfig.darkmode?t:e}function w_(e,t){const n=Wt(e),o=n?Vt(__(e)):__(e);return __uniConfig.darkmode&&n&&Gn(e,(e=>{const t=__(e);for(const n in t)o[n]=t[n]})),t&&y_(t),o}const x_={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},T_=co({props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},setup(e,{emit:t}){const n=nn(""),o=()=>a.value=!1,r=()=>(o(),t("close","cancel")),i=()=>(o(),t("close","confirm",n.value)),a=gb(e,{onEsc:r,onEnter:()=>{!e.editable&&i()}}),s=function(e){const t=nn(e.cancelColor),n=({theme:e})=>{((e,t)=>{t.value=x_[e].cancelColor})(e,t)};return Yn((()=>{e.visible?(t.value=e.cancelColor,"#000"===e.cancelColor&&("dark"===sy()&&n({theme:"dark"}),y_(n))):b_(n)})),t}(e);return()=>{const{title:t,content:o,showCancel:l,confirmText:c,confirmColor:u,editable:d,placeholderText:p}=e;return n.value=o,Jr(Hi,{name:"uni-fade"},{default:()=>[jo(Jr("uni-modal",{onTouchmove:sc},[pb,Jr("div",{class:"uni-modal"},[t&&Jr("div",{class:"uni-modal__hd"},[Jr("strong",{class:"uni-modal__title",textContent:t},null,8,["textContent"])]),d?Jr("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:p,value:o,onInput:e=>n.value=e.target.value},null,40,["placeholder","value","onInput"]):Jr("div",{class:"uni-modal__bd",onTouchmovePassive:lc,textContent:o},null,40,["onTouchmovePassive","textContent"]),Jr("div",{class:"uni-modal__ft"},[l&&Jr("div",{style:{color:s.value},class:"uni-modal__btn uni-modal__btn_default",onClick:r},[e.cancelText],12,["onClick"]),Jr("div",{style:{color:u},class:"uni-modal__btn uni-modal__btn_primary",onClick:i},[c],12,["onClick"])])])],40,["onTouchmove"]),[[_a,a.value]])]})}}});let S_;const k_=le((()=>{Lx.on("onHidePopup",(()=>S_.visible=!1))}));let C_;function E_(e,t){const n="confirm"===e,o={confirm:n,cancel:"cancel"===e};n&&S_.editable&&(o.content=t),C_&&C_(o)}const A_=Ed("showModal",((e,{resolve:t})=>{k_(),C_=t,S_?(x(S_,e),S_.visible=!0):(S_=Vt(e),Sn((()=>(fb(T_,S_,E_).mount(hb("u-a-m")),Sn((()=>S_.visible=!0))))))}),0,nh),M_={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==oh.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},P_={light:"#fff",dark:"rgba(255,255,255,0.9)"},I_=e=>P_[e],O_=co({name:"Toast",props:M_,setup(e){Sl(),kl();const{Icon:t}=function(e){const t=nn(I_(sy())),n=({theme:e})=>t.value=I_(e);Yn((()=>{e.visible?y_(n):b_(n)}));return{Icon:vi((()=>{switch(e.icon){case"success":return Jr(xc(vc,t.value,38),{class:"uni-toast__icon"});case"error":return Jr(xc(yc,t.value,38),{class:"uni-toast__icon"});case"loading":return Jr("i",{class:["uni-toast__icon","uni-loading"]},null,2);default:return null}}))}}(e),n=gb(e,{});return()=>{const{mask:o,duration:r,title:i,image:a}=e;return Jr(Hi,{name:"uni-fade"},{default:()=>[jo(Jr("uni-toast",{"data-duration":r},[o?Jr("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:sc},null,40,["onTouchmove"]):"",a||t.value?Jr("div",{class:"uni-toast"},[a?Jr("img",{src:a,class:"uni-toast__icon"},null,10,["src"]):t.value,Jr("p",{class:"uni-toast__content"},[i])]):Jr("div",{class:"uni-sample-toast"},[Jr("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[_a,n.value]])]})}}});let L_,$_,D_="";const R_=Re();function B_(e){L_?x(L_,e):(L_=Vt(x(e,{visible:!1})),Sn((()=>{R_.run((()=>{Gn([()=>L_.visible,()=>L_.duration],(([e,t])=>{if(e){if($_&&clearTimeout($_),"onShowLoading"===D_)return;$_=setTimeout((()=>{F_("onHideToast")}),t)}else $_&&clearTimeout($_)}))})),Lx.on("onHidePopup",(()=>F_("onHidePopup"))),fb(O_,L_,(()=>{})).mount(hb("u-a-t"))}))),setTimeout((()=>{L_.visible=!0}),10)}const N_=Ed("showToast",((e,{resolve:t,reject:n})=>{B_(e),D_="onShowToast",t()}),0,rh),q_={icon:"loading",duration:1e8,image:""},j_=Ed("showLoading",((e,{resolve:t,reject:n})=>{x(e,q_),B_(e),D_="onShowLoading",t()}),0,th),z_=Ed("hideToast",((e,{resolve:t,reject:n})=>{F_("onHideToast"),t()})),V_=Ed("hideLoading",((e,{resolve:t,reject:n})=>{F_("onHideLoading"),t()}));function F_(e){const{t:t}=_l();if(!D_)return;let n="";if("onHideToast"===e&&"onShowToast"!==D_?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==D_&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);D_="",setTimeout((()=>{L_.visible=!1}),10)}function U_(e){const t=nn(0),n=nn(0),o=vi((()=>t.value>=500&&n.value>=500)),r=vi((()=>{const t={content:{transform:"",left:"",top:"",bottom:""},triangle:{left:"",top:"",bottom:"","border-width":"","border-color":""}},r=t.content,i=t.triangle,a=e.popover;function s(e){return Number(e)||0}if(o.value&&a){x(i,{position:"absolute",width:"0",height:"0","margin-left":"-6px","border-style":"solid"});const e=s(a.left),t=s(a.width),o=s(a.top),l=s(a.height),c=e+t/2;r.transform="none !important";const u=Math.max(0,c-150);r.left=`${u}px`;let d=Math.max(12,c-u);d=Math.min(288,d),i.left=`${d}px`;const p=n.value/2;o+l-p>p-o?(r.top="auto",r.bottom=n.value-o+6+"px",i.bottom="-6px",i["border-width"]="6px 6px 0 6px",i["border-color"]="#fcfcfd transparent transparent transparent"):(r.top=`${o+l+6}px`,i.top="-6px",i["border-width"]="0 6px 6px 6px",i["border-color"]="transparent transparent #fcfcfd transparent")}return t}));return Io((()=>{const e=()=>{const{windowWidth:e,windowHeight:o,windowTop:r}=uni.getSystemInfoSync();t.value=e,n.value=o+(r||0)};window.addEventListener("resize",e),e(),Do((()=>{window.removeEventListener("resize",e)}))})),{isDesktop:o,popupStyle:r}}const W_={light:{listItemColor:"#000000",cancelItemColor:"#000000"},dark:{listItemColor:"rgba(255, 255, 255, 0.8)",cancelItemColor:"rgba(255, 255, 255)"}};const H_=co({name:"ActionSheet",props:{title:{type:String,default:""},itemList:{type:Array,default:()=>[]},itemColor:{type:String,default:"#000000"},popover:{type:Object,default:null},visible:{type:Boolean,default:!1}},emits:["close"],setup(e,{emit:t}){Tl();const n=nn(260),o=nn(0),r=nn(0),i=nn(0),a=nn(0),s=nn(null),l=nn(null),{t:c}=_l(),{_close:u}=function(e,t){function n(e){t("close",e)}const{key:o,disable:r}=db();return Gn((()=>e.visible),(e=>r.value=!e)),Yn((()=>{const{value:e}=o;"esc"===e&&n&&n(-1)})),{_close:n}}(e,t),{popupStyle:d}=U_(e);let p;function f(e){const t=i.value+e.deltaY;Math.abs(t)>10?(a.value+=t/3,a.value=a.value>=o.value?o.value:a.value<=0?0:a.value,p.scrollTo(a.value)):i.value=t,e.preventDefault()}Io((()=>{const{scroller:e,handleTouchStart:t,handleTouchMove:n,handleTouchEnd:o}=Wg(s.value,{enableY:!0,friction:new qg(1e-4),spring:new Vg(2,90,20),onScroll:e=>{a.value=e.target.scrollTop}});p=e,bg(s.value,(r=>{if(e)switch(r.detail.state){case"start":t(r);break;case"move":n(r);break;case"end":case"cancel":o(r)}}),!0)})),Gn((()=>e.visible),(()=>{Sn((()=>{e.title&&(r.value=document.querySelector(".uni-actionsheet__title").offsetHeight),p.update(),s.value&&(o.value=s.value.clientHeight-n.value),document.querySelectorAll(".uni-actionsheet__cell").forEach((e=>{!function(e){const t=20;let n=0,o=0;e.addEventListener("touchstart",(e=>{const t=e.changedTouches[0];n=t.clientX,o=t.clientY})),e.addEventListener("touchend",(e=>{const r=e.changedTouches[0];if(Math.abs(r.clientX-n)<t&&Math.abs(r.clientY-o)<t){const t=e.target,n=e.currentTarget,o=new CustomEvent("click",{bubbles:!0,cancelable:!0,target:t,currentTarget:n});["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{o[e]=r[e]})),e.target.dispatchEvent(o)}}))}(e)}))}))}));const h=function(e){const t=Vt({listItemColor:"#000",cancelItemColor:"#000"}),n=({theme:e})=>{!function(e,t){["listItemColor","cancelItemColor"].forEach((n=>{t[n]=W_[e][n]}))}(e,t)};return Yn((()=>{e.visible?(t.listItemColor=t.cancelItemColor=e.itemColor,"#000"===e.itemColor&&(n({theme:sy()}),y_(n))):b_(n)})),t}(e);return()=>Jr("uni-actionsheet",{onTouchmove:sc},[Jr(Hi,{name:"uni-fade"},{default:()=>[jo(Jr("div",{class:"uni-mask uni-actionsheet__mask",onClick:()=>u(-1)},null,8,["onClick"]),[[_a,e.visible]])]}),Jr("div",{class:["uni-actionsheet",{"uni-actionsheet_toggle":e.visible}],style:d.value.content},[Jr("div",{ref:l,class:"uni-actionsheet__menu",onWheel:f},[e.title?Jr(Or,null,[Jr("div",{class:"uni-actionsheet__cell",style:{height:`${r.value}px`}},null),Jr("div",{class:"uni-actionsheet__title"},[e.title])]):"",Jr("div",{style:{maxHeight:`${n.value}px`,overflow:"hidden"}},[Jr("div",{ref:s},[e.itemList.map(((e,t)=>Jr("div",{key:t,style:{color:h.listItemColor},class:"uni-actionsheet__cell",onClick:()=>u(t)},[e],12,["onClick"])))],512)])],40,["onWheel"]),Jr("div",{class:"uni-actionsheet__action"},[Jr("div",{style:{color:h.cancelItemColor},class:"uni-actionsheet__cell",onClick:()=>u(-1)},[c("uni.showActionSheet.cancel")],12,["onClick"])]),Jr("div",{style:d.value.triangle},null,4)],6)],40,["onTouchmove"])}});let Y_,X_,G_;const J_=le((()=>{Lx.on("onHidePopup",(()=>G_.visible=!1))}));function Q_(e){-1===e?X_&&X_("cancel"):Y_&&Y_({tapIndex:e})}const K_=Ed("showActionSheet",((e,{resolve:t,reject:n})=>{J_(),Y_=t,X_=n,G_?(x(G_,e),G_.visible=!0):(G_=Vt(e),Sn((()=>(fb(H_,G_,Q_).mount(hb("u-s-a-s")),Sn((()=>G_.visible=!0))))))}),0,eh),Z_=Ed("loadFontFace",(({family:e,source:t,desc:n},{resolve:o,reject:r})=>{(function(e,t,n){const o=document.fonts;if(o){const r=new FontFace(e,t,n);return r.load().then((()=>{o.add&&o.add(r)}))}return new Promise((o=>{const r=document.createElement("style"),i=[];if(n){const{style:e,weight:t,stretch:o,unicodeRange:r,variant:a,featureSettings:s}=n;e&&i.push(`font-style:${e}`),t&&i.push(`font-weight:${t}`),o&&i.push(`font-stretch:${o}`),r&&i.push(`unicode-range:${r}`),a&&i.push(`font-variant:${a}`),s&&i.push(`font-feature-settings:${s}`)}r.innerText=`@font-face{font-family:"${e}";src:${t};${i.join(";")}}`,document.head.appendChild(r),o()}))})(e,t,n).then((()=>{o()})).catch((e=>{r(`loadFontFace:fail ${e}`)}))}));function ew(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,Lx.emit("onNavigationBarChange",{titleText:t})}Yn(t),bo(t)}function tw(e,t,n,o,r){if(!e)return r("page not found");const{navigationBar:i}=e;switch(t){case"setNavigationBarColor":const{frontColor:e,backgroundColor:t,animation:o}=n,{duration:r,timingFunc:a}=o;e&&(i.titleColor="#000000"===e?"#000000":"#ffffff"),t&&(i.backgroundColor=t),i.duration=r+"ms",i.timingFunc=a;break;case"showNavigationBarLoading":i.loading=!0;break;case"hideNavigationBarLoading":i.loading=!1;break;case"setNavigationBarTitle":const{title:s}=n;i.titleText=s}o()}const nw=Ed("setNavigationBarColor",((e,{resolve:t,reject:n})=>{tw(Cc(),"setNavigationBarColor",e,t,n)}),0,Kf),ow=Ed("showNavigationBarLoading",((e,{resolve:t,reject:n})=>{tw(Cc(),"showNavigationBarLoading",e||{},t,n)})),rw=Ed("hideNavigationBarLoading",((e,{resolve:t,reject:n})=>{tw(Cc(),"hideNavigationBarLoading",e||{},t,n)})),iw=Ed("setNavigationBarTitle",((e,{resolve:t,reject:n})=>{tw(Cc(),"setNavigationBarTitle",e,t,n)})),aw=Ed("pageScrollTo",(({scrollTop:e,selector:t,duration:n},{resolve:o})=>{!function(e,t,n){if(I(e)){const t=document.querySelector(e);if(t){const{height:o,top:r}=t.getBoundingClientRect();e=r+window.pageYOffset,n&&(e-=o)}}e<0&&(e=0);const o=document.documentElement,{clientHeight:r,scrollHeight:i}=o;if(e=Math.min(e,i-r),0===t)return void(o.scrollTop=document.body.scrollTop=e);if(window.scrollY===e)return;const a=t=>{if(t<=0)return void window.scrollTo(0,e);const n=e-window.scrollY;requestAnimationFrame((function(){window.scrollTo(0,window.scrollY+n/t*10),a(t-10)}))};a(t)}(t||e||0,n,!0),o()}),0,Zf),sw=Ed("startPullDownRefresh",((e,{resolve:t})=>{Lx.invokeViewMethod("startPullDownRefresh",{},Ec()),t()})),lw=Ed("stopPullDownRefresh",((e,{resolve:t})=>{Lx.invokeViewMethod("stopPullDownRefresh",{},Ec()),t()})),cw=["text","iconPath","iconfont","selectedIconPath","visible"],uw=["color","selectedColor","backgroundColor","borderStyle","midButton"],dw=["badge","redDot"];function pw(e,t,n){t.forEach((function(t){k(n,t)&&(e[t]=n[t])}))}function fw(e,t,n){const o=Dm();switch(e){case"showTabBar":o.shown=!0;break;case"hideTabBar":o.shown=!1;break;case"setTabBarItem":const{index:e}=t,n=o.list[e],r=n.pagePath;pw(n,cw,t);const{pagePath:i}=t;if(i){const t=ae(i);t!==r&&function(e,t,n){const o=Bc(ae(t));if(o){const{meta:e}=o;delete e.tabBarIndex,e.isQuit=e.isTabBar=!1}const r=Bc(ae(n));if(r){const{meta:t}=r;t.tabBarIndex=e,t.isQuit=t.isTabBar=!0;const o=__uniConfig.tabBar;o&&o.list&&o.list[e]&&(o.list[e].pagePath=se(n))}}(e,r,t)}break;case"setTabBarStyle":pw(o,uw,t);break;case"showTabBarRedDot":pw(o.list[t.index],dw,{badge:"",redDot:!0});break;case"setTabBarBadge":pw(o.list[t.index],dw,{badge:t.text,redDot:!0});break;case"hideTabBarRedDot":case"removeTabBarBadge":pw(o.list[t.index],dw,{badge:"",redDot:!1})}n()}const hw=Ed("setTabBarItem",((e,{resolve:t})=>{fw("setTabBarItem",e,t)}),0,ah),gw=Ed("setTabBarStyle",((e,{resolve:t})=>{fw("setTabBarStyle",e,t)}),0,lh),mw=Ed("hideTabBar",((e,{resolve:t})=>{fw("hideTabBar",e||{},t)})),vw=Ed("showTabBar",((e,{resolve:t})=>{fw("showTabBar",e||{},t)})),yw=Ed("hideTabBarRedDot",((e,{resolve:t})=>{fw("hideTabBarRedDot",e,t)}),0,ch),bw=Ed("showTabBarRedDot",((e,{resolve:t})=>{fw("showTabBarRedDot",e,t)}),0,uh),_w=Ed("removeTabBarBadge",((e,{resolve:t})=>{fw("removeTabBarBadge",e,t)}),0,dh),ww=Ed("setTabBarBadge",((e,{resolve:t})=>{fw("setTabBarBadge",e,t)}),0,ph),xw=du({name:"TabBar",setup(){const e=nn([]),t=Dm(),n=w_(t,(()=>{const e=__(t);n.backgroundColor=e.backgroundColor,n.borderStyle=e.borderStyle,n.color=e.color,n.selectedColor=e.selectedColor,n.blurEffect=e.blurEffect,e.list&&e.list.length&&e.list.forEach(((e,t)=>{n.list[t].iconPath=e.iconPath,n.list[t].selectedIconPath=e.selectedIconPath}))}));!function(e,t){function n(){let n=[];n=e.list.filter((e=>!1!==e.visible)),t.value=n}nn(x({type:"midButton"},e.midButton)),Yn(n)}(n,e),function(e){Gn((()=>e.shown),(t=>{fc({"--window-bottom":Wm(t?parseInt(e.height):0)})}))}(n);const o=function(e,t,n){return Yn((()=>{const o=e.meta;if(o.isTabBar){const e=o.route,r=n.value.findIndex((t=>t.pagePath===e));t.selectedIndex=r}})),(t,n)=>()=>{const{pagePath:o,text:r}=t;let i=ae(o);i===__uniRoutes[0].alias&&(i="/"),e.path!==i?uni.switchTab({from:"tabBar",url:i,tabBarText:r}):Ic("onTabItemTap",{index:n,text:r,pagePath:o})}}(hl(),n,e),{style:r,borderStyle:i,placeholderStyle:a}=function(e){const t=vi((()=>{let t=e.backgroundColor;const n=e.blurEffect;return t||zm&&n&&"none"!==n&&(t=Tw[n]),{backgroundColor:t||"#f7f7fa",backdropFilter:"none"!==n?"blur(10px)":n}})),n=vi((()=>{const{borderStyle:t}=e;return{backgroundColor:Sw[t]||t}})),o=vi((()=>({height:e.height})));return{style:t,borderStyle:n,placeholderStyle:o}}(n);return Io((()=>{n.iconfontSrc&&Z_({family:"UniTabbarIconFont",source:`url("${n.iconfontSrc}")`})})),()=>{const t=function(e,t,n){const{selectedIndex:o,selectedColor:r,color:i}=e;return n.value.map(((n,a)=>{const s=o===a;return function(e,t,n,o,r,i,a,s){return Jr("div",{key:a,class:"uni-tabbar__item",onClick:s(r,a)},[kw(e,t||"",n,o,r,i)],8,["onClick"])}(s?r:i,s&&n.selectedIconPath||n.iconPath||"",n.iconfont?s&&n.iconfont.selectedText||n.iconfont.text:void 0,n.iconfont?s&&n.iconfont.selectedColor||n.iconfont.color:void 0,n,e,a,t)}))}(n,o,e);return Jr("uni-tabbar",{class:"uni-tabbar-"+n.position},[Jr("div",{class:"uni-tabbar",style:r.value},[Jr("div",{class:"uni-tabbar-border",style:i.value},null,4),t],4),Jr("div",{class:"uni-placeholder",style:a.value},null,4)],2)}}});const Tw={dark:"rgb(0, 0, 0, 0.8)",light:"rgb(250, 250, 250, 0.8)",extralight:"rgb(250, 250, 250, 0.8)"},Sw={white:"rgba(255, 255, 255, 0.33)",black:"rgba(0, 0, 0, 0.33)"};function kw(e,t,n,o,r,i){const{height:a}=i;return Jr("div",{class:"uni-tabbar__bd",style:{height:a}},[n?Ew(n,o||"rgb(0, 0, 0, 0.8)",r,i):t&&Cw(t,r,i),r.text&&Aw(e,r,i),r.redDot&&Mw(r.badge)],4)}function Cw(e,t,n){const{type:o,text:r}=t,{iconWidth:i}=n;return Jr("div",{class:"uni-tabbar__icon"+(r?" uni-tabbar__icon__diff":""),style:{width:i,height:i}},["midButton"!==o&&Jr("img",{src:Cu(e)},null,8,["src"])],6)}function Ew(e,t,n,o){var r;const{type:i,text:a}=n,{iconWidth:s}=o,l="uni-tabbar__icon"+(a?" uni-tabbar__icon__diff":""),c={width:s,height:s},u={fontSize:(null==(r=n.iconfont)?void 0:r.fontSize)||s,color:t};return Jr("div",{class:l,style:c},["midButton"!==i&&Jr("div",{class:"uni-tabbar__iconfont",style:u},[e],4)],6)}function Aw(e,t,n){const{iconPath:o,text:r}=t,{fontSize:i,spacing:a}=n;return Jr("div",{class:"uni-tabbar__label",style:{color:e,fontSize:i,lineHeight:o?"normal":1.8,marginTop:o?a:"inherit"}},[r],4)}function Mw(e){return Jr("div",{class:"uni-tabbar__reddot"+(e?" uni-tabbar__badge":"")},[e],2)}let Pw;function Iw(){return Pw}const Ow=du({name:"Layout",setup(e,{emit:t}){const n=nn(null);pc({"--status-bar-height":"0px","--top-window-height":"0px","--window-left":"0px","--window-right":"0px","--window-margin":"0px","--tab-bar-height":"0px"});const o=function(){const e=hl();return{routeKey:vi((()=>Zm("/"+e.meta.route,Lm()))),isTabBar:vi((()=>e.meta.isTabBar)),routeCache:tv}}(),{layoutState:r,windowState:i}=function(){Om();{const e=Vt({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return Gn((()=>e.marginWidth),(e=>pc({"--window-margin":e+"px"}))),Gn((()=>e.leftWindowWidth+e.marginWidth),(e=>{pc({"--window-left":e+"px"})})),Gn((()=>e.rightWindowWidth+e.marginWidth),(e=>{pc({"--window-right":e+"px"})})),{layoutState:e,windowState:vi((()=>({})))}}}();!function(e,t){const n=Om();function o(){const o=document.body.clientWidth,r=Xm();let i={};if(r.length>0){i=r[r.length-1].$page.meta}else{const e=Bc(n.path,!0);e&&(i=e.meta)}const a=parseInt(String((k(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let s=!1;s=o>a,s&&a?(e.marginWidth=(o-a)/2,Sn((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+a+"px;margin:0 auto;")}))):(e.marginWidth=0,Sn((()=>{const e=t.value;e&&e.removeAttribute("style")})))}Gn([()=>n.path],o),Io((()=>{o(),window.addEventListener("resize",o)}))}(r,n);const a=function(e){const t=Om(),n=Dm(),o=vi((()=>t.meta.isTabBar&&n.shown));return pc({"--tab-bar-height":n.height}),o}(),s=function(e){const t=nn(!1);return vi((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(a);return Pw=r,()=>{const e=function(e,t,n,o,r,i){return function({routeKey:e,isTabBar:t,routeCache:n}){return Jr(dl,null,{default:qn((({Component:o})=>[(Nr(),Fr(vo,{matchBy:"key",cache:n},[(Nr(),Fr(Uo(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))])),_:1})}(e)}(o),t=function(e){return jo(Jr(xw,null,null,512),[[_a,e.value]])}(a);return Jr("uni-app",{ref:n,class:s.value},[e,t],2)}}});const Lw=Ed("showTopWindow",((e,{resolve:t,reject:n})=>{const o=Iw();o?(o.apiShowTopWindow=!0,Sn(t)):n()})),$w=Ed("hideTopWindow",((e,{resolve:t,reject:n})=>{const o=Iw();o?(o.apiShowTopWindow=!1,Sn(t)):n()})),Dw=Ed("showLeftWindow",((e,{resolve:t,reject:n})=>{const o=Iw();o?(o.apiShowLeftWindow=!0,Sn(t)):n()})),Rw=Ed("hideLeftWindow",((e,{resolve:t,reject:n})=>{const o=Iw();o?(o.apiShowLeftWindow=!1,Sn(t)):n()})),Bw=Ed("showRightWindow",((e,{resolve:t,reject:n})=>{const o=Iw();o?(o.apiShowRightWindow=!0,Sn(t)):n()})),Nw=Ed("hideRightWindow",((e,{resolve:t,reject:n})=>{const o=Iw();o?(o.apiShowRightWindow=!1,Sn(t)):n()})),qw=Cd(0,(()=>{const e=Iw();return x({},e&&e.topWindowStyle)})),jw=Cd(0,(e=>{const t=Iw();t&&(t.topWindowStyle=e)})),zw=Cd(0,(()=>{const e=Iw();return x({},e&&e.leftWindowStyle)})),Vw=Cd(0,(e=>{const t=Iw();t&&(t.leftWindowStyle=e)})),Fw=Cd(0,(()=>{const e=Iw();return x({},e&&e.rightWindowStyle)})),Uw=Cd(0,(e=>{const t=Iw();t&&(t.rightWindowStyle=e)})),Ww=Ed("saveImageToPhotosAlbum",Id("saveImageToPhotosAlbum")),Hw=Cd(0,Md("getRecorderManager")),Yw=Ed("saveVideoToPhotosAlbum",Id("saveVideoToPhotosAlbum")),Xw=Cd(0,Md("createCameraContext")),Gw=Cd(0,Md("createLivePlayerContext")),Jw=Ed("saveFile",Id("saveFile")),Qw=Ed("getSavedFileList",Id("getSavedFileList")),Kw=Ed("getSavedFileInfo",Id("getSavedFileInfo")),Zw=Ed("removeSavedFile",Id("removeSavedFile")),ex=Td("onMemoryWarning",Pd("onMemoryWarning")),tx=Td("onGyroscopeChange",Pd("onGyroscopeChange")),nx=Ed("startGyroscope",Id("startGyroscope")),ox=Ed("stopGyroscope",Id("stopGyroscope")),rx=Ed("scanCode",Id("scanCode")),ix=Ed("setScreenBrightness",Id("setScreenBrightness")),ax=Ed("getScreenBrightness",Id("getScreenBrightness")),sx=Ed("setKeepScreenOn",Id("setKeepScreenOn")),lx=Td("onUserCaptureScreen",Pd("onUserCaptureScreen")),cx=Ed("addPhoneContact",Id("addPhoneContact")),ux=Ed("login",Id("login")),dx=Ed("getProvider",Id("getProvider")),px=Object.defineProperty({__proto__:null,$emit:Qd,$off:Jd,$on:Xd,$once:Gd,addInterceptor:Wd,addPhoneContact:cx,arrayBufferToBase64:Ld,base64ToArrayBuffer:Od,canIUse:Fm,canvasGetImageData:Sp,canvasPutImageData:kp,canvasToTempFilePath:Cp,chooseFile:ab,chooseImage:lb,chooseLocation:t_,chooseVideo:Mb,clearStorage:Gy,clearStorageSync:Xy,closePreviewImage:Eb,closeSocket:Fb,connectSocket:jb,createAnimation:Gp,createCameraContext:Xw,createCanvasContext:Tp,createInnerAudioContext:ny,createIntersectionObserver:$p,createLivePlayerContext:Gw,createMapContext:op,createMediaQueryObserver:Bp,createSelectorQuery:Up,createVideoContext:ep,cssBackdropFilter:zm,cssConstant:jm,cssEnv:qm,cssVar:Nm,downloadFile:$b,getAppBaseInfo:hy,getClipboardData:Ry,getDeviceInfo:fy,getEnterOptionsSync:lf,getFileInfo:Ky,getImageInfo:tb,getLaunchOptionsSync:cf,getLeftWindowStyle:zw,getLocale:Zp,getLocation:Gb,getNetworkType:wy,getProvider:dx,getPushClientId:mf,getRecorderManager:Hw,getRightWindowStyle:Fw,getSavedFileInfo:Kw,getSavedFileList:Qw,getScreenBrightness:ax,getSelectedTextRange:of,getStorage:Wy,getStorageInfo:Qy,getStorageInfoSync:Jy,getStorageSync:Uy,getSystemInfo:my,getSystemInfoSync:gy,getTopWindowStyle:qw,getVideoInfo:nb,getWindowInfo:cy,hideKeyboard:eb,hideLeftWindow:Rw,hideLoading:V_,hideNavigationBarLoading:rw,hideRightWindow:Nw,hideTabBar:mw,hideTabBarRedDot:yw,hideToast:z_,hideTopWindow:$w,interceptors:{},invokePushCallback:function(e){if("enabled"===e.type)pf=!0;else if("clientId"===e.type)uf=e.cid,df=e.errMsg,gf(uf,e.errMsg);else if("pushMsg"===e.type){const t={type:"receive",data:ff(e.message)};for(let e=0;e<vf.length;e++){if((0,vf[e])(t),t.stopped)break}}else"click"===e.type&&vf.forEach((t=>{t({type:"click",data:ff(e.message)})}))},loadFontFace:Z_,login:ux,makePhoneCall:oy,navigateBack:u_,navigateTo:p_,offAccelerometerChange:Sy,offAppHide:function(e){sf("onHide",e)},offAppShow:function(e){sf("onShow",e)},offCompassChange:My,offError:function(e){sf("onError",e)},offLocationChange:s_,offLocationChangeError:c_,offNetworkStatusChange:_y,offPageNotFound:function(e){sf("onPageNotFound",e)},offPushMessage:e=>{if(e){const t=vf.indexOf(e);t>-1&&vf.splice(t,1)}else vf.length=0},offThemeChange:jy,offUnhandledRejection:function(e){sf("onUnhandledRejection",e)},offWindowResize:Kp,onAccelerometerChange:Ty,onAppHide:function(e){af("onHide",e)},onAppShow:function(e){af("onShow",e)},onCompassChange:Ay,onCreateVueApp:function(e){if(Ee)return e(Ee);Ae.push(e)},onError:function(e){af("onError",e)},onGyroscopeChange:tx,onLocaleChange:ef,onLocationChange:a_,onLocationChangeError:l_,onMemoryWarning:ex,onNetworkStatusChange:by,onPageNotFound:function(e){af("onPageNotFound",e)},onPushMessage:e=>{-1===vf.indexOf(e)&&vf.push(e)},onSocketClose:Xb,onSocketError:Hb,onSocketMessage:Yb,onSocketOpen:Wb,onTabBarMidButtonTap:Jp,onThemeChange:qy,onUnhandledRejection:function(e){af("onUnhandledRejection",e)},onUserCaptureScreen:lx,onWindowResize:Qp,openDocument:Zy,openLocation:Kb,pageScrollTo:aw,preloadPage:v_,previewImage:Cb,reLaunch:h_,redirectTo:f_,removeInterceptor:Hd,removeSavedFile:Zw,removeStorage:Yy,removeStorageSync:Hy,removeTabBarBadge:_w,request:Pb,saveFile:Jw,saveImageToPhotosAlbum:Ww,saveVideoToPhotosAlbum:Yw,scanCode:rx,sendSocketMessage:Vb,setClipboardData:By,setKeepScreenOn:sx,setLeftWindowStyle:Vw,setLocale:tf,setNavigationBarColor:nw,setNavigationBarTitle:iw,setPageMeta:nf,setRightWindowStyle:Uw,setScreenBrightness:ix,setStorage:Vy,setStorageSync:zy,setTabBarBadge:ww,setTabBarItem:hw,setTabBarStyle:gw,setTopWindowStyle:jw,showActionSheet:K_,showLeftWindow:Dw,showLoading:j_,showModal:A_,showNavigationBarLoading:ow,showRightWindow:Bw,showTabBar:vw,showTabBarRedDot:bw,showToast:N_,showTopWindow:Lw,startAccelerometer:ky,startCompass:Py,startGyroscope:nx,startLocationUpdate:r_,startPullDownRefresh:sw,stopAccelerometer:Cy,stopCompass:Iy,stopGyroscope:ox,stopLocationUpdate:i_,stopPullDownRefresh:lw,switchTab:m_,uploadFile:Rb,upx2px:Vd,vibrateLong:$y,vibrateShort:Ly},Symbol.toStringTag,{value:"Module"}),fx=du({name:"MapLocation",setup(){const e=Vt({latitude:0,longitude:0,rotate:0});{let t=function(t){e.rotate=t.direction},n=function(){Gb({type:"gcj02",success:t=>{e.latitude=t.latitude,e.longitude=t.longitude},complete:()=>{i=setTimeout(n,3e4)}})},o=function(){i&&clearTimeout(i),My(t)};const r=Hn("onMapReady");let i;Ay(t),r(n),Do(o);const a=Hn("addMapChidlContext"),s=Hn("removeMapChidlContext"),l={id:"MAP_LOCATION",state:e};a(l),Do((()=>s(l)))}return()=>e.latitude?Jr(Yv,oi({anchor:{x:.5,y:.5},width:"44",height:"44",iconPath:Nv},e),null,16,["iconPath"]):null}}),hx=du({name:"MapPolygon",props:{dashArray:{type:Array,default:()=>[0,0]},points:{type:Array,required:!0},strokeWidth:{type:Number,default:1},strokeColor:{type:String,default:"#000000"},fillColor:{type:String,default:"#00000000"},zIndex:{type:Number,default:0}},setup(e){let t;return Hn("onMapReady")(((n,o,r)=>{function i(){const{points:r,strokeWidth:i,strokeColor:a,dashArray:s,fillColor:l,zIndex:c}=e,u=r.map((e=>{const{latitude:t,longitude:n}=e;return Uv()?[n,t]:Wv()?new o.Point(n,t):new o.LatLng(t,n)})),{r:d,g:p,b:f,a:h}=Xv(l),{r:g,g:m,b:v,a:y}=Xv(a),b={clickable:!0,cursor:"crosshair",editable:!1,map:n,fillColor:"",path:u,strokeColor:"",strokeDashStyle:s.some((e=>e>0))?"dash":"solid",strokeWeight:i,visible:!0,zIndex:c};o.Color?(b.fillColor=new o.Color(d,p,f,h),b.strokeColor=new o.Color(g,m,v,y)):(b.fillColor=`rgb(${d}, ${p}, ${f})`,b.fillOpacity=h,b.strokeColor=`rgb(${g}, ${m}, ${v})`,b.strokeOpacity=y),t?t.setOptions(b):Wv()?(t=new o.Polygon(b.path,b),n.addOverlay(t)):t=new o.Polygon(b)}i(),Gn(e,i)})),Do((()=>{t.setMap(null)})),()=>null}});function gx(e){const t=[];return C(e)&&e.forEach((e=>{e&&e.latitude&&e.longitude&&t.push({latitude:e.latitude,longitude:e.longitude})})),t}function mx(e,t,n){return Wv()?function(e,t,n){return new e.Point(n,t)}(e,t,n):Uv()?function(e,t,n){return new e.LngLat(n,t)}(e,t,n):function(e,t,n){return new e.LatLng(t,n)}(e,t,n)}function vx(e){return"getLat"in e?e.getLat():Wv()?e.lat:e.lat()}function yx(e){return"getLng"in e?e.getLng():Wv()?e.lng:e.lng()}function bx(e,t,n){const o=mu(t,n),r=nn(null);let i,a;const s=Vt({latitude:Number(e.latitude),longitude:Number(e.longitude),includePoints:gx(e.includePoints)}),l=[];let c,u;function d(e){c?e(a,i,o):l.push(e)}const p=[];function f(e){u?e():l.push(e)}const h={};function g(){const e=a.getCenter();return{scale:a.getZoom(),centerLocation:{latitude:vx(e),longitude:yx(e)}}}function m(){if(Uv()){const e=[];s.includePoints.forEach((t=>{e.push([t.longitude,t.latitude])}));const t=new i.Bounds(...e);a.setBounds(t)}else if(Wv());else{const e=new i.LatLngBounds;s.includePoints.forEach((({latitude:t,longitude:n})=>{const o=new i.LatLng(t,n);e.extend(o)})),a.fitBounds(e)}}function v(){const t=r.value,l=mx(i,s.latitude,s.longitude),c=i.event||i.Event,d=new i.Map(t,{center:l,zoom:Number(e.scale),disableDoubleClickZoom:!0,mapTypeControl:!1,zoomControl:!1,scaleControl:!1,panControl:!1,fullscreenControl:!1,streetViewControl:!1,keyboardShortcuts:!1,minZoom:5,maxZoom:18,draggable:!0});if(Wv()&&(d.centerAndZoom(l,Number(e.scale)),d.enableScrollWheelZoom(),d._printLog&&d._printLog("uniapp")),Gn((()=>e.scale),(e=>{d.setZoom(Number(e)||16)})),f((()=>{s.includePoints.length&&(m(),function(){const e=mx(i,s.latitude,s.longitude);a.setCenter(e)}())})),Wv())d.addEventListener("click",(()=>{o("tap",{},{}),o("click",{},{})})),d.addEventListener("dragstart",(()=>{o("regionchange",{},{type:"begin",causedBy:"gesture"})})),d.addEventListener("dragend",(()=>{o("regionchange",{},x({type:"end",causedBy:"drag"},g()))}));else{const e=c.addListener(d,"bounds_changed",(()=>{e.remove(),u=!0,p.forEach((e=>e())),p.length=0}));c.addListener(d,"click",(()=>{o("tap",{},{}),o("click",{},{})})),c.addListener(d,"dragstart",(()=>{o("regionchange",{},{type:"begin",causedBy:"gesture"})})),c.addListener(d,"dragend",(()=>{o("regionchange",{},x({type:"end",causedBy:"drag"},g()))}));const t=()=>{n("update:scale",d.getZoom()),o("regionchange",{},x({type:"end",causedBy:"scale"},g()))};c.addListener(d,"zoom_changed",t),c.addListener(d,"zoomend",t),c.addListener(d,"center_changed",(()=>{const e=d.getCenter(),t=vx(e),o=yx(e);n("update:latitude",t),n("update:longitude",o)}))}return d}Gn([()=>e.latitude,()=>e.longitude],(([e,t])=>{const n=Number(e),o=Number(t);if((n!==s.latitude||o!==s.longitude)&&(s.latitude=n,s.longitude=o,a)){const e=mx(i,s.latitude,s.longitude);a.setCenter(e)}})),Gn((()=>e.includePoints),(e=>{s.includePoints=gx(e),u&&m()}),{deep:!0});try{ym(((e,t={})=>{switch(e){case"getCenterLocation":d((()=>{const n=a.getCenter();de(t,{latitude:vx(n),longitude:yx(n),errMsg:`${e}:ok`})}));break;case"moveToLocation":{let n=Number(t.latitude),o=Number(t.longitude);if(!n||!o){const e=h.MAP_LOCATION;e&&(n=e.state.latitude,o=e.state.longitude)}if(n&&o){if(s.latitude=n,s.longitude=o,a){const e=mx(i,n,o);a.setCenter(e)}d((()=>{de(t,`${e}:ok`)}))}else de(t,`${e}:fail`)}break;case"translateMarker":d((()=>{const n=h[t.markerId];if(n){try{n.translate(t)}catch(o){de(t,`${e}:fail ${o.message}`)}de(t,`${e}:ok`)}else de(t,`${e}:fail not found`)}));break;case"includePoints":s.includePoints=gx(t.includePoints),(u||Uv())&&m(),f((()=>{de(t,`${e}:ok`)}));break;case"getRegion":f((()=>{const n=a.getBounds(),o=n.getSouthWest(),r=n.getNorthEast();de(t,{southwest:{latitude:vx(o),longitude:yx(o)},northeast:{latitude:vx(r),longitude:yx(r)},errMsg:`${e}:ok`})}));break;case"getScale":d((()=>{de(t,{scale:a.getZoom(),errMsg:`${e}:ok`})}))}}),_m(),!0)}catch(y){}return Io((()=>{Dv(e.libraries,(e=>{i=e,a=v(),c=!0,l.forEach((e=>e(a,i,o))),l.length=0,o("updated",{},{})}))})),Wn("onMapReady",d),Wn("addMapChidlContext",(function(e){h[e.id]=e})),Wn("removeMapChidlContext",(function(e){delete h[e.id]})),{state:s,mapRef:r,trigger:o}}const _x=uu({name:"Map",props:{id:{type:String,default:""},latitude:{type:[String,Number],default:0},longitude:{type:[String,Number],default:0},scale:{type:[String,Number],default:16},markers:{type:Array,default:()=>[]},includePoints:{type:Array,default:()=>[]},polyline:{type:Array,default:()=>[]},circles:{type:Array,default:()=>[]},controls:{type:Array,default:()=>[]},showLocation:{type:[Boolean,String],default:!1},libraries:{type:Array,default:()=>[]},polygons:{type:Array,default:()=>[]}},emits:["markertap","labeltap","callouttap","controltap","regionchange","tap","click","updated","update:scale","update:latitude","update:longitude"],setup(e,{emit:t,slots:n}){const o=nn(null),{mapRef:r,trigger:i}=bx(e,o,t);return()=>Jr("uni-map",{ref:o,id:e.id},[Jr("div",{ref:r,style:"width: 100%; height: 100%; position: relative; overflow: hidden"},null,512),e.markers.map((e=>Jr(Yv,oi({key:e.id},e),null,16))),e.polyline.map((e=>Jr(Jv,e,null,16))),e.circles.map((e=>Jr(Qv,e,null,16))),e.controls.map((e=>Jr(Zv,oi(e,{trigger:i}),null,16,["trigger"]))),e.showLocation&&Jr(fx,null,null),e.polygons.map((e=>Jr(hx,e,null,16))),Jr("div",{style:"position: absolute;top: 0;width: 100%;height: 100%;overflow: hidden;pointer-events: none;"},[n.default&&n.default()])],8,["id"])}});function xx(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!Ur(e)}function Tx(e){if(e.mode===Cx.TIME)return"00:00";if(e.mode===Cx.DATE){const t=(new Date).getFullYear()-150;switch(e.fields){case Ex.YEAR:return t.toString();case Ex.MONTH:return t+"-01";default:return t+"-01-01"}}return""}function Sx(e){if(e.mode===Cx.TIME)return"23:59";if(e.mode===Cx.DATE){const t=(new Date).getFullYear()+150;switch(e.fields){case Ex.YEAR:return t.toString();case Ex.MONTH:return t+"-12";default:return t+"-12-31"}}return""}function kx(e,t,n,o){const r=e.mode===Cx.DATE?"-":":",i=e.mode===Cx.DATE?t.dateArray:t.timeArray;let a;if(e.mode===Cx.TIME)a=2;else switch(e.fields){case Ex.YEAR:a=1;break;case Ex.MONTH:a=2;break;default:a=3}const s=String(n).split(r);let l=[];for(let c=0;c<a;c++){const e=s[c];l.push(i[c].indexOf(e))}return l.indexOf(-1)>=0&&(l=o?kx(e,t,o):l.map((()=>0))),l}const Cx={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date"},Ex={YEAR:"year",MONTH:"month",DAY:"day"},Ax={PICKER:"picker",SELECT:"select"},Mx=uu({name:"Picker",compatConfig:{MODE:3},props:{name:{type:String,default:""},range:{type:Array,default:()=>[]},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:Cx.SELECTOR,validator:e=>Object.values(Cx).includes(e)},fields:{type:String,default:""},start:{type:String,default:e=>Tx(e)},end:{type:String,default:e=>Sx(e)},disabled:{type:[Boolean,String],default:!1},selectorType:{type:String,default:""}},emits:["change","cancel","columnchange"],setup(e,{emit:t,slots:n}){Pl();const{t:o}=_l(),r=nn(null),i=nn(null),a=nn(null),s=nn(null),l=nn(!1),{state:c,rangeArray:u}=function(e){const t=Vt({valueSync:void 0,visible:!1,contentVisible:!1,popover:null,valueChangeSource:"",timeArray:[],dateArray:[],valueArray:[],oldValueArray:[],isDesktop:!1,popupStyle:{content:{},triangle:{}}}),n=vi((()=>{let n=e.range;switch(e.mode){case Cx.SELECTOR:return[n];case Cx.MULTISELECTOR:return n;case Cx.TIME:return t.timeArray;case Cx.DATE:{const n=t.dateArray;switch(e.fields){case Ex.YEAR:return[n[0]];case Ex.MONTH:return[n[0],n[1]];default:return[n[0],n[1],n[2]]}}}return[]}));return{state:t,rangeArray:n}}(e),d=mu(r,t),{system:p,selectorTypeComputed:f,_show:h,_l10nColumn:g,_l10nItem:m,_input:v,_fixInputPosition:y,_pickerViewChange:b,_cancel:_,_change:w,_resetFormData:x,_getFormData:T,_createTime:S,_createDate:k,_setValueSync:E}=function(e,t,n,o,r,i,a){const s=function(){const e=nn(!1);return e.value=(()=>0===String(navigator.vendor).indexOf("Apple")&&navigator.maxTouchPoints>0)(),e}(),l=function(){const e=nn("");return e.value=(()=>{if(/win|mac/i.test(navigator.platform)){if("Google Inc."===navigator.vendor)return"chrome";if(/Firefox/.test(navigator.userAgent))return"firefox"}return""})(),e}(),c=vi((()=>{const t=e.selectorType;return Object.values(Ax).includes(t)?t:s.value?Ax.PICKER:Ax.SELECT})),u=vi((()=>e.mode===Cx.DATE&&!Object.values(Ex).includes(e.fields)&&t.isDesktop?l.value:"")),d=vi((()=>kx(e,t,e.start,Tx(e)))),p=vi((()=>kx(e,t,e.end,Sx(e))));function f(n){if(e.disabled)return;t.valueChangeSource="";let o=r.value,i=n.currentTarget;o.remove(),(document.querySelector("uni-app")||document.body).appendChild(o),o.style.display="block";const a=i.getBoundingClientRect();t.popover={top:a.top,left:a.left,width:a.width,height:a.height},setTimeout((()=>{t.visible=!0}),20)}function h(){return{value:t.valueSync,key:e.name}}function g(){switch(e.mode){case Cx.SELECTOR:t.valueSync=0;break;case Cx.MULTISELECTOR:t.valueSync=e.value.map((e=>0));break;case Cx.DATE:case Cx.TIME:t.valueSync=""}}function m(){let e=[],n=[];for(let t=0;t<24;t++)e.push((t<10?"0":"")+t);for(let t=0;t<60;t++)n.push((t<10?"0":"")+t);t.timeArray.push(e,n)}function v(){let t=(new Date).getFullYear(),n=t-150,o=t+150;if(e.start){const t=new Date(e.start).getFullYear();!isNaN(t)&&t<n&&(n=t)}if(e.end){const t=new Date(e.end).getFullYear();!isNaN(t)&&t>o&&(o=t)}return{start:n,end:o}}function y(){let e=[];const n=v();for(let t=n.start,i=n.end;t<=i;t++)e.push(String(t));let o=[];for(let t=1;t<=12;t++)o.push((t<10?"0":"")+t);let r=[];for(let t=1;t<=31;t++)r.push((t<10?"0":"")+t);t.dateArray.push(e,o,r)}function b(e){return 60*e[0]+e[1]}function _(e){const t=31;return e[0]*t*12+(e[1]||0)*t+(e[2]||0)}function w(e,t){for(let n=0;n<e.length&&n<t.length;n++)e[n]=t[n]}function x(){let n=e.value;switch(e.mode){case Cx.MULTISELECTOR:{C(n)||(n=t.valueArray),C(t.valueSync)||(t.valueSync=[]);const o=t.valueSync.length=Math.max(n.length,e.range.length);for(let r=0;r<o;r++){const o=Number(n[r]),i=Number(t.valueSync[r]),a=isNaN(o)?isNaN(i)?0:i:o,s=e.range[r]?e.range[r].length-1:0;t.valueSync.splice(r,1,a<0||a>s?0:a)}}break;case Cx.TIME:case Cx.DATE:t.valueSync=String(n);break;default:{const e=Number(n);t.valueSync=e<0?0:e;break}}}function T(){let n,o=t.valueSync;switch(e.mode){case Cx.MULTISELECTOR:n=[...o];break;case Cx.TIME:n=kx(e,t,o,ue({mode:Cx.TIME}));break;case Cx.DATE:n=kx(e,t,o,ue({mode:Cx.DATE}));break;default:n=[o]}t.oldValueArray=[...n],t.valueArray=[...n]}function S(){let n=t.valueArray;switch(e.mode){case Cx.SELECTOR:return n[0];case Cx.MULTISELECTOR:return n.map((e=>e));case Cx.TIME:return t.valueArray.map(((e,n)=>t.timeArray[n][e])).join(":");case Cx.DATE:return t.valueArray.map(((e,n)=>t.dateArray[n][e])).join("-")}}function k(){A(),t.valueChangeSource="click";const e=S();t.valueSync=C(e)?e.map((e=>e)):e,n("change",{},{value:e})}function E(e){if("firefox"===u.value&&e){const{top:n,left:o,width:r,height:i}=t.popover,{pageX:a,pageY:s}=e;if(a>o&&a<o+r&&s>n&&s<n+i)return}A(),n("cancel",{},{})}function A(){t.visible=!1,setTimeout((()=>{let e=r.value;e.remove(),o.value.prepend(e),e.style.display="none"}),260)}function M(){e.mode===Cx.SELECTOR&&c.value===Ax.SELECT&&(i.value.scrollTop=34*t.valueArray[0])}function P(e){const n=e.target;t.valueSync=n.value,Sn((()=>{k()}))}function I(e){if("chrome"===u.value){const t=o.value.getBoundingClientRect(),n=32;a.value.style.left=e.clientX-t.left-1.5*n+"px",a.value.style.top=e.clientY-t.top-.5*n+"px"}}function O(e){t.valueArray=L(e.detail.value,!0)}function L(t,n){const{getLocale:o}=_l();if(e.mode===Cx.DATE){const r=o();if(!r.startsWith("zh"))switch(e.fields){case Ex.YEAR:return t;case Ex.MONTH:return[t[1],t[0]];default:switch(r){case"es":case"fr":return[t[2],t[1],t[0]];default:return n?[t[2],t[0],t[1]]:[t[1],t[2],t[0]]}}}return t}function $(t,n){const{getLocale:o}=_l();if(e.mode===Cx.DATE){const r=o();if(r.startsWith("zh")){return t+["年","月","日"][n]}if(e.fields!==Ex.YEAR&&n===(e.fields===Ex.MONTH||"es"!==r&&"fr"!==r?0:1)){let e;switch(r){case"es":e=["enero","febrero","marzo","abril","mayo","junio","​​julio","agosto","septiembre","octubre","noviembre","diciembre"];break;case"fr":e=["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"];break;default:e=["January","February","March","April","May","June","July","August","September","October","November","December"]}return e[Number(t)-1]}}return t}return Gn((()=>t.visible),(e=>{e?(clearTimeout(Px),t.contentVisible=e,M()):Px=setTimeout((()=>{t.contentVisible=e}),300)})),Gn([()=>e.mode,()=>e.value,()=>e.range],x,{deep:!0}),Gn((()=>t.valueSync),T,{deep:!0}),Gn((()=>t.valueArray),(o=>{if(e.mode===Cx.TIME||e.mode===Cx.DATE){const n=e.mode===Cx.TIME?b:_,o=t.valueArray,r=d.value,i=p.value;if(e.mode===Cx.DATE){const e=t.dateArray,n=e[2].length,r=Number(e[2][o[2]])||1,i=new Date(`${e[0][o[0]]}/${e[1][o[1]]}/${r}`).getDate();i<r&&(o[2]-=i+n-r)}n(o)<n(r)?w(o,r):n(o)>n(i)&&w(o,i)}o.forEach(((o,r)=>{o!==t.oldValueArray[r]&&(t.oldValueArray[r]=o,e.mode===Cx.MULTISELECTOR&&n("columnchange",{},{column:r,value:o}))}))})),{selectorTypeComputed:c,system:u,_show:f,_cancel:E,_change:k,_l10nColumn:L,_l10nItem:$,_input:P,_resetFormData:g,_getFormData:h,_createTime:m,_createDate:y,_setValueSync:x,_fixInputPosition:I,_pickerViewChange:O}}(e,c,d,r,i,a,s);!function(e,t,n){const{key:o,disable:r}=db();Yn((()=>{r.value=!e.visible})),Gn(o,(e=>{"esc"===e?t():"enter"===e&&n()}))}(c,_,w),function(e,t){const n=Hn(vu,!1);if(n){const o={reset:e,submit:()=>{const e=["",null],{key:n,value:o}=t();return""!==n&&(e[0]=n,e[1]=o),e}};n.addField(o),$o((()=>{n.removeField(o)}))}}(x,T),S(),k(),E();const A=U_(c);return Yn((()=>{c.isDesktop=A.isDesktop.value,c.popupStyle=A.popupStyle.value})),$o((()=>{i.value&&i.value.remove()})),Io((()=>{l.value=!0})),()=>{let t;const{visible:d,contentVisible:x,valueArray:T,popupStyle:S,valueSync:k}=c,{rangeKey:C,mode:E,start:A,end:M}=e,P=hu(e,"disabled");return Jr("uni-picker",oi({ref:r},P,{onClick:gu(h)}),[l.value?Jr("div",{ref:i,class:["uni-picker-container",`uni-${E}-${f.value}`],onWheel:sc,onTouchmove:sc},[Jr(Hi,{name:"uni-fade"},{default:()=>[jo(Jr("div",{class:"uni-mask uni-picker-mask",onClick:gu(_),onMousemove:y},null,40,["onClick","onMousemove"]),[[_a,d]])]}),p.value?null:Jr("div",{class:[{"uni-picker-toggle":d},"uni-picker-custom"],style:S.content},[Jr("div",{class:"uni-picker-header",onClick:lc},[Jr("div",{class:"uni-picker-action uni-picker-action-cancel",onClick:gu(_)},[o("uni.picker.cancel")],8,["onClick"]),Jr("div",{class:"uni-picker-action uni-picker-action-confirm",onClick:w},[o("uni.picker.done")],8,["onClick"])],8,["onClick"]),x?Jr(Ng,{value:g(T),class:"uni-picker-content",onChange:b},xx(t=Yo(g(u.value),((e,t)=>{let n;return Jr(Yg,{key:t},xx(n=Yo(e,((e,n)=>Jr("div",{key:n,class:"uni-picker-item"},["object"==typeof e?e[C]||"":m(e,t)]))))?n:{default:()=>[n],_:1})})))?t:{default:()=>[t],_:1},8,["value","onChange"]):null,Jr("div",{ref:a,class:"uni-picker-select",onWheel:lc,onTouchmove:lc},[Yo(u.value[0],((e,t)=>Jr("div",{key:t,class:["uni-picker-item",{selected:T[0]===t}],onClick:()=>{T[0]=t,w()}},["object"==typeof e?e[C]||"":e],10,["onClick"])))],40,["onWheel","onTouchmove"]),Jr("div",{style:S.triangle},null,4)],6)],40,["onWheel","onTouchmove"]):null,Jr("div",null,[n.default&&n.default()]),p.value?Jr("div",{class:"uni-picker-system",onMousemove:gu(y)},[Jr("input",{class:["uni-picker-system_input",p.value],ref:s,value:k,type:E,tabindex:"-1",min:A,max:M,onChange:e=>{v(e),lc(e)}},null,42,["value","type","min","max","onChange"])],40,["onMousemove"]):null],16,["onClick"])}}});let Px;const Ix=x(ql,{publishHandler(e,t,n){Lx.subscribeHandler(e,t,n)}}),Ox=px,Lx=x(Qc,{publishHandler(e,t,n){Ix.subscribeHandler(e,t,n)}}),$x=du({name:"PageHead",setup(){const e=nn(null),t=Pm(),n=w_(t.navigationBar,(()=>{const e=__(t.navigationBar);n.backgroundColor=e.backgroundColor,n.titleColor=e.titleColor})),{clazz:o,style:r}=function(e){const t=vi((()=>{const{type:t,titlePenetrate:n,shadowColorType:o}=e,r={"uni-page-head":!0,"uni-page-head-transparent":"transparent"===t,"uni-page-head-titlePenetrate":"YES"===n,"uni-page-head-shadow":!!o};return o&&(r[`uni-page-head-shadow-${o}`]=!0),r})),n=vi((()=>({backgroundColor:e.backgroundColor,color:e.titleColor,transitionDuration:e.duration,transitionTimingFunction:e.timingFunc})));return{clazz:t,style:n}}(n);return()=>{const i=function(e,t){if(!t)return Jr("div",{class:"uni-page-head-btn",onClick:Rx},[xc(bc,"transparent"===e.type?"#fff":e.titleColor,27)],8,["onClick"])}(n,t.isQuit),a=n.type||"default",s="transparent"!==a&&"float"!==a&&Jr("div",{class:{"uni-placeholder":!0,"uni-placeholder-titlePenetrate":n.titlePenetrate}},null,2);return Jr("uni-page-head",{"uni-page-head-type":a},[Jr("div",{ref:e,class:o.value,style:r.value},[Jr("div",{class:"uni-page-head-hd"},[i]),Dx(n),Jr("div",{class:"uni-page-head-ft"},[])],6),s],8,["uni-page-head-type"])}}});function Dx(e,t){return function({type:e,loading:t,titleSize:n,titleText:o,titleImage:r}){return Jr("div",{class:"uni-page-head-bd"},[Jr("div",{style:{fontSize:n,opacity:"transparent"===e?0:1},class:"uni-page-head__title"},[t?Jr("i",{class:"uni-loading"},null):r?Jr("img",{src:r,class:"uni-page-head__title_image"},null,8,["src"]):o],4)])}(e)}function Rx(){1===Xm().length?uni.reLaunch({url:"/"}):uni.navigateBack({from:"backbutton",success(){}})}const Bx={name:"PageRefresh",setup(){const{pullToRefresh:e}=Pm();return{offset:e.offset,color:e.color}}},Nx=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},qx={class:"uni-page-refresh-inner"},jx=["fill"],zx=[Gr("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null,-1),Gr("path",{d:"M0 0h24v24H0z",fill:"none"},null,-1)],Vx={class:"uni-page-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},Fx=["stroke"];const Ux=Nx(Bx,[["render",function(e,t,n,r,i,a){return Nr(),Vr("uni-page-refresh",null,[Gr("div",{style:o({"margin-top":r.offset+"px"}),class:"uni-page-refresh"},[Gr("div",qx,[(Nr(),Vr("svg",{fill:r.color,class:"uni-page-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},zx,8,jx)),(Nr(),Vr("svg",Vx,[Gr("circle",{stroke:r.color,class:"uni-page-refresh__path",cx:"50",cy:"50",r:"20",fill:"none","stroke-width":"4","stroke-miterlimit":"10"},null,8,Fx)]))])],4)])}]]);function Wx(e,t,n){const o=Array.prototype.slice.call(e.changedTouches).filter((e=>e.identifier===t))[0];return!!o&&(e.deltaY=o.pageY-n,!0)}const Hx="aborting",Yx="refreshing",Xx="restoring";function Gx(e){const{id:t,pullToRefresh:n}=Pm(),{range:o,height:r}=n;let i,a,s,l,c,u,d,p;ym((()=>{p||(p=Yx,m(),setTimeout((()=>{w()}),50))}),"startPullDownRefresh",!1,t),ym((()=>{p===Yx&&(v(),p=Xx,m(),function(e){if(!a)return;s.transition="-webkit-transform 0.3s",s.transform+=" scale(0.01)";const t=function(){n&&clearTimeout(n),a.removeEventListener("webkitTransitionEnd",t),s.transition="",s.transform="translate3d(-50%, 0, 0)",e()};a.addEventListener("webkitTransitionEnd",t);const n=setTimeout(t,350)}((()=>{v(),p=f=h=null})))}),"stopPullDownRefresh",!1,t),Io((()=>{i=e.value.$el,a=i.querySelector(".uni-page-refresh"),s=a.style,l=a.querySelector(".uni-page-refresh-inner").style}));let f=null,h=null;function g(e){p&&i&&i.classList[e]("uni-page-refresh--"+p)}function m(){g("add")}function v(){g("remove")}const y=gu((e=>{const t=e.changedTouches[0];c=t.identifier,u=t.pageY,d=!([Hx,Yx,Xx].indexOf(p)>=0)})),b=gu((e=>{if(!d)return;if(!Wx(e,c,u))return;let{deltaY:t}=e;if(0!==(document.documentElement.scrollTop||document.body.scrollTop))return void(c=null);if(t<0&&!p)return;e.preventDefault(),null===f&&(h=t,p="pulling",m()),t-=h,t<0&&(t=0),f=t;(t>=o&&"reached"!==p||t<o&&"pulling"!==p)&&(v(),p="reached"===p?"pulling":"reached",m()),function(e){if(!a)return;let t=e/o;t>1?t=1:t*=t*t;const n=Math.round(e/(o/r))||0;l.transform="rotate("+360*t+"deg)",s.clip="rect("+(45-n)+"px,45px,45px,-5px)",s.transform="translate3d(-50%, "+n+"px, 0)"}(t)})),_=gu((e=>{Wx(e,c,u)&&null!==p&&("pulling"===p?(v(),p=Hx,m(),function(e){if(!a)return;if(s.transform){s.transition="-webkit-transform 0.3s",s.transform="translate3d(-50%, 0, 0)";const t=function(){n&&clearTimeout(n),a.removeEventListener("webkitTransitionEnd",t),s.transition="",e()};a.addEventListener("webkitTransitionEnd",t);const n=setTimeout(t,350)}else e()}((()=>{v(),p=f=h=null}))):"reached"===p&&(v(),p=Yx,m(),w()))}));function w(){a&&(s.transition="-webkit-transform 0.2s",s.transform="translate3d(-50%, "+r+"px, 0)",Ic(t,"onPullDownRefresh"))}return{onTouchstartPassive:y,onTouchmove:b,onTouchend:_,onTouchcancel:_}}const Jx=du({name:"PageBody",setup(e,t){const n=Pm(),o=nn(null),r=n.enablePullDownRefresh?Gx(o):null;return()=>{const e=function(e,t){if(!t.enablePullDownRefresh)return null;return Jr(Ux,{ref:e},null,512)}(o,n);return Jr(Or,null,[e,Jr("uni-page-wrapper",r,[Jr("uni-page-body",null,[Xo(t.slots,"default")])],16)])}}});const Qx=du({name:"Page",setup(e,t){const n=Im(Lm()),o=n.navigationBar;return ew(n),()=>Jr("uni-page",{"data-page":n.route},"custom"!==o.style?[Jr($x),Kx(t)]:[Kx(t)])}});function Kx(e){return Nr(),Fr(Jx,{key:0},{default:qn((()=>[Xo(e.slots,"page")])),_:3})}const Zx={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.getApp=yv,window.getCurrentPages=Xm,window.wx=Ox,window.uni=Ox,window.UniViewJSBridge=Ix,window.UniServiceJSBridge=Lx,window.rpx2px=Vd,window.__setupPage=e=>xv(e);const eT=Object.assign({}),tT=Object.assign;window.__uniConfig=tT({tabBar:{position:"bottom",color:"#000000",selectedColor:"#fa2209",borderStyle:"black",blurEffect:"none",fontSize:"10px",iconWidth:"24px",spacing:"3px",height:"50px",backgroundColor:"#ffffff",list:[{pagePath:"pages/index/index",iconPath:"/static/tabbar/home.png",selectedIconPath:"/static/tabbar/home-active.png",text:"首页"},{pagePath:"pages/category/index",iconPath:"/static/tabbar/cate.png",selectedIconPath:"/static/tabbar/cate-active.png",text:"分类"},{pagePath:"pages/cart/index",iconPath:"/static/tabbar/cart.png",selectedIconPath:"/static/tabbar/cart-active.png",text:"购物车"},{pagePath:"pages/user/index",iconPath:"/static/tabbar/user.png",selectedIconPath:"/static/tabbar/user-active.png",text:"我的"}],selectedIndex:0,shown:!0},globalStyle:{maxWidth:750,rpxCalcMaxDeviceWidth:750,rpxCalcBaseDeviceWidth:560,rpxCalcIncludeWidth:9999,backgroundTextStyle:"dark",navigationBar:{backgroundColor:"#ffffff",titleText:"",type:"default",titleColor:"#000000"},isNVue:!1},easycom:{autoscan:!0,custom:{}},compilerVersion:"3.99"},{appId:"__UNI__EBB6AD2",appName:"萤火商城2.0",appVersion:"2.3.3",appVersionCode:233,async:Zx,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{maps:{qqmap:{key:"ZWEBZ-R7N3U-BJSVH-4TCR3-66MDQ-S3FDJ"}}},qqMapKey:"ZWEBZ-R7N3U-BJSVH-4TCR3-66MDQ-S3FDJ",bMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"zh-Hans",fallbackLocale:"",locales:Object.keys(eT).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return tT(e[n]||(e[n]={}),eT[t].default),e}),{}),router:{mode:"hash",base:"./",assets:"assets",routerBase:"/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const nT={delay:Zx.delay,timeout:Zx.timeout,suspensible:Zx.suspensible};Zx.loading&&(nT.loadingComponent={name:"SystemAsyncLoading",render:()=>Jr(Vo(Zx.loading))}),Zx.error&&(nT.errorComponent={name:"SystemAsyncError",render:()=>Jr(Vo(Zx.error))});const oT=()=>t((()=>import("./pages-index-index.a929d494.js")),["./pages-index-index.a929d494.js","./index.dcc70600.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./uni-app.es.e82e6f02.js","./index.75d8b8f9.js","./mp-html.a2824d73.js","./mp-html-87831716.css","./myCoupon.db435fbe.js","./index.c3498ad6.js","./index-93a2b2a0.css","./index.1bbb2eaf.js","./index-d0d63531.css","./GoodsStatus.0e37afb9.js","./u-tag.84923274.js","./u-tag-9d25ed65.css","./color.813a9497.js","./ActiveStatus.5854b176.js","./index-9fa6be0d.css","./index-c18c389e.css"],import.meta.url).then((e=>xv(e.default||e))),rT=po(tT({loader:oT},nT)),iT=()=>t((()=>import("./pages-category-index.def0e645.js")),["./pages-category-index.def0e645.js","./wxofficial.0e36c140.js","./index.a4448d0b.js","./_plugin-vue_export-helper.1b428a4d.js","./index-5dfdfcf0.css","./index.e51653c2.js","./index-505570eb.css","./mescroll-mixins.c39f59bf.js","./uni-app.es.e82e6f02.js","./mescroll-mixins-70c23850.css","./color.813a9497.js","./SpecType.64510fc0.js","./cart.5d8f488a.js","./index.7178dccf.js","./index.65692508.js","./index-565734b8.css","./index-1ec49006.css"],import.meta.url).then((e=>xv(e.default||e))),aT=po(tT({loader:iT},nT)),sT=()=>t((()=>import("./pages-cart-index.9aabe9c9.js")),["./pages-cart-index.9aabe9c9.js","./u-icon.b0b5f2fb.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon-7ebe2527.css","./uni-app.es.e82e6f02.js","./index.e51653c2.js","./index-505570eb.css","./index.300a261a.js","./index.7178dccf.js","./index-0a868898.css","./cart.5d8f488a.js","./index-d885155c.css"],import.meta.url).then((e=>xv(e.default||e))),lT=po(tT({loader:sT},nT)),cT=()=>t((()=>import("./pages-user-index.a07fd0aa.js")),["./pages-user-index.a07fd0aa.js","./index.c3498ad6.js","./_plugin-vue_export-helper.1b428a4d.js","./index-93a2b2a0.css","./index.300a261a.js","./index.7178dccf.js","./index-0a868898.css","./index.75d8b8f9.js","./user.e96beade.js","./order.8a64f781.js","./index-694e5e16.css"],import.meta.url).then((e=>xv(e.default||e))),uT=po(tT({loader:cT},nT)),dT=()=>t((()=>import("./pages-custom-index.2b1b398f.js")),["./pages-custom-index.2b1b398f.js","./index.dcc70600.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./uni-app.es.e82e6f02.js","./index.75d8b8f9.js","./mp-html.a2824d73.js","./mp-html-87831716.css","./myCoupon.db435fbe.js","./index.c3498ad6.js","./index-93a2b2a0.css","./index.1bbb2eaf.js","./index-d0d63531.css","./GoodsStatus.0e37afb9.js","./u-tag.84923274.js","./u-tag-9d25ed65.css","./color.813a9497.js","./ActiveStatus.5854b176.js","./index-9fa6be0d.css","./index-698e7b18.css"],import.meta.url).then((e=>xv(e.default||e))),pT=po(tT({loader:dT},nT)),fT=()=>t((()=>import("./pages-search-index.4753e722.js")),["./pages-search-index.4753e722.js","./_plugin-vue_export-helper.1b428a4d.js","./index-2e68cdcc.css"],import.meta.url).then((e=>xv(e.default||e))),hT=po(tT({loader:fT},nT)),gT=()=>t((()=>import("./pages-login-index.c78e8dae.js")),["./pages-login-index.c78e8dae.js","./captcha.a7e5b097.js","./verify.8a332272.js","./_plugin-vue_export-helper.1b428a4d.js","./upload.2de084f1.js","./index.c3498ad6.js","./index-93a2b2a0.css","./index-e71571d2.css"],import.meta.url).then((e=>xv(e.default||e))),mT=po(tT({loader:gT},nT)),vT=()=>t((()=>import("./pages-user-bind-index.31d3357a.js")),["./pages-user-bind-index.31d3357a.js","./user.e96beade.js","./captcha.a7e5b097.js","./verify.8a332272.js","./_plugin-vue_export-helper.1b428a4d.js","./index-94cdd09e.css"],import.meta.url).then((e=>xv(e.default||e))),yT=po(tT({loader:vT},nT)),bT=()=>t((()=>import("./pages-user-personal-index.c626dfc4.js")),["./pages-user-personal-index.c626dfc4.js","./u-form.4fff34e3.js","./u-icon.b0b5f2fb.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon-7ebe2527.css","./uni-app.es.e82e6f02.js","./u-form-79329cbc.css","./index.c3498ad6.js","./index-93a2b2a0.css","./user.e96beade.js","./upload.2de084f1.js","./index-a365a307.css"],import.meta.url).then((e=>xv(e.default||e))),_T=po(tT({loader:bT},nT)),wT=()=>t((()=>import("./pages-article-index.79fa0adb.js")),["./pages-article-index.79fa0adb.js","./u-tabs.7be79441.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./u-tabs-de6e5783.css","./mescroll-mixins.c39f59bf.js","./mescroll-mixins-70c23850.css","./wxofficial.0e36c140.js","./index.c4013507.js","./index-0270c5d3.css"],import.meta.url).then((e=>xv(e.default||e))),xT=po(tT({loader:wT},nT)),TT=()=>t((()=>import("./pages-article-detail.e2e771ff.js")),["./pages-article-detail.e2e771ff.js","./mp-html.a2824d73.js","./_plugin-vue_export-helper.1b428a4d.js","./mp-html-87831716.css","./uni-app.es.e82e6f02.js","./wxofficial.0e36c140.js","./index.c4013507.js","./detail-45f6fa9d.css"],import.meta.url).then((e=>xv(e.default||e))),ST=po(tT({loader:TT},nT)),kT=()=>t((()=>import("./pages-help-index.3fc338fa.js")),["./pages-help-index.3fc338fa.js","./mescroll-mixins.c39f59bf.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./mescroll-mixins-70c23850.css","./index-327b0424.css"],import.meta.url).then((e=>xv(e.default||e))),CT=po(tT({loader:kT},nT)),ET=()=>t((()=>import("./pages-coupon-index.bbec9c73.js")),["./pages-coupon-index.bbec9c73.js","./myCoupon.db435fbe.js","./CouponType.5c03ebc1.js","./index.e51653c2.js","./_plugin-vue_export-helper.1b428a4d.js","./index-505570eb.css","./index-0d5339bd.css"],import.meta.url).then((e=>xv(e.default||e))),AT=po(tT({loader:ET},nT)),MT=()=>t((()=>import("./pages-goods-list.c4c526d3.js")),["./pages-goods-list.c4c526d3.js","./mescroll-mixins.c39f59bf.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./mescroll-mixins-70c23850.css","./wxofficial.0e36c140.js","./index.7178dccf.js","./index.a4448d0b.js","./index-5dfdfcf0.css","./list-f9dd0bb6.css"],import.meta.url).then((e=>xv(e.default||e))),PT=po(tT({loader:MT},nT)),IT=()=>t((()=>import("./pages-goods-detail.cb1094b6.js")),["./pages-goods-detail.cb1094b6.js","./mp-html.a2824d73.js","./_plugin-vue_export-helper.1b428a4d.js","./mp-html-87831716.css","./uni-app.es.e82e6f02.js","./wxofficial.0e36c140.js","./index.7178dccf.js","./cart.5d8f488a.js","./SpecType.64510fc0.js","./index.300a261a.js","./index-0a868898.css","./Comment.37d36275.js","./u-popup.3af38475.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./u-popup-d77a0d36.css","./comment.4ec7f90e.js","./comment-81119d34.css","./index.c3498ad6.js","./index-93a2b2a0.css","./Comment-0f921262.css","./index.75d8b8f9.js","./color.813a9497.js","./index.65692508.js","./index-565734b8.css","./detail-2d6bcb8c.css"],import.meta.url).then((e=>xv(e.default||e))),OT=po(tT({loader:IT},nT)),LT=()=>t((()=>import("./pages-comment-index.d7c13b74.js")),["./pages-comment-index.d7c13b74.js","./u-tabs.7be79441.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./u-tabs-de6e5783.css","./comment.4ec7f90e.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./comment-81119d34.css","./mescroll-mixins.c39f59bf.js","./mescroll-mixins-70c23850.css","./index.c3498ad6.js","./index-93a2b2a0.css","./index-f1672eba.css"],import.meta.url).then((e=>xv(e.default||e))),$T=po(tT({loader:LT},nT)),DT=()=>t((()=>import("./pages-my-coupon-index.6cd2ceb5.js")),["./pages-my-coupon-index.6cd2ceb5.js","./u-tabs.7be79441.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./u-tabs-de6e5783.css","./mescroll-mixins.c39f59bf.js","./mescroll-mixins-70c23850.css","./myCoupon.db435fbe.js","./CouponType.5c03ebc1.js","./index-162bb7fa.css"],import.meta.url).then((e=>xv(e.default||e))),RT=po(tT({loader:DT},nT)),BT=()=>t((()=>import("./pages-address-index.55ab4b0d.js")),["./pages-address-index.55ab4b0d.js","./address.3209875b.js","./index.e51653c2.js","./_plugin-vue_export-helper.1b428a4d.js","./index-505570eb.css","./index-ed2ccc98.css"],import.meta.url).then((e=>xv(e.default||e))),NT=po(tT({loader:BT},nT)),qT=()=>t((()=>import("./pages-address-create.1243d6de.js")),["./pages-address-create.1243d6de.js","./u-form.4fff34e3.js","./u-icon.b0b5f2fb.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon-7ebe2527.css","./uni-app.es.e82e6f02.js","./u-form-79329cbc.css","./select-region.e9758daf.js","./u-loading.6cecf685.js","./u-loading-1c18ab8d.css","./u-popup.3af38475.js","./u-popup-d77a0d36.css","./select-region-d143df5a.css","./verify.8a332272.js","./address.3209875b.js","./create-1e135d86.css"],import.meta.url).then((e=>xv(e.default||e))),jT=po(tT({loader:qT},nT)),zT=()=>t((()=>import("./pages-address-update.3cbe8865.js")),["./pages-address-update.3cbe8865.js","./u-form.4fff34e3.js","./u-icon.b0b5f2fb.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon-7ebe2527.css","./uni-app.es.e82e6f02.js","./u-form-79329cbc.css","./select-region.e9758daf.js","./u-loading.6cecf685.js","./u-loading-1c18ab8d.css","./u-popup.3af38475.js","./u-popup-d77a0d36.css","./select-region-d143df5a.css","./verify.8a332272.js","./address.3209875b.js","./update-12377811.css"],import.meta.url).then((e=>xv(e.default||e))),VT=po(tT({loader:zT},nT)),FT=()=>t((()=>import("./pages-points-log.936bf52f.js")),["./pages-points-log.936bf52f.js","./mescroll-mixins.c39f59bf.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./mescroll-mixins-70c23850.css","./log-ddf66f5b.css"],import.meta.url).then((e=>xv(e.default||e))),UT=po(tT({loader:FT},nT)),WT=()=>t((()=>import("./pages-wallet-index.d7a76b15.js")),["./pages-wallet-index.d7a76b15.js","./user.e96beade.js","./_plugin-vue_export-helper.1b428a4d.js","./index-35275bf9.css"],import.meta.url).then((e=>xv(e.default||e))),HT=po(tT({loader:WT},nT)),YT=()=>t((()=>import("./pages-wallet-balance-log.d330d72b.js")),["./pages-wallet-balance-log.d330d72b.js","./mescroll-mixins.c39f59bf.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./mescroll-mixins-70c23850.css","./log-25177340.css"],import.meta.url).then((e=>xv(e.default||e))),XT=po(tT({loader:YT},nT)),GT=()=>t((()=>import("./pages-wallet-recharge-index.3266ea19.js")),["./pages-wallet-recharge-index.3266ea19.js","./u-modal.1cb17301.js","./u-loading.6cecf685.js","./_plugin-vue_export-helper.1b428a4d.js","./u-loading-1c18ab8d.css","./uni-app.es.e82e6f02.js","./u-popup.3af38475.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./u-popup-d77a0d36.css","./u-modal-d51465a3.css","./wechat.dd20fa9d.js","./index-b2dc9651.css"],import.meta.url).then((e=>xv(e.default||e))),JT=po(tT({loader:GT},nT)),QT=()=>t((()=>import("./pages-wallet-recharge-order.587db9a7.js")),["./pages-wallet-recharge-order.587db9a7.js","./mescroll-mixins.c39f59bf.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./mescroll-mixins-70c23850.css","./order-e5fb6074.css"],import.meta.url).then((e=>xv(e.default||e))),KT=po(tT({loader:QT},nT)),ZT=()=>t((()=>import("./pages-checkout-index.87cdfba5.js")),["./pages-checkout-index.87cdfba5.js","./u-loading.6cecf685.js","./_plugin-vue_export-helper.1b428a4d.js","./u-loading-1c18ab8d.css","./uni-app.es.e82e6f02.js","./u-modal.1cb17301.js","./u-popup.3af38475.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./u-popup-d77a0d36.css","./u-modal-d51465a3.css","./verify.8a332272.js","./CouponType.5c03ebc1.js","./OrderType.8e5d413f.js","./index-21f34205.css"],import.meta.url).then((e=>xv(e.default||e))),eS=po(tT({loader:ZT},nT)),tS=()=>t((()=>import("./pages-checkout-cashier-index.8a28b1c4.js")),["./pages-checkout-cashier-index.8a28b1c4.js","./u-modal.1cb17301.js","./u-loading.6cecf685.js","./_plugin-vue_export-helper.1b428a4d.js","./u-loading-1c18ab8d.css","./uni-app.es.e82e6f02.js","./u-popup.3af38475.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./u-popup-d77a0d36.css","./u-modal-d51465a3.css","./wechat.dd20fa9d.js","./index.1bbb2eaf.js","./index-d0d63531.css","./OrderType.8e5d413f.js","./index-29066b80.css"],import.meta.url).then((e=>xv(e.default||e))),nS=po(tT({loader:tS},nT)),oS=()=>t((()=>import("./pages-order-center.f3ca9822.js")),["./pages-order-center.f3ca9822.js","./user.e96beade.js","./_plugin-vue_export-helper.1b428a4d.js","./center-e004384a.css"],import.meta.url).then((e=>xv(e.default||e))),rS=po(tT({loader:oS},nT)),iS=()=>t((()=>import("./pages-order-index.7ac85b72.js")),["./pages-order-index.7ac85b72.js","./u-tabs.7be79441.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./u-tabs-de6e5783.css","./mescroll-mixins.c39f59bf.js","./mescroll-mixins-70c23850.css","./u-popup.3af38475.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./u-popup-d77a0d36.css","./OrderType.8e5d413f.js","./order.8a64f781.js","./index-48032962.css"],import.meta.url).then((e=>xv(e.default||e))),aS=po(tT({loader:iS},nT)),sS=()=>t((()=>import("./pages-order-detail.cde9b575.js")),["./pages-order-detail.cde9b575.js","./u-popup.3af38475.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./u-popup-d77a0d36.css","./OrderType.8e5d413f.js","./order.8a64f781.js","./close.45fd8c32.js","./detail-707972c7.css"],import.meta.url).then((e=>xv(e.default||e))),lS=po(tT({loader:sS},nT)),cS=()=>t((()=>import("./pages-order-express-index.7fa266c6.js")),["./pages-order-express-index.7fa266c6.js","./u-tabs.7be79441.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./u-tabs-de6e5783.css","./order.8a64f781.js","./index-beec8f4a.css"],import.meta.url).then((e=>xv(e.default||e))),uS=po(tT({loader:cS},nT)),dS=()=>t((()=>import("./pages-order-extract-check.3f3dced0.js")),["./pages-order-extract-check.3f3dced0.js","./OrderType.8e5d413f.js","./close.45fd8c32.js","./_plugin-vue_export-helper.1b428a4d.js","./check-e5561e4b.css"],import.meta.url).then((e=>xv(e.default||e))),pS=po(tT({loader:dS},nT)),fS=()=>t((()=>import("./pages-order-comment-index.beb22190.js")),["./pages-order-comment-index.beb22190.js","./upload.2de084f1.js","./_plugin-vue_export-helper.1b428a4d.js","./index-b2709ea1.css"],import.meta.url).then((e=>xv(e.default||e))),hS=po(tT({loader:fS},nT)),gS=()=>t((()=>import("./pages-refund-index.8e70ddc3.js")),["./pages-refund-index.8e70ddc3.js","./u-tabs.7be79441.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./u-tabs-de6e5783.css","./mescroll-mixins.c39f59bf.js","./mescroll-mixins-70c23850.css","./refund.0fd6de35.js","./index-be769660.css"],import.meta.url).then((e=>xv(e.default||e))),mS=po(tT({loader:gS},nT)),vS=()=>t((()=>import("./pages-refund-detail.fcdec618.js")),["./pages-refund-detail.fcdec618.js","./RefundType.8e86f613.js","./refund.0fd6de35.js","./_plugin-vue_export-helper.1b428a4d.js","./detail-f0b28823.css"],import.meta.url).then((e=>xv(e.default||e))),yS=po(tT({loader:vS},nT)),bS=()=>t((()=>import("./pages-refund-apply.906a18f3.js")),["./pages-refund-apply.906a18f3.js","./RefundType.8e86f613.js","./upload.2de084f1.js","./refund.0fd6de35.js","./_plugin-vue_export-helper.1b428a4d.js","./apply-1819be6c.css"],import.meta.url).then((e=>xv(e.default||e))),_S=po(tT({loader:bS},nT)),wS=()=>t((()=>import("./pages-shop-extract.70c7a517.js")),["./pages-shop-extract.70c7a517.js","./shop.76bc7849.js","./index.e51653c2.js","./_plugin-vue_export-helper.1b428a4d.js","./index-505570eb.css","./extract-2e401507.css"],import.meta.url).then((e=>xv(e.default||e))),xS=po(tT({loader:wS},nT)),TS=()=>t((()=>import("./pages-shop-detail.9d40171c.js")),["./pages-shop-detail.9d40171c.js","./wxofficial.0e36c140.js","./shop.76bc7849.js","./_plugin-vue_export-helper.1b428a4d.js","./detail-e19c586b.css"],import.meta.url).then((e=>xv(e.default||e))),SS=po(tT({loader:TS},nT)),kS=()=>t((()=>import("./pages-dealer-index.163a9f83.js")),["./pages-dealer-index.163a9f83.js","./index.c3498ad6.js","./_plugin-vue_export-helper.1b428a4d.js","./index-93a2b2a0.css","./index.ad5534a6.js","./index-7377769d.css"],import.meta.url).then((e=>xv(e.default||e))),CS=po(tT({loader:kS},nT)),ES=()=>t((()=>import("./pages-dealer-apply.a88bafc1.js")),["./pages-dealer-apply.a88bafc1.js","./u-modal.1cb17301.js","./u-loading.6cecf685.js","./_plugin-vue_export-helper.1b428a4d.js","./u-loading-1c18ab8d.css","./uni-app.es.e82e6f02.js","./u-popup.3af38475.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./u-popup-d77a0d36.css","./u-modal-d51465a3.css","./Setting.09e3e42b.js","./apply-0747ddf8.css"],import.meta.url).then((e=>xv(e.default||e))),AS=po(tT({loader:ES},nT)),MS=()=>t((()=>import("./pages-dealer-withdraw-apply.21ac821f.js")),["./pages-dealer-withdraw-apply.21ac821f.js","./index.ad5534a6.js","./ApplyStatus.af61e480.js","./Setting.09e3e42b.js","./_plugin-vue_export-helper.1b428a4d.js","./apply-01a52c76.css"],import.meta.url).then((e=>xv(e.default||e))),PS=po(tT({loader:MS},nT)),IS=()=>t((()=>import("./pages-dealer-withdraw-list.12f90479.js")),["./pages-dealer-withdraw-list.12f90479.js","./u-tabs.7be79441.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./u-tabs-de6e5783.css","./mescroll-mixins.c39f59bf.js","./mescroll-mixins-70c23850.css","./u-modal.1cb17301.js","./u-loading.6cecf685.js","./u-loading-1c18ab8d.css","./u-popup.3af38475.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./u-popup-d77a0d36.css","./u-modal-d51465a3.css","./ApplyStatus.af61e480.js","./Setting.09e3e42b.js","./list-7e9b6121.css"],import.meta.url).then((e=>xv(e.default||e))),OS=po(tT({loader:IS},nT)),LS=()=>t((()=>import("./pages-dealer-poster.2a2e779a.js")),["./pages-dealer-poster.2a2e779a.js","./Setting.09e3e42b.js","./_plugin-vue_export-helper.1b428a4d.js","./poster-744d32bb.css"],import.meta.url).then((e=>xv(e.default||e))),$S=po(tT({loader:LS},nT)),DS=()=>t((()=>import("./pages-dealer-order.c9781614.js")),["./pages-dealer-order.c9781614.js","./u-tabs.7be79441.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./u-tabs-de6e5783.css","./mescroll-mixins.c39f59bf.js","./mescroll-mixins-70c23850.css","./index.c3498ad6.js","./index-93a2b2a0.css","./Setting.09e3e42b.js","./order-d5c47dfe.css"],import.meta.url).then((e=>xv(e.default||e))),RS=po(tT({loader:DS},nT)),BS=()=>t((()=>import("./pages-dealer-team.6a2f0f43.js")),["./pages-dealer-team.6a2f0f43.js","./u-tabs.7be79441.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./u-tabs-de6e5783.css","./mescroll-mixins.c39f59bf.js","./mescroll-mixins-70c23850.css","./index.c3498ad6.js","./index-93a2b2a0.css","./index.ad5534a6.js","./Setting.09e3e42b.js","./team-3f89fefd.css"],import.meta.url).then((e=>xv(e.default||e))),NS=po(tT({loader:BS},nT)),qS=()=>t((()=>import("./pages-bargain-index.e313b6b5.js")),["./pages-bargain-index.e313b6b5.js","./mescroll-mixins.c39f59bf.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./mescroll-mixins-70c23850.css","./wxofficial.0e36c140.js","./index.c3498ad6.js","./index-93a2b2a0.css","./index.1bbb2eaf.js","./index-d0d63531.css","./task.3bb3a8ea.js","./index-c35b1129.css"],import.meta.url).then((e=>xv(e.default||e))),jS=po(tT({loader:qS},nT)),zS=()=>t((()=>import("./pages-bargain-goods-index.34da7cd2.js")),["./pages-bargain-goods-index.34da7cd2.js","./mp-html.a2824d73.js","./_plugin-vue_export-helper.1b428a4d.js","./mp-html-87831716.css","./uni-app.es.e82e6f02.js","./u-modal.1cb17301.js","./u-loading.6cecf685.js","./u-loading-1c18ab8d.css","./u-popup.3af38475.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./u-popup-d77a0d36.css","./u-modal-d51465a3.css","./wxofficial.0e36c140.js","./Comment.37d36275.js","./comment.4ec7f90e.js","./comment-81119d34.css","./index.c3498ad6.js","./index-93a2b2a0.css","./Comment-0f921262.css","./index.75d8b8f9.js","./color.813a9497.js","./task.3bb3a8ea.js","./index.65692508.js","./index-565734b8.css","./index.1bbb2eaf.js","./index-d0d63531.css","./index.7178dccf.js","./cart.5d8f488a.js","./index-c87a4400.css"],import.meta.url).then((e=>xv(e.default||e))),VS=po(tT({loader:zS},nT)),FS=()=>t((()=>import("./pages-bargain-task.a7ee9a4a.js")),["./pages-bargain-task.a7ee9a4a.js","./u-modal.1cb17301.js","./u-loading.6cecf685.js","./_plugin-vue_export-helper.1b428a4d.js","./u-loading-1c18ab8d.css","./uni-app.es.e82e6f02.js","./u-popup.3af38475.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./u-popup-d77a0d36.css","./u-modal-d51465a3.css","./wxofficial.0e36c140.js","./index.c3498ad6.js","./index-93a2b2a0.css","./index.1bbb2eaf.js","./index-d0d63531.css","./index.7178dccf.js","./task.3bb3a8ea.js","./task-21526cf9.css"],import.meta.url).then((e=>xv(e.default||e))),US=po(tT({loader:FS},nT)),WS=()=>t((()=>import("./pages-sharp-index.9e632a6b.js")),["./pages-sharp-index.9e632a6b.js","./mescroll-mixins.c39f59bf.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./mescroll-mixins-70c23850.css","./wxofficial.0e36c140.js","./index.1bbb2eaf.js","./index-d0d63531.css","./color.813a9497.js","./GoodsStatus.0e37afb9.js","./goods.67c4c971.js","./index-ee8c66cb.css"],import.meta.url).then((e=>xv(e.default||e))),HS=po(tT({loader:WS},nT)),YS=()=>t((()=>import("./pages-sharp-goods-index.65950a1e.js")),["./pages-sharp-goods-index.65950a1e.js","./mp-html.a2824d73.js","./_plugin-vue_export-helper.1b428a4d.js","./mp-html-87831716.css","./uni-app.es.e82e6f02.js","./wxofficial.0e36c140.js","./Comment.37d36275.js","./u-popup.3af38475.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./u-popup-d77a0d36.css","./comment.4ec7f90e.js","./comment-81119d34.css","./index.c3498ad6.js","./index-93a2b2a0.css","./Comment-0f921262.css","./index.75d8b8f9.js","./color.813a9497.js","./index.65692508.js","./index-565734b8.css","./index.1bbb2eaf.js","./index-d0d63531.css","./goods.67c4c971.js","./cart.5d8f488a.js","./GoodsStatus.0e37afb9.js","./index-3fd0cdb3.css"],import.meta.url).then((e=>xv(e.default||e))),XS=po(tT({loader:YS},nT)),GS=()=>t((()=>import("./pages-groupon-index.17de1849.js")),["./pages-groupon-index.17de1849.js","./u-tag.84923274.js","./u-icon.b0b5f2fb.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon-7ebe2527.css","./uni-app.es.e82e6f02.js","./u-tag-9d25ed65.css","./mescroll-mixins.c39f59bf.js","./mescroll-mixins-70c23850.css","./wxofficial.0e36c140.js","./color.813a9497.js","./ActiveStatus.5854b176.js","./task.d6759a83.js","./goods.1e7e06db.js","./Setting.0059d4d7.js","./index-4599e764.css"],import.meta.url).then((e=>xv(e.default||e))),JS=po(tT({loader:GS},nT)),QS=()=>t((()=>import("./pages-groupon-goods-index.584ab911.js")),["./pages-groupon-goods-index.584ab911.js","./mp-html.a2824d73.js","./_plugin-vue_export-helper.1b428a4d.js","./mp-html-87831716.css","./uni-app.es.e82e6f02.js","./u-modal.1cb17301.js","./u-loading.6cecf685.js","./u-loading-1c18ab8d.css","./u-popup.3af38475.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./u-popup-d77a0d36.css","./u-modal-d51465a3.css","./wxofficial.0e36c140.js","./Comment.37d36275.js","./comment.4ec7f90e.js","./comment-81119d34.css","./index.c3498ad6.js","./index-93a2b2a0.css","./Comment-0f921262.css","./index.75d8b8f9.js","./SkuPopup.51844580.js","./color.813a9497.js","./ActiveStatus.5854b176.js","./SkuPopup-c2d43cb5.css","./index.1bbb2eaf.js","./index-d0d63531.css","./task.d6759a83.js","./goods.1e7e06db.js","./cart.5d8f488a.js","./index-cb446797.css"],import.meta.url).then((e=>xv(e.default||e))),KS=po(tT({loader:QS},nT)),ZS=()=>t((()=>import("./pages-groupon-task-index.1d730dda.js")),["./pages-groupon-task-index.1d730dda.js","./u-modal.1cb17301.js","./u-loading.6cecf685.js","./_plugin-vue_export-helper.1b428a4d.js","./u-loading-1c18ab8d.css","./uni-app.es.e82e6f02.js","./u-popup.3af38475.js","./u-icon.b0b5f2fb.js","./u-icon-7ebe2527.css","./u-popup-d77a0d36.css","./u-modal-d51465a3.css","./wxofficial.0e36c140.js","./index.c3498ad6.js","./index-93a2b2a0.css","./index.1bbb2eaf.js","./index-d0d63531.css","./index.300a261a.js","./index.7178dccf.js","./index-0a868898.css","./SkuPopup.51844580.js","./color.813a9497.js","./ActiveStatus.5854b176.js","./SkuPopup-c2d43cb5.css","./task.d6759a83.js","./Setting.0059d4d7.js","./index-50409dce.css"],import.meta.url).then((e=>xv(e.default||e))),ek=po(tT({loader:ZS},nT)),tk=()=>t((()=>import("./pages-live-index.133f0d73.js")),["./pages-live-index.133f0d73.js","./mescroll-mixins.c39f59bf.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.e82e6f02.js","./mescroll-mixins-70c23850.css","./wxofficial.0e36c140.js","./index-0b0af47f.css"],import.meta.url).then((e=>xv(e.default||e))),nk=po(tT({loader:tk},nT));function ok(e,t){return Nr(),Fr(Qx,null,{page:qn((()=>[Jr(e,tT({},t,{ref:"page"}),null,512)])),_:1})}function rk(){return"undefined"!=typeof navigator&&"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}}window.__uniRoutes=[{path:"/",alias:"/pages/index/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(rT,t)}},loader:oT,meta:{isQuit:!0,isEntry:!0,isTabBar:!0,tabBarIndex:0,enablePullDownRefresh:!0,navigationBar:{type:"default"},isNVue:!1}},{path:"/pages/category/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(aT,t)}},loader:iT,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:1,navigationBar:{titleText:"全部分类",type:"default"},isNVue:!1}},{path:"/pages/cart/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(lT,t)}},loader:sT,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:2,navigationBar:{titleText:"购物车",type:"default"},isNVue:!1}},{path:"/pages/user/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(uT,t)}},loader:cT,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:3,enablePullDownRefresh:!0,navigationBar:{titleText:"个人中心",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/custom/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(pT,t)}},loader:dT,meta:{enablePullDownRefresh:!0,navigationBar:{type:"default"},isNVue:!1}},{path:"/pages/search/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(hT,t)}},loader:fT,meta:{navigationBar:{titleText:"商品搜索",type:"default"},isNVue:!1}},{path:"/pages/login/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(mT,t)}},loader:gT,meta:{navigationBar:{titleText:"会员登录",type:"default"},isNVue:!1}},{path:"/pages/user/bind/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(yT,t)}},loader:vT,meta:{navigationBar:{titleText:"绑定手机",type:"default"},isNVue:!1}},{path:"/pages/user/personal/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(_T,t)}},loader:bT,meta:{navigationBar:{titleText:"个人信息",type:"default"},isNVue:!1}},{path:"/pages/article/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(xT,t)}},loader:wT,meta:{navigationBar:{titleText:"资讯列表",type:"default"},isNVue:!1}},{path:"/pages/article/detail",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(ST,t)}},loader:TT,meta:{navigationBar:{titleText:"资讯详情",type:"default"},isNVue:!1}},{path:"/pages/help/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(CT,t)}},loader:kT,meta:{navigationBar:{titleText:"帮助中心",type:"default"},isNVue:!1}},{path:"/pages/coupon/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(AT,t)}},loader:ET,meta:{navigationBar:{titleText:"领券中心",type:"default"},isNVue:!1}},{path:"/pages/goods/list",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(PT,t)}},loader:MT,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"商品列表",type:"default"},isNVue:!1}},{path:"/pages/goods/detail",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(OT,t)}},loader:IT,meta:{navigationBar:{titleText:"商品详情页",type:"default"},isNVue:!1}},{path:"/pages/comment/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok($T,t)}},loader:LT,meta:{navigationBar:{titleText:"商品评价页",type:"default"},isNVue:!1}},{path:"/pages/my-coupon/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(RT,t)}},loader:DT,meta:{navigationBar:{titleText:"我的优惠券",type:"default"},isNVue:!1}},{path:"/pages/address/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(NT,t)}},loader:BT,meta:{navigationBar:{titleText:"收货地址",type:"default"},isNVue:!1}},{path:"/pages/address/create",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(jT,t)}},loader:qT,meta:{navigationBar:{titleText:"新增收货地址",type:"default"},isNVue:!1}},{path:"/pages/address/update",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(VT,t)}},loader:zT,meta:{navigationBar:{titleText:"编辑收货地址",type:"default"},isNVue:!1}},{path:"/pages/points/log",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(UT,t)}},loader:FT,meta:{navigationBar:{titleText:"账单明细",type:"default"},isNVue:!1}},{path:"/pages/wallet/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(HT,t)}},loader:WT,meta:{navigationBar:{titleText:"我的钱包",type:"default"},isNVue:!1}},{path:"/pages/wallet/balance/log",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(XT,t)}},loader:YT,meta:{navigationBar:{titleText:"账单详情",type:"default"},isNVue:!1}},{path:"/pages/wallet/recharge/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(JT,t)}},loader:GT,meta:{navigationBar:{titleText:"充值中心",type:"default"},isNVue:!1}},{path:"/pages/wallet/recharge/order",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(KT,t)}},loader:QT,meta:{navigationBar:{titleText:"充值记录",type:"default"},isNVue:!1}},{path:"/pages/checkout/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(eS,t)}},loader:ZT,meta:{navigationBar:{titleText:"订单结算台",type:"default"},isNVue:!1}},{path:"/pages/checkout/cashier/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(nS,t)}},loader:tS,meta:{navigationBar:{titleText:"支付订单",type:"default"},isNVue:!1}},{path:"/pages/order/center",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(rS,t)}},loader:oS,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"订单中心",type:"default"},isNVue:!1}},{path:"/pages/order/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(aS,t)}},loader:iS,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"我的订单",type:"default"},isNVue:!1}},{path:"/pages/order/detail",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(lS,t)}},loader:sS,meta:{navigationBar:{backgroundColor:"#e8c269",titleText:"订单详情",type:"default",titleColor:"#ffffff"},isNVue:!1}},{path:"/pages/order/express/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(uS,t)}},loader:cS,meta:{navigationBar:{titleText:"物流跟踪",type:"default"},isNVue:!1}},{path:"/pages/order/extract/check",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(pS,t)}},loader:dS,meta:{navigationBar:{titleText:"订单自提核销",type:"default"},isNVue:!1}},{path:"/pages/order/comment/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(hS,t)}},loader:fS,meta:{navigationBar:{titleText:"订单评价",type:"default"},isNVue:!1}},{path:"/pages/refund/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(mS,t)}},loader:gS,meta:{navigationBar:{titleText:"退换/售后",type:"default"},isNVue:!1}},{path:"/pages/refund/detail",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(yS,t)}},loader:vS,meta:{navigationBar:{titleText:"售后详情",type:"default"},isNVue:!1}},{path:"/pages/refund/apply",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(_S,t)}},loader:bS,meta:{navigationBar:{titleText:"申请售后",type:"default"},isNVue:!1}},{path:"/pages/shop/extract",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(xS,t)}},loader:wS,meta:{navigationBar:{titleText:"选择自提门店",type:"default"},isNVue:!1}},{path:"/pages/shop/detail",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(SS,t)}},loader:TS,meta:{navigationBar:{titleText:"门店详情",type:"default"},isNVue:!1}},{path:"/pages/dealer/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(CS,t)}},loader:kS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/apply",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(AS,t)}},loader:ES,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/withdraw/apply",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(PS,t)}},loader:MS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/withdraw/list",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(OS,t)}},loader:IS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/poster",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok($S,t)}},loader:LS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/order",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(RS,t)}},loader:DS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/team",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(NS,t)}},loader:BS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/bargain/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(jS,t)}},loader:qS,meta:{navigationBar:{titleText:"砍价会场",type:"default"},isNVue:!1}},{path:"/pages/bargain/goods/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(VS,t)}},loader:zS,meta:{navigationBar:{titleText:"砍价商品",type:"default"},isNVue:!1}},{path:"/pages/bargain/task",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(US,t)}},loader:FS,meta:{navigationBar:{titleText:"砍价任务",type:"default"},isNVue:!1}},{path:"/pages/sharp/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(HS,t)}},loader:WS,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"整点秒杀会场",type:"default"},isNVue:!1}},{path:"/pages/sharp/goods/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(XS,t)}},loader:YS,meta:{navigationBar:{titleText:"秒杀商品详情",type:"default"},isNVue:!1}},{path:"/pages/groupon/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(JS,t)}},loader:GS,meta:{navigationBar:{titleText:"拼团活动",type:"default"},isNVue:!1}},{path:"/pages/groupon/goods/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(KS,t)}},loader:QS,meta:{navigationBar:{titleText:"拼团商品",type:"default"},isNVue:!1}},{path:"/pages/groupon/task/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(ek,t)}},loader:ZS,meta:{navigationBar:{backgroundColor:"#FF5644",titleText:"拼团详情",type:"default",titleColor:"#ffffff"},isNVue:!1}},{path:"/pages/live/index",component:{setup(){const e=yv(),t=e&&e.$route&&e.$route.query||{};return()=>ok(nk,t)}},loader:tk,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"直播列表",type:"default"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const ik="function"==typeof Proxy;class ak{constructor(e,t){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=e,this.hook=t;const n={};if(e.settings)for(const i in e.settings){const t=e.settings[i];n[i]=t.defaultValue}const o=`__vue-devtools-plugin-settings__${e.id}`;let r={...n};try{const e=localStorage.getItem(o),t=JSON.parse(e);Object.assign(r,t)}catch(kC){}this.fallbacks={getSettings:()=>r,setSettings(e){try{localStorage.setItem(o,JSON.stringify(e))}catch(kC){}r=e}},t.on("plugin:settings:set",((e,t)=>{e===this.plugin.id&&this.fallbacks.setSettings(t)})),this.proxiedOn=new Proxy({},{get:(e,t)=>this.target?this.target.on[t]:(...e)=>{this.onQueue.push({method:t,args:e})}}),this.proxiedTarget=new Proxy({},{get:(e,t)=>this.target?this.target[t]:"on"===t?this.proxiedOn:Object.keys(this.fallbacks).includes(t)?(...e)=>(this.targetQueue.push({method:t,args:e,resolve:()=>{}}),this.fallbacks[t](...e)):(...e)=>new Promise((n=>{this.targetQueue.push({method:t,args:e,resolve:n})}))})}async setRealTarget(e){this.target=e;for(const t of this.onQueue)this.target.on[t.method](...t.args);for(const t of this.targetQueue)t.resolve(await this.target[t.method](...t.args))}}function sk(e,t){const n=rk(),o=rk().__VUE_DEVTOOLS_GLOBAL_HOOK__,r=ik&&e.enableEarlyProxy;if(!o||!n.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__&&r){const i=r?new ak(e,o):null;(n.__VUE_DEVTOOLS_PLUGINS__=n.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:e,setupFn:t,proxy:i}),i&&t(i.proxiedTarget)}else o.emit("devtools-plugin:setup",e,t)}
/*!
 * vuex v4.1.0
 * (c) 2022 Evan You
 * @license MIT
 */function lk(e,t){Object.keys(e).forEach((function(n){return t(e[n],n)}))}function ck(e,t,n){return t.indexOf(e)<0&&(n&&n.prepend?t.unshift(e):t.push(e)),function(){var n=t.indexOf(e);n>-1&&t.splice(n,1)}}function uk(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var n=e.state;pk(e,n,[],e._modules.root,!0),dk(e,n,t)}function dk(e,t,n){var o=e._state,r=e._scope;e.getters={},e._makeLocalGettersCache=Object.create(null);var i=e._wrappedGetters,a={},s={},l=Re(!0);l.run((function(){lk(i,(function(t,n){a[n]=function(e,t){return function(){return e(t)}}(t,e),s[n]=vi((function(){return a[n]()})),Object.defineProperty(e.getters,n,{get:function(){return s[n].value},enumerable:!0})}))})),e._state=Vt({data:t}),e._scope=l,e.strict&&function(e){Gn((function(){return e._state.data}),(function(){}),{deep:!0,flush:"sync"})}(e),o&&n&&e._withCommit((function(){o.data=null})),r&&r.stop()}function pk(e,t,n,o,r){var i=!n.length,a=e._modules.getNamespace(n);if(o.namespaced&&(e._modulesNamespaceMap[a],e._modulesNamespaceMap[a]=o),!i&&!r){var s=hk(t,n.slice(0,-1)),l=n[n.length-1];e._withCommit((function(){s[l]=o.state}))}var c=o.context=function(e,t,n){var o=""===t,r={dispatch:o?e.dispatch:function(n,o,r){var i=gk(n,o,r),a=i.payload,s=i.options,l=i.type;return s&&s.root||(l=t+l),e.dispatch(l,a)},commit:o?e.commit:function(n,o,r){var i=gk(n,o,r),a=i.payload,s=i.options,l=i.type;s&&s.root||(l=t+l),e.commit(l,a,s)}};return Object.defineProperties(r,{getters:{get:o?function(){return e.getters}:function(){return fk(e,t)}},state:{get:function(){return hk(e.state,n)}}}),r}(e,a,n);o.forEachMutation((function(t,n){!function(e,t,n,o){(e._mutations[t]||(e._mutations[t]=[])).push((function(t){n.call(e,o.state,t)}))}(e,a+n,t,c)})),o.forEachAction((function(t,n){var o=t.root?n:a+n,r=t.handler||t;!function(e,t,n,o){(e._actions[t]||(e._actions[t]=[])).push((function(t){var r,i=n.call(e,{dispatch:o.dispatch,commit:o.commit,getters:o.getters,state:o.state,rootGetters:e.getters,rootState:e.state},t);return(r=i)&&"function"==typeof r.then||(i=Promise.resolve(i)),e._devtoolHook?i.catch((function(t){throw e._devtoolHook.emit("vuex:error",t),t})):i}))}(e,o,r,c)})),o.forEachGetter((function(t,n){!function(e,t,n,o){if(e._wrappedGetters[t])return;e._wrappedGetters[t]=function(e){return n(o.state,o.getters,e.state,e.getters)}}(e,a+n,t,c)})),o.forEachChild((function(o,i){pk(e,t,n.concat(i),o,r)}))}function fk(e,t){if(!e._makeLocalGettersCache[t]){var n={},o=t.length;Object.keys(e.getters).forEach((function(r){if(r.slice(0,o)===t){var i=r.slice(o);Object.defineProperty(n,i,{get:function(){return e.getters[r]},enumerable:!0})}})),e._makeLocalGettersCache[t]=n}return e._makeLocalGettersCache[t]}function hk(e,t){return t.reduce((function(e,t){return e[t]}),e)}function gk(e,t,n){var o;return null!==(o=e)&&"object"==typeof o&&e.type&&(n=t,t=e,e=e.type),{type:e,payload:t,options:n}}var mk=0;function vk(e,t){sk({id:"org.vuejs.vuex",app:e,label:"Vuex",homepage:"https://next.vuex.vuejs.org/",logo:"https://vuejs.org/images/icons/favicon-96x96.png",packageName:"vuex",componentStateTypes:["vuex bindings"]},(function(n){n.addTimelineLayer({id:"vuex:mutations",label:"Vuex Mutations",color:yk}),n.addTimelineLayer({id:"vuex:actions",label:"Vuex Actions",color:yk}),n.addInspector({id:"vuex",label:"Vuex",icon:"storage",treeFilterPlaceholder:"Filter stores..."}),n.on.getInspectorTree((function(n){if(n.app===e&&"vuex"===n.inspectorId)if(n.filter){var o=[];xk(o,t._modules.root,n.filter,""),n.rootNodes=o}else n.rootNodes=[wk(t._modules.root,"")]})),n.on.getInspectorState((function(n){if(n.app===e&&"vuex"===n.inspectorId){var o=n.nodeId;fk(t,o),n.state=function(e,t,n){t="root"===n?t:t[n];var o=Object.keys(t),r={state:Object.keys(e.state).map((function(t){return{key:t,editable:!0,value:e.state[t]}}))};if(o.length){var i=function(e){var t={};return Object.keys(e).forEach((function(n){var o=n.split("/");if(o.length>1){var r=t,i=o.pop();o.forEach((function(e){r[e]||(r[e]={_custom:{value:{},display:e,tooltip:"Module",abstract:!0}}),r=r[e]._custom.value})),r[i]=Tk((function(){return e[n]}))}else t[n]=Tk((function(){return e[n]}))})),t}(t);r.getters=Object.keys(i).map((function(e){return{key:e.endsWith("/")?_k(e):e,editable:!1,value:Tk((function(){return i[e]}))}}))}return r}((r=t._modules,(a=(i=o).split("/").filter((function(e){return e}))).reduce((function(e,t,n){var o=e[t];if(!o)throw new Error('Missing module "'+t+'" for path "'+i+'".');return n===a.length-1?o:o._children}),"root"===i?r:r.root._children)),"root"===o?t.getters:t._makeLocalGettersCache,o)}var r,i,a})),n.on.editInspectorState((function(n){if(n.app===e&&"vuex"===n.inspectorId){var o=n.nodeId,r=n.path;"root"!==o&&(r=o.split("/").filter(Boolean).concat(r)),t._withCommit((function(){n.set(t._state.data,r,n.state.value)}))}})),t.subscribe((function(e,t){var o={};e.payload&&(o.payload=e.payload),o.state=t,n.notifyComponentUpdate(),n.sendInspectorTree("vuex"),n.sendInspectorState("vuex"),n.addTimelineEvent({layerId:"vuex:mutations",event:{time:Date.now(),title:e.type,data:o}})})),t.subscribeAction({before:function(e,t){var o={};e.payload&&(o.payload=e.payload),e._id=mk++,e._time=Date.now(),o.state=t,n.addTimelineEvent({layerId:"vuex:actions",event:{time:e._time,title:e.type,groupId:e._id,subtitle:"start",data:o}})},after:function(e,t){var o={},r=Date.now()-e._time;o.duration={_custom:{type:"duration",display:r+"ms",tooltip:"Action duration",value:r}},e.payload&&(o.payload=e.payload),o.state=t,n.addTimelineEvent({layerId:"vuex:actions",event:{time:Date.now(),title:e.type,groupId:e._id,subtitle:"end",data:o}})}})}))}var yk=8702998,bk={label:"namespaced",textColor:16777215,backgroundColor:6710886};function _k(e){return e&&"root"!==e?e.split("/").slice(-2,-1)[0]:"Root"}function wk(e,t){return{id:t||"root",label:_k(t),tags:e.namespaced?[bk]:[],children:Object.keys(e._children).map((function(n){return wk(e._children[n],t+n+"/")}))}}function xk(e,t,n,o){o.includes(n)&&e.push({id:o||"root",label:o.endsWith("/")?o.slice(0,o.length-1):o||"Root",tags:t.namespaced?[bk]:[]}),Object.keys(t._children).forEach((function(r){xk(e,t._children[r],n,o+r+"/")}))}function Tk(e){try{return e()}catch(kC){return kC}}var Sk=function(e,t){this.runtime=t,this._children=Object.create(null),this._rawModule=e;var n=e.state;this.state=("function"==typeof n?n():n)||{}},kk={namespaced:{configurable:!0}};kk.namespaced.get=function(){return!!this._rawModule.namespaced},Sk.prototype.addChild=function(e,t){this._children[e]=t},Sk.prototype.removeChild=function(e){delete this._children[e]},Sk.prototype.getChild=function(e){return this._children[e]},Sk.prototype.hasChild=function(e){return e in this._children},Sk.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},Sk.prototype.forEachChild=function(e){lk(this._children,e)},Sk.prototype.forEachGetter=function(e){this._rawModule.getters&&lk(this._rawModule.getters,e)},Sk.prototype.forEachAction=function(e){this._rawModule.actions&&lk(this._rawModule.actions,e)},Sk.prototype.forEachMutation=function(e){this._rawModule.mutations&&lk(this._rawModule.mutations,e)},Object.defineProperties(Sk.prototype,kk);var Ck=function(e){this.register([],e,!1)};function Ek(e,t,n){if(t.update(n),n.modules)for(var o in n.modules){if(!t.getChild(o))return;Ek(e.concat(o),t.getChild(o),n.modules[o])}}Ck.prototype.get=function(e){return e.reduce((function(e,t){return e.getChild(t)}),this.root)},Ck.prototype.getNamespace=function(e){var t=this.root;return e.reduce((function(e,n){return e+((t=t.getChild(n)).namespaced?n+"/":"")}),"")},Ck.prototype.update=function(e){Ek([],this.root,e)},Ck.prototype.register=function(e,t,n){var o=this;void 0===n&&(n=!0);var r=new Sk(t,n);0===e.length?this.root=r:this.get(e.slice(0,-1)).addChild(e[e.length-1],r);t.modules&&lk(t.modules,(function(t,r){o.register(e.concat(r),t,n)}))},Ck.prototype.unregister=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1],o=t.getChild(n);o&&o.runtime&&t.removeChild(n)},Ck.prototype.isRegistered=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1];return!!t&&t.hasChild(n)};var Ak=function(e){var t=this;void 0===e&&(e={});var n=e.plugins;void 0===n&&(n=[]);var o=e.strict;void 0===o&&(o=!1);var r=e.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new Ck(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._scope=null,this._devtools=r;var i=this,a=this.dispatch,s=this.commit;this.dispatch=function(e,t){return a.call(i,e,t)},this.commit=function(e,t,n){return s.call(i,e,t,n)},this.strict=o;var l=this._modules.root.state;pk(this,l,[],this._modules.root),dk(this,l),n.forEach((function(e){return e(t)}))},Mk={state:{configurable:!0}};Ak.prototype.install=function(e,t){e.provide(t||"store",this),e.config.globalProperties.$store=this,void 0!==this._devtools&&this._devtools&&vk(e,this)},Mk.state.get=function(){return this._state.data},Mk.state.set=function(e){},Ak.prototype.commit=function(e,t,n){var o=this,r=gk(e,t,n),i=r.type,a=r.payload,s={type:i,payload:a},l=this._mutations[i];l&&(this._withCommit((function(){l.forEach((function(e){e(a)}))})),this._subscribers.slice().forEach((function(e){return e(s,o.state)})))},Ak.prototype.dispatch=function(e,t){var n=this,o=gk(e,t),r=o.type,i=o.payload,a={type:r,payload:i},s=this._actions[r];if(s){try{this._actionSubscribers.slice().filter((function(e){return e.before})).forEach((function(e){return e.before(a,n.state)}))}catch(kC){}var l=s.length>1?Promise.all(s.map((function(e){return e(i)}))):s[0](i);return new Promise((function(e,t){l.then((function(t){try{n._actionSubscribers.filter((function(e){return e.after})).forEach((function(e){return e.after(a,n.state)}))}catch(kC){}e(t)}),(function(e){try{n._actionSubscribers.filter((function(e){return e.error})).forEach((function(t){return t.error(a,n.state,e)}))}catch(kC){}t(e)}))}))}},Ak.prototype.subscribe=function(e,t){return ck(e,this._subscribers,t)},Ak.prototype.subscribeAction=function(e,t){return ck("function"==typeof e?{before:e}:e,this._actionSubscribers,t)},Ak.prototype.watch=function(e,t,n){var o=this;return Gn((function(){return e(o.state,o.getters)}),t,Object.assign({},n))},Ak.prototype.replaceState=function(e){var t=this;this._withCommit((function(){t._state.data=e}))},Ak.prototype.registerModule=function(e,t,n){void 0===n&&(n={}),"string"==typeof e&&(e=[e]),this._modules.register(e,t),pk(this,this.state,e,this._modules.get(e),n.preserveState),dk(this,this.state)},Ak.prototype.unregisterModule=function(e){var t=this;"string"==typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit((function(){delete hk(t.state,e.slice(0,-1))[e[e.length-1]]})),uk(this)},Ak.prototype.hasModule=function(e){return"string"==typeof e&&(e=[e]),this._modules.isRegistered(e)},Ak.prototype.hotUpdate=function(e){this._modules.update(e),uk(this,!0)},Ak.prototype._withCommit=function(e){var t=this._committing;this._committing=!0,e(),this._committing=t},Object.defineProperties(Ak.prototype,Mk);const Pk={set(e,t,n){uni.setStorageSync(e,t);const o=parseInt(n);if(o>0){let t=Date.parse(new Date);t=t/1e3+o,uni.setStorageSync(e+"_expiry",t+"")}else uni.removeStorageSync(e+"_expiry")},get(e,t){const n=parseInt(uni.getStorageSync(e+"_expiry"));if(n&&parseInt(n)<Date.parse(new Date)/1e3)return t||!1;const o=uni.getStorageSync(e);return o||(null!=t&&""!=t||(t=!1),t)},remove(e){uni.removeStorageSync(e),uni.removeStorageSync(e+"_expiry")},clear(){uni.clearStorageSync()}},Ik={state:{storeId:null,platform:"",refereeId:null,modules:[]},mutations:{SET_STORE_ID:(e,t)=>{e.storeId=t},SET_PLATFORM:(e,t)=>{e.platform=t},SET_REFEREE_ID:(e,t)=>{e.refereeId=t},SET_MODULES:(e,t)=>{e.modules=t}},actions:{setRefereeId({commit:e},t){const n=this,o=parseInt(t);return new Promise(((t,r)=>{o>0&&n.getters.userId!=o&&(Pk.set("refereeId",o),e("SET_REFEREE_ID",o),t())}))},SetModules:({commit:e},t)=>new Promise(((n,o)=>{Pk.set("modules",t),e("SET_MODULES",t),n()}))}},Ok=(e,t)=>{let n=/^(http|https):\/\//.test(t.url),o=Object.assign({timeout:e.timeout},e.config,t);return"FILE"==t.method?o.url=n?t.url:e.fileUrl+t.url:o.url=n?t.url:e.baseUrl+t.url,t.header?o.header=Object.assign({},e.header,t.header):o.header=Object.assign({},e.header),o};var Lk={qiniuRegion:"",qiniuImageURLPrefix:"",qiniuUploadToken:"",qiniuUploadTokenURL:"",qiniuUploadTokenFunction:null,qiniuShouldUseQiniuFileName:!1};function $k(e,t,n,o,r,i){var a;if(null!=e)if(o&&function(e){e.region?Lk.qiniuRegion=e.region:console.error("qiniu uploader need your bucket region"),e.uptoken?Lk.qiniuUploadToken=e.uptoken:e.uptokenURL?Lk.qiniuUploadTokenURL=e.uptokenURL:e.uptokenFunc&&(Lk.qiniuUploadTokenFunction=e.uptokenFunc),e.domain&&(Lk.qiniuImageURLPrefix=e.domain),Lk.qiniuShouldUseQiniuFileName=e.shouldUseQiniuFileName}(o),Lk.qiniuUploadToken)Dk(e,t,n,o,r,i);else if(Lk.qiniuUploadTokenURL)a=function(){Dk(e,t,n,o,r,i)},wx.request({url:Lk.qiniuUploadTokenURL,success:function(e){var t=e.data.uptoken;t&&t.length>0?(Lk.qiniuUploadToken=t,a&&a()):console.error("qiniuUploader cannot get your token, please check the uptokenURL or server")},fail:function(e){console.error("qiniu UploadToken is null, please check the init config or networking: "+e)}});else{if(!Lk.qiniuUploadTokenFunction)return void console.error("qiniu uploader need one of [uptoken, uptokenURL, uptokenFunc]");if(Lk.qiniuUploadToken=Lk.qiniuUploadTokenFunction(),null==Lk.qiniuUploadToken&&Lk.qiniuUploadToken.length>0)return void console.error("qiniu UploadTokenFunction result is null, please check the return value");Dk(e,t,n,o,r,i)}else console.error("qiniu uploader need filePath to upload")}function Dk(e,t,n,o,r,i){if(null==Lk.qiniuUploadToken&&Lk.qiniuUploadToken.length>0)console.error("qiniu UploadToken is null, please check the init config or networking");else{var a=function(e){var t=null;switch(e){case"ECN":t="https://up.qbox.me";break;case"NCN":t="https://up-z1.qbox.me";break;case"SCN":t="https://up-z2.qbox.me";break;case"NA":t="https://up-na0.qbox.me";break;case"ASG":t="https://up-as0.qbox.me";break;default:console.error("please make the region is with one of [ECN, SCN, NCN, NA, ASG]")}return t}(Lk.qiniuRegion),s=e.split("//")[1];o&&o.key&&(s=o.key);var l={token:Lk.qiniuUploadToken};Lk.qiniuShouldUseQiniuFileName||(l.key=s);var c=wx.uploadFile({url:a,filePath:e,name:"file",formData:l,success:function(e){var o=e.data;e.data.hasOwnProperty("type")&&"Buffer"===e.data.type&&(o=String.fromCharCode.apply(null,e.data.data));try{var r=JSON.parse(o),i=Lk.qiniuImageURLPrefix+"/"+r.key;r.imageURL=i,t&&t(r)}catch(kC){console.log("parse JSON failed, origin String is: "+o),n&&n(kC)}},fail:function(e){console.error(e),n&&n(e)}});c.onProgressUpdate((e=>{r&&r(e)})),i&&i((()=>{c.abort()}))}}const Rk=function(e){return new Promise(((t,n)=>{uni.chooseImage({count:e.count||9,sizeType:e.sizeType||["original","compressed"],sourceType:e.sourceType||["album","camera"],success:function(e){t(e.tempFiles)},fail:e=>{n({errMsg:e.errMsg,errCode:e.errCode,statusCode:0})}})}))},Bk=function(e){return new Promise(((t,n)=>{uni.chooseVideo({sourceType:e.sourceType||["album","camera"],compressed:e.compressed||!1,maxDuration:e.maxDuration||60,camera:e.camera||"back",success:function(e){let n=[{path:e.tempFilePath}];n[0].duration=e.duration,n[0].size=e.size,n[0].height=e.height,n[0].width=e.width,n[0].name=e.name,t(n)},fail:e=>{n({errMsg:e.errMsg,errCode:e.errCode,statusCode:0})}})}))},Nk=function(e,t){return new Promise(((n,o)=>{if(Array.isArray(e.files)){let r=e.files.length,i=new Array;t?t((t=>{let a=t.visitPrefix.length;"/"==t.visitPrefix.charAt(a-1)&&(t.visitPrefix=t.visitPrefix.substring(0,a-1)),function a(s){let l=e.files[s],c=function(e,t=""){const n="0123456789qwertyuioplkjhgfdsazxcvbnm";let o="",r=new Date;for(let i=0;i<e;i++)o+=n.charAt(Math.ceil(1e8*Math.random())%n.length);return"file/"+t+r.getTime()+o}(10,t.folderPath),u={fileIndex:s,files:e.files,...l};if(l.name){u.name=l.name;let e=l.name.split(".");c+="."+e[e.length-1]}$k(l.path||l,(t=>{u.url=t.imageURL,e.onEachUpdate&&e.onEachUpdate({url:t.imageURL,...u}),i.push(t.imageURL),r-1>s?a(s+1):n(i)}),(e=>{o(e)}),{region:t.region||"SCN",domain:t.visitPrefix,key:c,uptoken:t.token,uptokenURL:"UpTokenURL.com/uptoken"},(t=>{console.log(e),e.onProgressUpdate&&e.onProgressUpdate(Object.assign({},u,t))}))}(0)})):o({errMsg:"请添加七牛云回调方法：getQnToken",statusCode:0})}else o({errMsg:"files 必须是数组类型",statusCode:0})}))};const qk=Object.assign({},{name:"萤火商城2.0",apiUrl:"./index.php?s=/api/",storeId:10001,enabledSettingCache:!0,enabledAppShareWeixin:!1,enabledH5Multi:!1,domainIdRegex:/shop[\-]?(\d+)\./},{name:"萤火商城2.0",apiUrl:"./index.php?s=/api/",storeId:10001,enabledSettingCache:!0,enabledH5Multi:!0}),jk={all:()=>qk,get:(e,t)=>qk.hasOwnProperty(e)?qk[e]:(console.error(`检测到不存在的配置项: ${e}`),t),getStoreId(){if(this.get("enabledH5Multi")){const e=window.location.hostname,t=this.get("domainIdRegex");if(e.match(t)){return t.exec(e)[1].trim()}}return this.get("storeId")}},zk=jk.get("apiUrl"),Vk=new class extends class{constructor(e){this.baseUrl=e.baseUrl||"",this.fileUrl=e.fileUrl||"",this.timeout=e.timeout||6e3,this.defaultUploadUrl=e.defaultUploadUrl||"",this.header=e.header||{},this.config=e.config||{isPrompt:!0,load:!0,isFactory:!0,resend:0}}post(e="",t={},n={}){return this.request({method:"POST",data:t,url:e,...n})}get(e="",t={},n={}){return this.request({method:"GET",data:t,url:e,...n})}put(e="",t={},n={}){return this.request({method:"PUT",data:t,url:e,...n})}delete(e="",t={},n={}){return this.request({method:"DELETE",data:t,url:e,...n})}jsonp(e="",t={},n={}){return this.request({method:"JSONP",data:t,url:e,...n})}async request(e){let t,n=!1;try{if(!e.url)throw{errMsg:"【request】缺失数据url",statusCode:0};if(t=Ok(this,e),n=!0,this.requestStart){let e=this.requestStart(t);if("object"!=typeof e)throw{errMsg:"【request】请求开始拦截器未通过",statusCode:0,data:t.data,method:t.method,header:t.header,url:t.url};["data","header","isPrompt","load","isFactory"].forEach((n=>{t[n]=e[n]}))}let o={};if(o="JSONP"==t.method?await(e=>new Promise(((t,n)=>{let o="";Object.keys(e.data).forEach((t=>{o+=t+"="+e.data[t]+"&"})),""!==o&&(o=o.substr(0,o.lastIndexOf("&"))),e.url=e.url+"?"+o;let r="callback"+Math.ceil(1e6*Math.random());window[r]=e=>{t(e)};let i=document.createElement("script");i.src=e.url+"&callback="+r,document.head.appendChild(i),document.head.removeChild(i)})))(t):await(e=>new Promise(((t,n)=>{let o=!0,r={url:e.url,header:e.header,success:e=>{o=!1,t(e)},fail:e=>{o=!1,"request:fail abort"==e.errMsg?n({errMsg:"请求超时，请重新尝试",statusCode:0}):n(e)}};e.method&&(r.method=e.method),e.data&&(r.data=e.data),e.dataType&&(r.dataType=e.dataType),e.responseType&&(r.responseType=e.responseType),e.withCredentials&&(r.withCredentials=e.withCredentials);let i=uni.request(r);setTimeout((()=>{o&&i.abort()}),e.timeout)})))(t),t.isFactory&&this.dataFactory){let e=await this.dataFactory({...t,response:o});return Promise.resolve(e)}return Promise.resolve(o)}catch(o){return this.requestError&&this.requestError(o),Promise.reject(o)}finally{n&&this.requestEnd&&this.requestEnd(t)}}}{constructor(e){super(e)}async qnImgUpload(e={}){let t;try{t=await Rk(e),e.onSelectComplete&&e.onSelectComplete(t)}catch(n){return this.requestError&&this.requestError(n),Promise.reject(n)}if(t)return this.qnFileUpload({...e,files:t})}async qnVideoUpload(e={}){let t;try{t=await Bk(e),e.onSelectComplete&&e.onSelectComplete(t)}catch(n){return this.requestError&&this.requestError(n),Promise.reject(n)}if(t)return this.qnFileUpload({...e,files:t})}async qnFileUpload(e={}){let t;try{if(t={...this.config,...e,header:{},method:"FILE"},this.requestStart){let e=this.requestStart(t);if("object"!=typeof e)throw{errMsg:"【request】请求开始拦截器未通过",statusCode:0,data:t.data,method:t.method,header:t.header,url:t.url};["load","files"].forEach((n=>{t[n]=e[n]}))}let n=await Nk(t,this.getQnToken);return Promise.resolve(n)}catch(n){return this.requestError&&this.requestError(n),Promise.reject(n)}finally{this.requestEnd&&this.requestEnd(t)}}async urlImgUpload(){let e={};arguments[0]&&("string"==typeof arguments[0]?e.url=arguments[0]:"object"==typeof arguments[0]&&(e=Object.assign(e,arguments[0]))),arguments[1]&&"object"==typeof arguments[1]&&(e=Object.assign(e,arguments[1]));try{e.files=await Rk(e),e.onSelectComplete&&e.onSelectComplete(e.files)}catch(t){return this.requestError&&this.requestError(t),Promise.reject(t)}if(e.files)return this.urlFileUpload(e)}async urlVideoUpload(){let e={};arguments[0]&&("string"==typeof arguments[0]?e.url=arguments[0]:"object"==typeof arguments[0]&&(e=Object.assign(e,arguments[0]))),arguments[1]&&"object"==typeof arguments[1]&&(e=Object.assign(e,arguments[1]));try{e.files=await Bk(e),e.onSelectComplete&&e.onSelectComplete(e.files)}catch(t){return this.requestError&&this.requestError(t),Promise.reject(t)}if(e.files)return this.urlFileUpload(e)}async urlFileUpload(){let e={method:"FILE"};arguments[0]&&("string"==typeof arguments[0]?e.url=arguments[0]:"object"==typeof arguments[0]&&(e=Object.assign(e,arguments[0]))),arguments[1]&&"object"==typeof arguments[1]&&(e=Object.assign(e,arguments[1])),!e.url&&this.defaultUploadUrl&&(e.url=this.defaultUploadUrl);let t=!1;try{if(!e.url)throw{errMsg:"【request】文件上传缺失数据url",statusCode:0,data:e.data,method:e.method,header:e.header,url:e.url};if(e=Ok(this,e),t=!0,this.requestStart){let t=this.requestStart(e);if("object"!=typeof t)throw{errMsg:"【request】请求开始拦截器未通过",statusCode:0,data:e.data,method:e.method,header:e.header,url:e.url};["data","header","isPrompt","load","isFactory","files"].forEach((n=>{e[n]=t[n]}))}let n=await function(e,t){return new Promise(((n,o)=>{if(e.header["Content-Type"]&&delete e.header["Content-Type"],e.header["content-type"]&&delete e.header["content-type"],Array.isArray(e.files)){let r=function(s){let l=e.files[s],c={fileIndex:s,files:e.files,...l},u={url:e.url,filePath:l.path,header:e.header,name:e.name||"file",success:l=>{e.isFactory&&t?t({...e,response:l}).then((t=>{a.push(t),e.onEachUpdate&&e.onEachUpdate({data:t,...c}),i<=s?n(a):r(s+1)}),(e=>{o(e)})):(e.onEachUpdate&&e.onEachUpdate({data:l,...c}),a.push(l),i<=s?n(a):r(s+1))},fail:e=>{o(e)}};e.data&&(u.formData=e.data),uni.uploadFile(u).onProgressUpdate((t=>{e.onProgressUpdate&&e.onProgressUpdate(Object.assign({},c,t))}))};const i=e.files.length-1;let a=new Array;r(0)}else o({errMsg:"files 必须是数组类型",statusCode:0})}))}(e,this.dataFactory);return Promise.resolve(n)}catch(n){return this.requestError&&this.requestError(n),Promise.reject(n)}finally{t&&this.requestEnd&&this.requestEnd(e)}}}({baseUrl:zk,fileUrl:zk,defaultUploadUrl:"upload/image",header:{"content-type":"application/json;charset=utf-8"},timeout:15e3,config:{isPrompt:!0,load:!0,isFactory:!0}});let Fk=0;Vk.requestStart=e=>{if(e.load&&(Fk<=0&&uni.showLoading({title:"加载中",mask:!0}),Fk+=1),"FILE"==e.method&&e.maxSize){const t=e.maxSize;for(let n of e.files)if(n.size>t)return setTimeout((()=>{uni.showToast({title:"图片过大，请重新上传",icon:"none"})}),10),!1}return e.header.storeId=pC.getters.storeId,e.header.platform=pC.getters.platform,e.header["Access-Token"]=pC.getters.token,e},Vk.requestEnd=e=>{e.load&&(Fk-=1,Fk<=0&&uni.hideLoading())};let Uk=!1;Vk.dataFactory=async e=>{if(!e.response.statusCode||200!=e.response.statusCode)return Promise.reject({statusCode:e.response.statusCode,errMsg:"http状态码错误"});let t=e.response.data;if("string"==typeof t)try{t=JSON.parse(t)}catch(n){t=!1}return!1===t||"object"!=typeof t?Promise.reject({statusCode:e.response.statusCode,errMsg:"请检查api地址能否访问正常"}):200==t.status?Promise.resolve(t):401==t.status?(pC.dispatch("Logout"),Uk||(Uk=!0,uni.showModal({title:"温馨提示",content:"此时此刻需要您登录喔~",confirmText:"去登录",cancelText:"再逛会",success:e=>{e.confirm&&uni.navigateTo({url:"/pages/login/index"}),e.cancel&&Xm().length>1&&uni.navigateBack(),Uk=!1}})),Promise.reject({statusCode:0,errMsg:t.message,result:t})):500==t.status?(e.isPrompt&&setTimeout((()=>{uni.showToast({title:t.message,icon:"none",duration:2500})}),10),Promise.reject({statusCode:0,errMsg:t.message,result:t})):void 0},Vk.requestError=e=>{if(0===e.statusCode)throw e;setTimeout((()=>Wk(e)),10)};const Wk=e=>{let t=`网络请求出错：${e.errMsg}`;"request:fail"===e.errMsg&&(t="网络请求错误：请检查api地址能否访问正常"),uni.showToast({title:t,icon:"none",duration:3500})},Hk="passport/login",Yk="passport/loginMpWx",Xk="passport/loginWxOfficial",Gk="passport/loginMpWxMobile",Jk="passport/loginMpAlipay",Qk="passport/isPersonalMpweixin";function Kk(e,t){return Vk.post(Qk,e,t)}const Zk=(e,{token:t,userId:n})=>{const o=2592e3;Pk.set("userId",n,o),Pk.set("AccessToken",t,o),e("SET_TOKEN",t),e("SET_USER_ID",n)},eC=e=>e.replace(/\-/g,"/"),tC=(e={})=>{const t=[];for(const n in e){const o=e[n];o&&(aC(o)?o.forEach((e=>{t.push(n+"="+e)})):t.push(n+"="+o))}return t.join("&")},nC=(e="")=>{var t=new Object;if(e)for(var n=e.split("&"),o=0;o<n.length;o++)t[n[o].split("=")[0]]=n[o].split("=")[1]||"";return t},oC=(e,t)=>{for(var n in t)if(t[n]==e)return!0;return!1},rC=e=>0===Object.keys(e).length,iC=e=>"[object Object]"===Object.prototype.toString.call(e),aC=e=>"[object Array]"===Object.prototype.toString.call(e),sC=e=>aC(e)?0===e.length:iC(e)?rC(e):!e,lC=e=>{let t=aC(e)?[]:{};if("object"==typeof e){for(let n in e)t[n]="object"==typeof e[n]?lC(e[n]):e[n];return t}};function cC(e,t=100){let n;return function(){const o=this,r=arguments;n&&clearTimeout(n),n=setTimeout((function(){e.apply(o,r)}),t)}}const uC=(e,t)=>e.filter((e=>t.indexOf(e)>-1)),dC=e=>e*(()=>{const{windowWidth:e}=uni.getSystemInfoSync();return(e>750?560:e)/750})();const pC=new Ak({modules:{app:Ik,user:{state:{token:"",userId:null},mutations:{SET_TOKEN:(e,t)=>{e.token=t},SET_USER_ID:(e,t)=>{e.userId=t}},actions:{Login:({commit:e},t)=>new Promise(((n,o)=>{(function(e){return Vk.post(Hk,e)})({form:t}).then((t=>{const o=t.data;Zk(e,o),n(t)})).catch(o)})),LoginMpWx:({commit:e},t)=>new Promise(((n,o)=>{(function(e,t){return Vk.post(Yk,e,t)})({form:t},{isPrompt:!1}).then((t=>{const o=t.data;Zk(e,o),n(t)})).catch(o)})),LoginWxOfficial:({commit:e},t)=>new Promise(((n,o)=>{(function(e,t){return Vk.post(Xk,e,t)})({form:t},{isPrompt:!1}).then((t=>{const o=t.data;Zk(e,o),n(t)})).catch(o)})),LoginMpWxMobile:({commit:e},t)=>new Promise(((n,o)=>{(function(e,t){return Vk.post(Gk,e,t)})({form:t},{isPrompt:!1}).then((t=>{const o=t.data;Zk(e,o),n(t)})).catch(o)})),LoginMpAlipay:({commit:e},t)=>new Promise(((n,o)=>{(function(e,t){return Vk.post(Jk,e,t)})({form:t},{isPrompt:!1}).then((t=>{const o=t.data;Zk(e,o),n(t)})).catch(o)})),Logout({commit:e},t){const n=this;return new Promise(((t,o)=>{n.getters.userId>0&&(Pk.remove("userId"),Pk.remove("AccessToken"),e("SET_TOKEN",""),e("SET_USER_ID",null),t())}))}}},theme:{state:{appTheme:{mainBg:"#fa2209",mainBg2:"#ff6335",mainText:"#ffffff",viceBg:"#ffb100",viceBg2:"#ffb900",viceText:"#ffffff"}},mutations:{SET_APP_THEME:(e,t)=>{sC(t)||(e.appTheme=t)}},actions:{SetAppTheme:({commit:e},t)=>new Promise(((n,o)=>{Pk.set("appTheme",t),e("SET_APP_THEME",t),n()}))}}},state:{},mutations:{},actions:{},getters:{storeId:e=>e.app.storeId,platform:e=>e.app.platform,token:e=>e.user.token,userId:e=>e.user.userId,refereeId:e=>e.app.refereeId,appTheme:e=>e.theme.appTheme,modules:e=>e.app.modules}}),fC="store/data",hC=()=>Vk.get(fC),gC=(()=>{const e=window.navigator.userAgent.toLowerCase();return"micromessenger"===String(e.match(/MicroMessenger/i))})()?"WXOFFICIAL":"H5",mC="WXOFFICIAL"===gC,vC="setting/data";class yC{constructor(e){const t=[],n=[];if(!Array.isArray(e))throw new Error("param is not an array!");e.map((e=>{e.key&&e.name&&(t.push(e.key),n.push(e.value),this[e.key]=e,e.key!==e.value&&(this[e.value]=e))})),this.data=e,this.keyArr=t,this.valueArr=n}keyOf(e){return this.data[this.keyArr.indexOf(e)]}valueOf(e){return this.data[this.valueArr.indexOf(e)]}getNameByKey(e){const t=this.keyOf(e);if(!t)throw new Error("No enum constant"+e);return t.name}getNameByValue(e){const t=this.valueOf(e);if(!t)throw new Error("No enum constant"+e);return t.name}getValueByKey(e){const t=this.keyOf(e);if(!t)throw new Error("No enum constant"+e);return t.key}getData(){return this.data}}const bC=new yC([{key:"REGISTER",name:"账户注册设置",value:"register"},{key:"APP_THEME",name:"店铺页面风格",value:"app_theme"},{key:"PAGE_CATEGORY_TEMPLATE",name:"分类页模板",value:"page_category_template"},{key:"POINTS",name:"积分设置",value:"points"},{key:"RECHARGE",name:"充值设置",value:"recharge"},{key:"RECOMMENDED",name:"商品推荐设置",value:"recommended"},{key:"CUSTOMER",name:"商城客服设置",value:"customer"}]),_C=e=>{Pk.set("Setting",e,600)},wC=()=>new Promise(((e,t)=>{Vk.get(vC).then((t=>{e(t.data.setting)}))})),xC=e=>(null==e&&(e=jk.get("enabledSettingCache")),new Promise(((t,n)=>{const o=Pk.get("Setting");e&&o?t(o):wC().then((e=>{_C(e),t(e)}))}))),TC=(e,t)=>new Promise(((n,o)=>{xC(t).then((t=>n(t[e])))})),SC={setStorage:_C,data:xC,item:TC,setAppTheme:()=>new Promise(((e,t)=>{TC(bC.APP_THEME.value).then((t=>{pC.dispatch("SetAppTheme",t),e()}))})),isShowCustomerBtn:async()=>{const e=await TC(bC.CUSTOMER.value,!0);return!!e.enabled&&("wxqykf"===e.provider||"mpwxkf"===e.provider&&"MP-WEIXIN"===gC)}};var kC,CC={};kC=window,{get exports(){return CC},set exports(e){CC=e}}.exports=function(e,t){if(!e.jWeixin){var n,o={config:"preVerifyJSAPI",onMenuShareTimeline:"menu:share:timeline",onMenuShareAppMessage:"menu:share:appmessage",onMenuShareQQ:"menu:share:qq",onMenuShareWeibo:"menu:share:weiboApp",onMenuShareQZone:"menu:share:QZone",previewImage:"imagePreview",getLocation:"geoLocation",openProductSpecificView:"openProductViewWithPid",addCard:"batchAddCard",openCard:"batchViewCard",chooseWXPay:"getBrandWCPayRequest",openEnterpriseRedPacket:"getRecevieBizHongBaoRequest",startSearchBeacons:"startMonitoringBeacons",stopSearchBeacons:"stopMonitoringBeacons",onSearchBeacons:"onBeaconsInRange",consumeAndShareCard:"consumedShareCard",openAddress:"editAddress"},r=function(){var e={};for(var t in o)e[o[t]]=t;return e}(),i=e.document,a=i.title,s=navigator.userAgent.toLowerCase(),l=navigator.platform.toLowerCase(),c=!(!l.match("mac")&&!l.match("win")),u=-1!=s.indexOf("wxdebugger"),d=-1!=s.indexOf("micromessenger"),p=-1!=s.indexOf("android"),f=-1!=s.indexOf("iphone")||-1!=s.indexOf("ipad"),h=(n=s.match(/micromessenger\/(\d+\.\d+\.\d+)/)||s.match(/micromessenger\/(\d+\.\d+)/))?n[1]:"",g={initStartTime:O(),initEndTime:0,preVerifyStartTime:0,preVerifyEndTime:0},m={version:1,appId:"",initTime:0,preVerifyTime:0,networkType:"",isPreVerifyOk:1,systemType:f?1:p?2:-1,clientVersion:h,url:encodeURIComponent(location.href)},v={},y={_completes:[]},b={state:0,data:{}};L((function(){g.initEndTime=O()}));var _=!1,w=[],x={config:function(t){I("config",v=t);var n=!1!==v.check;L((function(){if(n)k(o.config,{verifyJsApiList:P(v.jsApiList),verifyOpenTagList:P(v.openTagList)},function(){y._complete=function(e){g.preVerifyEndTime=O(),b.state=1,b.data=e},y.success=function(e){m.isPreVerifyOk=0},y.fail=function(e){y._fail?y._fail(e):b.state=-1};var e=y._completes;return e.push((function(){!function(){if(!(c||u||v.debug||h<"6.0.2"||m.systemType<0)){var e=new Image;m.appId=v.appId,m.initTime=g.initEndTime-g.initStartTime,m.preVerifyTime=g.preVerifyEndTime-g.preVerifyStartTime,x.getNetworkType({isInnerInvoke:!0,success:function(t){m.networkType=t.networkType;var n="https://open.weixin.qq.com/sdk/report?v="+m.version+"&o="+m.isPreVerifyOk+"&s="+m.systemType+"&c="+m.clientVersion+"&a="+m.appId+"&n="+m.networkType+"&i="+m.initTime+"&p="+m.preVerifyTime+"&u="+m.url;e.src=n}})}}()})),y.complete=function(t){for(var n=0,o=e.length;n<o;++n)e[n]();y._completes=[]},y}()),g.preVerifyStartTime=O();else{b.state=1;for(var e=y._completes,t=0,r=e.length;t<r;++t)e[t]();y._completes=[]}})),x.invoke||(x.invoke=function(t,n,o){e.WeixinJSBridge&&WeixinJSBridge.invoke(t,E(n),o)},x.on=function(t,n){e.WeixinJSBridge&&WeixinJSBridge.on(t,n)})},ready:function(e){0!=b.state?e():(y._completes.push(e),!d&&v.debug&&e())},error:function(e){h<"6.0.2"||(-1==b.state?e(b.data):y._fail=e)},checkJsApi:function(e){k("checkJsApi",{jsApiList:P(e.jsApiList)},(e._complete=function(e){if(p){var t=e.checkResult;t&&(e.checkResult=JSON.parse(t))}e=function(e){var t=e.checkResult;for(var n in t){var o=r[n];o&&(t[o]=t[n],delete t[n])}return e}(e)},e))},onMenuShareTimeline:function(e){C(o.onMenuShareTimeline,{complete:function(){k("shareTimeline",{title:e.title||a,desc:e.title||a,img_url:e.imgUrl||"",link:e.link||location.href,type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareAppMessage:function(e){C(o.onMenuShareAppMessage,{complete:function(t){"favorite"===t.scene?k("sendAppMessage",{title:e.title||a,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""}):k("sendAppMessage",{title:e.title||a,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareQQ:function(e){C(o.onMenuShareQQ,{complete:function(){k("shareQQ",{title:e.title||a,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareWeibo:function(e){C(o.onMenuShareWeibo,{complete:function(){k("shareWeiboApp",{title:e.title||a,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareQZone:function(e){C(o.onMenuShareQZone,{complete:function(){k("shareQZone",{title:e.title||a,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},updateTimelineShareData:function(e){k("updateTimelineShareData",{title:e.title,link:e.link,imgUrl:e.imgUrl},e)},updateAppMessageShareData:function(e){k("updateAppMessageShareData",{title:e.title,desc:e.desc,link:e.link,imgUrl:e.imgUrl},e)},startRecord:function(e){k("startRecord",{},e)},stopRecord:function(e){k("stopRecord",{},e)},onVoiceRecordEnd:function(e){C("onVoiceRecordEnd",e)},playVoice:function(e){k("playVoice",{localId:e.localId},e)},pauseVoice:function(e){k("pauseVoice",{localId:e.localId},e)},stopVoice:function(e){k("stopVoice",{localId:e.localId},e)},onVoicePlayEnd:function(e){C("onVoicePlayEnd",e)},uploadVoice:function(e){k("uploadVoice",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},downloadVoice:function(e){k("downloadVoice",{serverId:e.serverId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},translateVoice:function(e){k("translateVoice",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},chooseImage:function(e){k("chooseImage",{scene:"1|2",count:e.count||9,sizeType:e.sizeType||["original","compressed"],sourceType:e.sourceType||["album","camera"]},(e._complete=function(e){if(p){var t=e.localIds;try{t&&(e.localIds=JSON.parse(t))}catch(n){}}},e))},getLocation:function(e){},previewImage:function(e){k(o.previewImage,{current:e.current,urls:e.urls},e)},uploadImage:function(e){k("uploadImage",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},downloadImage:function(e){k("downloadImage",{serverId:e.serverId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},getLocalImgData:function(e){!1===_?(_=!0,k("getLocalImgData",{localId:e.localId},(e._complete=function(e){if(_=!1,0<w.length){var t=w.shift();wx.getLocalImgData(t)}},e))):w.push(e)},getNetworkType:function(e){k("getNetworkType",{},(e._complete=function(e){e=function(e){var t=e.errMsg;e.errMsg="getNetworkType:ok";var n=e.subtype;if(delete e.subtype,n)e.networkType=n;else{var o=t.indexOf(":"),r=t.substring(o+1);switch(r){case"wifi":case"edge":case"wwan":e.networkType=r;break;default:e.errMsg="getNetworkType:fail"}}return e}(e)},e))},openLocation:function(e){k("openLocation",{latitude:e.latitude,longitude:e.longitude,name:e.name||"",address:e.address||"",scale:e.scale||28,infoUrl:e.infoUrl||""},e)},getLocation:function(e){k(o.getLocation,{type:(e=e||{}).type||"wgs84"},(e._complete=function(e){delete e.type},e))},hideOptionMenu:function(e){k("hideOptionMenu",{},e)},showOptionMenu:function(e){k("showOptionMenu",{},e)},closeWindow:function(e){k("closeWindow",{},e=e||{})},hideMenuItems:function(e){k("hideMenuItems",{menuList:e.menuList},e)},showMenuItems:function(e){k("showMenuItems",{menuList:e.menuList},e)},hideAllNonBaseMenuItem:function(e){k("hideAllNonBaseMenuItem",{},e)},showAllNonBaseMenuItem:function(e){k("showAllNonBaseMenuItem",{},e)},scanQRCode:function(e){k("scanQRCode",{needResult:(e=e||{}).needResult||0,scanType:e.scanType||["qrCode","barCode"]},(e._complete=function(e){if(f){var t=e.resultStr;if(t){var n=JSON.parse(t);e.resultStr=n&&n.scan_code&&n.scan_code.scan_result}}},e))},openAddress:function(e){k(o.openAddress,{},(e._complete=function(e){var t;(t=e).postalCode=t.addressPostalCode,delete t.addressPostalCode,t.provinceName=t.proviceFirstStageName,delete t.proviceFirstStageName,t.cityName=t.addressCitySecondStageName,delete t.addressCitySecondStageName,t.countryName=t.addressCountiesThirdStageName,delete t.addressCountiesThirdStageName,t.detailInfo=t.addressDetailInfo,delete t.addressDetailInfo,e=t},e))},openProductSpecificView:function(e){k(o.openProductSpecificView,{pid:e.productId,view_type:e.viewType||0,ext_info:e.extInfo},e)},addCard:function(e){for(var t=e.cardList,n=[],r=0,i=t.length;r<i;++r){var a=t[r],s={card_id:a.cardId,card_ext:a.cardExt};n.push(s)}k(o.addCard,{card_list:n},(e._complete=function(e){var t=e.card_list;if(t){for(var n=0,o=(t=JSON.parse(t)).length;n<o;++n){var r=t[n];r.cardId=r.card_id,r.cardExt=r.card_ext,r.isSuccess=!!r.is_succ,delete r.card_id,delete r.card_ext,delete r.is_succ}e.cardList=t,delete e.card_list}},e))},chooseCard:function(e){k("chooseCard",{app_id:v.appId,location_id:e.shopId||"",sign_type:e.signType||"SHA1",card_id:e.cardId||"",card_type:e.cardType||"",card_sign:e.cardSign,time_stamp:e.timestamp+"",nonce_str:e.nonceStr},(e._complete=function(e){e.cardList=e.choose_card_info,delete e.choose_card_info},e))},openCard:function(e){for(var t=e.cardList,n=[],r=0,i=t.length;r<i;++r){var a=t[r],s={card_id:a.cardId,code:a.code};n.push(s)}k(o.openCard,{card_list:n},e)},consumeAndShareCard:function(e){k(o.consumeAndShareCard,{consumedCardId:e.cardId,consumedCode:e.code},e)},chooseWXPay:function(e){k(o.chooseWXPay,A(e),e)},openEnterpriseRedPacket:function(e){k(o.openEnterpriseRedPacket,A(e),e)},startSearchBeacons:function(e){k(o.startSearchBeacons,{ticket:e.ticket},e)},stopSearchBeacons:function(e){k(o.stopSearchBeacons,{},e)},onSearchBeacons:function(e){C(o.onSearchBeacons,e)},openEnterpriseChat:function(e){k("openEnterpriseChat",{useridlist:e.userIds,chatname:e.groupName},e)},launchMiniProgram:function(e){k("launchMiniProgram",{targetAppId:e.targetAppId,path:function(e){if("string"==typeof e&&0<e.length){var t=e.split("?")[0],n=e.split("?")[1];return t+=".html",void 0!==n?t+"?"+n:t}}(e.path),envVersion:e.envVersion},e)},openBusinessView:function(e){k("openBusinessView",{businessType:e.businessType,queryString:e.queryString||"",envVersion:e.envVersion},(e._complete=function(e){if(p){var t=e.extraData;if(t)try{e.extraData=JSON.parse(t)}catch(n){e.extraData={}}}},e))},miniProgram:{navigateBack:function(e){e=e||{},L((function(){k("invokeMiniProgramAPI",{name:"navigateBack",arg:{delta:e.delta||1}},e)}))},navigateTo:function(e){L((function(){k("invokeMiniProgramAPI",{name:"navigateTo",arg:{url:e.url}},e)}))},redirectTo:function(e){L((function(){k("invokeMiniProgramAPI",{name:"redirectTo",arg:{url:e.url}},e)}))},switchTab:function(e){L((function(){k("invokeMiniProgramAPI",{name:"switchTab",arg:{url:e.url}},e)}))},reLaunch:function(e){L((function(){k("invokeMiniProgramAPI",{name:"reLaunch",arg:{url:e.url}},e)}))},postMessage:function(e){L((function(){k("invokeMiniProgramAPI",{name:"postMessage",arg:e.data||{}},e)}))},getEnv:function(t){L((function(){t({miniprogram:"miniprogram"===e.__wxjs_environment})}))}}},T=1,S={};return i.addEventListener("error",(function(e){if(!p){var t=e.target,n=t.tagName,o=t.src;if(("IMG"==n||"VIDEO"==n||"AUDIO"==n||"SOURCE"==n)&&-1!=o.indexOf("wxlocalresource://")){e.preventDefault(),e.stopPropagation();var r=t["wx-id"];if(r||(r=T++,t["wx-id"]=r),S[r])return;S[r]=!0,wx.ready((function(){wx.getLocalImgData({localId:o,success:function(e){t.src=e.localData}})}))}}}),!0),i.addEventListener("load",(function(e){if(!p){var t=e.target,n=t.tagName;if(t.src,"IMG"==n||"VIDEO"==n||"AUDIO"==n||"SOURCE"==n){var o=t["wx-id"];o&&(S[o]=!1)}}}),!0),t&&(e.wx=e.jWeixin=x),x}function k(t,n,o){e.WeixinJSBridge?WeixinJSBridge.invoke(t,E(n),(function(e){M(t,e,o)})):I(t,o)}function C(t,n,o){e.WeixinJSBridge?WeixinJSBridge.on(t,(function(e){o&&o.trigger&&o.trigger(e),M(t,e,n)})):I(t,o||n)}function E(e){return(e=e||{}).appId=v.appId,e.verifyAppId=v.appId,e.verifySignType="sha1",e.verifyTimestamp=v.timestamp+"",e.verifyNonceStr=v.nonceStr,e.verifySignature=v.signature,e}function A(e){return{timeStamp:e.timestamp+"",nonceStr:e.nonceStr,package:e.package,paySign:e.paySign,signType:e.signType||"SHA1"}}function M(e,t,n){"openEnterpriseChat"!=e&&"openBusinessView"!==e||(t.errCode=t.err_code),delete t.err_code,delete t.err_desc,delete t.err_detail;var o=t.errMsg;o||(o=t.err_msg,delete t.err_msg,o=function(e,t){var n=e,o=r[n];o&&(n=o);var i="ok";if(t){var a=t.indexOf(":");"confirm"==(i=t.substring(a+1))&&(i="ok"),"failed"==i&&(i="fail"),-1!=i.indexOf("failed_")&&(i=i.substring(7)),-1!=i.indexOf("fail_")&&(i=i.substring(5)),"access denied"!=(i=(i=i.replace(/_/g," ")).toLowerCase())&&"no permission to execute"!=i||(i="permission denied"),"config"==n&&"function not exist"==i&&(i="ok"),""==i&&(i="fail")}return n+":"+i}(e,o),t.errMsg=o),(n=n||{})._complete&&(n._complete(t),delete n._complete),o=t.errMsg||"",v.debug&&!n.isInnerInvoke&&alert(JSON.stringify(t));var i=o.indexOf(":");switch(o.substring(i+1)){case"ok":n.success&&n.success(t);break;case"cancel":n.cancel&&n.cancel(t);break;default:n.fail&&n.fail(t)}n.complete&&n.complete(t)}function P(e){if(e){for(var t=0,n=e.length;t<n;++t){var r=e[t],i=o[r];i&&(e[t]=i)}return e}}function I(e,t){if(!(!v.debug||t&&t.isInnerInvoke)){var n=r[e];n&&(e=n),t&&t._complete&&delete t._complete,console.log('"'+e+'",',t||"")}}function O(){return(new Date).getTime()}function L(t){d&&(e.WeixinJSBridge?t():i.addEventListener&&i.addEventListener("WeixinJSBridgeReady",t,!1))}}(kC);const EC={data:[],current_page:1,last_page:1,per_page:15,total:0},AC=(e,t)=>{uni.showToast({title:e,icon:"success",mask:!0,duration:1500,success(){t&&t()}})},MC=(e,t)=>{uni.showModal({title:"友情提示",content:e,showCancel:!1,success(e){t&&t()}})},PC=(e,t=1500,n=!0)=>{uni.showToast({title:e,icon:"none",mask:n,duration:t})},IC=()=>["pages/index/index","pages/category/index","pages/cart/index","pages/user/index"],OC=(e,t,n)=>{let o=e;if(!sC(t)){o+="#/"+t;const e=LC(n);sC(e)||(o+="?"+e)}return o},LC=e=>tC($C(e)),$C=e=>({refereeId:pC.getters.userId,...e}),DC=(e,t={},n="navigateTo")=>{if(!e||0==e.length)return!1;if(oC(e,["pages/index/index","pages/category/index","pages/cart/index","pages/user/index"]))return uni.switchTab({url:`/${e}`}),!0;const o=sC(t)?"":"?"+tC(t);return"navigateTo"===n&&uni.navigateTo({url:`/${e}${o}`}),"redirectTo"===n&&uni.redirectTo({url:`/${e}${o}`}),!0},RC=()=>{const e=Xm(),t=e[e.length-1].$page.fullPath.split("?");return{path:t[0].slice(1),query:nC(t[1])}},BC=e=>{uni.setStorageSync("cartTotalNum",Number(e))},NC=()=>{const e=(e=>{const t=uni.getStorageSync("cartTotalNum")||0;return qC()?t:0})();e>0?uni.setTabBarBadge({index:2,text:`${e}`}):uni.removeTabBarBadge({index:2})},qC=()=>!!pC.getters.userId,jC=()=>lC(EC),zC=(e,t,n)=>(1==n&&(t.data=[]),t.data.concat(e.data)),VC=e=>{return n="scene",iC(t=e)&&void 0!==t[n]?(e=>{if(void 0===e)return{};const t={},n=decodeURIComponent(e).split(",");for(const o in n){const e=n[o].split(":");e.length>0&&e[0]&&(t[e[0]]=e[1]||null)}return t})(e.scene):{};var t,n},FC=e=>oC(e,pC.getters.modules),UC=e=>e.filter((e=>FC(e))).length>0,WC=e=>e.filter((e=>!e.moduleKey||FC(e.moduleKey))),HC="wxofficial/jssdkConfig",YC="wxofficial/oauthUrl",XC="wxofficial/oauthUserInfo",GC={jssdkConfig:e=>Vk.get(HC,{url:e}),oauthUrl:e=>Vk.get(YC,{callbackUrl:e}),oauthUserInfo:e=>Vk.get(XC,{code:e})},JC=async()=>{const{path:e,query:t}=RC();return new Promise(((n,o)=>{aE.h5Url().then((o=>{const r=OC(o,e,t);n(r)}))}))},QC=async()=>{const e=window.location.href.split("#")[0],t=await GC.jssdkConfig(e);CC.config(t.data.config),CC.error((e=>{console.error("jWeixin.error",e),MC(`微信链接分享配置错误：${e.errMsg}`)}))},KC=async e=>{const t=await oE.isWxofficialLinkShareCard();mC&&t&&(e.link=await JC(),CC.ready((()=>{console.log("jWeixin.ready",e),CC.updateAppMessageShareData(e),CC.updateTimelineShareData(e)})))};let ZC=!1;const eE=()=>new Promise(((e,t)=>{aE.data().then((t=>{e(t.clientData.wxofficial.setting)}))})),tE=e=>new Promise(((t,n)=>{eE().then((n=>t(n[e])))})),nE=async()=>await tE("share"),oE={data:eE,item:tE,isWxofficialLinkShareCard:()=>new Promise(((e,t)=>{tE("share").then((t=>e(Boolean(t.enabled))))})),updateShareData:async e=>{ZC=!0;const t=await nE(),n=Object.assign({},t,e);console.log("options",n),KC(n)},setGlobalShareCard:async(e=!1)=>{const t=await nE();mC&&t.enabled&&(window.location.href.split("#")[0],await QC(),ZC&&!e||KC(t))}},rE=e=>{(e=>{Pk.set("Store",e,600)})(e),SC.setStorage(e.setting),SC.setAppTheme(),mC&&oE.setGlobalShareCard(!1),pC.dispatch("SetModules",e.modules)},iE=(e=!0)=>new Promise(((t,n)=>{const o=Pk.get("Store");e&&o?t(o):new Promise(((e,t)=>{hC().then((t=>{rE(t.data),e(t.data)}))})).then((e=>{t(e)}))})),aE={data:iE,storeInfo:()=>new Promise(((e,t)=>{iE().then((t=>e(t.storeInfo)))})),h5Url:()=>new Promise(((e,t)=>{iE().then((t=>{const n=t.clientData.h5.setting.baseUrl;e(n)}))})),isEnabledDealer:()=>new Promise(((e,t)=>{iE().then((t=>{const n=Boolean(t.dealer.setting.is_open);e(n)}))}))},sE={globalData:{},onLaunch({path:e,query:t,scene:n}){this.onStartupQuery(iC(t)?t:{}),this.getStoreInfo()},methods:{onStartupQuery(e){const t=VC(e),n=e.refereeId?e.refereeId:t.uid;n>0&&this.setRefereeId(n)},setRefereeId(e){pC.dispatch("setRefereeId",e)},getStoreInfo(){aE.data(!1)},updateManager(){const e=uni.getUpdateManager();e.onCheckForUpdate((e=>{})),e.onUpdateReady((()=>{uni.showModal({title:"更新提示",content:"新版本已经准备好，即将重启应用",showCancel:!1,success(t){t.confirm&&e.applyUpdate()}})})),e.onUpdateFailed((()=>{uni.showModal({title:"更新提示",content:"新版本下载失败",showCancel:!1})}))}}};function lE(){pC.commit("SET_STORE_ID",jk.getStoreId()),pC.commit("SET_PLATFORM",gC),pC.commit("SET_TOKEN",Pk.get("AccessToken")),pC.commit("SET_USER_ID",Pk.get("userId")),pC.commit("SET_REFEREE_ID",Pk.get("refereeId")),pC.commit("SET_APP_THEME",Pk.get("appTheme")),pC.commit("SET_MODULES",Pk.get("modules"))}wv(sE,{init:bv,setup(e){const t=Om(),n=()=>{var n;n=e,Object.keys(rf).forEach((e=>{rf[e].forEach((t=>{Ao(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i,onError:a}=e,s=function({path:e,query:t}){return x(Sh,{path:e,query:t}),x(kh,Sh),x({},Sh)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:be(t.query)});if(o&&X(o,s),r&&X(r,s),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};i&&X(i,e)}a&&(e.appContext.config.errorHandler=e=>{X(a,e)})};return Hn(Zs).isReady().then(n),Io((()=>{window.addEventListener("resize",xe(Tv,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",Sv),document.addEventListener("visibilitychange",kv),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{Lx.emit("onThemeChange",{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(Nr(),Fr(Ow));e.setup=(e,o)=>{const r=t&&t(e,o);return P(r)?n:r},e.render=n}});const cE={data:()=>({platform:gC}),computed:{appTheme:()=>pC.getters.appTheme,appThemeStyle:()=>(e=>{let t="";for(const n in e)t+=`--${n.replace(/([A-Z])/g,"-$1").toLowerCase()}:${e[n]};`;return t})(pC.getters.appTheme)}},uE={data:()=>({}),onLoad(){this.$u.getRect=this.$uGetRect},methods:{$uGetRect(e,t){return new Promise((n=>{uni.createSelectorQuery().in(this)[t?"selectAll":"select"](e).boundingClientRect((e=>{t&&Array.isArray(e)&&e.length&&n(e),!t&&e&&n(e)})).exec()}))},getParentData(e=""){this.parent||(this.parent=!1),this.parent=this.$u.$parent.call(this,e),this.parent&&(Object.keys(this.parentData).map((e=>{this.parentData[e]=this.parent[e]})),this.parentData.value=this.parent.modelValue)},preventEvent(e){e&&e.stopPropagation&&e.stopPropagation()}},onReachBottom(){uni.$emit("uOnReachBottom")},beforeUnmount(){if(this.parent&&uni.$u.test.array(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}}};function dE(e){if([null,void 0,NaN,!1].includes(e))return e;if("object"!=typeof e&&"function"!=typeof e)return e;var t,n=(t=e,"[object Array]"===Object.prototype.toString.call(t)?[]:{});for(let o in e)e.hasOwnProperty(o)&&(n[o]="object"==typeof e[o]?dE(e[o]):e[o]);return n}function pE(e={},t={}){if("object"!=typeof(e=dE(e))||"object"!=typeof t)return!1;for(var n in t)t.hasOwnProperty(n)&&(n in e?"object"!=typeof e[n]||"object"!=typeof t[n]?e[n]=t[n]:e[n].concat&&t[n].concat?e[n]=e[n].concat(t[n]):e[n]=pE(e[n],t[n]):e[n]=t[n]);return e}function fE(e){switch(typeof e){case"undefined":return!0;case"string":if(0==e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(0===e||isNaN(e))return!0;break;case"object":if(null===e||0===e.length)return!0;for(var t in e)return!1;return!0}return!1}const hE={email:function(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)},mobile:function(e){return/^1[23456789]\d{9}$/.test(e)},url:function(e){return/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w-.\/?%&=]*)?/.test(e)},date:function(e){return!/Invalid|NaN/.test(new Date(e).toString())},dateISO:function(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},number:function(e){return/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(e)},digits:function(e){return/^\d+$/.test(e)},idCard:function(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)},carNo:function(e){const t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===e.length?n.test(e):8===e.length&&t.test(e)},amount:function(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)},chinese:function(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)},letter:function(e){return/^[a-zA-Z]*$/.test(e)},enOrNum:function(e){return/^[0-9a-zA-Z]*$/g.test(e)},contains:function(e,t){return e.indexOf(t)>=0},range:function(e,t){return e>=t[0]&&e<=t[1]},rangeLength:function(e,t){return e.length>=t[0]&&e.length<=t[1]},empty:fE,isEmpty:fE,jsonString:function(e){if("string"==typeof e)try{var t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(kC){return!1}return!1},landline:function(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)},object:function(e){return"[object Object]"===Object.prototype.toString.call(e)},array:function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)},code:function(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)}};const gE=new class{setConfig(e){this.config=pE(this.config,e)}request(e={}){if(this.interceptor.request&&"function"==typeof this.interceptor.request){let t=this.interceptor.request(e);if(!1===t)return new Promise((()=>{}));this.options=t}return e.dataType=e.dataType||this.config.dataType,e.responseType=e.responseType||this.config.responseType,e.url=e.url||"",e.params=e.params||{},e.header=Object.assign({},this.config.header,e.header),e.method=e.method||this.config.method,new Promise(((t,n)=>{e.complete=e=>{if(uni.hideLoading(),clearTimeout(this.config.timer),this.config.timer=null,this.config.originalData)if(this.interceptor.response&&"function"==typeof this.interceptor.response){let o=this.interceptor.response(e);!1!==o?t(o):n(e)}else t(e);else if(200==e.statusCode)if(this.interceptor.response&&"function"==typeof this.interceptor.response){let o=this.interceptor.response(e.data);!1!==o?t(o):n(e.data)}else t(e.data);else n(e)},e.url=hE.url(e.url)?e.url:this.config.baseUrl+(0==e.url.indexOf("/")?e.url:"/"+e.url),this.config.showLoading&&!this.config.timer&&(this.config.timer=setTimeout((()=>{uni.showLoading({title:this.config.loadingText,mask:this.config.loadingMask}),this.config.timer=null}),this.config.loadingTime)),uni.request(e)}))}constructor(){this.config={baseUrl:"",header:{},method:"POST",dataType:"json",responseType:"text",showLoading:!0,loadingText:"请求中...",loadingTime:800,timer:null,originalData:!1,loadingMask:!0},this.interceptor={request:null,response:null},this.get=(e,t={},n={})=>this.request({method:"GET",url:e,header:n,data:t}),this.post=(e,t={},n={})=>this.request({url:e,method:"POST",header:n,data:t}),this.put=(e,t={},n={})=>this.request({url:e,method:"PUT",header:n,data:t}),this.delete=(e,t={},n={})=>this.request({url:e,method:"DELETE",header:n,data:t})}};const mE=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,t){e=e&&this.addRootPath(e);let n="";return/.*\/.*\?.*=.*/.test(e)?(n=uni.$u.queryParams(t,!1),e+"&"+n):(n=uni.$u.queryParams(t),e+n)}async route(e={},t={}){let n={};if("string"==typeof e?(n.url=this.mixinParam(e,t),n.type="navigateTo"):(n=uni.$u.deepClone(e,this.config),n.url=this.mixinParam(e.url,e.params)),t.intercept&&(this.config.intercept=t.intercept),n.params=t,n=uni.$u.deepMerge(this.config,n),"function"==typeof uni.$u.routeIntercept){await new Promise(((e,t)=>{uni.$u.routeIntercept(n,e)}))&&this.openPage(n)}else this.openPage(n)}openPage(e){const{url:t,type:n,delta:o,animationType:r,animationDuration:i}=e;"navigateTo"!=e.type&&"to"!=e.type||uni.navigateTo({url:t,animationType:r,animationDuration:i}),"redirectTo"!=e.type&&"redirect"!=e.type||uni.redirectTo({url:t}),"switchTab"!=e.type&&"tab"!=e.type||uni.switchTab({url:t}),"reLaunch"!=e.type&&"launch"!=e.type||uni.reLaunch({url:t}),"navigateBack"!=e.type&&"back"!=e.type||uni.navigateBack({delta:o})}}).route;function vE(e=null,t="yyyy-mm-dd"){e||(e=Number(new Date)),10==e.toString().length&&(e*=1e3);let n,o=new Date(e),r={"y+":o.getFullYear().toString(),"m+":(o.getMonth()+1).toString(),"d+":o.getDate().toString(),"h+":o.getHours().toString(),"M+":o.getMinutes().toString(),"s+":o.getSeconds().toString()};for(let i in r)n=new RegExp("("+i+")").exec(t),n&&(t=t.replace(n[1],1==n[1].length?r[i]:r[i].padStart(n[1].length,"0")));return t}function yE(e,t=!0){if((e=e.toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e)){if(4===e.length){let t="#";for(let n=1;n<4;n+=1)t+=e.slice(n,n+1).concat(e.slice(n,n+1));e=t}let n=[];for(let t=1;t<7;t+=2)n.push(parseInt("0x"+e.slice(t,t+2)));return t?`rgb(${n[0]},${n[1]},${n[2]})`:n}if(/^(rgb|RGB)/.test(e)){return e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map((e=>Number(e)))}return e}function bE(e){let t=e;if(/^(rgb|RGB)/.test(t)){let e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(","),n="#";for(let t=0;t<e.length;t++){let o=Number(e[t]).toString(16);o=1==String(o).length?"0"+o:o,"0"===o&&(o+=o),n+=o}return 7!==n.length&&(n=t),n}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t))return t;{let e=t.replace(/#/,"").split("");if(6===e.length)return t;if(3===e.length){let t="#";for(let n=0;n<e.length;n+=1)t+=e[n]+e[n];return t}}}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if("[object String]"!==Object.prototype.toString.call(t))throw new TypeError("fillString must be String");let n=this;if(n.length>=e)return String(n);let o=e-n.length,r=Math.ceil(o/t.length);for(;r>>=1;)t+=t,1===r&&(t+=t);return t.slice(0,o)+n});const _E={colorGradient:function(e="rgb(0, 0, 0)",t="rgb(255, 255, 255)",n=10){let o=yE(e,!1),r=o[0],i=o[1],a=o[2],s=yE(t,!1),l=(s[0]-r)/n,c=(s[1]-i)/n,u=(s[2]-a)/n,d=[];for(let p=0;p<n;p++){let e=bE("rgb("+Math.round(l*p+r)+","+Math.round(c*p+i)+","+Math.round(u*p+a)+")");d.push(e)}return d},hexToRgb:yE,rgbToHex:bE,colorToRgba:function(e,t=.3){let n=(e=bE(e)).toLowerCase();if(n&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(n)){if(4===n.length){var o="#";for(let e=1;e<4;e+=1)o+=n.slice(e,e+1).concat(n.slice(e,e+1));n=o}var r=[];for(let e=1;e<7;e+=2)r.push(parseInt("0x"+n.slice(e,e+2)));return"rgba("+r.join(",")+","+t+")"}return n}};let wE=null;let xE=[],TE=[];const SE={v:"1.10.1",version:"1.10.1",type:["primary","success","info","error","warning"]};const kE={queryParams:function(e={},t=!0,n="brackets"){let o=t?"?":"",r=[];-1==["indices","brackets","repeat","comma"].indexOf(n)&&(n="brackets");for(let i in e){let t=e[i];if(!(["",void 0,null].indexOf(t)>=0))if(t.constructor===Array)switch(n){case"indices":for(let n=0;n<t.length;n++)r.push(i+"["+n+"]="+t[n]);break;case"brackets":default:t.forEach((e=>{r.push(i+"[]="+e)}));break;case"repeat":t.forEach((e=>{r.push(i+"="+e)}));break;case"comma":let e="";t.forEach((t=>{e+=(e?",":"")+t})),r.push(i+"="+e)}else r.push(i+"="+t)}return r.length?o+r.join("&"):""},route:mE,timeFormat:vE,date:vE,timeFrom:function(e=null,t="yyyy-mm-dd"){e||(e=Number(new Date)),10==e.toString().length&&(e*=1e3);let n=+new Date(Number(e)),o=(Number(new Date)-n)/1e3,r="";switch(!0){case o<300:r="刚刚";break;case o>=300&&o<3600:r=parseInt(o/60)+"分钟前";break;case o>=3600&&o<86400:r=parseInt(o/3600)+"小时前";break;case o>=86400&&o<2592e3:r=parseInt(o/86400)+"天前";break;default:r=!1===t?o>=2592e3&&o<31536e3?parseInt(o/2592e3)+"个月前":parseInt(o/31536e3)+"年前":vE(n,t)}return r},colorGradient:_E.colorGradient,colorToRgba:_E.colorToRgba,guid:function(e=32,t=!0,n=null){let o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),r=[];if(n=n||o.length,e)for(let i=0;i<e;i++)r[i]=o[0|Math.random()*n];else{let e;r[8]=r[13]=r[18]=r[23]="-",r[14]="4";for(let t=0;t<36;t++)r[t]||(e=0|16*Math.random(),r[t]=o[19==t?3&e|8:e])}return t?(r.shift(),"u"+r.join("")):r.join("")},color:{primary:"#2979ff",primaryDark:"#2b85e4",primaryDisabled:"#a0cfff",primaryLight:"#ecf5ff",bgColor:"#f3f4f6",info:"#909399",infoDark:"#82848a",infoDisabled:"#c8c9cc",infoLight:"#f4f4f5",warning:"#ff9900",warningDark:"#f29100",warningDisabled:"#fcbd71",warningLight:"#fdf6ec",error:"#fa3534",errorDark:"#dd6161",errorDisabled:"#fab6b6",errorLight:"#fef0f0",success:"#19be6b",successDark:"#18b566",successDisabled:"#71d5a1",successLight:"#dbf1e1",mainColor:"#303133",contentColor:"#606266",tipsColor:"#909399",lightColor:"#c0c4cc",borderColor:"#e4e7ed"},sys:function(){return uni.getSystemInfoSync()},os:function(){return uni.getSystemInfoSync().platform},type2icon:function(e="success",t=!1){-1==["primary","info","error","warning","success"].indexOf(e)&&(e="success");let n="";switch(e){case"primary":case"info":n="info-circle";break;case"error":n="close-circle";break;case"warning":n="error-circle";break;default:n="checkmark-circle"}return t&&(n+="-fill"),n},randomArray:function(e=[]){return e.sort((()=>Math.random()-.5))},wranning:function(e){},get:gE.get,post:gE.post,put:gE.put,delete:gE.delete,hexToRgb:_E.hexToRgb,rgbToHex:_E.rgbToHex,test:hE,random:function(e,t){if(e>=0&&t>0&&t>=e){let n=t-e+1;return Math.floor(Math.random()*n+e)}return 0},deepClone:dE,deepMerge:pE,getParent:function(e,t){let n=this.$parent;for(;n;){if(n.$options.name===e){let e={};if(Array.isArray(t))t.map((t=>{e[t]=n[t]?n[t]:""}));else for(let o in t)Array.isArray(t[o])?t[o].length?e[o]=t[o]:e[o]=n[o]:t[o].constructor===Object?Object.keys(t[o]).length?e[o]=t[o]:e[o]=n[o]:e[o]=t[o]||!1===t[o]?t[o]:n[o];return e}n=n.$parent}return{}},$parent:function(e){let t=this.$parent;for(;t;){if(!t.$options||t.$options.name===e)return t;t=t.$parent}return!1},addUnit:function(e="auto",t="rpx"){return e=String(e),hE.number(e)?`${e}${t}`:e},trim:function(e,t="both"){return"both"==t?e.replace(/^\s+|\s+$/g,""):"left"==t?e.replace(/^\s*/,""):"right"==t?e.replace(/(\s*$)/g,""):"all"==t?e.replace(/\s+/g,""):e},type:["primary","success","error","warning","info"],http:gE,toast:function(e,t=1500){uni.showToast({title:e,icon:"none",duration:t})},config:SE,zIndex:{toast:10090,noNetwork:10080,popup:10075,mask:10070,navbar:980,topTips:975,sticky:970,indexListSticky:965},debounce:function(e,t=500,n=!1){if(null!==wE&&clearTimeout(wE),n){var o=!wE;wE=setTimeout((function(){wE=null}),t),o&&"function"==typeof e&&e()}else wE=setTimeout((function(){"function"==typeof e&&e()}),t)},throttle:function(e,t=500,n=!0,o="default"){xE[o]||(xE[o]=null),n?TE[o]||(TE[o]=!0,"function"==typeof e&&e(),xE[o]=setTimeout((()=>{TE[o]=!1}),t)):TE[o]||(TE[o]=!0,xE[o]=setTimeout((()=>{TE[o]=!1,"function"==typeof e&&e()}),t))}};uni.$u=kE;const CE={install:e=>{e.mixin(uE),e.config.globalProperties.$u=kE}};(function(){const e=Sa({...sE,store:pC,created:lE});return e.config.globalProperties.$toast=PC,e.config.globalProperties.$success=AC,e.config.globalProperties.$error=MC,e.config.globalProperties.$navTo=DC,e.config.globalProperties.$getShareUrlParams=LC,e.config.globalProperties.$checkModule=FC,e.config.globalProperties.$checkModules=UC,e.use(CE),e.mixin(cE),{app:e}})().app.use(uv).mount("#app");export{Vk as $,bC as A,Xo as B,oC as C,qC as D,yC as E,Or as F,uC as G,cC as H,cg as I,WC as J,pC as K,nC as L,am as M,rm as N,Mv as O,Gr as P,lC as Q,sC as R,aE as S,Xm as T,Tu as U,Kk as V,tC as W,GC as X,IC as Y,VC as Z,Gg as _,Jr as a,Yg as a0,Ng as a1,Pk as a2,fm as a3,yu as a4,aC as a5,iC as a6,rC as a7,gC as a8,I as a9,em as aa,eC as ab,mC as ac,oE as ad,jk as ae,RC as af,OC as ag,Mx as ah,$C as ai,Vr as b,Fr as c,Yo as d,Zr as e,Kr as f,Uh as g,dC as h,hm as i,nm as j,l as k,cm as l,ba as m,o as n,Nr as o,BC as p,jC as q,Vo as r,NC as s,f as t,zC as u,jo as v,qn as w,_a as x,Uo as y,SC as z};
