<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\groupon;

use think\model\Collection;
use app\common\library\helper;
use app\store\model\GoodsSku as GoodsSkuModel;
use app\common\model\groupon\GoodsSku as GrouponGoodsSkuModel;
use app\common\enum\goods\SpecType as SpecTypeEnum;

/**
 * 拼团商品SKU模型
 * Class GoodsSku
 * @package app\store\model\groupon
 */
class GoodsSku extends GrouponGoodsSkuModel
{
    /**
     * 隐藏的字段
     * @var string[]
     */
    protected $hidden = [
        'store_id',
        'create_time',
        'update_time',
    ];

    /**
     * 获取拼团商品SKU列表 (包含主商品sku的一些数据)
     * @param Collection $grouponSkuList 拼团商品SKU列表
     * @param int $goodsId 主商品ID
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getCommonSkuList(Collection $grouponSkuList, int $goodsId): array
    {
        // 获取主商品SKU列表
        $mainSkuData = static::getMainSkuList($goodsId);
        // 合并整理成新的数据 (商品价格、库存数量、商品sku编码)
        $data = [];
        foreach ($grouponSkuList as &$item) {
            $mainItem = $mainSkuData[$item['goods_sku_id']];
            $item['goods_sku_no'] = $mainItem['goods_sku_no'];
            $item['goods_price'] = $mainItem['goods_price'];
            $item['stock_num'] = $mainItem['stock_num'];
        }
        return $data;
    }

    /**
     * 获取主商品SKU列表
     * @param int $goodsId 主商品ID
     * @return array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private static function getMainSkuList(int $goodsId)
    {
        // 获取主商品SKU列表
        $mainSkuList = GoodsSkuModel::getSkuList($goodsId);
        if ($mainSkuList->isEmpty()) {
            return [];
        }
        // 将列表数据主键设置为goods_sku_id
        return helper::arrayColumn2Key($mainSkuList->toArray(), 'goods_sku_id');
    }

    /**
     * 获取商品价格高低区间 (根据sku列表数据)
     * @param array $skuList
     * @param bool $isSteps
     * @return array
     */
    public static function getGoodsPrices(array $skuList, bool $isSteps): array
    {
        // 阶梯拼团价格
        if ($isSteps) {
            $stepsPriceAttr = [];
            foreach ($skuList as $item) {
                $stepsPriceAttr = array_merge($stepsPriceAttr, $item['steps_price_config']);
            }
            if (!empty($stepsPriceAttr)) {
                return [min($stepsPriceAttr), max($stepsPriceAttr)];
            }
        }
        // 普通拼团价格
        $goodsPriceArr = helper::getArrayColumn($skuList, 'groupon_price');
        return [min($goodsPriceArr), max($goodsPriceArr)];
    }

    /**
     * 生成skuList数据(用于编辑拼团商品)
     * @param int $goodsId 主商品ID
     * @param array $skuList 用户提交的SKU数据
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getNewSkuList(int $goodsId, array $skuList): array
    {
        // 获取主商品SKU列表
        $mainSkuData = static::getMainSkuList($goodsId);
        foreach ($skuList as &$skuItem) {
            $mainItem = $mainSkuData[$skuItem['goods_sku_id']];
            $skuItem['spec_value_ids'] = $mainItem['spec_value_ids'];
            $skuItem['goods_props'] = $mainItem['goods_props'];
        }
        return $skuList;
    }

    /**
     * 生成默认skuList数据(用于新增拼团商品)
     * @param int $goodsId 主商品ID
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getDefaultSkuList(int $goodsId)
    {
        // 获取主商品SKU列表
        $data = static::getMainSkuList($goodsId);
        foreach ($data as &$skuItem) {
            $skuItem['groupon_price'] = $skuItem['goods_price'];
            $skuItem['steps_price_config'] = [];
            $skuItem['create_time'] = time();
            $skuItem['update_time'] = time();
        }
        return $data;
    }

    /**
     * 新增商品sku记录
     * @param int $grouponGoodsId 拼团商品ID
     * @param array $newSkuList
     * @param int $specType
     * @return array|bool|false
     */
    public static function add(int $grouponGoodsId, int $specType = SpecTypeEnum::SINGLE, array $newSkuList = [])
    {
        // 单规格模式
        if ($specType === SpecTypeEnum::SINGLE) {
            return (new static)->save(array_merge($newSkuList, [
                'groupon_goods_id' => $grouponGoodsId,
                'goods_sku_id' => 0,
                'store_id' => self::$storeId
            ]));
        } // 多规格模式
        elseif ($specType === SpecTypeEnum::MULTI) {
            // 批量写入商品sku记录
            return static::increasedFroMulti($grouponGoodsId, $newSkuList);
        }
        return false;
    }

    /**
     * 更新商品sku记录
     * @param int $grouponGoodsId 拼团商品ID
     * @param int $specType
     * @param array $skuList
     * @return array|bool|false
     */
    public static function edit(int $grouponGoodsId, int $specType = SpecTypeEnum::SINGLE, array $skuList = [])
    {
        // 删除所有的sku记录
        static::deleteAll(['groupon_goods_id' => $grouponGoodsId]);
        // 新增商品sku记录
        return static::add($grouponGoodsId, $specType, $skuList);
    }

    /**
     * 批量写入商品sku记录
     * @param int $grouponGoodsId 拼团商品ID
     * @param array $skuList
     * @return array|false
     */
    private static function increasedFroMulti(int $grouponGoodsId, array $skuList)
    {
        $dataset = [];
        foreach ($skuList as $skuItem) {
            unset($skuItem['id']);
            $dataset[] = array_merge($skuItem, [
                'id' => null,   // 此处的id必须是数据库自增
                'goods_sku_id' => $skuItem['goods_sku_id'],
                'groupon_price' => $skuItem['groupon_price'] ?: 0.01,
                'steps_price_config' => $skuItem['steps_price_config'] ?? [],
                'goods_props' => $skuItem['goods_props'],
                'spec_value_ids' => $skuItem['spec_value_ids'],
                'groupon_goods_id' => $grouponGoodsId,
                'store_id' => self::$storeId
            ]);
        }
        return (new static)->addAll($dataset);
    }
}