<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\bargain;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\bargain\Active as ActiveModel;

/**
 * 砍价活动管理
 * Class Active
 * @package app\store\controller\apps\bargain
 */
class Active extends Controller
{
    /**
     * 砍价活动列表
     * @param string $search
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(string $search = ''): Json
    {
        $model = new ActiveModel;
        $list = $model->getList($search);
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 砍价活动详情
     * @param int $activeId
     * @return Json
     */
    public function detail(int $activeId): Json
    {
        // 砍价活动详情
        $detail = ActiveModel::detail($activeId);
        return $this->renderSuccess(compact('detail'));
    }

    /**
     * 新增砍价活动
     * @return Json
     */
    public function add(): Json
    {
        // 新增记录
        $model = new ActiveModel;
        if ($model->add($this->postForm())) {
            return $this->renderSuccess('添加成功');
        }
        return $this->renderError($model->getError() ?: '添加失败');
    }

    /**
     * 更新砍价活动
     * @param int $activeId
     * @return Json
     */
    public function edit(int $activeId): Json
    {
        // 砍价活动详情
        $model = ActiveModel::detail($activeId);
        // 更新记录
        if ($model->edit($this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }

    /**
     * 删除砍价活动
     * @param int $activeId
     * @return Json
     */
    public function delete(int $activeId): Json
    {
        // 砍价活动详情
        $model = ActiveModel::detail($activeId);
        if (!$model->setDelete()) {
            return $this->renderError($model->getError() ?: '删除失败');
        }
        return $this->renderSuccess('删除成功');
    }
}