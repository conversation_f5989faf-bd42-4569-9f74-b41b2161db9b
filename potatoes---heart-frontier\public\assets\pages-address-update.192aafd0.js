import{a as e,_ as a,b as o}from"./u-form.4d62b4b6.js";import{o as l,c as t,w as r,n as s,i,a as d,f as n,k as m,y as u}from"./index-86a01735.js";import{r as p}from"./uni-app.es.1cc81ac8.js";import{_ as c}from"./select-region.aa33d0f2.js";import{a as f}from"./verify.3975cb19.js";import{c as g,a as h,e as b}from"./address.fee2943c.js";import{_}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.abb5b4fa.js";import"./emitter.1571a5d9.js";import"./u-loading.d2db78f5.js";import"./u-popup.6604e8b6.js";import"./u-mask.6931c55d.js";const y={name:[{required:!0,message:"请输入姓名",trigger:["blur","change"]}],phone:[{required:!0,message:"请输入手机号",trigger:["blur","change"]},{validator:(e,a,o)=>f(a),message:"手机号码不正确",trigger:["blur"]}],region:[{required:!0,message:"请选择省市区",trigger:["blur","change"],type:"array"}],detail:[{required:!0,message:"请输入详细地址",trigger:["blur","change"]}]};const V=_({components:{SelectRegion:c},data:()=>({form:{content:"",name:"",phone:"",region:[],detail:""},rules:y,isLoading:!0,disabled:!1,addressId:null}),onLoad({addressId:e}){this.addressId=e,this.getDetail()},onReady(){this.$refs.uForm.setRules(this.rules)},methods:{getDetail(){const e=this;g(e.addressId).then((a=>{const o=a.data.detail;e.createFormData(o)}))},handleAnalysis(){const e=this;h(e.form.content).then((a=>{const o=a.data.detail;e.createFormData(o)}))},createFormData(e){const{form:a}=this;a.name=e.name,a.phone=e.phone,a.detail=e.detail,a.region=this.createRegion(e)},createRegion(e){return 0==e.province_id||0==e.city_id||0==e.region_id?(this.$toast("很抱歉，地区未能识别请手动选择",2e3),[]):[{label:e.region.province,value:e.province_id},{label:e.region.city,value:e.city_id},{label:e.region.region,value:e.region_id}]},handleSubmit(){const e=this;if(e.disabled)return!1;e.$refs.uForm.validate((a=>{a&&(e.disabled=!0,b(e.addressId,e.form).then((a=>{e.$toast(a.message),uni.navigateBack()})).finally((()=>e.disabled=!1)))}))}}},[["render",function(f,g,h,b,_,y){const V=p(u("u-input"),e),j=p(u("u-form-item"),a),v=i,x=p(u("select-region"),c),F=p(u("u-form"),o);return l(),t(v,{class:"container",style:s(f.appThemeStyle)},{default:r((()=>[d(v,{class:"form-analysis form-wrapper"},{default:r((()=>[d(j,{prop:"name","border-bottom":!1},{default:r((()=>[d(V,{modelValue:_.form.content,"onUpdate:modelValue":g[0]||(g[0]=e=>_.form.content=e),type:"textarea",placeholder:"粘贴地址信息，自动解析姓名、电话和地址","custom-style":{height:"150rpx"},"auto-height":!1},null,8,["modelValue"])])),_:1}),d(v,{class:"analysis-foot clearfix"},{default:r((()=>[d(v,{class:"analysis-btn",onClick:g[1]||(g[1]=e=>y.handleAnalysis())},{default:r((()=>[n("智能识别")])),_:1})])),_:1})])),_:1}),d(v,{class:"page-title"},{default:r((()=>[n("收货地址")])),_:1}),d(v,{class:"form-wrapper"},{default:r((()=>[d(F,{model:_.form,ref:"uForm","label-width":"140rpx"},{default:r((()=>[d(j,{label:"姓名",prop:"name"},{default:r((()=>[d(V,{modelValue:_.form.name,"onUpdate:modelValue":g[2]||(g[2]=e=>_.form.name=e),placeholder:"请输入收货人姓名"},null,8,["modelValue"])])),_:1}),d(j,{label:"电话",prop:"phone"},{default:r((()=>[d(V,{modelValue:_.form.phone,"onUpdate:modelValue":g[3]||(g[3]=e=>_.form.phone=e),placeholder:"请输入收货人手机号"},null,8,["modelValue"])])),_:1}),d(j,{label:"地区",prop:"region"},{default:r((()=>[d(x,{ref:"sRegion",modelValue:_.form.region,"onUpdate:modelValue":g[4]||(g[4]=e=>_.form.region=e)},null,8,["modelValue"])])),_:1}),d(j,{label:"详细地址",prop:"detail","border-bottom":!1},{default:r((()=>[d(V,{modelValue:_.form.detail,"onUpdate:modelValue":g[5]||(g[5]=e=>_.form.detail=e),placeholder:"街道门牌、楼层等信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1}),d(v,{class:"footer"},{default:r((()=>[d(v,{class:"btn-wrapper"},{default:r((()=>[d(v,{class:m(["btn-item btn-item-main",{disabled:_.disabled}]),onClick:g[6]||(g[6]=e=>y.handleSubmit())},{default:r((()=>[n("保存")])),_:1},8,["class"])])),_:1})])),_:1})])),_:1},8,["style"])}],["__scopeId","data-v-72691e6a"]]);export{V as default};
