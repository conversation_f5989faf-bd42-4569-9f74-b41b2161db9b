<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\service\qrcode;

use app\common\model\h5\Setting as H5SettingModel;
use app\common\model\wxapp\Setting as WxappSettingModel;
use app\common\service\BaseService;
use app\common\library\Download;
use app\common\library\wechat\Qrcode as WechatQrcode;
use app\common\enum\Client as ClientEnum;
use cores\exception\BaseException;

/**
 * 二维码服务基类
 * Class Base
 * @package app\common\service\qrcode
 */
class BaseQRcode extends BaseService
{
    /**
     * 保存微二维码到文件
     * @param int $storeId
     * @param string $scene 场景值 (例如: uid:10001)
     * @param string|null $page 页面地址
     * @param string $channel 二维码渠道(小程序码、h5码)
     * @return string
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getQrcode(int $storeId, string $scene, string $page = null, string $channel = 'H5'): string
    {
        // 根据指定渠道生成不同的二维码
        if ($channel === ClientEnum::MP_WEIXIN) {
            return $this->getMpWeiXinQrcode($storeId, $scene, $page, $channel);
        }
        return $this->getH5Qrcode($storeId, $scene, $page, $channel);
    }

    /**
     * 保存微二维码到文件 (微信小程序码)
     * @param int $storeId 商城ID
     * @param string $scene 场景值 (例如: uid:10001)
     * @param string|null $page 页面地址
     * @param string $channel 二维码渠道(小程序码、h5码)
     * @return string
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws BaseException
     */
    protected function getMpWeiXinQrcode(int $storeId, string $scene, string $page = null, string $channel = 'H5'): string
    {
        // 获取二维码路径
        $savePath = $this->getFilePath($storeId, $scene, $page, $channel);
        if (file_exists($savePath)) {
            return $savePath;
        }
        // 小程序配置信息
        $wxConfig = WxappSettingModel::getConfigBasic($storeId);
        // 请求api获取小程序码
        $Qrcode = new WechatQrcode($wxConfig['app_id'], $wxConfig['app_secret']);
        $content = $Qrcode->getQrcode($scene, $page);
        // 保存到文件
        file_put_contents($savePath, $content);
        return $savePath;
    }

    /**
     * 保存微二维码到文件 (H5二维码)
     * @param int $storeId 商城ID
     * @param string $scene 参数值 (例如: uid:10001)
     * @param string|null $page 页面url
     * @param string $channel 二维码渠道(小程序码、h5码)
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected function getH5Qrcode(int $storeId, string $scene, string $page = null, string $channel = 'H5'): string
    {
        // 获取二维码路径
        $savePath = $this->getFilePath($storeId, $scene, $page, $channel);
        if (file_exists($savePath)) return $savePath;
        // 二维码参数
        $path = $page ?: 'pages/index/index';
        $route = $path . (!empty($scene) ? '?scene=' . urlencode($scene) : null);
        // 获取H5端的访问域名
        $url = H5SettingModel::getH5Url($storeId) . "#/{$route}";
        // 生成二维码
        \PHPQRCode\QRcode::png($url, $savePath, 'L', 15, 1);
        return $savePath;
    }

    /**
     * 获取二维码路径
     * @param int $storeId
     * @param string $scene
     * @param string|null $page
     * @param string $channel 二维码渠道(小程序码、h5码)
     * @return string
     */
    private function getFilePath(int $storeId, string $scene, string $page = null, string $channel = 'H5'): string
    {
        // 文件目录
        $dirPath = runtime_root_path() . "image/{$storeId}";
        !is_dir($dirPath) && mkdir($dirPath, 0755, true);
        // 文件名称
        $fileName = 'qrcode_' . md5("{$storeId}{$scene}{$page}-{$channel}") . '.png';
        // 文件路径
        return "{$dirPath}/{$fileName}";
    }

    /**
     * 获取网络图片到临时目录
     * @param int $storeId 商城ID
     * @param string $url 图片链接
     * @param string $prefix
     * @return string
     * @throws BaseException
     */
    protected function saveTempImage(int $storeId, string $url, string $prefix = 'temp'): string
    {
        $Download = new Download;
        return $Download->saveTempImage($storeId, $url, $prefix);
    }
}