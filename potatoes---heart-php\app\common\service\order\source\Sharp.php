<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\service\order\source;

use cores\exception\BaseException;
use app\common\model\sharp\Goods as SharpGoodsModel;
use app\common\model\sharp\GoodsSku as SharpGoodsSkuModel;
use app\common\enum\goods\DeductStockType as DeductStockTypeEnum;

/**
 * 订单来源-秒杀订单
 * Class Sharp
 * @package app\common\service\order\source
 */
class Sharp extends Basics
{
    /**
     * 判断订单是否允许付款
     * @param $order
     * @return bool
     * @throws BaseException
     */
    public function checkOrderStatusOnPay($order): bool
    {
        // 判断订单状态
        if (!$this->checkOrderStatusOnPayCommon($order)) {
            return false;
        }
        // 判断商品状态、库存
        if (!$this->checkGoodsStatusOnPay($order['goods'])) {
            return false;
        }
        return true;
    }

    /**
     * 判断订单是否允许取消
     * @param $order
     * @return bool
     */
    public function checkOrderByCancel($order): bool
    {
        // 判断订单是否允许取消
        if (!$this->checkOrderByCancelCommon($order)) {
            return false;
        }
        return true;
    }

    /**
     * 判断商品状态、库存 (未付款订单)
     * @param $goodsList
     * @return bool
     * @throws BaseException
     */
    private function checkGoodsStatusOnPay($goodsList): bool
    {
        foreach ($goodsList as $goods) {
            // 秒杀商品信息
            $sharpGoods = SharpGoodsModel::detail($goods['goods_source_id']);
            // 判断商品是否下架
            if (empty($sharpGoods) || $sharpGoods['is_delete'] || !$sharpGoods['status']) {
                $this->error = "很抱歉，商品 [{$goods['goods_name']}] 不存在或已下架";
                return false;
            }
            // 商品sku信息
            $sharpGoods['skuInfo'] = SharpGoodsSkuModel::getSkuInfo($sharpGoods['goods_id'], $sharpGoods['sharp_goods_id'], $goods['goods_sku_id']);
            // 付款减库存
            if ($goods['deduct_stock_type'] == DeductStockTypeEnum::PAYMENT && $goods['total_num'] > $sharpGoods['skuInfo']['seckill_stock']) {
                $this->error = "很抱歉，商品 [{$goods['goods_name']}] 库存不足";
                return false;
            }
        }
        return true;
    }

    /**
     * 判断订单是否允许发货
     * @param $order
     * @return bool
     */
    public function checkOrderByDelivery($order): bool
    {
        // 判断订单是否允许发货
        if (!$this->checkOrderByDeliveryCommon($order)) {
            return false;
        }
        return true;
    }
}