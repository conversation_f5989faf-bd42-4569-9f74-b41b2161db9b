<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\goods;

use app\common\enum\EnumBasics;

/**
 * 枚举类：商品批量导入状态
 * Class ImportStatus
 * @package app\common\enum\goods
 */
class ImportStatus extends EnumBasics
{
    // 进行中
    const NORMAL = 10;

    // 导入完成
    const COMPLETED = 20;

    // 导入失败
    const FAIL = 30;

    /**
     * 获取枚举类型值
     * @return array
     */
    public static function data(): array
    {
        return [
            self::NORMAL => [
                'name' => '进行中',
                'value' => self::NORMAL,
            ],
            self::COMPLETED => [
                'name' => '导入完成',
                'value' => self::COMPLETED,
            ],
            self::FAIL => [
                'name' => '导入失败',
                'value' => self::FAIL,
            ]
        ];
    }
}
