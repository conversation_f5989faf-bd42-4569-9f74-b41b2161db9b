import{_ as t}from"./u-tabs.a615f5ab.js";import{$ as a,p as e,q as s,o as i,c as l,w as o,n as r,i as c,a as n,b as d,d as m,F as u,x as h,k as p,f as _,t as f,e as g,l as y,g as b}from"./index-b4f0c008.js";import{r as L}from"./uni-app.es.7ced0c2d.js";import{M as w,_ as x}from"./mescroll-mixins.b5a2f915.js";import{W as T}from"./wxofficial.99fe166e.js";import{l as I}from"./index.ea1d23fc.js";import{_ as v}from"./_plugin-vue_export-helper.1b428a4d.js";const C="article.category/list";const S=v({mixins:[w,T],data:()=>({tabList:[],curTab:0,categoryId:0,articleList:e(),upOption:{auto:!0,page:{size:15},noMoreSize:3}}),onLoad(t){const a=this;a.categoryId=t.categoryId||0,a.getCategoryList(),a.setWxofficialShareData()},methods:{upCallback(t){const a=this;a.getArticleList(t.num).then((t=>{const e=t.data.length,s=t.data.total;a.mescroll.endBySize(e,s)})).catch((()=>a.mescroll.endErr()))},getCategoryList(){a.get(C).then((t=>{this.setTabList(t.data.list)}))},setTabList(t){const a=this;if(a.tabList=[{value:0,name:"全部"}],t.forEach((t=>{a.tabList.push({value:t.category_id,name:t.name})})),a.categoryId>0){const t=a.tabList.findIndex((t=>t.value==a.categoryId));a.curTab=t>-1?t:0}},getArticleList(t=1){const a=this;return new Promise(((e,i)=>{I({categoryId:a.categoryId,page:t},{load:!1}).then((i=>{const l=i.data.list;a.articleList.data=s(l,a.articleList,t),e(l)})).catch(i)}))},onChangeTab(t){this.curTab=t,this.categoryId=this.tabList[t].value,this.onRefreshList()},onRefreshList(){this.articleList=e(),setTimeout((()=>this.mescroll.resetUpScroll()),120)},onTargetDetail(t){this.$navTo("pages/article/detail",{articleId:t})},setWxofficialShareData(){this.updateShareCardData({title:"文章首页"})}},onShareAppMessage(){return{title:"文章首页",path:"/pages/article/index?"+this.$getShareUrlParams()}},onShareTimeline(){return{title:"文章首页",path:"/pages/article/index?"+this.$getShareUrlParams()}}},[["render",function(a,e,s,w,T,I){const v=L(h("u-tabs"),t),C=y,S=c,k=b,j=L(h("mescroll-body"),x);return i(),l(S,{class:"container",style:r(a.appThemeStyle)},{default:o((()=>[n(j,{ref:"mescrollRef",sticky:!0,onInit:a.mescrollInit,down:{use:!1},up:T.upOption,onUp:I.upCallback},{default:o((()=>[n(v,{list:T.tabList,"is-scroll":!0,modelValue:T.curTab,"onUpdate:modelValue":e[0]||(e[0]=t=>T.curTab=t),"active-color":a.appTheme.mainBg,duration:.2,onChange:I.onChangeTab},null,8,["list","modelValue","active-color","duration","onChange"]),n(S,{class:"article-list"},{default:o((()=>[(i(!0),d(u,null,m(T.articleList.data,((t,a)=>(i(),l(S,{class:p(["article-item",[`show-type__${t.show_type}`]]),key:a,onClick:a=>I.onTargetDetail(t.article_id)},{default:o((()=>[10==t.show_type?(i(),d(u,{key:0},[n(S,{class:"article-item__left flex-box"},{default:o((()=>[n(S,{class:"article-item__title"},{default:o((()=>[n(C,{class:"twoline-hide"},{default:o((()=>[_(f(t.title),1)])),_:2},1024)])),_:2},1024),n(S,{class:"article-item__footer m-top10"},{default:o((()=>[n(C,{class:"article-views f-24 col-8"},{default:o((()=>[_(f(t.show_views)+"次浏览",1)])),_:2},1024)])),_:2},1024)])),_:2},1024),n(S,{class:"article-item__image"},{default:o((()=>[n(k,{class:"image",mode:"widthFix",src:t.image_url},null,8,["src"])])),_:2},1024)],64)):g("",!0),20==t.show_type?(i(),d(u,{key:1},[n(S,{class:"article-item__title"},{default:o((()=>[n(C,{class:"twoline-hide"},{default:o((()=>[_(f(t.title),1)])),_:2},1024)])),_:2},1024),n(S,{class:"article-item__image m-top20"},{default:o((()=>[n(k,{class:"image",mode:"widthFix",src:t.image_url},null,8,["src"])])),_:2},1024),n(S,{class:"article-item__footer m-top10"},{default:o((()=>[n(C,{class:"article-views f-24 col-8"},{default:o((()=>[_(f(t.show_views)+"次浏览",1)])),_:2},1024)])),_:2},1024)],64)):g("",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1},8,["onInit","up","onUp"])])),_:1},8,["style"])}],["__scopeId","data-v-13555183"]]);export{S as default};
