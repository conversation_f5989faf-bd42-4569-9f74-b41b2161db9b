(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["order"],{"0447":function(e,t,a){},"0794":function(e,t,a){},"0d4b":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"container"},[t("a-card",{staticClass:"mb-20",attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-alert",{staticClass:"mb-20",attrs:{showIcon:!0,message:"功能说明：用于订单数据导出，导出后的文件请用Excel打开查看",banner:""}}),t("a-form",{staticClass:"my-form",attrs:{form:e.myForm,layout:"inline"},on:{submit:e.handleSubmit}},[t("a-row",{attrs:{gutter:24}},[t("a-col",{attrs:{span:12}},[t("a-form-item",{attrs:{label:"下单时间"}},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime",{rules:[{required:!0,message:"请选择下单时间"}]}],expression:"['betweenTime', { rules: [{ required: true, message: '请选择下单时间' }] }]"}],attrs:{format:"YYYY-MM-DD"}})],1)],1),t("a-col",{attrs:{span:12}},[t("a-form-item",{attrs:{label:"订单来源"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["orderSource",{initialValue:-1}],expression:"['orderSource', { initialValue: -1 }]"}]},[t("a-select-option",{attrs:{value:-1}},[e._v("全部")]),e._l(e.OrderSourceEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])}))],2)],1)],1),t("a-col",{attrs:{span:12}},[t("a-form-item",{attrs:{label:"支付方式"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["payMethod",{initialValue:""}],expression:"['payMethod', { initialValue: '' }]"}]},[t("a-select-option",{attrs:{value:""}},[e._v("全部")]),e._l(e.PaymentMethodEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])}))],2)],1)],1),t("a-col",{attrs:{span:12}},[t("a-form-item",{attrs:{label:"配送方式"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["deliveryType",{initialValue:-1}],expression:"['deliveryType', { initialValue: -1 }]"}]},[t("a-select-option",{attrs:{value:-1}},[e._v("全部")]),e._l(e.DeliveryTypeEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])}))],2)],1)],1),t("a-col",{attrs:{span:24}},[t("a-form-item",{staticClass:"item-main",attrs:{label:"导出的字段"}},[t("a-checkbox-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["columns",{initialValue:e.columnValue,rules:[{required:!0,message:"请选择导出的字段"}]}],expression:"['columns', { initialValue: columnValue, rules: [{ required: true, message: '请选择导出的字段' }] }]"}],attrs:{options:e.columnData}})],1)],1),t("a-col",{attrs:{span:24}},[t("a-form-item",{staticClass:"form-btn"},[t("a-button",{staticClass:"btn-main",attrs:{type:"primary",icon:"download","html-type":"submit",loading:e.isBtnLoading}},[e._v("导出")]),t("a-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1)],1)],1)],1),t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v("历史导出记录")]),t("ExportList",{ref:"ExportList"})],1)],1)},s=[],i=(a("d3b7"),a("8ded")),n=a.n(i),o=a("c1df"),d=a.n(o),l=a("ca00"),c=a("fa04"),u=a("b775"),m={exportOrder:"/order.export/exportOrder",list:"/order.export/list"};function p(e){return Object(u["b"])({url:m.exportOrder,method:"get",params:e})}function v(e){return Object(u["b"])({url:m.list,method:"get",params:e})}var f,h=a("4a95"),_=a("3c76"),b=function(){var e=this,t=e._self._c;return t("s-table",{ref:"table",attrs:{rowKey:"id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:10},scopedSlots:e._u([{key:"time",fn:function(a){return[t("span",[e._v(e._s(a.start_time))]),t("span",{staticClass:"ml-5 mr-5"},[e._v("到")]),t("span",[e._v(e._s(a.end_time))])]}},{key:"status",fn:function(a){return[t("a-tag",{attrs:{color:e.ExportStatusColorEnum[a]}},[e._v(e._s(e.ExportStatusEnum[a].name))])]}},{key:"action",fn:function(a,r){return t("span",{staticClass:"actions"},[r.status===e.ExportStatusEnum.COMPLETED.value?t("a",{directives:[{name:"action",rawName:"v-action:download",arg:"download"}],attrs:{href:r.download_url,target:"_blank"}},[e._v("下载")]):e._e()])}}])})},g=[],y=a("ade3"),E=a("2af9"),C=a("5c06"),L=new C["a"]([{key:"NORMAL",name:"进行中",value:10},{key:"COMPLETED",name:"已完成",value:20},{key:"FAIL",name:"失败",value:30}]),S=(f={},Object(y["a"])(f,L.NORMAL.value,""),Object(y["a"])(f,L.COMPLETED.value,"green"),Object(y["a"])(f,L.FAIL.value,"red"),f),w={name:"ExportList",components:{STable:E["d"]},data:function(){return{isLoading:!1,ExportStatusEnum:L,ExportStatusColorEnum:S,columns:[{title:"记录ID",dataIndex:"id"},{title:"下单时间",scopedSlots:{customRender:"time"}},{title:"导出时间",dataIndex:"create_time"},{title:"导出状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"操作",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(e){return v(e).then((function(e){return e.data.list}))}}},methods:{handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)}}},x=w,D=a("2877"),R=Object(D["a"])(x,b,g,!1,null,"7cc988ee",null),O=R.exports,k=Object(c["c"])([{label:"订单ID",value:"order_id"},{label:"订单号",value:"order_no"},{label:"商品信息",value:"goods_detail"},{label:"商品总额",value:"total_price"},{label:"优惠券抵扣金额",value:"coupon_money",moduleKey:"market-coupon"},{label:"积分抵扣金额",value:"points_money",moduleKey:"market-points"},{label:"后台改价",value:"update_price",moduleKey:"order-updatePrice"},{label:"运费金额",value:"express_price"},{label:"订单实付款",value:"pay_price"},{label:"支付方式",value:"pay_method"},{label:"下单时间",value:"create_time"},{label:"买家信息",value:"user_info"},{label:"买家留言",value:"buyer_remark"},{label:"配送方式",value:"delivery_type"},{label:"收货人",value:"receipt_name"},{label:"联系电话",value:"receipt_phone"},{label:"收货地址",value:"receipt_address"},{label:"物流公司",value:"express_company"},{label:"物流单号",value:"express_no"},{label:"自提门店名称",value:"extract_shop_name"},{label:"自提联系人",value:"extract_linkman"},{label:"自提联系电话",value:"extract_phone"},{label:"付款状态",value:"pay_status"},{label:"付款时间",value:"pay_time"},{label:"发货状态",value:"delivery_status"},{label:"发货时间",value:"delivery_time"},{label:"收货状态",value:"receipt_status"},{label:"收货时间",value:"receipt_time"},{label:"订单状态",value:"order_status"},{label:"是否已评价",value:"is_comment"},{label:"订单来源",value:"order_source"}]),I=["order_no","goods_detail","total_price","coupon_money","points_money","update_price","express_price","pay_price","pay_method","create_time","user_info","buyer_remark","delivery_type","receipt_name","receipt_phone","receipt_address","express_company","express_no","pay_status","pay_time","delivery_status","delivery_time","receipt_status","receipt_time","order_status","order_source"],P="order_export_form",T={name:"Export",components:{ExportList:O},data:function(){return{myForm:this.$form.createForm(this),isLoading:!1,isBtnLoading:!1,labelCol:{span:8},wrapperCol:{span:16},columnData:k,columnValue:I}},created:function(){this.setFieldsValue()},beforeCreate:function(){Object(l["a"])(this,{inArray:l["e"],DeliveryTypeEnum:h["b"],OrderSourceEnum:h["c"],OrderStatusEnum:h["d"],PaymentMethodEnum:_["a"]})},methods:{setFieldsValue:function(){var e=n.a.get(P);if(!Object(l["f"])(e)){e.betweenTime=[d()(e.betweenTime[0]),d()(e.betweenTime[1])];var t=this.myForm.setFieldsValue;this.$nextTick((function(){return t(e)}))}},handleSubmit:function(e){var t=this;e.preventDefault(),this.myForm.validateFields((function(e,a){e||(t.onSave2Local(a),t.onFormSubmit(a))}))},onSave2Local:function(e){n.a.set(P,e,1296e6)},onFormSubmit:function(e){var t=this;t.isLoading=!0,t.isBtnLoading=!0,p(e).then((function(e){t.$message.success(e.message,1.5),t.$refs.ExportList.handleRefresh()})).finally((function(e){t.isLoading=!1,t.isBtnLoading=!1}))},handleReset:function(){this.myForm.resetFields(),n.a.remove(P)}}},F=T,A=(a("78bd"),Object(D["a"])(F,r,s,!1,null,"e4cde5de",null));t["default"]=A.exports},"0d80":function(e,t,a){},"1d6e":function(e,t,a){"use strict";a("4b9e")},"1da1":function(e,t,a){"use strict";a.d(t,"a",(function(){return s}));a("d3b7");function r(e,t,a,r,s,i,n){try{var o=e[i](n),d=o.value}catch(l){return void a(l)}o.done?t(d):Promise.resolve(d).then(r,s)}function s(e){return function(){var t=this,a=arguments;return new Promise((function(s,i){var n=e.apply(t,a);function o(e){r(n,s,i,o,d,"next",e)}function d(e){r(n,s,i,o,d,"throw",e)}o(void 0)}))}}},2264:function(e,t,a){"use strict";a.d(t,"a",(function(){return k})),a.d(t,"b",(function(){return N}));a("b0c0");var r,s,i=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:680,visible:e.visible,isLoading:e.isLoading,confirmLoading:e.isLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-alert",{staticClass:"mb-30",attrs:{showIcon:!0,message:"功能说明",banner:""}},[t("template",{slot:"description"},[t("p",[e._v("1. 用于打印发货物流的电子面单，无需人工录入单号，提升效率；")]),t("p",[e._v("2. 获取电子面单成功后，将自动录入物流单号并更新订单状态为已发货；")])])],2),t("a-form-model",{ref:"ruleForm",attrs:{model:e.formData,rules:e.rules,"label-col":e.labelCol,wrapperCol:e.wrapperCol}},[t("a-tabs",{model:{value:e.tabKey,callback:function(t){e.tabKey=t},expression:"tabKey"}},[t("a-tab-pane",{key:1,attrs:{tab:"未发货的商品"}},[t("a-table",{attrs:{rowKey:"order_goods_id",columns:e.columns1,dataSource:e.notDeliveredList,"row-selection":e.rowSelection,pagination:!1,scroll:{y:"320px",scrollToFirstRowOnChange:!0}},scopedSlots:e._u([{key:"goods",fn:function(e){return[t("GoodsItem",{attrs:{data:{image:e.goods_image,imageAlt:"商品图片",title:e.goods_name,goodsProps:e.goods_props,titleWidth:170},subTitleColor:!0}})]}},{key:"quantity",fn:function(a,r){return t("span",{},[e._v(e._s(r.total_num-r.delivery_num))])}},{key:"input",fn:function(a,r,s){return[t("a-input-number",{attrs:{min:1,max:r.total_num-r.delivery_num,precision:0},model:{value:e.packGoodsData[s].deliveryNum,callback:function(t){e.$set(e.packGoodsData[s],"deliveryNum",t)},expression:"packGoodsData[index].deliveryNum"}})]}}])}),t("a-form-model-item",{attrs:{label:"电子面单模板",prop:"templateId"}},[t("a-select",{attrs:{placeholder:"请选择电子面单模板"},model:{value:e.formData.templateId,callback:function(t){e.$set(e.formData,"templateId",t)},expression:"formData.templateId"}},e._l(e.templateList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.template_id}},[e._v(e._s(a.name))])})),1),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{to:{path:"/apps/eorder/template/index"}}},[e._v("模板管理")])],1)],1),t("a-form-model-item",{attrs:{label:"发货地址",prop:"addressId"}},[t("a-select",{attrs:{placeholder:"请选择发货地址"},model:{value:e.formData.addressId,callback:function(t){e.$set(e.formData,"addressId",t)},expression:"formData.addressId"}},e._l(e.addressList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.address_id}},[e._v(e._s(a.full_address))])})),1),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{to:{path:"/store/address/index"}}},[e._v("地址管理")])],1)],1)],1),t("a-tab-pane",{key:2,attrs:{tab:"已发货的商品"}},[t("a-table",{attrs:{columns:e.columns2,rowKey:"order_goods_id",dataSource:e.deliveredList,pagination:!1,scroll:{y:"320px",scrollToFirstRowOnChange:!0}},scopedSlots:e._u([{key:"goods",fn:function(e){return[t("GoodsItem",{attrs:{data:{image:e.goods_image,imageAlt:"商品图片",title:e.goods_name,goodsProps:e.goods_props,titleWidth:170},subTitleColor:!0}})]}},{key:"total_num",fn:function(a){return t("span",{},[e._v(e._s(a))])}},{key:"quantity",fn:function(a,r){return t("span",{},[e._v(e._s(r.delivery_num))])}}])})],1)],1)],1)],1)],1)},n=[],o=a("c7eb"),d=a("1da1"),l=(a("d3b7"),a("159b"),a("d81d"),a("4de4"),a("caad"),a("2532"),a("86d6")),c=a("632d"),u=a("4d55"),m=a("4a95"),p=a("6245"),v=a("ab09"),f=(a("cd17"),a("ed3b")),h=(a("3b18"),a("f64c"));a("ac1f"),a("466d");function _(){try{var e=navigator.userAgent;if(e.match(/Windows\sPhone/i))return!0;if(e.match(/iPhone|iPod|iPad/i))return!0;if(e.match(/Android/i))return!0;if(e.match(/Edge\D?\d+/i))return!0;var t=e.match(/Trident\D?\d+/i),a=e.match(/MSIE\D?\d+/i),r=e.match(/OPR\D?\d+/i),s=e.match(/Firefox\D?\d+/i),i=e.match(/x64/i);if(!t&&!a&&i)return!0;if(s){if(s=s[0].match(/\d+/),s[0]>=41||i)return!0}else if(r){if(r=r[0].match(/\d+/),r[0]>=32)return!0}else if(!t&&!a){var n=e.match(/Chrome\D?\d+/i);if(n&&(n=n[0].match(/\d+/),n[0]>=41))return!0}return!1}catch(o){return!0}}function b(){if("loading"!=s&&"complete"!=s){s="loading";var e=document.head||document.getElementsByTagName("head")[0]||document.documentElement,t=document.createElement("script"),a=document.createElement("script");"https:"==window.location.protocol?(t.src="https://localhost.lodop.net:8443/CLodopfuncs.js",a.src="https://localhost.lodop.net:8444/CLodopfuncs.js"):(t.src="http://localhost:8000/CLodopfuncs.js",a.src="http://localhost:18000/CLodopfuncs.js"),t.onload=a.onload=function(){s="complete"},t.onerror=a.onerror=function(e){s="complete"},e.insertBefore(t,e.firstChild),e.insertBefore(a,e.firstChild),!!(t.src+a.src).match(/\/\/localho|\/\/127.0.0./i)}}function g(e,t){var a,i="打印控件CLodop未安装启动，请先下载并执行安装",n="，完成后请刷新或重启浏览器。";try{var o=navigator.userAgent,d=!!o.match(/MSIE/i)||!!o.match(/Trident/i);if(_()){try{a=getCLodop()}catch(l){}if(!a&&"complete"!==s)return y("loading"==s?"网页还没加载完毕，请稍等一下再操作":"没有加载CLodop的主js，请先调用loadCLodop过程"),!1;if(!a)return C(i+n),!1;CLODOP.CVERSION,t&&t.parentNode&&t.parentNode.removeChild(t),e&&e.parentNode&&e.parentNode.removeChild(e)}else e||t?a=d?e:t:r||(a=document.createElement("object"),a.setAttribute("width",0),a.setAttribute("height",0),a.setAttribute("style","position:absolute;left:0px;top:-100px;width:0px;height:0px;"),d?a.setAttribute("classid","clsid:2105C259-1E0C-4534-8141-A753534CB4CA"):a.setAttribute("type","application/x-print-lodop"),document.documentElement.appendChild(a),r=a);return a.VERSION<"6.2.2.6"&&_(),a.SET_LICENSES("","13528A153BAEE3A0254B9507DCDE2839","EDE92F75B6A3D917F65910","D60BC84D7CF2DE18156A6F88987304CB6D8"),a}catch(l){E("getLodop出错:"+l)}}function y(e){h["a"].warning(e,1.5)}function E(e){f["a"].error({title:"打印控件调用错误",content:e})}function C(e){f["a"].confirm({title:"友情提示",content:e,okText:"前往下载",onOk:function(){window.open("http://www.kdniao.com/documents-instrument")}})}_()&&b();var L=[{title:"商品信息",scopedSlots:{customRender:"goods"},width:"50%"},{title:"可发货的数量",scopedSlots:{customRender:"quantity"},width:"20%"},{title:"发货数量",scopedSlots:{customRender:"input"},width:"30%"}],S=[{title:"商品信息",scopedSlots:{customRender:"goods"},width:"50%"},{title:"购买数量",dataIndex:"total_num",width:"20%"},{title:"已发货的数量",scopedSlots:{customRender:"quantity"},width:"20%"}],w={templateId:[{required:!0,message:"请选择电子面单模板",trigger:"blur"}],addressId:[{required:!0,message:"请选择发货地址",trigger:"blur"}]},x={components:{GoodsItem:v["a"]},data:function(){return{title:"打印电子面单",labelCol:{span:7},wrapperCol:{span:13},visible:!1,isLoading:!1,columns1:L,columns2:S,rules:w,tabKey:1,LODOP:void 0,formData:{packGoodsData:[],templateId:void 0,addressId:void 0},addressList:[],templateList:[],record:{},packGoodsData:[],selectedRowKeys:[]}},computed:{rowSelection:function(){var e=this;return{onChange:function(t,a){e.selectedRowKeys=t},selectedRowKeys:this.selectedRowKeys}},notDeliveredList:function(){var e=this.record;if(e&&e.goods){var t=[];return e.goods.forEach((function(e){e.delivery_status!=m["a"].DELIVERED.value&&t.push(e)})),this.selectedRowKeys=t.map((function(e){return e.order_goods_id})),t}},deliveredList:function(){var e=this.record;if(e&&e.goods){var t=[];return e.goods.forEach((function(e){e.delivery_status!=m["a"].NOT_DELIVERED.value&&t.push(e)})),t}}},created:function(){var e=this;return Object(d["a"])(Object(o["a"])().mark((function t(){return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.getAddressList();case 3:return t.next=5,e.getTemplateList();case 5:e.isLoading=!1;case 6:case"end":return t.stop()}}),t)})))()},methods:{handle:function(e){g()&&(this.visible=!0,this.record=e,this.initPackGoodsData())},initPackGoodsData:function(){var e=this.notDeliveredList;e&&e.length&&(this.packGoodsData=e.map((function(e){return{orderGoodsId:e.order_goods_id,deliveryNum:e.total_num-e.delivery_num}})))},getAddressList:function(){var e=this;return Object(d["a"])(Object(o["a"])().mark((function t(){return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,u["b"]({type:p["a"].DELIVERY.value}).then((function(t){return e.addressList=t.data.list}));case 2:case"end":return t.stop()}}),t)})))()},getTemplateList:function(){var e=this;return Object(d["a"])(Object(o["a"])().mark((function t(){return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,c["b"]().then((function(t){return e.templateList=t.data.list}));case 2:case"end":return t.stop()}}),t)})))()},handleSubmit:function(e){var t=this;if(e.preventDefault(),this.tabKey=1,!this.selectedRowKeys.length)return this.$message.error("您还没有选择要发货的商品"),!1;this.formData.packGoodsData=this.packGoodsData.filter((function(e){return t.selectedRowKeys.includes(e.orderGoodsId)})),this.$refs.ruleForm.validate((function(e){e&&t.onFormSubmit()}))},handleCancel:function(){this.visible=!1,this.$refs.ruleForm.resetFields()},onFormSubmit:function(){var e=this;this.isLoading=!0,l["d"]({orderId:this.record.order_id,form:this.formData}).then((function(t){e.$message.success(t.message,1.5),e.handleCancel(),e.printEOrder(t.data.template),e.$emit("handleSubmit",!0)})).finally((function(){return e.isLoading=!1}))},printEOrder:function(e){LODOP.PRINT_INIT(),LODOP.SET_PRINT_PAGESIZE(0),LODOP.NewPageA(),LODOP.ADD_PRINT_HTML(0,0,"100%","100%",e),LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREWINDOW",!0),LODOP.SET_PRINT_MODE("CATCH_PRINT_STATUS",!0),LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT","Auto-Width"),LODOP.SET_SHOW_MODE("HIDE_PAGE_PERCENT",!0),LODOP.PREVIEW()}}},D=x,R=(a("f1bf"),a("2877")),O=Object(R["a"])(D,i,n,!1,null,"fa86088e",null),k=O.exports,I=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:520,visible:e.visible,isLoading:e.isLoading,confirmLoading:e.isLoading,maskClosable:!1,okText:"打印"},on:{ok:e.handleOK,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.isLoading}},[t("div",{staticClass:"eorder-content",domProps:{innerHTML:e._s(e.eorderContent)}},[e._v("s")])])],1)},P=[],T={components:{},data:function(){return{title:"查看电子面单",labelCol:{span:7},wrapperCol:{span:13},visible:!1,isLoading:!1,LODOP:void 0,record:{},eorderContent:""}},methods:{handle:function(e){this.visible=!0,this.record=e,this.getDetail()},getDetail:function(){var e=this;this.isLoading=!0,l["c"](this.record.delivery_id).then((function(t){return e.eorderContent=t.data.detail.eorder_html})).finally((function(){return e.isLoading=!1}))},handleOK:function(e){this.eorderContent?g()&&this.printEOrder(this.eorderContent):this.$message.error("很抱歉，没有电子面单内容",1.2)},handleCancel:function(){this.visible=!1,this.eorderContent=""},printEOrder:function(e){LODOP.PRINT_INIT(),LODOP.SET_PRINT_PAGESIZE(0),LODOP.NewPageA(),LODOP.ADD_PRINT_HTML(0,0,"100%","100%",e),LODOP.SET_PRINT_MODE("AUTO_CLOSE_PREWINDOW",!0),LODOP.SET_PRINT_MODE("CATCH_PRINT_STATUS",!0),LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT","Auto-Width"),LODOP.SET_SHOW_MODE("HIDE_PAGE_PERCENT",!0),LODOP.PREVIEW()}}},F=T,A=(a("42c6"),Object(R["a"])(F,I,P,!1,null,"3add4df0",null)),N=A.exports},"2a66":function(e,t,a){"use strict";a.d(t,"e",(function(){return i})),a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"d",(function(){return d})),a.d(t,"c",(function(){return l}));var r=a("b775"),s={list:"/setting.express/list",all:"/setting.express/all",add:"/setting.express/add",edit:"/setting.express/edit",delete:"/setting.express/delete"};function i(e){return Object(r["b"])({url:s.list,method:"get",params:e})}function n(e){return Object(r["b"])({url:s.all,method:"get",params:e})}function o(e){return Object(r["b"])({url:s.add,method:"post",data:e})}function d(e){return Object(r["b"])({url:s.edit,method:"post",data:e})}function l(e){return Object(r["b"])({url:s.delete,method:"post",data:e})}},"34b5":function(e,t,a){},"3a10":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"a",(function(){return n}));var r=a("b775"),s={list:"/order/list",detail:"/order/detail"};function i(e){return Object(r["b"])({url:s.list,method:"get",params:e})}function n(e){return Object(r["b"])({url:s.detail,method:"get",params:e})}},"3c07":function(e,t,a){"use strict";a("8e58")},"3c76":function(e,t,a){"use strict";a.d(t,"a",(function(){return s}));var r=a("5c06"),s=new r["a"]([{key:"WECHAT",name:"微信支付",value:"wechat"},{key:"ALIPAY",name:"支付宝",value:"alipay"},{key:"BALANCE",name:"余额支付",value:"balance"}])},"42c6":function(e,t,a){"use strict";a("0447")},"4362a":function(e,t,a){},4669:function(e,t,a){},"4a95":function(e,t,a){"use strict";a.d(t,"a",(function(){return s})),a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"d",(function(){return o})),a.d(t,"f",(function(){return d})),a.d(t,"g",(function(){return l})),a.d(t,"e",(function(){return c}));var r=a("5c06"),s=new r["a"]([{key:"NOT_DELIVERED",name:"未发货",value:10},{key:"DELIVERED",name:"已发货",value:20},{key:"PART_DELIVERED",name:"部分发货",value:30}]),i=new r["a"]([{key:"EXPRESS",name:"快递配送",value:10},{key:"EXTRACT",name:"上门自提",value:20},{key:"NOTHING",name:"无需配送",value:30}]),n=new r["a"]([{key:"MASTER",name:"普通订单",value:10},{key:"BARGAIN",name:"砍价订单",value:20},{key:"SHARP",name:"秒杀订单",value:30},{key:"GROUPON",name:"拼团订单",value:40}]),o=new r["a"]([{key:"NORMAL",name:"进行中",value:10},{key:"CANCELLED",name:"已取消",value:20},{key:"APPLY_CANCEL",name:"待取消",value:21},{key:"COMPLETED",name:"已完成",value:30}]),d=new r["a"]([{key:"PENDING",name:"待支付",value:10},{key:"SUCCESS",name:"已支付",value:20}]),l=new r["a"]([{key:"NOT_RECEIVED",name:"未收货",value:10},{key:"RECEIVED",name:"已收货",value:20}]),c=new r["a"]([{key:"PHYSICAL",name:"实物订单",value:10},{key:"VIRTUAL",name:"虚拟订单",value:20}])},"4b9e":function(e,t,a){},"4c24":function(e,t,a){},"4d55":function(e,t,a){"use strict";a.d(t,"e",(function(){return i})),a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"d",(function(){return d})),a.d(t,"c",(function(){return l}));var r=a("b775"),s={list:"/store.address/list",all:"/store.address/all",add:"/store.address/add",edit:"/store.address/edit",delete:"/store.address/delete"};function i(e){return Object(r["b"])({url:s.list,method:"get",params:e})}function n(e){return Object(r["b"])({url:s.all,method:"get",params:e})}function o(e){return Object(r["b"])({url:s.add,method:"post",data:e})}function d(e){return Object(r["b"])({url:s.edit,method:"post",data:e})}function l(e){return Object(r["b"])({url:s.delete,method:"post",data:e})}},"51bb":function(e,t,a){"use strict";a("0794")},"56db":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("div",[t("a-spin",{attrs:{spinning:e.isLoading}}),e.isLoading?e._e():t("div",{staticClass:"detail-content"},[t("a-card",{attrs:{bordered:!1}},[e.record.status==e.RefundStatusEnum.NORMAL.value?t("div",{staticClass:"detail-actions"},[t("div",{staticClass:"ant-descriptions-title"},[e._v("售后单操作")]),t("div",{staticClass:"alerts mt-10 mb-15"},[e.record.audit_status==e.AuditStatusEnum.WAIT.value?t("a-alert",{attrs:{message:"当前买家已发起售后申请，请及时审核处理",banner:""}}):e._e()],1),t("div",{staticClass:"actions mt-10"},[e.$auth("/order/refund/index.audit")?t("div",[e.record.audit_status==e.AuditStatusEnum.WAIT.value?t("a-button",{attrs:{type:"primary"},on:{click:e.handleAudit}},[e._v("商家审核")]):e._e()],1):e._e(),e.$auth("/order/refund/index.receipt")?t("div",[e.record.type==e.RefundTypeEnum.RETURN.value&&e.record.audit_status==e.AuditStatusEnum.REVIEWED.value&&e.record.is_user_send&&!e.record.is_receipt?t("a-button",{attrs:{type:"primary"},on:{click:e.handleReceipt}},[e._v("确认收货")]):e._e()],1):e._e()]),t("a-divider",{staticClass:"o-divider"})],1):e._e(),t("a-descriptions",{attrs:{title:"售后单信息"}},[t("a-descriptions-item",{attrs:{label:"订单号"}},[t("router-link",{attrs:{title:"查看订单详情",to:{path:"/order/detail",query:{orderId:e.record.order_id}},target:"_blank"}},[e._v(e._s(e.record.orderData.order_no))])],1),t("a-descriptions-item",{attrs:{label:"买家信息"}},[t("a-tooltip",[t("template",{slot:"title"},[e._v("会员ID: "+e._s(e.record.user.user_id))]),t("span",{staticClass:"c-p"},[e._v(e._s(e.record.user.nick_name))])],2)],1),t("a-descriptions-item",{attrs:{label:"订单支付总额"}},[t("span",{staticClass:"c-p"},[t("span",[e._v("￥")]),t("span",[e._v(e._s(e.record.orderData.pay_price))])])]),t("a-descriptions-item",{attrs:{label:"售后类型"}},[t("a-tag",[e._v(e._s(e.RefundTypeEnum[e.record.type].name))])],1),t("a-descriptions-item",{attrs:{label:"售后单状态"}},[t("a-tag",{attrs:{color:e.renderRefundStatusColor(e.record.status)}},[e._v(e._s(e.RefundStatusEnum[e.record.status].name))])],1),t("a-descriptions-item",{attrs:{label:"申请时间"}},[e._v(e._s(e.record.create_time))])],1),t("a-divider",{staticClass:"o-divider"}),t("a-descriptions",{attrs:{title:"处理进度"}},[t("a-descriptions-item",{attrs:{label:"审核状态 (商家)"}},[t("a-tag",{attrs:{color:e.renderAuditStatusColor(e.record.audit_status)}},[e._v(e._s(e.AuditStatusEnum[e.record.audit_status].name))])],1),e.record.type==e.RefundTypeEnum.RETURN.value?t("a-descriptions-item",{attrs:{label:"发货状态 (买家)"}},[t("a-tag",{attrs:{color:e.record.is_user_send?"green":""}},[e._v(e._s(e.record.is_user_send?"已发货":"待发货"))])],1):e._e(),e.record.type==e.RefundTypeEnum.RETURN.value?t("a-descriptions-item",{attrs:{label:"收货状态 (商家)"}},[t("a-tag",{attrs:{color:e.record.is_receipt?"green":""}},[e._v(e._s(e.record.is_receipt?"已收货":"待收货"))])],1):e._e(),e.record.audit_status==e.AuditStatusEnum.REJECTED.value?t("a-descriptions-item",{attrs:{label:"拒绝原因"}},[t("span",[e._v(e._s(e.record.refuse_desc))])]):e._e()],1)],1),t("a-card",{staticClass:"mt-20",attrs:{bordered:!1}},[t("a-descriptions",{attrs:{title:"买家申请原因"}},[t("a-descriptions-item",{attrs:{label:"售后描述"}},[e._v(e._s(e.record.apply_desc?e.record.apply_desc:"--"))])],1),e.record.images.length?[t("a-divider",{staticClass:"o-divider"}),t("a-descriptions",{attrs:{title:"申请凭证"}},[t("a-descriptions-item",[t("div",{staticClass:"image-list"},e._l(e.record.images,(function(e){return t("div",{key:e.image_id,staticClass:"file-item"},[t("a",{attrs:{href:e.image_url,target:"_blank"}},[t("div",{staticClass:"img-cover",style:{backgroundImage:"url('".concat(e.image_url,"')")}})])])})),0)])],1)]:e._e()],2),t("a-card",{staticClass:"mt-20",attrs:{bordered:!1}},[t("div",{staticClass:"ant-descriptions-title"},[e._v("售后商品")]),t("div",{staticClass:"goods-list"},[t("a-table",{attrs:{rowKey:"order_goods_id",columns:e.goodsColumns,dataSource:[e.record.orderGoods],pagination:!1},scopedSlots:e._u([{key:"goodsInfo",fn:function(e,a){return[t("GoodsItem",{attrs:{data:{image:a.goods_image,imageAlt:"商品图片",title:a.goods_name,goodsProps:a.goods_props}}})]}},{key:"goods_no",fn:function(a){return t("span",{},[e._v(e._s(a||"--"))])}},{key:"goods_price",fn:function(t){return[e._v("￥"+e._s(t))]}},{key:"total_num",fn:function(a){return t("span",{},[e._v("x"+e._s(a))])}},{key:"total_pay_price",fn:function(a){return t("span",{},[e._v("￥"+e._s(a))])}}],null,!1,113254456)})],1)]),e.record.audit_status==e.AuditStatusEnum.REVIEWED.value?t("a-card",{staticClass:"mt-20",attrs:{bordered:!1}},[t("a-descriptions",{attrs:{title:"商家退货地址"}},[t("a-descriptions-item",{attrs:{label:"收货人姓名"}},[e._v(e._s(e.record.address.name))]),t("a-descriptions-item",{attrs:{label:"联系电话"}},[e._v(e._s(e.record.address.phone))]),t("a-descriptions-item",{attrs:{label:"所在地区"}},e._l(e.record.address.region,(function(a,r){return t("span",{key:r,staticClass:"region mr-5"},[e._v(e._s(a))])})),0),t("a-descriptions-item",{attrs:{label:"详细地址"}},[e._v(e._s(e.record.address.detail))])],1)],1):e._e(),e.record.type==e.RefundTypeEnum.RETURN.value&&e.record.audit_status==e.AuditStatusEnum.REVIEWED.value&&e.record.is_user_send?t("a-card",{staticClass:"mt-20",attrs:{bordered:!1}},[t("a-descriptions",{attrs:{title:"退货物流信息"}},[t("a-descriptions-item",{attrs:{label:"物流公司"}},[e._v(e._s(e.record.express.express_name))]),t("a-descriptions-item",{attrs:{label:"物流单号"}},[e._v(e._s(e.record.express_no))]),t("a-descriptions-item",{attrs:{label:"发货状态"}},[t("a-tag",{attrs:{color:e.record.is_user_send?"green":""}},[e._v(e._s(e.record.is_user_send?"已发货":"待发货"))])],1),t("a-descriptions-item",{attrs:{label:"发货时间"}},[e._v(e._s(e.record.send_time))])],1)],1):e._e()],1),t("AuditForm",{ref:"AuditForm",on:{handleSubmit:e.handleRefresh}}),t("ReceiptForm",{ref:"ReceiptForm",on:{handleSubmit:e.handleRefresh}})],1)},s=[],i=a("ade3"),n=(a("d3b7"),a("ca00")),o=a("e585"),d=a("ab09"),l=a("6ab1"),c=a("b4a5"),u=[{title:"商品信息",scopedSlots:{customRender:"goodsInfo"}},{title:"商品编码",dataIndex:"goods_no",scopedSlots:{customRender:"goods_no"}},{title:"重量(Kg)",dataIndex:"goods_weight",scopedSlots:{customRender:"goods_weight"}},{title:"单价",dataIndex:"goods_price",scopedSlots:{customRender:"goods_price"}},{title:"购买数量",dataIndex:"total_num",scopedSlots:{customRender:"total_num"}},{title:"实际付款价",dataIndex:"total_pay_price",scopedSlots:{customRender:"total_pay_price"}}],m={name:"Index",components:{GoodsItem:d["a"],UserItem:d["c"],AuditForm:c["a"],ReceiptForm:c["b"]},data:function(){return{isLoading:!0,orderRefundId:null,record:{},goodsColumns:u,goodsList:[]}},beforeCreate:function(){Object(n["a"])(this,{AuditStatusEnum:l["a"],RefundStatusEnum:l["b"],RefundTypeEnum:l["c"]})},created:function(){this.orderRefundId=this.$route.query.orderRefundId,this.handleRefresh()},methods:{handleRefresh:function(){this.getDetail()},getDetail:function(){var e=this,t=this.orderRefundId;this.isLoading=!0,o["b"]({orderRefundId:t}).then((function(t){e.record=t.data.detail,e.goodsList=[e.record.orderGoods]})).finally((function(){e.isLoading=!1}))},renderAuditStatusColor:function(e){var t,a=this.AuditStatusEnum,r=(t={},Object(i["a"])(t,a.WAIT.value,""),Object(i["a"])(t,a.REVIEWED.value,"green"),Object(i["a"])(t,a.REJECTED.value,"red"),t);return r[e]},renderRefundStatusColor:function(e){var t,a=this.RefundStatusEnum,r=(t={},Object(i["a"])(t,a.NORMAL.value,""),Object(i["a"])(t,a.REJECTED.value,"red"),Object(i["a"])(t,a.COMPLETED.value,"green"),Object(i["a"])(t,a.CANCELLED.value,"red"),t);return r[e]},handleAudit:function(){var e=this.record;this.$refs.AuditForm.show(e)},handleReceipt:function(){var e=this.record;this.$refs.ReceiptForm.show(e)}}},p=m,v=(a("f0a9"),a("2877")),f=Object(v["a"])(p,r,s,!1,null,"30f2b0fc",null);t["default"]=f.exports},"5cd4":function(e,t,a){"use strict";a("4362a")},6245:function(e,t,a){"use strict";a.d(t,"a",(function(){return s}));var r=a("5c06"),s=new r["a"]([{key:"DELIVERY",name:"发货地址",value:10},{key:"RETURN",name:"退货地址",value:20}])},"632d":function(e,t,a){"use strict";a.d(t,"f",(function(){return i})),a.d(t,"b",(function(){return n})),a.d(t,"d",(function(){return o})),a.d(t,"a",(function(){return d})),a.d(t,"e",(function(){return l})),a.d(t,"c",(function(){return c}));var r=a("b775"),s={list:"/eorder.template/list",all:"/eorder.template/all",detail:"/eorder.template/detail",add:"/eorder.template/add",edit:"/eorder.template/edit",delete:"/eorder.template/delete"};function i(e){return Object(r["b"])({url:s.list,method:"get",params:e})}function n(e){return Object(r["b"])({url:s.all,method:"get",params:e})}function o(e,t){return Object(r["b"])({url:s.detail,method:"get",params:{templateId:e,params:t}})}function d(e){return Object(r["b"])({url:s.add,method:"post",data:e})}function l(e){return Object(r["b"])({url:s.edit,method:"post",data:e})}function c(e){return Object(r["b"])({url:s.delete,method:"post",data:e})}},"6ab1":function(e,t,a){"use strict";a.d(t,"a",(function(){return s})),a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return n}));var r=a("5c06"),s=new r["a"]([{key:"WAIT",name:"待审核",value:0},{key:"REVIEWED",name:"已同意",value:10},{key:"REJECTED",name:"已拒绝",value:20}]),i=new r["a"]([{key:"NORMAL",name:"进行中",value:0},{key:"REJECTED",name:"已拒绝",value:10},{key:"COMPLETED",name:"已完成",value:20},{key:"CANCELLED",name:"已取消",value:30}]),n=new r["a"]([{key:"RETURN",name:"退货退款",value:10},{key:"EXCHANGE",name:"换货",value:20}])},"6dd2":function(e,t,a){"use strict";a.d(t,"e",(function(){return i})),a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"d",(function(){return d})),a.d(t,"c",(function(){return l}));var r=a("b775"),s={list:"/shop.clerk/list",all:"/shop.clerk/all",add:"/shop.clerk/add",edit:"/shop.clerk/edit",delete:"/shop.clerk/delete"};function i(e){return Object(r["b"])({url:s.list,method:"get",params:e})}function n(e){return Object(r["b"])({url:s.all,method:"get",params:e})}function o(e){return Object(r["b"])({url:s.add,method:"post",data:e})}function d(e){return Object(r["b"])({url:s.edit,method:"post",data:e})}function l(e){return Object(r["b"])({url:s.delete,method:"post",data:e})}},"6e5c":function(e,t,a){"use strict";a("34b5")},"70f2":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("div",{staticClass:"upload-dragger"},[t("a-upload-dragger",{attrs:{accept:".xls, .xlsx",multiple:!1,fileList:e.fileList,showUploadList:!1,beforeUpload:e.beforeUpload,remove:e.handleRemove}},[t("p",{staticClass:"ant-upload-drag-icon"},[t("a-icon",{attrs:{type:"inbox"}})],1),e.fileList.length?t("div",[t("p",{staticClass:"ant-upload-text"},[t("span",[e._v(e._s(e.fileList[0].name))]),t("a",{staticClass:"ml-10",attrs:{href:"javascript:void(0);"},on:{click:function(t){return t.stopPropagation(),e.handleRemove(e.fileList[0])}}},[e._v("删除")])]),t("a-button",{staticClass:"mt-20",attrs:{type:"primary"},on:{click:function(t){return t.stopPropagation(),e.onFormSubmit.apply(null,arguments)}}},[e._v("立即导入")])],1):t("div",[t("p",{staticClass:"ant-upload-text"},[e._v("点击选择文件，或者将文件拖拽至此区域")]),t("p",{staticClass:"ant-upload-hint"},[e._v("仅支持 .xls, .xlsx 格式，限2M以内")])])])],1),t("div",{staticClass:"upload-rules"},[t("p",{staticClass:"title"},[e._v("导入规则")]),t("p",{staticClass:"text"},[e._v(" 1. 上传前请先按照导入模版格式填写信息； "),t("a",{attrs:{href:"static/template/batch-delivery.xlsx",target:"_blank"}},[e._v("下载模版文件")])]),t("p",{staticClass:"text"},[e._v("2. 导入的订单以订单号做唯一判断，重复数据将无法导入；")]),t("p",{staticClass:"text"},[e._v("3. 单次最多导入200条，超出部分将自动无法导入；")])])])],1)},s=[],i=(a("a434"),a("d3b7"),a("86d6")),n={data:function(){return{isLoading:!1,fileList:[],uploadSizeLimit:"2"}},created:function(){},methods:{beforeUpload:function(e){var t=e.size/1024/1024;return t>this.uploadSizeLimit?(this.$message.error("上传的文件大小不能超出".concat(this.uploadSizeLimit,"MB")),!1):(this.fileList=[e],!1)},handleRemove:function(e){var t=this.fileList,a=t.indexOf(e);a>-1&&t.splice(a,1)},onFormSubmit:function(){var e=this,t=this.fileList,a=new FormData;a.append("file",t[0]),this.isLoading=!0,i["a"](a).then((function(t){e.fileList=[],e.$message.success(t.message,1.5),setTimeout((function(){return e.$router.back()}),1200)})).finally((function(){return e.isLoading=!1}))}}},o=n,d=(a("1d6e"),a("2877")),l=Object(d["a"])(o,r,s,!1,null,"05a41ea3",null);t["default"]=l.exports},"73db":function(e,t,a){"use strict";a("4669")},"78bd":function(e,t,a){"use strict";a("0d80")},"7ebd":function(e,t,a){},"84d7":function(e,t,a){},"86d6":function(e,t,a){"use strict";a.d(t,"e",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"b",(function(){return o})),a.d(t,"a",(function(){return d})),a.d(t,"d",(function(){return l}));var r=a("b775"),s={list:"/order.delivery/list",detail:"/order.delivery/detail",delivery:"/order.delivery/delivery",batch:"/order.delivery/batch",eorder:"/order.delivery/eorder"};function i(e){return Object(r["b"])({url:s.list,method:"get",params:e})}function n(e){return Object(r["b"])({url:s.detail,method:"get",params:{deliveryId:e}})}function o(e){return Object(r["b"])({url:s.delivery,method:"post",data:e})}function d(e){return Object(r["b"])({url:s.batch,method:"post",data:e})}function l(e){return Object(r["b"])({url:s.eorder,method:"post",data:e})}},"884a":function(e,t,a){"use strict";a.d(t,"e",(function(){return i})),a.d(t,"f",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return d})),a.d(t,"c",(function(){return l})),a.d(t,"d",(function(){return c}));var r=a("b775"),s={updatePrice:"/order.event/updatePrice",updateRemark:"/order.event/updateRemark",confirmCancel:"/order.event/confirmCancel",delete:"/order.event/delete",extract:"/order.event/extract",printer:"/order.event/printer"};function i(e){return Object(r["b"])({url:s.updatePrice,method:"post",data:e})}function n(e){return Object(r["b"])({url:s.updateRemark,method:"post",data:e})}function o(e){return Object(r["b"])({url:s.confirmCancel,method:"post",data:e})}function d(e){return Object(r["b"])({url:s.delete,method:"post",data:{orderId:e}})}function l(e){return Object(r["b"])({url:s.extract,method:"post",data:e})}function c(e){return Object(r["b"])({url:s.printer,method:"post",data:e})}},"8e58":function(e,t,a){},a25d:function(e,t,a){},aa42:function(e,t,a){"use strict";a("84d7")},ab06:function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("div",[t("a-spin",{attrs:{spinning:e.isLoading}}),e.isLoading?e._e():t("div",{staticClass:"order-content"},[t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"order-progress",class:"progress-".concat(e.progress)},[t("ul",[t("li",[t("span",[e._v("下单时间")]),t("div",{staticClass:"tip"},[e._v(e._s(e.record.create_time))])]),t("li",[t("span",[e._v("付款")]),e.record.pay_status==e.PayStatusEnum.SUCCESS.value?t("div",{staticClass:"tip"},[e._v("付款于 "+e._s(e.record.pay_time))]):e._e()]),t("li",[t("span",[e._v("发货")]),e.record.delivery_status!=e.DeliveryStatusEnum.NOT_DELIVERED.value?t("div",{staticClass:"tip"},[e._v("发货于 "+e._s(e.record.delivery_time))]):e._e()]),t("li",[t("span",[e._v("收货")]),e.record.receipt_status==e.ReceiptStatusEnum.RECEIVED.value?t("div",{staticClass:"tip"},[e._v("收货于 "+e._s(e.record.receipt_time))]):e._e()]),t("li",[t("span",[e._v("完成")]),e.record.order_status==e.OrderStatusEnum.COMPLETED.value?t("div",{staticClass:"tip"},[e._v("完成于 "+e._s(e.record.receipt_time))]):e._e()])])])]),t("a-card",{staticClass:"mt-20",attrs:{bordered:!1}},[e.record.order_status!=e.OrderStatusEnum.CANCELLED.value?[t("div",{staticClass:"ant-descriptions-title"},[e._v("订单操作")]),t("div",{staticClass:"alerts mt-10 mb-15"},[e.record.order_status==e.OrderStatusEnum.APPLY_CANCEL.value?t("a-alert",{attrs:{message:"当前买家已付款并申请取消订单，请审核是否同意，如同意则自动退回付款金额（原路退款）并关闭订单。",banner:""}}):e._e()],1),t("div",{staticClass:"actions clearfix mt-10"},[e.$module("order-updatePrice")&&e.$auth("/order/detail.updatePrice")?t("div",{staticClass:"action-item"},[e.record.pay_status==e.PayStatusEnum.PENDING.value?t("a-button",{on:{click:e.handleUpdatePrice}},[e._v("订单改价")]):e._e()],1):e._e(),e.$auth("/order/list/all.deliver")?t("div",{staticClass:"action-item"},[e.record.pay_status==e.PayStatusEnum.SUCCESS.value&&e.inArray(e.record.delivery_type,[e.DeliveryTypeEnum.EXPRESS.value,e.DeliveryTypeEnum.NOTHING.value])&&e.record.delivery_status!=e.DeliveryStatusEnum.DELIVERED.value&&!e.inArray(e.record.order_status,[e.OrderStatusEnum.CANCELLED.value,e.OrderStatusEnum.APPLY_CANCEL.value])?t("a-button",{attrs:{type:"primary"},on:{click:e.handleDelivery}},[e._v("发货")]):e._e()],1):e._e(),e.$auth("/order/list/all.extract")?t("div",{staticClass:"action-item"},[e.record.pay_status!=e.PayStatusEnum.SUCCESS.value||e.record.delivery_type!=e.DeliveryTypeEnum.EXTRACT.value||e.record.delivery_status==e.DeliveryStatusEnum.DELIVERED.value||e.inArray(e.record.order_status,[e.OrderStatusEnum.CANCELLED.value,e.OrderStatusEnum.APPLY_CANCEL.value])?e._e():t("a-button",{attrs:{type:"primary"},on:{click:e.handleExtract}},[e._v("自提核销")])],1):e._e(),e.$auth("/order/list/all.cancel")?t("div",{staticClass:"action-item"},[e.record.order_status==e.OrderStatusEnum.APPLY_CANCEL.value?t("a-button",{attrs:{type:"primary"},on:{click:e.handleCancel}},[e._v("审核取消订单")]):e._e()],1):e._e(),e.$auth("/order/detail.merchantRemark")?t("div",{staticClass:"action-item"},[t("a-button",{on:{click:e.handleMerchantRemark}},[e._v("商家备注")])],1):e._e(),e.$module("order-printer")&&e.$auth("/order/detail.printer")?t("div",{staticClass:"action-item"},[t("a-button",{on:{click:e.handlePrinter}},[e._v("打印小票")])],1):e._e()]),t("a-divider",{staticClass:"o-divider"})]:e._e(),t("a-descriptions",{attrs:{title:"订单信息"}},[t("a-descriptions-item",{attrs:{label:"订单号"}},[e._v(e._s(e.record.order_no))]),t("a-descriptions-item",{attrs:{label:"实付款金额"}},[e._v("￥"+e._s(e.record.pay_price))]),t("a-descriptions-item",{attrs:{label:"支付方式"}},[e.record.pay_method?t("a-tag",{attrs:{color:"green"}},[e._v(e._s(e.PaymentMethodEnum[e.record.pay_method].name))]):t("span",[e._v("--")])],1),t("a-descriptions-item",{attrs:{label:"配送方式"}},[t("a-tag",{attrs:{color:"green"}},[e._v(e._s(e.DeliveryTypeEnum[e.record.delivery_type].name))])],1),t("a-descriptions-item",{attrs:{label:"运费金额"}},[e._v("￥"+e._s(e.record.express_price))]),t("a-descriptions-item",{attrs:{label:"订单状态"}},[t("a-tag",{attrs:{color:e.renderOrderStatusColor(e.record.order_status)}},[e._v(e._s(e.OrderStatusEnum[e.record.order_status].name))])],1),t("a-descriptions-item",{attrs:{label:"买家信息"}},[t("a-tooltip",[t("template",{slot:"title"},[e._v("会员ID: "+e._s(e.record.user.user_id))]),t("span",{staticClass:"c-p"},[e._v(e._s(e.record.user.nick_name))])],2)],1),t("a-descriptions-item",{attrs:{label:"买家留言"}},[t("span",[e._v(e._s(e.record.buyer_remark?e.record.buyer_remark:"-"))])]),t("a-descriptions-item",{attrs:{label:"商家备注"}},[t("span",[e._v(e._s(e.record.merchant_remark?e.record.merchant_remark:"-"))])]),e.record.trade?t("a-descriptions-item",{attrs:{label:"第三方支付订单号"}},[t("span",[e._v(e._s(e.record.trade?e.record.trade.out_trade_no:"-"))])]):e._e(),e.record.trade?t("a-descriptions-item",{attrs:{label:"支付流水号"}},[t("span",[e._v(e._s(e.record.trade?e.record.trade.trade_no:"-"))])]):e._e()],1)],2),t("a-card",{staticClass:"mt-20",attrs:{bordered:!1}},[t("div",{staticClass:"ant-descriptions-title"},[e._v("订单商品")]),t("div",{staticClass:"goods-list"},[t("a-table",{attrs:{rowKey:"order_goods_id",columns:e.goodsColumns,dataSource:e.record.goods,pagination:!1},scopedSlots:e._u([{key:"goodsInfo",fn:function(e,a){return[t("GoodsItem",{attrs:{data:{image:a.goods_image,imageAlt:"商品图片",title:a.goods_name,goodsProps:a.goods_props}}})]}},{key:"goods_no",fn:function(a){return t("span",{},[e._v(e._s(a||"--"))])}},{key:"goods_price",fn:function(a,r){return[t("p",{class:{"f-through":r.is_user_grade}},[e._v("￥"+e._s(a))]),r.is_user_grade?t("p",[t("a-tooltip",[t("template",{slot:"title"},[t("span",{staticClass:"f-13"},[e._v("会员等级折扣价")])]),t("strong",[e._v("会员价：")]),t("span",[e._v("￥"+e._s(r.grade_goods_price))])],2)],1):e._e()]}},{key:"total_num",fn:function(a){return t("span",{},[e._v("x"+e._s(a))])}},{key:"total_price",fn:function(a){return t("span",{},[e._v("￥"+e._s(a))])}}],null,!1,369901077)}),t("div",{staticClass:"order-price"},[t("table",{staticClass:"fl-r"},[t("tbody",[t("tr",[t("td",[e._v("订单总额：")]),t("td",[e._v("￥"+e._s(e.record.total_price))])]),e.record.coupon_money>0?t("tr",[t("td",[e._v("优惠券抵扣：")]),t("td",[e._v("-￥"+e._s(e.record.coupon_money))])]):e._e(),e.record.points_money>0?t("tr",[t("td",[e._v("积分抵扣：")]),t("td",[e._v("-￥"+e._s(e.record.points_money))])]):e._e(),0!=e.record.update_price.value?t("tr",[t("td",[e._v("商家改价：")]),t("td",[e._v(e._s(e.record.update_price.symbol)+"￥"+e._s(e.record.update_price.value))])]):e._e(),t("tr",[t("td",[e._v("运费金额：")]),t("td",[e._v("+￥"+e._s(e.record.express_price))])]),t("tr",[t("td",[e._v("实付款金额：")]),t("td",[t("strong",[e._v("￥"+e._s(e.record.pay_price))])])])])])])],1)]),e.record.order_type==e.OrderTypeEnum.PHYSICAL.value?[e.record.pay_status!=e.PayStatusEnum.SUCCESS.value||e.record.delivery_status==e.DeliveryStatusEnum.NOT_DELIVERED.value||e.inArray(e.record.order_status,[e.OrderStatusEnum.CANCELLED.value,e.OrderStatusEnum.APPLY_CANCEL.value])?e._e():t("a-card",{staticClass:"mt-20",attrs:{bordered:!1}},[!e.record.delivery.length&&e.record.express?t("a-descriptions",{attrs:{title:"配送信息"}},[t("a-descriptions-item",{attrs:{label:"物流公司"}},[e._v(e._s(e.record.express.express_name))]),t("a-descriptions-item",{attrs:{label:"物流单号"}},[e._v(e._s(e.record.express_no))]),t("a-descriptions-item",{attrs:{label:"发货状态"}},[t("a-tag",{attrs:{color:e.record.delivery_status==e.DeliveryStatusEnum.DELIVERED.value?"green":""}},[e._v(e._s(e.DeliveryStatusEnum[e.record.delivery_status].name))])],1),t("a-descriptions-item",{attrs:{label:"发货时间"}},[e._v(e._s(e.record.delivery_time))])],1):e._e(),e.record.delivery.length?t("div",[t("a-descriptions",{attrs:{title:"发货信息"}}),t("a-tabs",{class:{"hide-bar":e.record.delivery.length<2},attrs:{"default-active-key":1}},e._l(e.record.delivery,(function(a,r){return t("a-tab-pane",{key:r+1,attrs:{tab:"包裹".concat(r+1)}},[t("a-descriptions",[t("a-descriptions-item",{attrs:{label:"物流公司"}},[e._v(e._s(20==a.delivery_method?"无需物流":a.express.express_name))]),t("a-descriptions-item",{attrs:{label:"物流单号"}},[e._v(e._s(a.express_no?a.express_no:"--"))]),t("a-descriptions-item",{attrs:{label:"发货状态"}},[t("a-tag",{attrs:{color:"green"}},[e._v("已发货")])],1)],1),t("div",{staticClass:"deliver-goods-list clearfix"},e._l(a.goods,(function(a,r){return t("div",{key:r,staticClass:"goods-item"},[t("a-tooltip",[t("template",{slot:"title"},[e._v(e._s(a.goods.goods_name))]),t("img",{staticClass:"goods-img",attrs:{src:a.goods.goods_image,alt:"商品图片"}}),t("div",{staticClass:"title"},[e._v("共"+e._s(a.delivery_num)+"件")])],2)],1)})),0)],1)})),1)],1):e._e()],1),e.record.delivery_type==e.DeliveryTypeEnum.EXPRESS.value?t("a-card",{staticClass:"mt-20",attrs:{bordered:!1}},[t("a-descriptions",{attrs:{title:"买家收货地址"}},[t("a-descriptions-item",{attrs:{label:"收货人姓名"}},[e._v(e._s(e.record.address.name))]),t("a-descriptions-item",{attrs:{label:"联系电话"}},[e._v(e._s(e.record.address.phone))]),t("a-descriptions-item",{attrs:{label:"收货地区"}},[e._v(" "+e._s(e.record.address.region.province)+" "+e._s(e.record.address.region.city)+" "+e._s(e.record.address.region.region)+" ")]),t("a-descriptions-item",{attrs:{label:"详细地址"}},[e._v(e._s(e.record.address.detail))])],1)],1):e._e(),e.record.delivery_type==e.DeliveryTypeEnum.EXTRACT.value?t("a-card",{staticClass:"mt-20",attrs:{bordered:!1}},[t("a-descriptions",{attrs:{title:"自提信息"}},[t("a-descriptions-item",{attrs:{label:"姓名"}},[t("span",{staticClass:"c-p"},[e._v(e._s(e.record.extract.linkman))]),t("span",{staticClass:"f-13 ml-5"},[e._v("(取货人)")])]),t("a-descriptions-item",{attrs:{label:"电话"}},[t("span",{staticClass:"c-p"},[e._v(e._s(e.record.extract.phone))]),t("span",{staticClass:"f-12 ml-5"},[e._v("(取货人)")])]),t("a-descriptions-item",{attrs:{label:""}}),t("a-descriptions-item",{attrs:{label:"自提门店"}},[t("a-tooltip",[t("template",{slot:"title"},[e._v("门店ID: "+e._s(e.record.extract_shop.shop_id))]),t("span",[e._v(e._s(e.record.extract_shop.shop_name))])],2)],1),t("a-descriptions-item",{attrs:{label:"门店联系人"}},[e._v(e._s(e.record.extract_shop.linkman))]),t("a-descriptions-item",{attrs:{label:"门店电话"}},[e._v(e._s(e.record.extract_shop.phone))]),t("a-descriptions-item",{attrs:{label:"门店地址"}},[t("span",[e._v(e._s(e.record.extract_shop.region.province))]),t("span",[e._v(e._s(e.record.extract_shop.region.city))]),t("span",[e._v(e._s(e.record.extract_shop.region.region))]),t("span",{staticClass:"ml-5"},[e._v(e._s(e.record.extract_shop.address))])])],1),e.record.pay_status!=e.PayStatusEnum.SUCCESS.value||e.record.delivery_status!=e.DeliveryStatusEnum.DELIVERED.value||e.inArray(e.record.order_status,[e.OrderStatusEnum.CANCELLED.value,e.OrderStatusEnum.APPLY_CANCEL.value])?e._e():[t("a-divider",{staticClass:"o-divider"}),t("a-descriptions",{attrs:{title:"核销信息"}},[t("a-descriptions-item",{attrs:{label:"门店名称"}},[t("a-tooltip",[t("template",{slot:"title"},[e._v("门店ID: "+e._s(e.record.extract_shop.shop_id))]),t("span",[e._v(e._s(e.record.extract_shop.shop_name))])],2)],1),t("a-descriptions-item",{attrs:{label:"核销员"}},[t("a-tooltip",[t("template",{slot:"title"},[e._v("核销员ID: "+e._s(e.record.extract_clerk.clerk_id))]),t("span",[e._v(e._s(e.record.extract_clerk.real_name))])],2)],1),t("a-descriptions-item",{attrs:{label:"核销状态"}},[t("a-tag",{attrs:{color:e.record.delivery_status==e.DeliveryStatusEnum.DELIVERED.value?"green":""}},[e._v("已核销")])],1),t("a-descriptions-item",{attrs:{label:"核销时间"}},[e._v(e._s(e.record.delivery_time))])],1)]],2):e._e()]:e._e()],2),t("DeliveryForm",{ref:"DeliveryForm",on:{handleSubmit:e.handleRefresh}}),t("ExtractForm",{ref:"ExtractForm",on:{handleSubmit:e.handleRefresh}}),t("CancelForm",{ref:"CancelForm",on:{handleSubmit:e.handleRefresh}}),t("PrinterForm",{ref:"PrinterForm",on:{handleSubmit:e.handleRefresh}}),t("PriceForm",{ref:"PriceForm",on:{handleSubmit:e.handleRefresh}}),t("RemarkForm",{ref:"RemarkForm",on:{handleSubmit:e.handleRefresh}})],1)},s=[],i=a("ade3"),n=(a("d3b7"),a("159b"),a("7db0"),a("ca00")),o=a("3a10"),d=a("ab09"),l=a("ac82"),c=a("4a95"),u=a("3c76"),m=[{title:"商品信息",scopedSlots:{customRender:"goodsInfo"}},{title:"商品编码",dataIndex:"goods_no",scopedSlots:{customRender:"goods_no"}},{title:"重量(Kg)",dataIndex:"goods_weight",scopedSlots:{customRender:"goods_weight"}},{title:"单价",dataIndex:"goods_price",scopedSlots:{customRender:"goods_price"}},{title:"购买数量",dataIndex:"total_num",scopedSlots:{customRender:"total_num"}},{title:"商品总价",dataIndex:"total_price",scopedSlots:{customRender:"total_price"}}],p={name:"Index",components:{GoodsItem:d["a"],UserItem:d["c"],DeliveryForm:l["b"],ExtractForm:l["c"],CancelForm:l["a"],PrinterForm:l["e"],PriceForm:l["d"],RemarkForm:l["f"]},data:function(){return{OrderTypeEnum:c["e"],DeliveryStatusEnum:c["a"],DeliveryTypeEnum:c["b"],OrderSourceEnum:c["c"],OrderStatusEnum:c["d"],PayStatusEnum:c["f"],ReceiptStatusEnum:c["g"],PaymentMethodEnum:u["a"],inArray:n["e"],isLoading:!0,orderId:null,record:{},progress:2,goodsColumns:m}},created:function(){this.orderId=this.$route.query.orderId,this.handleRefresh()},methods:{handleRefresh:function(){this.getDetail()},getDetail:function(){var e=this,t=this.orderId;this.isLoading=!0,o["a"]({orderId:t}).then((function(t){e.record=t.data.detail,e.initData()})).finally((function(){return e.isLoading=!1}))},initData:function(){this.initProgress(),this.initDeliveryGoods()},initProgress:function(){var e=this.record;this.progress=2,e.pay_status==c["f"].SUCCESS.value&&(this.progress+=1),e.delivery_status!=c["a"].NOT_DELIVERED.value&&(this.progress+=1),e.receipt_status==c["g"].RECEIVED.value&&(this.progress+=1)},initDeliveryGoods:function(){var e=this.record;e.delivery.length&&e.delivery.forEach((function(t){t.goods.forEach((function(t){t.goods=e.goods.find((function(e){return e.order_goods_id==t.order_goods_id}))}))}))},renderOrderStatusColor:function(e){var t,a=this.OrderStatusEnum,r=(t={},Object(i["a"])(t,a.NORMAL.value,""),Object(i["a"])(t,a.CANCELLED.value,"red"),Object(i["a"])(t,a.APPLY_CANCEL.value,"red"),Object(i["a"])(t,a.COMPLETED.value,"green"),t);return r[e]},handleDelivery:function(){var e=this.record;this.$refs.DeliveryForm.show(e)},handleExtract:function(){var e=this.record;this.$refs.ExtractForm.show(e)},handleCancel:function(){var e=this.record;this.$refs.CancelForm.show(e)},handlePrinter:function(){var e=this.record;this.$refs.PrinterForm.show(e)},handleUpdatePrice:function(){var e=this.record;this.$refs.PriceForm.show(e)},handleMerchantRemark:function(){var e=this.record;this.$refs.RemarkForm.show(e)}}},v=p,f=(a("ebb6"),a("2877")),h=Object(f["a"])(v,r,s,!1,null,"80446ce4",null);t["default"]=h.exports},ac82:function(e,t,a){"use strict";a.d(t,"b",(function(){return h})),a.d(t,"c",(function(){return S})),a.d(t,"a",(function(){return k})),a.d(t,"e",(function(){return j})),a.d(t,"d",(function(){return K})),a.d(t,"f",(function(){return J}));var r=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:680,visible:e.visible,isLoading:e.isLoading,confirmLoading:e.isLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form-model",{ref:"ruleForm",attrs:{model:e.formData,rules:e.rules,"label-col":e.labelCol,wrapperCol:e.wrapperCol}},[t("a-tabs",{model:{value:e.tabKey,callback:function(t){e.tabKey=t},expression:"tabKey"}},[t("a-tab-pane",{key:1,attrs:{tab:"未发货的商品"}},[t("a-table",{attrs:{rowKey:"order_goods_id",columns:e.columns1,dataSource:e.notDeliveredList,"row-selection":e.rowSelection,pagination:!1,scroll:{y:"320px",scrollToFirstRowOnChange:!0}},scopedSlots:e._u([{key:"goods",fn:function(e){return[t("GoodsItem",{attrs:{data:{image:e.goods_image,imageAlt:"商品图片",title:e.goods_name,goodsProps:e.goods_props,titleWidth:170},subTitleColor:!0}})]}},{key:"quantity",fn:function(a,r){return t("span",{},[e._v(e._s(r.total_num-r.delivery_num))])}},{key:"input",fn:function(a,r,s){return[t("a-input-number",{attrs:{min:1,max:r.total_num-r.delivery_num,precision:0},model:{value:e.packGoodsData[s].deliveryNum,callback:function(t){e.$set(e.packGoodsData[s],"deliveryNum",t)},expression:"packGoodsData[index].deliveryNum"}})]}}])}),t("a-form-model-item",{attrs:{label:"发货方式",prop:"deliveryMethod",required:""}},[t("a-radio-group",{model:{value:e.formData.deliveryMethod,callback:function(t){e.$set(e.formData,"deliveryMethod",t)},expression:"formData.deliveryMethod"}},[t("a-radio",{attrs:{value:10}},[e._v("物流信息")]),t("a-radio",{attrs:{value:20}},[e._v("无需物流")])],1)],1),10==e.formData.deliveryMethod?t("div",[t("a-form-model-item",{attrs:{label:"物流公司",prop:"expressId"}},[t("a-select",{attrs:{placeholder:"请选择物流公司"},model:{value:e.formData.expressId,callback:function(t){e.$set(e.formData,"expressId",t)},expression:"formData.expressId"}},e._l(e.expressList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.express_id}},[e._v(e._s(a.express_name))])})),1),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{target:"_blank",to:{path:"/setting/delivery/express/index"}}},[e._v("物流公司管理")])],1)],1),t("a-form-model-item",{attrs:{label:"物流单号",prop:"expressNo",extra:"请手动录入物流单号或快递单号"}},[t("a-input",{model:{value:e.formData.expressNo,callback:function(t){e.$set(e.formData,"expressNo",t)},expression:"formData.expressNo"}})],1)],1):e._e()],1),t("a-tab-pane",{key:2,attrs:{tab:"已发货的商品"}},[t("a-table",{attrs:{columns:e.columns2,rowKey:"order_goods_id",dataSource:e.deliveredList,pagination:!1,scroll:{y:"320px",scrollToFirstRowOnChange:!0}},scopedSlots:e._u([{key:"goods",fn:function(e){return[t("GoodsItem",{attrs:{data:{image:e.goods_image,imageAlt:"商品图片",title:e.goods_name,goodsProps:e.goods_props,titleWidth:170},subTitleColor:!0}})]}},{key:"total_num",fn:function(a){return t("span",{},[e._v(e._s(a))])}},{key:"quantity",fn:function(a,r){return t("span",{},[e._v(e._s(r.delivery_num))])}}])})],1)],1)],1)],1)],1)},s=[],i=(a("d3b7"),a("159b"),a("d81d"),a("4de4"),a("caad"),a("2532"),a("86d6")),n=a("2a66"),o=a("4a95"),d=a("ab09"),l=[{title:"商品信息",scopedSlots:{customRender:"goods"},width:"50%"},{title:"可发货的数量",scopedSlots:{customRender:"quantity"},width:"20%"},{title:"发货数量",scopedSlots:{customRender:"input"},width:"30%"}],c=[{title:"商品信息",scopedSlots:{customRender:"goods"},width:"50%"},{title:"购买数量",dataIndex:"total_num",width:"20%"},{title:"已发货的数量",scopedSlots:{customRender:"quantity"},width:"20%"}],u={expressId:[{required:!0,message:"请选择物流公司",trigger:"blur"}],expressNo:[{required:!0,message:"请填写物流单号",trigger:"blur"}]},m={components:{GoodsItem:d["a"]},data:function(){return{title:"订单发货",labelCol:{span:6},wrapperCol:{span:13},visible:!1,isLoading:!1,columns1:l,columns2:c,rules:u,tabKey:1,expressList:[],record:{},packGoodsData:[],formData:{deliveryMethod:10,packGoodsData:[],expressId:void 0,expressNo:""},selectedRowKeys:[]}},computed:{rowSelection:function(){var e=this;return{onChange:function(t,a){e.selectedRowKeys=t},selectedRowKeys:this.selectedRowKeys}},notDeliveredList:function(){var e=this.record;if(e&&e.goods){var t=[];return e.goods.forEach((function(e){e.delivery_status!=o["a"].DELIVERED.value&&t.push(e)})),this.selectedRowKeys=t.map((function(e){return e.order_goods_id})),t}},deliveredList:function(){var e=this.record;if(e&&e.goods){var t=[];return e.goods.forEach((function(e){e.delivery_status!=o["a"].NOT_DELIVERED.value&&t.push(e)})),t}}},created:function(){this.getExpressList()},methods:{show:function(e){this.record=e,10==e.order_type?this.showModalByPhysical():20==e.order_type&&this.showModalByVirtual()},showModalByPhysical:function(){this.visible=!0,this.initPackGoodsData()},showModalByVirtual:function(){var e=this;e.$confirm({title:"该订单商品无需配送，是否确认已交付给买家？",onOk:function(){e.onFormSubmit()}})},initPackGoodsData:function(){var e=this.notDeliveredList;e&&e.length&&(this.packGoodsData=e.map((function(e){return{orderGoodsId:e.order_goods_id,deliveryNum:e.total_num-e.delivery_num}})))},getExpressList:function(){var e=this;this.isLoading=!0,n["b"]().then((function(t){return e.expressList=t.data.list})).finally((function(){return e.isLoading=!1}))},handleSubmit:function(e){var t=this;if(e.preventDefault(),this.tabKey=1,!this.selectedRowKeys.length)return this.$message.error("您还没有选择要发货的商品"),!1;this.formData.packGoodsData=this.packGoodsData.filter((function(e){return t.selectedRowKeys.includes(e.orderGoodsId)})),this.$refs.ruleForm.validate((function(e){e&&t.onFormSubmit()}))},handleCancel:function(){this.visible=!1,this.$refs.ruleForm&&this.$refs.ruleForm.resetFields()},onFormSubmit:function(){var e=this;this.isLoading=!0,i["b"]({orderId:this.record.order_id,form:this.formData}).then((function(t){e.$message.success(t.message,1.5),e.handleCancel(),e.$emit("handleSubmit",!0)})).finally((function(){return e.isLoading=!1}))}}},p=m,v=(a("51bb"),a("2877")),f=Object(v["a"])(p,r,s,!1,null,"c3153d98",null),h=f.exports,_=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:640,visible:e.visible,isLoading:e.isLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"门店核销员",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["clerk_id",{rules:[{required:!0,message:"请选择核销员"}]}],expression:"['clerk_id', { rules: [{ required: true, message: '请选择核销员' }] }]"}],attrs:{placeholder:"请选择核销员"}},e._l(e.shopClerkList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.clerk_id}},[t("span",[e._v(e._s(a.real_name))]),t("span",{staticClass:"ml-5 f-13"},[e._v("("+e._s(a.shop.shop_name)+")")])])})),1),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{target:"_blank",to:{path:"/store/shop/clerk/index"}}},[e._v("门店店员管理")])],1)],1),t("a-form-item",{attrs:{label:"买家取货状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"买家必须已取货才可确认核销"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("已取货")]),t("a-radio",{attrs:{value:0,disabled:""}},[e._v("未取货")])],1)],1)],1)],1)],1)},b=[],g=a("884a"),y=a("6dd2"),E={data:function(){return{title:"门店自提核销",labelCol:{span:7},wrapperCol:{span:13},visible:!1,isLoading:!1,form:this.$form.createForm(this),shopClerkList:[],record:{}}},created:function(){this.getShopClerkList()},methods:{show:function(e){this.visible=!0,this.record=e},getShopClerkList:function(){var e=this;this.isLoading=!0,y["b"]().then((function(t){e.shopClerkList=t.data.list})).finally((function(){e.isLoading=!1}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.isLoading=!0,g["c"]({orderId:this.record.order_id,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){t.isLoading=!1}))}}},C=E,L=Object(v["a"])(C,_,b,!1,null,null,null),S=L.exports,w=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:560,visible:e.visible,isLoading:e.isLoading,confirmLoading:e.isLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"实付款金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("span",[e._v("￥"+e._s(e.record.pay_price))])]),t("a-form-item",{attrs:{label:"审核状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"同意后将退回付款金额并关闭订单"}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("同意")]),t("a-radio",{attrs:{value:0}},[e._v("拒绝")])],1)],1)],1)],1)],1)},x=[],D={data:function(){return{title:"审核取消订单",labelCol:{span:7},wrapperCol:{span:13},visible:!1,isLoading:!1,form:this.$form.createForm(this),record:{}}},created:function(){},methods:{show:function(e){this.visible=!0,this.record=e},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.isLoading=!0,g["a"]({orderId:this.record.order_id,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){t.isLoading=!1}))}}},R=D,O=Object(v["a"])(R,w,x,!1,null,null,null),k=O.exports,I=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:560,visible:e.visible,isLoading:e.isLoading,confirmLoading:e.isLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"选择打印机",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["printerId",{rules:[{required:!0,message:"请选择小票打印机"}]}],expression:"['printerId', { rules: [{ required: true, message: '请选择小票打印机' }] }]"}],attrs:{placeholder:"请选择小票打印机"}},e._l(e.printerList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.printer_id}},[t("span",[e._v(e._s(a.printer_name))])])})),1),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{target:"_blank",to:{path:"/setting/printer/index"}}},[e._v("小票打印机管理")])],1)],1)],1)],1)],1)},P=[],T=a("f50c"),F={data:function(){return{title:"小票打印",labelCol:{span:7},wrapperCol:{span:13},visible:!1,isLoading:!1,form:this.$form.createForm(this),printerList:[],record:{}}},created:function(){this.getPrinterList()},methods:{show:function(e){this.visible=!0,this.record=e},getPrinterList:function(){var e=this;this.isLoading=!0,T["b"]().then((function(t){e.printerList=t.data.list})).finally((function(){e.isLoading=!1}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.isLoading=!0,g["d"]({orderId:this.record.order_id,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){t.isLoading=!1}))}}},A=F,N=Object(v["a"])(A,I,P,!1,null,null,null),j=N.exports,$=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:560,visible:e.visible,isLoading:e.isLoading,confirmLoading:e.isLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"订单金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"最终付款金额 = 订单金额 + 运费金额"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["order_price",{rules:[{required:!0,message:"请输入订单金额"}]}],expression:"['order_price', { rules: [{ required: true, message: '请输入订单金额' }] }]"}],attrs:{min:.01,precision:2}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"运费金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["express_price",{rules:[{required:!0,message:"请输入运费金额"}]}],expression:"['express_price', { rules: [{ required: true, message: '请输入运费金额' }] }]"}],attrs:{min:0,precision:2}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1)],1)],1)],1)},V=[],M=(a("a9e3"),a("2ef0")),G=a.n(M),q={data:function(){return{title:"修改订单金额",labelCol:{span:7},wrapperCol:{span:13},visible:!1,isLoading:!1,form:this.$form.createForm(this),record:{}}},created:function(){},methods:{show:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this.record,t=this.$nextTick,a=this.form.setFieldsValue;t((function(){var t=Number(e.update_price.value);"-"===e.update_price.symbol&&(t=-t),a({order_price:G.a.add(Number(e.order_price),t),express_price:Number(e.express_price)})}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.isLoading=!0,g["e"]({orderId:this.record.order_id,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){t.isLoading=!1}))}}},U=q,Y=Object(v["a"])(U,$,V,!1,null,null,null),K=Y.exports,W=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:560,visible:e.visible,isLoading:e.isLoading,confirmLoading:e.isLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"备注内容",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"商家备注内容仅后台可见，用户端不可见"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["content"],expression:"['content']"}]})],1)],1)],1)],1)},B=[],H={data:function(){return{title:"商家备注",labelCol:{span:6},wrapperCol:{span:14},visible:!1,isLoading:!1,form:this.$form.createForm(this),record:{}}},created:function(){},methods:{show:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this.record,t=this.$nextTick,a=this.form.setFieldsValue;t((function(){a({content:e.merchant_remark})}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.isLoading=!0,g["f"]({orderId:this.record.order_id,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){return t.isLoading=!1}))}}},X=H,z=Object(v["a"])(X,W,B,!1,null,null,null),J=z.exports},b4a5:function(e,t,a){"use strict";a.d(t,"a",(function(){return v})),a.d(t,"b",(function(){return y}));a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:560,visible:e.visible,isLoading:e.isLoading,confirmLoading:e.isLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.isLoading}},[e.visible?t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"售后类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-tag",[e._v(e._s(e.RefundTypeEnum[e.record.type].name))])],1),t("a-form-item",{attrs:{label:"审核状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["audit_status",{initialValue:10,rules:[{required:!0}]}],expression:"['audit_status', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("同意")]),t("a-radio",{attrs:{value:20}},[e._v("拒绝")])],1)],1),e.form.getFieldValue("audit_status")==e.AuditStatusEnum.REVIEWED.value?t("a-form-item",{attrs:{label:"退货地址",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["address_id",{rules:[{required:!0,message:"请选择退货地址"}]}],expression:"['address_id', { rules: [{ required: true, message: '请选择退货地址' }] }]"}],attrs:{placeholder:"请选择退货地址"}},e._l(e.addressList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.address_id}},[e._v(e._s(a.full_address))])})),1),t("div",{staticClass:"form-item-help"},[t("router-link",{attrs:{target:"_blank",to:{path:"/store/address/index"}}},[e._v("地址管理")])],1)],1):e._e(),e.form.getFieldValue("audit_status")==e.AuditStatusEnum.REJECTED.value?t("a-form-item",{attrs:{label:"拒绝原因",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["refuse_desc",{rules:[{required:!0,message:"请输入拒绝原因"}]}],expression:"['refuse_desc', { rules: [{ required: true, message: '请输入拒绝原因' }] }]"}],attrs:{autoSize:{minRows:4}}})],1):e._e()],1):e._e()],1)],1)},s=[],i=(a("d3b7"),a("ca00")),n=a("e585"),o=a("4d55"),d=a("6ab1"),l=a("6245"),c={data:function(){return{title:"售后单审核",labelCol:{span:7},wrapperCol:{span:13},visible:!1,isLoading:!1,form:this.$form.createForm(this),addressList:[],record:{}}},beforeCreate:function(){Object(i["a"])(this,{AuditStatusEnum:d["a"],RefundTypeEnum:d["c"]})},created:function(){this.getAddressList()},methods:{show:function(e){var t=this;this.visible=!0,this.record=e,this.$nextTick((function(){t.$forceUpdate()}))},getAddressList:function(){var e=this;this.isLoading=!0,o["b"]({type:l["a"].RETURN.value}).then((function(t){return e.addressList=t.data.list})).finally((function(){return e.isLoading=!1}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.isLoading=!0,n["a"]({orderRefundId:this.record.order_refund_id,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){t.isLoading=!1}))}}},u=c,m=(a("b89b"),a("2877")),p=Object(m["a"])(u,r,s,!1,null,"4c705c72",null),v=p.exports,f=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:565,visible:e.visible,isLoading:e.isLoading,confirmLoading:e.isLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.isLoading}},[e.record.type==e.RefundTypeEnum.RETURN.value?t("a-alert",{attrs:{message:"请确认已收到寄回的商品，确认后自动退回付款金额（原路退款）并关闭当前售后单",banner:""}}):e._e(),e.visible?t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"售后类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-tag",[e._v(e._s(e.RefundTypeEnum[e.record.type].name))])],1),t("a-form-item",{attrs:{label:"订单付款的总金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("span",[e._v("￥"+e._s(e.record.orderData.pay_price)+"元")])]),t("a-form-item",{attrs:{label:"退款的金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["refund_money",{rules:[{required:!0,message:"请输入退款金额"}]}],expression:"['refund_money', { rules: [{ required: true, message: '请输入退款金额' }] }]"}],attrs:{min:0,precision:2}}),t("span",{staticClass:"ml-10"},[e._v("元")]),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("请输入退款金额，最多"+e._s(Math.min(e.record.orderGoods.total_pay_price,e.record.orderData.pay_price))+"元，最多不能大于订单实际付款的总金额")])])],1)],1):e._e()],1)],1)},h=[],_={data:function(){return{title:"确认收货并退款",labelCol:{span:8},wrapperCol:{span:13},visible:!1,isLoading:!1,form:this.$form.createForm(this),record:{}}},beforeCreate:function(){Object(i["a"])(this,{AuditStatusEnum:d["a"],RefundTypeEnum:d["c"]})},methods:{show:function(e){var t=this;this.visible=!0,this.record=e,this.$nextTick((function(){t.$forceUpdate()}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.isLoading=!0,n["d"]({orderRefundId:this.record.order_refund_id,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){t.isLoading=!1}))}}},b=_,g=(a("aa42"),Object(m["a"])(b,f,h,!1,null,"3d8fae34",null)),y=g.exports},b89b:function(e,t,a){"use strict";a("a25d")},bee9:function(e,t,a){"use strict";a.r(t);a("b0c0"),a("99af");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("a-spin",{attrs:{spinning:e.isLoading}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",{staticClass:"row-item-search"},[t("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[t("a-form-item",{attrs:{label:"订单查询"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["searchValue"],expression:"['searchValue']"}],staticStyle:{width:"337px"},attrs:{placeholder:"请输入关键词"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["searchType",{initialValue:10}],expression:"['searchType', { initialValue: 10 }]"}],staticStyle:{width:"100px"},attrs:{slot:"addonBefore"},slot:"addonBefore"},e._l(e.SearchTypeEnum,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1)],1)],1),t("a-form-item",{attrs:{label:"订单来源"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["orderSource",{initialValue:-1}],expression:"['orderSource', { initialValue: -1 }]"}]},[t("a-select-option",{attrs:{value:-1}},[e._v("全部")]),e._l(e.OrderSourceEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])}))],2)],1),t("a-form-item",{attrs:{label:"支付方式"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["payMethod",{initialValue:""}],expression:"['payMethod', { initialValue: '' }]"}]},[t("a-select-option",{attrs:{value:""}},[e._v("全部")]),e._l(e.PaymentMethodEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])}))],2)],1),t("a-form-item",{attrs:{label:"下单时间"}},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime"],expression:"['betweenTime']"}],attrs:{format:"YYYY-MM-DD"}})],1),t("a-form-item",{staticClass:"search-btn"},[t("a-button",{staticClass:"mr-15",attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")]),t("a-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1)],1),t("a-row",{staticClass:"actions"},[e.$auth("/order/tools/delivery/batch")?t("div",{staticClass:"action-item"},[t("router-link",{attrs:{to:{path:"/order/tools/delivery/batch"}}},[t("a-button",{attrs:{type:"primary"}},[e._v("批量发货")])],1)],1):e._e(),e.$auth("/order/tools/delivery/record")?t("div",{staticClass:"action-item"},[t("router-link",{attrs:{to:{path:"/order/tools/delivery/record"}}},[t("a-button",{attrs:{type:"default"}},[e._v("发货记录")])],1)],1):e._e()])],1),t("div",{staticClass:"ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered"},[t("div",{staticClass:"ant-table-content"},[t("div",{staticClass:"ant-table-body"},[t("table",[t("thead",{staticClass:"ant-table-thead"},[t("tr",e._l(e.columns,(function(a,r){return t("th",{key:r},[t("span",{staticClass:"ant-table-header-column"},[t("div",[t("span",{staticClass:"ant-table-column-title"},[e._v(e._s(a.title))])])])])})),0)]),t("tbody",{staticClass:"ant-table-tbody"},[e._l(e.orderList.data,(function(a){return[t("tr",{key:"order_".concat(a.order_id,"_1"),staticClass:"order-empty"},[t("td",{attrs:{colspan:"8"}})]),t("tr",{key:"order_".concat(a.order_id,"_2")},[t("td",{attrs:{colspan:"8"}},[t("span",{staticClass:"mr-20"},[e._v(e._s(a.create_time))]),t("span",{staticClass:"mr-20"},[e._v("订单号："+e._s(a.order_no))]),t("platform-icon",{attrs:{name:a.platform,showTips:!0}})],1)]),e._l(a.goods,(function(r,s){return t("tr",{key:"orderGoods_".concat(a.order_id,"_").concat(s)},[t("td",[t("GoodsItem",{attrs:{data:{image:r.goods_image,imageAlt:"商品图片",title:r.goods_name,goodsProps:r.goods_props}}})],1),t("td",[t("p",[e._v("￥"+e._s(r.goods_price))]),t("p",[e._v("×"+e._s(r.total_num))])]),0===s?[t("td",{attrs:{rowspan:a.goods.length}},[t("p",[e._v("￥"+e._s(a.pay_price))]),t("p",{staticClass:"c-muted-1"},[e._v("(含运费：￥"+e._s(a.express_price)+")")])]),t("td",{attrs:{rowspan:a.goods.length}},[t("UserItem",{attrs:{user:a.user}})],1),t("td",{attrs:{rowspan:a.goods.length}},[t("a-tag",[e._v(e._s(e.DeliveryTypeEnum[a.delivery_type].name))])],1),t("td",{attrs:{rowspan:a.goods.length}},[t("div",{staticClass:"f-12"},[t("p",[e._v("收货人："+e._s(a.address.name))]),t("p",[e._v("联系电话："+e._s(a.address.phone))]),t("p",{staticClass:"oneline-hide",staticStyle:{"max-width":"260px"}},[e._v(" 收货地址： "+e._s(a.address.region.province)+" "+e._s(a.address.region.city)+" "+e._s(a.address.region.region)+" "+e._s(a.address.detail)+" ")])])]),t("td",{attrs:{rowspan:a.goods.length}},[t("p",{staticClass:"mtb-2"},[t("a-tag",{attrs:{color:a.delivery_status==e.DeliveryStatusEnum.DELIVERED.value?"green":""}},[e._v(e._s(e.DeliveryStatusEnum[a.delivery_status].name))])],1)]),t("td",{attrs:{rowspan:a.goods.length}},[t("div",{staticClass:"actions"},[e.$auth("/order/detail")?t("router-link",{attrs:{to:{path:"/order/detail",query:{orderId:a.order_id}}}},[e._v("详情")]):e._e(),a.pay_status!=e.PayStatusEnum.SUCCESS.value||a.delivery_type!=e.DeliveryTypeEnum.EXPRESS.value||a.delivery_status==e.DeliveryStatusEnum.DELIVERED.value||e.inArray(a.order_status,[e.OrderStatusEnum.CANCELLED.value,e.OrderStatusEnum.APPLY_CANCEL.value])?e._e():[t("a",{directives:[{name:"action",rawName:"v-action:deliver",arg:"deliver"}],on:{click:function(t){return e.handleDelivery(a)}}},[e._v("发货")]),e.$module("order-eorder")?t("a",{directives:[{name:"action",rawName:"v-action:eorder",arg:"eorder"}],on:{click:function(t){return e.handleEorder(a)}}},[e._v("电子面单")]):e._e()]],2)])]:e._e()],2)}))]}))],2)])]),e.orderList.data.length?e._e():t("a-empty",{attrs:{image:e.simpleImage}})],1)]),e.orderList.data.length?t("div",{staticClass:"pagination"},[t("a-pagination",{attrs:{current:e.page,pageSize:e.orderList.per_page,total:e.orderList.total},on:{change:e.onChangePage}})],1):e._e(),t("DeliveryForm",{ref:"DeliveryForm",on:{handleSubmit:e.handleRefresh}}),t("EorderForm",{ref:"EorderForm",on:{handleSubmit:e.handleRefresh}})],1)],1)},s=[],i=a("5530"),n=(a("06f4"),a("fc25")),o=(a("d3b7"),a("ca00")),d=a("3a10"),l=a("8d5f"),c=a("ab09"),u=a("4a95"),m=a("3c76"),p=a("ac82"),v=a("2264"),f=[{title:"商品信息",align:"center",dataIndex:"goods",scopedSlots:{customRender:"goods"}},{title:"单价/数量",align:"center",scopedSlots:{customRender:"unit_price"}},{title:"实付款",align:"center",dataIndex:"pay_price",scopedSlots:{customRender:"pay_price"}},{title:"买家",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"配送方式",dataIndex:"delivery_type",scopedSlots:{customRender:"delivery_type"}},{title:"配送信息",dataIndex:"delivery",scopedSlots:{customRender:"delivery"}},{title:"发货状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],h=[{name:"订单号",value:10},{name:"会员昵称",value:20},{name:"会员ID",value:30}],_={name:"Index",components:{PlatformIcon:l["a"],GoodsItem:c["a"],UserItem:c["c"],DeliveryForm:p["b"],EorderForm:v["a"]},data:function(){return{dataType:"delivery",searchForm:this.$form.createForm(this),queryParam:{},isLoading:!1,columns:f,page:1,orderList:{data:[],total:0,per_page:10}}},beforeCreate:function(){Object(o["a"])(this,{inArray:o["e"],DeliveryStatusEnum:u["a"],DeliveryTypeEnum:u["b"],OrderSourceEnum:u["c"],OrderStatusEnum:u["d"],PayStatusEnum:u["f"],ReceiptStatusEnum:u["g"],PaymentMethodEnum:m["a"],SearchTypeEnum:h,simpleImage:n["a"].PRESENTED_IMAGE_SIMPLE})},created:function(){this.init()},methods:{init:function(){this.searchForm.resetFields(),this.queryParam={},this.handleRefresh(!0)},getList:function(){var e=this,t=this.dataType,a=this.queryParam,r=this.page;return this.isLoading=!0,d["b"](Object(i["a"])(Object(i["a"])({dataType:t},a),{},{deliveryType:u["b"].EXPRESS.value,page:r})).then((function(t){e.orderList=t.data.list})).finally((function(){e.isLoading=!1}))},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e&&(this.page=1),this.getList()},handleSearch:function(e){var t=this;e.preventDefault(),this.searchForm.validateFields((function(e,a){e||(t.queryParam=Object(i["a"])(Object(i["a"])({},t.queryParam),a),t.handleRefresh(!0))}))},handleReset:function(){this.searchForm.resetFields()},onChangePage:function(e){this.page=e,this.handleRefresh()},handleDelivery:function(e){this.$refs.DeliveryForm.show(e)},handleEorder:function(e){this.$refs.EorderForm.handle(e)}}},b=_,g=(a("6e5c"),a("2877")),y=Object(g["a"])(b,r,s,!1,null,"4416b731",null);t["default"]=y.exports},c2b1:function(e,t,a){},c7eb:function(e,t,a){"use strict";a.d(t,"a",(function(){return s}));a("a4d3"),a("e01a"),a("d3b7"),a("d28b"),a("3ca3"),a("ddb0"),a("b636"),a("944a"),a("0c47"),a("23dc"),a("3410"),a("159b"),a("b0c0"),a("131a"),a("fb6a");var r=a("53ca");function s(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
s=function(){return e};var e={},t=Object.prototype,a=t.hasOwnProperty,i=Object.defineProperty||function(e,t,a){e[t]=a.value},n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",d=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function c(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(k){c=function(e,t,a){return e[t]=a}}function u(e,t,a,r){var s=t&&t.prototype instanceof v?t:v,n=Object.create(s.prototype),o=new D(r||[]);return i(n,"_invoke",{value:L(e,a,o)}),n}function m(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(k){return{type:"throw",arg:k}}}e.wrap=u;var p={};function v(){}function f(){}function h(){}var _={};c(_,o,(function(){return this}));var b=Object.getPrototypeOf,g=b&&b(b(R([])));g&&g!==t&&a.call(g,o)&&(_=g);var y=h.prototype=v.prototype=Object.create(_);function E(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function s(i,n,o,d){var l=m(e[i],e,n);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==Object(r["a"])(u)&&a.call(u,"__await")?t.resolve(u.__await).then((function(e){s("next",e,o,d)}),(function(e){s("throw",e,o,d)})):t.resolve(u).then((function(e){c.value=e,o(c)}),(function(e){return s("throw",e,o,d)}))}d(l.arg)}var n;i(this,"_invoke",{value:function(e,a){function r(){return new t((function(t,r){s(e,a,t,r)}))}return n=n?n.then(r,r):r()}})}function L(e,t,a){var r="suspendedStart";return function(s,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===s)throw i;return O()}for(a.method=s,a.arg=i;;){var n=a.delegate;if(n){var o=S(n,a);if(o){if(o===p)continue;return o}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===r)throw r="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r="executing";var d=m(e,t,a);if("normal"===d.type){if(r=a.done?"completed":"suspendedYield",d.arg===p)continue;return{value:d.arg,done:a.done}}"throw"===d.type&&(r="completed",a.method="throw",a.arg=d.arg)}}}function S(e,t){var a=t.method,r=e.iterator[a];if(void 0===r)return t.delegate=null,"throw"===a&&e.iterator["return"]&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==a&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+a+"' method")),p;var s=m(r,e.iterator,t.arg);if("throw"===s.type)return t.method="throw",t.arg=s.arg,t.delegate=null,p;var i=s.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,p):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function w(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function x(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function D(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(w,this),this.reset(!0)}function R(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,s=function t(){for(;++r<e.length;)if(a.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return s.next=s}}return{next:O}}function O(){return{value:void 0,done:!0}}return f.prototype=h,i(y,"constructor",{value:h,configurable:!0}),i(h,"constructor",{value:f,configurable:!0}),f.displayName=c(h,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,c(e,l,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},E(C.prototype),c(C.prototype,d,(function(){return this})),e.AsyncIterator=C,e.async=function(t,a,r,s,i){void 0===i&&(i=Promise);var n=new C(u(t,a,r,s),i);return e.isGeneratorFunction(a)?n:n.next().then((function(e){return e.done?e.value:n.next()}))},E(y),c(y,l,"Generator"),c(y,o,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),a=[];for(var r in t)a.push(r);return a.reverse(),function e(){for(;a.length;){var r=a.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=R,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(x),!e)for(var t in this)"t"===t.charAt(0)&&a.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(a,r){return n.type="throw",n.arg=e,t.next=a,r&&(t.method="next",t.arg=void 0),!!r}for(var s=this.tryEntries.length-1;s>=0;--s){var i=this.tryEntries[s],n=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var o=a.call(i,"catchLoc"),d=a.call(i,"finallyLoc");if(o&&d){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(o){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!d)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var s=this.tryEntries[r];if(s.tryLoc<=this.prev&&a.call(s,"finallyLoc")&&this.prev<s.finallyLoc){var i=s;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var n=i?i.completion:{};return n.type=e,n.arg=t,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(n)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),x(a),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var r=a.completion;if("throw"===r.type){var s=r.arg;x(a)}return s}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,a){return this.delegate={iterator:R(e),resultName:t,nextLoc:a},"next"===this.method&&(this.arg=void 0),p}},e}},d409:function(e,t,a){"use strict";a.r(t);a("b0c0"),a("99af");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("a-spin",{attrs:{spinning:e.isLoading}},[t("div",{staticClass:"card-title"},[e._v("发货记录（物流配送）")]),t("div",{staticClass:"table-operator"},[t("a-row",{staticClass:"row-item-search"},[t("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[t("a-form-item",{attrs:{label:""}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["searchValue"],expression:"['searchValue']"}],staticStyle:{width:"337px"},attrs:{placeholder:"请输入关键词"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["searchType",{initialValue:10}],expression:"['searchType', { initialValue: 10 }]"}],staticStyle:{width:"100px"},attrs:{slot:"addonBefore"},slot:"addonBefore"},e._l(e.SearchTypeEnum,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1)],1)],1),t("a-form-item",{attrs:{label:"发货时间"}},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime"],expression:"['betweenTime']"}],attrs:{format:"YYYY-MM-DD"}})],1),t("a-form-item",{staticClass:"search-btn"},[t("a-button",{staticClass:"mr-15",attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")]),t("a-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1)],1)],1),t("div",{staticClass:"ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered"},[t("div",{staticClass:"ant-table-content"},[t("div",{staticClass:"ant-table-body"},[t("table",[t("thead",{staticClass:"ant-table-thead"},[t("tr",e._l(e.columns,(function(a,r){return t("th",{key:r},[t("span",{staticClass:"ant-table-header-column"},[t("div",[t("span",{staticClass:"ant-table-column-title"},[e._v(e._s(a.title))])])])])})),0)]),t("tbody",{staticClass:"ant-table-tbody"},[e._l(e.orderList.data,(function(a){return[t("tr",{key:"order_".concat(a.delivery_id,"_1"),staticClass:"order-empty"},[t("td",{attrs:{colspan:"8"}})]),t("tr",{key:"order_".concat(a.delivery_id,"_2")},[t("td",{attrs:{colspan:"8"}},[t("span",{staticClass:"mr-20"},[e._v("发货时间："+e._s(a.create_time))]),t("span",{staticClass:"mr-20"},[e._v("订单号："+e._s(a.orderData.order_no))]),t("platform-icon",{attrs:{name:a.orderData.platform,showTips:!0}})],1)]),e._l(a.goods,(function(r,s){return t("tr",{key:"orderGoods_".concat(a.delivery_id,"_").concat(s)},[t("td",[t("GoodsItem",{attrs:{data:{image:r.goods.goods_image,imageAlt:"商品图片",title:r.goods.goods_name,goodsProps:r.goods.goods_props}}})],1),t("td",[t("p",[e._v("×"+e._s(r.goods.delivery_num))])]),0===s?[t("td",{attrs:{rowspan:a.goods.length}},[t("UserItem",{attrs:{user:a.orderData.user}})],1),t("td",{attrs:{rowspan:a.goods.length}},[t("a-tag",[e._v(e._s(e.DeliveryMethodEnum[a.delivery_method].name))])],1),t("td",{attrs:{rowspan:a.goods.length}},[a.express?t("div",{staticClass:"f-12"},[t("p",[e._v(e._s(a.express.express_name))]),t("p",[e._v(e._s(a.express_no))])]):t("span",[e._v("--")])]),t("td",{attrs:{rowspan:a.goods.length}},[a.orderData.address?t("div",{staticClass:"f-12"},[t("p",[e._v("收货人："+e._s(a.orderData.address.name))]),t("p",[e._v("联系电话："+e._s(a.orderData.address.phone))]),t("p",{staticClass:"oneline-hide",staticStyle:{"max-width":"260px"}},[e._v(" 收货地址： "+e._s(a.orderData.address.region.province)+" "+e._s(a.orderData.address.region.city)+" "+e._s(a.orderData.address.region.region)+" "+e._s(a.orderData.address.detail)+" ")])]):t("span",[e._v("--")])]),t("td",{attrs:{rowspan:a.goods.length}},[t("div",{staticClass:"actions"},[e.$auth("/order/detail")?t("router-link",{attrs:{to:{path:"/order/detail",query:{orderId:a.order_id}},target:"_blank"}},[e._v("订单详情")]):e._e(),a.delivery_method==e.DeliveryMethodEnum.EORDER.value?[e.$auth("/order/tools/delivery.eorder")?t("a",{attrs:{title:"查看电子面单内容"},on:{click:function(t){return e.handleEorder(a)}}},[e._v("电子面单")]):e._e()]:e._e()],2)])]:e._e()],2)}))]}))],2)])]),e.orderList.data.length?e._e():t("a-empty",{attrs:{image:e.simpleImage}})],1)]),e.orderList.data.length?t("div",{staticClass:"pagination"},[t("a-pagination",{attrs:{current:e.page,pageSize:e.orderList.per_page,total:e.orderList.total},on:{change:e.onChangePage}})],1):e._e(),t("EorderShow",{ref:"EorderShow"})],1)],1)},s=[],i=a("5530"),n=(a("06f4"),a("fc25")),o=(a("d3b7"),a("ca00")),d=a("86d6"),l=a("8d5f"),c=a("ab09"),u=a("5c06"),m=new u["a"]([{key:"MANUAL",name:"手动录入",value:10},{key:"NORMAL",name:"无需物流",value:20},{key:"EORDER",name:"电子面单",value:30}]),p=a("2264"),v=[{title:"商品信息",align:"center",dataIndex:"goods",scopedSlots:{customRender:"goods"}},{title:"发货数量",align:"center",scopedSlots:{customRender:"delivery_num"}},{title:"买家",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"物流方式",dataIndex:"delivery_method",scopedSlots:{customRender:"delivery_method"}},{title:"物流公司/单号",dataIndex:"express",scopedSlots:{customRender:"express"}},{title:"配送信息",dataIndex:"address",scopedSlots:{customRender:"address"}},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],f=[{name:"订单号",value:10}],h={name:"Index",components:{PlatformIcon:l["a"],GoodsItem:c["a"],UserItem:c["c"],EorderShow:p["b"]},data:function(){return{searchForm:this.$form.createForm(this),queryParam:{},isLoading:!1,columns:v,page:1,orderList:{data:[],total:0,per_page:10}}},beforeCreate:function(){Object(o["a"])(this,{inArray:o["e"],DeliveryMethodEnum:m,SearchTypeEnum:f,simpleImage:n["a"].PRESENTED_IMAGE_SIMPLE})},created:function(){this.init()},methods:{init:function(){this.searchForm.resetFields(),this.queryParam={},this.handleRefresh(!0)},getList:function(){var e=this,t=this.queryParam,a=this.page;return this.isLoading=!0,d["e"](Object(i["a"])(Object(i["a"])({},t),{},{page:a})).then((function(t){e.orderList=t.data.list})).finally((function(){return e.isLoading=!1}))},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e&&(this.page=1),this.getList()},handleSearch:function(e){var t=this;e.preventDefault(),this.searchForm.validateFields((function(e,a){e||(t.queryParam=Object(i["a"])(Object(i["a"])({},t.queryParam),a),t.handleRefresh(!0))}))},handleReset:function(){this.searchForm.resetFields()},onChangePage:function(e){this.page=e,this.handleRefresh()},handleEorder:function(e){this.$refs.EorderShow.handle(e)}}},_=h,b=(a("3c07"),a("2877")),g=Object(b["a"])(_,r,s,!1,null,"6fce4afe",null);t["default"]=g.exports},e585:function(e,t,a){"use strict";a.d(t,"c",(function(){return i})),a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"d",(function(){return d}));var r=a("b775"),s={list:"/order.refund/list",detail:"/order.refund/detail",audit:"/order.refund/audit",receipt:"/order.refund/receipt"};function i(e){return Object(r["b"])({url:s.list,method:"get",params:e})}function n(e){return Object(r["b"])({url:s.detail,method:"get",params:e})}function o(e){return Object(r["b"])({url:s.audit,method:"post",data:e})}function d(e){return Object(r["b"])({url:s.receipt,method:"post",data:e})}},ea1f:function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("a-spin",{attrs:{spinning:e.isLoading}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",{staticClass:"row-item-search"},[t("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[t("a-form-item",{attrs:{label:"关键词查询"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["searchValue"],expression:"['searchValue']"}],staticStyle:{width:"342px"},attrs:{placeholder:"请输入关键词"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["searchType",{initialValue:10}],expression:"['searchType', { initialValue: 10 }]"}],staticStyle:{width:"100px"},attrs:{slot:"addonBefore"},slot:"addonBefore"},e._l(e.SearchTypeEnum,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1)],1)],1),t("a-form-item",{attrs:{label:"售后类型"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["refundType",{initialValue:-1}],expression:"['refundType', { initialValue: -1 }]"}]},[t("a-select-option",{attrs:{value:-1}},[e._v("全部")]),e._l(e.RefundTypeEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])}))],2)],1),t("a-form-item",{attrs:{label:"售后单状态"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["refundStatus",{initialValue:-1}],expression:"['refundStatus', { initialValue: -1 }]"}]},[t("a-select-option",{attrs:{value:-1}},[e._v("全部")]),e._l(e.RefundStatusEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])}))],2)],1),t("a-form-item",{attrs:{label:"申请时间"}},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime"],expression:"['betweenTime']"}],attrs:{format:"YYYY-MM-DD"}})],1),t("a-form-item",{staticClass:"search-btn"},[t("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1),t("a-form-item",{staticClass:"search-btn"},[t("a-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1)],1)],1),t("div",{staticClass:"ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered"},[t("div",{staticClass:"ant-table-content"},[t("div",{staticClass:"ant-table-body"},[t("table",[t("thead",{staticClass:"ant-table-thead"},[t("tr",e._l(e.columns,(function(a,r){return t("th",{key:r},[t("span",{staticClass:"ant-table-header-column"},[t("div",[t("span",{staticClass:"ant-table-column-title"},[e._v(e._s(a.title))])])])])})),0)]),t("tbody",{staticClass:"ant-table-tbody"},[e._l(e.refundList.data,(function(a){return[t("tr",{key:"refund_".concat(a.order_refund_id,"_1"),staticClass:"order-empty"},[t("td",{attrs:{colspan:"8"}})]),t("tr",{key:"refund_".concat(a.order_refund_id,"_2")},[t("td",{attrs:{colspan:"8"}},[t("span",{staticClass:"mr-20"},[e._v(e._s(a.create_time))]),t("span",[e._v("订单号："+e._s(a.order_no))])])]),t("tr",{key:"refund_".concat(a.order_refund_id,"_3")},[t("td",[t("GoodsItem",{attrs:{data:{image:a.orderGoods.goods_image,imageAlt:"商品图片",title:a.orderGoods.goods_name,goodsProps:a.orderGoods.goods_props}}})],1),t("td",[t("p",[e._v("￥"+e._s(a.orderGoods.goods_price))]),t("p",[e._v("×"+e._s(a.orderGoods.total_num))])]),t("td",[t("p",[e._v("￥"+e._s(a.orderGoods.total_pay_price))])]),t("td",[t("UserItem",{attrs:{user:a.user}})],1),t("td",[t("a-tag",[e._v(e._s(e.RefundTypeEnum[a.type].name))])],1),t("td",[t("p",{staticClass:"mtb-2"},[t("span",{staticClass:"f-13"},[e._v("商家审核：")]),t("a-tag",{attrs:{color:e.renderAuditStatusColor(a.audit_status)}},[e._v(e._s(e.AuditStatusEnum[a.audit_status].name))])],1),a.type==e.RefundTypeEnum.RETURN.value?t("p",{staticClass:"mtb-2"},[t("span",{staticClass:"f-13"},[e._v("用户发货：")]),t("a-tag",{attrs:{color:a.is_user_send?"green":""}},[e._v(e._s(a.is_user_send?"已发货":"待发货"))])],1):e._e(),a.type==e.RefundTypeEnum.RETURN.value?t("p",{staticClass:"mtb-2"},[t("span",{staticClass:"f-13"},[e._v("商家收货：")]),t("a-tag",{attrs:{color:a.is_receipt?"green":""}},[e._v(e._s(a.is_receipt?"已收货":"待收货"))])],1):e._e()]),t("td",[t("a-tag",{attrs:{color:e.renderRefundStatusColor(a.status)}},[e._v(e._s(e.RefundStatusEnum[a.status].name))])],1),t("td",[t("div",{staticClass:"actions"},[e.$auth("/order/refund/detail")?t("router-link",{attrs:{to:{path:"/order/refund/detail",query:{orderRefundId:a.order_refund_id}}}},[e._v("详情")]):e._e(),a.audit_status==e.AuditStatusEnum.WAIT.value?t("a",{directives:[{name:"action",rawName:"v-action:audit",arg:"audit"}],on:{click:function(t){return e.handleAudit(a)}}},[e._v("审核")]):e._e(),a.type==e.RefundTypeEnum.RETURN.value&&a.audit_status==e.AuditStatusEnum.REVIEWED.value&&a.is_user_send&&!a.is_receipt?t("a",{directives:[{name:"action",rawName:"v-action:receipt",arg:"receipt"}],on:{click:function(t){return e.handleReceipt(a)}}},[e._v("确认收货")]):e._e()],1)])])]}))],2)])]),e.refundList.data.length?e._e():t("a-empty",{attrs:{image:e.simpleImage}})],1)]),e.refundList.data.length?t("div",{staticClass:"pagination"},[t("a-pagination",{attrs:{current:e.page,pageSize:e.refundList.per_page,total:e.refundList.total},on:{change:e.onChangePage}})],1):e._e(),t("AuditForm",{ref:"AuditForm",on:{handleSubmit:e.handleRefresh}}),t("ReceiptForm",{ref:"ReceiptForm",on:{handleSubmit:e.handleRefresh}})],1)],1)},s=[],i=a("ade3"),n=a("5530"),o=(a("06f4"),a("fc25")),d=(a("d3b7"),a("ca00")),l=a("e585"),c=a("ab09"),u=a("6ab1"),m=a("b4a5"),p=[{title:"商品信息",align:"center"},{title:"单价/数量",align:"center",scopedSlots:{customRender:"unit_price"}},{title:"付款金额",align:"center",dataIndex:"total_pay_price",scopedSlots:{customRender:"pay_price"}},{title:"买家",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"售后类型",dataIndex:"type",scopedSlots:{customRender:"type"}},{title:"处理进度",dataIndex:"progress",scopedSlots:{customRender:"progress"}},{title:"售后单状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],v=[{name:"订单号",value:10},{name:"会员昵称",value:20},{name:"会员ID",value:30}],f={name:"Index",components:{GoodsItem:c["a"],UserItem:c["c"],AuditForm:m["a"],ReceiptForm:m["b"]},data:function(){return{searchForm:this.$form.createForm(this),queryParam:{},isLoading:!1,columns:p,page:1,refundList:{data:[],total:0,per_page:10}}},beforeCreate:function(){Object(d["a"])(this,{inArray:d["e"],AuditStatusEnum:u["a"],RefundStatusEnum:u["b"],RefundTypeEnum:u["c"],SearchTypeEnum:v,simpleImage:o["a"].PRESENTED_IMAGE_SIMPLE})},created:function(){this.init()},methods:{init:function(){this.searchForm.resetFields(),this.queryParam={},this.handleRefresh(!0)},getList:function(){var e=this,t=this.queryParam,a=this.page;return this.isLoading=!0,l["c"](Object(n["a"])(Object(n["a"])({},t),{},{page:a})).then((function(t){e.refundList=t.data.list})).finally((function(){e.isLoading=!1}))},renderAuditStatusColor:function(e){var t,a=this.AuditStatusEnum,r=(t={},Object(i["a"])(t,a.WAIT.value,""),Object(i["a"])(t,a.REVIEWED.value,"green"),Object(i["a"])(t,a.REJECTED.value,"red"),t);return r[e]},renderRefundStatusColor:function(e){var t,a=this.RefundStatusEnum,r=(t={},Object(i["a"])(t,a.NORMAL.value,""),Object(i["a"])(t,a.REJECTED.value,"red"),Object(i["a"])(t,a.COMPLETED.value,"green"),Object(i["a"])(t,a.CANCELLED.value,"red"),t);return r[e]},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e&&(this.page=1),this.getList()},handleSearch:function(e){var t=this;e.preventDefault(),this.searchForm.validateFields((function(e,a){e||(t.queryParam=Object(n["a"])(Object(n["a"])({},t.queryParam),a),t.handleRefresh(!0))}))},handleReset:function(){this.searchForm.resetFields()},onChangePage:function(e){this.page=e,this.handleRefresh()},handleAudit:function(e){this.$refs.AuditForm.show(e)},handleReceipt:function(e){this.$refs.ReceiptForm.show(e)}}},h=f,_=(a("5cd4"),a("2877")),b=Object(_["a"])(h,r,s,!1,null,"3da161e7",null);t["default"]=b.exports},ebb6:function(e,t,a){"use strict";a("c2b1")},f0a9:function(e,t,a){"use strict";a("7ebd")},f1bf:function(e,t,a){"use strict";a("4c24")},f50c:function(e,t,a){"use strict";a.d(t,"e",(function(){return i})),a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"d",(function(){return d})),a.d(t,"c",(function(){return l}));var r=a("b775"),s={list:"/setting.printer/list",all:"/setting.printer/all",add:"/setting.printer/add",edit:"/setting.printer/edit",delete:"/setting.printer/delete"};function i(e){return Object(r["b"])({url:s.list,method:"get",params:e})}function n(e){return Object(r["b"])({url:s.all,method:"get",params:e})}function o(e){return Object(r["b"])({url:s.add,method:"post",data:e})}function d(e){return Object(r["b"])({url:s.edit,method:"post",data:e})}function l(e){return Object(r["b"])({url:s.delete,method:"post",data:e})}},fecbd:function(e,t,a){"use strict";a.r(t);a("b0c0"),a("99af"),a("caad"),a("2532");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("a-spin",{attrs:{spinning:e.isLoading}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",{staticClass:"row-item-search"},[t("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[t("a-form-item",{attrs:{label:"关键词"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["searchValue"],expression:"['searchValue']"}],staticStyle:{width:"337px"},attrs:{placeholder:"请输入关键词"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["searchType",{initialValue:10}],expression:"['searchType', { initialValue: 10 }]"}],staticStyle:{width:"100px"},attrs:{slot:"addonBefore"},slot:"addonBefore"},e._l(e.SearchTypeEnum,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1)],1)],1),t("a-form-item",{attrs:{label:"订单来源"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["orderSource",{initialValue:-1}],expression:"['orderSource', { initialValue: -1 }]"}]},[t("a-select-option",{attrs:{value:-1}},[e._v("全部")]),e._l(e.OrderSourceEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])}))],2)],1),t("a-form-item",{attrs:{label:"支付方式"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["payMethod",{initialValue:""}],expression:"['payMethod', { initialValue: '' }]"}]},[t("a-select-option",{attrs:{value:""}},[e._v("全部")]),e._l(e.PaymentMethodEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])}))],2)],1),t("a-form-item",{attrs:{label:"配送方式"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["deliveryType",{initialValue:-1}],expression:"['deliveryType', { initialValue: -1 }]"}]},[t("a-select-option",{attrs:{value:-1}},[e._v("全部")]),e._l(e.DeliveryTypeEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])}))],2)],1),t("a-form-item",{attrs:{label:"下单时间"}},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime"],expression:"['betweenTime']"}],attrs:{format:"YYYY-MM-DD"}})],1),t("a-form-item",{attrs:{label:"自提门店"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["extractShopId",{initialValue:0}],expression:"['extractShopId', { initialValue: 0 }]"}]},[t("a-select-option",{attrs:{value:0}},[e._v("全部")]),e._l(e.shopList,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.shop_id}},[e._v(e._s(a.shop_name))])}))],2)],1),t("a-form-item",{staticClass:"search-btn"},[t("a-button",{staticClass:"mr-15",attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")]),t("a-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1)],1)],1),t("div",{staticClass:"ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered"},[t("div",{staticClass:"ant-table-content"},[t("div",{staticClass:"ant-table-scroll"},[t("div",{staticClass:"ant-table-body",staticStyle:{"overflow-x":"scroll"}},[t("table",{staticStyle:{width:"1450px"}},[t("thead",{staticClass:"ant-table-thead"},[t("tr",e._l(e.columns,(function(a,r){return t("th",{key:r},[t("span",{staticClass:"ant-table-header-column"},[t("div",[t("span",{staticClass:"ant-table-column-title"},[e._v(e._s(a.title))])])])])})),0)]),t("tbody",{staticClass:"ant-table-tbody"},[e._l(e.orderList.data,(function(a){return[t("tr",{key:"order_".concat(a.order_id,"_1"),staticClass:"order-empty"},[t("td",{attrs:{colspan:"8"}})]),t("tr",{key:"order_".concat(a.order_id,"_2")},[t("td",{attrs:{colspan:"8"}},[t("span",{staticClass:"mr-20"},[e._v(e._s(a.create_time))]),t("span",{staticClass:"mr-20"},[e._v("订单号："+e._s(a.order_no))]),t("platform-icon",{attrs:{name:a.platform,showTips:!0}})],1)]),e._l(a.goods,(function(r,s){return t("tr",{key:"orderGoods_".concat(a.order_id,"_").concat(s)},[t("td",[t("GoodsItem",{attrs:{data:{image:r.goods_image,imageAlt:"商品图片",title:r.goods_name,goodsProps:r.goods_props}}})],1),t("td",[t("p",[e._v("￥"+e._s(r.goods_price))]),t("p",[e._v("×"+e._s(r.total_num))])]),0===s?[t("td",{attrs:{rowspan:a.goods.length}},[t("p",[e._v("￥"+e._s(a.pay_price))]),t("p",{staticClass:"c-muted-1"},[e._v("(含运费：￥"+e._s(a.express_price)+")")])]),t("td",{attrs:{rowspan:a.goods.length}},[t("UserItem",{attrs:{user:a.user}})],1),t("td",{attrs:{rowspan:a.goods.length}},[a.pay_method?t("a-tag",[e._v(e._s(e.PaymentMethodEnum[a.pay_method].name))]):t("span",[e._v("--")])],1),t("td",{attrs:{rowspan:a.goods.length}},[t("a-tag",[e._v(e._s(e.DeliveryTypeEnum[a.delivery_type].name))])],1),t("td",{attrs:{rowspan:a.goods.length}},[t("p",{staticClass:"mtb-2"},[t("span",{staticClass:"f-13"},[e._v("付款状态：")]),t("a-tag",{attrs:{color:a.pay_status==e.PayStatusEnum.SUCCESS.value?"green":""}},[e._v(e._s(e.PayStatusEnum[a.pay_status].name))])],1),t("p",{staticClass:"mtb-2"},[t("span",{staticClass:"f-13"},[e._v("发货状态：")]),t("a-tag",{attrs:{color:a.delivery_status==e.DeliveryStatusEnum.DELIVERED.value?"green":""}},[e._v(e._s(e.DeliveryStatusEnum[a.delivery_status].name))])],1),t("p",{staticClass:"mtb-2"},[t("span",{staticClass:"f-13"},[e._v("收货状态：")]),t("a-tag",{attrs:{color:a.receipt_status==e.ReceiptStatusEnum.RECEIVED.value?"green":""}},[e._v(e._s(e.ReceiptStatusEnum[a.receipt_status].name))])],1),[e.OrderStatusEnum.CANCELLED.value,e.OrderStatusEnum.APPLY_CANCEL.value].includes(a.order_status)?t("p",{staticClass:"mtb-2"},[t("span",{staticClass:"f-13"},[e._v("订单状态：")]),t("a-tag",{attrs:{color:e.renderOrderStatusColor(a.order_status)}},[e._v(e._s(e.OrderStatusEnum[a.order_status].name))])],1):e._e()]),t("td",{attrs:{rowspan:a.goods.length}},[t("div",{staticClass:"actions"},[e.$auth("/order/detail")?t("router-link",{attrs:{to:{path:"/order/detail",query:{orderId:a.order_id}},target:"_blank"}},[e._v("详情")]):e._e(),a.pay_status==e.PayStatusEnum.SUCCESS.value&&e.inArray(a.delivery_type,[e.DeliveryTypeEnum.EXPRESS.value,e.DeliveryTypeEnum.NOTHING.value])&&a.delivery_status!=e.DeliveryStatusEnum.DELIVERED.value&&!e.inArray(a.order_status,[e.OrderStatusEnum.CANCELLED.value,e.OrderStatusEnum.APPLY_CANCEL.value])?t("a",{directives:[{name:"action",rawName:"v-action:deliver",arg:"deliver"}],on:{click:function(t){return e.handleDelivery(a)}}},[e._v("发货")]):e._e(),a.pay_status!=e.PayStatusEnum.SUCCESS.value||a.delivery_type!=e.DeliveryTypeEnum.EXTRACT.value||a.delivery_status==e.DeliveryStatusEnum.DELIVERED.value||e.inArray(a.order_status,[e.OrderStatusEnum.CANCELLED.value,e.OrderStatusEnum.APPLY_CANCEL.value])?e._e():t("a",{directives:[{name:"action",rawName:"v-action:extract",arg:"extract"}],on:{click:function(t){return e.handleExtract(a)}}},[e._v("自提核销")]),a.order_status==e.OrderStatusEnum.APPLY_CANCEL.value?t("a",{directives:[{name:"action",rawName:"v-action:cancel",arg:"cancel"}],on:{click:function(t){return e.handleCancel(a)}}},[e._v("审核取消")]):e._e(),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(a)}}},[e._v("删除")])],1)])]:e._e()],2)}))]}))],2)])]),e.orderList.data.length?e._e():t("a-empty",{attrs:{image:e.simpleImage}})],1)])]),e.orderList.data.length?t("div",{staticClass:"pagination"},[t("a-pagination",{attrs:{current:e.page,pageSize:e.orderList.per_page,total:e.orderList.total},on:{change:e.onChangePage}})],1):e._e(),t("DeliveryForm",{ref:"DeliveryForm",on:{handleSubmit:e.handleRefresh}}),t("ExtractForm",{ref:"ExtractForm",on:{handleSubmit:e.handleRefresh}}),t("CancelForm",{ref:"CancelForm",on:{handleSubmit:e.handleRefresh}})],1)],1)},s=[],i=a("ade3"),n=a("5530"),o=(a("06f4"),a("fc25")),d=(a("d3b7"),a("ca00")),l=a("3a10"),c=a("884a"),u=a("3858"),m=a("8d5f"),p=a("ab09"),v=a("4a95"),f=a("3c76"),h=a("ac82"),_=[{title:"商品信息",align:"center",dataIndex:"goods",scopedSlots:{customRender:"goods"}},{title:"单价/数量",align:"center",scopedSlots:{customRender:"unit_price"}},{title:"实付款",align:"center",dataIndex:"pay_price",scopedSlots:{customRender:"pay_price"}},{title:"买家",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"支付方式",dataIndex:"pay_method",scopedSlots:{customRender:"pay_method"}},{title:"配送方式",dataIndex:"delivery_type",scopedSlots:{customRender:"delivery_type"}},{title:"交易状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],b=[{name:"订单号",value:10},{name:"会员昵称",value:20},{name:"会员ID",value:30},{name:"收货人姓名",value:40},{name:"收货人电话",value:50}],g={name:"Index",components:{PlatformIcon:m["a"],GoodsItem:p["a"],UserItem:p["c"],DeliveryForm:h["b"],ExtractForm:h["c"],CancelForm:h["a"]},data:function(){return{dataType:this.getDataType(),searchForm:this.$form.createForm(this),queryParam:{},isLoading:!1,columns:_,page:1,orderList:{data:[],total:0,per_page:10},shopList:[]}},beforeCreate:function(){Object(d["a"])(this,{inArray:d["e"],DeliveryStatusEnum:v["a"],DeliveryTypeEnum:v["b"],OrderSourceEnum:v["c"],OrderStatusEnum:v["d"],PayStatusEnum:v["f"],ReceiptStatusEnum:v["g"],PaymentMethodEnum:f["a"],SearchTypeEnum:b,simpleImage:o["a"].PRESENTED_IMAGE_SIMPLE})},watch:{$route:function(){this.init()}},created:function(){this.init(),this.getShopList()},methods:{init:function(){this.dataType=this.getDataType(),this.searchForm.resetFields(),this.queryParam={},this.handleRefresh(!0)},getShopList:function(){var e=this;u["b"]().then((function(t){e.shopList=t.data.list}))},getDataType:function(){return this.$route.path.split("/")[3]},getList:function(){var e=this,t=this.dataType,a=this.queryParam,r=this.page;return this.isLoading=!0,l["b"](Object(n["a"])(Object(n["a"])({dataType:t},a),{},{page:r})).then((function(t){e.orderList=t.data.list})).finally((function(){e.isLoading=!1}))},renderOrderStatusColor:function(e){var t,a=this.OrderStatusEnum,r=(t={},Object(i["a"])(t,a.NORMAL.value,""),Object(i["a"])(t,a.CANCELLED.value,"red"),Object(i["a"])(t,a.APPLY_CANCEL.value,"red"),Object(i["a"])(t,a.COMPLETED.value,"green"),t);return r[e]},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e&&(this.page=1),this.getList()},handleSearch:function(e){var t=this;e.preventDefault(),this.searchForm.validateFields((function(e,a){e||(t.queryParam=Object(n["a"])(Object(n["a"])({},t.queryParam),a),t.handleRefresh(!0))}))},handleReset:function(){this.searchForm.resetFields()},onChangePage:function(e){this.page=e,this.handleRefresh()},handleDelete:function(e){var t=this,a=t.$confirm({title:"您确定要删除该订单记录吗?",content:"删除后不可恢复，请谨慎操作",onOk:function(){return c["b"](e.order_id).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleDelivery:function(e){this.$refs.DeliveryForm.show(e)},handleExtract:function(e){this.$refs.ExtractForm.show(e)},handleCancel:function(e){this.$refs.CancelForm.show(e)}}},y=g,E=(a("73db"),a("2877")),C=Object(E["a"])(y,r,s,!1,null,"6cdd5c96",null);t["default"]=C.exports}}]);