<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\dealer;

use think\response\Json;
use app\api\controller\Controller;
use app\api\model\dealer\User as DealerUserModel;
use app\api\model\dealer\Apply as DealerApplyModel;
use app\api\service\User as UserService;

/**
 * 分销商申请
 * Class Apply
 * @package app\api\controller\user\dealer
 */
class Apply extends Controller
{
    /**
     * 分销商申请状态
     * @return Json
     * @throws \cores\exception\BaseException
     */
    public function status(): Json
    {
        return $this->renderSuccess([
            // 当前是否为分销商
            'isDealer' => DealerUserModel::isDealerUser(UserService::getCurrentLoginUserId()),
            // 当前是否在申请中
            'isApplying' => DealerApplyModel::isApplying(UserService::getCurrentLoginUserId()),
            // 推荐人昵称
            'refereeName' => DealerUserModel::getRefereeName(),
        ]);
    }

    /**
     * 提交分销商申请
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function submit(): Json
    {
        $model = new DealerApplyModel;
        if ($model->submit($this->postForm())) {
            return $this->renderSuccess('分销商申请已提交');
        }
        return $this->renderError($model->getError() ?: '提交失败');
    }
}