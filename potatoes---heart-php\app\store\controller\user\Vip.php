<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\user;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\xj\Vip as ViprModel;

/**
 * 余额记录
 * Class Vip
 * @package app\store\controller\user
 */
class Vip extends Controller
{
    /**
     * 充值记录
     * @return Json
     */
    public function order(): Json
    {
        $model = new ViprModel;
        $list = $model->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }
     public function edit(int $orderId): Json
    {
        // 会员等级详情
        $model = ViprModel::detail($orderId);
        // 更新记录
        if ($model->edit($this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }
}
