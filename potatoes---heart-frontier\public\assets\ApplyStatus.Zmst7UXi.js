import{$ as e,E as a}from"./index-DfvVnKvn.js";const s="dealer.withdraw/list",l="dealer.withdraw/submit",n=a=>e.get(s,a),m=a=>e.post(l,a),t=new a([{key:"WECHAT",name:"微信零钱",value:10},{key:"ALIPAY",name:"支付宝",value:20},{key:"BANK_CARD",name:"银行卡",value:30}]),A=new a([{key:"WAIT",name:"待审核",value:10},{key:"PASSED",name:"审核通过",value:20},{key:"REJECT",name:"驳回",value:30},{key:"PAYMENT",name:"已打款",value:40}]);export{A,t as P,n as l,m as s};
