<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\api\model\xj;

use app\api\service\User as UserService;
use app\common\model\xj\Message as MessageModel;
use cores\exception\BaseException;
use think\facade\Db;

/**
 * 商品评价模型
 * Class Message
 * @package app\api\model
 */
class Message extends MessageModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'is_delete',
        'store_id',
        'update_time',
    ];

    public function getCreateTimeAttr($value): string
    {
        return Date('Y/m/d', $value);
    }

    public static function getNew()
    {
        $user = UserService::getCurrentLoginUser(false);
        if ($user) {
            $detail = parent::detail(['user_id' => $user['user_id'], 'is_read' => 0]);
        } else {
            $detail = false;
        }

        return $detail;
    }

    /**
     * 获取文章详情
     * @param int $articleId 文章ID
     * @return Message|array|null
     * @throws \cores\exception\BaseException
     */
    public static function getDetail(int $articleId)
    {
        // 获取文章详情
        $detail = parent::detail($articleId, ['image']);
        if (empty($detail) || $detail['is_delete']) {
            throwError('很抱歉，当前视频不存在');
        }
        // 累积文章实际阅读数
        // static::setIncActualViews($articleId);
        return $detail;
    }

    /**
     * 累积文章实际阅读数
     * @param int $articleId 文章ID
     * @return void
     */
    private static function setIncActualViews(int $articleId): void
    {
        (new static )->setInc($articleId, 'actual_views', 1);
    }

    /**
     * 获取文章列表
     * @param int $categoryId
     * @param int $limit
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getList(int $limit = 10): \think\Paginator
    {
        // 检索查询条件
        $filter = [];
        $userId = UserService::getCurrentLoginUserId();
        // 获取列表数据
        $list = $this
            ->where($filter)
            ->where('user_id', '=', $userId)
          
            ->order(['create_time' => 'desc'])
            ->paginate($limit);
        return $list;
    }

    public function getListDetail(int $categoryId = 0, int $videoId, $page = 1, int $limit = 10)
    {
        // 检索查询条件
        $filter                      = [];
        $categoryId > 0 && $filter[] = ['category_id', '=', $categoryId];
        $first                       = $this

            ->where('status', '=', 1)
            ->where('is_delete', '=', 0)
            ->where('id', '=', $videoId)
            ->find();

        $start = ($page - 1) * $limit;
        if ($page == 1) {
            $limit = 9;
        }
        // $end   = $page * $limit;
        $list = $this
            ->where($filter)
            ->where('status', '=', 1)
            ->where('is_delete', '=', 0)
            ->where('id', '<>', $videoId)
            ->order(['sort' => 'asc', 'create_time' => 'desc'])
            ->limit($start, $limit)
            ->select()->toArray();
        if ($page == 1) {

            $list = array_merge([$first], $list);
        }
        return $list;
    }
}
