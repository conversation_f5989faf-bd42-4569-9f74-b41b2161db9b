<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\dealer;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\dealer\OrderVip as OrderVipModel;

/**
 * 分销订单
 * Class OrderVip
 * @package app\store\controller\apps\dealer
 */
class OrderVip extends Controller
{
    /**
     * 分销订单列表
     * @return Json
     */
    public function list(): Json
    {
        $model = new OrderVipModel;
        $list = $model->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }
     /**
     * 提现审核
     * @param int $id
     * @return Json
     */
    public function audit(int $id): Json
    {
        $model = OrderVipModel::detail(['id'=>$id]);
        if ($model->audit($this->postForm())) {
            return $this->renderSuccess('操作成功');
        }
        return $this->renderError($model->getError() ?: '操作失败');
    }

    /**
     * 确认已打款
     * @param int $id
     * @return Json
     */
    public function payed(int $id): Json
    {
        // 提现记录详情
        $model = OrderVipModel::detail(['id'=>$id]);
        // 验证已冻结佣金是否合法
        if (!$model->verifyUserFreezeMoney($model['user_id'], (float)$model['money'])) {
            return $this->renderError($model->getError());
        }
        // 确认已打款
        if ($model->payed()) {
            return $this->renderSuccess('操作成功');
        }
        return $this->renderError($model->getError() ?: '操作失败');
    }

    /**
     * 分销商提现：微信商家转账到零钱
     * 需满足条件才可使用该API
     * 1.必须是微信小程序用户(存在openid)
     * 2.申请提现时选择的打款方式为微信
     * 3.小程序必须有商家转账到零钱的API权限 详情: https://pay.weixin.qq.com/wiki/doc/apiv3/open/pay/chapter4_3_1.shtml
     * @param int $id 提现记录ID
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function wechatPay(int $id): Json
    {
      $model = OrderVipModel::detail(['id'=>$id]);
        // 验证已冻结佣金是否合法
        if (!$model->verifyUserFreezeMoney($model['user_id'], (float)$model['money'])) {
            return $this->renderError($model->getError());
        }
        // 确认打款
        if ($model->wechatPay()) {
            return $this->renderSuccess('操作成功，已提交微信转账申请，具体结果请登录微信支付商户平台中查看');
        }
        return $this->renderError($model->getError() ?: '操作失败');
    }
}