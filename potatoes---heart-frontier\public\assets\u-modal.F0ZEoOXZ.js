import{v as e,o as t,c as o,w as l,a,n,f as s,t as u,b as i,B as d,d as r,F as c,i as m}from"./index-DAm19nhc.js";import{_ as p}from"./u-loading.DgJsa0sr.js";import{r as f}from"./uni-app.es.CS65jdCG.js";import{_ as y}from"./u-popup.D5lrqP5U.js";import{_ as h}from"./_plugin-vue_export-helper.BCo6x5W8.js";const _=h({name:"u-modal",emits:["update:modelValue","input","confirm","cancel"],props:{value:{type:Boolean,default:!1},modelValue:{type:Boolean,default:!1},zIndex:{type:[Number,String],default:""},title:{type:[String],default:"提示"},width:{type:[Number,String],default:600},content:{type:String,default:"内容"},showTitle:{type:Boolean,default:!0},showConfirmButton:{type:Boolean,default:!0},showCancelButton:{type:<PERSON><PERSON>an,default:!1},confirmText:{type:String,default:"确认"},cancelText:{type:String,default:"取消"},confirmColor:{type:String,default:"#2979ff"},cancelColor:{type:String,default:"#606266"},borderRadius:{type:[Number,String],default:16},titleStyle:{type:Object,default:()=>({})},contentStyle:{type:Object,default:()=>({})},cancelStyle:{type:Object,default:()=>({})},confirmStyle:{type:Object,default:()=>({})},zoom:{type:Boolean,default:!0},asyncClose:{type:Boolean,default:!1},maskCloseAble:{type:Boolean,default:!1},negativeTop:{type:[String,Number],default:0},blur:{type:[Number,String],default:0}},data:()=>({loading:!1,popupValue:!1}),computed:{valueCom(){return this.modelValue},cancelBtnStyle(){return Object.assign({color:this.cancelColor},this.cancelStyle)},confirmBtnStyle(){return Object.assign({color:this.confirmColor},this.confirmStyle)},uZIndex(){return this.zIndex?this.zIndex:this.$u.zIndex.popup}},watch:{valueCom:{immediate:!0,handler(e){!0===e&&(this.loading=!1),this.popupValue=e}}},methods:{confirm(){this.asyncClose?this.loading=!0:(this.$emit("input",!1),this.$emit("update:modelValue",!1)),this.$emit("confirm")},cancel(){this.$emit("cancel"),this.$emit("input",!1),this.$emit("update:modelValue",!1),setTimeout((()=>{this.loading=!1}),300)},popupClose(){this.$emit("input",!1),this.$emit("update:modelValue",!1)},clearLoading(){this.loading=!1}}},[["render",function(h,_,b,g,C,S){const v=m,B=f(e("u-loading"),p),k=f(e("u-popup"),y);return t(),o(v,null,{default:l((()=>[a(k,{blur:b.blur,zoom:b.zoom,mode:"center",popup:!1,"z-index":S.uZIndex,modelValue:C.popupValue,"onUpdate:modelValue":_[0]||(_[0]=e=>C.popupValue=e),length:b.width,"mask-close-able":b.maskCloseAble,"border-radius":b.borderRadius,onClose:S.popupClose,"negative-top":b.negativeTop},{default:l((()=>[a(v,{class:"u-model"},{default:l((()=>[b.showTitle?(t(),o(v,{key:0,class:"u-model__title u-line-1",style:n([b.titleStyle])},{default:l((()=>[s(u(b.title),1)])),_:1},8,["style"])):i("",!0),a(v,{class:"u-model__content"},{default:l((()=>[h.$slots.default||h.$slots.$default?(t(),o(v,{key:0,style:n([b.contentStyle])},{default:l((()=>[d(h.$slots,"default",{},void 0,!0)])),_:3},8,["style"])):(t(),o(v,{key:1,class:"u-model__content__message",style:n([b.contentStyle])},{default:l((()=>[s(u(b.content),1)])),_:1},8,["style"]))])),_:3}),b.showCancelButton||b.showConfirmButton?(t(),o(v,{key:1,class:"u-model__footer u-border-top"},{default:l((()=>[b.showCancelButton?(t(),o(v,{key:0,"hover-stay-time":100,"hover-class":"u-model__btn--hover",class:"u-model__footer__button",style:n([S.cancelBtnStyle]),onClick:S.cancel},{default:l((()=>[s(u(b.cancelText),1)])),_:1},8,["style","onClick"])):i("",!0),b.showConfirmButton||h.$slots["confirm-button"]?(t(),o(v,{key:1,"hover-stay-time":100,"hover-class":b.asyncClose?"none":"u-model__btn--hover",class:"u-model__footer__button hairline-left",style:n([S.confirmBtnStyle]),onClick:S.confirm},{default:l((()=>[h.$slots["confirm-button"]?d(h.$slots,"confirm-button",{key:0},void 0,!0):(t(),r(c,{key:1},[C.loading?(t(),o(B,{key:0,mode:"circle",color:b.confirmColor},null,8,["color"])):(t(),r(c,{key:1},[s(u(b.confirmText),1)],64))],64))])),_:3},8,["hover-class","style","onClick"])):i("",!0)])),_:3})):i("",!0)])),_:3})])),_:3},8,["blur","zoom","z-index","modelValue","length","mask-close-able","border-radius","onClose","negative-top"])])),_:3})}],["__scopeId","data-v-4aed7a24"]]);export{_};
