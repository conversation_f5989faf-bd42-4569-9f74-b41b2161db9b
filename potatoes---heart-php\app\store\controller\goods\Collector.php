<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\goods;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\goods\Collector as CollectorModel;
use cores\exception\BaseException;

/**
 * 商品采集管理
 * Class Collector
 * @package app\store\controller\goods
 */
class Collector extends Controller
{
    /**
     * 获取列表记录
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function list(): Json
    {
        $model = new CollectorModel;
        $list = $model->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 执行商品采集
     * @return Json
     * @throws BaseException
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function batch(): Json
    {
        // 新增记录
        $model = new CollectorModel;
        if ($model->batch($this->postData())) {
            return $this->renderSuccess('已添加到采集任务中，请在采集记录中查看结果');
        }
        return $this->renderError($model->getError() ?: '操作失败');
    }

    /**
     * 删除记录
     * @param int $id
     * @return Json
     */
    public function delete(int $id): Json
    {
        $model = CollectorModel::detail($id);
        if (!$model->setDelete()) {
            return $this->renderError($model->getError() ?: '删除失败');
        }
        return $this->renderSuccess('删除成功');
    }
}
