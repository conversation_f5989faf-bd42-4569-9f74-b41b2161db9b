import{c as e,w as a,n as s,b as t,i as o,o as l,a as d,f as i,t as c,d as n,e as r,F as m,k as u,g as f,l as g,a4 as p}from"./index-BI5vpG2u.js";import{a as h}from"./RefundType.g7iLwISV.js";import{i as _}from"./upload.D-wXB-_d.js";import{g as b,b as x}from"./refund.DA8er2wL.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";const w=y({data:()=>({RefundTypeEnum:h,isLoading:!0,orderGoodsId:null,goods:{},formData:{images:[],type:10,content:""},imageList:[],maxImageLength:6,disabled:!1}),onLoad({orderGoodsId:e}){this.orderGoodsId=e,this.getGoodsDetail()},methods:{getGoodsDetail(){const e=this;e.isLoading=!0,b(e.orderGoodsId).then((a=>{e.goods=a.data.goods,e.isLoading=!1}))},onSwitchService(e){this.formData.type=e},chooseImage(){const e=this,a=e.imageList;uni.chooseImage({count:6-a.length,sizeType:["original","compressed"],sourceType:["album","camera"],success({tempFiles:s}){e.imageList=a.concat(s)}})},deleteImage(e){this.imageList.splice(e,1)},handleSubmit(){const e=this,{imageList:a}=e;return!0!==e.disabled&&(e.formData.content.trim().length?(e.disabled=!0,void(a.length>0?e.uploadFile().then((()=>e.onSubmit())).catch((a=>{e.disabled=!1,0!==a.statusCode&&e.$toast(a.errMsg),console.log("err",a)})):e.onSubmit())):(e.$toast("请填写申请原因"),!1))},onSubmit(){const e=this;x(e.orderGoodsId,e.formData).then((a=>{e.$toast(a.message),setTimeout((()=>{e.disabled=!1,uni.navigateBack()}),1500)})).catch((a=>e.disabled=!1))},uploadFile(){const e=this,{imageList:a}=e;return new Promise(((s,t)=>{a.length>0?_(a).then((a=>{e.formData.images=a,s(a)})).catch(t):s()}))}}},[["render",function(h,_,b,x,y,w){const k=f,v=o,L=g,I=p;return y.isLoading?t("",!0):(l(),e(v,{key:0,class:"container",style:s(h.appThemeStyle)},{default:a((()=>[d(v,{class:"goods-detail b-f dis-flex flex-dir-row"},{default:a((()=>[d(v,{class:"left"},{default:a((()=>[d(k,{class:"goods-image",src:y.goods.goods_image},null,8,["src"])])),_:1}),d(v,{class:"right dis-flex flex-box flex-dir-column flex-x-around"},{default:a((()=>[d(v,{class:"goods-name"},{default:a((()=>[d(L,{class:"twoline-hide"},{default:a((()=>[i(c(y.goods.goods_name),1)])),_:1})])),_:1}),d(v,{class:"dis-flex col-9 f-24"},{default:a((()=>[d(v,{class:"flex-box"},{default:a((()=>[d(v,{class:"goods-props clearfix"},{default:a((()=>[(l(!0),n(m,null,r(y.goods.goods_props,((s,t)=>(l(),e(v,{class:"goods-props-item",key:t},{default:a((()=>[d(L,null,{default:a((()=>[i(c(s.value.name),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1}),d(L,{class:"t-r"},{default:a((()=>[i("×"+c(y.goods.total_num),1)])),_:1})])),_:1})])),_:1})])),_:1}),d(v,{class:"row-service b-f m-top20"},{default:a((()=>[d(v,{class:"row-title"},{default:a((()=>[i("服务类型")])),_:1}),d(v,{class:"service-switch dis-flex"},{default:a((()=>[(l(!0),n(m,null,r(y.RefundTypeEnum.data,((s,t)=>(l(),e(v,{class:u(["switch-item",{active:y.formData.type==s.value}]),key:t,onClick:e=>w.onSwitchService(s.value)},{default:a((()=>[i(c(s.name),1)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1}),d(v,{class:"row-textarea b-f m-top20"},{default:a((()=>[d(v,{class:"row-title"},{default:a((()=>[i("申请原因")])),_:1}),d(v,{class:"content"},{default:a((()=>[d(I,{class:"textarea",modelValue:y.formData.content,"onUpdate:modelValue":_[0]||(_[0]=e=>y.formData.content=e),maxlength:"2000",placeholder:"请详细填写申请原因，注意保持商品的完好，建议您先与卖家沟通",placeholderStyle:"color:#ccc"},null,8,["modelValue"])])),_:1})])),_:1}),y.formData.type==y.RefundTypeEnum.RETURN.value?(l(),e(v,{key:0,class:"row-money b-f m-top20 dis-flex"},{default:a((()=>[d(v,{class:"row-title"},{default:a((()=>[i("退款金额")])),_:1}),d(v,{class:"money col-m"},{default:a((()=>[i("￥"+c(y.goods.total_pay_price),1)])),_:1})])),_:1})):t("",!0),d(v,{class:"row-voucher b-f m-top20"},{default:a((()=>[d(v,{class:"row-title"},{default:a((()=>[i("上传凭证 (最多6张)")])),_:1}),d(v,{class:"image-list"},{default:a((()=>[(l(!0),n(m,null,r(y.imageList,((s,t)=>(l(),e(v,{class:"image-preview",key:t},{default:a((()=>[d(L,{class:"image-delete iconfont icon-shanchu",onClick:e=>w.deleteImage(t)},null,8,["onClick"]),d(k,{class:"image",mode:"aspectFill",src:s.path},null,8,["src"])])),_:2},1024)))),128)),y.imageList.length<y.maxImageLength?(l(),e(v,{key:0,class:"image-picker",onClick:_[1]||(_[1]=e=>w.chooseImage())},{default:a((()=>[d(L,{class:"choose-icon iconfont icon-camera"}),d(L,{class:"choose-text"},{default:a((()=>[i("上传图片")])),_:1})])),_:1})):t("",!0)])),_:1})])),_:1}),d(v,{class:"footer-fixed"},{default:a((()=>[d(v,{class:"btn-wrapper"},{default:a((()=>[d(v,{class:u(["btn-item btn-item-main",{disabled:y.disabled}]),onClick:_[2]||(_[2]=e=>w.handleSubmit())},{default:a((()=>[i("确认提交")])),_:1},8,["class"])])),_:1})])),_:1})])),_:1},8,["style"]))}],["__scopeId","data-v-e07a50e0"]]);export{w as default};
