<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model;

/**
 * 满额包邮活动模型
 * Class FullFree
 * @package app\common\model
 */
class FullFree
{
    // 支持的优惠叠加
    protected static array $stackingData = ['coupon', 'full-discount', 'points'];

    /**
     * 获取禁用的叠加优惠
     * @param array $stacking 满额包邮设置的叠加优惠
     * @return array|string[]
     */
    public static function getDisabledStacking(array $stacking): array
    {
        return \array_diff(self::$stackingData, $stacking);
    }

    /**
     * 判断是否支持订单结算中的叠加优惠
     * @param array $stacking 满额包邮设置的叠加优惠
     * @param array $enabledStacking 订单中启用的叠加优惠
     * @return bool
     */
    public static function checkOrderStacking(array $stacking, array $enabledStacking): bool
    {
        // 禁用的叠加优惠方式
        $disabled = self::getDisabledStacking($stacking);
        // 判断是否支持订单结算中的叠加优惠
        return empty(\array_intersect($disabled, $enabledStacking));
    }
}
