<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\api\model\xj;

use app\api\service\User as UserService;
use app\common\model\User as UserModel;
use app\common\model\xj\Video as VideoModel;
use cores\exception\BaseException;
use think\facade\Db;

/**
 * 商品评价模型
 * Class Video
 * @package app\api\model
 */
class Video extends VideoModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'virtual_views',
        'actual_views',
        'is_delete',
        'store_id',
        'create_time',
        'update_time',
    ];

    /**
     * 获取器：文章详情HTML实体转换回普通字符
     * @param $value
     * @return string
     */
    public function getVideoContentAttr($value): string
    {
        return htmlspecialchars_decode($value);
    }

    public function addView(int $articleId)
    {
        // 当前用户ID
        $userInfo = UserService::getCurrentLoginUser();

        $userId = $userInfo['user_id'];

        //是否观看-24小时
        $time = time() - 86400;
        $log  = Db::name('xj_video_log')
            ->where('create_time', '>', $time)->where('video_id', $articleId)->where('user_id', $userId)->find();

        //查询会员等级
        $user = Db::name('user')->where('user_id', $userId)->where('vip_endtime', '>=', time())->find();
        if ($userInfo['grade_id'] == 10007) {
            //普通会员
            $weight = Db::name('user_grade')->where('grade_id', $userInfo['grade_id'])->value('weight');
            $name   = 'points' . $weight;
            $points = $this[$name];
            //增加积分
            $data['value']       = $points;
            $data['store_id']    = self::$storeId;
            $data['create_time'] = time();
            $data['date']        = Date('Y-m-d');
            $data['user_id']     = $userId;
            $data['video_id']    = $articleId;
            $data['video_name']  = $this['title'];
            Db::name('xj_video_log')->insert($data);

            $describe = "观看视频：{$this['title']}";
            UserModel::setIncPoints($userId, $points, $describe);
            $score = $points;
            
        } elseif (! $log && $user) {
            $weight = Db::name('user_grade')->where('grade_id', $user['grade_id'])->value('weight');
            $name   = 'points' . $weight;
            $points = $this[$name];
            //增加积分
            $data['value']       = $points;
            $data['store_id']    = self::$storeId;
            $data['create_time'] = time();
            $data['date']        = Date('Y-m-d');
            $data['user_id']     = $userId;
            $data['video_id']    = $articleId;
            $data['video_name']  = $this['title'];
            Db::name('xj_video_log')->insert($data);

            $describe = "观看视频：{$this['title']}";
            UserModel::setIncPoints($userId, $points, $describe);
            $score = $points;

        } else {
            $score = 0;
        }

        // 累积文章实际阅读数
        static::setIncActualViews($articleId);
        return $score;
    }

    /**
     * 获取文章详情
     * @param int $articleId 文章ID
     * @return Video|array|null
     * @throws \cores\exception\BaseException
     */
    public static function getDetail(int $articleId)
    {
        // 获取文章详情
        $detail = parent::detail($articleId, ['image']);
        if (empty($detail) || $detail['is_delete']) {
            throwError('很抱歉，当前视频不存在');
        }
        // 累积文章实际阅读数
        // static::setIncActualViews($articleId);
        return $detail;
    }

    /**
     * 累积文章实际阅读数
     * @param int $articleId 文章ID
     * @return void
     */
    private static function setIncActualViews(int $articleId): void
    {
        (new static )->setInc($articleId, 'actual_views', 1);
    }

    /**
     * 获取文章列表
     * @param int $categoryId
     * @param int $limit
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getList(int $categoryId = 0, int $limit = 10): \think\Paginator
    {
        // 检索查询条件
        $filter                      = [];
        $categoryId > 0 && $filter[] = ['category_id', '=', $categoryId];
        // 获取列表数据
        $list = $this->withoutField(['content'])
            ->where($filter)
            ->where('status', '=', 1)
            ->where('is_delete', '=', 0)
            ->order(['sort' => 'asc', 'create_time' => 'desc'])
            ->paginate($limit);
        return static::preload($list, ['image', 'category']);
    }

    public function getListDetail(int $categoryId = 0, int $videoId, $page = 1, int $limit = 10)
    {
        // 检索查询条件
        $filter                      = [];
        $categoryId > 0 && $filter[] = ['category_id', '=', $categoryId];
        $first                       = $this

            ->where('status', '=', 1)
            ->where('is_delete', '=', 0)
            ->where('id', '=', $videoId)
            ->find();

        $start = ($page - 1) * $limit;
        if ($page == 1) {
            $limit = 9;
        }
        // $end   = $page * $limit;
        $list = $this
            ->where($filter)
            ->where('status', '=', 1)
            ->where('is_delete', '=', 0)
            ->where('id', '<>', $videoId)
            ->order(['sort' => 'asc', 'create_time' => 'desc'])
            ->limit($start, $limit)
            ->select()->toArray();
        if ($page == 1) {

            $list = array_merge([$first], $list);
        }
        return $list;
    }
}
