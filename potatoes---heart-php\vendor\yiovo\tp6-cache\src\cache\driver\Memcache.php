<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2025 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace yiovo\cache\cache\driver;

use DateInterval;
use DateTimeInterface;
use yiovo\cache\cache\Driver;
use think\exception\InvalidCacheException;

/**
 * Memcache缓存类
 */
class Memcache extends Driver
{
    /**
     * 配置参数
     * @var array
     */
    protected $options = [
        'host'        => '127.0.0.1',
        'port'        => 11211,
        'expire'      => 0,
        'timeout'     => 0,
        'persistent'  => true,
        'prefix'      => '',
        'tag_prefix'  => 'tag:',
        'serialize'   => [],
        'fail_delete' => false,
    ];

    /**
     * 架构函数
     * @access public
     * @param array $options 缓存参数
     * @throws \BadFunctionCallException
     */
    public function __construct(array $options = [])
    {
        if (!extension_loaded('memcache')) {
            throw new \BadFunctionCallException('not support: memcache');
        }

        if (!empty($options)) {
            $this->options = array_merge($this->options, $options);
        }

        $this->handler = new \Memcache;

        // 支持集群
        $hosts = (array) $this->options['host'];
        $ports = (array) $this->options['port'];

        if (empty($ports[0])) {
            $ports[0] = 11211;
        }

        // 建立连接
        foreach ($hosts as $i => $host) {
            $port = $ports[$i] ?? $ports[0];
            $this->options['timeout'] > 0 ?
                $this->handler->addServer($host, (int) $port, $this->options['persistent'], 1, (int) $this->options['timeout']) :
                $this->handler->addServer($host, (int) $port, $this->options['persistent'], 1);
        }
    }

    /**
     * 判断缓存
     * @access public
     * @param string $name 缓存变量名
     * @return bool
     */
    public function has($name): bool
    {
        $key = $this->getCacheKey($name);

        return false !== $this->handler->get($key);
    }

    /**
     * 读取缓存
     * @access public
     * @param string $name    缓存变量名
     * @param mixed  $default 默认值
     * @return mixed
     */
    public function get($name, $default = null): mixed
    {
        $result = $this->handler->get($this->getCacheKey($name));

        try {
            return false !== $result ? $this->unserialize($result) : $this->getDefaultValue($name, $default);
        } catch (InvalidCacheException $e) {
            return $this->getDefaultValue($name, $default, true);
        }
    }

    /**
     * 写入缓存
     * @access public
     * @param string                             $name   缓存变量名
     * @param mixed                              $value  存储数据
     * @param int|DateTimeInterface|DateInterval $expire 有效时间（秒）
     * @return bool
     */
    public function set($name, $value, $expire = null): bool
    {
        if (is_null($expire)) {
            $expire = $this->options['expire'];
        }

        $key    = $this->getCacheKey($name);
        $expire = $this->getExpireTime($expire);
        $value  = $this->serialize($value);

        if ($this->handler->set($key, $value, 0, $expire)) {
            return true;
        }

        return false;
    }

    /**
     * 自增缓存（针对数值缓存）
     * @access public
     * @param string $name 缓存变量名
     * @param int    $step 步长
     * @return false|int
     */
    public function inc($name, $step = 1)
    {
        $key = $this->getCacheKey($name);

        if ($this->handler->get($key)) {
            return $this->handler->increment($key, $step);
        }

        return $this->handler->set($key, $step);
    }

    /**
     * 自减缓存（针对数值缓存）
     * @access public
     * @param string $name 缓存变量名
     * @param int    $step 步长
     * @return false|int
     */
    public function dec($name, $step = 1)
    {
        $key   = $this->getCacheKey($name);
        $value = $this->handler->get($key) - $step;
        $res   = $this->handler->set($key, $value);

        return !$res ? false : $value;
    }

    /**
     * 删除缓存
     * @access public
     * @param string     $name 缓存变量名
     * @param bool|false $ttl
     * @return bool
     */
    public function delete($name, $ttl = false): bool
    {
        $key = $this->getCacheKey($name);

        return false === $ttl ?
            $this->handler->delete($key) :
            $this->handler->delete($key, $ttl);
    }

    /**
     * 清除缓存
     * @access public
     * @return bool
     */
    public function clear(): bool
    {
        return $this->handler->flush();
    }

    /**
     * 删除缓存标签
     * @access public
     * @param array $keys 缓存标识列表
     * @return void
     */
    public function clearTag($keys): void
    {
        foreach ($keys as $key) {
            $this->handler->delete($key);
        }
    }
}
