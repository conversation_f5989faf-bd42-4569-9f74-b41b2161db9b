import{o as e,c as a,r as s,z as o,Z as t,v as l,x as i,w as d,n,i as c,e as u,a as r,f,t as m,b as g,d as p,F as _,l as h,U as k,y as x,j as S}from"./index-b996d08a.js";import{_ as v}from"./mp-html.a2824d73.js";import{r as y}from"./uni-app.es.e82e6f02.js";import{_ as C}from"./u-modal.1cb17301.js";import{W as w}from"./wxofficial.0e36c140.js";import{S as b,a as I,C as T}from"./Comment.37d36275.js";import{C as P}from"./index.75d8b8f9.js";import{h as B}from"./color.813a9497.js";import{p as j,b as L,d as V}from"./task.3bb3a8ea.js";import{G as N}from"./index.65692508.js";import{_ as A}from"./_plugin-vue_export-helper.1b428a4d.js";import{C as D}from"./index.1bbb2eaf.js";import{d as R}from"./index.7178dccf.js";import{t as $}from"./cart.5d8f488a.js";import"./u-loading.6cecf685.js";import"./u-popup.3af38475.js";import"./u-icon.b0b5f2fb.js";import"./comment.4ec7f90e.js";import"./index.c3498ad6.js";const M=A({components:{ShareSheet:b,CustomerBtn:P,SlideImage:I,SkuPopup:A({components:{GoodsSkuPopup:N},emits:["update:modelValue"],props:{modelValue:{Type:Boolean,default:!1},skuMode:{type:Number,default:1},active:{type:Object,default:{}},goods:{type:Object,default:{}}},data:()=>({goodsInfo:{}}),computed:{activedBtnBackgroundColor(){return B(this.appTheme.mainBg,.1)}},created(){const e=this,{goods:a}=e;e.goodsInfo={_id:a.goods_id,name:a.goods_name,goods_thumb:a.goods_image,sku_list:e.getSkuList(),spec_list:e.getSpecList()}},methods:{onChangeValue(e){this.$emit("update:modelValue",e)},getSkuList(){const e=this,{goods:{goods_name:a,goods_image:s,skuList:o}}=e,t=[];return o.forEach((o=>{t.push({_id:o.id,goods_sku_id:o.goods_sku_id,goods_id:o.goods_id,goods_name:a,image:o.image_url?o.image_url:s,price:100*o.goods_price,stock:o.stock_num,spec_value_ids:o.spec_value_ids,sku_name_arr:e.getSkuNameArr(o.spec_value_ids)})})),t},getSkuNameArr(e){const a=this,s=[];return e&&e.forEach(((e,o)=>{const t=a.getSpecValueName(e,o);s.push(t)})),s.length?s:["默认"]},getSpecValueName(e,a){const{goods:{specList:s}}=this;return s[a].valueList.find((a=>a.spec_value_id==e)).spec_value},getSpecList(){const{goods:{specList:e}}=this,a=[];return e.forEach((e=>{const s=[];e.valueList.forEach((e=>{s.push({name:e.spec_value})})),a.push({name:e.spec_name,list:s})})),a.length?a:[{name:"默认",list:[{name:"默认"}]}]},openSkuPopup(){},closeSkuPopup(){},buyNow(e){j({activeId:this.active.active_id,goodsSkuId:e.goods_sku_id}).then((e=>{const a=e.data.taskId;this.$navTo("pages/bargain/task",{taskId:a})})),this.onChangeValue(!1)}}},[["render",function(o,t,l,i,d,n){const c=s("goods-sku-popup");return e(),a(c,{modelValue:l.modelValue,onInput:n.onChangeValue,"border-radius":"20",localdata:d.goodsInfo,mode:l.skuMode,maskCloseAble:!0,priceColor:o.appTheme.mainBg,buyNowBackgroundColor:o.appTheme.mainBg,addCartColor:o.appTheme.viceText,addCartBackgroundColor:o.appTheme.viceBg,activedStyle:{color:o.appTheme.mainBg,borderColor:o.appTheme.mainBg,backgroundColor:n.activedBtnBackgroundColor},onOpen:n.openSkuPopup,onClose:n.closeSkuPopup,onBuyNow:n.buyNow,buyNowText:"立即砍价",maxBuyNum:1},null,8,["modelValue","onInput","localdata","mode","priceColor","buyNowBackgroundColor","addCartColor","addCartBackgroundColor","activedStyle","onOpen","onClose","onBuyNow"])}]]),Comment:T,CountDown:D},mixins:[w],data:()=>({isLoading:!0,showSkuPopup:!1,skuMode:3,showShareSheet:!1,showRules:!1,posterApiCall:L,activeId:null,goodsId:null,active:{},goods:{},setting:null,isPartake:null,taskId:null,cartTotal:0,isShowCustomerBtn:!1}),async onLoad(e){this.onRecordQuery(e),this.onRefreshPage(),this.isShowCustomerBtn=await o.isShowCustomerBtn()},methods:{onRecordQuery(e){const a=t(e);this.activeId=e.activeId?parseInt(e.activeId):parseInt(a.aid),this.goodsId=e.goodsId?parseInt(e.goodsId):parseInt(a.gid)},onRefreshPage(){const e=this;e.isLoading=!0,Promise.all([e.getActiveDetail(),e.getGoodsDetail(),e.getCartTotal()]).then((()=>e.setWxofficialShareData())).finally((()=>e.isLoading=!1))},getActiveDetail(){const e=this;return new Promise(((a,s)=>{V(e.activeId).then((s=>{e.active=s.data.active,e.setting=s.data.setting,e.isPartake=s.data.isPartake,e.taskId=s.data.taskId,a(s)})).catch(s)}))},getGoodsDetail(){const e=this;return new Promise(((a,s)=>{R(e.goodsId,!1).then((s=>{e.goods=s.data.detail,a(s)})).catch(s)}))},getCartTotal(){const e=this;return new Promise(((a,s)=>{$().then((s=>{e.cartTotal=s.data.cartTotal,a(s)})).catch(s)}))},onShowSkuPopup(){this.showSkuPopup=!this.showSkuPopup},onShowShareSheet(){this.showShareSheet=!this.showShareSheet},handleShowRules(){this.showRules=!0},onTargetHome(e){this.$navTo("pages/index/index")},onTargetCart(){this.$navTo("pages/cart/index")},handleMainBtn(){const e=this;if(!e.isPartake)return e.onShowSkuPopup();e.$navTo("pages/bargain/task",{taskId:e.taskId})},setWxofficialShareData(){const{goods:e}=this;this.updateShareCardData({title:e.goods_name,desc:e.selling_point,imgUrl:e.goods_image})}},onShareAppMessage(){const e=this,a=e.$getShareUrlParams({activeId:e.activeId,goodsId:e.goodsId});return{title:e.goods.goods_name,path:`/pages/bargain/goods/index?${a}`}},onShareTimeline(){const e=this,a=e.$getShareUrlParams({activeId:e.activeId,goodsId:e.goodsId});return{title:e.goods.goods_name,path:`/pages/bargain/goods/index?${a}`}}},[["render",function(o,t,w,b,I,T){const P=s("SlideImage"),B=h,j=c,L=k,V=s("count-down"),N=s("SkuPopup"),A=s("Comment"),D=y(x("mp-html"),v),R=s("customer-btn"),$=s("share-sheet"),M=S,U=y(x("u-modal"),C);return l((e(),a(j,{class:"container",style:n(o.appThemeStyle)},{default:d((()=>[I.isLoading?u("",!0):(e(),a(P,{key:0,video:I.goods.video,videoCover:I.goods.videoCover,images:I.goods.goods_images},null,8,["video","videoCover","images"])),I.isLoading?u("",!0):(e(),a(j,{key:1,class:"goods-info m-top20"},{default:d((()=>[r(j,{class:"info-item info-item__top dis-flex flex-x-between flex-y-end"},{default:d((()=>[r(j,{class:"block-left dis-flex flex-y-center"},{default:d((()=>[r(j,{class:"active-tag"},{default:d((()=>[r(B,null,{default:d((()=>[f("限时砍价")])),_:1})])),_:1}),r(B,{class:"floor-price__samll"},{default:d((()=>[f("￥")])),_:1}),r(B,{class:"floor-price"},{default:d((()=>[f(m(I.active.floor_price),1)])),_:1}),r(B,{class:"original-price"},{default:d((()=>[f("￥"+m(I.goods.goods_price_min),1)])),_:1})])),_:1}),r(j,{class:"block-right dis-flex"},{default:d((()=>[r(j,{class:"goods-sales"},{default:d((()=>[r(B,null,{default:d((()=>[f("已砍成"+m(I.active.active_sales)+"件",1)])),_:1})])),_:1})])),_:1})])),_:1}),r(j,{class:"info-item info-item__name dis-flex flex-y-center"},{default:d((()=>[r(j,{class:"goods-name flex-box"},{default:d((()=>[r(B,{class:"twoline-hide"},{default:d((()=>[f(m(I.goods.goods_name),1)])),_:1})])),_:1}),r(j,{class:"goods-share__line"}),r(j,{class:"goods-share"},{default:d((()=>[r(L,{class:"share-btn dis-flex flex-dir-column",onClick:t[0]||(t[0]=e=>T.onShowShareSheet())},{default:d((()=>[r(B,{class:"share__icon iconfont icon-fenxiang"}),r(B,{class:"f-24"},{default:d((()=>[f("分享")])),_:1})])),_:1})])),_:1})])),_:1}),I.goods.selling_point?(e(),a(j,{key:0,class:"info-item info-item_selling-point"},{default:d((()=>[r(B,null,{default:d((()=>[f(m(I.goods.selling_point),1)])),_:1})])),_:1})):u("",!0),0==I.active.is_end?(e(),a(j,{key:1,class:"info-item info-item_status info-item_countdown dis-flex flex-y-center"},{default:d((()=>[r(B,{class:"countdown-icon iconfont icon-naozhong"}),r(B,null,{default:d((()=>[f("距离活动结束")])),_:1}),r(B,{class:"m-r-10"},{default:d((()=>[f("还剩")])),_:1}),r(V,{date:I.active.end_time,separator:"zh",theme:"text"},null,8,["date"])])),_:1})):u("",!0),1==I.active.is_end?(e(),a(j,{key:2,class:"info-item info-item_status info-item_end"},{default:d((()=>[r(B,{class:"countdown-icon iconfont icon-naozhong"}),r(B,null,{default:d((()=>[f("砍价活动已结束，下次记得早点来哦~")])),_:1})])),_:1})):u("",!0)])),_:1})),r(j,{class:"bargain-rules m-top20 b-f",onClick:t[1]||(t[1]=e=>T.handleShowRules())},{default:d((()=>[r(j,{class:"item-title dis-flex"},{default:d((()=>[r(j,{class:"block-left flex-box"},{default:d((()=>[r(B,null,{default:d((()=>[f("砍价玩法")])),_:1})])),_:1}),r(j,{class:"block-right"},{default:d((()=>[r(B,{class:"show-more col-9"},{default:d((()=>[f("查看规则")])),_:1})])),_:1})])),_:1}),r(j,{class:"rule-simple dis-flex flex-x-around"},{default:d((()=>[r(j,{class:"simple-item dis-flex flex-dir-column flex-y-center"},{default:d((()=>[r(j,{class:"i-number dis-flex flex-x-center flex-y-center"},{default:d((()=>[r(B,{class:"f-30"},{default:d((()=>[f("1")])),_:1})])),_:1}),r(j,{class:"i-text f-28"},{default:d((()=>[f("点击砍价")])),_:1})])),_:1}),r(j,{class:"simple-item dis-flex flex-dir-column flex-y-center"},{default:d((()=>[r(j,{class:"i-number dis-flex flex-x-center flex-y-center"},{default:d((()=>[r(B,{class:"f-30"},{default:d((()=>[f("2")])),_:1})])),_:1}),r(j,{class:"i-text f-28"},{default:d((()=>[f("找人帮砍")])),_:1})])),_:1}),r(j,{class:"simple-item dis-flex flex-dir-column flex-y-center"},{default:d((()=>[r(j,{class:"i-number dis-flex flex-x-center flex-y-center"},{default:d((()=>[r(B,{class:"f-30"},{default:d((()=>[f("3")])),_:1})])),_:1}),r(j,{class:"i-text f-28"},{default:d((()=>[f("砍到最低")])),_:1})])),_:1}),r(j,{class:"simple-item dis-flex flex-dir-column flex-y-center"},{default:d((()=>[r(j,{class:"i-number dis-flex flex-x-center flex-y-center"},{default:d((()=>[r(B,{class:"f-30"},{default:d((()=>[f("4")])),_:1})])),_:1}),r(j,{class:"i-text f-28"},{default:d((()=>[f("优惠购买")])),_:1})])),_:1})])),_:1})])),_:1}),20==I.goods.spec_type?(e(),a(j,{key:2,class:"goods-choice m-top20 b-f",onClick:t[2]||(t[2]=e=>T.onShowSkuPopup())},{default:d((()=>[r(j,{class:"spec-list"},{default:d((()=>[r(j,{class:"flex-box"},{default:d((()=>[r(B,{class:"col-8"},{default:d((()=>[f("选择：")])),_:1}),(e(!0),g(_,null,p(I.goods.specList,((s,o)=>(e(),a(B,{class:"spec-name",key:o},{default:d((()=>[f(m(s.spec_name),1)])),_:2},1024)))),128))])),_:1}),r(j,{class:"f-26 col-9 t-r"},{default:d((()=>[r(B,{class:"iconfont icon-arrow-right"})])),_:1})])),_:1})])),_:1})):u("",!0),I.isLoading?u("",!0):(e(),a(N,{key:3,modelValue:I.showSkuPopup,"onUpdate:modelValue":t[3]||(t[3]=e=>I.showSkuPopup=e),skuMode:I.skuMode,active:I.active,goods:I.goods},null,8,["modelValue","skuMode","active","goods"])),I.isLoading?u("",!0):(e(),a(A,{key:4,"goods-id":I.goodsId,limit:2},null,8,["goods-id"])),I.isLoading?u("",!0):(e(),a(j,{key:5,class:"goods-content m-top20"},{default:d((()=>[r(j,{class:"item-title b-f"},{default:d((()=>[r(B,null,{default:d((()=>[f("商品描述")])),_:1})])),_:1}),""!=I.goods.content?(e(),a(j,{key:0,class:"goods-content__detail b-f"},{default:d((()=>[r(D,{content:I.goods.content},null,8,["content"])])),_:1})):u("",!0)])),_:1})),r(j,{class:"footer-fixed"},{default:d((()=>[r(j,{class:"footer-container"},{default:d((()=>[r(j,{class:"foo-item-fast"},{default:d((()=>[r(j,{class:"fast-item fast-item--home",onClick:T.onTargetHome},{default:d((()=>[r(j,{class:"fast-icon"},{default:d((()=>[r(B,{class:"iconfont icon-shouye"})])),_:1}),r(j,{class:"fast-text"},{default:d((()=>[r(B,null,{default:d((()=>[f("首页")])),_:1})])),_:1})])),_:1},8,["onClick"]),I.isShowCustomerBtn?(e(),a(R,{key:0,showCard:!0,cardTitle:I.goods.goods_name,cardImage:I.goods.goods_image},{default:d((()=>[r(j,{class:"fast-item"},{default:d((()=>[r(j,{class:"fast-icon"},{default:d((()=>[r(B,{class:"iconfont icon-kefu1"})])),_:1}),r(j,{class:"fast-text"},{default:d((()=>[r(B,null,{default:d((()=>[f("客服")])),_:1})])),_:1})])),_:1})])),_:1},8,["cardTitle","cardImage"])):u("",!0),I.isShowCustomerBtn?u("",!0):(e(),a(j,{key:1,class:"fast-item fast-item--cart",onClick:T.onTargetCart},{default:d((()=>[I.cartTotal>0?(e(),a(j,{key:0,class:"fast-badge fast-badge--fixed"},{default:d((()=>[f(m(I.cartTotal>99?"99+":I.cartTotal),1)])),_:1})):u("",!0),r(j,{class:"fast-icon"},{default:d((()=>[r(B,{class:"iconfont icon-gouwuche"})])),_:1}),r(j,{class:"fast-text"},{default:d((()=>[r(B,null,{default:d((()=>[f("购物车")])),_:1})])),_:1})])),_:1},8,["onClick"]))])),_:1}),r(j,{class:"foo-item-btn"},{default:d((()=>[r(j,{class:"btn-wrapper"},{default:d((()=>[I.active.is_start&&!I.active.is_end?(e(),a(j,{key:0,class:"btn-item btn--main",onClick:t[4]||(t[4]=e=>T.handleMainBtn(3))},{default:d((()=>[r(B,null,{default:d((()=>[f(m(I.isPartake?"继续砍价":"立即砍价"),1)])),_:1})])),_:1})):(e(),a(L,{key:1,class:"btn-item btn--gray"},{default:d((()=>[r(B,null,{default:d((()=>[f(m(I.active.is_end?"活动已结束":"活动未开启"),1)])),_:1})])),_:1}))])),_:1})])),_:1})])),_:1})])),_:1}),r($,{modelValue:I.showShareSheet,"onUpdate:modelValue":t[5]||(t[5]=e=>I.showShareSheet=e),shareTitle:I.goods.goods_name,shareImageUrl:I.goods.goods_image,posterApiCall:I.posterApiCall,posterApiParam:{activeId:I.activeId}},null,8,["modelValue","shareTitle","shareImageUrl","posterApiCall","posterApiParam"]),I.isLoading?u("",!0):(e(),a(U,{key:6,modelValue:I.showRules,"onUpdate:modelValue":t[6]||(t[6]=e=>I.showRules=e),title:"砍价规则"},{default:d((()=>[r(M,{style:{height:"610rpx"},"scroll-y":!0},{default:d((()=>[r(j,{class:"pops-content"},{default:d((()=>[r(B,null,{default:d((()=>[f(m(I.setting.rulesDesc),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"]))])),_:1},8,["style"])),[[i,!I.isLoading]])}],["__scopeId","data-v-fddb5ba1"]]);export{M as default};
