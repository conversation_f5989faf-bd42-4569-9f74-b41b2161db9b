<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model;

use app\common\model\Promote as PromoteModel;

/**
 * 开屏推广活动模型
 * Class Promote
 * @package app\api\model
 */
class Promote extends PromoteModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'views_num',
        'click_num',
        'sort',
        'store_id',
        'is_delete',
        'create_time',
        'update_time',
    ];

    /**
     * 获取开启的活动列表
     * @return Promote[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getActiveList()
    {
        return $this->where('start_time', '<=', \time())
            ->where('end_time', '>=', \time())
            ->where('status', '=', 1)
            ->where('is_delete', '=', 0)
            ->order(['sort', $this->getPk()])
            ->select();
    }

    /**
     * 累积展现次数
     * @param int $promoteId
     * @return mixed
     */
    public function updateViewsNum(int $promoteId)
    {
        return $this->setInc($promoteId, 'views_num', 1);
    }

    /**
     * 累积点击次数
     * @param int $promoteId
     * @return mixed
     */
    public function updateClickNum(int $promoteId)
    {
        return $this->setInc($promoteId, 'click_num', 1);
    }
}
