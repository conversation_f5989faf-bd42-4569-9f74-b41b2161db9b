import{u as e,o as t,c as a,w as i,a as s,k as l,n,A as d,i as o,r,d as u,e as h,F as c,b as p,f as m,t as f,l as g}from"./index-BrSKFjFf.js";import{_}from"./u-icon.Bawpp3Hr.js";import{r as b}from"./uni-app.es.BT6Htq7o.js";import{_ as D}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{E as v}from"./emitter.DrjJCwnj.js";import{l as S,d as y,r as C,s as k}from"./address.D0KguL4N.js";import{E as w}from"./index.DENyYjtH.js";const z=D({name:"u-radio",emits:["change"],props:{name:{type:[String,Number],default:""},size:{type:[String,Number],default:34},shape:{type:String,default:""},disabled:{type:[String,Boolean],default:""},labelDisabled:{type:[String,Boolean],default:""},activeColor:{type:String,default:""},iconSize:{type:[String,Number],default:""},labelSize:{type:[String,Number],default:""}},data:()=>({parentData:{iconSize:null,labelDisabled:null,disabled:null,shape:null,activeColor:null,size:null,width:null,height:null,value:null,wrap:null,modelValue:null}}),created(){this.parent=!1,this.updateParentData(),this.parent.children.push(this)},computed:{elDisabled(){return""!==this.disabled?this.disabled:null!==this.parentData.disabled&&this.parentData.disabled},elLabelDisabled(){return""!==this.labelDisabled?this.labelDisabled:null!==this.parentData.labelDisabled&&this.parentData.labelDisabled},elSize(){return this.size?this.size:this.parentData.size?this.parentData.size:34},elIconSize(){return this.iconSize?this.iconSize:this.parentData.iconSize?this.parentData.iconSize:20},elActiveColor(){return this.activeColor?this.activeColor:this.parentData.activeColor?this.parentData.activeColor:"primary"},elShape(){return this.shape?this.shape:this.parentData.shape?this.parentData.shape:"circle"},iconStyle(){let e={};return this.elActiveColor&&this.parentData.value===this.name&&!this.elDisabled&&(e.borderColor=this.elActiveColor,e.backgroundColor=this.elActiveColor),e.width=this.$u.addUnit(this.elSize),e.height=this.$u.addUnit(this.elSize),e},iconColor(){return this.name===this.parentData.value?"#ffffff":"transparent"},iconClass(){let e=[];return e.push("u-radio__icon-wrap--"+this.elShape),this.name===this.parentData.value&&e.push("u-radio__icon-wrap--checked"),this.elDisabled&&e.push("u-radio__icon-wrap--disabled"),this.name===this.parentData.value&&this.elDisabled&&e.push("u-radio__icon-wrap--disabled--checked"),e.join(" ")},radioStyle(){let e={};return this.parentData.width&&(e.width=this.$u.addUnit(this.parentData.width),e.flex=`0 0 ${this.$u.addUnit(this.parentData.width)}`),this.parentData.wrap&&(e.width="100%",e.flex="0 0 100%"),e}},methods:{updateParentData(){this.getParentData("u-radio-group")},onClickLabel(){this.elLabelDisabled||this.elDisabled||this.setRadioCheckedStatus()},toggle(){this.elDisabled||this.setRadioCheckedStatus()},emitEvent(){this.parentData.value!=this.name&&this.$emit("change",this.name)},setRadioCheckedStatus(){this.emitEvent(),this.parent&&(this.parent.setValue(this.name),this.parentData.value=this.name)}}},[["render",function(r,u,h,c,p,m){const f=b(e("u-icon"),_),g=o;return t(),a(g,{class:"u-radio",style:n([m.radioStyle])},{default:i((()=>[s(g,{class:l(["u-radio__icon-wrap",[m.iconClass]]),onClick:m.toggle,style:n([m.iconStyle])},{default:i((()=>[s(f,{class:"u-radio__icon-wrap__icon",name:"checkbox-mark",size:m.elIconSize,color:m.iconColor},null,8,["size","color"])])),_:1},8,["onClick","class","style"]),s(g,{class:"u-radio__label",onClick:m.onClickLabel,style:n({fontSize:r.$u.addUnit(h.labelSize)})},{default:i((()=>[d(r.$slots,"default",{},void 0,!0)])),_:3},8,["onClick","style"])])),_:3},8,["style"])}],["__scopeId","data-v-1295fd34"]]);const $=D({name:"u-radio-group",emits:["update:modelValue","input","change"],mixins:[v],props:{value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},disabled:{type:Boolean,default:!1},activeColor:{type:String,default:"#2979ff"},size:{type:[String,Number],default:34},labelDisabled:{type:Boolean,default:!1},shape:{type:String,default:"circle"},iconSize:{type:[String,Number],default:20},width:{type:[String,Number],default:"auto"},wrap:{type:Boolean,default:!1}},data:()=>({uFromData:{inputAlign:"left"}}),created(){this.children=[]},mounted(){let e=this.$u.$parent.call(this,"u-form");e&&Object.keys(this.uFromData).map((t=>{this.uFromData[t]=e[t]}))},watch:{parentData(){this.children.length&&this.children.map((e=>{"function"==typeof e.updateParentData&&e.updateParentData()}))}},computed:{valueCom(){return this.modelValue},parentData(){return[this.value,this.disabled,this.activeColor,this.size,this.labelDisabled,this.shape,this.iconSize,this.width,this.wrap,this.modelValue]}},methods:{setValue(e){this.children.map((t=>{t.parentData.value!=e&&(t.parentData.value="")})),this.$emit("input",e),this.$emit("update:modelValue",e),this.$emit("change",e),setTimeout((()=>{this.dispatch("u-form-item","onFieldChange",e)}),60)}}},[["render",function(e,s,n,r,u,h){const c=o;return t(),a(c,{class:l(["u-radio-group u-clearfix","right"==u.uFromData.inputAlign?"flex-end":""])},{default:i((()=>[d(e.$slots,"default",{},void 0,!0)])),_:3},8,["class"])}],["__scopeId","data-v-2aeb793f"]]);const I=D({components:{Empty:w},data:()=>({options:{},isLoading:!0,list:[],defaultId:null}),onLoad(e){this.options=e},onShow(){this.getPageData()},methods:{getPageData(){const e=this;e.isLoading=!0,Promise.all([e.getDefaultId(),e.getAddressList()]).then((()=>{e.onReorder()})).finally((()=>e.isLoading=!1))},getAddressList(){const e=this;return new Promise(((t,a)=>{S().then((a=>{e.list=a.data.list,t(a)})).catch(a)}))},getDefaultId(){return new Promise(((e,t)=>{const a=this;y().then((t=>{a.defaultId=t.data.defaultId,e(t)})).catch(t)}))},onReorder(){const e=this;e.list.sort((t=>t.address_id==e.defaultId?-1:1))},handleCreate(){this.$navTo("pages/address/create")},handleUpdate(e){this.$navTo("pages/address/update",{addressId:e})},handleRemove(e){const t=this;uni.showModal({title:"提示",content:"您确定要删除当前收货地址吗?",success({confirm:a}){a&&t.onRemove(e)}})},onRemove(e){const t=this;C(e).then((e=>{t.getPageData()}))},handleSetDefault(e){const t=this;k(e).then((e=>{"checkout"===t.options.from&&uni.navigateBack()}))}}},[["render",function(l,d,_,D,v,S){const y=g,C=o,k=b(e("u-radio"),z),w=b(e("u-radio-group"),$),I=r("empty");return t(),a(C,{class:"container",style:n(l.appThemeStyle)},{default:i((()=>[s(C,{class:"addres-list"},{default:i((()=>[(t(!0),u(c,null,h(v.list,((e,n)=>(t(),a(C,{class:"address-item",key:n},{default:i((()=>[s(C,{class:"contacts"},{default:i((()=>[s(y,{class:"name"},{default:i((()=>[m(f(e.name),1)])),_:2},1024),s(y,{class:"phone"},{default:i((()=>[m(f(e.phone),1)])),_:2},1024)])),_:2},1024),s(C,{class:"address"},{default:i((()=>[(t(!0),u(c,null,h(e.region,((e,s)=>(t(),a(y,{class:"region",key:s},{default:i((()=>[m(f(e),1)])),_:2},1024)))),128)),s(y,{class:"detail"},{default:i((()=>[m(f(e.detail),1)])),_:2},1024)])),_:2},1024),s(C,{class:"line"}),s(C,{class:"item-option"},{default:i((()=>[s(C,{class:"_left"},{default:i((()=>[s(C,{class:"item-radio"},{default:i((()=>[s(w,{modelValue:v.defaultId,"onUpdate:modelValue":d[0]||(d[0]=e=>v.defaultId=e),onChange:t=>S.handleSetDefault(e.address_id)},{default:i((()=>[s(k,{name:e.address_id,"active-color":l.appTheme.mainBg},{default:i((()=>[m(f(e.address_id==v.defaultId?"默认":"选择"),1)])),_:2},1032,["name","active-color"])])),_:2},1032,["modelValue","onChange"])])),_:2},1024)])),_:2},1024),s(C,{class:"_right"},{default:i((()=>[s(C,{class:"events"},{default:i((()=>[s(C,{class:"event-item",onClick:t=>S.handleUpdate(e.address_id)},{default:i((()=>[s(y,{class:"iconfont icon-edit"}),s(y,{class:"title"},{default:i((()=>[m("编辑")])),_:1})])),_:2},1032,["onClick"]),s(C,{class:"event-item",onClick:t=>S.handleRemove(e.address_id)},{default:i((()=>[s(y,{class:"iconfont icon-delete"}),s(y,{class:"title"},{default:i((()=>[m("删除")])),_:1})])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1}),v.list.length?p("",!0):(t(),a(I,{key:0,isLoading:v.isLoading,tips:"亲，暂无收货地址"},null,8,["isLoading"])),s(C,{class:"footer-fixed"},{default:i((()=>[s(C,{class:"btn-wrapper"},{default:i((()=>[s(C,{class:"btn-item btn-item-main",onClick:d[1]||(d[1]=e=>S.handleCreate())},{default:i((()=>[m("添加新地址")])),_:1})])),_:1})])),_:1})])),_:1},8,["style"])}],["__scopeId","data-v-eca7affb"]]);export{I as default};
