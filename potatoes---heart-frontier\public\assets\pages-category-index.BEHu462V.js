import{E as e,$ as t,r as a,o as s,c as l,w as o,a as n,d as i,e as c,F as r,b as d,i as u,f as h,t as m,g,h as p,n as y,j as f,k as _,l as C,m as S,q as k,s as x,u as I,v as L,x as v,y as b,z as P}from"./index-BrSKFjFf.js";import{W as E}from"./wxofficial.BsQrFOat.js";import{S as w}from"./index.CnCQY1On.js";import{P as T}from"./index.BnRfJc5X.js";import{E as A}from"./index.DENyYjtH.js";import{_ as R}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{M as j,_ as O}from"./mescroll-mixins.Dz0M-6WC.js";import{r as D}from"./uni-app.es.BT6Htq7o.js";import{A as G}from"./index.Doi9vb8z.js";import{l as B}from"./index.DObYClFz.js";import"./u-icon.Bawpp3Hr.js";import"./u-mask.Coif7lGR.js";import"./color.D-c1b2x3.js";import"./GoodsSource.DjEDuqbs.js";import"./cart.B-WHsPTS.js";import"./index.BHTFC2cv.js";const $={onPageScroll(e){this.handlePageScroll(e)},onReachBottom(){this.handleReachBottom()},onPullDownRefresh(){this.handlePullDownRefresh()},data(){return{mescroll:{onPageScroll:e=>{this.handlePageScroll(e)},onReachBottom:()=>{this.handleReachBottom()},onPullDownRefresh:()=>{this.handlePullDownRefresh()}}}},methods:{handlePageScroll(e){let t=this.$refs.mescrollItem;t&&t.mescroll&&t.mescroll.onPageScroll(e)},handleReachBottom(){let e=this.$refs.mescrollItem;e&&e.mescroll&&e.mescroll.onReachBottom()},handlePullDownRefresh(){let e=this.$refs.mescrollItem;e&&e.mescroll&&e.mescroll.onPullDownRefresh()}}},N=new e([{key:"ONE_LEVEL_BIG",name:"一级分类[大图]",value:10},{key:"ONE_LEVEL_SMALL",name:"一级分类[小图]",value:11},{key:"TWO_LEVEL",name:"二级分类",value:20},{key:"COMMODITY",name:"一级分类+商品",value:30}]),M="category/list";let V;const U=R({components:{Search:w,Primary:R({components:{Empty:A},props:{display:{type:Number,default:10},list:{type:Array,default:[]}},data:()=>({PageCategoryStyleEnum:N}),methods:{onTargetGoodsList(e){this.$navTo("pages/goods/list",{categoryId:e})}}},[["render",function(e,t,p,y,f,_){const C=g,S=u,k=a("empty");return s(),l(S,{class:"primary"},{default:o((()=>[p.list.length>0&&p.display==f.PageCategoryStyleEnum.ONE_LEVEL_BIG.value?(s(),l(S,{key:0,class:"cate-content"},{default:o((()=>[n(S,{class:"cate-wrapper cate_style__10 clearfix"},{default:o((()=>[(s(!0),i(r,null,c(p.list,((e,t)=>(s(),l(S,{class:"cate-item",key:t,onClick:t=>_.onTargetGoodsList(e.category_id)},{default:o((()=>[e.image?(s(),l(C,{key:0,class:"image",mode:"widthFix",src:e.image.preview_url},null,8,["src"])):d("",!0)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):d("",!0),p.list.length>0&&p.display==f.PageCategoryStyleEnum.ONE_LEVEL_SMALL.value?(s(),l(S,{key:1,class:"cate-content"},{default:o((()=>[n(S,{class:"cate-wrapper cate_style__11 clearfix"},{default:o((()=>[(s(!0),i(r,null,c(p.list,((e,t)=>(s(),l(S,{class:"cate-item",key:t,onClick:t=>_.onTargetGoodsList(e.category_id)},{default:o((()=>[e.image?(s(),l(C,{key:0,class:"image",mode:"scaleToFill",src:e.image.preview_url},null,8,["src"])):d("",!0),n(S,{class:"cate-name"},{default:o((()=>[h(m(e.name),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):d("",!0),p.list.length?d("",!0):(s(),l(k,{key:2,tips:"亲，暂无商品分类"+p.display},null,8,["tips"]))])),_:1})}],["__scopeId","data-v-784c3793"]]),Secondary:R({props:{list:{type:Array,default:[]}},data:()=>({curIndex:0}),methods:{handleSelectNav(e){this.curIndex=e},handleGoods(e){this.$navTo("pages/goods/list",{categoryId:e})}}},[["render",function(e,t,a,S,k,x){const I=C,L=f,v=g,b=u;return s(),l(b,{class:"container",style:y(e.appThemeStyle)},{default:o((()=>[n(L,{class:"cate-left","scroll-y":!0,onTouchmove:t[0]||(t[0]=p((()=>{}),["stop","prevent"]))},{default:o((()=>[(s(!0),i(r,null,c(a.list,((e,t)=>(s(),l(I,{class:_(["type-nav",{selected:k.curIndex==t}]),key:t,onClick:e=>x.handleSelectNav(t)},{default:o((()=>[h(m(e.name),1)])),_:2},1032,["class","onClick"])))),128))])),_:1}),n(b,{class:"cate-content"},{default:o((()=>[n(b,{class:"category-list clearfix"},{default:o((()=>[(s(!0),i(r,null,c(a.list[k.curIndex].children,((e,t)=>(s(),l(b,{class:"category-item",key:t,onClick:t=>x.handleGoods(e.category_id)},{default:o((()=>[e.image?(s(),l(b,{key:0,class:"item-image"},{default:o((()=>[n(v,{class:"image",mode:"scaleToFill",src:e.image.preview_url},null,8,["src"])])),_:2},1024)):d("",!0),n(b,{class:"item-name"},{default:o((()=>[n(I,{class:"oneline-hide"},{default:o((()=>[h(m(e.name),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})])),_:1},8,["style"])}],["__scopeId","data-v-4138b0b9"]]),Commodity:R({components:{AddCartBtn:R({props:{btnStyle:{type:Number,default:1}},data:()=>({value:!1,goodsInfo:{}}),methods:{handleAddCart(){this.$emit("handleAddCart")}}},[["render",function(e,t,a,i,c,r){const d=C,h=u;return s(),l(h,{class:"add-cart",onClick:p(r.handleAddCart,["stop"])},{default:o((()=>[n(d,{class:_(["icon iconfont",[`icon-jiagou${a.btnStyle}`]])},null,8,["class"])])),_:1},8,["onClick"])}],["__scopeId","data-v-0f678d54"]]),AddCartPopup:G},mixins:[j],props:{list:{type:Array,default:[]},setting:{type:Object,default:()=>{}}},data:()=>({curIndex:-1,showSubCate:!1,curIndex2:-1,goodsList:S(),upOption:{auto:!0,page:{size:15},noMoreSize:3,toTop:{right:30,bottom:48,zIndex:9}}}),computed:{subCateList(){return this.list[this.curIndex]&&this.list[this.curIndex].children?this.list[this.curIndex].children:[]}},methods:{upCallback(e){const t=this;t.getGoodsList(e.num).then((e=>{const a=e.data.length,s=e.data.total;t.mescroll.endBySize(a,s)})).catch((()=>t.mescroll.endErr()))},getGoodsList(e=1){const t=this,a=t.getCategoryId();return new Promise(((s,l)=>{B({categoryId:a,page:e},{load:!1}).then((a=>{const l=a.data.list;t.goodsList.data=k(l,t.goodsList,e),t.goodsList.last_page=l.last_page,s(l)})).catch(l)}))},getCategoryId(){const e=this;return e.curIndex2>-1?e.subCateList[e.curIndex2].category_id:e.curIndex>-1?e.list[e.curIndex].category_id:0},handleSelectNav(e){this.curIndex=e,this.onRefreshList(),this.showSubCate=!1,this.curIndex2=-1},handleSelectSubCate(e){this.curIndex2=e,this.showSubCate=!1,this.onRefreshList()},onRefreshList(){this.goodsList=S(),setTimeout((()=>this.mescroll.resetUpScroll()),120)},onTargetGoods(e){this.$navTo("pages/goods/detail",{goodsId:e})},handleAddCart(e){this.$refs.AddCartPopup.handle(e)},onUpdateCartTabBadge(){console.log("onUpdateCartTabBadge"),x()},handleShowSubCate(){this.showSubCate=!this.showSubCate}}},[["render",function(e,t,S,k,x,b){const P=C,E=f,w=u,T=g,A=a("add-cart-btn"),R=a("AddCartPopup"),j=D(I("mescroll-body"),O);return s(),l(w,{class:"container",style:y(e.appThemeStyle)},{default:o((()=>[n(E,{class:"cate-left","scroll-y":!0,onTouchmove:t[1]||(t[1]=p((()=>{}),["stop","prevent"]))},{default:o((()=>[n(P,{class:_(["type-nav",{selected:-1==x.curIndex}]),onClick:t[0]||(t[0]=e=>b.handleSelectNav(-1))},{default:o((()=>[h("全部")])),_:1},8,["class"]),(s(!0),i(r,null,c(S.list,((e,t)=>(s(),l(P,{class:_(["type-nav",{selected:x.curIndex==t}]),key:t,onClick:e=>b.handleSelectNav(t)},{default:o((()=>[h(m(e.name),1)])),_:2},1032,["class","onClick"])))),128))])),_:1}),n(j,{ref:"mescrollRef",sticky:!0,onInit:e.mescrollInit,down:{use:!1},up:x.upOption,bottombar:!1,onUp:b.upCallback},{default:o((()=>[n(w,{class:"cate-content"},{default:o((()=>[b.subCateList.length?(s(),l(w,{key:0,class:_(["sub-cate-list clearfix",{"display-fold":!x.showSubCate}]),onTouchmove:t[3]||(t[3]=p((()=>{}),["stop","prevent"]))},{default:o((()=>[n(w,{class:"nav-icon",onClick:b.handleShowSubCate},{default:o((()=>[n(P,{class:_(["iconfont",[x.showSubCate?"icon-arrow-up":"icon-arrow-down"]])},null,8,["class"])])),_:1},8,["onClick"]),n(w,{class:_(["sub-cate-item",{selected:-1==x.curIndex2}]),onClick:t[2]||(t[2]=e=>b.handleSelectSubCate(-1))},{default:o((()=>[n(P,null,{default:o((()=>[h("全部")])),_:1})])),_:1},8,["class"]),(s(!0),i(r,null,c(b.subCateList,((e,t)=>(s(),l(w,{class:_(["sub-cate-item",{selected:x.curIndex2==t}]),key:t,onClick:e=>b.handleSelectSubCate(t)},{default:o((()=>[n(P,null,{default:o((()=>[h(m(e.name),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1},8,["class"])):d("",!0),n(w,{class:"goods-list"},{default:o((()=>[(s(!0),i(r,null,c(x.goodsList.data,((e,t)=>(s(),l(w,{class:"goods-item--container",key:t},{default:o((()=>[n(w,{class:"goods-item",onClick:t=>b.onTargetGoods(e.goods_id)},{default:o((()=>[n(w,{class:"goods-item-left"},{default:o((()=>[n(T,{class:"image",src:e.goods_image},null,8,["src"])])),_:2},1024),n(w,{class:"goods-item-right"},{default:o((()=>[n(w,{class:"goods-name"},{default:o((()=>[n(P,{class:"twoline-hide"},{default:o((()=>[h(m(e.goods_name),1)])),_:2},1024)])),_:2},1024),n(w,{class:"goods-item-desc"},{default:o((()=>[n(w,{class:"desc-footer"},{default:o((()=>[n(w,{class:"item-prices oneline-hide"},{default:o((()=>[n(P,{class:"price-x"},{default:o((()=>[h("¥"+m(e.goods_price_min),1)])),_:2},1024),e.line_price_min>0?(s(),l(P,{key:0,class:"price-y"},{default:o((()=>[h("¥"+m(e.line_price_min),1)])),_:2},1024)):d("",!0)])),_:2},1024),S.setting.showAddCart?(s(),l(A,{key:0,btnStyle:S.setting.cartStyle,onHandleAddCart:t=>b.handleAddCart(e)},null,8,["btnStyle","onHandleAddCart"])):d("",!0)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1}),L(n(w,{class:"mask",onTouchmove:t[4]||(t[4]=p((()=>{}),["stop","prevent"])),onClick:b.handleShowSubCate},null,8,["onClick"]),[[v,x.showSubCate]]),n(R,{ref:"AddCartPopup",onAddCart:b.onUpdateCartTabBadge},null,8,["onAddCart"])])),_:1})])),_:1},8,["onInit","up","onUp"])])),_:1},8,["style"])}],["__scopeId","data-v-1cc4bc26"]]),PromotePopup:T},mixins:[$,E],data:()=>({PageCategoryStyleEnum:N,list:[],setting:{},isLoading:!0}),onLoad(){this.onRefreshPage()},onShow(){(new Date).getTime()-V>3e5&&this.onRefreshPage()},methods:{onRefreshPage(){V=(new Date).getTime(),this.getPageData(),x()},getPageData(){const e=this;e.isLoading=!0,Promise.all([b.data(!1),t.get(M)]).then((t=>{e.initSetting(t[0]),e.initCategory(t[1]),e.setWxofficialShareData()})).finally((()=>e.isLoading=!1))},initSetting(e){this.setting=e[P.PAGE_CATEGORY_TEMPLATE.value]},initCategory(e){this.list=e.data.list},setWxofficialShareData(){const{setting:e}=this;this.updateShareCardData({title:e.shareTitle})}},onShareAppMessage(){return{title:_this.templet.shareTitle,path:"/pages/category/index?"+this.$getShareUrlParams()}},onShareTimeline(){return{title:_this.templet.shareTitle,path:"/pages/category/index?"+this.$getShareUrlParams()}}},[["render",function(e,t,i,c,r,h){const m=a("search"),g=u,p=a("primary"),y=a("secondary"),f=a("commodity"),_=a("PromotePopup");return s(),l(g,{class:"container"},{default:o((()=>[n(g,{class:"search"},{default:o((()=>[n(m,{tips:"搜索商品",onEvent:t[0]||(t[0]=t=>e.$navTo("pages/search/index"))})])),_:1}),r.setting.style==r.PageCategoryStyleEnum.ONE_LEVEL_BIG.value||r.setting.style==r.PageCategoryStyleEnum.ONE_LEVEL_SMALL.value?(s(),l(p,{key:0,display:r.setting.style,list:r.list},null,8,["display","list"])):d("",!0),r.setting.style==r.PageCategoryStyleEnum.TWO_LEVEL.value?(s(),l(y,{key:1,list:r.list},null,8,["list"])):d("",!0),r.setting.style==r.PageCategoryStyleEnum.COMMODITY.value?(s(),l(f,{key:2,ref:"mescrollItem",list:r.list,setting:r.setting},null,8,["list","setting"])):d("",!0),n(_)])),_:1})}],["__scopeId","data-v-3aa708e4"]]);export{U as default};
