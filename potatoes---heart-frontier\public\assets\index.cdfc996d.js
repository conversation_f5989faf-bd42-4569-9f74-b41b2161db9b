import{z as t,A as s,o,c as e,w as a,a as i,B as n,e as r,U as c,i as p}from"./index-4ddb689d.js";import{_ as f}from"./_plugin-vue_export-helper.1b428a4d.js";const d=f({props:{},data:()=>({isShow:!1,setting:{}}),async created(){this.isShow=await t.isShowCustomerBtn(),this.setting=await t.item(s.CUSTOMER.value,!0)},methods:{handleContact(){const{setting:t}=this;if("wxqykf"==t.provider){if(!t.config.wxqykf.url||!t.config.wxqykf.corpId)return void this.$toast("客服链接和企业ID不能为空");window.open(t.config.wxqykf.url)}}}},[["render",function(t,s,f,d,l,w){const h=c,u=p;return l.isShow?(o(),e(u,{key:0},{default:a((()=>[i(h,{class:"btn-normal","open-type":"mpwxkf"==l.setting.provider?"contact":"",onClick:s[0]||(s[0]=t=>w.handleContact())},{default:a((()=>[n(t.$slots,"default")])),_:3},8,["open-type"])])),_:3})):r("",!0)}]]);export{d as C};
