<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\store\model\xj;

use app\common\model\xj\Order as VipModel;

/**
 * 文章模型
 * Class Vip
 * @package app\store\model
 */
class Vip extends VipModel
{
    /**
     * 获取列表
     * @param array $param
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     */
    public function getList(array $param = []): \think\Paginator
    {
        // 设置默认的检索数据
        $params = $this->setQueryDefaultValue($param, [
            'user_id'       => 0,  // 用户ID
            'search'        => '', // 查询内容
            'recharge_type' => 0,  // 充值方式
            'pay_status'    => 0,  // 支付状态
            'betweenTime'   => [], // 起止时间
        ]);
        // 检索查询条件
        $filter = [];
        // 用户ID
        $params['user_id'] > 0 && $filter[] = ['order.user_id', '=', $params['user_id']];
        // 用户昵称/订单号
        ! empty($params['search']) && $filter[] = ['user.nick_name', 'like', "%{$params['search']}%"];
        // 充值方式
        $params['recharge_type'] > 0 && $filter[] = ['order.recharge_type', '=', (int) $params['recharge_type']];
        // 支付状态
        $params['pay_status'] > 0 && $filter[] = ['order.pay_status', '=', (int) $params['pay_status']];
        // 起止时间
        if (! empty($params['betweenTime'])) {
            $times    = between_time($params['betweenTime']);
            $filter[] = ['order.pay_time', '>=', $times['start_time']];
            $filter[] = ['order.pay_time', '<', $times['end_time'] + 86400];
        }

        // 查询列表数据
        $data = $this->alias('order')
            ->join('user', 'user.user_id = order.user_id')
            ->join('user_grade g', 'g.grade_id = order.new_grade_id')
            ->where($filter)

            ->where('order.is_delete', '=', 0)
            ->where('order.pay_time', '>', 0)
            ->field('order.*,g.name as grade_name')
            ->order(['order.pay_time' => 'desc'])
            ->paginate(15);
        // 获取文章的图片和分类
        return self::preload($data, ['user']);
    }

    /**
     * 新增记录
     * @param array $data
     * @return bool
     */
    public function add(array $data): bool
    {
        if (empty($data['image_id'])) {
            $this->error = '请上传封面图';
            return false;
        }

        $data['store_id'] = self::$storeId;
        return $this->save($data);
    }

    /**
     * 更新记录
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
      

        return $this->save($data) !== false;
    }

    /**
     * 软删除
     * @return bool
     */
    public function setDelete(): bool
    {
        return $this->save(['is_delete' => 1]);
    }

    /**
     * 获取文章总数量
     * @param array $where
     * @return int
     */
    public static function getVipTotal(array $where = []): int
    {
        return (new static )->where($where)->where('is_delete', '=', 0)->count();
    }
}
