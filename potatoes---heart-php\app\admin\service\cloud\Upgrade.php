<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\admin\service\cloud;

use cores\authorize\Http;
use cores\authorize\AuthFile;
use cores\library\Version;
use cores\exception\BaseException;
use app\common\library\helper;
use app\common\service\BaseService;
use cores\authorize\update\Update;

/**
 * 在线更新服务
 * Class Upgrade
 */
class Upgrade extends BaseService
{
    /**
     * 获取在线更新信息
     * @return array
     * @throws BaseException
     */
    public function getInfo(): array
    {
        // 获取系统更新记录
        $list = $this->getUpgradeList();
        // 获取当前版本号
        $currentVersion = Version::getVersion();
        // 获取下一个更新的版本
        $nextVersion = Version::nextVersion($currentVersion, helper::getArrayColumn($list, 'version'));
        return ['updateList' => $list, 'currentVersion' => $currentVersion, 'nextVersion' => $nextVersion];
    }

    /**
     * 下载指定的版本模块
     * @param int $versionId 版本ID
     * @param string $moduleKey 模块key
     * @return string
     * @throws BaseException
     */
    public function download(int $versionId, string $moduleKey): string
    {
        // 获取系统指定版本信息
        $versionInfo = $this->getUpgradeDetail($versionId, [$moduleKey]);
        if (empty($versionInfo['modules']) || !isset($versionInfo['modules'][$moduleKey])) {
            throwError('很抱歉，指定的版本模块不存在');
        }
        return $versionInfo['modules'][$moduleKey]['download_url'];
    }

    /**
     * 在线更新到指定版本
     * @param int $versionId 版本ID (必须是下一版本号, 不能跨版本)
     * @return bool
     * @throws BaseException
     */
    public function update(int $versionId): bool
    {
        // 获取系统指定版本信息
        $versionInfo = $this->getUpgradeDetail($versionId, ['server', 'database']);
        if (!isset($versionInfo['modules']['server']) || !isset($versionInfo['modules']['database'])) {
            throwError('很抱歉，指定的版本模块不存在');
        }
        // 执行一键更新
        $UpdateService = new Update($versionInfo['version'], $versionInfo['modules']);
        return $UpdateService->handle();
    }

    /**
     * 获取系统指定版本信息
     * @param int $versionId 版本ID
     * @param array $modules 指定的模块
     * @return array
     * @throws BaseException
     */
    private function getUpgradeDetail(int $versionId, array $modules = []): array
    {
        return $this->getHttp()->getUpgradeDetail($versionId, $modules);
    }

    /**
     * 获取系统更新列表记录
     * @return array
     * @throws BaseException
     */
    private function getUpgradeList(): array
    {
        return $this->getHttp()->getUpgradeList();
    }

    /**
     * 实例化Http类
     * @return Http
     * @throws BaseException
     */
    private function getHttp(): Http
    {
        $authData = $this->getAuthData();
        $Http = new Http();
        $Http->setAppToken($authData['AppID'], $authData['AppSecret']);
        return $Http;
    }

    /**
     * 读取许可证文件信息
     * @return array
     * @throws BaseException
     */
    private function getAuthData(): array
    {
        $AuthFile = AuthFile::getInstance();
        $auth = $AuthFile->getAuthFile();
        return $AuthFile->getActivationData($auth);
    }
}