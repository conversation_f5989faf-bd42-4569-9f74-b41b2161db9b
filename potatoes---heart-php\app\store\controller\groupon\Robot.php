<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，拼单商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\groupon;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\groupon\Robot as RobotModel;

/**
 * 拼团机器人
 * Class Robot
 * @package app\store\controller\groupon
 */
class Robot extends Controller
{
    /**
     * 获取列表记录
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function list(): Json
    {
        $model = new RobotModel;
        $list = $model->getList();
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 添加机器人
     * @return Json
     */
    public function add(): Json
    {
        // 新增记录
        $model = new RobotModel;
        if ($model->add($this->postForm())) {
            return $this->renderSuccess('添加成功');
        }
        return $this->renderError($model->getError() ?: '添加失败');
    }

    /**
     * 编辑机器人
     * @param int $robotId
     * @return Json
     */
    public function edit(int $robotId): Json
    {
        // 拼单详情
        $model = RobotModel::detail($robotId);
        // 更新记录
        if ($model->edit($this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }

    /**
     * 删除机器人
     * @param int $robotId
     * @return Json
     */
    public function delete(int $robotId): Json
    {
        // 拼单详情
        $model = RobotModel::detail($robotId);
        if (!$model->setDelete()) {
            return $this->renderError($model->getError() ?: '删除失败');
        }
        return $this->renderSuccess('删除成功');
    }
}
