<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\dealer;

use app\common\enum\user\balanceLog\Scene as SceneEnum;
use app\store\model\dealer\Referee as RefereeModel;
use app\common\model\dealer\User as UserModel;
use app\store\service\store\User as StoreUserService;
use app\store\model\dealer\BalanceLog as BalanceLogModel;
/**
 * 分销商用户模型
 * Class User
 * @package app\store\model\dealer
 */
class User extends UserModel
{

      public function recharge(array $data): bool
    {
        $data=$data['balance'];
        // 当前操作人用户名
        $storeUserName = StoreUserService::getLoginInfo()['user']['user_name'];
        if (! isset($data['money']) || $data['money'] === '' || $data['money'] < 0) {
            $this->error = '请输入正确的金额';
            return false;
        }
        // 判断充值方式，计算最终金额
        if ($data['mode'] === 'inc') {
            $diffMoney = $data['money'];
        } elseif ($data['mode'] === 'dec') {
            $diffMoney = -$data['money'];
        } else {
            $diffMoney = helper::bcsub($data['money'], $this['balance']);
        }
        // 更新记录
        $this->transaction(function () use ($storeUserName, $data, $diffMoney) {
            // 更新账户余额
            static::setIncBalance((int) $this['user_id'], (float) $diffMoney);
            // 新增余额变动记录
            BalanceLogModel::add(SceneEnum::ADMIN, [
                'user_id' => $this['user_id'],
                'money'   => (float) $diffMoney,
                'remark'  => $data['remark'],
            ], [$storeUserName]);
        });
        return true;

    }
    /**
     * 获取分销商用户列表
     * @param string $search
     * @return mixed
     */
    public function getList(string $search = '')
    {
        // 检索查询条件
        $filter = [];
        if (!empty($search)) {
            $filter[] = ['user.nick_name|dealer.real_name|dealer.mobile', 'like', "%{$search}%"];
        }
        // 查询列表记录
        return $this->alias('dealer')
            ->field('dealer.*')
            ->with(['referee.avatar', 'user.avatar'])
            ->join('user', 'user.user_id = dealer.user_id')
            ->where($filter)
            ->where('dealer.is_delete', '=', 0)
            ->order(['dealer.create_time' => 'desc'])
            ->paginate(15);
    }

    /**
     * 编辑分销商用户
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
        return $this->save($data) !== false;
    }

    /**
     * 删除分销商用户
     * @return mixed
     */
    public function setDelete()
    {
        return $this->transaction(function () {
            // 获取一级团队成员ID集
            $RefereeModel = new RefereeModel;
            $team1Ids = $RefereeModel->getTeamUserIds($this['user_id'], 1);
            if (!empty($team1Ids)) {
                // 一级团队成员归属到平台
                $this->setFromplatform($team1Ids);
                // 一级推荐人ID
                $referee1Id = RefereeModel::getRefereeUserId($this['user_id'], 1, true);
                if ($referee1Id > 0) {
                    // 一级推荐人的成员数量(二级)
                    $this->setDecTeamNum($referee1Id, 2, count($team1Ids));
                    // 一级推荐人的成员数量(三级)
                    $team2Ids = $RefereeModel->getTeamUserIds($this['user_id'], 2);
                    !empty($team2Ids) && $this->setDecTeamNum($referee1Id, 3, count($team2Ids));
                    // 二级推荐人的成员数量(三级)
                    $referee2Id = RefereeModel::getRefereeUserId($this['user_id'], 2, true);
                    $referee2Id > 0 && $this->setDecTeamNum($referee2Id, 3, count($team1Ids));
                    // 清空分销商下级成员与上级推荐人的关系记录
                    $RefereeModel->onClearTop(array_merge($team1Ids, $team2Ids));
                }
            }
            // 清空下级推荐记录
            $RefereeModel->onClearTeam($this['user_id']);
            // 标记当前分销商记录为已删除
            return $this->delete();
        });
    }

    /**
     * 一级团队成员归属到平台
     * @param array $userIds
     * @return void
     */
    private function setFromplatform(array $userIds): void
    {
        static::updateBase(['referee_id' => 0], [
            ['user_id', 'in', $userIds],
            ['is_delete', '=', 0]
        ]);
    }

    /**
     * 删除用户的上级推荐关系
     * @param int $userId
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function onDeleteReferee(int $userId): bool
    {
        // 获取推荐人列表
        $list = RefereeModel::getRefereeList($userId);
        if (!$list->isEmpty()) {
            // 递减推荐人的下级成员数量
            foreach ($list as $item) {
                $item['dealer'] && $this->setDecTeamNum($item['dealer_id'], $item['level'], 1);
            }
            // 清空上级推荐关系
            (new RefereeModel)->onClearReferee($userId);
        }
        return true;
    }

    /**
     * 递减分销商成员数量
     * @param int $dealerId
     * @param int $level
     * @param int $number
     * @return mixed
     */
    private function setDecTeamNum(int $dealerId, int $level, int $number)
    {
        $field = [1 => 'first_num', 2 => 'second_num', 3 => 'third_num'];
        $where = [
            ['user_id', '=', $dealerId],
            ['is_delete', '=', 0]
        ];
        return $this->setDec($where, $field[$level], $number);
    }

    /**
     * 提现打款成功：累积提现佣金
     * @param int $userId
     * @param $money
     * @return bool|false
     */
    public static function totalMoney(int $userId, $money): bool
    {
        $model = self::detail($userId);
        return $model->save([
            'freeze_money' => $model['freeze_money'] - $money,
            'total_money' => $model['total_money'] + $money,
        ]);
    }

    /**
     * 提现驳回：解冻分销商资金
     * @param int $userId
     * @param $money
     * @return bool|false
     */
    public static function backFreezeMoney(int $userId, $money): bool
    {
        $model = self::detail($userId);
        return $model->save([
            'money' => $model['money'] + $money,
            'freeze_money' => $model['freeze_money'] - $money,
        ]);
    }
}