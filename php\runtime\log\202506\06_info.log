[2025-06-06 19:23:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208419
            [orderIds] => []
        )

)

[2025-06-06 19:23:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208430
            [orderIds] => []
        )

)

[2025-06-06 19:24:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208441
            [orderIds] => []
        )

)

[2025-06-06 19:24:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1749122650
            [orderIds] => []
        )

)

[2025-06-06 19:24:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1748604250
            [orderIds] => []
        )

)

[2025-06-06 19:24:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1748604250
            [orderIds] => []
        )

)

[2025-06-06 19:24:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-06 19:24:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-06 19:24:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208452
            [orderIds] => []
        )

)

[2025-06-06 19:24:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 19:24:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 19:24:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 19:24:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 19:24:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-06 19:24:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-06 19:24:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-06 19:24:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208463
            [orderIds] => []
        )

)

[2025-06-06 19:24:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208474
            [orderIds] => []
        )

)

[2025-06-06 19:24:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208485
            [orderIds] => []
        )

)

[2025-06-06 19:24:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208496
            [orderIds] => []
        )

)

[2025-06-06 19:25:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208507
            [orderIds] => []
        )

)

[2025-06-06 19:25:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208518
            [orderIds] => []
        )

)

[2025-06-06 19:25:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208529
            [orderIds] => []
        )

)

[2025-06-06 19:25:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208540
            [orderIds] => []
        )

)

[2025-06-06 19:25:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208551
            [orderIds] => []
        )

)

[2025-06-06 19:26:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208562
            [orderIds] => []
        )

)

[2025-06-06 19:26:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208573
            [orderIds] => []
        )

)

[2025-06-06 19:26:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208584
            [orderIds] => []
        )

)

[2025-06-06 19:26:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208595
            [orderIds] => []
        )

)

[2025-06-06 19:26:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208606
            [orderIds] => []
        )

)

[2025-06-06 19:26:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208617
            [orderIds] => []
        )

)

[2025-06-06 19:27:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208628
            [orderIds] => []
        )

)

[2025-06-06 19:27:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208639
            [orderIds] => []
        )

)

[2025-06-06 19:27:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208650
            [orderIds] => []
        )

)

[2025-06-06 19:27:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208661
            [orderIds] => []
        )

)

[2025-06-06 19:27:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208672
            [orderIds] => []
        )

)

[2025-06-06 19:28:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208683
            [orderIds] => []
        )

)

[2025-06-06 19:28:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208694
            [orderIds] => []
        )

)

[2025-06-06 19:28:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208705
            [orderIds] => []
        )

)

[2025-06-06 19:28:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208716
            [orderIds] => []
        )

)

[2025-06-06 19:28:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208727
            [orderIds] => []
        )

)

[2025-06-06 19:28:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208738
            [orderIds] => []
        )

)

[2025-06-06 19:29:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208749
            [orderIds] => []
        )

)

[2025-06-06 19:29:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208760
            [orderIds] => []
        )

)

[2025-06-06 19:29:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208771
            [orderIds] => []
        )

)

[2025-06-06 19:29:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208782
            [orderIds] => []
        )

)

[2025-06-06 19:29:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208793
            [orderIds] => []
        )

)

[2025-06-06 19:30:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208804
            [orderIds] => []
        )

)

[2025-06-06 19:30:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208815
            [orderIds] => []
        )

)

[2025-06-06 19:30:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208826
            [orderIds] => []
        )

)

[2025-06-06 19:30:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208837
            [orderIds] => []
        )

)

[2025-06-06 19:30:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208848
            [orderIds] => []
        )

)

[2025-06-06 19:30:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208859
            [orderIds] => []
        )

)

[2025-06-06 19:31:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208870
            [orderIds] => []
        )

)

[2025-06-06 19:31:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208881
            [orderIds] => []
        )

)

[2025-06-06 19:31:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208892
            [orderIds] => []
        )

)

[2025-06-06 19:31:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208903
            [orderIds] => []
        )

)

[2025-06-06 19:31:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208914
            [orderIds] => []
        )

)

[2025-06-06 19:32:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208925
            [orderIds] => []
        )

)

[2025-06-06 19:32:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208936
            [orderIds] => []
        )

)

[2025-06-06 19:32:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208947
            [orderIds] => []
        )

)

[2025-06-06 19:32:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208958
            [orderIds] => []
        )

)

[2025-06-06 19:32:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208969
            [orderIds] => []
        )

)

[2025-06-06 19:33:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208980
            [orderIds] => []
        )

)

[2025-06-06 19:33:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749208991
            [orderIds] => []
        )

)

[2025-06-06 19:33:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209002
            [orderIds] => []
        )

)

[2025-06-06 19:33:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209013
            [orderIds] => []
        )

)

[2025-06-06 19:33:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209024
            [orderIds] => []
        )

)

[2025-06-06 19:33:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209035
            [orderIds] => []
        )

)

[2025-06-06 19:34:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209046
            [orderIds] => []
        )

)

[2025-06-06 19:34:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 19:34:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 19:34:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 19:34:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 19:34:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209057
            [orderIds] => []
        )

)

[2025-06-06 19:34:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209068
            [orderIds] => []
        )

)

[2025-06-06 19:34:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209079
            [orderIds] => []
        )

)

[2025-06-06 19:34:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209090
            [orderIds] => []
        )

)

[2025-06-06 19:35:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209101
            [orderIds] => []
        )

)

[2025-06-06 19:35:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209112
            [orderIds] => []
        )

)

[2025-06-06 19:35:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209123
            [orderIds] => []
        )

)

[2025-06-06 19:35:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209134
            [orderIds] => []
        )

)

[2025-06-06 19:35:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209145
            [orderIds] => []
        )

)

[2025-06-06 19:35:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209156
            [orderIds] => []
        )

)

[2025-06-06 19:36:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209167
            [orderIds] => []
        )

)

[2025-06-06 19:36:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209178
            [orderIds] => []
        )

)

[2025-06-06 19:36:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209189
            [orderIds] => []
        )

)

[2025-06-06 19:36:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209200
            [orderIds] => []
        )

)

[2025-06-06 19:36:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209211
            [orderIds] => []
        )

)

[2025-06-06 19:37:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209222
            [orderIds] => []
        )

)

[2025-06-06 19:37:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209233
            [orderIds] => []
        )

)

[2025-06-06 19:37:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209244
            [orderIds] => []
        )

)

[2025-06-06 19:37:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209255
            [orderIds] => []
        )

)

[2025-06-06 19:37:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209266
            [orderIds] => []
        )

)

[2025-06-06 19:37:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209277
            [orderIds] => []
        )

)

[2025-06-06 19:38:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209288
            [orderIds] => []
        )

)

[2025-06-06 19:38:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209299
            [orderIds] => []
        )

)

[2025-06-06 19:38:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209310
            [orderIds] => []
        )

)

[2025-06-06 19:38:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209321
            [orderIds] => []
        )

)

[2025-06-06 19:38:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209332
            [orderIds] => []
        )

)

[2025-06-06 19:39:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209343
            [orderIds] => []
        )

)

[2025-06-06 19:39:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209354
            [orderIds] => []
        )

)

[2025-06-06 19:39:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209365
            [orderIds] => []
        )

)

[2025-06-06 19:39:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209376
            [orderIds] => []
        )

)

[2025-06-06 19:39:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209387
            [orderIds] => []
        )

)

[2025-06-06 19:39:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209398
            [orderIds] => []
        )

)

[2025-06-06 19:40:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209409
            [orderIds] => []
        )

)

[2025-06-06 19:40:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209420
            [orderIds] => []
        )

)

[2025-06-06 19:40:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209431
            [orderIds] => []
        )

)

[2025-06-06 19:40:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209442
            [orderIds] => []
        )

)

[2025-06-06 19:40:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209453
            [orderIds] => []
        )

)

[2025-06-06 19:41:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209464
            [orderIds] => []
        )

)

[2025-06-06 19:41:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209475
            [orderIds] => []
        )

)

[2025-06-06 19:41:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209486
            [orderIds] => []
        )

)

[2025-06-06 19:41:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209497
            [orderIds] => []
        )

)

[2025-06-06 19:41:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209508
            [orderIds] => []
        )

)

[2025-06-06 19:41:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209519
            [orderIds] => []
        )

)

[2025-06-06 19:42:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209530
            [orderIds] => []
        )

)

[2025-06-06 19:42:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209541
            [orderIds] => []
        )

)

[2025-06-06 19:42:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209552
            [orderIds] => []
        )

)

[2025-06-06 19:42:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209563
            [orderIds] => []
        )

)

[2025-06-06 19:42:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209574
            [orderIds] => []
        )

)

[2025-06-06 19:43:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209585
            [orderIds] => []
        )

)

[2025-06-06 19:43:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209596
            [orderIds] => []
        )

)

[2025-06-06 19:43:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209607
            [orderIds] => []
        )

)

[2025-06-06 19:43:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209618
            [orderIds] => []
        )

)

[2025-06-06 19:43:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209629
            [orderIds] => []
        )

)

[2025-06-06 19:44:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209640
            [orderIds] => []
        )

)

[2025-06-06 19:44:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209651
            [orderIds] => []
        )

)

[2025-06-06 19:44:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 19:44:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 19:44:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 19:44:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 19:44:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209662
            [orderIds] => []
        )

)

[2025-06-06 19:44:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209673
            [orderIds] => []
        )

)

[2025-06-06 19:44:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209684
            [orderIds] => []
        )

)

[2025-06-06 19:44:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209695
            [orderIds] => []
        )

)

[2025-06-06 19:45:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209706
            [orderIds] => []
        )

)

[2025-06-06 19:45:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209717
            [orderIds] => []
        )

)

[2025-06-06 19:45:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209728
            [orderIds] => []
        )

)

[2025-06-06 19:45:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209739
            [orderIds] => []
        )

)

[2025-06-06 19:45:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209750
            [orderIds] => []
        )

)

[2025-06-06 19:46:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209761
            [orderIds] => []
        )

)

[2025-06-06 19:46:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209772
            [orderIds] => []
        )

)

[2025-06-06 19:46:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209783
            [orderIds] => []
        )

)

[2025-06-06 19:46:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209794
            [orderIds] => []
        )

)

[2025-06-06 19:46:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209805
            [orderIds] => []
        )

)

[2025-06-06 19:46:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209816
            [orderIds] => []
        )

)

[2025-06-06 19:47:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209827
            [orderIds] => []
        )

)

[2025-06-06 19:47:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209838
            [orderIds] => []
        )

)

[2025-06-06 19:47:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209849
            [orderIds] => []
        )

)

[2025-06-06 19:47:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209860
            [orderIds] => []
        )

)

[2025-06-06 19:47:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209871
            [orderIds] => []
        )

)

[2025-06-06 19:48:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209882
            [orderIds] => []
        )

)

[2025-06-06 19:48:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209893
            [orderIds] => []
        )

)

[2025-06-06 19:48:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209904
            [orderIds] => []
        )

)

[2025-06-06 19:48:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209915
            [orderIds] => []
        )

)

[2025-06-06 19:48:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209926
            [orderIds] => []
        )

)

[2025-06-06 19:48:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209937
            [orderIds] => []
        )

)

[2025-06-06 19:49:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209948
            [orderIds] => []
        )

)

[2025-06-06 19:49:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209959
            [orderIds] => []
        )

)

[2025-06-06 19:49:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209970
            [orderIds] => []
        )

)

[2025-06-06 19:49:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209981
            [orderIds] => []
        )

)

[2025-06-06 19:49:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749209992
            [orderIds] => []
        )

)

[2025-06-06 19:50:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210003
            [orderIds] => []
        )

)

[2025-06-06 19:50:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210014
            [orderIds] => []
        )

)

[2025-06-06 19:50:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210025
            [orderIds] => []
        )

)

[2025-06-06 19:50:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210036
            [orderIds] => []
        )

)

[2025-06-06 19:50:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210047
            [orderIds] => []
        )

)

[2025-06-06 19:50:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210058
            [orderIds] => []
        )

)

[2025-06-06 19:51:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210069
            [orderIds] => []
        )

)

[2025-06-06 19:51:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210080
            [orderIds] => []
        )

)

[2025-06-06 19:51:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210091
            [orderIds] => []
        )

)

[2025-06-06 19:51:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210102
            [orderIds] => []
        )

)

[2025-06-06 19:51:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210113
            [orderIds] => []
        )

)

[2025-06-06 19:52:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210124
            [orderIds] => []
        )

)

[2025-06-06 19:52:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210135
            [orderIds] => []
        )

)

[2025-06-06 19:52:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210146
            [orderIds] => []
        )

)

[2025-06-06 19:52:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210157
            [orderIds] => []
        )

)

[2025-06-06 19:52:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210168
            [orderIds] => []
        )

)

[2025-06-06 19:52:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210179
            [orderIds] => []
        )

)

[2025-06-06 19:53:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210190
            [orderIds] => []
        )

)

[2025-06-06 19:53:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210201
            [orderIds] => []
        )

)

[2025-06-06 19:53:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210212
            [orderIds] => []
        )

)

[2025-06-06 19:53:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210223
            [orderIds] => []
        )

)

[2025-06-06 19:53:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210234
            [orderIds] => []
        )

)

[2025-06-06 19:54:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210245
            [orderIds] => []
        )

)

[2025-06-06 19:54:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1749124451
            [orderIds] => []
        )

)

[2025-06-06 19:54:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1748606051
            [orderIds] => []
        )

)

[2025-06-06 19:54:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1748606051
            [orderIds] => []
        )

)

[2025-06-06 19:54:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-06 19:54:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-06 19:54:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-06 19:54:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-06 19:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-06 19:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 19:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 19:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 19:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 19:54:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210256
            [orderIds] => []
        )

)

[2025-06-06 19:54:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210267
            [orderIds] => []
        )

)

[2025-06-06 19:54:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210278
            [orderIds] => []
        )

)

[2025-06-06 19:54:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210289
            [orderIds] => []
        )

)

[2025-06-06 19:55:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210300
            [orderIds] => []
        )

)

[2025-06-06 19:55:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210311
            [orderIds] => []
        )

)

[2025-06-06 19:55:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210322
            [orderIds] => []
        )

)

[2025-06-06 19:55:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210333
            [orderIds] => []
        )

)

[2025-06-06 19:55:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210344
            [orderIds] => []
        )

)

[2025-06-06 19:55:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210355
            [orderIds] => []
        )

)

[2025-06-06 19:56:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210366
            [orderIds] => []
        )

)

[2025-06-06 19:56:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210377
            [orderIds] => []
        )

)

[2025-06-06 19:56:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210388
            [orderIds] => []
        )

)

[2025-06-06 19:56:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210399
            [orderIds] => []
        )

)

[2025-06-06 19:56:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210410
            [orderIds] => []
        )

)

[2025-06-06 19:57:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210421
            [orderIds] => []
        )

)

[2025-06-06 19:57:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210432
            [orderIds] => []
        )

)

[2025-06-06 19:57:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210443
            [orderIds] => []
        )

)

[2025-06-06 19:57:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210454
            [orderIds] => []
        )

)

[2025-06-06 19:57:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210465
            [orderIds] => []
        )

)

[2025-06-06 19:57:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210476
            [orderIds] => []
        )

)

[2025-06-06 19:58:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210487
            [orderIds] => []
        )

)

[2025-06-06 19:58:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210498
            [orderIds] => []
        )

)

[2025-06-06 19:58:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210509
            [orderIds] => []
        )

)

[2025-06-06 19:58:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210520
            [orderIds] => []
        )

)

[2025-06-06 19:58:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210531
            [orderIds] => []
        )

)

[2025-06-06 19:59:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210542
            [orderIds] => []
        )

)

[2025-06-06 19:59:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210553
            [orderIds] => []
        )

)

[2025-06-06 19:59:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210564
            [orderIds] => []
        )

)

[2025-06-06 19:59:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210575
            [orderIds] => []
        )

)

[2025-06-06 19:59:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210586
            [orderIds] => []
        )

)

[2025-06-06 19:59:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210597
            [orderIds] => []
        )

)

[2025-06-06 20:00:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210608
            [orderIds] => []
        )

)

[2025-06-06 20:00:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210619
            [orderIds] => []
        )

)

[2025-06-06 20:00:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210630
            [orderIds] => []
        )

)

[2025-06-06 20:00:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210641
            [orderIds] => []
        )

)

[2025-06-06 20:00:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210652
            [orderIds] => []
        )

)

[2025-06-06 20:01:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210663
            [orderIds] => []
        )

)

[2025-06-06 20:01:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210674
            [orderIds] => []
        )

)

[2025-06-06 20:01:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210685
            [orderIds] => []
        )

)

[2025-06-06 20:01:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210696
            [orderIds] => []
        )

)

[2025-06-06 20:01:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210707
            [orderIds] => []
        )

)

[2025-06-06 20:01:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210718
            [orderIds] => []
        )

)

[2025-06-06 20:02:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210729
            [orderIds] => []
        )

)

[2025-06-06 20:02:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210740
            [orderIds] => []
        )

)

[2025-06-06 20:02:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210751
            [orderIds] => []
        )

)

[2025-06-06 20:02:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210762
            [orderIds] => []
        )

)

[2025-06-06 20:02:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210773
            [orderIds] => []
        )

)

[2025-06-06 20:03:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210784
            [orderIds] => []
        )

)

[2025-06-06 20:03:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210795
            [orderIds] => []
        )

)

[2025-06-06 20:03:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210806
            [orderIds] => []
        )

)

[2025-06-06 20:03:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210817
            [orderIds] => []
        )

)

[2025-06-06 20:03:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210828
            [orderIds] => []
        )

)

[2025-06-06 20:03:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210839
            [orderIds] => []
        )

)

[2025-06-06 20:04:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210850
            [orderIds] => []
        )

)

[2025-06-06 20:04:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 20:04:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 20:04:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 20:04:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 20:04:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210861
            [orderIds] => []
        )

)

[2025-06-06 20:04:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210872
            [orderIds] => []
        )

)

[2025-06-06 20:04:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210883
            [orderIds] => []
        )

)

[2025-06-06 20:04:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210894
            [orderIds] => []
        )

)

[2025-06-06 20:05:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210905
            [orderIds] => []
        )

)

[2025-06-06 20:05:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210916
            [orderIds] => []
        )

)

[2025-06-06 20:05:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210927
            [orderIds] => []
        )

)

[2025-06-06 20:05:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210938
            [orderIds] => []
        )

)

[2025-06-06 20:05:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210949
            [orderIds] => []
        )

)

[2025-06-06 20:06:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210960
            [orderIds] => []
        )

)

[2025-06-06 20:06:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210971
            [orderIds] => []
        )

)

[2025-06-06 20:06:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210982
            [orderIds] => []
        )

)

[2025-06-06 20:06:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749210993
            [orderIds] => []
        )

)

[2025-06-06 20:06:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211004
            [orderIds] => []
        )

)

[2025-06-06 20:06:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211015
            [orderIds] => []
        )

)

[2025-06-06 20:07:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211026
            [orderIds] => []
        )

)

[2025-06-06 20:07:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211037
            [orderIds] => []
        )

)

[2025-06-06 20:07:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211048
            [orderIds] => []
        )

)

[2025-06-06 20:07:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211059
            [orderIds] => []
        )

)

[2025-06-06 20:07:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211070
            [orderIds] => []
        )

)

[2025-06-06 20:08:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211081
            [orderIds] => []
        )

)

[2025-06-06 20:08:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211092
            [orderIds] => []
        )

)

[2025-06-06 20:08:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211103
            [orderIds] => []
        )

)

[2025-06-06 20:08:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211114
            [orderIds] => []
        )

)

[2025-06-06 20:08:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211125
            [orderIds] => []
        )

)

[2025-06-06 20:08:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211136
            [orderIds] => []
        )

)

[2025-06-06 20:09:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211147
            [orderIds] => []
        )

)

[2025-06-06 20:09:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211158
            [orderIds] => []
        )

)

[2025-06-06 20:09:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211169
            [orderIds] => []
        )

)

[2025-06-06 20:09:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211180
            [orderIds] => []
        )

)

[2025-06-06 20:09:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211191
            [orderIds] => []
        )

)

[2025-06-06 20:10:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211202
            [orderIds] => []
        )

)

[2025-06-06 20:10:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211213
            [orderIds] => []
        )

)

[2025-06-06 20:10:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211224
            [orderIds] => []
        )

)

[2025-06-06 20:10:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211235
            [orderIds] => []
        )

)

[2025-06-06 20:10:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211246
            [orderIds] => []
        )

)

[2025-06-06 20:10:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211257
            [orderIds] => []
        )

)

[2025-06-06 20:11:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211268
            [orderIds] => []
        )

)

[2025-06-06 20:11:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211279
            [orderIds] => []
        )

)

[2025-06-06 20:11:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211290
            [orderIds] => []
        )

)

[2025-06-06 20:11:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211301
            [orderIds] => []
        )

)

[2025-06-06 20:11:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211312
            [orderIds] => []
        )

)

[2025-06-06 20:12:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211323
            [orderIds] => []
        )

)

[2025-06-06 20:12:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211334
            [orderIds] => []
        )

)

[2025-06-06 20:12:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211345
            [orderIds] => []
        )

)

[2025-06-06 20:12:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211356
            [orderIds] => []
        )

)

[2025-06-06 20:12:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211367
            [orderIds] => []
        )

)

[2025-06-06 20:12:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211378
            [orderIds] => []
        )

)

[2025-06-06 20:13:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211389
            [orderIds] => []
        )

)

[2025-06-06 20:13:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211400
            [orderIds] => []
        )

)

[2025-06-06 20:13:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211411
            [orderIds] => []
        )

)

[2025-06-06 20:13:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211422
            [orderIds] => []
        )

)

[2025-06-06 20:13:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211433
            [orderIds] => []
        )

)

[2025-06-06 20:14:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211444
            [orderIds] => []
        )

)

[2025-06-06 20:14:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211455
            [orderIds] => []
        )

)

[2025-06-06 20:14:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 20:14:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 20:14:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 20:14:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 20:14:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211466
            [orderIds] => []
        )

)

[2025-06-06 20:14:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211477
            [orderIds] => []
        )

)

[2025-06-06 20:14:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211488
            [orderIds] => []
        )

)

[2025-06-06 20:14:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211499
            [orderIds] => []
        )

)

[2025-06-06 20:15:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211510
            [orderIds] => []
        )

)

[2025-06-06 20:15:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211521
            [orderIds] => []
        )

)

[2025-06-06 20:15:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211532
            [orderIds] => []
        )

)

[2025-06-06 20:15:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211543
            [orderIds] => []
        )

)

[2025-06-06 20:15:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211554
            [orderIds] => []
        )

)

[2025-06-06 20:16:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211565
            [orderIds] => []
        )

)

[2025-06-06 20:16:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211576
            [orderIds] => []
        )

)

[2025-06-06 20:16:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211587
            [orderIds] => []
        )

)

[2025-06-06 20:16:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211598
            [orderIds] => []
        )

)

[2025-06-06 20:16:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211609
            [orderIds] => []
        )

)

[2025-06-06 20:17:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211620
            [orderIds] => []
        )

)

[2025-06-06 20:17:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211631
            [orderIds] => []
        )

)

[2025-06-06 20:17:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211642
            [orderIds] => []
        )

)

[2025-06-06 20:17:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211653
            [orderIds] => []
        )

)

[2025-06-06 20:17:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211664
            [orderIds] => []
        )

)

[2025-06-06 20:17:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211675
            [orderIds] => []
        )

)

[2025-06-06 20:18:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211686
            [orderIds] => []
        )

)

[2025-06-06 20:18:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211697
            [orderIds] => []
        )

)

[2025-06-06 20:18:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211708
            [orderIds] => []
        )

)

[2025-06-06 20:18:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211719
            [orderIds] => []
        )

)

[2025-06-06 20:18:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211730
            [orderIds] => []
        )

)

[2025-06-06 20:19:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211741
            [orderIds] => []
        )

)

[2025-06-06 20:19:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211752
            [orderIds] => []
        )

)

[2025-06-06 20:19:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211763
            [orderIds] => []
        )

)

[2025-06-06 20:19:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211774
            [orderIds] => []
        )

)

[2025-06-06 20:19:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211785
            [orderIds] => []
        )

)

[2025-06-06 20:19:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211796
            [orderIds] => []
        )

)

[2025-06-06 20:20:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211807
            [orderIds] => []
        )

)

[2025-06-06 20:20:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211818
            [orderIds] => []
        )

)

[2025-06-06 20:20:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211829
            [orderIds] => []
        )

)

[2025-06-06 20:20:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211840
            [orderIds] => []
        )

)

[2025-06-06 20:20:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211851
            [orderIds] => []
        )

)

[2025-06-06 20:21:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211862
            [orderIds] => []
        )

)

[2025-06-06 20:21:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211873
            [orderIds] => []
        )

)

[2025-06-06 20:21:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211884
            [orderIds] => []
        )

)

[2025-06-06 20:21:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211895
            [orderIds] => []
        )

)

[2025-06-06 20:21:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211906
            [orderIds] => []
        )

)

[2025-06-06 20:21:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211917
            [orderIds] => []
        )

)

[2025-06-06 20:22:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211928
            [orderIds] => []
        )

)

[2025-06-06 20:22:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211939
            [orderIds] => []
        )

)

[2025-06-06 20:22:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211950
            [orderIds] => []
        )

)

[2025-06-06 20:22:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211961
            [orderIds] => []
        )

)

[2025-06-06 20:22:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211972
            [orderIds] => []
        )

)

[2025-06-06 20:23:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211983
            [orderIds] => []
        )

)

[2025-06-06 20:23:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749211994
            [orderIds] => []
        )

)

[2025-06-06 20:23:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212005
            [orderIds] => []
        )

)

[2025-06-06 20:23:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212016
            [orderIds] => []
        )

)

[2025-06-06 20:23:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212027
            [orderIds] => []
        )

)

[2025-06-06 20:23:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212038
            [orderIds] => []
        )

)

[2025-06-06 20:24:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212049
            [orderIds] => []
        )

)

[2025-06-06 20:24:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1749126252
            [orderIds] => []
        )

)

[2025-06-06 20:24:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1748607852
            [orderIds] => []
        )

)

[2025-06-06 20:24:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1748607852
            [orderIds] => []
        )

)

[2025-06-06 20:24:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-06 20:24:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-06 20:24:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-06 20:24:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-06 20:24:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-06 20:24:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 20:24:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 20:24:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 20:24:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 20:24:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212060
            [orderIds] => []
        )

)

[2025-06-06 20:24:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212071
            [orderIds] => []
        )

)

[2025-06-06 20:24:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212082
            [orderIds] => []
        )

)

[2025-06-06 20:24:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212093
            [orderIds] => []
        )

)

[2025-06-06 20:25:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212104
            [orderIds] => []
        )

)

[2025-06-06 20:25:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212115
            [orderIds] => []
        )

)

[2025-06-06 20:25:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212126
            [orderIds] => []
        )

)

[2025-06-06 20:25:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212137
            [orderIds] => []
        )

)

[2025-06-06 20:25:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212148
            [orderIds] => []
        )

)

[2025-06-06 20:25:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212159
            [orderIds] => []
        )

)

[2025-06-06 20:26:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212170
            [orderIds] => []
        )

)

[2025-06-06 20:26:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212181
            [orderIds] => []
        )

)

[2025-06-06 20:26:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212192
            [orderIds] => []
        )

)

[2025-06-06 20:26:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212203
            [orderIds] => []
        )

)

[2025-06-06 20:26:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212214
            [orderIds] => []
        )

)

[2025-06-06 20:27:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212225
            [orderIds] => []
        )

)

[2025-06-06 20:27:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212236
            [orderIds] => []
        )

)

[2025-06-06 20:27:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212247
            [orderIds] => []
        )

)

[2025-06-06 20:27:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212258
            [orderIds] => []
        )

)

[2025-06-06 20:27:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212269
            [orderIds] => []
        )

)

[2025-06-06 20:28:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212280
            [orderIds] => []
        )

)

[2025-06-06 20:28:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212291
            [orderIds] => []
        )

)

[2025-06-06 20:28:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212302
            [orderIds] => []
        )

)

[2025-06-06 20:28:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212313
            [orderIds] => []
        )

)

[2025-06-06 20:28:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212324
            [orderIds] => []
        )

)

[2025-06-06 20:28:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212335
            [orderIds] => []
        )

)

[2025-06-06 20:29:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212346
            [orderIds] => []
        )

)

[2025-06-06 20:29:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212357
            [orderIds] => []
        )

)

[2025-06-06 20:29:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212368
            [orderIds] => []
        )

)

[2025-06-06 20:29:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212379
            [orderIds] => []
        )

)

[2025-06-06 20:29:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212390
            [orderIds] => []
        )

)

[2025-06-06 20:30:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212401
            [orderIds] => []
        )

)

[2025-06-06 20:30:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212412
            [orderIds] => []
        )

)

[2025-06-06 20:30:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212423
            [orderIds] => []
        )

)

[2025-06-06 20:30:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212434
            [orderIds] => []
        )

)

[2025-06-06 20:30:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212445
            [orderIds] => []
        )

)

[2025-06-06 20:30:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212456
            [orderIds] => []
        )

)

[2025-06-06 20:31:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212467
            [orderIds] => []
        )

)

[2025-06-06 20:31:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212478
            [orderIds] => []
        )

)

[2025-06-06 20:31:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212489
            [orderIds] => []
        )

)

[2025-06-06 20:31:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212500
            [orderIds] => []
        )

)

[2025-06-06 20:31:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212511
            [orderIds] => []
        )

)

[2025-06-06 20:32:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212522
            [orderIds] => []
        )

)

[2025-06-06 20:32:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212533
            [orderIds] => []
        )

)

[2025-06-06 20:32:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212544
            [orderIds] => []
        )

)

[2025-06-06 20:32:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212555
            [orderIds] => []
        )

)

[2025-06-06 20:32:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212566
            [orderIds] => []
        )

)

[2025-06-06 20:32:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212577
            [orderIds] => []
        )

)

[2025-06-06 20:33:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212588
            [orderIds] => []
        )

)

[2025-06-06 20:33:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212599
            [orderIds] => []
        )

)

[2025-06-06 20:33:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212610
            [orderIds] => []
        )

)

[2025-06-06 20:33:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212621
            [orderIds] => []
        )

)

[2025-06-06 20:33:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212632
            [orderIds] => []
        )

)

[2025-06-06 20:34:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212643
            [orderIds] => []
        )

)

[2025-06-06 20:34:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212654
            [orderIds] => []
        )

)

[2025-06-06 20:34:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 20:34:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 20:34:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 20:34:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 20:34:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212665
            [orderIds] => []
        )

)

[2025-06-06 20:34:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212676
            [orderIds] => []
        )

)

[2025-06-06 20:34:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212687
            [orderIds] => []
        )

)

[2025-06-06 20:34:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212698
            [orderIds] => []
        )

)

[2025-06-06 20:35:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212709
            [orderIds] => []
        )

)

[2025-06-06 20:35:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212720
            [orderIds] => []
        )

)

[2025-06-06 20:35:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212731
            [orderIds] => []
        )

)

[2025-06-06 20:35:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212742
            [orderIds] => []
        )

)

[2025-06-06 20:35:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212753
            [orderIds] => []
        )

)

[2025-06-06 20:36:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212764
            [orderIds] => []
        )

)

[2025-06-06 20:36:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212775
            [orderIds] => []
        )

)

[2025-06-06 20:36:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212786
            [orderIds] => []
        )

)

[2025-06-06 20:36:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212797
            [orderIds] => []
        )

)

[2025-06-06 20:36:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212808
            [orderIds] => []
        )

)

[2025-06-06 20:36:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212819
            [orderIds] => []
        )

)

[2025-06-06 20:37:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212830
            [orderIds] => []
        )

)

[2025-06-06 20:37:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212841
            [orderIds] => []
        )

)

[2025-06-06 20:37:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212852
            [orderIds] => []
        )

)

[2025-06-06 20:37:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212863
            [orderIds] => []
        )

)

[2025-06-06 20:37:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212874
            [orderIds] => []
        )

)

[2025-06-06 20:38:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212885
            [orderIds] => []
        )

)

[2025-06-06 20:38:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212896
            [orderIds] => []
        )

)

[2025-06-06 20:38:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212907
            [orderIds] => []
        )

)

[2025-06-06 20:38:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212918
            [orderIds] => []
        )

)

[2025-06-06 20:38:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212929
            [orderIds] => []
        )

)

[2025-06-06 20:39:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212940
            [orderIds] => []
        )

)

[2025-06-06 20:39:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212951
            [orderIds] => []
        )

)

[2025-06-06 20:39:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212962
            [orderIds] => []
        )

)

[2025-06-06 20:39:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212973
            [orderIds] => []
        )

)

[2025-06-06 20:39:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212984
            [orderIds] => []
        )

)

[2025-06-06 20:39:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749212995
            [orderIds] => []
        )

)

[2025-06-06 20:40:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213006
            [orderIds] => []
        )

)

[2025-06-06 20:40:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213017
            [orderIds] => []
        )

)

[2025-06-06 20:40:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213028
            [orderIds] => []
        )

)

[2025-06-06 20:40:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213039
            [orderIds] => []
        )

)

[2025-06-06 20:40:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213050
            [orderIds] => []
        )

)

[2025-06-06 20:41:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213061
            [orderIds] => []
        )

)

[2025-06-06 20:41:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213072
            [orderIds] => []
        )

)

[2025-06-06 20:41:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213083
            [orderIds] => []
        )

)

[2025-06-06 20:41:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213094
            [orderIds] => []
        )

)

[2025-06-06 20:41:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213105
            [orderIds] => []
        )

)

[2025-06-06 20:41:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213116
            [orderIds] => []
        )

)

[2025-06-06 20:42:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213127
            [orderIds] => []
        )

)

[2025-06-06 20:42:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213138
            [orderIds] => []
        )

)

[2025-06-06 20:42:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213149
            [orderIds] => []
        )

)

[2025-06-06 20:42:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213160
            [orderIds] => []
        )

)

[2025-06-06 20:42:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213171
            [orderIds] => []
        )

)

[2025-06-06 20:43:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213182
            [orderIds] => []
        )

)

[2025-06-06 20:43:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213193
            [orderIds] => []
        )

)

[2025-06-06 20:43:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213204
            [orderIds] => []
        )

)

[2025-06-06 20:43:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213215
            [orderIds] => []
        )

)

[2025-06-06 20:43:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213226
            [orderIds] => []
        )

)

[2025-06-06 20:43:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213237
            [orderIds] => []
        )

)

[2025-06-06 20:44:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213248
            [orderIds] => []
        )

)

[2025-06-06 20:44:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213259
            [orderIds] => []
        )

)

[2025-06-06 20:44:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 20:44:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 20:44:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 20:44:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 20:44:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213270
            [orderIds] => []
        )

)

[2025-06-06 20:44:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213281
            [orderIds] => []
        )

)

[2025-06-06 20:44:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213292
            [orderIds] => []
        )

)

[2025-06-06 20:45:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213303
            [orderIds] => []
        )

)

[2025-06-06 20:45:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213314
            [orderIds] => []
        )

)

[2025-06-06 20:45:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213325
            [orderIds] => []
        )

)

[2025-06-06 20:45:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213336
            [orderIds] => []
        )

)

[2025-06-06 20:45:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213347
            [orderIds] => []
        )

)

[2025-06-06 20:45:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213358
            [orderIds] => []
        )

)

[2025-06-06 20:46:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213369
            [orderIds] => []
        )

)

[2025-06-06 20:46:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213380
            [orderIds] => []
        )

)

[2025-06-06 20:46:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213391
            [orderIds] => []
        )

)

[2025-06-06 20:46:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213402
            [orderIds] => []
        )

)

[2025-06-06 20:46:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213413
            [orderIds] => []
        )

)

[2025-06-06 20:47:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213424
            [orderIds] => []
        )

)

[2025-06-06 20:47:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213435
            [orderIds] => []
        )

)

[2025-06-06 20:47:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213446
            [orderIds] => []
        )

)

[2025-06-06 20:47:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213457
            [orderIds] => []
        )

)

[2025-06-06 20:47:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213468
            [orderIds] => []
        )

)

[2025-06-06 20:47:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213479
            [orderIds] => []
        )

)

[2025-06-06 20:48:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213490
            [orderIds] => []
        )

)

[2025-06-06 20:48:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213501
            [orderIds] => []
        )

)

[2025-06-06 20:48:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213512
            [orderIds] => []
        )

)

[2025-06-06 20:48:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213523
            [orderIds] => []
        )

)

[2025-06-06 20:48:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213534
            [orderIds] => []
        )

)

[2025-06-06 20:49:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213545
            [orderIds] => []
        )

)

[2025-06-06 20:49:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213556
            [orderIds] => []
        )

)

[2025-06-06 20:49:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213567
            [orderIds] => []
        )

)

[2025-06-06 20:49:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213578
            [orderIds] => []
        )

)

[2025-06-06 20:49:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213589
            [orderIds] => []
        )

)

[2025-06-06 20:50:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213600
            [orderIds] => []
        )

)

[2025-06-06 20:50:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213611
            [orderIds] => []
        )

)

[2025-06-06 20:50:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213622
            [orderIds] => []
        )

)

[2025-06-06 20:50:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213633
            [orderIds] => []
        )

)

[2025-06-06 20:50:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213644
            [orderIds] => []
        )

)

[2025-06-06 20:50:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213655
            [orderIds] => []
        )

)

[2025-06-06 20:51:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213666
            [orderIds] => []
        )

)

[2025-06-06 20:51:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213677
            [orderIds] => []
        )

)

[2025-06-06 20:51:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213688
            [orderIds] => []
        )

)

[2025-06-06 20:51:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213699
            [orderIds] => []
        )

)

[2025-06-06 20:51:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213710
            [orderIds] => []
        )

)

[2025-06-06 20:52:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213721
            [orderIds] => []
        )

)

[2025-06-06 20:52:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213732
            [orderIds] => []
        )

)

[2025-06-06 20:52:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213743
            [orderIds] => []
        )

)

[2025-06-06 20:52:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213754
            [orderIds] => []
        )

)

[2025-06-06 20:52:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213765
            [orderIds] => []
        )

)

[2025-06-06 20:52:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213776
            [orderIds] => []
        )

)

[2025-06-06 20:53:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213787
            [orderIds] => []
        )

)

[2025-06-06 20:53:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213798
            [orderIds] => []
        )

)

[2025-06-06 20:53:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213809
            [orderIds] => []
        )

)

[2025-06-06 20:53:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213820
            [orderIds] => []
        )

)

[2025-06-06 20:53:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213831
            [orderIds] => []
        )

)

[2025-06-06 20:54:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213842
            [orderIds] => []
        )

)

[2025-06-06 20:54:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1749128053
            [orderIds] => []
        )

)

[2025-06-06 20:54:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1748609653
            [orderIds] => []
        )

)

[2025-06-06 20:54:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1748609653
            [orderIds] => []
        )

)

[2025-06-06 20:54:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-06 20:54:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213853
            [orderIds] => []
        )

)

[2025-06-06 20:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-06 20:54:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-06 20:54:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-06 20:54:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-06 20:54:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 20:54:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 20:54:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 20:54:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 20:54:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213864
            [orderIds] => []
        )

)

[2025-06-06 20:54:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213875
            [orderIds] => []
        )

)

[2025-06-06 20:54:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213886
            [orderIds] => []
        )

)

[2025-06-06 20:54:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213897
            [orderIds] => []
        )

)

[2025-06-06 20:55:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213908
            [orderIds] => []
        )

)

[2025-06-06 20:55:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213919
            [orderIds] => []
        )

)

[2025-06-06 20:55:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213930
            [orderIds] => []
        )

)

[2025-06-06 20:55:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213941
            [orderIds] => []
        )

)

[2025-06-06 20:55:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213952
            [orderIds] => []
        )

)

[2025-06-06 20:56:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213963
            [orderIds] => []
        )

)

[2025-06-06 20:56:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213974
            [orderIds] => []
        )

)

[2025-06-06 20:56:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213985
            [orderIds] => []
        )

)

[2025-06-06 20:56:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749213996
            [orderIds] => []
        )

)

[2025-06-06 20:56:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214007
            [orderIds] => []
        )

)

[2025-06-06 20:56:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214018
            [orderIds] => []
        )

)

[2025-06-06 20:57:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214029
            [orderIds] => []
        )

)

[2025-06-06 20:57:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214040
            [orderIds] => []
        )

)

[2025-06-06 20:57:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214051
            [orderIds] => []
        )

)

[2025-06-06 20:57:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214062
            [orderIds] => []
        )

)

[2025-06-06 20:57:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214073
            [orderIds] => []
        )

)

[2025-06-06 20:58:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214084
            [orderIds] => []
        )

)

[2025-06-06 20:58:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214095
            [orderIds] => []
        )

)

[2025-06-06 20:58:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214106
            [orderIds] => []
        )

)

[2025-06-06 20:58:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214117
            [orderIds] => []
        )

)

[2025-06-06 20:58:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214128
            [orderIds] => []
        )

)

[2025-06-06 20:58:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214139
            [orderIds] => []
        )

)

[2025-06-06 20:59:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214150
            [orderIds] => []
        )

)

[2025-06-06 20:59:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214161
            [orderIds] => []
        )

)

[2025-06-06 20:59:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214172
            [orderIds] => []
        )

)

[2025-06-06 20:59:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214183
            [orderIds] => []
        )

)

[2025-06-06 20:59:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214194
            [orderIds] => []
        )

)

[2025-06-06 21:00:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214205
            [orderIds] => []
        )

)

[2025-06-06 21:00:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214216
            [orderIds] => []
        )

)

[2025-06-06 21:00:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214227
            [orderIds] => []
        )

)

[2025-06-06 21:00:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214238
            [orderIds] => []
        )

)

[2025-06-06 21:00:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214249
            [orderIds] => []
        )

)

[2025-06-06 21:01:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214260
            [orderIds] => []
        )

)

[2025-06-06 21:01:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214271
            [orderIds] => []
        )

)

[2025-06-06 21:01:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214282
            [orderIds] => []
        )

)

[2025-06-06 21:01:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214293
            [orderIds] => []
        )

)

[2025-06-06 21:01:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214304
            [orderIds] => []
        )

)

[2025-06-06 21:01:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214315
            [orderIds] => []
        )

)

[2025-06-06 21:02:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214326
            [orderIds] => []
        )

)

[2025-06-06 21:02:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214337
            [orderIds] => []
        )

)

[2025-06-06 21:02:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214348
            [orderIds] => []
        )

)

[2025-06-06 21:02:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214359
            [orderIds] => []
        )

)

[2025-06-06 21:02:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214370
            [orderIds] => []
        )

)

[2025-06-06 21:03:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214381
            [orderIds] => []
        )

)

[2025-06-06 21:03:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214392
            [orderIds] => []
        )

)

[2025-06-06 21:03:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214403
            [orderIds] => []
        )

)

[2025-06-06 21:03:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214414
            [orderIds] => []
        )

)

[2025-06-06 21:03:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214425
            [orderIds] => []
        )

)

[2025-06-06 21:03:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214436
            [orderIds] => []
        )

)

[2025-06-06 21:04:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214447
            [orderIds] => []
        )

)

[2025-06-06 21:04:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214458
            [orderIds] => []
        )

)

[2025-06-06 21:04:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 21:04:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 21:04:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 21:04:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 21:04:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214469
            [orderIds] => []
        )

)

[2025-06-06 21:04:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214480
            [orderIds] => []
        )

)

[2025-06-06 21:04:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214491
            [orderIds] => []
        )

)

[2025-06-06 21:05:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214502
            [orderIds] => []
        )

)

[2025-06-06 21:05:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214513
            [orderIds] => []
        )

)

[2025-06-06 21:05:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214524
            [orderIds] => []
        )

)

[2025-06-06 21:05:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214535
            [orderIds] => []
        )

)

[2025-06-06 21:05:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214546
            [orderIds] => []
        )

)

[2025-06-06 21:05:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214557
            [orderIds] => []
        )

)

[2025-06-06 21:06:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214568
            [orderIds] => []
        )

)

[2025-06-06 21:06:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214579
            [orderIds] => []
        )

)

[2025-06-06 21:06:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214590
            [orderIds] => []
        )

)

[2025-06-06 21:06:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214601
            [orderIds] => []
        )

)

[2025-06-06 21:06:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214612
            [orderIds] => []
        )

)

[2025-06-06 21:07:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214623
            [orderIds] => []
        )

)

[2025-06-06 21:07:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214634
            [orderIds] => []
        )

)

[2025-06-06 21:07:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214645
            [orderIds] => []
        )

)

[2025-06-06 21:07:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214656
            [orderIds] => []
        )

)

[2025-06-06 21:07:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214667
            [orderIds] => []
        )

)

[2025-06-06 21:07:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214678
            [orderIds] => []
        )

)

[2025-06-06 21:08:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214689
            [orderIds] => []
        )

)

[2025-06-06 21:08:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214700
            [orderIds] => []
        )

)

[2025-06-06 21:08:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214711
            [orderIds] => []
        )

)

[2025-06-06 21:08:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214722
            [orderIds] => []
        )

)

[2025-06-06 21:08:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214733
            [orderIds] => []
        )

)

[2025-06-06 21:09:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214744
            [orderIds] => []
        )

)

[2025-06-06 21:09:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214755
            [orderIds] => []
        )

)

[2025-06-06 21:09:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214766
            [orderIds] => []
        )

)

[2025-06-06 21:09:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214777
            [orderIds] => []
        )

)

[2025-06-06 21:09:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214788
            [orderIds] => []
        )

)

[2025-06-06 21:09:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214799
            [orderIds] => []
        )

)

[2025-06-06 21:10:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214810
            [orderIds] => []
        )

)

[2025-06-06 21:10:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214821
            [orderIds] => []
        )

)

[2025-06-06 21:10:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214832
            [orderIds] => []
        )

)

[2025-06-06 21:10:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214843
            [orderIds] => []
        )

)

[2025-06-06 21:10:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214854
            [orderIds] => []
        )

)

[2025-06-06 21:11:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214865
            [orderIds] => []
        )

)

[2025-06-06 21:11:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214876
            [orderIds] => []
        )

)

[2025-06-06 21:11:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214887
            [orderIds] => []
        )

)

[2025-06-06 21:11:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214898
            [orderIds] => []
        )

)

[2025-06-06 21:11:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214909
            [orderIds] => []
        )

)

[2025-06-06 21:12:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214920
            [orderIds] => []
        )

)

[2025-06-06 21:12:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214931
            [orderIds] => []
        )

)

[2025-06-06 21:12:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214942
            [orderIds] => []
        )

)

[2025-06-06 21:12:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214953
            [orderIds] => []
        )

)

[2025-06-06 21:12:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214964
            [orderIds] => []
        )

)

[2025-06-06 21:12:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214975
            [orderIds] => []
        )

)

[2025-06-06 21:13:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214986
            [orderIds] => []
        )

)

[2025-06-06 21:13:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749214997
            [orderIds] => []
        )

)

[2025-06-06 21:13:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215008
            [orderIds] => []
        )

)

[2025-06-06 21:13:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215019
            [orderIds] => []
        )

)

[2025-06-06 21:13:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215030
            [orderIds] => []
        )

)

[2025-06-06 21:14:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215041
            [orderIds] => []
        )

)

[2025-06-06 21:14:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215053
            [orderIds] => []
        )

)

[2025-06-06 21:14:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215063
            [orderIds] => []
        )

)

[2025-06-06 21:14:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 21:14:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 21:14:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 21:14:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 21:14:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215074
            [orderIds] => []
        )

)

[2025-06-06 21:14:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215084
            [orderIds] => []
        )

)

[2025-06-06 21:14:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215095
            [orderIds] => []
        )

)

[2025-06-06 21:15:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215106
            [orderIds] => []
        )

)

[2025-06-06 21:15:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215117
            [orderIds] => []
        )

)

[2025-06-06 21:15:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215128
            [orderIds] => []
        )

)

[2025-06-06 21:15:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215139
            [orderIds] => []
        )

)

[2025-06-06 21:15:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215150
            [orderIds] => []
        )

)

[2025-06-06 21:16:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215161
            [orderIds] => []
        )

)

[2025-06-06 21:16:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215172
            [orderIds] => []
        )

)

[2025-06-06 21:16:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215183
            [orderIds] => []
        )

)

[2025-06-06 21:16:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215194
            [orderIds] => []
        )

)

[2025-06-06 21:16:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215205
            [orderIds] => []
        )

)

[2025-06-06 21:16:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215216
            [orderIds] => []
        )

)

[2025-06-06 21:17:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215227
            [orderIds] => []
        )

)

[2025-06-06 21:17:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215238
            [orderIds] => []
        )

)

[2025-06-06 21:17:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215249
            [orderIds] => []
        )

)

[2025-06-06 21:17:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215260
            [orderIds] => []
        )

)

[2025-06-06 21:17:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215271
            [orderIds] => []
        )

)

[2025-06-06 21:18:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215282
            [orderIds] => []
        )

)

[2025-06-06 21:18:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215293
            [orderIds] => []
        )

)

[2025-06-06 21:18:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215304
            [orderIds] => []
        )

)

[2025-06-06 21:18:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215315
            [orderIds] => []
        )

)

[2025-06-06 21:18:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215326
            [orderIds] => []
        )

)

[2025-06-06 21:18:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215337
            [orderIds] => []
        )

)

[2025-06-06 21:19:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215348
            [orderIds] => []
        )

)

[2025-06-06 21:19:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215359
            [orderIds] => []
        )

)

[2025-06-06 21:19:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215370
            [orderIds] => []
        )

)

[2025-06-06 21:19:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215381
            [orderIds] => []
        )

)

[2025-06-06 21:19:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215392
            [orderIds] => []
        )

)

[2025-06-06 21:20:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215403
            [orderIds] => []
        )

)

[2025-06-06 21:20:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215414
            [orderIds] => []
        )

)

[2025-06-06 21:20:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215425
            [orderIds] => []
        )

)

[2025-06-06 21:20:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215436
            [orderIds] => []
        )

)

[2025-06-06 21:20:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215447
            [orderIds] => []
        )

)

[2025-06-06 21:20:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215458
            [orderIds] => []
        )

)

[2025-06-06 21:21:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215469
            [orderIds] => []
        )

)

[2025-06-06 21:21:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215480
            [orderIds] => []
        )

)

[2025-06-06 21:21:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215491
            [orderIds] => []
        )

)

[2025-06-06 21:21:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215502
            [orderIds] => []
        )

)

[2025-06-06 21:21:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215513
            [orderIds] => []
        )

)

[2025-06-06 21:22:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215524
            [orderIds] => []
        )

)

[2025-06-06 21:22:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215535
            [orderIds] => []
        )

)

[2025-06-06 21:22:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215546
            [orderIds] => []
        )

)

[2025-06-06 21:22:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215557
            [orderIds] => []
        )

)

[2025-06-06 21:22:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215568
            [orderIds] => []
        )

)

[2025-06-06 21:22:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215579
            [orderIds] => []
        )

)

[2025-06-06 21:23:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215590
            [orderIds] => []
        )

)

[2025-06-06 21:23:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215601
            [orderIds] => []
        )

)

[2025-06-06 21:23:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215612
            [orderIds] => []
        )

)

[2025-06-06 21:23:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215623
            [orderIds] => []
        )

)

[2025-06-06 21:23:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215634
            [orderIds] => []
        )

)

[2025-06-06 21:24:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215645
            [orderIds] => []
        )

)

[2025-06-06 21:24:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1749129854
            [orderIds] => []
        )

)

[2025-06-06 21:24:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1748611454
            [orderIds] => []
        )

)

[2025-06-06 21:24:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1748611454
            [orderIds] => []
        )

)

[2025-06-06 21:24:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-06 21:24:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-06 21:24:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215656
            [orderIds] => []
        )

)

[2025-06-06 21:24:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-06 21:24:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-06 21:24:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-06 21:24:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 21:24:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 21:24:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 21:24:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 21:24:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215667
            [orderIds] => []
        )

)

[2025-06-06 21:24:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215678
            [orderIds] => []
        )

)

[2025-06-06 21:24:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215689
            [orderIds] => []
        )

)

[2025-06-06 21:25:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215700
            [orderIds] => []
        )

)

[2025-06-06 21:25:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215711
            [orderIds] => []
        )

)

[2025-06-06 21:25:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215722
            [orderIds] => []
        )

)

[2025-06-06 21:25:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215733
            [orderIds] => []
        )

)

[2025-06-06 21:25:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215744
            [orderIds] => []
        )

)

[2025-06-06 21:25:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215755
            [orderIds] => []
        )

)

[2025-06-06 21:26:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215766
            [orderIds] => []
        )

)

[2025-06-06 21:26:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215777
            [orderIds] => []
        )

)

[2025-06-06 21:26:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215788
            [orderIds] => []
        )

)

[2025-06-06 21:26:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215799
            [orderIds] => []
        )

)

[2025-06-06 21:26:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215810
            [orderIds] => []
        )

)

[2025-06-06 21:27:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215821
            [orderIds] => []
        )

)

[2025-06-06 21:27:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215832
            [orderIds] => []
        )

)

[2025-06-06 21:27:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215843
            [orderIds] => []
        )

)

[2025-06-06 21:27:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215854
            [orderIds] => []
        )

)

[2025-06-06 21:27:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215865
            [orderIds] => []
        )

)

[2025-06-06 21:27:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215876
            [orderIds] => []
        )

)

[2025-06-06 21:28:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215887
            [orderIds] => []
        )

)

[2025-06-06 21:28:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215898
            [orderIds] => []
        )

)

[2025-06-06 21:28:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215909
            [orderIds] => []
        )

)

[2025-06-06 21:28:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215920
            [orderIds] => []
        )

)

[2025-06-06 21:28:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215931
            [orderIds] => []
        )

)

[2025-06-06 21:29:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215942
            [orderIds] => []
        )

)

[2025-06-06 21:29:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215953
            [orderIds] => []
        )

)

[2025-06-06 21:29:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215964
            [orderIds] => []
        )

)

[2025-06-06 21:29:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215975
            [orderIds] => []
        )

)

[2025-06-06 21:29:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215986
            [orderIds] => []
        )

)

[2025-06-06 21:29:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749215997
            [orderIds] => []
        )

)

[2025-06-06 21:30:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216008
            [orderIds] => []
        )

)

[2025-06-06 21:30:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216019
            [orderIds] => []
        )

)

[2025-06-06 21:30:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216030
            [orderIds] => []
        )

)

[2025-06-06 21:30:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216041
            [orderIds] => []
        )

)

[2025-06-06 21:30:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216052
            [orderIds] => []
        )

)

[2025-06-06 21:31:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216063
            [orderIds] => []
        )

)

[2025-06-06 21:31:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216074
            [orderIds] => []
        )

)

[2025-06-06 21:31:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216085
            [orderIds] => []
        )

)

[2025-06-06 21:31:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216096
            [orderIds] => []
        )

)

[2025-06-06 21:31:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216107
            [orderIds] => []
        )

)

[2025-06-06 21:31:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216118
            [orderIds] => []
        )

)

[2025-06-06 21:32:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216129
            [orderIds] => []
        )

)

[2025-06-06 21:32:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216140
            [orderIds] => []
        )

)

[2025-06-06 21:32:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216151
            [orderIds] => []
        )

)

[2025-06-06 21:32:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216162
            [orderIds] => []
        )

)

[2025-06-06 21:32:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216173
            [orderIds] => []
        )

)

[2025-06-06 21:33:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216184
            [orderIds] => []
        )

)

[2025-06-06 21:33:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216195
            [orderIds] => []
        )

)

[2025-06-06 21:33:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216206
            [orderIds] => []
        )

)

[2025-06-06 21:33:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216217
            [orderIds] => []
        )

)

[2025-06-06 21:33:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216228
            [orderIds] => []
        )

)

[2025-06-06 21:33:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216239
            [orderIds] => []
        )

)

[2025-06-06 21:34:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216250
            [orderIds] => []
        )

)

[2025-06-06 21:34:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216261
            [orderIds] => []
        )

)

[2025-06-06 21:34:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 21:34:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 21:34:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 21:34:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 21:34:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216272
            [orderIds] => []
        )

)

[2025-06-06 21:34:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216283
            [orderIds] => []
        )

)

[2025-06-06 21:34:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216294
            [orderIds] => []
        )

)

[2025-06-06 21:35:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216305
            [orderIds] => []
        )

)

[2025-06-06 21:35:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216316
            [orderIds] => []
        )

)

[2025-06-06 21:35:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216327
            [orderIds] => []
        )

)

[2025-06-06 21:35:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216338
            [orderIds] => []
        )

)

[2025-06-06 21:35:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216349
            [orderIds] => []
        )

)

[2025-06-06 21:36:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216360
            [orderIds] => []
        )

)

[2025-06-06 21:36:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216371
            [orderIds] => []
        )

)

[2025-06-06 21:36:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216382
            [orderIds] => []
        )

)

[2025-06-06 21:36:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216393
            [orderIds] => []
        )

)

[2025-06-06 21:36:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216404
            [orderIds] => []
        )

)

[2025-06-06 21:36:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216415
            [orderIds] => []
        )

)

[2025-06-06 21:37:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216426
            [orderIds] => []
        )

)

[2025-06-06 21:37:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216437
            [orderIds] => []
        )

)

[2025-06-06 21:37:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216448
            [orderIds] => []
        )

)

[2025-06-06 21:37:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216459
            [orderIds] => []
        )

)

[2025-06-06 21:37:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216470
            [orderIds] => []
        )

)

[2025-06-06 21:38:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216481
            [orderIds] => []
        )

)

[2025-06-06 21:38:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216492
            [orderIds] => []
        )

)

[2025-06-06 21:38:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216503
            [orderIds] => []
        )

)

[2025-06-06 21:38:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216514
            [orderIds] => []
        )

)

[2025-06-06 21:38:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216525
            [orderIds] => []
        )

)

[2025-06-06 21:38:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216536
            [orderIds] => []
        )

)

[2025-06-06 21:39:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216547
            [orderIds] => []
        )

)

[2025-06-06 21:39:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216558
            [orderIds] => []
        )

)

[2025-06-06 21:39:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216569
            [orderIds] => []
        )

)

[2025-06-06 21:39:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216580
            [orderIds] => []
        )

)

[2025-06-06 21:39:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216591
            [orderIds] => []
        )

)

[2025-06-06 21:40:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216602
            [orderIds] => []
        )

)

[2025-06-06 21:40:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216613
            [orderIds] => []
        )

)

[2025-06-06 21:40:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216624
            [orderIds] => []
        )

)

[2025-06-06 21:40:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216635
            [orderIds] => []
        )

)

[2025-06-06 21:40:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216646
            [orderIds] => []
        )

)

[2025-06-06 21:40:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216657
            [orderIds] => []
        )

)

[2025-06-06 21:41:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216668
            [orderIds] => []
        )

)

[2025-06-06 21:41:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216679
            [orderIds] => []
        )

)

[2025-06-06 21:41:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216690
            [orderIds] => []
        )

)

[2025-06-06 21:41:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216701
            [orderIds] => []
        )

)

[2025-06-06 21:41:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216712
            [orderIds] => []
        )

)

[2025-06-06 21:42:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216723
            [orderIds] => []
        )

)

[2025-06-06 21:42:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216734
            [orderIds] => []
        )

)

[2025-06-06 21:42:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216745
            [orderIds] => []
        )

)

[2025-06-06 21:42:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216756
            [orderIds] => []
        )

)

[2025-06-06 21:42:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216767
            [orderIds] => []
        )

)

[2025-06-06 21:42:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216778
            [orderIds] => []
        )

)

[2025-06-06 21:43:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216789
            [orderIds] => []
        )

)

[2025-06-06 21:43:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216800
            [orderIds] => []
        )

)

[2025-06-06 21:43:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216811
            [orderIds] => []
        )

)

[2025-06-06 21:43:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216822
            [orderIds] => []
        )

)

[2025-06-06 21:43:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216833
            [orderIds] => []
        )

)

[2025-06-06 21:44:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216844
            [orderIds] => []
        )

)

[2025-06-06 21:44:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216855
            [orderIds] => []
        )

)

[2025-06-06 21:44:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216866
            [orderIds] => []
        )

)

[2025-06-06 21:44:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 21:44:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 21:44:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 21:44:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 21:44:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216877
            [orderIds] => []
        )

)

[2025-06-06 21:44:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216888
            [orderIds] => []
        )

)

[2025-06-06 21:44:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216899
            [orderIds] => []
        )

)

[2025-06-06 21:45:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216910
            [orderIds] => []
        )

)

[2025-06-06 21:45:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216921
            [orderIds] => []
        )

)

[2025-06-06 21:45:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216932
            [orderIds] => []
        )

)

[2025-06-06 21:45:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216943
            [orderIds] => []
        )

)

[2025-06-06 21:45:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216954
            [orderIds] => []
        )

)

[2025-06-06 21:46:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216965
            [orderIds] => []
        )

)

[2025-06-06 21:46:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216976
            [orderIds] => []
        )

)

[2025-06-06 21:46:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216987
            [orderIds] => []
        )

)

[2025-06-06 21:46:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749216998
            [orderIds] => []
        )

)

[2025-06-06 21:46:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217009
            [orderIds] => []
        )

)

[2025-06-06 21:47:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217020
            [orderIds] => []
        )

)

[2025-06-06 21:47:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217031
            [orderIds] => []
        )

)

[2025-06-06 21:47:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217042
            [orderIds] => []
        )

)

[2025-06-06 21:47:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217053
            [orderIds] => []
        )

)

[2025-06-06 21:47:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217064
            [orderIds] => []
        )

)

[2025-06-06 21:47:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217075
            [orderIds] => []
        )

)

[2025-06-06 21:48:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217086
            [orderIds] => []
        )

)

[2025-06-06 21:48:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217097
            [orderIds] => []
        )

)

[2025-06-06 21:48:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217108
            [orderIds] => []
        )

)

[2025-06-06 21:48:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217119
            [orderIds] => []
        )

)

[2025-06-06 21:48:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217130
            [orderIds] => []
        )

)

[2025-06-06 21:49:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217141
            [orderIds] => []
        )

)

[2025-06-06 21:49:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217152
            [orderIds] => []
        )

)

[2025-06-06 21:49:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217163
            [orderIds] => []
        )

)

[2025-06-06 21:49:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217174
            [orderIds] => []
        )

)

[2025-06-06 21:49:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217185
            [orderIds] => []
        )

)

[2025-06-06 21:49:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217196
            [orderIds] => []
        )

)

[2025-06-06 21:50:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217207
            [orderIds] => []
        )

)

[2025-06-06 21:50:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217218
            [orderIds] => []
        )

)

[2025-06-06 21:50:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217229
            [orderIds] => []
        )

)

[2025-06-06 21:50:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217240
            [orderIds] => []
        )

)

[2025-06-06 21:50:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217251
            [orderIds] => []
        )

)

[2025-06-06 21:51:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217262
            [orderIds] => []
        )

)

[2025-06-06 21:51:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217273
            [orderIds] => []
        )

)

[2025-06-06 21:51:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217284
            [orderIds] => []
        )

)

[2025-06-06 21:51:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217295
            [orderIds] => []
        )

)

[2025-06-06 21:51:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217306
            [orderIds] => []
        )

)

[2025-06-06 21:51:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217317
            [orderIds] => []
        )

)

[2025-06-06 21:52:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217328
            [orderIds] => []
        )

)

[2025-06-06 21:52:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217339
            [orderIds] => []
        )

)

[2025-06-06 21:52:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217350
            [orderIds] => []
        )

)

[2025-06-06 21:52:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217361
            [orderIds] => []
        )

)

[2025-06-06 21:52:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217372
            [orderIds] => []
        )

)

[2025-06-06 21:53:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217383
            [orderIds] => []
        )

)

[2025-06-06 21:53:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217394
            [orderIds] => []
        )

)

[2025-06-06 21:53:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217405
            [orderIds] => []
        )

)

[2025-06-06 21:53:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217416
            [orderIds] => []
        )

)

[2025-06-06 21:53:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217427
            [orderIds] => []
        )

)

[2025-06-06 21:53:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217438
            [orderIds] => []
        )

)

[2025-06-06 21:54:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217449
            [orderIds] => []
        )

)

[2025-06-06 21:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1749131655
            [orderIds] => []
        )

)

[2025-06-06 21:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1748613255
            [orderIds] => []
        )

)

[2025-06-06 21:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1748613255
            [orderIds] => []
        )

)

[2025-06-06 21:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-06 21:54:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-06 21:54:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-06 21:54:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-06 21:54:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-06 21:54:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217460
            [orderIds] => []
        )

)

[2025-06-06 21:54:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 21:54:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 21:54:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 21:54:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 21:54:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217471
            [orderIds] => []
        )

)

[2025-06-06 21:54:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217482
            [orderIds] => []
        )

)

[2025-06-06 21:54:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217493
            [orderIds] => []
        )

)

[2025-06-06 21:55:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217504
            [orderIds] => []
        )

)

[2025-06-06 21:55:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217515
            [orderIds] => []
        )

)

[2025-06-06 21:55:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217526
            [orderIds] => []
        )

)

[2025-06-06 21:55:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217537
            [orderIds] => []
        )

)

[2025-06-06 21:55:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217548
            [orderIds] => []
        )

)

[2025-06-06 21:55:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217559
            [orderIds] => []
        )

)

[2025-06-06 21:56:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217570
            [orderIds] => []
        )

)

[2025-06-06 21:56:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217581
            [orderIds] => []
        )

)

[2025-06-06 21:56:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217592
            [orderIds] => []
        )

)

[2025-06-06 21:56:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217603
            [orderIds] => []
        )

)

[2025-06-06 21:56:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217614
            [orderIds] => []
        )

)

[2025-06-06 21:57:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217625
            [orderIds] => []
        )

)

[2025-06-06 21:57:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217636
            [orderIds] => []
        )

)

[2025-06-06 21:57:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217647
            [orderIds] => []
        )

)

[2025-06-06 21:57:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217658
            [orderIds] => []
        )

)

[2025-06-06 21:57:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217669
            [orderIds] => []
        )

)

[2025-06-06 21:58:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217680
            [orderIds] => []
        )

)

[2025-06-06 21:58:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217691
            [orderIds] => []
        )

)

[2025-06-06 21:58:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217702
            [orderIds] => []
        )

)

[2025-06-06 21:58:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217713
            [orderIds] => []
        )

)

[2025-06-06 21:58:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217724
            [orderIds] => []
        )

)

[2025-06-06 21:58:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217735
            [orderIds] => []
        )

)

[2025-06-06 21:59:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217746
            [orderIds] => []
        )

)

[2025-06-06 21:59:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217757
            [orderIds] => []
        )

)

[2025-06-06 21:59:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217768
            [orderIds] => []
        )

)

[2025-06-06 21:59:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217779
            [orderIds] => []
        )

)

[2025-06-06 21:59:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217790
            [orderIds] => []
        )

)

[2025-06-06 22:00:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217801
            [orderIds] => []
        )

)

[2025-06-06 22:00:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217812
            [orderIds] => []
        )

)

[2025-06-06 22:00:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217823
            [orderIds] => []
        )

)

[2025-06-06 22:00:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217834
            [orderIds] => []
        )

)

[2025-06-06 22:00:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217845
            [orderIds] => []
        )

)

[2025-06-06 22:00:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217856
            [orderIds] => []
        )

)

[2025-06-06 22:01:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217867
            [orderIds] => []
        )

)

[2025-06-06 22:01:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217878
            [orderIds] => []
        )

)

[2025-06-06 22:01:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217889
            [orderIds] => []
        )

)

[2025-06-06 22:01:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217900
            [orderIds] => []
        )

)

[2025-06-06 22:01:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217911
            [orderIds] => []
        )

)

[2025-06-06 22:02:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217922
            [orderIds] => []
        )

)

[2025-06-06 22:02:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217933
            [orderIds] => []
        )

)

[2025-06-06 22:02:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217944
            [orderIds] => []
        )

)

[2025-06-06 22:02:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217955
            [orderIds] => []
        )

)

[2025-06-06 22:02:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217966
            [orderIds] => []
        )

)

[2025-06-06 22:02:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217977
            [orderIds] => []
        )

)

[2025-06-06 22:03:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217988
            [orderIds] => []
        )

)

[2025-06-06 22:03:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749217999
            [orderIds] => []
        )

)

[2025-06-06 22:03:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218010
            [orderIds] => []
        )

)

[2025-06-06 22:03:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218021
            [orderIds] => []
        )

)

[2025-06-06 22:03:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218032
            [orderIds] => []
        )

)

[2025-06-06 22:04:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218043
            [orderIds] => []
        )

)

[2025-06-06 22:04:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218054
            [orderIds] => []
        )

)

[2025-06-06 22:04:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218065
            [orderIds] => []
        )

)

[2025-06-06 22:04:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 22:04:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 22:04:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 22:04:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 22:04:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218076
            [orderIds] => []
        )

)

[2025-06-06 22:04:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218087
            [orderIds] => []
        )

)

[2025-06-06 22:04:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218098
            [orderIds] => []
        )

)

[2025-06-06 22:05:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218109
            [orderIds] => []
        )

)

[2025-06-06 22:05:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218120
            [orderIds] => []
        )

)

[2025-06-06 22:05:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218131
            [orderIds] => []
        )

)

[2025-06-06 22:05:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218142
            [orderIds] => []
        )

)

[2025-06-06 22:05:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218153
            [orderIds] => []
        )

)

[2025-06-06 22:06:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218164
            [orderIds] => []
        )

)

[2025-06-06 22:06:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218175
            [orderIds] => []
        )

)

[2025-06-06 22:06:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218186
            [orderIds] => []
        )

)

[2025-06-06 22:06:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218197
            [orderIds] => []
        )

)

[2025-06-06 22:06:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218208
            [orderIds] => []
        )

)

[2025-06-06 22:06:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218219
            [orderIds] => []
        )

)

[2025-06-06 22:07:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218230
            [orderIds] => []
        )

)

[2025-06-06 22:07:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218241
            [orderIds] => []
        )

)

[2025-06-06 22:07:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218252
            [orderIds] => []
        )

)

[2025-06-06 22:07:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218263
            [orderIds] => []
        )

)

[2025-06-06 22:07:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218274
            [orderIds] => []
        )

)

[2025-06-06 22:08:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218285
            [orderIds] => []
        )

)

[2025-06-06 22:08:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218296
            [orderIds] => []
        )

)

[2025-06-06 22:08:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218307
            [orderIds] => []
        )

)

[2025-06-06 22:08:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218318
            [orderIds] => []
        )

)

[2025-06-06 22:08:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218329
            [orderIds] => []
        )

)

[2025-06-06 22:09:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218340
            [orderIds] => []
        )

)

[2025-06-06 22:09:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218351
            [orderIds] => []
        )

)

[2025-06-06 22:09:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218362
            [orderIds] => []
        )

)

[2025-06-06 22:09:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218373
            [orderIds] => []
        )

)

[2025-06-06 22:09:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218384
            [orderIds] => []
        )

)

[2025-06-06 22:09:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218395
            [orderIds] => []
        )

)

[2025-06-06 22:10:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218406
            [orderIds] => []
        )

)

[2025-06-06 22:10:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218417
            [orderIds] => []
        )

)

[2025-06-06 22:10:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218428
            [orderIds] => []
        )

)

[2025-06-06 22:10:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218439
            [orderIds] => []
        )

)

[2025-06-06 22:10:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218450
            [orderIds] => []
        )

)

[2025-06-06 22:11:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218461
            [orderIds] => []
        )

)

[2025-06-06 22:11:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218472
            [orderIds] => []
        )

)

[2025-06-06 22:11:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218483
            [orderIds] => []
        )

)

[2025-06-06 22:11:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218494
            [orderIds] => []
        )

)

[2025-06-06 22:11:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218505
            [orderIds] => []
        )

)

[2025-06-06 22:11:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218516
            [orderIds] => []
        )

)

[2025-06-06 22:12:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218527
            [orderIds] => []
        )

)

[2025-06-06 22:12:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218538
            [orderIds] => []
        )

)

[2025-06-06 22:12:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218549
            [orderIds] => []
        )

)

[2025-06-06 22:12:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218560
            [orderIds] => []
        )

)

[2025-06-06 22:12:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218571
            [orderIds] => []
        )

)

[2025-06-06 22:13:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218582
            [orderIds] => []
        )

)

[2025-06-06 22:13:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218593
            [orderIds] => []
        )

)

[2025-06-06 22:13:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218604
            [orderIds] => []
        )

)

[2025-06-06 22:13:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218615
            [orderIds] => []
        )

)

[2025-06-06 22:13:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218626
            [orderIds] => []
        )

)

[2025-06-06 22:13:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218637
            [orderIds] => []
        )

)

[2025-06-06 22:14:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218648
            [orderIds] => []
        )

)

[2025-06-06 22:14:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218659
            [orderIds] => []
        )

)

[2025-06-06 22:14:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 22:14:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 22:14:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 22:14:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 22:14:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218670
            [orderIds] => []
        )

)

[2025-06-06 22:14:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218681
            [orderIds] => []
        )

)

[2025-06-06 22:14:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218692
            [orderIds] => []
        )

)

[2025-06-06 22:15:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218703
            [orderIds] => []
        )

)

[2025-06-06 22:15:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218714
            [orderIds] => []
        )

)

[2025-06-06 22:15:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218725
            [orderIds] => []
        )

)

[2025-06-06 22:15:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218736
            [orderIds] => []
        )

)

[2025-06-06 22:15:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218747
            [orderIds] => []
        )

)

[2025-06-06 22:15:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218758
            [orderIds] => []
        )

)

[2025-06-06 22:16:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218769
            [orderIds] => []
        )

)

[2025-06-06 22:16:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218780
            [orderIds] => []
        )

)

[2025-06-06 22:16:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218791
            [orderIds] => []
        )

)

[2025-06-06 22:16:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218802
            [orderIds] => []
        )

)

[2025-06-06 22:16:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218813
            [orderIds] => []
        )

)

[2025-06-06 22:17:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218824
            [orderIds] => []
        )

)

[2025-06-06 22:17:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218835
            [orderIds] => []
        )

)

[2025-06-06 22:17:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218846
            [orderIds] => []
        )

)

[2025-06-06 22:17:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218857
            [orderIds] => []
        )

)

[2025-06-06 22:17:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218868
            [orderIds] => []
        )

)

[2025-06-06 22:17:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218879
            [orderIds] => []
        )

)

[2025-06-06 22:18:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218890
            [orderIds] => []
        )

)

[2025-06-06 22:18:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218901
            [orderIds] => []
        )

)

[2025-06-06 22:18:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218912
            [orderIds] => []
        )

)

[2025-06-06 22:18:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218923
            [orderIds] => []
        )

)

[2025-06-06 22:18:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218934
            [orderIds] => []
        )

)

[2025-06-06 22:19:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218945
            [orderIds] => []
        )

)

[2025-06-06 22:19:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218956
            [orderIds] => []
        )

)

[2025-06-06 22:19:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218967
            [orderIds] => []
        )

)

[2025-06-06 22:19:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218978
            [orderIds] => []
        )

)

[2025-06-06 22:19:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749218989
            [orderIds] => []
        )

)

[2025-06-06 22:20:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219000
            [orderIds] => []
        )

)

[2025-06-06 22:20:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219011
            [orderIds] => []
        )

)

[2025-06-06 22:20:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219022
            [orderIds] => []
        )

)

[2025-06-06 22:20:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219033
            [orderIds] => []
        )

)

[2025-06-06 22:20:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219044
            [orderIds] => []
        )

)

[2025-06-06 22:20:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219055
            [orderIds] => []
        )

)

[2025-06-06 22:21:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219066
            [orderIds] => []
        )

)

[2025-06-06 22:21:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219077
            [orderIds] => []
        )

)

[2025-06-06 22:21:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219088
            [orderIds] => []
        )

)

[2025-06-06 22:21:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219099
            [orderIds] => []
        )

)

[2025-06-06 22:21:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219110
            [orderIds] => []
        )

)

[2025-06-06 22:22:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219121
            [orderIds] => []
        )

)

[2025-06-06 22:22:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219132
            [orderIds] => []
        )

)

[2025-06-06 22:22:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219143
            [orderIds] => []
        )

)

[2025-06-06 22:22:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219154
            [orderIds] => []
        )

)

[2025-06-06 22:22:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219165
            [orderIds] => []
        )

)

[2025-06-06 22:22:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219176
            [orderIds] => []
        )

)

[2025-06-06 22:23:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219187
            [orderIds] => []
        )

)

[2025-06-06 22:23:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219198
            [orderIds] => []
        )

)

[2025-06-06 22:23:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219209
            [orderIds] => []
        )

)

[2025-06-06 22:23:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219220
            [orderIds] => []
        )

)

[2025-06-06 22:23:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219231
            [orderIds] => []
        )

)

[2025-06-06 22:24:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219242
            [orderIds] => []
        )

)

[2025-06-06 22:24:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219253
            [orderIds] => []
        )

)

[2025-06-06 22:24:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1749133456
            [orderIds] => []
        )

)

[2025-06-06 22:24:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1748615056
            [orderIds] => []
        )

)

[2025-06-06 22:24:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1748615056
            [orderIds] => []
        )

)

[2025-06-06 22:24:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-06 22:24:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-06 22:24:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-06 22:24:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-06 22:24:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-06 22:24:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219264
            [orderIds] => []
        )

)

[2025-06-06 22:24:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 22:24:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 22:24:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 22:24:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 22:24:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219275
            [orderIds] => []
        )

)

[2025-06-06 22:24:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219286
            [orderIds] => []
        )

)

[2025-06-06 22:24:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219297
            [orderIds] => []
        )

)

[2025-06-06 22:25:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219308
            [orderIds] => []
        )

)

[2025-06-06 22:25:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219319
            [orderIds] => []
        )

)

[2025-06-06 22:25:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219330
            [orderIds] => []
        )

)

[2025-06-06 22:25:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219341
            [orderIds] => []
        )

)

[2025-06-06 22:25:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219352
            [orderIds] => []
        )

)

[2025-06-06 22:26:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219363
            [orderIds] => []
        )

)

[2025-06-06 22:26:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219374
            [orderIds] => []
        )

)

[2025-06-06 22:26:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219385
            [orderIds] => []
        )

)

[2025-06-06 22:26:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219396
            [orderIds] => []
        )

)

[2025-06-06 22:26:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219407
            [orderIds] => []
        )

)

[2025-06-06 22:26:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219418
            [orderIds] => []
        )

)

[2025-06-06 22:27:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219429
            [orderIds] => []
        )

)

[2025-06-06 22:27:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219440
            [orderIds] => []
        )

)

[2025-06-06 22:27:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219451
            [orderIds] => []
        )

)

[2025-06-06 22:27:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219462
            [orderIds] => []
        )

)

[2025-06-06 22:27:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219473
            [orderIds] => []
        )

)

[2025-06-06 22:28:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219484
            [orderIds] => []
        )

)

[2025-06-06 22:28:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219495
            [orderIds] => []
        )

)

[2025-06-06 22:28:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219506
            [orderIds] => []
        )

)

[2025-06-06 22:28:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219517
            [orderIds] => []
        )

)

[2025-06-06 22:28:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219528
            [orderIds] => []
        )

)

[2025-06-06 22:28:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219539
            [orderIds] => []
        )

)

[2025-06-06 22:29:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219550
            [orderIds] => []
        )

)

[2025-06-06 22:29:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219561
            [orderIds] => []
        )

)

[2025-06-06 22:29:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219572
            [orderIds] => []
        )

)

[2025-06-06 22:29:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219583
            [orderIds] => []
        )

)

[2025-06-06 22:29:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219594
            [orderIds] => []
        )

)

[2025-06-06 22:30:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219605
            [orderIds] => []
        )

)

[2025-06-06 22:30:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219616
            [orderIds] => []
        )

)

[2025-06-06 22:30:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219627
            [orderIds] => []
        )

)

[2025-06-06 22:30:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219638
            [orderIds] => []
        )

)

[2025-06-06 22:30:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219649
            [orderIds] => []
        )

)

[2025-06-06 22:31:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219660
            [orderIds] => []
        )

)

[2025-06-06 22:31:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219671
            [orderIds] => []
        )

)

[2025-06-06 22:31:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219682
            [orderIds] => []
        )

)

[2025-06-06 22:31:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219693
            [orderIds] => []
        )

)

[2025-06-06 22:31:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219704
            [orderIds] => []
        )

)

[2025-06-06 22:31:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219715
            [orderIds] => []
        )

)

[2025-06-06 22:32:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219726
            [orderIds] => []
        )

)

[2025-06-06 22:32:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219737
            [orderIds] => []
        )

)

[2025-06-06 22:32:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219748
            [orderIds] => []
        )

)

[2025-06-06 22:32:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219759
            [orderIds] => []
        )

)

[2025-06-06 22:32:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219770
            [orderIds] => []
        )

)

[2025-06-06 22:33:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219781
            [orderIds] => []
        )

)

[2025-06-06 22:33:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219792
            [orderIds] => []
        )

)

[2025-06-06 22:33:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219803
            [orderIds] => []
        )

)

[2025-06-06 22:33:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219814
            [orderIds] => []
        )

)

[2025-06-06 22:33:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219825
            [orderIds] => []
        )

)

[2025-06-06 22:33:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219836
            [orderIds] => []
        )

)

[2025-06-06 22:34:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219847
            [orderIds] => []
        )

)

[2025-06-06 22:34:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219858
            [orderIds] => []
        )

)

[2025-06-06 22:34:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219869
            [orderIds] => []
        )

)

[2025-06-06 22:34:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 22:34:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 22:34:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 22:34:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 22:34:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219880
            [orderIds] => []
        )

)

[2025-06-06 22:34:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219891
            [orderIds] => []
        )

)

[2025-06-06 22:35:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219902
            [orderIds] => []
        )

)

[2025-06-06 22:35:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219913
            [orderIds] => []
        )

)

[2025-06-06 22:35:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219924
            [orderIds] => []
        )

)

[2025-06-06 22:35:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219935
            [orderIds] => []
        )

)

[2025-06-06 22:35:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219946
            [orderIds] => []
        )

)

[2025-06-06 22:35:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219957
            [orderIds] => []
        )

)

[2025-06-06 22:36:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219968
            [orderIds] => []
        )

)

[2025-06-06 22:36:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219979
            [orderIds] => []
        )

)

[2025-06-06 22:36:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749219990
            [orderIds] => []
        )

)

[2025-06-06 22:36:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220001
            [orderIds] => []
        )

)

[2025-06-06 22:36:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220012
            [orderIds] => []
        )

)

[2025-06-06 22:37:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220023
            [orderIds] => []
        )

)

[2025-06-06 22:37:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220034
            [orderIds] => []
        )

)

[2025-06-06 22:37:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220045
            [orderIds] => []
        )

)

[2025-06-06 22:37:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220056
            [orderIds] => []
        )

)

[2025-06-06 22:37:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220067
            [orderIds] => []
        )

)

[2025-06-06 22:37:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220078
            [orderIds] => []
        )

)

[2025-06-06 22:38:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220089
            [orderIds] => []
        )

)

[2025-06-06 22:38:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220100
            [orderIds] => []
        )

)

[2025-06-06 22:38:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220111
            [orderIds] => []
        )

)

[2025-06-06 22:38:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220122
            [orderIds] => []
        )

)

[2025-06-06 22:38:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220133
            [orderIds] => []
        )

)

[2025-06-06 22:39:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220144
            [orderIds] => []
        )

)

[2025-06-06 22:39:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220155
            [orderIds] => []
        )

)

[2025-06-06 22:39:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220166
            [orderIds] => []
        )

)

[2025-06-06 22:39:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220177
            [orderIds] => []
        )

)

[2025-06-06 22:39:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220188
            [orderIds] => []
        )

)

[2025-06-06 22:39:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220199
            [orderIds] => []
        )

)

[2025-06-06 22:40:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220210
            [orderIds] => []
        )

)

[2025-06-06 22:40:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220221
            [orderIds] => []
        )

)

[2025-06-06 22:40:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220232
            [orderIds] => []
        )

)

[2025-06-06 22:40:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220243
            [orderIds] => []
        )

)

[2025-06-06 22:40:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220254
            [orderIds] => []
        )

)

[2025-06-06 22:41:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220265
            [orderIds] => []
        )

)

[2025-06-06 22:41:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220276
            [orderIds] => []
        )

)

[2025-06-06 22:41:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220287
            [orderIds] => []
        )

)

[2025-06-06 22:41:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220298
            [orderIds] => []
        )

)

[2025-06-06 22:41:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220309
            [orderIds] => []
        )

)

[2025-06-06 22:42:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220320
            [orderIds] => []
        )

)

[2025-06-06 22:42:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220331
            [orderIds] => []
        )

)

[2025-06-06 22:42:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220342
            [orderIds] => []
        )

)

[2025-06-06 22:42:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220353
            [orderIds] => []
        )

)

[2025-06-06 22:42:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220364
            [orderIds] => []
        )

)

[2025-06-06 22:42:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220375
            [orderIds] => []
        )

)

[2025-06-06 22:43:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220386
            [orderIds] => []
        )

)

[2025-06-06 22:43:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220397
            [orderIds] => []
        )

)

[2025-06-06 22:43:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220408
            [orderIds] => []
        )

)

[2025-06-06 22:43:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220419
            [orderIds] => []
        )

)

[2025-06-06 22:43:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220430
            [orderIds] => []
        )

)

[2025-06-06 22:44:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220441
            [orderIds] => []
        )

)

[2025-06-06 22:44:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220452
            [orderIds] => []
        )

)

[2025-06-06 22:44:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220463
            [orderIds] => []
        )

)

[2025-06-06 22:44:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 22:44:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 22:44:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 22:44:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 22:44:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220474
            [orderIds] => []
        )

)

[2025-06-06 22:44:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220485
            [orderIds] => []
        )

)

[2025-06-06 22:44:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220496
            [orderIds] => []
        )

)

[2025-06-06 22:45:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220507
            [orderIds] => []
        )

)

[2025-06-06 22:45:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220518
            [orderIds] => []
        )

)

[2025-06-06 22:45:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220529
            [orderIds] => []
        )

)

[2025-06-06 22:45:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220540
            [orderIds] => []
        )

)

[2025-06-06 22:45:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220551
            [orderIds] => []
        )

)

[2025-06-06 22:46:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220562
            [orderIds] => []
        )

)

[2025-06-06 22:46:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220573
            [orderIds] => []
        )

)

[2025-06-06 22:46:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220584
            [orderIds] => []
        )

)

[2025-06-06 22:46:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220595
            [orderIds] => []
        )

)

[2025-06-06 22:46:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220606
            [orderIds] => []
        )

)

[2025-06-06 22:46:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220617
            [orderIds] => []
        )

)

[2025-06-06 22:47:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220628
            [orderIds] => []
        )

)

[2025-06-06 22:47:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220639
            [orderIds] => []
        )

)

[2025-06-06 22:47:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220650
            [orderIds] => []
        )

)

[2025-06-06 22:47:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220661
            [orderIds] => []
        )

)

[2025-06-06 22:47:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220672
            [orderIds] => []
        )

)

[2025-06-06 22:48:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220683
            [orderIds] => []
        )

)

[2025-06-06 22:48:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220694
            [orderIds] => []
        )

)

[2025-06-06 22:48:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220705
            [orderIds] => []
        )

)

[2025-06-06 22:48:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220716
            [orderIds] => []
        )

)

[2025-06-06 22:48:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220727
            [orderIds] => []
        )

)

[2025-06-06 22:48:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220738
            [orderIds] => []
        )

)

[2025-06-06 22:49:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220749
            [orderIds] => []
        )

)

[2025-06-06 22:49:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220760
            [orderIds] => []
        )

)

[2025-06-06 22:49:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220771
            [orderIds] => []
        )

)

[2025-06-06 22:49:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220782
            [orderIds] => []
        )

)

[2025-06-06 22:49:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220793
            [orderIds] => []
        )

)

[2025-06-06 22:50:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220804
            [orderIds] => []
        )

)

[2025-06-06 22:50:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220815
            [orderIds] => []
        )

)

[2025-06-06 22:50:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220826
            [orderIds] => []
        )

)

[2025-06-06 22:50:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220837
            [orderIds] => []
        )

)

[2025-06-06 22:50:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220848
            [orderIds] => []
        )

)

[2025-06-06 22:50:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220859
            [orderIds] => []
        )

)

[2025-06-06 22:51:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220870
            [orderIds] => []
        )

)

[2025-06-06 22:51:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220881
            [orderIds] => []
        )

)

[2025-06-06 22:51:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220892
            [orderIds] => []
        )

)

[2025-06-06 22:51:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220903
            [orderIds] => []
        )

)

[2025-06-06 22:51:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220914
            [orderIds] => []
        )

)

[2025-06-06 22:52:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220925
            [orderIds] => []
        )

)

[2025-06-06 22:52:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220936
            [orderIds] => []
        )

)

[2025-06-06 22:52:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220947
            [orderIds] => []
        )

)

[2025-06-06 22:52:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220958
            [orderIds] => []
        )

)

[2025-06-06 22:52:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220969
            [orderIds] => []
        )

)

[2025-06-06 22:53:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220980
            [orderIds] => []
        )

)

[2025-06-06 22:53:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749220991
            [orderIds] => []
        )

)

[2025-06-06 22:53:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221002
            [orderIds] => []
        )

)

[2025-06-06 22:53:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221013
            [orderIds] => []
        )

)

[2025-06-06 22:53:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221024
            [orderIds] => []
        )

)

[2025-06-06 22:53:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221035
            [orderIds] => []
        )

)

[2025-06-06 22:54:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221046
            [orderIds] => []
        )

)

[2025-06-06 22:54:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1749135257
            [orderIds] => []
        )

)

[2025-06-06 22:54:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1748616857
            [orderIds] => []
        )

)

[2025-06-06 22:54:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1748616857
            [orderIds] => []
        )

)

[2025-06-06 22:54:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-06 22:54:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221057
            [orderIds] => []
        )

)

[2025-06-06 22:54:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-06 22:54:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-06 22:54:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-06 22:54:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-06 22:54:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221068
            [orderIds] => []
        )

)

[2025-06-06 22:54:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 22:54:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 22:54:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 22:54:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 22:54:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221079
            [orderIds] => []
        )

)

[2025-06-06 22:54:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221090
            [orderIds] => []
        )

)

[2025-06-06 22:55:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221101
            [orderIds] => []
        )

)

[2025-06-06 22:55:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221112
            [orderIds] => []
        )

)

[2025-06-06 22:55:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221123
            [orderIds] => []
        )

)

[2025-06-06 22:55:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221134
            [orderIds] => []
        )

)

[2025-06-06 22:55:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221145
            [orderIds] => []
        )

)

[2025-06-06 22:55:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221156
            [orderIds] => []
        )

)

[2025-06-06 22:56:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221167
            [orderIds] => []
        )

)

[2025-06-06 22:56:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221178
            [orderIds] => []
        )

)

[2025-06-06 22:56:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221189
            [orderIds] => []
        )

)

[2025-06-06 22:56:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221200
            [orderIds] => []
        )

)

[2025-06-06 22:56:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221211
            [orderIds] => []
        )

)

[2025-06-06 22:57:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221222
            [orderIds] => []
        )

)

[2025-06-06 22:57:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221233
            [orderIds] => []
        )

)

[2025-06-06 22:57:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221244
            [orderIds] => []
        )

)

[2025-06-06 22:57:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221255
            [orderIds] => []
        )

)

[2025-06-06 22:57:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221266
            [orderIds] => []
        )

)

[2025-06-06 22:57:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221277
            [orderIds] => []
        )

)

[2025-06-06 22:58:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221288
            [orderIds] => []
        )

)

[2025-06-06 22:58:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221299
            [orderIds] => []
        )

)

[2025-06-06 22:58:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221310
            [orderIds] => []
        )

)

[2025-06-06 22:58:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221321
            [orderIds] => []
        )

)

[2025-06-06 22:58:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221332
            [orderIds] => []
        )

)

[2025-06-06 22:59:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221343
            [orderIds] => []
        )

)

[2025-06-06 22:59:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221354
            [orderIds] => []
        )

)

[2025-06-06 22:59:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221365
            [orderIds] => []
        )

)

[2025-06-06 22:59:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221376
            [orderIds] => []
        )

)

[2025-06-06 22:59:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221387
            [orderIds] => []
        )

)

[2025-06-06 22:59:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221398
            [orderIds] => []
        )

)

[2025-06-06 23:00:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221409
            [orderIds] => []
        )

)

[2025-06-06 23:00:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221420
            [orderIds] => []
        )

)

[2025-06-06 23:00:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221431
            [orderIds] => []
        )

)

[2025-06-06 23:00:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221442
            [orderIds] => []
        )

)

[2025-06-06 23:00:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221453
            [orderIds] => []
        )

)

[2025-06-06 23:01:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221464
            [orderIds] => []
        )

)

[2025-06-06 23:01:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221475
            [orderIds] => []
        )

)

[2025-06-06 23:01:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221486
            [orderIds] => []
        )

)

[2025-06-06 23:01:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221497
            [orderIds] => []
        )

)

[2025-06-06 23:01:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221508
            [orderIds] => []
        )

)

[2025-06-06 23:01:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221519
            [orderIds] => []
        )

)

[2025-06-06 23:02:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221530
            [orderIds] => []
        )

)

[2025-06-06 23:02:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221541
            [orderIds] => []
        )

)

[2025-06-06 23:02:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221552
            [orderIds] => []
        )

)

[2025-06-06 23:02:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221563
            [orderIds] => []
        )

)

[2025-06-06 23:02:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221574
            [orderIds] => []
        )

)

[2025-06-06 23:03:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221585
            [orderIds] => []
        )

)

[2025-06-06 23:03:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221596
            [orderIds] => []
        )

)

[2025-06-06 23:03:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221607
            [orderIds] => []
        )

)

[2025-06-06 23:03:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221618
            [orderIds] => []
        )

)

[2025-06-06 23:03:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221629
            [orderIds] => []
        )

)

[2025-06-06 23:04:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221640
            [orderIds] => []
        )

)

[2025-06-06 23:04:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221651
            [orderIds] => []
        )

)

[2025-06-06 23:04:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221662
            [orderIds] => []
        )

)

[2025-06-06 23:04:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221673
            [orderIds] => []
        )

)

[2025-06-06 23:04:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 23:04:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 23:04:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 23:04:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 23:04:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221684
            [orderIds] => []
        )

)

[2025-06-06 23:04:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221695
            [orderIds] => []
        )

)

[2025-06-06 23:05:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221706
            [orderIds] => []
        )

)

[2025-06-06 23:05:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221717
            [orderIds] => []
        )

)

[2025-06-06 23:05:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221728
            [orderIds] => []
        )

)

[2025-06-06 23:05:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221739
            [orderIds] => []
        )

)

[2025-06-06 23:05:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221750
            [orderIds] => []
        )

)

[2025-06-06 23:06:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221761
            [orderIds] => []
        )

)

[2025-06-06 23:06:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221772
            [orderIds] => []
        )

)

[2025-06-06 23:06:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221783
            [orderIds] => []
        )

)

[2025-06-06 23:06:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221794
            [orderIds] => []
        )

)

[2025-06-06 23:06:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221805
            [orderIds] => []
        )

)

[2025-06-06 23:06:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221816
            [orderIds] => []
        )

)

[2025-06-06 23:07:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221827
            [orderIds] => []
        )

)

[2025-06-06 23:07:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221838
            [orderIds] => []
        )

)

[2025-06-06 23:07:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221849
            [orderIds] => []
        )

)

[2025-06-06 23:07:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221860
            [orderIds] => []
        )

)

[2025-06-06 23:07:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221871
            [orderIds] => []
        )

)

[2025-06-06 23:08:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221882
            [orderIds] => []
        )

)

[2025-06-06 23:08:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221893
            [orderIds] => []
        )

)

[2025-06-06 23:08:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221904
            [orderIds] => []
        )

)

[2025-06-06 23:08:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221915
            [orderIds] => []
        )

)

[2025-06-06 23:08:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221926
            [orderIds] => []
        )

)

[2025-06-06 23:08:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221937
            [orderIds] => []
        )

)

[2025-06-06 23:09:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221948
            [orderIds] => []
        )

)

[2025-06-06 23:09:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221959
            [orderIds] => []
        )

)

[2025-06-06 23:09:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221970
            [orderIds] => []
        )

)

[2025-06-06 23:09:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221981
            [orderIds] => []
        )

)

[2025-06-06 23:09:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749221992
            [orderIds] => []
        )

)

[2025-06-06 23:10:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222003
            [orderIds] => []
        )

)

[2025-06-06 23:10:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222014
            [orderIds] => []
        )

)

[2025-06-06 23:10:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222025
            [orderIds] => []
        )

)

[2025-06-06 23:10:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222036
            [orderIds] => []
        )

)

[2025-06-06 23:10:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222047
            [orderIds] => []
        )

)

[2025-06-06 23:10:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222058
            [orderIds] => []
        )

)

[2025-06-06 23:11:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222069
            [orderIds] => []
        )

)

[2025-06-06 23:11:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222080
            [orderIds] => []
        )

)

[2025-06-06 23:11:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222091
            [orderIds] => []
        )

)

[2025-06-06 23:11:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222102
            [orderIds] => []
        )

)

[2025-06-06 23:11:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222113
            [orderIds] => []
        )

)

[2025-06-06 23:12:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222124
            [orderIds] => []
        )

)

[2025-06-06 23:12:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222135
            [orderIds] => []
        )

)

[2025-06-06 23:12:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222146
            [orderIds] => []
        )

)

[2025-06-06 23:12:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222157
            [orderIds] => []
        )

)

[2025-06-06 23:12:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222168
            [orderIds] => []
        )

)

[2025-06-06 23:12:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222179
            [orderIds] => []
        )

)

[2025-06-06 23:13:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222190
            [orderIds] => []
        )

)

[2025-06-06 23:13:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222201
            [orderIds] => []
        )

)

[2025-06-06 23:13:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222212
            [orderIds] => []
        )

)

[2025-06-06 23:13:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222223
            [orderIds] => []
        )

)

[2025-06-06 23:13:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222234
            [orderIds] => []
        )

)

[2025-06-06 23:14:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222245
            [orderIds] => []
        )

)

[2025-06-06 23:14:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222256
            [orderIds] => []
        )

)

[2025-06-06 23:14:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222267
            [orderIds] => []
        )

)

[2025-06-06 23:14:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 23:14:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 23:14:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 23:14:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 23:14:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222278
            [orderIds] => []
        )

)

[2025-06-06 23:14:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222289
            [orderIds] => []
        )

)

[2025-06-06 23:15:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222300
            [orderIds] => []
        )

)

[2025-06-06 23:15:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222311
            [orderIds] => []
        )

)

[2025-06-06 23:15:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222322
            [orderIds] => []
        )

)

[2025-06-06 23:15:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222333
            [orderIds] => []
        )

)

[2025-06-06 23:15:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222344
            [orderIds] => []
        )

)

[2025-06-06 23:15:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222355
            [orderIds] => []
        )

)

[2025-06-06 23:16:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222366
            [orderIds] => []
        )

)

[2025-06-06 23:16:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222377
            [orderIds] => []
        )

)

[2025-06-06 23:16:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222388
            [orderIds] => []
        )

)

[2025-06-06 23:16:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222399
            [orderIds] => []
        )

)

[2025-06-06 23:16:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222410
            [orderIds] => []
        )

)

[2025-06-06 23:17:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222421
            [orderIds] => []
        )

)

[2025-06-06 23:17:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222432
            [orderIds] => []
        )

)

[2025-06-06 23:17:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222443
            [orderIds] => []
        )

)

[2025-06-06 23:17:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222454
            [orderIds] => []
        )

)

[2025-06-06 23:17:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222465
            [orderIds] => []
        )

)

[2025-06-06 23:17:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222476
            [orderIds] => []
        )

)

[2025-06-06 23:18:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222487
            [orderIds] => []
        )

)

[2025-06-06 23:18:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222498
            [orderIds] => []
        )

)

[2025-06-06 23:18:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222509
            [orderIds] => []
        )

)

[2025-06-06 23:18:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222520
            [orderIds] => []
        )

)

[2025-06-06 23:18:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222531
            [orderIds] => []
        )

)

[2025-06-06 23:19:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222542
            [orderIds] => []
        )

)

[2025-06-06 23:19:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222553
            [orderIds] => []
        )

)

[2025-06-06 23:19:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222564
            [orderIds] => []
        )

)

[2025-06-06 23:19:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222575
            [orderIds] => []
        )

)

[2025-06-06 23:19:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222586
            [orderIds] => []
        )

)

[2025-06-06 23:19:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222597
            [orderIds] => []
        )

)

[2025-06-06 23:20:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222608
            [orderIds] => []
        )

)

[2025-06-06 23:20:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222619
            [orderIds] => []
        )

)

[2025-06-06 23:20:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222630
            [orderIds] => []
        )

)

[2025-06-06 23:20:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222641
            [orderIds] => []
        )

)

[2025-06-06 23:20:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222652
            [orderIds] => []
        )

)

[2025-06-06 23:21:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222663
            [orderIds] => []
        )

)

[2025-06-06 23:21:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222674
            [orderIds] => []
        )

)

[2025-06-06 23:21:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222685
            [orderIds] => []
        )

)

[2025-06-06 23:21:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222696
            [orderIds] => []
        )

)

[2025-06-06 23:21:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222707
            [orderIds] => []
        )

)

[2025-06-06 23:21:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222718
            [orderIds] => []
        )

)

[2025-06-06 23:22:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222729
            [orderIds] => []
        )

)

[2025-06-06 23:22:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222740
            [orderIds] => []
        )

)

[2025-06-06 23:22:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222751
            [orderIds] => []
        )

)

[2025-06-06 23:22:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222762
            [orderIds] => []
        )

)

[2025-06-06 23:22:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222773
            [orderIds] => []
        )

)

[2025-06-06 23:23:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222784
            [orderIds] => []
        )

)

[2025-06-06 23:23:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222795
            [orderIds] => []
        )

)

[2025-06-06 23:23:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222806
            [orderIds] => []
        )

)

[2025-06-06 23:23:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222817
            [orderIds] => []
        )

)

[2025-06-06 23:23:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222828
            [orderIds] => []
        )

)

[2025-06-06 23:23:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222839
            [orderIds] => []
        )

)

[2025-06-06 23:24:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222850
            [orderIds] => []
        )

)

[2025-06-06 23:24:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1749137058
            [orderIds] => []
        )

)

[2025-06-06 23:24:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1748618658
            [orderIds] => []
        )

)

[2025-06-06 23:24:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1748618658
            [orderIds] => []
        )

)

[2025-06-06 23:24:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-06 23:24:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-06 23:24:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-06 23:24:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-06 23:24:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222861
            [orderIds] => []
        )

)

[2025-06-06 23:24:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-06 23:24:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222872
            [orderIds] => []
        )

)

[2025-06-06 23:24:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 23:24:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 23:24:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 23:24:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 23:24:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222883
            [orderIds] => []
        )

)

[2025-06-06 23:24:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222894
            [orderIds] => []
        )

)

[2025-06-06 23:25:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222905
            [orderIds] => []
        )

)

[2025-06-06 23:25:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222916
            [orderIds] => []
        )

)

[2025-06-06 23:25:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222927
            [orderIds] => []
        )

)

[2025-06-06 23:25:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222938
            [orderIds] => []
        )

)

[2025-06-06 23:25:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222949
            [orderIds] => []
        )

)

[2025-06-06 23:26:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222960
            [orderIds] => []
        )

)

[2025-06-06 23:26:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222971
            [orderIds] => []
        )

)

[2025-06-06 23:26:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222982
            [orderIds] => []
        )

)

[2025-06-06 23:26:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749222993
            [orderIds] => []
        )

)

[2025-06-06 23:26:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223004
            [orderIds] => []
        )

)

[2025-06-06 23:26:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223015
            [orderIds] => []
        )

)

[2025-06-06 23:27:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223026
            [orderIds] => []
        )

)

[2025-06-06 23:27:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223037
            [orderIds] => []
        )

)

[2025-06-06 23:27:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223048
            [orderIds] => []
        )

)

[2025-06-06 23:27:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223059
            [orderIds] => []
        )

)

[2025-06-06 23:27:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223070
            [orderIds] => []
        )

)

[2025-06-06 23:28:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223081
            [orderIds] => []
        )

)

[2025-06-06 23:28:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223092
            [orderIds] => []
        )

)

[2025-06-06 23:28:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223103
            [orderIds] => []
        )

)

[2025-06-06 23:28:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223114
            [orderIds] => []
        )

)

[2025-06-06 23:28:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223125
            [orderIds] => []
        )

)

[2025-06-06 23:28:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223136
            [orderIds] => []
        )

)

[2025-06-06 23:29:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223147
            [orderIds] => []
        )

)

[2025-06-06 23:29:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223158
            [orderIds] => []
        )

)

[2025-06-06 23:29:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223169
            [orderIds] => []
        )

)

[2025-06-06 23:29:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223180
            [orderIds] => []
        )

)

[2025-06-06 23:29:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223191
            [orderIds] => []
        )

)

[2025-06-06 23:30:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223202
            [orderIds] => []
        )

)

[2025-06-06 23:30:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223213
            [orderIds] => []
        )

)

[2025-06-06 23:30:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223224
            [orderIds] => []
        )

)

[2025-06-06 23:30:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223235
            [orderIds] => []
        )

)

[2025-06-06 23:30:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223246
            [orderIds] => []
        )

)

[2025-06-06 23:30:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223257
            [orderIds] => []
        )

)

[2025-06-06 23:31:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223268
            [orderIds] => []
        )

)

[2025-06-06 23:31:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223279
            [orderIds] => []
        )

)

[2025-06-06 23:31:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223290
            [orderIds] => []
        )

)

[2025-06-06 23:31:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223301
            [orderIds] => []
        )

)

[2025-06-06 23:31:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223312
            [orderIds] => []
        )

)

[2025-06-06 23:32:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223323
            [orderIds] => []
        )

)

[2025-06-06 23:32:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223334
            [orderIds] => []
        )

)

[2025-06-06 23:32:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223345
            [orderIds] => []
        )

)

[2025-06-06 23:32:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223356
            [orderIds] => []
        )

)

[2025-06-06 23:32:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223367
            [orderIds] => []
        )

)

[2025-06-06 23:32:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223378
            [orderIds] => []
        )

)

[2025-06-06 23:33:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223389
            [orderIds] => []
        )

)

[2025-06-06 23:33:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223400
            [orderIds] => []
        )

)

[2025-06-06 23:33:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223411
            [orderIds] => []
        )

)

[2025-06-06 23:33:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223422
            [orderIds] => []
        )

)

[2025-06-06 23:33:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223433
            [orderIds] => []
        )

)

[2025-06-06 23:34:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223444
            [orderIds] => []
        )

)

[2025-06-06 23:34:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223455
            [orderIds] => []
        )

)

[2025-06-06 23:34:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223466
            [orderIds] => []
        )

)

[2025-06-06 23:34:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223477
            [orderIds] => []
        )

)

[2025-06-06 23:34:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 23:34:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 23:34:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 23:34:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 23:34:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223488
            [orderIds] => []
        )

)

[2025-06-06 23:34:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223499
            [orderIds] => []
        )

)

[2025-06-06 23:35:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223510
            [orderIds] => []
        )

)

[2025-06-06 23:35:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223521
            [orderIds] => []
        )

)

[2025-06-06 23:35:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223532
            [orderIds] => []
        )

)

[2025-06-06 23:35:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223543
            [orderIds] => []
        )

)

[2025-06-06 23:35:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223554
            [orderIds] => []
        )

)

[2025-06-06 23:36:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223565
            [orderIds] => []
        )

)

[2025-06-06 23:36:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223576
            [orderIds] => []
        )

)

[2025-06-06 23:36:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223587
            [orderIds] => []
        )

)

[2025-06-06 23:36:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223598
            [orderIds] => []
        )

)

[2025-06-06 23:36:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223609
            [orderIds] => []
        )

)

[2025-06-06 23:37:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223620
            [orderIds] => []
        )

)

[2025-06-06 23:37:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223631
            [orderIds] => []
        )

)

[2025-06-06 23:37:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223642
            [orderIds] => []
        )

)

[2025-06-06 23:37:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223653
            [orderIds] => []
        )

)

[2025-06-06 23:37:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223664
            [orderIds] => []
        )

)

[2025-06-06 23:37:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223675
            [orderIds] => []
        )

)

[2025-06-06 23:38:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223686
            [orderIds] => []
        )

)

[2025-06-06 23:38:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223697
            [orderIds] => []
        )

)

[2025-06-06 23:38:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223708
            [orderIds] => []
        )

)

[2025-06-06 23:38:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223719
            [orderIds] => []
        )

)

[2025-06-06 23:38:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223730
            [orderIds] => []
        )

)

[2025-06-06 23:39:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223741
            [orderIds] => []
        )

)

[2025-06-06 23:39:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223752
            [orderIds] => []
        )

)

[2025-06-06 23:39:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223763
            [orderIds] => []
        )

)

[2025-06-06 23:39:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223774
            [orderIds] => []
        )

)

[2025-06-06 23:39:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223785
            [orderIds] => []
        )

)

[2025-06-06 23:39:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223796
            [orderIds] => []
        )

)

[2025-06-06 23:40:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223807
            [orderIds] => []
        )

)

[2025-06-06 23:40:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223818
            [orderIds] => []
        )

)

[2025-06-06 23:40:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223829
            [orderIds] => []
        )

)

[2025-06-06 23:40:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223840
            [orderIds] => []
        )

)

[2025-06-06 23:40:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223851
            [orderIds] => []
        )

)

[2025-06-06 23:41:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223862
            [orderIds] => []
        )

)

[2025-06-06 23:41:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223873
            [orderIds] => []
        )

)

[2025-06-06 23:41:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223884
            [orderIds] => []
        )

)

[2025-06-06 23:41:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223895
            [orderIds] => []
        )

)

[2025-06-06 23:41:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223906
            [orderIds] => []
        )

)

[2025-06-06 23:41:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223917
            [orderIds] => []
        )

)

[2025-06-06 23:42:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223928
            [orderIds] => []
        )

)

[2025-06-06 23:42:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223939
            [orderIds] => []
        )

)

[2025-06-06 23:42:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223950
            [orderIds] => []
        )

)

[2025-06-06 23:42:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223961
            [orderIds] => []
        )

)

[2025-06-06 23:42:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223972
            [orderIds] => []
        )

)

[2025-06-06 23:43:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223983
            [orderIds] => []
        )

)

[2025-06-06 23:43:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749223994
            [orderIds] => []
        )

)

[2025-06-06 23:43:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224005
            [orderIds] => []
        )

)

[2025-06-06 23:43:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224016
            [orderIds] => []
        )

)

[2025-06-06 23:43:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224027
            [orderIds] => []
        )

)

[2025-06-06 23:43:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224038
            [orderIds] => []
        )

)

[2025-06-06 23:44:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224049
            [orderIds] => []
        )

)

[2025-06-06 23:44:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224060
            [orderIds] => []
        )

)

[2025-06-06 23:44:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224071
            [orderIds] => []
        )

)

[2025-06-06 23:44:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 23:44:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 23:44:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 23:44:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 23:44:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224082
            [orderIds] => []
        )

)

[2025-06-06 23:44:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224093
            [orderIds] => []
        )

)

[2025-06-06 23:45:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224104
            [orderIds] => []
        )

)

[2025-06-06 23:45:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224115
            [orderIds] => []
        )

)

[2025-06-06 23:45:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224126
            [orderIds] => []
        )

)

[2025-06-06 23:45:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224137
            [orderIds] => []
        )

)

[2025-06-06 23:45:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224148
            [orderIds] => []
        )

)

[2025-06-06 23:45:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224159
            [orderIds] => []
        )

)

[2025-06-06 23:46:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224170
            [orderIds] => []
        )

)

[2025-06-06 23:46:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224181
            [orderIds] => []
        )

)

[2025-06-06 23:46:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224192
            [orderIds] => []
        )

)

[2025-06-06 23:46:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224203
            [orderIds] => []
        )

)

[2025-06-06 23:46:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224214
            [orderIds] => []
        )

)

[2025-06-06 23:47:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224225
            [orderIds] => []
        )

)

[2025-06-06 23:47:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224236
            [orderIds] => []
        )

)

[2025-06-06 23:47:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224247
            [orderIds] => []
        )

)

[2025-06-06 23:47:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224258
            [orderIds] => []
        )

)

[2025-06-06 23:47:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224269
            [orderIds] => []
        )

)

[2025-06-06 23:48:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224280
            [orderIds] => []
        )

)

[2025-06-06 23:48:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224291
            [orderIds] => []
        )

)

[2025-06-06 23:48:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224302
            [orderIds] => []
        )

)

[2025-06-06 23:48:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224313
            [orderIds] => []
        )

)

[2025-06-06 23:48:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224324
            [orderIds] => []
        )

)

[2025-06-06 23:48:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224335
            [orderIds] => []
        )

)

[2025-06-06 23:49:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224346
            [orderIds] => []
        )

)

[2025-06-06 23:49:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224357
            [orderIds] => []
        )

)

[2025-06-06 23:49:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224368
            [orderIds] => []
        )

)

[2025-06-06 23:49:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224379
            [orderIds] => []
        )

)

[2025-06-06 23:49:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224390
            [orderIds] => []
        )

)

[2025-06-06 23:50:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224401
            [orderIds] => []
        )

)

[2025-06-06 23:50:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224412
            [orderIds] => []
        )

)

[2025-06-06 23:50:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224423
            [orderIds] => []
        )

)

[2025-06-06 23:50:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224434
            [orderIds] => []
        )

)

[2025-06-06 23:50:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224445
            [orderIds] => []
        )

)

[2025-06-06 23:50:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224456
            [orderIds] => []
        )

)

[2025-06-06 23:51:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224467
            [orderIds] => []
        )

)

[2025-06-06 23:51:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224478
            [orderIds] => []
        )

)

[2025-06-06 23:51:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224489
            [orderIds] => []
        )

)

[2025-06-06 23:51:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224500
            [orderIds] => []
        )

)

[2025-06-06 23:51:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224511
            [orderIds] => []
        )

)

[2025-06-06 23:52:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224522
            [orderIds] => []
        )

)

[2025-06-06 23:52:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224533
            [orderIds] => []
        )

)

[2025-06-06 23:52:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224544
            [orderIds] => []
        )

)

[2025-06-06 23:52:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224555
            [orderIds] => []
        )

)

[2025-06-06 23:52:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224566
            [orderIds] => []
        )

)

[2025-06-06 23:52:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224577
            [orderIds] => []
        )

)

[2025-06-06 23:53:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224588
            [orderIds] => []
        )

)

[2025-06-06 23:53:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224599
            [orderIds] => []
        )

)

[2025-06-06 23:53:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224610
            [orderIds] => []
        )

)

[2025-06-06 23:53:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224621
            [orderIds] => []
        )

)

[2025-06-06 23:53:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224632
            [orderIds] => []
        )

)

[2025-06-06 23:54:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224643
            [orderIds] => []
        )

)

[2025-06-06 23:54:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224654
            [orderIds] => []
        )

)

[2025-06-06 23:54:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1749138859
            [orderIds] => []
        )

)

[2025-06-06 23:54:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1748620459
            [orderIds] => []
        )

)

[2025-06-06 23:54:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1748620459
            [orderIds] => []
        )

)

[2025-06-06 23:54:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-06 23:54:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-06 23:54:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-06 23:54:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-06 23:54:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-06 23:54:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224665
            [orderIds] => []
        )

)

[2025-06-06 23:54:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224676
            [orderIds] => []
        )

)

[2025-06-06 23:54:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-06 23:54:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 23:54:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-06 23:54:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-06 23:54:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224687
            [orderIds] => []
        )

)

[2025-06-06 23:54:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224698
            [orderIds] => []
        )

)

[2025-06-06 23:55:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224709
            [orderIds] => []
        )

)

[2025-06-06 23:55:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224720
            [orderIds] => []
        )

)

[2025-06-06 23:55:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224731
            [orderIds] => []
        )

)

[2025-06-06 23:55:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224742
            [orderIds] => []
        )

)

[2025-06-06 23:55:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224753
            [orderIds] => []
        )

)

[2025-06-06 23:56:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224764
            [orderIds] => []
        )

)

[2025-06-06 23:56:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224775
            [orderIds] => []
        )

)

[2025-06-06 23:56:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224786
            [orderIds] => []
        )

)

[2025-06-06 23:56:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224797
            [orderIds] => []
        )

)

[2025-06-06 23:56:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224808
            [orderIds] => []
        )

)

[2025-06-06 23:56:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224819
            [orderIds] => []
        )

)

[2025-06-06 23:57:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224830
            [orderIds] => []
        )

)

[2025-06-06 23:57:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224841
            [orderIds] => []
        )

)

[2025-06-06 23:57:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224852
            [orderIds] => []
        )

)

[2025-06-06 23:57:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224863
            [orderIds] => []
        )

)

[2025-06-06 23:57:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224874
            [orderIds] => []
        )

)

[2025-06-06 23:58:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224885
            [orderIds] => []
        )

)

[2025-06-06 23:58:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224896
            [orderIds] => []
        )

)

[2025-06-06 23:58:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224907
            [orderIds] => []
        )

)

[2025-06-06 23:58:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224918
            [orderIds] => []
        )

)

[2025-06-06 23:58:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224929
            [orderIds] => []
        )

)

[2025-06-06 23:59:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224940
            [orderIds] => []
        )

)

[2025-06-06 23:59:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224951
            [orderIds] => []
        )

)

[2025-06-06 23:59:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224962
            [orderIds] => []
        )

)

[2025-06-06 23:59:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224973
            [orderIds] => []
        )

)

[2025-06-06 23:59:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224984
            [orderIds] => []
        )

)

[2025-06-06 23:59:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1749224995
            [orderIds] => []
        )

)

