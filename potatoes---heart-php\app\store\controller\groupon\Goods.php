<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，拼单商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\groupon;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\groupon\Goods as GoodsModel;
use cores\exception\BaseException;

/**
 * 拼团商品
 * Class Goods
 * @package app\store\controller\groupon
 */
class Goods extends Controller
{
    /**
     * 获取列表记录
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function list(): Json
    {
        $model = new GoodsModel;
        $list = $model->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 秒杀商品详情
     * @param int $grouponGoodsId 拼团商品ID
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(int $grouponGoodsId): Json
    {
        // 获取商品详情
        $model = new GoodsModel;
        $detail = $model->getDetail($grouponGoodsId);
        return $this->renderSuccess(compact('detail'));
    }

    /**
     * 添加商品
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function add(): Json
    {
        // 新增记录
        $model = new GoodsModel;
        if ($model->add($this->postForm())) {
            return $this->renderSuccess('添加成功，请在编辑页设置拼团价格');
        }
        return $this->renderError($model->getError() ?: '添加失败');
    }

    /**
     * 编辑商品
     * @param int $grouponGoodsId 拼团商品ID
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function edit(int $grouponGoodsId): Json
    {
        // 拼单详情
        $model = GoodsModel::detail($grouponGoodsId);
        // 更新记录
        if ($model->edit($this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }

    /**
     * 删除商品
     * @param int $grouponGoodsId 拼团商品ID
     * @return Json
     */
    public function delete(int $grouponGoodsId): Json
    {
        // 拼单详情
        $model = GoodsModel::detail($grouponGoodsId);
        if (!$model->setDelete()) {
            return $this->renderError($model->getError() ?: '删除失败');
        }
        return $this->renderSuccess('删除成功');
    }
}
