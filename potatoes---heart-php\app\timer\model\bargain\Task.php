<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\timer\model\bargain;

use app\common\model\bargain\Task as TaskModel;

/**
 * 砍价任务模型
 * Class Task
 * @package app\api\model\bargain
 */
class Task extends TaskModel
{
    /**
     * 获取已过期但未结束的砍价任务
     * @param int $storeId
     * @return array
     */
    public function getEndTaskIds(int $storeId): array
    {
        return $this->where('end_time', '<=', time())
            ->where('status', '=', 1)
            ->where('is_delete', '=', 0)
            ->where('store_id', '=', $storeId)
            ->column('task_id');
    }

    /**
     * 将砍价任务标记为已结束(批量)
     * @param array $taskIds 砍价任务ID集
     * @return bool
     */
    public function setIsEnd(array $taskIds): bool
    {
        return static::updateBase(['status' => 0], [['task_id', 'in', $taskIds]]);
    }
}