<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\admin\model;

use app\common\model\Page as PageModel;

/**
 * 页面模型
 * Class Page
 * @package app\admin\model
 */
class Page extends PageModel
{
    /**
     * 新增小程序首页diy默认设置
     * @param int $storeId
     * @return bool|false
     */
    public function insertDefault(int $storeId): bool
    {
        $items = $this->getDefaultItems();
        return $this->save([
            'page_type' => 10,
            'page_name' => '商城首页',
            'page_data' => [
                'page' => $this->getDefaultPage(),
                'items' => [$items['search'], $items['banner']]
            ],
            'store_id' => $storeId
        ]);
    }
}
