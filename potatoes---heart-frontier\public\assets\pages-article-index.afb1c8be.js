import{_ as t}from"./u-tabs.ea263673.js";import{$ as a,q as e,u as s,o as i,c as l,w as r,n as o,i as c,a as n,b as u,d,F as m,y as h,k as p,f as _,t as f,e as g,l as b,g as L}from"./index-4ddb689d.js";import{r as y}from"./uni-app.es.24af5d4f.js";import{M as w,_ as T}from"./mescroll-mixins.a7931123.js";import{W as x}from"./wxofficial.956096a5.js";import{l as v}from"./index.d37cf054.js";import{_ as C}from"./_plugin-vue_export-helper.1b428a4d.js";const S="article.category/list";const k=C({mixins:[w,x],data:()=>({tabList:[],curTab:0,articleList:e(),upOption:{auto:!0,page:{size:15},noMoreSize:3}}),onLoad(t){this.getCategoryList(t.categoryId),this.setWxofficialShareData()},methods:{upCallback(t){const a=this;a.getArticleList(t.num).then((t=>{const e=t.data.length,s=t.data.total;a.mescroll.endBySize(e,s)})).catch((()=>a.mescroll.endErr()))},getCategoryList(t){a.get(S).then((a=>{this.setTabList(a.data.list,t)}))},setTabList(t,a){const e=this;if(e.tabList=[{value:0,name:"全部"}],t.forEach((t=>{e.tabList.push({value:t.category_id,name:t.name})})),a>0){const t=e.tabList.findIndex((t=>t.value==a));e.curTab=t>-1?t:0}},getArticleList(t=1){const a=this;return new Promise(((e,i)=>{v({categoryId:a.getTabValue(),page:t},{load:!1}).then((i=>{const l=i.data.list;a.articleList.data=s(l,a.articleList,t),e(l)})).catch(i)}))},onChangeTab(t){this.curTab=t,this.onRefreshList()},getTabValue(){const t=this;return t.tabList.length?t.tabList[t.curTab].value:0},onRefreshList(){this.articleList=e(),setTimeout((()=>this.mescroll.resetUpScroll()),120)},onTargetDetail(t){this.$navTo("pages/article/detail",{articleId:t})},setWxofficialShareData(){this.updateShareCardData({title:"文章首页"})}},onShareAppMessage(){return{title:"文章首页",path:"/pages/article/index?"+this.$getShareUrlParams()}},onShareTimeline(){return{title:"文章首页",path:"/pages/article/index?"+this.$getShareUrlParams()}}},[["render",function(a,e,s,w,x,v){const C=y(h("u-tabs"),t),S=b,k=c,I=L,j=y(h("mescroll-body"),T);return i(),l(k,{class:"container",style:o(a.appThemeStyle)},{default:r((()=>[n(j,{ref:"mescrollRef",sticky:!0,onInit:a.mescrollInit,down:{use:!1},up:x.upOption,onUp:v.upCallback},{default:r((()=>[n(C,{list:x.tabList,"is-scroll":!0,current:x.curTab,"active-color":a.appTheme.mainBg,duration:.2,onChange:v.onChangeTab},null,8,["list","current","active-color","duration","onChange"]),n(k,{class:"article-list"},{default:r((()=>[(i(!0),u(m,null,d(x.articleList.data,((t,a)=>(i(),l(k,{class:p(["article-item",[`show-type__${t.show_type}`]]),key:a,onClick:a=>v.onTargetDetail(t.article_id)},{default:r((()=>[10==t.show_type?(i(),u(m,{key:0},[n(k,{class:"article-item__left flex-box"},{default:r((()=>[n(k,{class:"article-item__title"},{default:r((()=>[n(S,{class:"twoline-hide"},{default:r((()=>[_(f(t.title),1)])),_:2},1024)])),_:2},1024),n(k,{class:"article-item__footer m-top10"},{default:r((()=>[n(S,{class:"article-views f-24 col-8"},{default:r((()=>[_(f(t.show_views)+"次浏览",1)])),_:2},1024)])),_:2},1024)])),_:2},1024),n(k,{class:"article-item__image"},{default:r((()=>[n(I,{class:"image",mode:"widthFix",src:t.image_url},null,8,["src"])])),_:2},1024)],64)):g("",!0),20==t.show_type?(i(),u(m,{key:1},[n(k,{class:"article-item__title"},{default:r((()=>[n(S,{class:"twoline-hide"},{default:r((()=>[_(f(t.title),1)])),_:2},1024)])),_:2},1024),n(k,{class:"article-item__image m-top20"},{default:r((()=>[n(I,{class:"image",mode:"widthFix",src:t.image_url},null,8,["src"])])),_:2},1024),n(k,{class:"article-item__footer m-top10"},{default:r((()=>[n(S,{class:"article-views f-24 col-8"},{default:r((()=>[_(f(t.show_views)+"次浏览",1)])),_:2},1024)])),_:2},1024)],64)):g("",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1},8,["onInit","up","onUp"])])),_:1},8,["style"])}],["__scopeId","data-v-8e17acb4"]]);export{k as default};
