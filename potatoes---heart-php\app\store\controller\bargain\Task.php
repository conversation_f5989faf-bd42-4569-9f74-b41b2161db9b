<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\bargain;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\bargain\Task as TaskModel;
use app\store\model\bargain\TaskHelp as TaskHelpModel;

/**
 * 砍价任务管理
 * Class Task
 * @package app\store\controller\apps\bargain
 */
class Task extends Controller
{
    /**
     * 砍价任务列表
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(): Json
    {
        $model = new TaskModel;
        $list = $model->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 砍价榜
     * @param int $taskId
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function help(int $taskId): Json
    {
        $model = new TaskHelpModel;
        $list = $model->getList($taskId);
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 删除砍价任务
     * @param int $taskId
     * @return Json
     */
    public function delete(int $taskId): Json
    {
        // 砍价活动详情
        $model = TaskModel::detail($taskId);
        if (!$model->setDelete()) {
            return $this->renderError($model->getError() ?: '删除失败');
        }
        return $this->renderSuccess('删除成功');
    }
}