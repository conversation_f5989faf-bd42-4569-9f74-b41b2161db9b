import{$ as e,v as t,o as a,c as l,w as s,a as o,f as u,d as n,e as c,F as d,b as r,t as i,n as p,l as m,i as _,j as f,h,k}from"./index-Bo6hY7ZC.js";import{_ as y}from"./u-tag.B8U4SC5W.js";import{r as g}from"./uni-app.es.DtaL5fPi.js";import{_ as w}from"./u-popup.qw0t8K2V.js";import{_ as L}from"./u-modal.6LKX92cH.js";import{r as x}from"./myCoupon.WsqtsnX5.js";import{C}from"./CouponType.urBvWZTt.js";import{_ as b}from"./_plugin-vue_export-helper.BCo6x5W8.js";const v="market/detail";const T=b({props:{goodsId:{type:Number,default:null},goodsSource:{type:Number,default:null}},data:()=>({CouponTypeEnum:C,isLoading:!0,showPopup:!1,descModal:{show:!1,title:"",content:""},marketList:[],couponList:[]}),created(){this.getMarketDetail()},methods:{getMarketDetail(){const t=this;var a;t.isLoading=!0,(a={goodsId:t.goodsId||0,source:t.goodsSource},e.get(v,a)).then((e=>{t.marketList=e.data.marketList,t.couponList=e.data.couponList})).finally((()=>t.isLoading=!1))},handlePopup(){this.showPopup=!this.showPopup},handleReceive(e){const t=this;x(e).then((e=>{t.$success(e.message),t.getMarketDetail()}))},handleDescribe(e){this.couponList[e].expand=!this.couponList[e].expand},handleContent(e){const t=this.marketList[e];t.describe&&(this.descModal.show=!0,this.descModal.title=t.tagName,this.descModal.content=t.describe)}}},[["render",function(e,x,C,b,v,T){const M=m,j=_,D=g(t("u-tag"),y),N=f,I=g(t("u-popup"),w),P=g(t("u-modal"),L);return a(),l(j,{class:"market-wrapper",style:p(e.appThemeStyle)},{default:s((()=>[v.couponList.length||v.marketList.length?(a(),l(j,{key:0,class:"market-info"},{default:s((()=>[v.couponList.length?(a(),l(j,{key:0,class:"draw-item center",onClick:x[0]||(x[0]=e=>T.handlePopup())},{default:s((()=>[o(j,{class:"draw-left"},{default:s((()=>[o(M,null,{default:s((()=>[u("领券：")])),_:1})])),_:1}),o(j,{class:"draw-content"},{default:s((()=>[o(j,{class:"coupon-list"},{default:s((()=>[(a(!0),n(d,null,c(v.couponList,((e,t)=>(a(),l(j,{class:"coupon-item",key:t},{default:s((()=>[o(j,{class:"tag-wrapper"},{default:s((()=>[o(M,{class:"tag-text"},{default:s((()=>[u(i(e.name),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1}),o(j,{class:"draw-right"},{default:s((()=>[o(M,{class:"draw-arrow iconfont icon-arrow-right"})])),_:1})])),_:1})):r("",!0),v.marketList.length?(a(),l(j,{key:1,class:"draw-item",onClick:x[1]||(x[1]=e=>T.handlePopup())},{default:s((()=>[o(j,{class:"draw-left"},{default:s((()=>[o(M,null,{default:s((()=>[u("促销：")])),_:1})])),_:1}),o(j,{class:"draw-content"},{default:s((()=>[o(j,{class:"market-list"},{default:s((()=>[(a(!0),n(d,null,c(v.marketList,((t,c)=>(a(),n(d,{key:c},[t.isFirst?(a(),l(j,{key:0,class:"market-item"},{default:s((()=>[o(D,{color:e.appTheme.mainBg,"border-color":e.appTheme.mainBg,text:t.tagName,type:"error",size:"mini",mode:"plain"},null,8,["color","border-color","text"]),o(j,{class:"market-content"},{default:s((()=>[o(M,null,{default:s((()=>[u(i(t.title),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)):r("",!0)],64)))),128))])),_:1})])),_:1}),o(j,{class:"draw-right"},{default:s((()=>[o(M,{class:"draw-arrow iconfont icon-arrow-right"})])),_:1})])),_:1})):r("",!0)])),_:1})):r("",!0),o(I,{modelValue:v.showPopup,"onUpdate:modelValue":x[2]||(x[2]=e=>v.showPopup=e),mode:"bottom",closeable:!0,"border-radius":26},{default:s((()=>[o(j,{class:"popup-content"},{default:s((()=>[o(j,{class:"title"},{default:s((()=>[u("优惠详情")])),_:1}),o(N,{class:"content-scroll","scroll-y":!0},{default:s((()=>[v.marketList.length?(a(),l(j,{key:0,class:"market-title"},{default:s((()=>[o(M,null,{default:s((()=>[u("促销")])),_:1})])),_:1})):r("",!0),v.marketList.length?(a(),l(j,{key:1,class:"market-list"},{default:s((()=>[(a(!0),n(d,null,c(v.marketList,((t,n)=>(a(),l(j,{class:"market-item",key:n,onClick:h((e=>T.handleContent(n)),["stop"])},{default:s((()=>[o(j,{class:"item-left"},{default:s((()=>[o(D,{color:e.appTheme.mainBg,"border-color":e.appTheme.mainBg,text:t.tagName,type:"error",size:"mini",mode:"plain"},null,8,["color","border-color","text"])])),_:2},1024),o(j,{class:"item-content"},{default:s((()=>[o(M,null,{default:s((()=>[u(i(t.title),1)])),_:2},1024)])),_:2},1024),t.describe?(a(),l(j,{key:0,class:"item-right"},{default:s((()=>[o(M,{class:"draw-arrow iconfont icon-arrow-right"})])),_:1})):r("",!0)])),_:2},1032,["onClick"])))),128))])),_:1})):r("",!0),v.couponList.length?(a(),l(j,{key:2,class:"market-title m-top30"},{default:s((()=>[o(M,null,{default:s((()=>[u("领券")])),_:1})])),_:1})):r("",!0),v.couponList.length?(a(),l(j,{key:3,class:"coupon-list"},{default:s((()=>[(a(!0),n(d,null,c(v.couponList,((t,c)=>(a(),l(j,{class:"coupon-item",key:c},{default:s((()=>[o(j,{class:k(["item-wrapper",[t.state.value?"":"disable"]])},{default:s((()=>[o(j,{class:"coupon-tag"},{default:s((()=>[o(M,null,{default:s((()=>[u(i(v.CouponTypeEnum[t.coupon_type].name),1)])),_:2},1024)])),_:2},1024),o(j,{class:"coupon-left"},{default:s((()=>[o(j,{class:"coupon-reduce"},{default:s((()=>[t.coupon_type==v.CouponTypeEnum.FULL_DISCOUNT.value?(a(),n(d,{key:0},[o(j,{class:"coupon-reduce-unit"},{default:s((()=>[o(M,null,{default:s((()=>[u("￥")])),_:1})])),_:1}),o(j,{class:"coupon-reduce-amount"},{default:s((()=>[o(M,{class:"value"},{default:s((()=>[u(i(t.reduce_price),1)])),_:2},1024)])),_:2},1024)],64)):r("",!0),t.coupon_type==v.CouponTypeEnum.DISCOUNT.value?(a(),l(j,{key:1,class:"coupon-reduce-amount"},{default:s((()=>[o(M,{class:"value"},{default:s((()=>[u(i(t.discount)+"折",1)])),_:2},1024)])),_:2},1024)):r("",!0)])),_:2},1024),o(M,{class:"coupon-hint"},{default:s((()=>[u("满"+i(t.min_price)+"元可用",1)])),_:2},1024)])),_:2},1024),o(j,{class:"coupon-content"},{default:s((()=>[o(j,{class:"coupon-name"},{default:s((()=>[u(i(t.name),1)])),_:2},1024),o(j,{class:"coupon-middle"},{default:s((()=>[o(j,{class:"coupon-expire"},{default:s((()=>[t.expire_type==v.CouponTypeEnum.FULL_DISCOUNT.value?(a(),l(M,{key:0},{default:s((()=>[u("领取后"+i(t.expire_day)+"天内有效",1)])),_:2},1024)):r("",!0),t.expire_type==v.CouponTypeEnum.DISCOUNT.value?(a(),l(M,{key:1},{default:s((()=>[t.start_time===t.end_time?(a(),n(d,{key:0},[u(i(t.start_time)+" 当天有效",1)],64)):(a(),n(d,{key:1},[u(i(t.start_time)+"~"+i(t.end_time),1)],64))])),_:2},1024)):r("",!0)])),_:2},1024)])),_:2},1024),t.describe?(a(),l(j,{key:0,class:"coupon-expand",onClick:e=>T.handleDescribe(c)},{default:s((()=>[o(M,null,{default:s((()=>[u("使用说明")])),_:1}),o(M,{class:k(["coupon-expand-arrow iconfont icon-arrow-down",[t.expand?"expand":""]])},null,8,["class"])])),_:2},1032,["onClick"])):r("",!0)])),_:2},1024),o(j,{class:"coupon-right"},{default:s((()=>[e.$checkModule("market-coupon")&&t.state.value?(a(),l(j,{key:0,class:"btn-receive",onClick:e=>T.handleReceive(t.coupon_id)},{default:s((()=>[o(M,null,{default:s((()=>[u("领取")])),_:1})])),_:2},1032,["onClick"])):r("",!0),t.state.value?r("",!0):(a(),l(j,{key:1,class:"state-text"},{default:s((()=>[o(M,null,{default:s((()=>[u(i(t.state.text),1)])),_:2},1024)])),_:2},1024))])),_:2},1024)])),_:2},1032,["class"]),o(j,{class:k([[t.expand?"expand":""],"coupon-expand-rules"])},{default:s((()=>[o(j,{class:"coupon-expand-rules-content"},{default:s((()=>[o(j,{class:"pre"},{default:s((()=>[u(i(t.describe),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class"])])),_:2},1024)))),128))])),_:1})):r("",!0)])),_:1})])),_:1})])),_:1},8,["modelValue"]),o(P,{modelValue:v.descModal.show,"onUpdate:modelValue":x[3]||(x[3]=e=>v.descModal.show=e),title:v.descModal.title},{default:s((()=>[o(j,{class:"pops-content"},{default:s((()=>[o(M,null,{default:s((()=>[u(i(v.descModal.content),1)])),_:1})])),_:1})])),_:1},8,["modelValue","title"])])),_:1},8,["style"])}],["__scopeId","data-v-42232e15"]]);export{T as M};
