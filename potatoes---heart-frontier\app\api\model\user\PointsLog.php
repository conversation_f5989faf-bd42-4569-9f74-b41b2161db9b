<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\api\model\user;

use app\api\service\User as UserService;
use app\common\model\dealer\Setting as SettingModel;
use app\common\model\user\PointsLog as PointsLogModel;
use cores\exception\BaseException;
use think\facade\Db;

/**
 * 用户积分变动明细模型
 * Class PointsLog
 * @package app\api\model\user
 */
class PointsLog extends PointsLogModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'store_id',
    ];

    /**
     * 获取日志明细列表
     * @return \think\Paginator
     * @throws BaseException
     * @throws \think\db\exception\DbException
     */
    public function getList(): \think\Paginator
    {
        // 当前用户ID
        $userId = UserService::getCurrentLoginUserId();
        // 获取列表数据
        return $this->where('user_id', '=', $userId)
            ->order(['create_time' => 'desc'])
            ->paginate(15);
    }

    public function getDuihuan()
    {

        $user = UserService::getCurrentLoginUser(true);
        $year = date('Y-01-01');

        $apply = Db::name('dealer_withdraw')
        ->where('create_time', '>=', strtotime($year))
        ->where('type',3)
        ->where('user_id', $user['user_id'])->select()->toArray();

        $month = Date('m');
        if ($month == 12 && count($apply) == 0) {
            //年底兑现
            $points_z = floor($user['points'] * 0.3);

            $amount = $points_z * 0.3 / 10; // 积分→现金

            $data['type']     = '年底提现';
            $data['amount']   = $amount;
            $data['points_z'] = $points_z;
            $data['points']   = $user['points'];
            $data['message']  = "可提现总积分的30%：{$amount}元（15工作日到账）";

            $data['can'] = 1;

        } else {
            //上个月
            $timeStart = date('Y-m-01', strtotime('first day of previous month'));

            $timeEnd = date('Y-m-01');

            if ($month == 1) {
                //1月可提现设置为0
                $points = 0;

            } else {
                $points = Db::name('user_points_log')
                    ->where('create_time', '>=', strtotime($timeStart))
                    ->where('create_time', '<', strtotime($timeEnd))
                    ->where('value', '>', 0)->where('type', 0)
                    ->where('user_id', $user['user_id'])
                    ->sum('value');

                if ($points > $user['points']) {
                    $points = $user['points'];
                }

            }

            $points_z = floor($points * 0.2);

            $amount = $points_z / 10; // 积分→现金

            //查询当前月是否提现过

            $log_month   = Db::name('dealer_withdraw')->where('type',3)->where('create_time', '>=', strtotime($timeEnd))->where('user_id', $user['user_id'])->find();
            $data['can'] = 1;
            if ($log_month) {
                $data['can']   = 0;
                $data['error'] = '当前月已经提现过';
                $amount        = 0;
                $points_z      = 0;
                $points        = 0;
            }

            $data['type']     = '当月提现';
            $data['amount']   = $amount;
            $data['points_z'] = $points_z;
            $data['points']   = $points;
            $data['message']  = "可提上月积分的20%：{$amount}元（15工作日到账）";

        }

        return $data;
    }

    public function getPoints()
    {
        $user = UserService::getCurrentLoginUser(true);

        // 分销商基本设置
        $setting = SettingModel::getItem('commission');

        $data['points_all'] = Db::name('user_points_log')->where('user_id', $user['user_id'])
            ->where('value', '>', 0)->where('type', 0)->sum('value');

        $data['points_xiaofei'] = Db::name('user_points_log')->where('user_id', $user['user_id'])
            ->where('value', '<', 0)->where('type', 0)->sum('value');

        $data['points_freeze'] = $user['points_freeze'];

        $data['points']         = $user['points'];
        $data['points_xiaofei'] = -$data['points_xiaofei'];
        //能否提现
        $data['can'] = $user['grade_id'] != 10007 ? 1 : 0;

        return $data;
    }
    //获取一级分销员的积分分成
    public function getDealerPoints()
    {
            $userId = UserService::getCurrentLoginUserId();
        // 分销商基本设置
        $setting = SettingModel::getItem('commission');

        $refereeUser = Db::name('dealer_referee')->where('level', 1)->where('dealer_id', $userId)->select()->toArray();
        $ids         = array_column($refereeUser, 'user_id');

        $time = strtotime(Date('Y-01-01'));

        $points = Db::name('user_points_log')->where('user_id', 'in', $ids)
            ->where('create_time', '>=', $time)->where('type', 0)->where('is_settled', 0)->where('value', '>', 0)->sum('value');
        $money = $setting['points_money'] / 100;

        $points = round($points * $money, 2);

        return $points;
    }
}
