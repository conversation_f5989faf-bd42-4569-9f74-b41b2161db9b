<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\api\model\user;

use app\api\service\User as UserService;
use app\common\model\user\Grade as GradeModel;
use think\facade\Db;
/**
 * 用户会员等级模型
 * Class Grade
 * @package app\api\model\user
 */
class Grade extends GradeModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'store_id',
        'status',
        'is_delete',
        'create_time',
        'update_time',
        'store_id',
    ];

    public function getGradeList()
    {
        $userId = UserService::getCurrentLoginUserId();
        $user   = Db::name('user')->where('user_id', $userId)->find();
        if ($user['grade_id'] > 0) {
            if ($user['vip_endtime'] > time()) {
                $weight = Db::name('user_grade')->where('grade_id', $user['grade_id'])->value('weight');
            } else {
                $weight = 2;
            }

        } else {
            $weight = 2;
        }

        // 获取列表数据
        return $this
            ->where('is_delete', 0)
            ->where('weight', '>=', $weight)
            ->order(['weight' => 'asc'])
            ->select()->toArray();
    }
}
