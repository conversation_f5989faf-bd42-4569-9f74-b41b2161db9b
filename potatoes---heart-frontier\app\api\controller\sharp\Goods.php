<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\sharp;

use think\response\Json;
use app\api\controller\Controller;
use app\api\service\User as UserService;
use app\api\service\sharp\Active as ActiveService;
use app\common\service\qrcode\sharp\Goods as GoodsPoster;
use cores\exception\BaseException;

/**
 * 整点秒杀-商品管理
 * Class Goods
 * @package app\api\controller\sharp
 */
class Goods extends Controller
{
    /**
     * 秒杀活动商品列表
     * @param int $activeTimeId 秒杀会场场次ID
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(int $activeTimeId): Json
    {
        // 获取秒杀活动会场首页数据
        $service = new ActiveService;
        $list = $service->getGoodsListByActiveTimeId($activeTimeId);
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 获取活动商品详情
     * @param int $activeTimeId 秒杀会场场次ID
     * @param int $sharpGoodsId 秒杀商品ID
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(int $activeTimeId, int $sharpGoodsId): Json
    {
        // 获取秒杀活动商品详情
        $service = new ActiveService;
        $data = $service->getActiveGoodsDetail($activeTimeId, $sharpGoodsId);
        return $this->renderSuccess($data);
    }

    /**
     * 生成商品海报
     * @param int $activeTimeId 秒杀会场场次ID
     * @param int $sharpGoodsId 秒杀商品ID
     * @param string $channel 二维码渠道(小程序码、h5码)
     * @return Json
     * @throws BaseException
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function poster(int $activeTimeId, int $sharpGoodsId, string $channel = 'H5'): Json
    {
        // 获取秒杀活动商品详情
        $service = new ActiveService;
        $data = $service->getActiveGoodsDetail($activeTimeId, $sharpGoodsId);
        // 生成商品海报图
        $userId = UserService::getCurrentLoginUserId();
        $Qrcode = new GoodsPoster($data['active'], $data['goods'], $userId, $channel);
        return $this->renderSuccess(['imageUrl' => $Qrcode->getImage()]);
    }
}