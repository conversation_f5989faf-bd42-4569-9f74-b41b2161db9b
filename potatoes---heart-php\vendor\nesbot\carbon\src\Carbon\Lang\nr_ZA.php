<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return array_replace_recursive(require __DIR__.'/en.php', [
    'formats' => [
        'L' => 'DD/MM/YYYY',
    ],
    'months' => ['<PERSON><PERSON>', 'uFeb<PERSON>bari', 'uMatjhi', 'u-<PERSON>eli', '<PERSON><PERSON>', 'Juni', '<PERSON>ayi', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Di<PERSON><PERSON>'],
    'months_short' => ['Jan', 'Feb', 'Mat', 'Apr', 'Mey', 'Jun', 'Jul', 'Arh', 'Sep', 'Okt', 'Usi', 'Dis'],
    'weekdays' => ['u<PERSON><PERSON><PERSON>', 'uMvu<PERSON>', 'uL<PERSON><PERSON>i', 'lesith<PERSON><PERSON>', 'u<PERSON><PERSON><PERSON>', 'ngo<PERSON><PERSON><PERSON><PERSON>', 'umG<PERSON><PERSON><PERSON>'],
    'weekdays_short' => ['Son', 'Mvu', 'Bil', 'Tha', 'Ne', 'Hla', 'Gqi'],
    'weekdays_min' => ['Son', 'Mvu', 'Bil', 'Tha', 'Ne', 'Hla', 'Gqi'],
    'day_of_first_week_of_year' => 1,
    'first_day_of_week' => 0,
]);
