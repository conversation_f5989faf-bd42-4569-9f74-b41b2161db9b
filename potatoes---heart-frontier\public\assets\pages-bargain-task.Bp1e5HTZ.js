import{O as s,S as a,ae as t,r as e,v as l,c as o,w as i,n,b as d,i as r,o as c,a as u,f,t as _,d as h,F as m,k as g,e as k,l as p,g as b,X as w,j as y}from"./index-DAm19nhc.js";import{_ as v}from"./u-modal.F0ZEoOXZ.js";import{r as x}from"./uni-app.es.CS65jdCG.js";import{W as C}from"./wxofficial.DtJt89yj.js";import{A as I}from"./index.C-__j2-W.js";import{C as S}from"./index.BKoYCVS8.js";import{b as B,a as j}from"./index.C0mVC63V.js";import{c as L,d as P,h as $,e as D}from"./task.DOPO1LtO.js";import{_ as T}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./u-loading.DgJsa0sr.js";import"./u-popup.D5lrqP5U.js";import"./u-mask.DjRdn3WP.js";import"./u-icon.BJNYmTLf.js";const R=T({components:{AvatarImage:I,CountDown:S},mixins:[C],data:()=>({isLoading:!0,taskId:void 0,activeId:void 0,task:{},active:{},goods:{},goodsSkuInfo:{},helpList:[],isCreater:!1,isCut:!1,setting:{},showRules:!1,disabled:!1,showBuyBtn:!1,showShareBtn:!1,showCatBtn:!1,showOtherBtn:!1}),onLoad(s){this.taskId=s.taskId,this.onRefreshPage()},methods:{onRefreshPage(){const s=this;s.isLoading=!0,s.getTaskDetail().then((a=>{Promise.all([s.getActiveDetail(),s.getGoodsBasic(),s.getGoodsSku(),s.getHelpList()]).then((()=>{s.initShowBtn(),s.setWxofficialShareData()})).finally((()=>s.isLoading=!1))}))},getTaskDetail(){const s=this;return new Promise(((a,t)=>{L(s.taskId).then((t=>{s.task=t.data.taskInfo,s.activeId=s.task.active_id,s.isCreater=t.data.isCreater,s.isCut=t.data.isCut,s.setting=t.data.setting,a(t)})).catch(t)}))},getActiveDetail(){const s=this;return new Promise(((a,t)=>{P(s.activeId).then((t=>{s.active=t.data.active,a(t)})).catch(t)}))},getGoodsBasic(){const s=this,a=s.task.goods_id;return new Promise(((t,e)=>{B(a,!1).then((a=>{s.goods=a.data.detail,t(a)})).catch(e)}))},getGoodsSku(){const s=this,a=s.task.goods_id,t=s.task.goods_sku_id;return new Promise(((e,l)=>{j(a,t).then((a=>{s.goodsSkuInfo=a.data.skuInfo,e(a)})).catch(l)}))},getHelpList(){const s=this;return new Promise(((a,t)=>{$(s.taskId).then((t=>{s.helpList=t.data.list,a(t)})).catch(t)}))},initShowBtn(){const s=this,a=s.isCreater&&!s.task.is_buy&&s.task.status&&(!s.active.is_floor_buy||s.task.is_floor),t=!s.isCreater&&!s.isCut&&!s.task.is_floor&&s.task.status,e=!t&&!s.task.is_floor&&s.task.status,l=!a&&!e&&!t;s.showBuyBtn=a,s.showCatBtn=t,s.showShareBtn=e,s.showOtherBtn=l},handleShowRules(){this.showRules=!0},handleBuyNow(){this.$navTo("pages/checkout/index",{mode:"bargain",taskId:this.taskId})},handleHelpCut(){const s=this;s.disabled=!0,D(s.taskId).then((a=>{s.$toast(a.message),setTimeout((()=>s.onRefreshPage()),1800)})).finally((()=>s.disabled=!1))},handleShareBtn(){this.handleCopyLink()},handleCopyLink(){const s=this;s.getShareUrl().then((a=>{uni.setClipboardData({data:a,success:()=>s.$toast("复制链接成功，快去发送给朋友吧"),fail:({errMsg:a})=>s.$toast("复制失败 "+a)})}))},getShareUrl(){const{path:e,query:l}=s();return new Promise(((s,o)=>{a.h5Url().then((a=>{const o=t(a,e,l);s(o)}))}))},setWxofficialShareData(){const{active:s,goods:a}=this;this.updateShareCardData({title:s.share_title,desc:s.prompt_words,imgUrl:a.goods_image})}},onShareAppMessage(){const s=this,a=s.$getShareUrlParams({taskId:s.taskId});return{title:s.active.share_title,path:`/pages/bargain/task?${a}`}},onShareTimeline(){const s=this,a=s.$getShareUrlParams({taskId:s.taskId});return{title:s.active.share_title,path:`/pages/bargain/task?${a}`}}},[["render",function(s,a,t,C,I,S){const B=p,j=r,L=e("avatar-image"),P=b,$=w,D=e("count-down"),T=y,R=x(l("u-modal"),v);return I.isLoading?d("",!0):(c(),o(j,{key:0,class:"container",style:n(s.appThemeStyle)},{default:i((()=>[u(j,{class:"header dis-flex flex-x-between"},{default:i((()=>[u(j,{class:"item-touch",onClick:a[0]||(a[0]=a=>s.$navTo("pages/index/index"))},{default:i((()=>[u(B,null,{default:i((()=>[f("返回首页")])),_:1})])),_:1}),u(j,{class:"item-touch",onClick:a[1]||(a[1]=s=>S.handleShowRules())},{default:i((()=>[u(B,null,{default:i((()=>[f("玩法详情")])),_:1})])),_:1})])),_:1}),u(j,{class:"content"},{default:i((()=>[u(j,{class:"infos-wrap"},{default:i((()=>[u(j,{class:"infos-top"},{default:i((()=>[u(j,{class:"infos-img"},{default:i((()=>[u(L,{url:I.task.user.avatar_url,width:104},null,8,["url"])])),_:1}),u(j,{class:"infos-name"},{default:i((()=>[u(B,null,{default:i((()=>[f(_(I.task.user.nick_name),1)])),_:1})])),_:1})])),_:1}),u(j,{class:"infos-mask"},{default:i((()=>[I.active.prompt_words?(c(),o(j,{key:0,class:"infos-prompt"},{default:i((()=>[u(B,null,{default:i((()=>[f(_(I.active.prompt_words),1)])),_:1})])),_:1})):d("",!0),u(j,{class:"infos-item",onClick:a[2]||(a[2]=a=>s.$navTo("pages/bargain/goods/index",{activeId:I.activeId,goodsId:I.goods.goods_id}))},{default:i((()=>[u(j,{class:"infos-item-img"},{default:i((()=>[u(P,{class:"image",src:I.goodsSkuInfo.goods_image?I.goodsSkuInfo.goods_image:I.goods.goods_image},null,8,["src"])])),_:1}),u(j,{class:"infos-item-info"},{default:i((()=>[u(j,{class:"infos-item-name"},{default:i((()=>[u(B,{class:"twoline-hide"},{default:i((()=>[f(_(I.goods.goods_name),1)])),_:1})])),_:1}),u(j,{class:"infos-item-stock"},{default:i((()=>[u(j,{class:"stock-widget"},{default:i((()=>[u(B,null,{default:i((()=>[f("仅剩")])),_:1}),u(B,{class:"stock-num"},{default:i((()=>[f(_(I.goodsSkuInfo.stock_num),1)])),_:1}),u(B,null,{default:i((()=>[f("件")])),_:1})])),_:1})])),_:1}),u(j,{class:"infos-item-price dis-flex flex-y-end"},{default:i((()=>[u(B,{class:"price1 col-m"},{default:i((()=>[f("底价¥")])),_:1}),u(B,{class:"price2 col-m"},{default:i((()=>[f(_(I.task.floor_price),1)])),_:1}),u(B,{class:"price3"},{default:i((()=>[f("¥"+_(I.task.goods_price),1)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),u(j,{class:"connect"},{default:i((()=>[u(j,{class:"connect-ring bgf-ring--left"},{default:i((()=>[u(B,{class:"line"})])),_:1}),u(j,{class:"connect-ring bgf-ring--right"},{default:i((()=>[u(B,{class:"line"})])),_:1})])),_:1}),u(j,{class:"bargain-wrap"},{default:i((()=>[u(j,{class:"bargain-info"},{default:i((()=>[I.task.status?(c(),o(j,{key:0,class:"bargain-ing"},{default:i((()=>[I.task.is_floor?(c(),h(m,{key:1},[u(B,null,{default:i((()=>[f("已砍至最低")])),_:1}),u(B,{class:"focal col-m"},{default:i((()=>[f(_(I.task.floor_price),1)])),_:1}),u(B,null,{default:i((()=>[f("元，砍价成功！")])),_:1})],64)):(c(),h(m,{key:0},[u(B,null,{default:i((()=>[f("已砍")])),_:1}),u(B,{class:"focal col-m"},{default:i((()=>[f(_(I.task.cut_money),1)])),_:1}),u(B,null,{default:i((()=>[f("元，还差")])),_:1}),u(B,{class:"focal col-m"},{default:i((()=>[f(_(I.task.surplus_money),1)])),_:1}),u(B,null,{default:i((()=>[f("元")])),_:1})],64))])),_:1})):(c(),o(j,{key:1,class:"bargain-ing"},{default:i((()=>[u(B,{class:"col-9"},{default:i((()=>[f("该砍价任务已结束～")])),_:1})])),_:1}))])),_:1}),u(j,{class:"bgn__process m-top30"},{default:i((()=>[u(j,{class:"bgn__process-bottom"},{default:i((()=>[u(j,{class:"bgn__process-process process--ani",style:n({width:`${I.task.bargain_rate}%`})},null,8,["style"])])),_:1})])),_:1}),u(j,{class:"btn-container m-top30 dis-flex flex-x-center"},{default:i((()=>[I.showBuyBtn?(c(),o(j,{key:0,class:g(["btn-item btn-item__buy",{complete:I.task.is_floor}]),onClick:a[3]||(a[3]=s=>S.handleBuyNow())},{default:i((()=>[u(B,null,{default:i((()=>[f("立即购买")])),_:1})])),_:1},8,["class"])):d("",!0),I.showShareBtn?(c(),o($,{key:1,"open-type":"share",class:"btn-normal",onClick:a[4]||(a[4]=s=>S.handleShareBtn())},{default:i((()=>[u(j,{class:"btn-item btn-item__main"},{default:i((()=>[u(B,null,{default:i((()=>[f("邀请好友砍价")])),_:1})])),_:1})])),_:1})):d("",!0),I.showCatBtn?(c(),o(j,{key:2,class:"btn-item btn-item__main btn-item-long",onClick:a[5]||(a[5]=s=>S.handleHelpCut())},{default:i((()=>[u(B,null,{default:i((()=>[f("帮TA砍一刀")])),_:1})])),_:1})):d("",!0),I.showOtherBtn?(c(),o(j,{key:3,class:"btn-item btn-item__main btn-item-long",onClick:a[6]||(a[6]=a=>s.$navTo("pages/bargain/index"))},{default:i((()=>[u(B,null,{default:i((()=>[f("查看其他砍价活动")])),_:1})])),_:1})):d("",!0)])),_:1}),I.task.status?(c(),o(j,{key:0,class:"bargain-p"},{default:i((()=>[u(j,{class:"bargain-people dis-flex flex-x-center flex-y-center"},{default:i((()=>[u(B,null,{default:i((()=>[f("活动还剩")])),_:1}),u(D,{date:I.active.end_time,separator:"zh",theme:"text"},null,8,["date"]),u(B,null,{default:i((()=>[f("结束，快来砍价吧~")])),_:1})])),_:1})])),_:1})):d("",!0)])),_:1}),I.helpList.length?(c(),o(j,{key:0,class:"records-container"},{default:i((()=>[u(j,{class:"records"},{default:i((()=>[u(j,{class:"records-back"}),u(j,{class:"records-content"},{default:i((()=>[u(j,{class:"records-h2"},{default:i((()=>[u(B,null,{default:i((()=>[f("好友助力榜")])),_:1})])),_:1}),u(j,{class:"friend-help"},{default:i((()=>[(c(!0),h(m,null,k(I.helpList,((s,a)=>(c(),o(j,{class:"records-item",key:a},{default:i((()=>[u(j,{class:"records-left"},{default:i((()=>[u(L,{url:s.user.avatar_url,width:70},null,8,["url"]),u(B,{class:"nick-name"},{default:i((()=>[f(_(s.user.nick_name),1)])),_:2},1024)])),_:2},1024),u(j,{class:"records-right"},{default:i((()=>[u(B,{class:"bold m-r-6"},{default:i((()=>[f("帮砍了")])),_:1}),u(B,{class:"red"},{default:i((()=>[f("¥"+_(s.cut_money),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})])),_:1})):d("",!0)])),_:1}),I.isLoading?d("",!0):(c(),o(R,{key:0,modelValue:I.showRules,"onUpdate:modelValue":a[7]||(a[7]=s=>I.showRules=s),title:"砍价规则"},{default:i((()=>[u(T,{style:{height:"610rpx"},"scroll-y":!0},{default:i((()=>[u(j,{class:"pops-content"},{default:i((()=>[u(B,null,{default:i((()=>[f(_(I.setting.rulesDesc),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"]))])),_:1},8,["style"]))}],["__scopeId","data-v-bc2f69dd"]]);export{R as default};
