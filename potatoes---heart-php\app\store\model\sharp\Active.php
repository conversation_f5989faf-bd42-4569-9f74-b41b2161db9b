<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\sharp;

use app\common\library\helper;
use app\common\model\sharp\Active as ActiveModel;
use app\store\model\sharp\ActiveTime as ActiveTimeModel;

/**
 * 整点秒杀-活动会场模型
 * Class Active
 * @package app\store\model\sharp
 */
class Active extends ActiveModel
{
    /**
     * 获取器：活动日期
     * @param $value
     * @return false|string
     */
    public function getActiveDateAttr($value)
    {
        return date('Y-m-d', $value);
    }

    /**
     * 获取活动会场列表
     * @return mixed
     * @throws \think\db\exception\DbException
     */
    public function getList()
    {
        // 获取列表记录
        $list = $this->with(['active_time'])
            ->where('is_delete', '=', 0)
            ->order(['active_date' => 'desc'])
            ->paginate(15);
        // 整理共计场次
        return $this->getActiveTimeCount($list);
    }

    /**
     * 整理共计场次
     * @param $list
     * @return mixed
     */
    private function getActiveTimeCount($list)
    {
        foreach ($list as &$item) {
            $activeTimeArr = helper::getArrayColumn($item['active_time'], 'active_time');
            $item['activeTimeCount'] = count($activeTimeArr) ?: 0;
        }
        return $list;
    }

    /**
     * 新增记录
     * @param array $data
     * @return false|mixed
     */
    public function add(array $data)
    {
        // 表单验证
        if (!$this->onValidate($data)) {
            return false;
        }
        // 新增活动
        $data['store_id'] = static::$storeId;
        $data['active_date'] = $this->formatActiveDate($data['active_date']);
        $data['sharpGoodsIds'] = $data['sharpGoodsIds'] ?? [];
        return $this->transaction(function () use ($data) {
            // 新增活动记录
            $this->save($data);
            // 新增活动场次
            (new ActiveTimeModel)->onBatchAdd(
                (int)$this['active_id'],
                $data['activeTimes'],
                $data['sharpGoodsIds']
            );
            return true;
        });
    }

    /**
     * 格式化活动日期
     * @param string $date
     * @return false|int
     */
    private function formatActiveDate(string $date)
    {
        return strtotime(date('Y-m-d', strtotime($date)));
    }

    /**
     * 表单验证
     * @param $data
     * @return bool
     */
    private function onValidate($data): bool
    {
        // 活动日期是否已存在
        if ($this->isExistByActiveDate($data['active_date'])) {
            $this->error = '该活动日期已存在';
            return false;
        }
        //    // 验证是否选择商品
        //    if (!isset($data['sharpGoodsIds']) || empty($data['sharpGoodsIds'])) {
        //        $this->error = '您还没有选择秒杀商品';
        //        return false;
        //    }
        return true;
    }

    /**
     * 活动日期是否已存在
     * @param string $date
     * @return bool
     */
    private function isExistByActiveDate(string $date): bool
    {
        return (bool)(new static)->where('active_date', '=', $this->formatActiveDate($date))
            ->where('is_delete', '=', 0)
            ->value('active_id');
    }

    /**
     * 修改商品状态
     * @param int $state 活动状态
     * @return bool|false
     */
    public function setStatus(int $state): bool
    {
        return $this->save(['status' => $state]) !== false;
    }

    /**
     * 软删除
     * @return bool|false
     */
    public function setDelete(): bool
    {
        // 同步删除场次和商品关联
        (new ActiveTimeModel)->onDeleteByActiveId($this['active_id']);
        // 将该活动设置为已删除
        return $this->save(['is_delete' => 1]);
    }
}