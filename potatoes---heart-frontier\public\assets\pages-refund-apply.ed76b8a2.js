import{o as e,c as a,w as s,n as t,e as o,i as l,a as d,f as i,t as c,b as n,d as r,F as m,k as u,g as f,l as g,a3 as p}from"./index-4ddb689d.js";import{a as h}from"./RefundType.055df00b.js";import{i as _}from"./upload.3f305180.js";import{g as b,b as x}from"./refund.57dda06a.js";import{_ as y}from"./_plugin-vue_export-helper.1b428a4d.js";const w=y({data:()=>({RefundTypeEnum:h,isLoading:!0,orderGoodsId:null,goods:{},formData:{images:[],type:10,content:""},imageList:[],maxImageLength:6,disabled:!1}),onLoad({orderGoodsId:e}){this.orderGoodsId=e,this.getGoodsDetail()},methods:{getGoodsDetail(){const e=this;e.isLoading=!0,b(e.orderGoodsId).then((a=>{e.goods=a.data.goods,e.isLoading=!1}))},onSwitchService(e){this.formData.type=e},chooseImage(){const e=this,a=e.imageList;uni.chooseImage({count:6-a.length,sizeType:["original","compressed"],sourceType:["album","camera"],success({tempFiles:s}){e.imageList=a.concat(s)}})},deleteImage(e){this.imageList.splice(e,1)},handleSubmit(){const e=this,{imageList:a}=e;return!0!==e.disabled&&(e.formData.content.trim().length?(e.disabled=!0,void(a.length>0?e.uploadFile().then((()=>e.onSubmit())).catch((a=>{e.disabled=!1,0!==a.statusCode&&e.$toast(a.errMsg),console.log("err",a)})):e.onSubmit())):(e.$toast("请填写申请原因"),!1))},onSubmit(){const e=this;x(e.orderGoodsId,e.formData).then((a=>{e.$toast(a.message),setTimeout((()=>{e.disabled=!1,uni.navigateBack()}),1500)})).catch((a=>e.disabled=!1))},uploadFile(){const e=this,{imageList:a}=e;return new Promise(((s,t)=>{a.length>0?_(a).then((a=>{e.formData.images=a,s(a)})).catch(t):s()}))}}},[["render",function(h,_,b,x,y,w){const k=f,v=l,L=g,I=p;return y.isLoading?o("",!0):(e(),a(v,{key:0,class:"container",style:t(h.appThemeStyle)},{default:s((()=>[d(v,{class:"goods-detail b-f dis-flex flex-dir-row"},{default:s((()=>[d(v,{class:"left"},{default:s((()=>[d(k,{class:"goods-image",src:y.goods.goods_image},null,8,["src"])])),_:1}),d(v,{class:"right dis-flex flex-box flex-dir-column flex-x-around"},{default:s((()=>[d(v,{class:"goods-name"},{default:s((()=>[d(L,{class:"twoline-hide"},{default:s((()=>[i(c(y.goods.goods_name),1)])),_:1})])),_:1}),d(v,{class:"dis-flex col-9 f-24"},{default:s((()=>[d(v,{class:"flex-box"},{default:s((()=>[d(v,{class:"goods-props clearfix"},{default:s((()=>[(e(!0),n(m,null,r(y.goods.goods_props,((t,o)=>(e(),a(v,{class:"goods-props-item",key:o},{default:s((()=>[d(L,null,{default:s((()=>[i(c(t.value.name),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1}),d(L,{class:"t-r"},{default:s((()=>[i("×"+c(y.goods.total_num),1)])),_:1})])),_:1})])),_:1})])),_:1}),d(v,{class:"row-service b-f m-top20"},{default:s((()=>[d(v,{class:"row-title"},{default:s((()=>[i("服务类型")])),_:1}),d(v,{class:"service-switch dis-flex"},{default:s((()=>[(e(!0),n(m,null,r(y.RefundTypeEnum.data,((t,o)=>(e(),a(v,{class:u(["switch-item",{active:y.formData.type==t.value}]),key:o,onClick:e=>w.onSwitchService(t.value)},{default:s((()=>[i(c(t.name),1)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1}),d(v,{class:"row-textarea b-f m-top20"},{default:s((()=>[d(v,{class:"row-title"},{default:s((()=>[i("申请原因")])),_:1}),d(v,{class:"content"},{default:s((()=>[d(I,{class:"textarea",modelValue:y.formData.content,"onUpdate:modelValue":_[0]||(_[0]=e=>y.formData.content=e),maxlength:"2000",placeholder:"请详细填写申请原因，注意保持商品的完好，建议您先与卖家沟通",placeholderStyle:"color:#ccc"},null,8,["modelValue"])])),_:1})])),_:1}),y.formData.type==y.RefundTypeEnum.RETURN.value?(e(),a(v,{key:0,class:"row-money b-f m-top20 dis-flex"},{default:s((()=>[d(v,{class:"row-title"},{default:s((()=>[i("退款金额")])),_:1}),d(v,{class:"money col-m"},{default:s((()=>[i("￥"+c(y.goods.total_pay_price),1)])),_:1})])),_:1})):o("",!0),d(v,{class:"row-voucher b-f m-top20"},{default:s((()=>[d(v,{class:"row-title"},{default:s((()=>[i("上传凭证 (最多6张)")])),_:1}),d(v,{class:"image-list"},{default:s((()=>[(e(!0),n(m,null,r(y.imageList,((t,o)=>(e(),a(v,{class:"image-preview",key:o},{default:s((()=>[d(L,{class:"image-delete iconfont icon-shanchu",onClick:e=>w.deleteImage(o)},null,8,["onClick"]),d(k,{class:"image",mode:"aspectFill",src:t.path},null,8,["src"])])),_:2},1024)))),128)),y.imageList.length<y.maxImageLength?(e(),a(v,{key:0,class:"image-picker",onClick:_[1]||(_[1]=e=>w.chooseImage())},{default:s((()=>[d(L,{class:"choose-icon iconfont icon-camera"}),d(L,{class:"choose-text"},{default:s((()=>[i("上传图片")])),_:1})])),_:1})):o("",!0)])),_:1})])),_:1}),d(v,{class:"footer-fixed"},{default:s((()=>[d(v,{class:"btn-wrapper"},{default:s((()=>[d(v,{class:u(["btn-item btn-item-main",{disabled:y.disabled}]),onClick:_[2]||(_[2]=e=>w.handleSubmit())},{default:s((()=>[i("确认提交")])),_:1},8,["class"])])),_:1})])),_:1})])),_:1},8,["style"]))}],["__scopeId","data-v-a36de93b"]]);export{w as default};
