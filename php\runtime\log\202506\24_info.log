[2025-06-24 19:22:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763543
            [orderIds] => []
        )

)

[2025-06-24 19:22:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763554
            [orderIds] => []
        )

)

[2025-06-24 19:22:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763565
            [orderIds] => []
        )

)

[2025-06-24 19:22:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763576
            [orderIds] => []
        )

)

[2025-06-24 19:23:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763587
            [orderIds] => []
        )

)

[2025-06-24 19:23:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763598
            [orderIds] => []
        )

)

[2025-06-24 19:23:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763609
            [orderIds] => []
        )

)

[2025-06-24 19:23:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763620
            [orderIds] => []
        )

)

[2025-06-24 19:23:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763631
            [orderIds] => []
        )

)

[2025-06-24 19:24:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763642
            [orderIds] => []
        )

)

[2025-06-24 19:24:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763653
            [orderIds] => []
        )

)

[2025-06-24 19:24:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763664
            [orderIds] => []
        )

)

[2025-06-24 19:24:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763675
            [orderIds] => []
        )

)

[2025-06-24 19:24:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763686
            [orderIds] => []
        )

)

[2025-06-24 19:24:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763697
            [orderIds] => []
        )

)

[2025-06-24 19:25:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763708
            [orderIds] => []
        )

)

[2025-06-24 19:25:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763719
            [orderIds] => []
        )

)

[2025-06-24 19:25:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763730
            [orderIds] => []
        )

)

[2025-06-24 19:25:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763741
            [orderIds] => []
        )

)

[2025-06-24 19:25:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763752
            [orderIds] => []
        )

)

[2025-06-24 19:26:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763763
            [orderIds] => []
        )

)

[2025-06-24 19:26:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763774
            [orderIds] => []
        )

)

[2025-06-24 19:26:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763785
            [orderIds] => []
        )

)

[2025-06-24 19:26:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763796
            [orderIds] => []
        )

)

[2025-06-24 19:26:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763807
            [orderIds] => []
        )

)

[2025-06-24 19:26:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763818
            [orderIds] => []
        )

)

[2025-06-24 19:27:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763829
            [orderIds] => []
        )

)

[2025-06-24 19:27:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 19:27:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 19:27:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 19:27:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 19:27:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763840
            [orderIds] => []
        )

)

[2025-06-24 19:27:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763851
            [orderIds] => []
        )

)

[2025-06-24 19:27:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763862
            [orderIds] => []
        )

)

[2025-06-24 19:27:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763873
            [orderIds] => []
        )

)

[2025-06-24 19:28:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763884
            [orderIds] => []
        )

)

[2025-06-24 19:28:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763895
            [orderIds] => []
        )

)

[2025-06-24 19:28:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763906
            [orderIds] => []
        )

)

[2025-06-24 19:28:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763917
            [orderIds] => []
        )

)

[2025-06-24 19:28:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763928
            [orderIds] => []
        )

)

[2025-06-24 19:28:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763939
            [orderIds] => []
        )

)

[2025-06-24 19:29:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763950
            [orderIds] => []
        )

)

[2025-06-24 19:29:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763961
            [orderIds] => []
        )

)

[2025-06-24 19:29:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763972
            [orderIds] => []
        )

)

[2025-06-24 19:29:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763983
            [orderIds] => []
        )

)

[2025-06-24 19:29:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750763994
            [orderIds] => []
        )

)

[2025-06-24 19:30:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764005
            [orderIds] => []
        )

)

[2025-06-24 19:30:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764016
            [orderIds] => []
        )

)

[2025-06-24 19:30:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764027
            [orderIds] => []
        )

)

[2025-06-24 19:30:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764038
            [orderIds] => []
        )

)

[2025-06-24 19:30:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764049
            [orderIds] => []
        )

)

[2025-06-24 19:31:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764060
            [orderIds] => []
        )

)

[2025-06-24 19:31:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764071
            [orderIds] => []
        )

)

[2025-06-24 19:31:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764082
            [orderIds] => []
        )

)

[2025-06-24 19:31:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764093
            [orderIds] => []
        )

)

[2025-06-24 19:31:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764104
            [orderIds] => []
        )

)

[2025-06-24 19:31:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764115
            [orderIds] => []
        )

)

[2025-06-24 19:32:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764126
            [orderIds] => []
        )

)

[2025-06-24 19:32:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764137
            [orderIds] => []
        )

)

[2025-06-24 19:32:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764148
            [orderIds] => []
        )

)

[2025-06-24 19:32:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764159
            [orderIds] => []
        )

)

[2025-06-24 19:32:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764170
            [orderIds] => []
        )

)

[2025-06-24 19:33:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764181
            [orderIds] => []
        )

)

[2025-06-24 19:33:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764192
            [orderIds] => []
        )

)

[2025-06-24 19:33:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764203
            [orderIds] => []
        )

)

[2025-06-24 19:33:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764214
            [orderIds] => []
        )

)

[2025-06-24 19:33:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764225
            [orderIds] => []
        )

)

[2025-06-24 19:33:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764236
            [orderIds] => []
        )

)

[2025-06-24 19:34:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764247
            [orderIds] => []
        )

)

[2025-06-24 19:34:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764258
            [orderIds] => []
        )

)

[2025-06-24 19:34:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764269
            [orderIds] => []
        )

)

[2025-06-24 19:34:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764280
            [orderIds] => []
        )

)

[2025-06-24 19:34:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764291
            [orderIds] => []
        )

)

[2025-06-24 19:35:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764302
            [orderIds] => []
        )

)

[2025-06-24 19:35:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764313
            [orderIds] => []
        )

)

[2025-06-24 19:35:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764324
            [orderIds] => []
        )

)

[2025-06-24 19:35:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764335
            [orderIds] => []
        )

)

[2025-06-24 19:35:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764346
            [orderIds] => []
        )

)

[2025-06-24 19:35:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764357
            [orderIds] => []
        )

)

[2025-06-24 19:36:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764368
            [orderIds] => []
        )

)

[2025-06-24 19:36:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764379
            [orderIds] => []
        )

)

[2025-06-24 19:36:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764390
            [orderIds] => []
        )

)

[2025-06-24 19:36:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764401
            [orderIds] => []
        )

)

[2025-06-24 19:36:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764412
            [orderIds] => []
        )

)

[2025-06-24 19:37:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764423
            [orderIds] => []
        )

)

[2025-06-24 19:37:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764434
            [orderIds] => []
        )

)

[2025-06-24 19:37:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 19:37:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 19:37:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 19:37:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 19:37:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764445
            [orderIds] => []
        )

)

[2025-06-24 19:37:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764456
            [orderIds] => []
        )

)

[2025-06-24 19:37:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764467
            [orderIds] => []
        )

)

[2025-06-24 19:37:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764478
            [orderIds] => []
        )

)

[2025-06-24 19:38:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764489
            [orderIds] => []
        )

)

[2025-06-24 19:38:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764500
            [orderIds] => []
        )

)

[2025-06-24 19:38:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764511
            [orderIds] => []
        )

)

[2025-06-24 19:38:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1750678714
            [orderIds] => []
        )

)

[2025-06-24 19:38:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1750160314
            [orderIds] => []
        )

)

[2025-06-24 19:38:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1750160314
            [orderIds] => []
        )

)

[2025-06-24 19:38:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-24 19:38:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-24 19:38:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-24 19:38:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-24 19:38:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-24 19:38:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764522
            [orderIds] => []
        )

)

[2025-06-24 19:38:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764533
            [orderIds] => []
        )

)

[2025-06-24 19:39:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764544
            [orderIds] => []
        )

)

[2025-06-24 19:39:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764555
            [orderIds] => []
        )

)

[2025-06-24 19:39:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764566
            [orderIds] => []
        )

)

[2025-06-24 19:39:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764578
            [orderIds] => []
        )

)

[2025-06-24 19:39:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764588
            [orderIds] => []
        )

)

[2025-06-24 19:39:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764598
            [orderIds] => []
        )

)

[2025-06-24 19:40:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764608
            [orderIds] => []
        )

)

[2025-06-24 19:40:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764619
            [orderIds] => []
        )

)

[2025-06-24 19:40:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764630
            [orderIds] => []
        )

)

[2025-06-24 19:40:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764641
            [orderIds] => []
        )

)

[2025-06-24 19:40:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764652
            [orderIds] => []
        )

)

[2025-06-24 19:41:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764663
            [orderIds] => []
        )

)

[2025-06-24 19:41:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764674
            [orderIds] => []
        )

)

[2025-06-24 19:41:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764685
            [orderIds] => []
        )

)

[2025-06-24 19:41:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764696
            [orderIds] => []
        )

)

[2025-06-24 19:41:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764707
            [orderIds] => []
        )

)

[2025-06-24 19:41:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764718
            [orderIds] => []
        )

)

[2025-06-24 19:42:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764729
            [orderIds] => []
        )

)

[2025-06-24 19:42:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764740
            [orderIds] => []
        )

)

[2025-06-24 19:42:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764751
            [orderIds] => []
        )

)

[2025-06-24 19:42:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764762
            [orderIds] => []
        )

)

[2025-06-24 19:42:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764773
            [orderIds] => []
        )

)

[2025-06-24 19:43:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764784
            [orderIds] => []
        )

)

[2025-06-24 19:43:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764795
            [orderIds] => []
        )

)

[2025-06-24 19:43:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764806
            [orderIds] => []
        )

)

[2025-06-24 19:43:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764817
            [orderIds] => []
        )

)

[2025-06-24 19:43:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764828
            [orderIds] => []
        )

)

[2025-06-24 19:43:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764839
            [orderIds] => []
        )

)

[2025-06-24 19:44:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764850
            [orderIds] => []
        )

)

[2025-06-24 19:44:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764861
            [orderIds] => []
        )

)

[2025-06-24 19:44:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764872
            [orderIds] => []
        )

)

[2025-06-24 19:44:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764883
            [orderIds] => []
        )

)

[2025-06-24 19:44:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764894
            [orderIds] => []
        )

)

[2025-06-24 19:45:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764905
            [orderIds] => []
        )

)

[2025-06-24 19:45:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764916
            [orderIds] => []
        )

)

[2025-06-24 19:45:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764927
            [orderIds] => []
        )

)

[2025-06-24 19:45:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764938
            [orderIds] => []
        )

)

[2025-06-24 19:45:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764949
            [orderIds] => []
        )

)

[2025-06-24 19:46:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764960
            [orderIds] => []
        )

)

[2025-06-24 19:46:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764971
            [orderIds] => []
        )

)

[2025-06-24 19:46:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764982
            [orderIds] => []
        )

)

[2025-06-24 19:46:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750764993
            [orderIds] => []
        )

)

[2025-06-24 19:46:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765004
            [orderIds] => []
        )

)

[2025-06-24 19:46:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765015
            [orderIds] => []
        )

)

[2025-06-24 19:47:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765026
            [orderIds] => []
        )

)

[2025-06-24 19:47:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765037
            [orderIds] => []
        )

)

[2025-06-24 19:47:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 19:47:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 19:47:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 19:47:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 19:47:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765048
            [orderIds] => []
        )

)

[2025-06-24 19:47:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765059
            [orderIds] => []
        )

)

[2025-06-24 19:47:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765070
            [orderIds] => []
        )

)

[2025-06-24 19:48:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765081
            [orderIds] => []
        )

)

[2025-06-24 19:48:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765092
            [orderIds] => []
        )

)

[2025-06-24 19:48:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765103
            [orderIds] => []
        )

)

[2025-06-24 19:48:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765114
            [orderIds] => []
        )

)

[2025-06-24 19:48:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765125
            [orderIds] => []
        )

)

[2025-06-24 19:48:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765136
            [orderIds] => []
        )

)

[2025-06-24 19:49:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765147
            [orderIds] => []
        )

)

[2025-06-24 19:49:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765158
            [orderIds] => []
        )

)

[2025-06-24 19:49:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765169
            [orderIds] => []
        )

)

[2025-06-24 19:49:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765180
            [orderIds] => []
        )

)

[2025-06-24 19:49:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765191
            [orderIds] => []
        )

)

[2025-06-24 19:50:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765202
            [orderIds] => []
        )

)

[2025-06-24 19:50:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765213
            [orderIds] => []
        )

)

[2025-06-24 19:50:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765224
            [orderIds] => []
        )

)

[2025-06-24 19:50:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765235
            [orderIds] => []
        )

)

[2025-06-24 19:50:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765246
            [orderIds] => []
        )

)

[2025-06-24 19:50:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765257
            [orderIds] => []
        )

)

[2025-06-24 19:51:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765268
            [orderIds] => []
        )

)

[2025-06-24 19:51:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765279
            [orderIds] => []
        )

)

[2025-06-24 19:51:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765290
            [orderIds] => []
        )

)

[2025-06-24 19:51:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765301
            [orderIds] => []
        )

)

[2025-06-24 19:51:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765312
            [orderIds] => []
        )

)

[2025-06-24 19:52:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765323
            [orderIds] => []
        )

)

[2025-06-24 19:52:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765334
            [orderIds] => []
        )

)

[2025-06-24 19:52:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765345
            [orderIds] => []
        )

)

[2025-06-24 19:52:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765356
            [orderIds] => []
        )

)

[2025-06-24 19:52:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765367
            [orderIds] => []
        )

)

[2025-06-24 19:52:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765378
            [orderIds] => []
        )

)

[2025-06-24 19:53:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765389
            [orderIds] => []
        )

)

[2025-06-24 19:53:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765400
            [orderIds] => []
        )

)

[2025-06-24 19:53:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765411
            [orderIds] => []
        )

)

[2025-06-24 19:53:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765422
            [orderIds] => []
        )

)

[2025-06-24 19:53:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765433
            [orderIds] => []
        )

)

[2025-06-24 19:54:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765444
            [orderIds] => []
        )

)

[2025-06-24 19:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765455
            [orderIds] => []
        )

)

[2025-06-24 19:54:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765466
            [orderIds] => []
        )

)

[2025-06-24 19:54:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765477
            [orderIds] => []
        )

)

[2025-06-24 19:54:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765488
            [orderIds] => []
        )

)

[2025-06-24 19:54:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765499
            [orderIds] => []
        )

)

[2025-06-24 19:55:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765510
            [orderIds] => []
        )

)

[2025-06-24 19:55:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765521
            [orderIds] => []
        )

)

[2025-06-24 19:55:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765532
            [orderIds] => []
        )

)

[2025-06-24 19:55:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765543
            [orderIds] => []
        )

)

[2025-06-24 19:55:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765554
            [orderIds] => []
        )

)

[2025-06-24 19:56:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765565
            [orderIds] => []
        )

)

[2025-06-24 19:56:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765576
            [orderIds] => []
        )

)

[2025-06-24 19:56:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765587
            [orderIds] => []
        )

)

[2025-06-24 19:56:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765598
            [orderIds] => []
        )

)

[2025-06-24 19:56:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765609
            [orderIds] => []
        )

)

[2025-06-24 19:57:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765620
            [orderIds] => []
        )

)

[2025-06-24 19:57:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765631
            [orderIds] => []
        )

)

[2025-06-24 19:57:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765642
            [orderIds] => []
        )

)

[2025-06-24 19:57:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 19:57:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 19:57:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 19:57:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 19:57:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765653
            [orderIds] => []
        )

)

[2025-06-24 19:57:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765664
            [orderIds] => []
        )

)

[2025-06-24 19:57:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765675
            [orderIds] => []
        )

)

[2025-06-24 19:58:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765686
            [orderIds] => []
        )

)

[2025-06-24 19:58:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765697
            [orderIds] => []
        )

)

[2025-06-24 19:58:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765708
            [orderIds] => []
        )

)

[2025-06-24 19:58:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765719
            [orderIds] => []
        )

)

[2025-06-24 19:58:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765730
            [orderIds] => []
        )

)

[2025-06-24 19:59:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765741
            [orderIds] => []
        )

)

[2025-06-24 19:59:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765752
            [orderIds] => []
        )

)

[2025-06-24 19:59:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765763
            [orderIds] => []
        )

)

[2025-06-24 19:59:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765774
            [orderIds] => []
        )

)

[2025-06-24 19:59:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765785
            [orderIds] => []
        )

)

[2025-06-24 19:59:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765796
            [orderIds] => []
        )

)

[2025-06-24 20:00:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765807
            [orderIds] => []
        )

)

[2025-06-24 20:00:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765818
            [orderIds] => []
        )

)

[2025-06-24 20:00:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765829
            [orderIds] => []
        )

)

[2025-06-24 20:00:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765840
            [orderIds] => []
        )

)

[2025-06-24 20:00:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765851
            [orderIds] => []
        )

)

[2025-06-24 20:01:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765862
            [orderIds] => []
        )

)

[2025-06-24 20:01:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765873
            [orderIds] => []
        )

)

[2025-06-24 20:01:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765884
            [orderIds] => []
        )

)

[2025-06-24 20:01:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765895
            [orderIds] => []
        )

)

[2025-06-24 20:01:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765906
            [orderIds] => []
        )

)

[2025-06-24 20:01:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765917
            [orderIds] => []
        )

)

[2025-06-24 20:02:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765928
            [orderIds] => []
        )

)

[2025-06-24 20:02:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765939
            [orderIds] => []
        )

)

[2025-06-24 20:02:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765950
            [orderIds] => []
        )

)

[2025-06-24 20:02:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765961
            [orderIds] => []
        )

)

[2025-06-24 20:02:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765972
            [orderIds] => []
        )

)

[2025-06-24 20:03:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765983
            [orderIds] => []
        )

)

[2025-06-24 20:03:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750765994
            [orderIds] => []
        )

)

[2025-06-24 20:03:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766005
            [orderIds] => []
        )

)

[2025-06-24 20:03:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766016
            [orderIds] => []
        )

)

[2025-06-24 20:03:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766027
            [orderIds] => []
        )

)

[2025-06-24 20:03:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766038
            [orderIds] => []
        )

)

[2025-06-24 20:04:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766049
            [orderIds] => []
        )

)

[2025-06-24 20:04:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766060
            [orderIds] => []
        )

)

[2025-06-24 20:04:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766071
            [orderIds] => []
        )

)

[2025-06-24 20:04:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766082
            [orderIds] => []
        )

)

[2025-06-24 20:04:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766093
            [orderIds] => []
        )

)

[2025-06-24 20:05:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766104
            [orderIds] => []
        )

)

[2025-06-24 20:05:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766115
            [orderIds] => []
        )

)

[2025-06-24 20:05:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766126
            [orderIds] => []
        )

)

[2025-06-24 20:05:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766137
            [orderIds] => []
        )

)

[2025-06-24 20:05:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766148
            [orderIds] => []
        )

)

[2025-06-24 20:05:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766159
            [orderIds] => []
        )

)

[2025-06-24 20:06:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766170
            [orderIds] => []
        )

)

[2025-06-24 20:06:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766181
            [orderIds] => []
        )

)

[2025-06-24 20:06:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766192
            [orderIds] => []
        )

)

[2025-06-24 20:06:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766203
            [orderIds] => []
        )

)

[2025-06-24 20:06:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766214
            [orderIds] => []
        )

)

[2025-06-24 20:07:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766225
            [orderIds] => []
        )

)

[2025-06-24 20:07:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766236
            [orderIds] => []
        )

)

[2025-06-24 20:07:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 20:07:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 20:07:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 20:07:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 20:07:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766247
            [orderIds] => []
        )

)

[2025-06-24 20:07:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766258
            [orderIds] => []
        )

)

[2025-06-24 20:07:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766269
            [orderIds] => []
        )

)

[2025-06-24 20:08:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766280
            [orderIds] => []
        )

)

[2025-06-24 20:08:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766291
            [orderIds] => []
        )

)

[2025-06-24 20:08:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766302
            [orderIds] => []
        )

)

[2025-06-24 20:08:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766313
            [orderIds] => []
        )

)

[2025-06-24 20:08:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1750680515
            [orderIds] => []
        )

)

[2025-06-24 20:08:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1750162115
            [orderIds] => []
        )

)

[2025-06-24 20:08:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1750162115
            [orderIds] => []
        )

)

[2025-06-24 20:08:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-24 20:08:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-24 20:08:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-24 20:08:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-24 20:08:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-24 20:08:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766324
            [orderIds] => []
        )

)

[2025-06-24 20:08:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766335
            [orderIds] => []
        )

)

[2025-06-24 20:09:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766346
            [orderIds] => []
        )

)

[2025-06-24 20:09:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766357
            [orderIds] => []
        )

)

[2025-06-24 20:09:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766368
            [orderIds] => []
        )

)

[2025-06-24 20:09:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766379
            [orderIds] => []
        )

)

[2025-06-24 20:09:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766390
            [orderIds] => []
        )

)

[2025-06-24 20:10:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766401
            [orderIds] => []
        )

)

[2025-06-24 20:10:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766412
            [orderIds] => []
        )

)

[2025-06-24 20:10:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766423
            [orderIds] => []
        )

)

[2025-06-24 20:10:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766434
            [orderIds] => []
        )

)

[2025-06-24 20:10:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766445
            [orderIds] => []
        )

)

[2025-06-24 20:10:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766456
            [orderIds] => []
        )

)

[2025-06-24 20:11:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766467
            [orderIds] => []
        )

)

[2025-06-24 20:11:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766478
            [orderIds] => []
        )

)

[2025-06-24 20:11:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766489
            [orderIds] => []
        )

)

[2025-06-24 20:11:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766500
            [orderIds] => []
        )

)

[2025-06-24 20:11:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766511
            [orderIds] => []
        )

)

[2025-06-24 20:12:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766522
            [orderIds] => []
        )

)

[2025-06-24 20:12:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766533
            [orderIds] => []
        )

)

[2025-06-24 20:12:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766544
            [orderIds] => []
        )

)

[2025-06-24 20:12:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766555
            [orderIds] => []
        )

)

[2025-06-24 20:12:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766566
            [orderIds] => []
        )

)

[2025-06-24 20:12:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766577
            [orderIds] => []
        )

)

[2025-06-24 20:13:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766588
            [orderIds] => []
        )

)

[2025-06-24 20:13:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766599
            [orderIds] => []
        )

)

[2025-06-24 20:13:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766610
            [orderIds] => []
        )

)

[2025-06-24 20:13:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766621
            [orderIds] => []
        )

)

[2025-06-24 20:13:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766632
            [orderIds] => []
        )

)

[2025-06-24 20:14:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766643
            [orderIds] => []
        )

)

[2025-06-24 20:14:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766654
            [orderIds] => []
        )

)

[2025-06-24 20:14:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766665
            [orderIds] => []
        )

)

[2025-06-24 20:14:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766676
            [orderIds] => []
        )

)

[2025-06-24 20:14:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766687
            [orderIds] => []
        )

)

[2025-06-24 20:14:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766698
            [orderIds] => []
        )

)

[2025-06-24 20:15:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766709
            [orderIds] => []
        )

)

[2025-06-24 20:15:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766720
            [orderIds] => []
        )

)

[2025-06-24 20:15:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766731
            [orderIds] => []
        )

)

[2025-06-24 20:15:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766742
            [orderIds] => []
        )

)

[2025-06-24 20:15:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766753
            [orderIds] => []
        )

)

[2025-06-24 20:16:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766764
            [orderIds] => []
        )

)

[2025-06-24 20:16:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766775
            [orderIds] => []
        )

)

[2025-06-24 20:16:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766786
            [orderIds] => []
        )

)

[2025-06-24 20:16:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766797
            [orderIds] => []
        )

)

[2025-06-24 20:16:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766808
            [orderIds] => []
        )

)

[2025-06-24 20:16:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766819
            [orderIds] => []
        )

)

[2025-06-24 20:17:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766830
            [orderIds] => []
        )

)

[2025-06-24 20:17:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766841
            [orderIds] => []
        )

)

[2025-06-24 20:17:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 20:17:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 20:17:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 20:17:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 20:17:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766852
            [orderIds] => []
        )

)

[2025-06-24 20:17:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766863
            [orderIds] => []
        )

)

[2025-06-24 20:17:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766874
            [orderIds] => []
        )

)

[2025-06-24 20:18:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766885
            [orderIds] => []
        )

)

[2025-06-24 20:18:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766896
            [orderIds] => []
        )

)

[2025-06-24 20:18:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766907
            [orderIds] => []
        )

)

[2025-06-24 20:18:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766918
            [orderIds] => []
        )

)

[2025-06-24 20:18:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766929
            [orderIds] => []
        )

)

[2025-06-24 20:19:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766940
            [orderIds] => []
        )

)

[2025-06-24 20:19:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766951
            [orderIds] => []
        )

)

[2025-06-24 20:19:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766962
            [orderIds] => []
        )

)

[2025-06-24 20:19:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766973
            [orderIds] => []
        )

)

[2025-06-24 20:19:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766984
            [orderIds] => []
        )

)

[2025-06-24 20:19:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750766995
            [orderIds] => []
        )

)

[2025-06-24 20:20:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767006
            [orderIds] => []
        )

)

[2025-06-24 20:20:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767017
            [orderIds] => []
        )

)

[2025-06-24 20:20:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767028
            [orderIds] => []
        )

)

[2025-06-24 20:20:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767039
            [orderIds] => []
        )

)

[2025-06-24 20:20:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767050
            [orderIds] => []
        )

)

[2025-06-24 20:21:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767061
            [orderIds] => []
        )

)

[2025-06-24 20:21:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767072
            [orderIds] => []
        )

)

[2025-06-24 20:21:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767083
            [orderIds] => []
        )

)

[2025-06-24 20:21:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767094
            [orderIds] => []
        )

)

[2025-06-24 20:21:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767105
            [orderIds] => []
        )

)

[2025-06-24 20:21:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767116
            [orderIds] => []
        )

)

[2025-06-24 20:22:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767127
            [orderIds] => []
        )

)

[2025-06-24 20:22:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767138
            [orderIds] => []
        )

)

[2025-06-24 20:22:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767149
            [orderIds] => []
        )

)

[2025-06-24 20:22:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767160
            [orderIds] => []
        )

)

[2025-06-24 20:22:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767171
            [orderIds] => []
        )

)

[2025-06-24 20:23:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767182
            [orderIds] => []
        )

)

[2025-06-24 20:23:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767193
            [orderIds] => []
        )

)

[2025-06-24 20:23:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767204
            [orderIds] => []
        )

)

[2025-06-24 20:23:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767215
            [orderIds] => []
        )

)

[2025-06-24 20:23:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767226
            [orderIds] => []
        )

)

[2025-06-24 20:23:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767237
            [orderIds] => []
        )

)

[2025-06-24 20:24:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767248
            [orderIds] => []
        )

)

[2025-06-24 20:24:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767259
            [orderIds] => []
        )

)

[2025-06-24 20:24:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767270
            [orderIds] => []
        )

)

[2025-06-24 20:24:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767281
            [orderIds] => []
        )

)

[2025-06-24 20:24:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767292
            [orderIds] => []
        )

)

[2025-06-24 20:25:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767303
            [orderIds] => []
        )

)

[2025-06-24 20:25:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767314
            [orderIds] => []
        )

)

[2025-06-24 20:25:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767325
            [orderIds] => []
        )

)

[2025-06-24 20:25:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767336
            [orderIds] => []
        )

)

[2025-06-24 20:25:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767347
            [orderIds] => []
        )

)

[2025-06-24 20:25:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767358
            [orderIds] => []
        )

)

[2025-06-24 20:26:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767369
            [orderIds] => []
        )

)

[2025-06-24 20:26:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767380
            [orderIds] => []
        )

)

[2025-06-24 20:26:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767391
            [orderIds] => []
        )

)

[2025-06-24 20:26:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767402
            [orderIds] => []
        )

)

[2025-06-24 20:26:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767413
            [orderIds] => []
        )

)

[2025-06-24 20:27:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767424
            [orderIds] => []
        )

)

[2025-06-24 20:27:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767435
            [orderIds] => []
        )

)

[2025-06-24 20:27:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 20:27:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 20:27:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 20:27:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 20:27:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767446
            [orderIds] => []
        )

)

[2025-06-24 20:27:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767457
            [orderIds] => []
        )

)

[2025-06-24 20:27:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767468
            [orderIds] => []
        )

)

[2025-06-24 20:27:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767479
            [orderIds] => []
        )

)

[2025-06-24 20:28:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767490
            [orderIds] => []
        )

)

[2025-06-24 20:28:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767501
            [orderIds] => []
        )

)

[2025-06-24 20:28:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767512
            [orderIds] => []
        )

)

[2025-06-24 20:28:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767523
            [orderIds] => []
        )

)

[2025-06-24 20:28:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767534
            [orderIds] => []
        )

)

[2025-06-24 20:29:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767545
            [orderIds] => []
        )

)

[2025-06-24 20:29:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767556
            [orderIds] => []
        )

)

[2025-06-24 20:29:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767567
            [orderIds] => []
        )

)

[2025-06-24 20:29:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767578
            [orderIds] => []
        )

)

[2025-06-24 20:29:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767589
            [orderIds] => []
        )

)

[2025-06-24 20:30:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767600
            [orderIds] => []
        )

)

[2025-06-24 20:30:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767611
            [orderIds] => []
        )

)

[2025-06-24 20:30:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767622
            [orderIds] => []
        )

)

[2025-06-24 20:30:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767633
            [orderIds] => []
        )

)

[2025-06-24 20:30:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767644
            [orderIds] => []
        )

)

[2025-06-24 20:30:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767655
            [orderIds] => []
        )

)

[2025-06-24 20:31:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767666
            [orderIds] => []
        )

)

[2025-06-24 20:31:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767677
            [orderIds] => []
        )

)

[2025-06-24 20:31:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767688
            [orderIds] => []
        )

)

[2025-06-24 20:31:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767699
            [orderIds] => []
        )

)

[2025-06-24 20:31:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767710
            [orderIds] => []
        )

)

[2025-06-24 20:32:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767721
            [orderIds] => []
        )

)

[2025-06-24 20:32:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767732
            [orderIds] => []
        )

)

[2025-06-24 20:32:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767743
            [orderIds] => []
        )

)

[2025-06-24 20:32:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767754
            [orderIds] => []
        )

)

[2025-06-24 20:32:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767765
            [orderIds] => []
        )

)

[2025-06-24 20:32:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767776
            [orderIds] => []
        )

)

[2025-06-24 20:33:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767787
            [orderIds] => []
        )

)

[2025-06-24 20:33:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767798
            [orderIds] => []
        )

)

[2025-06-24 20:33:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767809
            [orderIds] => []
        )

)

[2025-06-24 20:33:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767820
            [orderIds] => []
        )

)

[2025-06-24 20:33:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767831
            [orderIds] => []
        )

)

[2025-06-24 20:34:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767842
            [orderIds] => []
        )

)

[2025-06-24 20:34:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767853
            [orderIds] => []
        )

)

[2025-06-24 20:34:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767864
            [orderIds] => []
        )

)

[2025-06-24 20:34:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767875
            [orderIds] => []
        )

)

[2025-06-24 20:34:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767886
            [orderIds] => []
        )

)

[2025-06-24 20:34:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767897
            [orderIds] => []
        )

)

[2025-06-24 20:35:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767908
            [orderIds] => []
        )

)

[2025-06-24 20:35:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767919
            [orderIds] => []
        )

)

[2025-06-24 20:35:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767930
            [orderIds] => []
        )

)

[2025-06-24 20:35:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767941
            [orderIds] => []
        )

)

[2025-06-24 20:35:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767952
            [orderIds] => []
        )

)

[2025-06-24 20:36:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767963
            [orderIds] => []
        )

)

[2025-06-24 20:36:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767974
            [orderIds] => []
        )

)

[2025-06-24 20:36:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767985
            [orderIds] => []
        )

)

[2025-06-24 20:36:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750767996
            [orderIds] => []
        )

)

[2025-06-24 20:36:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768007
            [orderIds] => []
        )

)

[2025-06-24 20:36:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768018
            [orderIds] => []
        )

)

[2025-06-24 20:37:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768029
            [orderIds] => []
        )

)

[2025-06-24 20:37:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768040
            [orderIds] => []
        )

)

[2025-06-24 20:37:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 20:37:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 20:37:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 20:37:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 20:37:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768051
            [orderIds] => []
        )

)

[2025-06-24 20:37:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768062
            [orderIds] => []
        )

)

[2025-06-24 20:37:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768073
            [orderIds] => []
        )

)

[2025-06-24 20:38:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768084
            [orderIds] => []
        )

)

[2025-06-24 20:38:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768095
            [orderIds] => []
        )

)

[2025-06-24 20:38:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768106
            [orderIds] => []
        )

)

[2025-06-24 20:38:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1750682316
            [orderIds] => []
        )

)

[2025-06-24 20:38:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1750163916
            [orderIds] => []
        )

)

[2025-06-24 20:38:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1750163916
            [orderIds] => []
        )

)

[2025-06-24 20:38:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-24 20:38:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768117
            [orderIds] => []
        )

)

[2025-06-24 20:38:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-24 20:38:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-24 20:38:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-24 20:38:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-24 20:38:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768128
            [orderIds] => []
        )

)

[2025-06-24 20:38:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768139
            [orderIds] => []
        )

)

[2025-06-24 20:39:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768150
            [orderIds] => []
        )

)

[2025-06-24 20:39:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768161
            [orderIds] => []
        )

)

[2025-06-24 20:39:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768172
            [orderIds] => []
        )

)

[2025-06-24 20:39:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768183
            [orderIds] => []
        )

)

[2025-06-24 20:39:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768194
            [orderIds] => []
        )

)

[2025-06-24 20:40:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768205
            [orderIds] => []
        )

)

[2025-06-24 20:40:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768216
            [orderIds] => []
        )

)

[2025-06-24 20:40:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768227
            [orderIds] => []
        )

)

[2025-06-24 20:40:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768238
            [orderIds] => []
        )

)

[2025-06-24 20:40:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768249
            [orderIds] => []
        )

)

[2025-06-24 20:41:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768260
            [orderIds] => []
        )

)

[2025-06-24 20:41:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768271
            [orderIds] => []
        )

)

[2025-06-24 20:41:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768282
            [orderIds] => []
        )

)

[2025-06-24 20:41:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768293
            [orderIds] => []
        )

)

[2025-06-24 20:41:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768304
            [orderIds] => []
        )

)

[2025-06-24 20:41:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768315
            [orderIds] => []
        )

)

[2025-06-24 20:42:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768326
            [orderIds] => []
        )

)

[2025-06-24 20:42:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768337
            [orderIds] => []
        )

)

[2025-06-24 20:42:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768348
            [orderIds] => []
        )

)

[2025-06-24 20:42:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768359
            [orderIds] => []
        )

)

[2025-06-24 20:42:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768370
            [orderIds] => []
        )

)

[2025-06-24 20:43:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768381
            [orderIds] => []
        )

)

[2025-06-24 20:43:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768392
            [orderIds] => []
        )

)

[2025-06-24 20:43:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768403
            [orderIds] => []
        )

)

[2025-06-24 20:43:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768414
            [orderIds] => []
        )

)

[2025-06-24 20:43:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768425
            [orderIds] => []
        )

)

[2025-06-24 20:43:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768436
            [orderIds] => []
        )

)

[2025-06-24 20:44:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768447
            [orderIds] => []
        )

)

[2025-06-24 20:44:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768458
            [orderIds] => []
        )

)

[2025-06-24 20:44:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768469
            [orderIds] => []
        )

)

[2025-06-24 20:44:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768480
            [orderIds] => []
        )

)

[2025-06-24 20:44:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768491
            [orderIds] => []
        )

)

[2025-06-24 20:45:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768502
            [orderIds] => []
        )

)

[2025-06-24 20:45:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768513
            [orderIds] => []
        )

)

[2025-06-24 20:45:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768524
            [orderIds] => []
        )

)

[2025-06-24 20:45:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768535
            [orderIds] => []
        )

)

[2025-06-24 20:45:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768546
            [orderIds] => []
        )

)

[2025-06-24 20:45:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768557
            [orderIds] => []
        )

)

[2025-06-24 20:46:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768568
            [orderIds] => []
        )

)

[2025-06-24 20:46:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768579
            [orderIds] => []
        )

)

[2025-06-24 20:46:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768590
            [orderIds] => []
        )

)

[2025-06-24 20:46:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768601
            [orderIds] => []
        )

)

[2025-06-24 20:46:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768612
            [orderIds] => []
        )

)

[2025-06-24 20:47:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768623
            [orderIds] => []
        )

)

[2025-06-24 20:47:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768634
            [orderIds] => []
        )

)

[2025-06-24 20:47:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768645
            [orderIds] => []
        )

)

[2025-06-24 20:47:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 20:47:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 20:47:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 20:47:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 20:47:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768656
            [orderIds] => []
        )

)

[2025-06-24 20:47:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768667
            [orderIds] => []
        )

)

[2025-06-24 20:47:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768678
            [orderIds] => []
        )

)

[2025-06-24 20:48:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768689
            [orderIds] => []
        )

)

[2025-06-24 20:48:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768700
            [orderIds] => []
        )

)

[2025-06-24 20:48:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768711
            [orderIds] => []
        )

)

[2025-06-24 20:48:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768722
            [orderIds] => []
        )

)

[2025-06-24 20:48:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768733
            [orderIds] => []
        )

)

[2025-06-24 20:49:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768744
            [orderIds] => []
        )

)

[2025-06-24 20:49:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768755
            [orderIds] => []
        )

)

[2025-06-24 20:49:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768766
            [orderIds] => []
        )

)

[2025-06-24 20:49:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768777
            [orderIds] => []
        )

)

[2025-06-24 20:49:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768788
            [orderIds] => []
        )

)

[2025-06-24 20:49:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768799
            [orderIds] => []
        )

)

[2025-06-24 20:50:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768810
            [orderIds] => []
        )

)

[2025-06-24 20:50:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768821
            [orderIds] => []
        )

)

[2025-06-24 20:50:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768832
            [orderIds] => []
        )

)

[2025-06-24 20:50:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768843
            [orderIds] => []
        )

)

[2025-06-24 20:50:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768854
            [orderIds] => []
        )

)

[2025-06-24 20:51:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768865
            [orderIds] => []
        )

)

[2025-06-24 20:51:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768876
            [orderIds] => []
        )

)

[2025-06-24 20:51:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768887
            [orderIds] => []
        )

)

[2025-06-24 20:51:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768898
            [orderIds] => []
        )

)

[2025-06-24 20:51:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768909
            [orderIds] => []
        )

)

[2025-06-24 20:52:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768920
            [orderIds] => []
        )

)

[2025-06-24 20:52:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768931
            [orderIds] => []
        )

)

[2025-06-24 20:52:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768942
            [orderIds] => []
        )

)

[2025-06-24 20:52:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768953
            [orderIds] => []
        )

)

[2025-06-24 20:52:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768964
            [orderIds] => []
        )

)

[2025-06-24 20:52:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768975
            [orderIds] => []
        )

)

[2025-06-24 20:53:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768986
            [orderIds] => []
        )

)

[2025-06-24 20:53:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750768997
            [orderIds] => []
        )

)

[2025-06-24 20:53:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769008
            [orderIds] => []
        )

)

[2025-06-24 20:53:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769019
            [orderIds] => []
        )

)

[2025-06-24 20:53:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769030
            [orderIds] => []
        )

)

[2025-06-24 20:54:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769041
            [orderIds] => []
        )

)

[2025-06-24 20:54:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769052
            [orderIds] => []
        )

)

[2025-06-24 20:54:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769063
            [orderIds] => []
        )

)

[2025-06-24 20:54:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769074
            [orderIds] => []
        )

)

[2025-06-24 20:54:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769085
            [orderIds] => []
        )

)

[2025-06-24 20:54:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769096
            [orderIds] => []
        )

)

[2025-06-24 20:55:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769107
            [orderIds] => []
        )

)

[2025-06-24 20:55:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769118
            [orderIds] => []
        )

)

[2025-06-24 20:55:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769129
            [orderIds] => []
        )

)

[2025-06-24 20:55:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769140
            [orderIds] => []
        )

)

[2025-06-24 20:55:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769151
            [orderIds] => []
        )

)

[2025-06-24 20:56:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769162
            [orderIds] => []
        )

)

[2025-06-24 20:56:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769173
            [orderIds] => []
        )

)

[2025-06-24 20:56:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769184
            [orderIds] => []
        )

)

[2025-06-24 20:56:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769195
            [orderIds] => []
        )

)

[2025-06-24 20:56:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769206
            [orderIds] => []
        )

)

[2025-06-24 20:56:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769217
            [orderIds] => []
        )

)

[2025-06-24 20:57:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769228
            [orderIds] => []
        )

)

[2025-06-24 20:57:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769239
            [orderIds] => []
        )

)

[2025-06-24 20:57:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 20:57:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 20:57:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 20:57:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 20:57:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769250
            [orderIds] => []
        )

)

[2025-06-24 20:57:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769261
            [orderIds] => []
        )

)

[2025-06-24 20:57:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769272
            [orderIds] => []
        )

)

[2025-06-24 20:58:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769283
            [orderIds] => []
        )

)

[2025-06-24 20:58:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769294
            [orderIds] => []
        )

)

[2025-06-24 20:58:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769305
            [orderIds] => []
        )

)

[2025-06-24 20:58:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769316
            [orderIds] => []
        )

)

[2025-06-24 20:58:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769327
            [orderIds] => []
        )

)

[2025-06-24 20:58:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769338
            [orderIds] => []
        )

)

[2025-06-24 20:59:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769349
            [orderIds] => []
        )

)

[2025-06-24 20:59:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769360
            [orderIds] => []
        )

)

[2025-06-24 20:59:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769371
            [orderIds] => []
        )

)

[2025-06-24 20:59:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769382
            [orderIds] => []
        )

)

[2025-06-24 20:59:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769393
            [orderIds] => []
        )

)

[2025-06-24 21:00:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769404
            [orderIds] => []
        )

)

[2025-06-24 21:00:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769415
            [orderIds] => []
        )

)

[2025-06-24 21:00:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769426
            [orderIds] => []
        )

)

[2025-06-24 21:00:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769437
            [orderIds] => []
        )

)

[2025-06-24 21:00:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769448
            [orderIds] => []
        )

)

[2025-06-24 21:00:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769459
            [orderIds] => []
        )

)

[2025-06-24 21:01:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769470
            [orderIds] => []
        )

)

[2025-06-24 21:01:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769481
            [orderIds] => []
        )

)

[2025-06-24 21:01:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769492
            [orderIds] => []
        )

)

[2025-06-24 21:01:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769503
            [orderIds] => []
        )

)

[2025-06-24 21:01:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769514
            [orderIds] => []
        )

)

[2025-06-24 21:02:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769525
            [orderIds] => []
        )

)

[2025-06-24 21:02:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769536
            [orderIds] => []
        )

)

[2025-06-24 21:02:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769547
            [orderIds] => []
        )

)

[2025-06-24 21:02:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769558
            [orderIds] => []
        )

)

[2025-06-24 21:02:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769569
            [orderIds] => []
        )

)

[2025-06-24 21:03:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769580
            [orderIds] => []
        )

)

[2025-06-24 21:03:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769591
            [orderIds] => []
        )

)

[2025-06-24 21:03:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769602
            [orderIds] => []
        )

)

[2025-06-24 21:03:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769613
            [orderIds] => []
        )

)

[2025-06-24 21:03:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769624
            [orderIds] => []
        )

)

[2025-06-24 21:03:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769635
            [orderIds] => []
        )

)

[2025-06-24 21:04:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769646
            [orderIds] => []
        )

)

[2025-06-24 21:04:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769657
            [orderIds] => []
        )

)

[2025-06-24 21:04:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769668
            [orderIds] => []
        )

)

[2025-06-24 21:04:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769679
            [orderIds] => []
        )

)

[2025-06-24 21:04:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769690
            [orderIds] => []
        )

)

[2025-06-24 21:05:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769701
            [orderIds] => []
        )

)

[2025-06-24 21:05:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769712
            [orderIds] => []
        )

)

[2025-06-24 21:05:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769723
            [orderIds] => []
        )

)

[2025-06-24 21:05:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769734
            [orderIds] => []
        )

)

[2025-06-24 21:05:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769745
            [orderIds] => []
        )

)

[2025-06-24 21:05:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769756
            [orderIds] => []
        )

)

[2025-06-24 21:06:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769767
            [orderIds] => []
        )

)

[2025-06-24 21:06:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769778
            [orderIds] => []
        )

)

[2025-06-24 21:06:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769789
            [orderIds] => []
        )

)

[2025-06-24 21:06:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769800
            [orderIds] => []
        )

)

[2025-06-24 21:06:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769811
            [orderIds] => []
        )

)

[2025-06-24 21:07:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769822
            [orderIds] => []
        )

)

[2025-06-24 21:07:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769833
            [orderIds] => []
        )

)

[2025-06-24 21:07:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769844
            [orderIds] => []
        )

)

[2025-06-24 21:07:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 21:07:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 21:07:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 21:07:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 21:07:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769855
            [orderIds] => []
        )

)

[2025-06-24 21:07:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769866
            [orderIds] => []
        )

)

[2025-06-24 21:07:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769877
            [orderIds] => []
        )

)

[2025-06-24 21:08:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769888
            [orderIds] => []
        )

)

[2025-06-24 21:08:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769899
            [orderIds] => []
        )

)

[2025-06-24 21:08:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769910
            [orderIds] => []
        )

)

[2025-06-24 21:08:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1750684117
            [orderIds] => []
        )

)

[2025-06-24 21:08:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1750165717
            [orderIds] => []
        )

)

[2025-06-24 21:08:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1750165717
            [orderIds] => []
        )

)

[2025-06-24 21:08:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-24 21:08:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-24 21:08:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-24 21:08:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-24 21:08:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-24 21:08:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769921
            [orderIds] => []
        )

)

[2025-06-24 21:08:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769932
            [orderIds] => []
        )

)

[2025-06-24 21:09:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769943
            [orderIds] => []
        )

)

[2025-06-24 21:09:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769954
            [orderIds] => []
        )

)

[2025-06-24 21:09:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769965
            [orderIds] => []
        )

)

[2025-06-24 21:09:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769976
            [orderIds] => []
        )

)

[2025-06-24 21:09:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769987
            [orderIds] => []
        )

)

[2025-06-24 21:09:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750769998
            [orderIds] => []
        )

)

[2025-06-24 21:10:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770009
            [orderIds] => []
        )

)

[2025-06-24 21:10:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770020
            [orderIds] => []
        )

)

[2025-06-24 21:10:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770031
            [orderIds] => []
        )

)

[2025-06-24 21:10:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770042
            [orderIds] => []
        )

)

[2025-06-24 21:10:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770053
            [orderIds] => []
        )

)

[2025-06-24 21:11:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770064
            [orderIds] => []
        )

)

[2025-06-24 21:11:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770075
            [orderIds] => []
        )

)

[2025-06-24 21:11:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770086
            [orderIds] => []
        )

)

[2025-06-24 21:11:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770097
            [orderIds] => []
        )

)

[2025-06-24 21:11:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770108
            [orderIds] => []
        )

)

[2025-06-24 21:11:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770119
            [orderIds] => []
        )

)

[2025-06-24 21:12:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770130
            [orderIds] => []
        )

)

[2025-06-24 21:12:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770141
            [orderIds] => []
        )

)

[2025-06-24 21:12:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770152
            [orderIds] => []
        )

)

[2025-06-24 21:12:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770163
            [orderIds] => []
        )

)

[2025-06-24 21:12:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770174
            [orderIds] => []
        )

)

[2025-06-24 21:13:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770185
            [orderIds] => []
        )

)

[2025-06-24 21:13:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770196
            [orderIds] => []
        )

)

[2025-06-24 21:13:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770207
            [orderIds] => []
        )

)

[2025-06-24 21:13:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770218
            [orderIds] => []
        )

)

[2025-06-24 21:13:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770229
            [orderIds] => []
        )

)

[2025-06-24 21:14:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770240
            [orderIds] => []
        )

)

[2025-06-24 21:14:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770251
            [orderIds] => []
        )

)

[2025-06-24 21:14:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770262
            [orderIds] => []
        )

)

[2025-06-24 21:14:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770273
            [orderIds] => []
        )

)

[2025-06-24 21:14:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770284
            [orderIds] => []
        )

)

[2025-06-24 21:14:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770295
            [orderIds] => []
        )

)

[2025-06-24 21:15:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770306
            [orderIds] => []
        )

)

[2025-06-24 21:15:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770317
            [orderIds] => []
        )

)

[2025-06-24 21:15:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770328
            [orderIds] => []
        )

)

[2025-06-24 21:15:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770339
            [orderIds] => []
        )

)

[2025-06-24 21:15:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770350
            [orderIds] => []
        )

)

[2025-06-24 21:16:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770361
            [orderIds] => []
        )

)

[2025-06-24 21:16:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770372
            [orderIds] => []
        )

)

[2025-06-24 21:16:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770383
            [orderIds] => []
        )

)

[2025-06-24 21:16:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770394
            [orderIds] => []
        )

)

[2025-06-24 21:16:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770405
            [orderIds] => []
        )

)

[2025-06-24 21:16:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770416
            [orderIds] => []
        )

)

[2025-06-24 21:17:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770427
            [orderIds] => []
        )

)

[2025-06-24 21:17:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770438
            [orderIds] => []
        )

)

[2025-06-24 21:17:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770449
            [orderIds] => []
        )

)

[2025-06-24 21:17:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 21:17:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 21:17:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 21:17:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 21:17:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770460
            [orderIds] => []
        )

)

[2025-06-24 21:17:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770471
            [orderIds] => []
        )

)

[2025-06-24 21:18:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770482
            [orderIds] => []
        )

)

[2025-06-24 21:18:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770493
            [orderIds] => []
        )

)

[2025-06-24 21:18:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770504
            [orderIds] => []
        )

)

[2025-06-24 21:18:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770515
            [orderIds] => []
        )

)

[2025-06-24 21:18:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770526
            [orderIds] => []
        )

)

[2025-06-24 21:18:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770537
            [orderIds] => []
        )

)

[2025-06-24 21:19:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770548
            [orderIds] => []
        )

)

[2025-06-24 21:19:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770559
            [orderIds] => []
        )

)

[2025-06-24 21:19:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770570
            [orderIds] => []
        )

)

[2025-06-24 21:19:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770581
            [orderIds] => []
        )

)

[2025-06-24 21:19:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770592
            [orderIds] => []
        )

)

[2025-06-24 21:20:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770603
            [orderIds] => []
        )

)

[2025-06-24 21:20:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770614
            [orderIds] => []
        )

)

[2025-06-24 21:20:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770625
            [orderIds] => []
        )

)

[2025-06-24 21:20:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770636
            [orderIds] => []
        )

)

[2025-06-24 21:20:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770647
            [orderIds] => []
        )

)

[2025-06-24 21:20:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770658
            [orderIds] => []
        )

)

[2025-06-24 21:21:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770669
            [orderIds] => []
        )

)

[2025-06-24 21:21:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770680
            [orderIds] => []
        )

)

[2025-06-24 21:21:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770691
            [orderIds] => []
        )

)

[2025-06-24 21:21:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770702
            [orderIds] => []
        )

)

[2025-06-24 21:21:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770713
            [orderIds] => []
        )

)

[2025-06-24 21:22:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770724
            [orderIds] => []
        )

)

[2025-06-24 21:22:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770735
            [orderIds] => []
        )

)

[2025-06-24 21:22:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770746
            [orderIds] => []
        )

)

[2025-06-24 21:22:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770757
            [orderIds] => []
        )

)

[2025-06-24 21:22:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770768
            [orderIds] => []
        )

)

[2025-06-24 21:22:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770779
            [orderIds] => []
        )

)

[2025-06-24 21:23:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770790
            [orderIds] => []
        )

)

[2025-06-24 21:23:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770801
            [orderIds] => []
        )

)

[2025-06-24 21:23:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770812
            [orderIds] => []
        )

)

[2025-06-24 21:23:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770823
            [orderIds] => []
        )

)

[2025-06-24 21:23:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770834
            [orderIds] => []
        )

)

[2025-06-24 21:24:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770845
            [orderIds] => []
        )

)

[2025-06-24 21:24:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770856
            [orderIds] => []
        )

)

[2025-06-24 21:24:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770867
            [orderIds] => []
        )

)

[2025-06-24 21:24:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770878
            [orderIds] => []
        )

)

[2025-06-24 21:24:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770889
            [orderIds] => []
        )

)

[2025-06-24 21:25:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770900
            [orderIds] => []
        )

)

[2025-06-24 21:25:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770911
            [orderIds] => []
        )

)

[2025-06-24 21:25:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770922
            [orderIds] => []
        )

)

[2025-06-24 21:25:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770933
            [orderIds] => []
        )

)

[2025-06-24 21:25:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770944
            [orderIds] => []
        )

)

[2025-06-24 21:25:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770955
            [orderIds] => []
        )

)

[2025-06-24 21:26:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770966
            [orderIds] => []
        )

)

[2025-06-24 21:26:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770977
            [orderIds] => []
        )

)

[2025-06-24 21:26:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770988
            [orderIds] => []
        )

)

[2025-06-24 21:26:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750770999
            [orderIds] => []
        )

)

[2025-06-24 21:26:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771010
            [orderIds] => []
        )

)

[2025-06-24 21:27:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771021
            [orderIds] => []
        )

)

[2025-06-24 21:27:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771032
            [orderIds] => []
        )

)

[2025-06-24 21:27:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771043
            [orderIds] => []
        )

)

[2025-06-24 21:27:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 21:27:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 21:27:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 21:27:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 21:27:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771054
            [orderIds] => []
        )

)

[2025-06-24 21:27:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771065
            [orderIds] => []
        )

)

[2025-06-24 21:27:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771076
            [orderIds] => []
        )

)

[2025-06-24 21:28:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771087
            [orderIds] => []
        )

)

[2025-06-24 21:28:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771098
            [orderIds] => []
        )

)

[2025-06-24 21:28:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771109
            [orderIds] => []
        )

)

[2025-06-24 21:28:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771120
            [orderIds] => []
        )

)

[2025-06-24 21:28:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771131
            [orderIds] => []
        )

)

[2025-06-24 21:29:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771142
            [orderIds] => []
        )

)

[2025-06-24 21:29:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771153
            [orderIds] => []
        )

)

[2025-06-24 21:29:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771164
            [orderIds] => []
        )

)

[2025-06-24 21:29:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771175
            [orderIds] => []
        )

)

[2025-06-24 21:29:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771186
            [orderIds] => []
        )

)

[2025-06-24 21:29:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771197
            [orderIds] => []
        )

)

[2025-06-24 21:30:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771208
            [orderIds] => []
        )

)

[2025-06-24 21:30:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771219
            [orderIds] => []
        )

)

[2025-06-24 21:30:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771230
            [orderIds] => []
        )

)

[2025-06-24 21:30:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771241
            [orderIds] => []
        )

)

[2025-06-24 21:30:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771252
            [orderIds] => []
        )

)

[2025-06-24 21:31:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771263
            [orderIds] => []
        )

)

[2025-06-24 21:31:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771274
            [orderIds] => []
        )

)

[2025-06-24 21:31:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771285
            [orderIds] => []
        )

)

[2025-06-24 21:31:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771296
            [orderIds] => []
        )

)

[2025-06-24 21:31:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771307
            [orderIds] => []
        )

)

[2025-06-24 21:31:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771318
            [orderIds] => []
        )

)

[2025-06-24 21:32:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771329
            [orderIds] => []
        )

)

[2025-06-24 21:32:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771340
            [orderIds] => []
        )

)

[2025-06-24 21:32:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771351
            [orderIds] => []
        )

)

[2025-06-24 21:32:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771362
            [orderIds] => []
        )

)

[2025-06-24 21:32:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771373
            [orderIds] => []
        )

)

[2025-06-24 21:33:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771384
            [orderIds] => []
        )

)

[2025-06-24 21:33:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771395
            [orderIds] => []
        )

)

[2025-06-24 21:33:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771406
            [orderIds] => []
        )

)

[2025-06-24 21:33:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771417
            [orderIds] => []
        )

)

[2025-06-24 21:33:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771428
            [orderIds] => []
        )

)

[2025-06-24 21:33:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771439
            [orderIds] => []
        )

)

[2025-06-24 21:34:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771450
            [orderIds] => []
        )

)

[2025-06-24 21:34:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771461
            [orderIds] => []
        )

)

[2025-06-24 21:34:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771472
            [orderIds] => []
        )

)

[2025-06-24 21:34:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771483
            [orderIds] => []
        )

)

[2025-06-24 21:34:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771494
            [orderIds] => []
        )

)

[2025-06-24 21:35:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771505
            [orderIds] => []
        )

)

[2025-06-24 21:35:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771516
            [orderIds] => []
        )

)

[2025-06-24 21:35:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771527
            [orderIds] => []
        )

)

[2025-06-24 21:35:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771538
            [orderIds] => []
        )

)

[2025-06-24 21:35:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771549
            [orderIds] => []
        )

)

[2025-06-24 21:36:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771560
            [orderIds] => []
        )

)

[2025-06-24 21:36:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771571
            [orderIds] => []
        )

)

[2025-06-24 21:36:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771582
            [orderIds] => []
        )

)

[2025-06-24 21:36:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771593
            [orderIds] => []
        )

)

[2025-06-24 21:36:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771604
            [orderIds] => []
        )

)

[2025-06-24 21:36:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771615
            [orderIds] => []
        )

)

[2025-06-24 21:37:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771626
            [orderIds] => []
        )

)

[2025-06-24 21:37:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771637
            [orderIds] => []
        )

)

[2025-06-24 21:37:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771648
            [orderIds] => []
        )

)

[2025-06-24 21:37:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 21:37:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 21:37:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 21:37:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 21:37:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771659
            [orderIds] => []
        )

)

[2025-06-24 21:37:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771670
            [orderIds] => []
        )

)

[2025-06-24 21:38:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771681
            [orderIds] => []
        )

)

[2025-06-24 21:38:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771692
            [orderIds] => []
        )

)

[2025-06-24 21:38:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771703
            [orderIds] => []
        )

)

[2025-06-24 21:38:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771714
            [orderIds] => []
        )

)

[2025-06-24 21:38:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1750685918
            [orderIds] => []
        )

)

[2025-06-24 21:38:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1750167518
            [orderIds] => []
        )

)

[2025-06-24 21:38:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1750167518
            [orderIds] => []
        )

)

[2025-06-24 21:38:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-24 21:38:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-24 21:38:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-24 21:38:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-24 21:38:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-24 21:38:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771725
            [orderIds] => []
        )

)

[2025-06-24 21:38:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771736
            [orderIds] => []
        )

)

[2025-06-24 21:39:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771747
            [orderIds] => []
        )

)

[2025-06-24 21:39:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771758
            [orderIds] => []
        )

)

[2025-06-24 21:39:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771769
            [orderIds] => []
        )

)

[2025-06-24 21:39:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771780
            [orderIds] => []
        )

)

[2025-06-24 21:39:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771791
            [orderIds] => []
        )

)

[2025-06-24 21:40:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771802
            [orderIds] => []
        )

)

[2025-06-24 21:40:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771813
            [orderIds] => []
        )

)

[2025-06-24 21:40:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771824
            [orderIds] => []
        )

)

[2025-06-24 21:40:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771835
            [orderIds] => []
        )

)

[2025-06-24 21:40:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771846
            [orderIds] => []
        )

)

[2025-06-24 21:40:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771857
            [orderIds] => []
        )

)

[2025-06-24 21:41:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771868
            [orderIds] => []
        )

)

[2025-06-24 21:41:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771879
            [orderIds] => []
        )

)

[2025-06-24 21:41:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771890
            [orderIds] => []
        )

)

[2025-06-24 21:41:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771901
            [orderIds] => []
        )

)

[2025-06-24 21:41:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771912
            [orderIds] => []
        )

)

[2025-06-24 21:42:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771923
            [orderIds] => []
        )

)

[2025-06-24 21:42:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771934
            [orderIds] => []
        )

)

[2025-06-24 21:42:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771945
            [orderIds] => []
        )

)

[2025-06-24 21:42:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771956
            [orderIds] => []
        )

)

[2025-06-24 21:42:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771967
            [orderIds] => []
        )

)

[2025-06-24 21:42:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771978
            [orderIds] => []
        )

)

[2025-06-24 21:43:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750771989
            [orderIds] => []
        )

)

[2025-06-24 21:43:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772000
            [orderIds] => []
        )

)

[2025-06-24 21:43:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772011
            [orderIds] => []
        )

)

[2025-06-24 21:43:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772022
            [orderIds] => []
        )

)

[2025-06-24 21:43:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772033
            [orderIds] => []
        )

)

[2025-06-24 21:44:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772044
            [orderIds] => []
        )

)

[2025-06-24 21:44:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772055
            [orderIds] => []
        )

)

[2025-06-24 21:44:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772066
            [orderIds] => []
        )

)

[2025-06-24 21:44:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772077
            [orderIds] => []
        )

)

[2025-06-24 21:44:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772088
            [orderIds] => []
        )

)

[2025-06-24 21:44:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772099
            [orderIds] => []
        )

)

[2025-06-24 21:45:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772110
            [orderIds] => []
        )

)

[2025-06-24 21:45:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772121
            [orderIds] => []
        )

)

[2025-06-24 21:45:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772132
            [orderIds] => []
        )

)

[2025-06-24 21:45:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772143
            [orderIds] => []
        )

)

[2025-06-24 21:45:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772154
            [orderIds] => []
        )

)

[2025-06-24 21:46:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772165
            [orderIds] => []
        )

)

[2025-06-24 21:46:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772176
            [orderIds] => []
        )

)

[2025-06-24 21:46:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772187
            [orderIds] => []
        )

)

[2025-06-24 21:46:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772198
            [orderIds] => []
        )

)

[2025-06-24 21:46:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772209
            [orderIds] => []
        )

)

[2025-06-24 21:47:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772220
            [orderIds] => []
        )

)

[2025-06-24 21:47:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772231
            [orderIds] => []
        )

)

[2025-06-24 21:47:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772242
            [orderIds] => []
        )

)

[2025-06-24 21:47:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772253
            [orderIds] => []
        )

)

[2025-06-24 21:47:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 21:47:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 21:47:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 21:47:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 21:47:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772264
            [orderIds] => []
        )

)

[2025-06-24 21:47:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772275
            [orderIds] => []
        )

)

[2025-06-24 21:48:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772286
            [orderIds] => []
        )

)

[2025-06-24 21:48:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772297
            [orderIds] => []
        )

)

[2025-06-24 21:48:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772308
            [orderIds] => []
        )

)

[2025-06-24 21:48:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772319
            [orderIds] => []
        )

)

[2025-06-24 21:48:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772330
            [orderIds] => []
        )

)

[2025-06-24 21:49:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772341
            [orderIds] => []
        )

)

[2025-06-24 21:49:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772352
            [orderIds] => []
        )

)

[2025-06-24 21:49:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772363
            [orderIds] => []
        )

)

[2025-06-24 21:49:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772374
            [orderIds] => []
        )

)

[2025-06-24 21:49:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772385
            [orderIds] => []
        )

)

[2025-06-24 21:49:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772396
            [orderIds] => []
        )

)

[2025-06-24 21:50:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772407
            [orderIds] => []
        )

)

[2025-06-24 21:50:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772418
            [orderIds] => []
        )

)

[2025-06-24 21:50:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772429
            [orderIds] => []
        )

)

[2025-06-24 21:50:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772440
            [orderIds] => []
        )

)

[2025-06-24 21:50:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772451
            [orderIds] => []
        )

)

[2025-06-24 21:51:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772462
            [orderIds] => []
        )

)

[2025-06-24 21:51:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772473
            [orderIds] => []
        )

)

[2025-06-24 21:51:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772484
            [orderIds] => []
        )

)

[2025-06-24 21:51:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772495
            [orderIds] => []
        )

)

[2025-06-24 21:51:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772506
            [orderIds] => []
        )

)

[2025-06-24 21:51:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772517
            [orderIds] => []
        )

)

[2025-06-24 21:52:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772528
            [orderIds] => []
        )

)

[2025-06-24 21:52:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772539
            [orderIds] => []
        )

)

[2025-06-24 21:52:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772550
            [orderIds] => []
        )

)

[2025-06-24 21:52:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772561
            [orderIds] => []
        )

)

[2025-06-24 21:52:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772572
            [orderIds] => []
        )

)

[2025-06-24 21:53:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772583
            [orderIds] => []
        )

)

[2025-06-24 21:53:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772594
            [orderIds] => []
        )

)

[2025-06-24 21:53:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772605
            [orderIds] => []
        )

)

[2025-06-24 21:53:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772616
            [orderIds] => []
        )

)

[2025-06-24 21:53:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772627
            [orderIds] => []
        )

)

[2025-06-24 21:53:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772638
            [orderIds] => []
        )

)

[2025-06-24 21:54:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772649
            [orderIds] => []
        )

)

[2025-06-24 21:54:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772660
            [orderIds] => []
        )

)

[2025-06-24 21:54:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772671
            [orderIds] => []
        )

)

[2025-06-24 21:54:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772682
            [orderIds] => []
        )

)

[2025-06-24 21:54:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772693
            [orderIds] => []
        )

)

[2025-06-24 21:55:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772704
            [orderIds] => []
        )

)

[2025-06-24 21:55:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772715
            [orderIds] => []
        )

)

[2025-06-24 21:55:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772726
            [orderIds] => []
        )

)

[2025-06-24 21:55:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772737
            [orderIds] => []
        )

)

[2025-06-24 21:55:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772748
            [orderIds] => []
        )

)

[2025-06-24 21:55:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772759
            [orderIds] => []
        )

)

[2025-06-24 21:56:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772770
            [orderIds] => []
        )

)

[2025-06-24 21:56:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772781
            [orderIds] => []
        )

)

[2025-06-24 21:56:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772792
            [orderIds] => []
        )

)

[2025-06-24 21:56:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772803
            [orderIds] => []
        )

)

[2025-06-24 21:56:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772814
            [orderIds] => []
        )

)

[2025-06-24 21:57:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772825
            [orderIds] => []
        )

)

[2025-06-24 21:57:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772836
            [orderIds] => []
        )

)

[2025-06-24 21:57:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772847
            [orderIds] => []
        )

)

[2025-06-24 21:57:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 21:57:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 21:57:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 21:57:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 21:57:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772858
            [orderIds] => []
        )

)

[2025-06-24 21:57:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772869
            [orderIds] => []
        )

)

[2025-06-24 21:58:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772880
            [orderIds] => []
        )

)

[2025-06-24 21:58:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772891
            [orderIds] => []
        )

)

[2025-06-24 21:58:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772902
            [orderIds] => []
        )

)

[2025-06-24 21:58:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772913
            [orderIds] => []
        )

)

[2025-06-24 21:58:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772924
            [orderIds] => []
        )

)

[2025-06-24 21:58:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772935
            [orderIds] => []
        )

)

[2025-06-24 21:59:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772946
            [orderIds] => []
        )

)

[2025-06-24 21:59:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772957
            [orderIds] => []
        )

)

[2025-06-24 21:59:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772968
            [orderIds] => []
        )

)

[2025-06-24 21:59:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772979
            [orderIds] => []
        )

)

[2025-06-24 21:59:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750772990
            [orderIds] => []
        )

)

[2025-06-24 22:00:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773001
            [orderIds] => []
        )

)

[2025-06-24 22:00:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773012
            [orderIds] => []
        )

)

[2025-06-24 22:00:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773023
            [orderIds] => []
        )

)

[2025-06-24 22:00:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773034
            [orderIds] => []
        )

)

[2025-06-24 22:00:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773045
            [orderIds] => []
        )

)

[2025-06-24 22:00:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773056
            [orderIds] => []
        )

)

[2025-06-24 22:01:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773067
            [orderIds] => []
        )

)

[2025-06-24 22:01:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773078
            [orderIds] => []
        )

)

[2025-06-24 22:01:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773089
            [orderIds] => []
        )

)

[2025-06-24 22:01:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773100
            [orderIds] => []
        )

)

[2025-06-24 22:01:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773111
            [orderIds] => []
        )

)

[2025-06-24 22:02:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773122
            [orderIds] => []
        )

)

[2025-06-24 22:02:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773133
            [orderIds] => []
        )

)

[2025-06-24 22:02:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773144
            [orderIds] => []
        )

)

[2025-06-24 22:02:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773155
            [orderIds] => []
        )

)

[2025-06-24 22:02:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773166
            [orderIds] => []
        )

)

[2025-06-24 22:02:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773177
            [orderIds] => []
        )

)

[2025-06-24 22:03:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773188
            [orderIds] => []
        )

)

[2025-06-24 22:03:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773199
            [orderIds] => []
        )

)

[2025-06-24 22:03:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773210
            [orderIds] => []
        )

)

[2025-06-24 22:03:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773221
            [orderIds] => []
        )

)

[2025-06-24 22:03:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773232
            [orderIds] => []
        )

)

[2025-06-24 22:04:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773243
            [orderIds] => []
        )

)

[2025-06-24 22:04:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773254
            [orderIds] => []
        )

)

[2025-06-24 22:04:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773265
            [orderIds] => []
        )

)

[2025-06-24 22:04:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773276
            [orderIds] => []
        )

)

[2025-06-24 22:04:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773287
            [orderIds] => []
        )

)

[2025-06-24 22:04:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773298
            [orderIds] => []
        )

)

[2025-06-24 22:05:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773309
            [orderIds] => []
        )

)

[2025-06-24 22:05:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773320
            [orderIds] => []
        )

)

[2025-06-24 22:05:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773331
            [orderIds] => []
        )

)

[2025-06-24 22:05:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773342
            [orderIds] => []
        )

)

[2025-06-24 22:05:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773353
            [orderIds] => []
        )

)

[2025-06-24 22:06:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773364
            [orderIds] => []
        )

)

[2025-06-24 22:06:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773375
            [orderIds] => []
        )

)

[2025-06-24 22:06:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773386
            [orderIds] => []
        )

)

[2025-06-24 22:06:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773397
            [orderIds] => []
        )

)

[2025-06-24 22:06:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773408
            [orderIds] => []
        )

)

[2025-06-24 22:06:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773419
            [orderIds] => []
        )

)

[2025-06-24 22:07:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773430
            [orderIds] => []
        )

)

[2025-06-24 22:07:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773441
            [orderIds] => []
        )

)

[2025-06-24 22:07:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773452
            [orderIds] => []
        )

)

[2025-06-24 22:07:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 22:07:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 22:07:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 22:07:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 22:07:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773463
            [orderIds] => []
        )

)

[2025-06-24 22:07:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773474
            [orderIds] => []
        )

)

[2025-06-24 22:08:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773485
            [orderIds] => []
        )

)

[2025-06-24 22:08:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773496
            [orderIds] => []
        )

)

[2025-06-24 22:08:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773507
            [orderIds] => []
        )

)

[2025-06-24 22:08:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773518
            [orderIds] => []
        )

)

[2025-06-24 22:08:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1750687719
            [orderIds] => []
        )

)

[2025-06-24 22:08:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1750169319
            [orderIds] => []
        )

)

[2025-06-24 22:08:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1750169319
            [orderIds] => []
        )

)

[2025-06-24 22:08:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-24 22:08:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-24 22:08:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-24 22:08:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-24 22:08:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-24 22:08:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773529
            [orderIds] => []
        )

)

[2025-06-24 22:09:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773540
            [orderIds] => []
        )

)

[2025-06-24 22:09:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773551
            [orderIds] => []
        )

)

[2025-06-24 22:09:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773562
            [orderIds] => []
        )

)

[2025-06-24 22:09:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773573
            [orderIds] => []
        )

)

[2025-06-24 22:09:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773584
            [orderIds] => []
        )

)

[2025-06-24 22:09:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773595
            [orderIds] => []
        )

)

[2025-06-24 22:10:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773606
            [orderIds] => []
        )

)

[2025-06-24 22:10:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773617
            [orderIds] => []
        )

)

[2025-06-24 22:10:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773628
            [orderIds] => []
        )

)

[2025-06-24 22:10:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773639
            [orderIds] => []
        )

)

[2025-06-24 22:10:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773650
            [orderIds] => []
        )

)

[2025-06-24 22:11:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773661
            [orderIds] => []
        )

)

[2025-06-24 22:11:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773672
            [orderIds] => []
        )

)

[2025-06-24 22:11:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773683
            [orderIds] => []
        )

)

[2025-06-24 22:11:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773694
            [orderIds] => []
        )

)

[2025-06-24 22:11:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773705
            [orderIds] => []
        )

)

[2025-06-24 22:11:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773716
            [orderIds] => []
        )

)

[2025-06-24 22:12:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773727
            [orderIds] => []
        )

)

[2025-06-24 22:12:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773738
            [orderIds] => []
        )

)

[2025-06-24 22:12:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773749
            [orderIds] => []
        )

)

[2025-06-24 22:12:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773760
            [orderIds] => []
        )

)

[2025-06-24 22:12:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773771
            [orderIds] => []
        )

)

[2025-06-24 22:13:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773782
            [orderIds] => []
        )

)

[2025-06-24 22:13:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773793
            [orderIds] => []
        )

)

[2025-06-24 22:13:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773804
            [orderIds] => []
        )

)

[2025-06-24 22:13:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773815
            [orderIds] => []
        )

)

[2025-06-24 22:13:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773826
            [orderIds] => []
        )

)

[2025-06-24 22:13:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773837
            [orderIds] => []
        )

)

[2025-06-24 22:14:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773848
            [orderIds] => []
        )

)

[2025-06-24 22:14:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773859
            [orderIds] => []
        )

)

[2025-06-24 22:14:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773870
            [orderIds] => []
        )

)

[2025-06-24 22:14:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773881
            [orderIds] => []
        )

)

[2025-06-24 22:14:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773892
            [orderIds] => []
        )

)

[2025-06-24 22:15:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773903
            [orderIds] => []
        )

)

[2025-06-24 22:15:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773914
            [orderIds] => []
        )

)

[2025-06-24 22:15:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773925
            [orderIds] => []
        )

)

[2025-06-24 22:15:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773936
            [orderIds] => []
        )

)

[2025-06-24 22:15:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773947
            [orderIds] => []
        )

)

[2025-06-24 22:15:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773958
            [orderIds] => []
        )

)

[2025-06-24 22:16:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773969
            [orderIds] => []
        )

)

[2025-06-24 22:16:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773980
            [orderIds] => []
        )

)

[2025-06-24 22:16:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750773991
            [orderIds] => []
        )

)

[2025-06-24 22:16:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774002
            [orderIds] => []
        )

)

[2025-06-24 22:16:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774013
            [orderIds] => []
        )

)

[2025-06-24 22:17:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774024
            [orderIds] => []
        )

)

[2025-06-24 22:17:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774035
            [orderIds] => []
        )

)

[2025-06-24 22:17:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774046
            [orderIds] => []
        )

)

[2025-06-24 22:17:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 22:17:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 22:17:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 22:17:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 22:17:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774057
            [orderIds] => []
        )

)

[2025-06-24 22:17:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774068
            [orderIds] => []
        )

)

[2025-06-24 22:17:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774078
            [orderIds] => []
        )

)

[2025-06-24 22:18:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774088
            [orderIds] => []
        )

)

[2025-06-24 22:18:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774099
            [orderIds] => []
        )

)

[2025-06-24 22:18:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774110
            [orderIds] => []
        )

)

[2025-06-24 22:18:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774121
            [orderIds] => []
        )

)

[2025-06-24 22:18:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774132
            [orderIds] => []
        )

)

[2025-06-24 22:19:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774143
            [orderIds] => []
        )

)

[2025-06-24 22:19:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774154
            [orderIds] => []
        )

)

[2025-06-24 22:19:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774165
            [orderIds] => []
        )

)

[2025-06-24 22:19:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774176
            [orderIds] => []
        )

)

[2025-06-24 22:19:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774187
            [orderIds] => []
        )

)

[2025-06-24 22:19:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774198
            [orderIds] => []
        )

)

[2025-06-24 22:20:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774209
            [orderIds] => []
        )

)

[2025-06-24 22:20:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774220
            [orderIds] => []
        )

)

[2025-06-24 22:20:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774231
            [orderIds] => []
        )

)

[2025-06-24 22:20:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774242
            [orderIds] => []
        )

)

[2025-06-24 22:20:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774253
            [orderIds] => []
        )

)

[2025-06-24 22:21:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774264
            [orderIds] => []
        )

)

[2025-06-24 22:21:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774275
            [orderIds] => []
        )

)

[2025-06-24 22:21:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774286
            [orderIds] => []
        )

)

[2025-06-24 22:21:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774297
            [orderIds] => []
        )

)

[2025-06-24 22:21:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774308
            [orderIds] => []
        )

)

[2025-06-24 22:21:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774319
            [orderIds] => []
        )

)

[2025-06-24 22:22:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774330
            [orderIds] => []
        )

)

[2025-06-24 22:22:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774341
            [orderIds] => []
        )

)

[2025-06-24 22:22:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774352
            [orderIds] => []
        )

)

[2025-06-24 22:22:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774363
            [orderIds] => []
        )

)

[2025-06-24 22:22:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774374
            [orderIds] => []
        )

)

[2025-06-24 22:23:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774385
            [orderIds] => []
        )

)

[2025-06-24 22:23:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774396
            [orderIds] => []
        )

)

[2025-06-24 22:23:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774407
            [orderIds] => []
        )

)

[2025-06-24 22:23:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774418
            [orderIds] => []
        )

)

[2025-06-24 22:23:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774429
            [orderIds] => []
        )

)

[2025-06-24 22:24:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774440
            [orderIds] => []
        )

)

[2025-06-24 22:24:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774451
            [orderIds] => []
        )

)

[2025-06-24 22:24:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774462
            [orderIds] => []
        )

)

[2025-06-24 22:24:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774473
            [orderIds] => []
        )

)

[2025-06-24 22:24:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774484
            [orderIds] => []
        )

)

[2025-06-24 22:24:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774495
            [orderIds] => []
        )

)

[2025-06-24 22:25:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774506
            [orderIds] => []
        )

)

[2025-06-24 22:25:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774517
            [orderIds] => []
        )

)

[2025-06-24 22:25:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774528
            [orderIds] => []
        )

)

[2025-06-24 22:25:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774539
            [orderIds] => []
        )

)

[2025-06-24 22:25:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774550
            [orderIds] => []
        )

)

[2025-06-24 22:26:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774561
            [orderIds] => []
        )

)

[2025-06-24 22:26:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774572
            [orderIds] => []
        )

)

[2025-06-24 22:26:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774583
            [orderIds] => []
        )

)

[2025-06-24 22:26:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774594
            [orderIds] => []
        )

)

[2025-06-24 22:26:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774605
            [orderIds] => []
        )

)

[2025-06-24 22:26:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774616
            [orderIds] => []
        )

)

[2025-06-24 22:27:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774627
            [orderIds] => []
        )

)

[2025-06-24 22:27:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774638
            [orderIds] => []
        )

)

[2025-06-24 22:27:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774649
            [orderIds] => []
        )

)

[2025-06-24 22:27:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 22:27:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 22:27:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 22:27:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 22:27:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774660
            [orderIds] => []
        )

)

[2025-06-24 22:27:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774671
            [orderIds] => []
        )

)

[2025-06-24 22:28:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774682
            [orderIds] => []
        )

)

[2025-06-24 22:28:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774693
            [orderIds] => []
        )

)

[2025-06-24 22:28:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774704
            [orderIds] => []
        )

)

[2025-06-24 22:28:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774715
            [orderIds] => []
        )

)

[2025-06-24 22:28:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774726
            [orderIds] => []
        )

)

[2025-06-24 22:28:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774737
            [orderIds] => []
        )

)

[2025-06-24 22:29:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774748
            [orderIds] => []
        )

)

[2025-06-24 22:29:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774759
            [orderIds] => []
        )

)

[2025-06-24 22:29:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774770
            [orderIds] => []
        )

)

[2025-06-24 22:29:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774781
            [orderIds] => []
        )

)

[2025-06-24 22:29:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774792
            [orderIds] => []
        )

)

[2025-06-24 22:30:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774803
            [orderIds] => []
        )

)

[2025-06-24 22:30:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774814
            [orderIds] => []
        )

)

[2025-06-24 22:30:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774825
            [orderIds] => []
        )

)

[2025-06-24 22:30:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774836
            [orderIds] => []
        )

)

[2025-06-24 22:30:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774847
            [orderIds] => []
        )

)

[2025-06-24 22:30:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774858
            [orderIds] => []
        )

)

[2025-06-24 22:31:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774869
            [orderIds] => []
        )

)

[2025-06-24 22:31:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774880
            [orderIds] => []
        )

)

[2025-06-24 22:31:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774891
            [orderIds] => []
        )

)

[2025-06-24 22:31:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774902
            [orderIds] => []
        )

)

[2025-06-24 22:31:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774913
            [orderIds] => []
        )

)

[2025-06-24 22:32:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774924
            [orderIds] => []
        )

)

[2025-06-24 22:32:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774935
            [orderIds] => []
        )

)

[2025-06-24 22:32:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774946
            [orderIds] => []
        )

)

[2025-06-24 22:32:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774957
            [orderIds] => []
        )

)

[2025-06-24 22:32:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774968
            [orderIds] => []
        )

)

[2025-06-24 22:32:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774979
            [orderIds] => []
        )

)

[2025-06-24 22:33:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750774990
            [orderIds] => []
        )

)

[2025-06-24 22:33:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775001
            [orderIds] => []
        )

)

[2025-06-24 22:33:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775012
            [orderIds] => []
        )

)

[2025-06-24 22:33:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775023
            [orderIds] => []
        )

)

[2025-06-24 22:33:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775034
            [orderIds] => []
        )

)

[2025-06-24 22:34:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775045
            [orderIds] => []
        )

)

[2025-06-24 22:34:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775056
            [orderIds] => []
        )

)

[2025-06-24 22:34:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775067
            [orderIds] => []
        )

)

[2025-06-24 22:34:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775078
            [orderIds] => []
        )

)

[2025-06-24 22:34:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775089
            [orderIds] => []
        )

)

[2025-06-24 22:35:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775100
            [orderIds] => []
        )

)

[2025-06-24 22:35:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775111
            [orderIds] => []
        )

)

[2025-06-24 22:35:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775122
            [orderIds] => []
        )

)

[2025-06-24 22:35:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775133
            [orderIds] => []
        )

)

[2025-06-24 22:35:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775144
            [orderIds] => []
        )

)

[2025-06-24 22:35:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775155
            [orderIds] => []
        )

)

[2025-06-24 22:36:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775166
            [orderIds] => []
        )

)

[2025-06-24 22:36:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775177
            [orderIds] => []
        )

)

[2025-06-24 22:36:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775188
            [orderIds] => []
        )

)

[2025-06-24 22:36:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775199
            [orderIds] => []
        )

)

[2025-06-24 22:36:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775210
            [orderIds] => []
        )

)

[2025-06-24 22:37:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775221
            [orderIds] => []
        )

)

[2025-06-24 22:37:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775232
            [orderIds] => []
        )

)

[2025-06-24 22:37:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775243
            [orderIds] => []
        )

)

[2025-06-24 22:37:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775254
            [orderIds] => []
        )

)

[2025-06-24 22:37:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 22:37:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 22:37:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 22:37:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 22:37:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775265
            [orderIds] => []
        )

)

[2025-06-24 22:37:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775276
            [orderIds] => []
        )

)

[2025-06-24 22:38:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775287
            [orderIds] => []
        )

)

[2025-06-24 22:38:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775298
            [orderIds] => []
        )

)

[2025-06-24 22:38:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775309
            [orderIds] => []
        )

)

[2025-06-24 22:38:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1750689520
            [orderIds] => []
        )

)

[2025-06-24 22:38:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1750171120
            [orderIds] => []
        )

)

[2025-06-24 22:38:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1750171120
            [orderIds] => []
        )

)

[2025-06-24 22:38:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775320
            [orderIds] => []
        )

)

[2025-06-24 22:38:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-24 22:38:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-24 22:38:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-24 22:38:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-24 22:38:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-24 22:38:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775331
            [orderIds] => []
        )

)

[2025-06-24 22:39:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775342
            [orderIds] => []
        )

)

[2025-06-24 22:39:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775353
            [orderIds] => []
        )

)

[2025-06-24 22:39:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775364
            [orderIds] => []
        )

)

[2025-06-24 22:39:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775375
            [orderIds] => []
        )

)

[2025-06-24 22:39:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775386
            [orderIds] => []
        )

)

[2025-06-24 22:39:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775397
            [orderIds] => []
        )

)

[2025-06-24 22:40:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775408
            [orderIds] => []
        )

)

[2025-06-24 22:40:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775419
            [orderIds] => []
        )

)

[2025-06-24 22:40:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775430
            [orderIds] => []
        )

)

[2025-06-24 22:40:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775441
            [orderIds] => []
        )

)

[2025-06-24 22:40:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775452
            [orderIds] => []
        )

)

[2025-06-24 22:41:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775463
            [orderIds] => []
        )

)

[2025-06-24 22:41:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775474
            [orderIds] => []
        )

)

[2025-06-24 22:41:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775485
            [orderIds] => []
        )

)

[2025-06-24 22:41:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775496
            [orderIds] => []
        )

)

[2025-06-24 22:41:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775507
            [orderIds] => []
        )

)

[2025-06-24 22:41:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775518
            [orderIds] => []
        )

)

[2025-06-24 22:42:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775529
            [orderIds] => []
        )

)

[2025-06-24 22:42:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775540
            [orderIds] => []
        )

)

[2025-06-24 22:42:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775551
            [orderIds] => []
        )

)

[2025-06-24 22:42:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775562
            [orderIds] => []
        )

)

[2025-06-24 22:42:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775573
            [orderIds] => []
        )

)

[2025-06-24 22:43:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775584
            [orderIds] => []
        )

)

[2025-06-24 22:43:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775595
            [orderIds] => []
        )

)

[2025-06-24 22:43:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775606
            [orderIds] => []
        )

)

[2025-06-24 22:43:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775617
            [orderIds] => []
        )

)

[2025-06-24 22:43:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775628
            [orderIds] => []
        )

)

[2025-06-24 22:43:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775639
            [orderIds] => []
        )

)

[2025-06-24 22:44:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775650
            [orderIds] => []
        )

)

[2025-06-24 22:44:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775661
            [orderIds] => []
        )

)

[2025-06-24 22:44:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775672
            [orderIds] => []
        )

)

[2025-06-24 22:44:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775683
            [orderIds] => []
        )

)

[2025-06-24 22:44:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775694
            [orderIds] => []
        )

)

[2025-06-24 22:45:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775705
            [orderIds] => []
        )

)

[2025-06-24 22:45:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775716
            [orderIds] => []
        )

)

[2025-06-24 22:45:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775727
            [orderIds] => []
        )

)

[2025-06-24 22:45:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775738
            [orderIds] => []
        )

)

[2025-06-24 22:45:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775749
            [orderIds] => []
        )

)

[2025-06-24 22:46:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775760
            [orderIds] => []
        )

)

[2025-06-24 22:46:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775771
            [orderIds] => []
        )

)

[2025-06-24 22:46:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775782
            [orderIds] => []
        )

)

[2025-06-24 22:46:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775793
            [orderIds] => []
        )

)

[2025-06-24 22:46:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775804
            [orderIds] => []
        )

)

[2025-06-24 22:46:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775815
            [orderIds] => []
        )

)

[2025-06-24 22:47:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775826
            [orderIds] => []
        )

)

[2025-06-24 22:47:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775837
            [orderIds] => []
        )

)

[2025-06-24 22:47:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775848
            [orderIds] => []
        )

)

[2025-06-24 22:47:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775859
            [orderIds] => []
        )

)

[2025-06-24 22:47:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 22:47:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 22:47:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 22:47:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 22:47:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775870
            [orderIds] => []
        )

)

[2025-06-24 22:48:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775881
            [orderIds] => []
        )

)

[2025-06-24 22:48:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775892
            [orderIds] => []
        )

)

[2025-06-24 22:48:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775903
            [orderIds] => []
        )

)

[2025-06-24 22:48:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775914
            [orderIds] => []
        )

)

[2025-06-24 22:48:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775925
            [orderIds] => []
        )

)

[2025-06-24 22:48:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775936
            [orderIds] => []
        )

)

[2025-06-24 22:49:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775947
            [orderIds] => []
        )

)

[2025-06-24 22:49:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775958
            [orderIds] => []
        )

)

[2025-06-24 22:49:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775969
            [orderIds] => []
        )

)

[2025-06-24 22:49:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775980
            [orderIds] => []
        )

)

[2025-06-24 22:49:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750775991
            [orderIds] => []
        )

)

[2025-06-24 22:50:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776002
            [orderIds] => []
        )

)

[2025-06-24 22:50:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776013
            [orderIds] => []
        )

)

[2025-06-24 22:50:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776024
            [orderIds] => []
        )

)

[2025-06-24 22:50:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776035
            [orderIds] => []
        )

)

[2025-06-24 22:50:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776046
            [orderIds] => []
        )

)

[2025-06-24 22:50:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776057
            [orderIds] => []
        )

)

[2025-06-24 22:51:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776068
            [orderIds] => []
        )

)

[2025-06-24 22:51:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776079
            [orderIds] => []
        )

)

[2025-06-24 22:51:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776090
            [orderIds] => []
        )

)

[2025-06-24 22:51:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776101
            [orderIds] => []
        )

)

[2025-06-24 22:51:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776112
            [orderIds] => []
        )

)

[2025-06-24 22:52:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776123
            [orderIds] => []
        )

)

[2025-06-24 22:52:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776134
            [orderIds] => []
        )

)

[2025-06-24 22:52:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776145
            [orderIds] => []
        )

)

[2025-06-24 22:52:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776156
            [orderIds] => []
        )

)

[2025-06-24 22:52:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776167
            [orderIds] => []
        )

)

[2025-06-24 22:52:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776178
            [orderIds] => []
        )

)

[2025-06-24 22:53:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776189
            [orderIds] => []
        )

)

[2025-06-24 22:53:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776200
            [orderIds] => []
        )

)

[2025-06-24 22:53:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776211
            [orderIds] => []
        )

)

[2025-06-24 22:53:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776222
            [orderIds] => []
        )

)

[2025-06-24 22:53:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776233
            [orderIds] => []
        )

)

[2025-06-24 22:54:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776244
            [orderIds] => []
        )

)

[2025-06-24 22:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776255
            [orderIds] => []
        )

)

[2025-06-24 22:54:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776266
            [orderIds] => []
        )

)

[2025-06-24 22:54:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776277
            [orderIds] => []
        )

)

[2025-06-24 22:54:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776288
            [orderIds] => []
        )

)

[2025-06-24 22:54:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776299
            [orderIds] => []
        )

)

[2025-06-24 22:55:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776310
            [orderIds] => []
        )

)

[2025-06-24 22:55:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776321
            [orderIds] => []
        )

)

[2025-06-24 22:55:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776332
            [orderIds] => []
        )

)

[2025-06-24 22:55:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776343
            [orderIds] => []
        )

)

[2025-06-24 22:55:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776354
            [orderIds] => []
        )

)

[2025-06-24 22:56:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776365
            [orderIds] => []
        )

)

[2025-06-24 22:56:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776376
            [orderIds] => []
        )

)

[2025-06-24 22:56:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776387
            [orderIds] => []
        )

)

[2025-06-24 22:56:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776398
            [orderIds] => []
        )

)

[2025-06-24 22:56:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776409
            [orderIds] => []
        )

)

[2025-06-24 22:57:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776420
            [orderIds] => []
        )

)

[2025-06-24 22:57:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776431
            [orderIds] => []
        )

)

[2025-06-24 22:57:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776442
            [orderIds] => []
        )

)

[2025-06-24 22:57:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776453
            [orderIds] => []
        )

)

[2025-06-24 22:57:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 22:57:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 22:57:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 22:57:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 22:57:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776464
            [orderIds] => []
        )

)

[2025-06-24 22:57:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776475
            [orderIds] => []
        )

)

[2025-06-24 22:58:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776486
            [orderIds] => []
        )

)

[2025-06-24 22:58:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776497
            [orderIds] => []
        )

)

[2025-06-24 22:58:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776508
            [orderIds] => []
        )

)

[2025-06-24 22:58:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776519
            [orderIds] => []
        )

)

[2025-06-24 22:58:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776530
            [orderIds] => []
        )

)

[2025-06-24 22:59:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776541
            [orderIds] => []
        )

)

[2025-06-24 22:59:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776552
            [orderIds] => []
        )

)

[2025-06-24 22:59:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776563
            [orderIds] => []
        )

)

[2025-06-24 22:59:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776574
            [orderIds] => []
        )

)

[2025-06-24 22:59:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776585
            [orderIds] => []
        )

)

[2025-06-24 22:59:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776596
            [orderIds] => []
        )

)

[2025-06-24 23:00:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776607
            [orderIds] => []
        )

)

[2025-06-24 23:00:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776618
            [orderIds] => []
        )

)

[2025-06-24 23:00:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776629
            [orderIds] => []
        )

)

[2025-06-24 23:00:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776640
            [orderIds] => []
        )

)

[2025-06-24 23:00:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776651
            [orderIds] => []
        )

)

[2025-06-24 23:01:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776662
            [orderIds] => []
        )

)

[2025-06-24 23:01:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776673
            [orderIds] => []
        )

)

[2025-06-24 23:01:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776684
            [orderIds] => []
        )

)

[2025-06-24 23:01:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776695
            [orderIds] => []
        )

)

[2025-06-24 23:01:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776706
            [orderIds] => []
        )

)

[2025-06-24 23:01:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776717
            [orderIds] => []
        )

)

[2025-06-24 23:02:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776728
            [orderIds] => []
        )

)

[2025-06-24 23:02:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776739
            [orderIds] => []
        )

)

[2025-06-24 23:02:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776750
            [orderIds] => []
        )

)

[2025-06-24 23:02:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776761
            [orderIds] => []
        )

)

[2025-06-24 23:02:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776772
            [orderIds] => []
        )

)

[2025-06-24 23:03:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776783
            [orderIds] => []
        )

)

[2025-06-24 23:03:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776794
            [orderIds] => []
        )

)

[2025-06-24 23:03:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776805
            [orderIds] => []
        )

)

[2025-06-24 23:03:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776816
            [orderIds] => []
        )

)

[2025-06-24 23:03:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776827
            [orderIds] => []
        )

)

[2025-06-24 23:03:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776838
            [orderIds] => []
        )

)

[2025-06-24 23:04:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776849
            [orderIds] => []
        )

)

[2025-06-24 23:04:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776860
            [orderIds] => []
        )

)

[2025-06-24 23:04:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776871
            [orderIds] => []
        )

)

[2025-06-24 23:04:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776882
            [orderIds] => []
        )

)

[2025-06-24 23:04:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776893
            [orderIds] => []
        )

)

[2025-06-24 23:05:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776904
            [orderIds] => []
        )

)

[2025-06-24 23:05:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776915
            [orderIds] => []
        )

)

[2025-06-24 23:05:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776926
            [orderIds] => []
        )

)

[2025-06-24 23:05:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776937
            [orderIds] => []
        )

)

[2025-06-24 23:05:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776948
            [orderIds] => []
        )

)

[2025-06-24 23:05:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776959
            [orderIds] => []
        )

)

[2025-06-24 23:06:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776970
            [orderIds] => []
        )

)

[2025-06-24 23:06:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776981
            [orderIds] => []
        )

)

[2025-06-24 23:06:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750776992
            [orderIds] => []
        )

)

[2025-06-24 23:06:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777003
            [orderIds] => []
        )

)

[2025-06-24 23:06:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777014
            [orderIds] => []
        )

)

[2025-06-24 23:07:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777025
            [orderIds] => []
        )

)

[2025-06-24 23:07:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777036
            [orderIds] => []
        )

)

[2025-06-24 23:07:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777047
            [orderIds] => []
        )

)

[2025-06-24 23:07:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777058
            [orderIds] => []
        )

)

[2025-06-24 23:07:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 23:07:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 23:07:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 23:07:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 23:07:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777069
            [orderIds] => []
        )

)

[2025-06-24 23:08:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777080
            [orderIds] => []
        )

)

[2025-06-24 23:08:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777091
            [orderIds] => []
        )

)

[2025-06-24 23:08:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777102
            [orderIds] => []
        )

)

[2025-06-24 23:08:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777113
            [orderIds] => []
        )

)

[2025-06-24 23:08:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1750691321
            [orderIds] => []
        )

)

[2025-06-24 23:08:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1750172921
            [orderIds] => []
        )

)

[2025-06-24 23:08:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1750172921
            [orderIds] => []
        )

)

[2025-06-24 23:08:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-24 23:08:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-24 23:08:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-24 23:08:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-24 23:08:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777124
            [orderIds] => []
        )

)

[2025-06-24 23:08:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-24 23:08:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777135
            [orderIds] => []
        )

)

[2025-06-24 23:09:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777146
            [orderIds] => []
        )

)

[2025-06-24 23:09:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777157
            [orderIds] => []
        )

)

[2025-06-24 23:09:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777168
            [orderIds] => []
        )

)

[2025-06-24 23:09:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777179
            [orderIds] => []
        )

)

[2025-06-24 23:09:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777190
            [orderIds] => []
        )

)

[2025-06-24 23:10:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777201
            [orderIds] => []
        )

)

[2025-06-24 23:10:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777212
            [orderIds] => []
        )

)

[2025-06-24 23:10:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777223
            [orderIds] => []
        )

)

[2025-06-24 23:10:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777234
            [orderIds] => []
        )

)

[2025-06-24 23:10:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777245
            [orderIds] => []
        )

)

[2025-06-24 23:10:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777256
            [orderIds] => []
        )

)

[2025-06-24 23:11:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777267
            [orderIds] => []
        )

)

[2025-06-24 23:11:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777278
            [orderIds] => []
        )

)

[2025-06-24 23:11:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777289
            [orderIds] => []
        )

)

[2025-06-24 23:11:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777300
            [orderIds] => []
        )

)

[2025-06-24 23:11:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777311
            [orderIds] => []
        )

)

[2025-06-24 23:12:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777322
            [orderIds] => []
        )

)

[2025-06-24 23:12:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777333
            [orderIds] => []
        )

)

[2025-06-24 23:12:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777344
            [orderIds] => []
        )

)

[2025-06-24 23:12:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777355
            [orderIds] => []
        )

)

[2025-06-24 23:12:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777366
            [orderIds] => []
        )

)

[2025-06-24 23:12:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777377
            [orderIds] => []
        )

)

[2025-06-24 23:13:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777388
            [orderIds] => []
        )

)

[2025-06-24 23:13:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777399
            [orderIds] => []
        )

)

[2025-06-24 23:13:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777410
            [orderIds] => []
        )

)

[2025-06-24 23:13:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777421
            [orderIds] => []
        )

)

[2025-06-24 23:13:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777432
            [orderIds] => []
        )

)

[2025-06-24 23:14:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777443
            [orderIds] => []
        )

)

[2025-06-24 23:14:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777454
            [orderIds] => []
        )

)

[2025-06-24 23:14:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777465
            [orderIds] => []
        )

)

[2025-06-24 23:14:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777476
            [orderIds] => []
        )

)

[2025-06-24 23:14:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777487
            [orderIds] => []
        )

)

[2025-06-24 23:14:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777498
            [orderIds] => []
        )

)

[2025-06-24 23:15:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777509
            [orderIds] => []
        )

)

[2025-06-24 23:15:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777520
            [orderIds] => []
        )

)

[2025-06-24 23:15:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777531
            [orderIds] => []
        )

)

[2025-06-24 23:15:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777542
            [orderIds] => []
        )

)

[2025-06-24 23:15:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777553
            [orderIds] => []
        )

)

[2025-06-24 23:16:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777564
            [orderIds] => []
        )

)

[2025-06-24 23:16:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777575
            [orderIds] => []
        )

)

[2025-06-24 23:16:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777586
            [orderIds] => []
        )

)

[2025-06-24 23:16:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777597
            [orderIds] => []
        )

)

[2025-06-24 23:16:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777608
            [orderIds] => []
        )

)

[2025-06-24 23:16:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777619
            [orderIds] => []
        )

)

[2025-06-24 23:17:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777630
            [orderIds] => []
        )

)

[2025-06-24 23:17:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777641
            [orderIds] => []
        )

)

[2025-06-24 23:17:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777652
            [orderIds] => []
        )

)

[2025-06-24 23:17:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 23:17:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 23:17:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 23:17:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 23:17:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777663
            [orderIds] => []
        )

)

[2025-06-24 23:17:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777674
            [orderIds] => []
        )

)

[2025-06-24 23:18:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777685
            [orderIds] => []
        )

)

[2025-06-24 23:18:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777696
            [orderIds] => []
        )

)

[2025-06-24 23:18:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777707
            [orderIds] => []
        )

)

[2025-06-24 23:18:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777718
            [orderIds] => []
        )

)

[2025-06-24 23:18:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777729
            [orderIds] => []
        )

)

[2025-06-24 23:19:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777740
            [orderIds] => []
        )

)

[2025-06-24 23:19:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777751
            [orderIds] => []
        )

)

[2025-06-24 23:19:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777762
            [orderIds] => []
        )

)

[2025-06-24 23:19:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777773
            [orderIds] => []
        )

)

[2025-06-24 23:19:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777784
            [orderIds] => []
        )

)

[2025-06-24 23:19:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777795
            [orderIds] => []
        )

)

[2025-06-24 23:20:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777806
            [orderIds] => []
        )

)

[2025-06-24 23:20:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777817
            [orderIds] => []
        )

)

[2025-06-24 23:20:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777828
            [orderIds] => []
        )

)

[2025-06-24 23:20:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777839
            [orderIds] => []
        )

)

[2025-06-24 23:20:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777850
            [orderIds] => []
        )

)

[2025-06-24 23:21:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777861
            [orderIds] => []
        )

)

[2025-06-24 23:21:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777872
            [orderIds] => []
        )

)

[2025-06-24 23:21:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777883
            [orderIds] => []
        )

)

[2025-06-24 23:21:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777894
            [orderIds] => []
        )

)

[2025-06-24 23:21:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777905
            [orderIds] => []
        )

)

[2025-06-24 23:21:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777916
            [orderIds] => []
        )

)

[2025-06-24 23:22:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777927
            [orderIds] => []
        )

)

[2025-06-24 23:22:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777938
            [orderIds] => []
        )

)

[2025-06-24 23:22:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777949
            [orderIds] => []
        )

)

[2025-06-24 23:22:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777960
            [orderIds] => []
        )

)

[2025-06-24 23:22:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777971
            [orderIds] => []
        )

)

[2025-06-24 23:23:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777982
            [orderIds] => []
        )

)

[2025-06-24 23:23:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750777993
            [orderIds] => []
        )

)

[2025-06-24 23:23:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778004
            [orderIds] => []
        )

)

[2025-06-24 23:23:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778015
            [orderIds] => []
        )

)

[2025-06-24 23:23:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778026
            [orderIds] => []
        )

)

[2025-06-24 23:23:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778037
            [orderIds] => []
        )

)

[2025-06-24 23:24:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778048
            [orderIds] => []
        )

)

[2025-06-24 23:24:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778059
            [orderIds] => []
        )

)

[2025-06-24 23:24:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778070
            [orderIds] => []
        )

)

[2025-06-24 23:24:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778081
            [orderIds] => []
        )

)

[2025-06-24 23:24:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778092
            [orderIds] => []
        )

)

[2025-06-24 23:25:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778103
            [orderIds] => []
        )

)

[2025-06-24 23:25:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778114
            [orderIds] => []
        )

)

[2025-06-24 23:25:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778125
            [orderIds] => []
        )

)

[2025-06-24 23:25:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778136
            [orderIds] => []
        )

)

[2025-06-24 23:25:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778147
            [orderIds] => []
        )

)

[2025-06-24 23:25:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778158
            [orderIds] => []
        )

)

[2025-06-24 23:26:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778169
            [orderIds] => []
        )

)

[2025-06-24 23:26:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778180
            [orderIds] => []
        )

)

[2025-06-24 23:26:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778191
            [orderIds] => []
        )

)

[2025-06-24 23:26:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778202
            [orderIds] => []
        )

)

[2025-06-24 23:26:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778213
            [orderIds] => []
        )

)

[2025-06-24 23:27:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778224
            [orderIds] => []
        )

)

[2025-06-24 23:27:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778235
            [orderIds] => []
        )

)

[2025-06-24 23:27:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778246
            [orderIds] => []
        )

)

[2025-06-24 23:27:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778257
            [orderIds] => []
        )

)

[2025-06-24 23:27:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 23:27:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 23:27:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 23:27:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 23:27:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778268
            [orderIds] => []
        )

)

[2025-06-24 23:27:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778279
            [orderIds] => []
        )

)

[2025-06-24 23:28:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778290
            [orderIds] => []
        )

)

[2025-06-24 23:28:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778301
            [orderIds] => []
        )

)

[2025-06-24 23:28:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778312
            [orderIds] => []
        )

)

[2025-06-24 23:28:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778323
            [orderIds] => []
        )

)

[2025-06-24 23:28:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778334
            [orderIds] => []
        )

)

[2025-06-24 23:29:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778345
            [orderIds] => []
        )

)

[2025-06-24 23:29:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778356
            [orderIds] => []
        )

)

[2025-06-24 23:29:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778367
            [orderIds] => []
        )

)

[2025-06-24 23:29:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778378
            [orderIds] => []
        )

)

[2025-06-24 23:29:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778389
            [orderIds] => []
        )

)

[2025-06-24 23:30:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778400
            [orderIds] => []
        )

)

[2025-06-24 23:30:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778411
            [orderIds] => []
        )

)

[2025-06-24 23:30:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778422
            [orderIds] => []
        )

)

[2025-06-24 23:30:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778433
            [orderIds] => []
        )

)

[2025-06-24 23:30:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778444
            [orderIds] => []
        )

)

[2025-06-24 23:30:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778455
            [orderIds] => []
        )

)

[2025-06-24 23:31:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778466
            [orderIds] => []
        )

)

[2025-06-24 23:31:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778477
            [orderIds] => []
        )

)

[2025-06-24 23:31:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778488
            [orderIds] => []
        )

)

[2025-06-24 23:31:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778499
            [orderIds] => []
        )

)

[2025-06-24 23:31:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778510
            [orderIds] => []
        )

)

[2025-06-24 23:32:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778521
            [orderIds] => []
        )

)

[2025-06-24 23:32:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778532
            [orderIds] => []
        )

)

[2025-06-24 23:32:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778543
            [orderIds] => []
        )

)

[2025-06-24 23:32:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778554
            [orderIds] => []
        )

)

[2025-06-24 23:32:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778565
            [orderIds] => []
        )

)

[2025-06-24 23:32:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778576
            [orderIds] => []
        )

)

[2025-06-24 23:33:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778587
            [orderIds] => []
        )

)

[2025-06-24 23:33:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778598
            [orderIds] => []
        )

)

[2025-06-24 23:33:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778609
            [orderIds] => []
        )

)

[2025-06-24 23:33:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778620
            [orderIds] => []
        )

)

[2025-06-24 23:33:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778631
            [orderIds] => []
        )

)

[2025-06-24 23:34:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778642
            [orderIds] => []
        )

)

[2025-06-24 23:34:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778653
            [orderIds] => []
        )

)

[2025-06-24 23:34:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778664
            [orderIds] => []
        )

)

[2025-06-24 23:34:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778675
            [orderIds] => []
        )

)

[2025-06-24 23:34:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778686
            [orderIds] => []
        )

)

[2025-06-24 23:34:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778697
            [orderIds] => []
        )

)

[2025-06-24 23:35:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778708
            [orderIds] => []
        )

)

[2025-06-24 23:35:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778719
            [orderIds] => []
        )

)

[2025-06-24 23:35:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778730
            [orderIds] => []
        )

)

[2025-06-24 23:35:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778741
            [orderIds] => []
        )

)

[2025-06-24 23:35:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778752
            [orderIds] => []
        )

)

[2025-06-24 23:36:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778763
            [orderIds] => []
        )

)

[2025-06-24 23:36:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778774
            [orderIds] => []
        )

)

[2025-06-24 23:36:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778785
            [orderIds] => []
        )

)

[2025-06-24 23:36:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778796
            [orderIds] => []
        )

)

[2025-06-24 23:36:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778807
            [orderIds] => []
        )

)

[2025-06-24 23:36:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778818
            [orderIds] => []
        )

)

[2025-06-24 23:37:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778829
            [orderIds] => []
        )

)

[2025-06-24 23:37:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778840
            [orderIds] => []
        )

)

[2025-06-24 23:37:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778851
            [orderIds] => []
        )

)

[2025-06-24 23:37:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778862
            [orderIds] => []
        )

)

[2025-06-24 23:37:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 23:37:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 23:37:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 23:37:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 23:37:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778873
            [orderIds] => []
        )

)

[2025-06-24 23:38:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778884
            [orderIds] => []
        )

)

[2025-06-24 23:38:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778895
            [orderIds] => []
        )

)

[2025-06-24 23:38:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778906
            [orderIds] => []
        )

)

[2025-06-24 23:38:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778917
            [orderIds] => []
        )

)

[2025-06-24 23:38:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1750693122
            [orderIds] => []
        )

)

[2025-06-24 23:38:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1750174722
            [orderIds] => []
        )

)

[2025-06-24 23:38:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1750174722
            [orderIds] => []
        )

)

[2025-06-24 23:38:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-06-24 23:38:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-06-24 23:38:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-06-24 23:38:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-06-24 23:38:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-06-24 23:38:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778928
            [orderIds] => []
        )

)

[2025-06-24 23:38:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778939
            [orderIds] => []
        )

)

[2025-06-24 23:39:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778950
            [orderIds] => []
        )

)

[2025-06-24 23:39:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778961
            [orderIds] => []
        )

)

[2025-06-24 23:39:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778972
            [orderIds] => []
        )

)

[2025-06-24 23:39:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778983
            [orderIds] => []
        )

)

[2025-06-24 23:39:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750778994
            [orderIds] => []
        )

)

[2025-06-24 23:40:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779005
            [orderIds] => []
        )

)

[2025-06-24 23:40:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779016
            [orderIds] => []
        )

)

[2025-06-24 23:40:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779027
            [orderIds] => []
        )

)

[2025-06-24 23:40:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779038
            [orderIds] => []
        )

)

[2025-06-24 23:40:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779049
            [orderIds] => []
        )

)

[2025-06-24 23:41:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779060
            [orderIds] => []
        )

)

[2025-06-24 23:41:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779071
            [orderIds] => []
        )

)

[2025-06-24 23:41:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779082
            [orderIds] => []
        )

)

[2025-06-24 23:41:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779093
            [orderIds] => []
        )

)

[2025-06-24 23:41:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779104
            [orderIds] => []
        )

)

[2025-06-24 23:41:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779115
            [orderIds] => []
        )

)

[2025-06-24 23:42:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779126
            [orderIds] => []
        )

)

[2025-06-24 23:42:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779137
            [orderIds] => []
        )

)

[2025-06-24 23:42:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779148
            [orderIds] => []
        )

)

[2025-06-24 23:42:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779159
            [orderIds] => []
        )

)

[2025-06-24 23:42:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779170
            [orderIds] => []
        )

)

[2025-06-24 23:43:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779181
            [orderIds] => []
        )

)

[2025-06-24 23:43:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779192
            [orderIds] => []
        )

)

[2025-06-24 23:43:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779203
            [orderIds] => []
        )

)

[2025-06-24 23:43:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779214
            [orderIds] => []
        )

)

[2025-06-24 23:43:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779225
            [orderIds] => []
        )

)

[2025-06-24 23:43:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779236
            [orderIds] => []
        )

)

[2025-06-24 23:44:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779247
            [orderIds] => []
        )

)

[2025-06-24 23:44:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779258
            [orderIds] => []
        )

)

[2025-06-24 23:44:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779269
            [orderIds] => []
        )

)

[2025-06-24 23:44:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779280
            [orderIds] => []
        )

)

[2025-06-24 23:44:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779291
            [orderIds] => []
        )

)

[2025-06-24 23:45:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779302
            [orderIds] => []
        )

)

[2025-06-24 23:45:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779313
            [orderIds] => []
        )

)

[2025-06-24 23:45:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779324
            [orderIds] => []
        )

)

[2025-06-24 23:45:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779335
            [orderIds] => []
        )

)

[2025-06-24 23:45:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779346
            [orderIds] => []
        )

)

[2025-06-24 23:45:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779357
            [orderIds] => []
        )

)

[2025-06-24 23:46:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779368
            [orderIds] => []
        )

)

[2025-06-24 23:46:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779379
            [orderIds] => []
        )

)

[2025-06-24 23:46:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779390
            [orderIds] => []
        )

)

[2025-06-24 23:46:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779401
            [orderIds] => []
        )

)

[2025-06-24 23:46:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779412
            [orderIds] => []
        )

)

[2025-06-24 23:47:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779423
            [orderIds] => []
        )

)

[2025-06-24 23:47:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779434
            [orderIds] => []
        )

)

[2025-06-24 23:47:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779445
            [orderIds] => []
        )

)

[2025-06-24 23:47:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779456
            [orderIds] => []
        )

)

[2025-06-24 23:47:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 23:47:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 23:47:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 23:47:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 23:47:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779467
            [orderIds] => []
        )

)

[2025-06-24 23:47:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779478
            [orderIds] => []
        )

)

[2025-06-24 23:48:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779489
            [orderIds] => []
        )

)

[2025-06-24 23:48:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779500
            [orderIds] => []
        )

)

[2025-06-24 23:48:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779511
            [orderIds] => []
        )

)

[2025-06-24 23:48:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779522
            [orderIds] => []
        )

)

[2025-06-24 23:48:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779533
            [orderIds] => []
        )

)

[2025-06-24 23:49:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779544
            [orderIds] => []
        )

)

[2025-06-24 23:49:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779555
            [orderIds] => []
        )

)

[2025-06-24 23:49:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779566
            [orderIds] => []
        )

)

[2025-06-24 23:49:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779577
            [orderIds] => []
        )

)

[2025-06-24 23:49:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779588
            [orderIds] => []
        )

)

[2025-06-24 23:49:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779599
            [orderIds] => []
        )

)

[2025-06-24 23:50:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779610
            [orderIds] => []
        )

)

[2025-06-24 23:50:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779621
            [orderIds] => []
        )

)

[2025-06-24 23:50:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779632
            [orderIds] => []
        )

)

[2025-06-24 23:50:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779643
            [orderIds] => []
        )

)

[2025-06-24 23:50:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779654
            [orderIds] => []
        )

)

[2025-06-24 23:51:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779665
            [orderIds] => []
        )

)

[2025-06-24 23:51:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779676
            [orderIds] => []
        )

)

[2025-06-24 23:51:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779687
            [orderIds] => []
        )

)

[2025-06-24 23:51:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779698
            [orderIds] => []
        )

)

[2025-06-24 23:51:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779709
            [orderIds] => []
        )

)

[2025-06-24 23:52:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779720
            [orderIds] => []
        )

)

[2025-06-24 23:52:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779731
            [orderIds] => []
        )

)

[2025-06-24 23:52:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779742
            [orderIds] => []
        )

)

[2025-06-24 23:52:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779753
            [orderIds] => []
        )

)

[2025-06-24 23:52:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779764
            [orderIds] => []
        )

)

[2025-06-24 23:52:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779775
            [orderIds] => []
        )

)

[2025-06-24 23:53:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779786
            [orderIds] => []
        )

)

[2025-06-24 23:53:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779797
            [orderIds] => []
        )

)

[2025-06-24 23:53:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779808
            [orderIds] => []
        )

)

[2025-06-24 23:53:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779819
            [orderIds] => []
        )

)

[2025-06-24 23:53:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779830
            [orderIds] => []
        )

)

[2025-06-24 23:54:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779841
            [orderIds] => []
        )

)

[2025-06-24 23:54:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779852
            [orderIds] => []
        )

)

[2025-06-24 23:54:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779863
            [orderIds] => []
        )

)

[2025-06-24 23:54:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779874
            [orderIds] => []
        )

)

[2025-06-24 23:54:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779885
            [orderIds] => []
        )

)

[2025-06-24 23:54:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779896
            [orderIds] => []
        )

)

[2025-06-24 23:55:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779907
            [orderIds] => []
        )

)

[2025-06-24 23:55:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779918
            [orderIds] => []
        )

)

[2025-06-24 23:55:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779929
            [orderIds] => []
        )

)

[2025-06-24 23:55:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779940
            [orderIds] => []
        )

)

[2025-06-24 23:55:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779951
            [orderIds] => []
        )

)

[2025-06-24 23:56:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779962
            [orderIds] => []
        )

)

[2025-06-24 23:56:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779973
            [orderIds] => []
        )

)

[2025-06-24 23:56:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779984
            [orderIds] => []
        )

)

[2025-06-24 23:56:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750779995
            [orderIds] => []
        )

)

[2025-06-24 23:56:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780006
            [orderIds] => []
        )

)

[2025-06-24 23:56:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780017
            [orderIds] => []
        )

)

[2025-06-24 23:57:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780028
            [orderIds] => []
        )

)

[2025-06-24 23:57:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780039
            [orderIds] => []
        )

)

[2025-06-24 23:57:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780050
            [orderIds] => []
        )

)

[2025-06-24 23:57:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780061
            [orderIds] => []
        )

)

[2025-06-24 23:57:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-06-24 23:57:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 23:57:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-06-24 23:57:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-06-24 23:57:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780072
            [orderIds] => []
        )

)

[2025-06-24 23:58:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780083
            [orderIds] => []
        )

)

[2025-06-24 23:58:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780094
            [orderIds] => []
        )

)

[2025-06-24 23:58:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780105
            [orderIds] => []
        )

)

[2025-06-24 23:58:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780116
            [orderIds] => []
        )

)

[2025-06-24 23:58:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780127
            [orderIds] => []
        )

)

[2025-06-24 23:58:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780138
            [orderIds] => []
        )

)

[2025-06-24 23:59:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780149
            [orderIds] => []
        )

)

[2025-06-24 23:59:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780160
            [orderIds] => []
        )

)

[2025-06-24 23:59:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780171
            [orderIds] => []
        )

)

[2025-06-24 23:59:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780182
            [orderIds] => []
        )

)

[2025-06-24 23:59:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1750780193
            [orderIds] => []
        )

)

