<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\timer\controller;

use think\facade\Event;
use app\common\service\system\Process as SystemProcessService;
use app\timer\model\Store as StoreModel;

/**
 * 商城定时任务
 * Class Store
 * @package app\timer\controller
 */
class Store extends Controller
{
    /**
     * 任务处理
     */
    public function handle()
    {
        // 记录定时任务最后执行时间
        SystemProcessService::setLastWorkingTime('timer');
        // 遍历商城列表并执行定时任务
        foreach (StoreModel::getStoreIds() as $storeId) {
            // 定时任务：商城订单
            Event::trigger('Order', ['storeId' => $storeId]);
            // 定时任务：用户优惠券
            Event::trigger('UserCoupon', ['storeId' => $storeId]);
            // 定时任务：会员等级
            Event::trigger('UserGrade', ['storeId' => $storeId]);
            // 定时任务：分销订单
            Event::trigger('DealerOrder', ['storeId' => $storeId]);
            // 定时任务：砍价任务
            Event::trigger('BargainTask', ['storeId' => $storeId]);
            // 定时任务：秒杀订单
            Event::trigger('SharpOrder', ['storeId' => $storeId]);
            // 定时任务：拼团拼单任务
            Event::trigger('GrouponTask', ['storeId' => $storeId]);
        }
    }
}