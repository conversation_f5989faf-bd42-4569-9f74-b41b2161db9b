import{o as e,c as s,r as a,z as o,Z as t,v as l,x as i,w as d,n,i as c,e as u,a as r,f,t as m,b as p,d as g,F as _,l as h,U as k,y as x,j as S}from"./index-4ddb689d.js";import{_ as v}from"./mp-html.2b6407c3.js";import{r as y}from"./uni-app.es.24af5d4f.js";import{_ as C}from"./u-modal.9219359e.js";import{W as w}from"./wxofficial.956096a5.js";import{S as b,a as I,C as P}from"./Comment.8cab973f.js";import{C as T}from"./index.cdfc996d.js";import{h as B}from"./color.813a9497.js";import{p as j,b as L,d as V}from"./task.812dd4ac.js";import{G as N}from"./index.0d19d98b.js";import{_ as A}from"./_plugin-vue_export-helper.1b428a4d.js";import{C as D}from"./index.994d85b2.js";import{d as R}from"./index.85d62ccb.js";import{t as $}from"./cart.86d426b5.js";import"./u-loading.7bd69747.js";import"./u-popup.37b3343e.js";import"./u-icon.e981d1c2.js";import"./comment.083546cf.js";import"./index.1169d7d7.js";const M=A({components:{ShareSheet:b,CustomerBtn:T,SlideImage:I,SkuPopup:A({components:{GoodsSkuPopup:N},emits:["update:modelValue"],props:{modelValue:{Type:Boolean,default:!1},skuMode:{type:Number,default:1},active:{type:Object,default:{}},goods:{type:Object,default:{}}},data:()=>({goodsInfo:{}}),computed:{activedBtnBackgroundColor(){return B(this.appTheme.mainBg,.1)}},created(){const e=this,{goods:s}=e;e.goodsInfo={_id:s.goods_id,name:s.goods_name,goods_thumb:s.goods_image,sku_list:e.getSkuList(),spec_list:e.getSpecList()}},methods:{onChangeValue(e){this.$emit("update:modelValue",e)},getSkuList(){const e=this,{goods:{goods_name:s,goods_image:a,skuList:o}}=e,t=[];return o.forEach((o=>{t.push({_id:o.id,goods_sku_id:o.goods_sku_id,goods_id:o.goods_id,goods_name:s,image:o.image_url?o.image_url:a,price:100*o.goods_price,stock:o.stock_num,spec_value_ids:o.spec_value_ids,sku_name_arr:e.getSkuNameArr(o.spec_value_ids)})})),t},getSkuNameArr(e){const s=this,a=[];return e&&e.forEach(((e,o)=>{const t=s.getSpecValueName(e,o);a.push(t)})),a.length?a:["默认"]},getSpecValueName(e,s){const{goods:{specList:a}}=this;return a[s].valueList.find((s=>s.spec_value_id==e)).spec_value},getSpecList(){const{goods:{specList:e}}=this,s=[];return e.forEach((e=>{const a=[];e.valueList.forEach((e=>{a.push({name:e.spec_value})})),s.push({name:e.spec_name,list:a})})),s.length?s:[{name:"默认",list:[{name:"默认"}]}]},openSkuPopup(){},closeSkuPopup(){},buyNow(e){j({activeId:this.active.active_id,goodsSkuId:e.goods_sku_id}).then((e=>{const s=e.data.taskId;this.$navTo("pages/bargain/task",{taskId:s})})),this.onChangeValue(!1)}}},[["render",function(o,t,l,i,d,n){const c=a("goods-sku-popup");return e(),s(c,{modelValue:l.modelValue,onInput:n.onChangeValue,"border-radius":"20",localdata:d.goodsInfo,mode:l.skuMode,maskCloseAble:!0,priceColor:o.appTheme.mainBg,buyNowBackgroundColor:o.appTheme.mainBg,addCartColor:o.appTheme.viceText,addCartBackgroundColor:o.appTheme.viceBg,activedStyle:{color:o.appTheme.mainBg,borderColor:o.appTheme.mainBg,backgroundColor:n.activedBtnBackgroundColor},onOpen:n.openSkuPopup,onClose:n.closeSkuPopup,onBuyNow:n.buyNow,buyNowText:"立即砍价",maxBuyNum:1},null,8,["modelValue","onInput","localdata","mode","priceColor","buyNowBackgroundColor","addCartColor","addCartBackgroundColor","activedStyle","onOpen","onClose","onBuyNow"])}]]),Comment:P,CountDown:D},mixins:[w],data:()=>({isLoading:!0,showSkuPopup:!1,skuMode:3,showShareSheet:!1,showRules:!1,posterApiCall:L,activeId:null,goodsId:null,active:{},goods:{},setting:null,isPartake:null,taskId:null,cartTotal:0,isShowCustomerBtn:!1}),async onLoad(e){this.onRecordQuery(e),this.onRefreshPage(),this.isShowCustomerBtn=await o.isShowCustomerBtn()},methods:{onRecordQuery(e){const s=t(e);this.activeId=e.activeId?parseInt(e.activeId):parseInt(s.aid),this.goodsId=e.goodsId?parseInt(e.goodsId):parseInt(s.gid)},onRefreshPage(){const e=this;e.isLoading=!0,Promise.all([e.getActiveDetail(),e.getGoodsDetail(),e.getCartTotal()]).then((()=>e.setWxofficialShareData())).then((()=>e.isLoading=!1))},getActiveDetail(){const e=this;return new Promise(((s,a)=>{V(e.activeId).then((a=>{e.active=a.data.active,e.setting=a.data.setting,e.isPartake=a.data.isPartake,e.taskId=a.data.taskId,s(a)})).catch(a)}))},getGoodsDetail(){const e=this;return new Promise(((s,a)=>{R(e.goodsId,!1).then((a=>{e.goods=a.data.detail,s(a)})).catch(a)}))},getCartTotal(){const e=this;return new Promise(((s,a)=>{$().then((a=>{e.cartTotal=a.data.cartTotal,s(a)})).catch(a)}))},onShowSkuPopup(){this.showSkuPopup=!this.showSkuPopup},onShowShareSheet(){this.showShareSheet=!this.showShareSheet},handleShowRules(){this.showRules=!0},onTargetHome(e){this.$navTo("pages/index/index")},onTargetCart(){this.$navTo("pages/cart/index")},handleMainBtn(){const e=this;if(!e.isPartake)return e.onShowSkuPopup();e.$navTo("pages/bargain/task",{taskId:e.taskId})},setWxofficialShareData(){const{goods:e}=this;this.updateShareCardData({title:e.goods_name,desc:e.selling_point,imgUrl:e.goods_image})}},onShareAppMessage(){const e=this,s=e.$getShareUrlParams({activeId:e.activeId,goodsId:e.goodsId});return{title:e.goods.goods_name,path:`/pages/bargain/goods/index?${s}`}},onShareTimeline(){const e=this,s=e.$getShareUrlParams({activeId:e.activeId,goodsId:e.goodsId});return{title:e.goods.goods_name,path:`/pages/bargain/goods/index?${s}`}}},[["render",function(o,t,w,b,I,P){const T=a("SlideImage"),B=h,j=c,L=k,V=a("count-down"),N=a("SkuPopup"),A=a("Comment"),D=y(x("mp-html"),v),R=a("customer-btn"),$=a("share-sheet"),M=S,U=y(x("u-modal"),C);return l((e(),s(j,{class:"container",style:n(o.appThemeStyle)},{default:d((()=>[I.isLoading?u("",!0):(e(),s(T,{key:0,video:I.goods.video,videoCover:I.goods.videoCover,images:I.goods.goods_images},null,8,["video","videoCover","images"])),I.isLoading?u("",!0):(e(),s(j,{key:1,class:"goods-info m-top20"},{default:d((()=>[r(j,{class:"info-item info-item__top dis-flex flex-x-between flex-y-end"},{default:d((()=>[r(j,{class:"block-left dis-flex flex-y-center"},{default:d((()=>[r(j,{class:"active-tag"},{default:d((()=>[r(B,null,{default:d((()=>[f("限时砍价")])),_:1})])),_:1}),r(B,{class:"floor-price__samll"},{default:d((()=>[f("￥")])),_:1}),r(B,{class:"floor-price"},{default:d((()=>[f(m(I.active.floor_price),1)])),_:1}),r(B,{class:"original-price"},{default:d((()=>[f("￥"+m(I.goods.goods_price_min),1)])),_:1})])),_:1}),r(j,{class:"block-right dis-flex"},{default:d((()=>[r(j,{class:"goods-sales"},{default:d((()=>[r(B,null,{default:d((()=>[f("已砍成"+m(I.active.active_sales)+"件",1)])),_:1})])),_:1})])),_:1})])),_:1}),r(j,{class:"info-item info-item__name dis-flex flex-y-center"},{default:d((()=>[r(j,{class:"goods-name flex-box"},{default:d((()=>[r(B,{class:"twoline-hide"},{default:d((()=>[f(m(I.goods.goods_name),1)])),_:1})])),_:1}),r(j,{class:"goods-share__line"}),r(j,{class:"goods-share"},{default:d((()=>[r(L,{class:"share-btn dis-flex flex-dir-column",onClick:t[0]||(t[0]=e=>P.onShowShareSheet())},{default:d((()=>[r(B,{class:"share__icon iconfont icon-fenxiang"}),r(B,{class:"f-24"},{default:d((()=>[f("分享")])),_:1})])),_:1})])),_:1})])),_:1}),I.goods.selling_point?(e(),s(j,{key:0,class:"info-item info-item_selling-point"},{default:d((()=>[r(B,null,{default:d((()=>[f(m(I.goods.selling_point),1)])),_:1})])),_:1})):u("",!0),0==I.active.is_end?(e(),s(j,{key:1,class:"info-item info-item_status info-item_countdown dis-flex flex-y-center"},{default:d((()=>[r(B,{class:"countdown-icon iconfont icon-naozhong"}),r(B,null,{default:d((()=>[f("距离活动结束")])),_:1}),r(B,{class:"m-r-10"},{default:d((()=>[f("还剩")])),_:1}),r(V,{date:I.active.end_time,separator:"zh",theme:"text"},null,8,["date"])])),_:1})):u("",!0),1==I.active.is_end?(e(),s(j,{key:2,class:"info-item info-item_status info-item_end"},{default:d((()=>[r(B,{class:"countdown-icon iconfont icon-naozhong"}),r(B,null,{default:d((()=>[f("砍价活动已结束，下次记得早点来哦~")])),_:1})])),_:1})):u("",!0)])),_:1})),r(j,{class:"bargain-rules m-top20 b-f",onClick:t[1]||(t[1]=e=>P.handleShowRules())},{default:d((()=>[r(j,{class:"item-title dis-flex"},{default:d((()=>[r(j,{class:"block-left flex-box"},{default:d((()=>[r(B,null,{default:d((()=>[f("砍价玩法")])),_:1})])),_:1}),r(j,{class:"block-right"},{default:d((()=>[r(B,{class:"show-more col-9"},{default:d((()=>[f("查看规则")])),_:1})])),_:1})])),_:1}),r(j,{class:"rule-simple dis-flex flex-x-around"},{default:d((()=>[r(j,{class:"simple-item dis-flex flex-dir-column flex-y-center"},{default:d((()=>[r(j,{class:"i-number dis-flex flex-x-center flex-y-center"},{default:d((()=>[r(B,{class:"f-30"},{default:d((()=>[f("1")])),_:1})])),_:1}),r(j,{class:"i-text f-28"},{default:d((()=>[f("点击砍价")])),_:1})])),_:1}),r(j,{class:"simple-item dis-flex flex-dir-column flex-y-center"},{default:d((()=>[r(j,{class:"i-number dis-flex flex-x-center flex-y-center"},{default:d((()=>[r(B,{class:"f-30"},{default:d((()=>[f("2")])),_:1})])),_:1}),r(j,{class:"i-text f-28"},{default:d((()=>[f("找人帮砍")])),_:1})])),_:1}),r(j,{class:"simple-item dis-flex flex-dir-column flex-y-center"},{default:d((()=>[r(j,{class:"i-number dis-flex flex-x-center flex-y-center"},{default:d((()=>[r(B,{class:"f-30"},{default:d((()=>[f("3")])),_:1})])),_:1}),r(j,{class:"i-text f-28"},{default:d((()=>[f("砍到最低")])),_:1})])),_:1}),r(j,{class:"simple-item dis-flex flex-dir-column flex-y-center"},{default:d((()=>[r(j,{class:"i-number dis-flex flex-x-center flex-y-center"},{default:d((()=>[r(B,{class:"f-30"},{default:d((()=>[f("4")])),_:1})])),_:1}),r(j,{class:"i-text f-28"},{default:d((()=>[f("优惠购买")])),_:1})])),_:1})])),_:1})])),_:1}),20==I.goods.spec_type?(e(),s(j,{key:2,class:"goods-choice m-top20 b-f",onClick:t[2]||(t[2]=e=>P.onShowSkuPopup())},{default:d((()=>[r(j,{class:"spec-list"},{default:d((()=>[r(j,{class:"flex-box"},{default:d((()=>[r(B,{class:"col-8"},{default:d((()=>[f("选择：")])),_:1}),(e(!0),p(_,null,g(I.goods.specList,((a,o)=>(e(),s(B,{class:"spec-name",key:o},{default:d((()=>[f(m(a.spec_name),1)])),_:2},1024)))),128))])),_:1}),r(j,{class:"f-26 col-9 t-r"},{default:d((()=>[r(B,{class:"iconfont icon-arrow-right"})])),_:1})])),_:1})])),_:1})):u("",!0),I.isLoading?u("",!0):(e(),s(N,{key:3,modelValue:I.showSkuPopup,"onUpdate:modelValue":t[3]||(t[3]=e=>I.showSkuPopup=e),skuMode:I.skuMode,active:I.active,goods:I.goods},null,8,["modelValue","skuMode","active","goods"])),I.isLoading?u("",!0):(e(),s(A,{key:4,"goods-id":I.goodsId,limit:2},null,8,["goods-id"])),I.isLoading?u("",!0):(e(),s(j,{key:5,class:"goods-content m-top20"},{default:d((()=>[r(j,{class:"item-title b-f"},{default:d((()=>[r(B,null,{default:d((()=>[f("商品描述")])),_:1})])),_:1}),""!=I.goods.content?(e(),s(j,{key:0,class:"goods-content__detail b-f"},{default:d((()=>[r(D,{content:I.goods.content},null,8,["content"])])),_:1})):u("",!0)])),_:1})),r(j,{class:"footer-fixed"},{default:d((()=>[r(j,{class:"footer-container"},{default:d((()=>[r(j,{class:"foo-item-fast"},{default:d((()=>[r(j,{class:"fast-item fast-item--home",onClick:P.onTargetHome},{default:d((()=>[r(j,{class:"fast-icon"},{default:d((()=>[r(B,{class:"iconfont icon-shouye"})])),_:1}),r(j,{class:"fast-text"},{default:d((()=>[r(B,null,{default:d((()=>[f("首页")])),_:1})])),_:1})])),_:1},8,["onClick"]),I.isShowCustomerBtn?(e(),s(R,{key:0},{default:d((()=>[r(j,{class:"fast-item"},{default:d((()=>[r(j,{class:"fast-icon"},{default:d((()=>[r(B,{class:"iconfont icon-kefu1"})])),_:1}),r(j,{class:"fast-text"},{default:d((()=>[r(B,null,{default:d((()=>[f("客服")])),_:1})])),_:1})])),_:1})])),_:1})):u("",!0),I.isShowCustomerBtn?u("",!0):(e(),s(j,{key:1,class:"fast-item fast-item--cart",onClick:P.onTargetCart},{default:d((()=>[I.cartTotal>0?(e(),s(j,{key:0,class:"fast-badge fast-badge--fixed"},{default:d((()=>[f(m(I.cartTotal>99?"99+":I.cartTotal),1)])),_:1})):u("",!0),r(j,{class:"fast-icon"},{default:d((()=>[r(B,{class:"iconfont icon-gouwuche"})])),_:1}),r(j,{class:"fast-text"},{default:d((()=>[r(B,null,{default:d((()=>[f("购物车")])),_:1})])),_:1})])),_:1},8,["onClick"]))])),_:1}),r(j,{class:"foo-item-btn"},{default:d((()=>[r(j,{class:"btn-wrapper"},{default:d((()=>[I.active.is_start&&!I.active.is_end?(e(),s(j,{key:0,class:"btn-item btn--main",onClick:t[4]||(t[4]=e=>P.handleMainBtn(3))},{default:d((()=>[r(B,null,{default:d((()=>[f(m(I.isPartake?"继续砍价":"立即砍价"),1)])),_:1})])),_:1})):(e(),s(L,{key:1,class:"btn-item btn--gray"},{default:d((()=>[r(B,null,{default:d((()=>[f(m(I.active.is_end?"活动已结束":"活动未开启"),1)])),_:1})])),_:1}))])),_:1})])),_:1})])),_:1})])),_:1}),r($,{modelValue:I.showShareSheet,"onUpdate:modelValue":t[5]||(t[5]=e=>I.showShareSheet=e),shareTitle:I.goods.goods_name,shareImageUrl:I.goods.goods_image,posterApiCall:I.posterApiCall,posterApiParam:{activeId:I.activeId}},null,8,["modelValue","shareTitle","shareImageUrl","posterApiCall","posterApiParam"]),I.isLoading?u("",!0):(e(),s(U,{key:6,modelValue:I.showRules,"onUpdate:modelValue":t[6]||(t[6]=e=>I.showRules=e),title:"砍价规则"},{default:d((()=>[r(M,{style:{height:"610rpx"},"scroll-y":!0},{default:d((()=>[r(j,{class:"pops-content"},{default:d((()=>[r(B,null,{default:d((()=>[f(m(I.setting.rulesDesc),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"]))])),_:1},8,["style"])),[[i,!I.isLoading]])}],["__scopeId","data-v-8da88621"]]);export{M as default};
