<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\api\controller\xj;

use app\api\controller\Controller;
use app\api\model\xj\Message as MessageModel;
use think\facade\Db;
use think\response\Json;

/**
 * 消息管理
 * Class Recharge
 * @package app\api\controller
 */
class Message extends Controller
{

    public function getNew(): Json
    {
        $detail = MessageModel::getNew();
        return $this->renderSuccess(compact('detail'));

    }
    public function detail(int $logId): Json
    {
        Db::name('xj_message')->where('log_id', $logId)->update(['is_read' => 1]);
        return $this->renderSuccess();
    }
    public function list(): Json
    {
        $model = new MessageModel;
        $list  = $model->getList();
        return $this->renderSuccess(compact('list'));
    }

}
