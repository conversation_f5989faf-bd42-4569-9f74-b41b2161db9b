import{$ as o}from"./index-2c18571c.js";const s="goods/list",d="goods/detail",e="goods/basic",g="goods/specData",t="goods/skuInfo",a="goods/recommended",r="goods/poster",i=(d,e)=>o.get(s,d,e),m=(s,e=!0,g={})=>(e=Number(e),o.get(d,{goodsId:s,verifyStatus:e,...g})),u=(s,d=!0,g={})=>(d=Number(d),o.get(e,{goodsId:s,verifyStatus:d,...g})),I=s=>o.get(g,{goodsId:s}),p=()=>o.get(a),b=(s,d,e)=>o.get(t,{goodsId:s,goodsSkuId:d,...e}),c=s=>o.get(r,s);export{b as a,u as b,m as d,i as l,c as p,p as r,I as s};
