<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller;

use think\response\Json;
use app\api\model\Promote as PromoteModel;

/**
 * 开屏推广活动
 * Class Promote
 * @package app\store\controller\Promote
 */
class Promote extends Controller
{
    /**
     * 获取开启的活动列表
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function activeList(): Json
    {
        $model = new PromoteModel;
        $list = $model->getActiveList();
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 累积展现次数
     * @param int $promoteId
     * @return Json
     */
    public function updateViewsNum(int $promoteId): Json
    {
        $model = new PromoteModel;
        if ($model->updateViewsNum($promoteId)) {
            return $this->renderSuccess();
        }
        return $this->renderError($model->getError() ?: '累积展现次数失败');
    }

    /**
     * 累积点击次数
     * @param int $promoteId
     * @return Json
     */
    public function updateClickNum(int $promoteId): Json
    {
        $model = new PromoteModel;
        if ($model->updateClickNum($promoteId)) {
            return $this->renderSuccess();
        }
        return $this->renderError($model->getError() ?: '累积点击次数失败');
    }
}
