<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\api\controller\xj;

use app\api\controller\Controller;
use app\api\model\xj\Video as VideoModel;
use think\response\Json;

/**
 * 文章控制器
 * Class Video
 * @package app\api\controller
 */
class Video extends Controller
{
    /**
     * 文章列表
     * @param int $categoryId
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function list(int $categoryId = 0): Json
    {
        $model = new VideoModel;
        $list  = $model->getList($categoryId);
        return $this->renderSuccess(compact('list'));
    }

      public function listDetail(int $categoryId = 0,int $videoId=0,int $page=1): Json
    {
        $model = new VideoModel;
        $list  = $model->getListDetail($categoryId,$videoId,$page);
        return $this->renderSuccess(compact('list'));
    }


    /**
     * 文章详情
     * @param int $articleId
     * @return Json
     * @throws \cores\exception\BaseException
     */
    public function detail(int $articleId): Json
    {
        $detail = VideoModel::getDetail($articleId);
        return $this->renderSuccess(compact('detail'));
    }
    public function addView(int $articleId): Json
    {
        $detail = VideoModel::getDetail($articleId);
        $score=$detail->addView($articleId);
        return $this->renderSuccess(compact('score'));
    }
}
