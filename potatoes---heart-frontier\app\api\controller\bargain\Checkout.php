<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller\bargain;

use think\response\Json;
use app\api\controller\Controller;
use app\api\model\bargain\Task as TaskModel;
use app\api\model\bargain\Setting as SettingModel;
use app\api\service\order\Checkout as CheckoutService;
use app\common\enum\order\OrderSource as OrderSourceEnum;

/**
 * 砍价订单结算控制器
 * Class Checkout
 * @package app\api\controller\bargain
 */
class Checkout extends Controller
{
    /**
     * 砍价订单结算
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function order(): Json
    {
        // 实例化结算台服务
        $Checkout = new CheckoutService;
        // 订单结算api参数
        $params = $Checkout->setParam($this->getParam([
            'taskId' => 0
        ]));
        // 获取砍价商品信息
        $goodsList = TaskModel::getTaskGoods((int)$params['taskId']);
        // 设置订单来源
        $Checkout->setOrderSource([
            'source' => OrderSourceEnum::BARGAIN,
            'sourceId' => (int)$params['taskId'],
        ]);
        // 砍价商品不参与 等级折扣、积分抵扣和优惠券折扣
        $Checkout->setCheckoutRule([
            'isUserGrade' => false,
            'isCoupon' => false,
            'isUsePoints' => false,
            'isUseFullDiscount' => false,
            'isUseFullFree' => false,
            'isDealer' => SettingModel::getIsDealer(),
        ]);
        // 获取订单结算信息
        $orderInfo = $Checkout->onCheckout($goodsList);
        if ($this->request->isGet()) {
            return $this->renderSuccess([
                'order' => $orderInfo,
                'personal' => $Checkout->getPersonal(),
                'setting' => $Checkout->getSetting(),
            ]);
        }
        // 订单结算提交
        if ($Checkout->hasError()) {
            return $this->renderError($Checkout->getError());
        }
        // 创建订单
        if (!$Checkout->createOrder($orderInfo)) {
            return $this->renderError($Checkout->getError() ?: '订单创建失败');
        }
        // 订单创建后将砍价任务结束
        TaskModel::setTaskEnd((int)$params['taskId']);
        // 返回结算信息
        return $this->renderSuccess(['orderId' => $Checkout->model['order_id']], '订单创建成功');
    }

    /**
     * 订单提交
     * @return Json
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function submit(): Json
    {
        return $this->order();
    }

    /**
     * 订单结算提交的参数
     * @param array $define
     * @return array
     */
    private function getParam(array $define = []): array
    {
        return array_merge($define, $this->request->param());
    }
}