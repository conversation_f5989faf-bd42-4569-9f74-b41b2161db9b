import{ab as a,o as t,c as e,w as s,a as o,d as r,f as n,t as i,F as c,b as d,n as l,k as m,l as u,i as p}from"./index-2c18571c.js";import{_ as y}from"./_plugin-vue_export-helper.1b428a4d.js";const h=y({props:{date:{type:String,default:""},separator:{type:String,default:"zh"},theme:{type:String,default:"text"},customBgColor:{type:String,default:"#252525"}},data:()=>({dynamic:{day:"0",hou:"00",min:"00",sec:"00"},separatorText:{day:"天",hou:"时",min:"分",sec:"秒"}}),created(){this.setSeparatorText(),this.onTime()},methods:{setSeparatorText(){const a=this.separatorText;"colon"===this.separator&&(a.day=":",a.hou=a.min=":",a.sec=""),this.separatorText=a},onTime(t=0){const e=this,s={},o=(new Date).getTime(),r=new Date(a(e.date)).getTime();if(r-o<=0)return!1;const n=(r-o)/1e3,i=parseInt(n/86400),c=parseInt(n%86400/3600),d=parseInt(n%86400%3600/60),l=parseInt(n%86400%3600%60);s.day=i,s.hou=e.timeFormat(c),s.min=e.timeFormat(d),s.sec=e.timeFormat(l),e.dynamic=s;const m=e.isEnd();m&&t>0&&e.$emit("finish"),m||setTimeout((()=>{e.onTime(++t)}),100)},isEnd(){const{dynamic:a}=this;return"00"==a.day&&"00"==a.hou&&"00"==a.min&&"00"==a.sec},timeFormat:a=>a<10?"0"+a:a}},[["render",function(a,y,h,f,T,_){const g=u,x=p;return h.date?(t(),e(x,{key:0,class:"count-down"},{default:s((()=>[o(x,{class:m([`${h.theme}-theme`,`separator-${h.separator}`])},{default:s((()=>[T.dynamic.day>0?(t(),r(c,{key:0},[o(g,{class:"dynamic-value"},{default:s((()=>[n(i(T.dynamic.day),1)])),_:1}),o(g,{class:"separator"},{default:s((()=>[n(i(T.separatorText.day),1)])),_:1})],64)):d("",!0),o(g,{class:"dynamic-value",style:l({backgroundColor:h.customBgColor})},{default:s((()=>[n(i(T.dynamic.hou),1)])),_:1},8,["style"]),o(g,{class:"separator"},{default:s((()=>[n(i(T.separatorText.hou),1)])),_:1}),o(g,{class:"dynamic-value",style:l({backgroundColor:h.customBgColor})},{default:s((()=>[n(i(T.dynamic.min),1)])),_:1},8,["style"]),o(g,{class:"separator"},{default:s((()=>[n(i(T.separatorText.min),1)])),_:1}),o(g,{class:"dynamic-value",style:l({backgroundColor:h.customBgColor})},{default:s((()=>[n(i(T.dynamic.sec),1)])),_:1},8,["style"]),o(g,{class:"separator"},{default:s((()=>[n(i(T.separatorText.sec),1)])),_:1})])),_:1},8,["class"])])),_:1})):d("",!0)}],["__scopeId","data-v-bc038875"]]);export{h as C};
