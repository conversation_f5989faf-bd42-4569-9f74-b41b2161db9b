import{v as e,o as t,c as s,w as i,a,k as l,n,B as o,i as d,h as r,f as h,I as u,C as c,m as p,s as m,D as g,G as f,H as b,r as _,t as C,b as k,d as y,e as x,F as v,l as S,g as I}from"./index-Dk2OK-Q2.js";import{_ as T}from"./u-icon.NnBtpP9Z.js";import{r as V}from"./uni-app.es.Bfy34Oxr.js";import{_ as F}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{E as $}from"./index.BwDMJz8h.js";import{R as z}from"./index.kzagRxFh.js";import{P as N}from"./index.ClHwW0WK.js";import{l as w,u as B,c as D}from"./cart.6i6S1wPM.js";import"./index.ahevMWeH.js";import"./u-mask.C7x5h6JE.js";const P=F({name:"u-checkbox",emits:["update:modelValue","input","change"],props:{value:{type:Boolean,default:!1},modelValue:{type:Boolean,default:!1},name:{type:[String,Number],default:""},shape:{type:String,default:""},disabled:{type:[String,Boolean],default:""},labelDisabled:{type:[String,Boolean],default:""},activeColor:{type:String,default:""},iconSize:{type:[String,Number],default:""},labelSize:{type:[String,Number],default:""},size:{type:[String,Number],default:""}},data:()=>({parentDisabled:!1,newParams:{}}),created(){this.parent=this.$u.$parent.call(this,"u-checkbox-group"),this.parent&&this.parent.children.push(this)},computed:{valueCom(){return this.modelValue},isDisabled(){return""!==this.disabled?this.disabled:!!this.parent&&this.parent.disabled},isLabelDisabled(){return""!==this.labelDisabled?this.labelDisabled:!!this.parent&&this.parent.labelDisabled},checkboxSize(){return this.size?this.size:this.parent?this.parent.size:34},checkboxIconSize(){return this.iconSize?this.iconSize:this.parent?this.parent.iconSize:20},elActiveColor(){return this.activeColor?this.activeColor:this.parent?this.parent.activeColor:"primary"},elShape(){return this.shape?this.shape:this.parent?this.parent.shape:"square"},iconStyle(){let e={};return this.elActiveColor&&this.valueCom&&!this.isDisabled&&(e.borderColor=this.elActiveColor,e.backgroundColor=this.elActiveColor),e.width=this.$u.addUnit(this.checkboxSize),e.height=this.$u.addUnit(this.checkboxSize),e},iconColor(){return this.valueCom?"#ffffff":"transparent"},iconClass(){let e=[];return e.push("u-checkbox__icon-wrap--"+this.elShape),1==this.valueCom&&e.push("u-checkbox__icon-wrap--checked"),this.isDisabled&&e.push("u-checkbox__icon-wrap--disabled"),this.valueCom&&this.isDisabled&&e.push("u-checkbox__icon-wrap--disabled--checked"),e.join(" ")},checkboxStyle(){let e={};return this.parent&&this.parent.width&&(e.width=this.parent.width,e.flex=`0 0 ${this.parent.width}`),this.parent&&this.parent.wrap&&(e.width="100%",e.flex="0 0 100%"),e}},mounted(){this._emitEvent()},watch:{valueCom:{handler:function(e,t){this._emitEvent()}}},methods:{_emitEvent(){let e={value:this.valueCom,name:this.name};this.parent&&this.parent.emitEvent&&this.parent.emitEvent(e)},onClickLabel(){this.isLabelDisabled||this.isDisabled||this.setValue()},toggle(){this.isDisabled||this.setValue()},emitEvent(){let e={value:!this.valueCom,name:this.name};this.$emit("change",e),this.parent&&this.parent.emitEvent&&this.parent.emitEvent(e)},setValue(){let e=this.valueCom,t=0;if(this.parent&&this.parent.children&&this.parent.children.map((e=>{e.value&&t++})),1==e)this.emitEvent(),this.$emit("input",!e),this.$emit("update:modelValue",!e);else{if(this.parent&&t>=this.parent.max)return this.$u.toast(`最多可选${this.parent.max}项`);this.emitEvent(),this.$emit("input",!e),this.$emit("update:modelValue",!e)}}}},[["render",function(r,h,u,c,p,m){const g=V(e("u-icon"),T),f=d;return t(),s(f,{class:"u-checkbox",style:n([m.checkboxStyle])},{default:i((()=>[a(f,{class:l(["u-checkbox__icon-wrap",[m.iconClass]]),onClick:m.toggle,style:n([m.iconStyle])},{default:i((()=>[a(g,{class:"u-checkbox__icon-wrap__icon",name:"checkbox-mark",size:m.checkboxIconSize,color:m.iconColor},null,8,["size","color"])])),_:1},8,["onClick","class","style"]),a(f,{class:"u-checkbox__label",onClick:m.onClickLabel,style:n({fontSize:r.$u.addUnit(u.labelSize)})},{default:i((()=>[o(r.$slots,"default",{},void 0,!0)])),_:3},8,["onClick","style"])])),_:3},8,["style"])}],["__scopeId","data-v-486055df"]]);const j=F({name:"u-numberbox",emits:["update:modelValue","input","change","blur","plus","minus"],props:{value:{type:Number,default:1},modelValue:{type:Number,default:1},bgColor:{type:String,default:"#F2F3F5"},min:{type:Number,default:0},max:{type:Number,default:99999},step:{type:Number,default:1},stepFirst:{type:Number,default:0},stepStrictly:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},size:{type:[Number,String],default:26},color:{type:String,default:"#323233"},inputWidth:{type:[Number,String],default:80},inputHeight:{type:[Number,String],default:50},index:{type:[Number,String],default:""},disabledInput:{type:Boolean,default:!1},cursorSpacing:{type:[Number,String],default:100},longPress:{type:Boolean,default:!0},pressTime:{type:[Number,String],default:250},positiveInteger:{type:Boolean,default:!0}},watch:{valueCom(e,t){this.changeFromInner||(this.inputVal=e,this.$nextTick((function(){this.changeFromInner=!1})))},inputVal(e,t){if(""==e)return;let s=0;s=this.isNumber(e)&&e>=this.min&&e<=this.max?e:t,this.positiveInteger&&(e<0||-1!==String(e).indexOf("."))&&(s=t,this.$nextTick((()=>{this.inputVal=t}))),this.handleChange(s,"change")},min(e){void 0!==e&&""!=e&&this.valueCom<e&&this.$emit("input",e)},max(e){void 0!==e&&""!=e&&this.valueCom>e&&this.$emit("input",e)}},data:()=>({inputVal:1,timer:null,changeFromInner:!1,innerChangeTimer:null}),created(){this.inputVal=Number(this.valueCom)},mounted(){},computed:{getCursorSpacing(){return Number(uni.upx2px(this.cursorSpacing))},valueCom(){return this.modelValue}},methods:{btnTouchStart(e){this[e](),this.longPress&&(clearInterval(this.timer),this.timer=null,this.timer=setInterval((()=>{this[e]()}),this.pressTime))},clearTimer(){this.$nextTick((()=>{clearInterval(this.timer),this.timer=null}))},minus(){this.computeVal("minus")},plus(){this.computeVal("plus")},calcPlus(e,t){let s,i,a;try{i=e.toString().split(".")[1].length}catch(l){i=0}try{a=t.toString().split(".")[1].length}catch(l){a=0}return s=Math.pow(10,Math.max(i,a)),((e*s+t*s)/s).toFixed(i>=a?i:a)},calcMinus(e,t){let s,i,a;try{i=e.toString().split(".")[1].length}catch(l){i=0}try{a=t.toString().split(".")[1].length}catch(l){a=0}return s=Math.pow(10,Math.max(i,a)),((e*s-t*s)/s).toFixed(i>=a?i:a)},computeVal(e){if(uni.hideKeyboard(),this.disabled)return;let t=0;if("minus"===e?t=this.stepFirst>0&&this.inputVal==this.stepFirst?this.min:this.calcMinus(this.inputVal,this.step):"plus"===e&&(t=this.stepFirst>0&&this.inputVal<this.stepFirst?this.stepFirst:this.calcPlus(this.inputVal,this.step)),this.stepStrictly){let s=t%this.step;s>0&&(t-=s),this.stepFirst>0&&t>0&&t<this.stepFirst&&("minus"===e?t=0:"plus"===e&&(t=this.stepFirst+(this.step-this.stepFirst%this.step)))}t>this.max?t=this.max:t<this.min&&(t=this.min),this.inputVal=t,this.handleChange(t,e)},onBlur(e){let t=0,s=e.detail.value;if(/(^\d+$)/.test(s)&&0!=s[0]||(t=this.min),t=+s,this.stepFirst>0&&this.inputVal<this.stepFirst&&this.inputVal>0&&(t=this.stepFirst),this.stepStrictly){let e=t%this.step;e>0&&(t-=e),this.stepFirst>0&&t>0&&t<this.stepFirst&&(t=this.stepFirst+(this.step-this.stepFirst%this.step))}t>this.max?t=this.max:t<this.min&&(t=this.min),this.$nextTick((()=>{this.inputVal=t})),this.handleChange(t,"blur")},handleChange(e,t){this.disabled||(this.innerChangeTimer&&(clearTimeout(this.innerChangeTimer),this.innerChangeTimer=null),this.changeFromInner=!0,this.innerChangeTimer=setTimeout((()=>{this.changeFromInner=!1}),150),this.$emit("input",Number(e)),this.$emit("update:modelValue",Number(e)),this.$emit(t,{value:Number(e),index:this.index}))},isNumber:e=>/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(e)}},[["render",function(e,o,c,p,m,g){const f=d,b=u;return t(),s(f,{class:"u-numberbox"},{default:i((()=>[a(f,{class:l(["u-icon-minus",{"u-icon-disabled":c.disabled||m.inputVal<=c.min}]),onTouchstart:o[0]||(o[0]=r((e=>g.btnTouchStart("minus")),["prevent"])),onTouchend:r(g.clearTimer,["stop","prevent"]),style:n({background:c.bgColor,height:c.inputHeight+"rpx",color:c.color,fontSize:c.size+"rpx",minHeight:"1.4em"})},{default:i((()=>[a(f,{style:n("font-size:"+(Number(c.size)+10)+"rpx"),class:"num-btn"},{default:i((()=>[h("－")])),_:1},8,["style"])])),_:1},8,["onTouchend","class","style"]),a(b,{disabled:c.disabledInput||c.disabled,"cursor-spacing":g.getCursorSpacing,class:l([{"u-input-disabled":c.disabled},"u-number-input"]),modelValue:m.inputVal,"onUpdate:modelValue":o[1]||(o[1]=e=>m.inputVal=e),onBlur:g.onBlur,type:"number",style:n({color:c.color,fontSize:c.size+"rpx",background:c.bgColor,height:c.inputHeight+"rpx",width:c.inputWidth+"rpx"})},null,8,["disabled","cursor-spacing","class","modelValue","onBlur","style"]),a(f,{class:l(["u-icon-plus",{"u-icon-disabled":c.disabled||m.inputVal>=c.max}]),onTouchstart:o[2]||(o[2]=r((e=>g.btnTouchStart("plus")),["prevent"])),onTouchend:r(g.clearTimer,["stop","prevent"]),style:n({background:c.bgColor,height:c.inputHeight+"rpx",color:c.color,fontSize:c.size+"rpx",minHeight:"1.4em"})},{default:i((()=>[a(f,{style:n("font-size:"+(Number(c.size)+10)+"rpx"),class:"num-btn"},{default:i((()=>[h("＋")])),_:1},8,["style"])])),_:1},8,["onTouchend","class","style"])])),_:1})}],["__scopeId","data-v-06eb1e00"]]);const L=F({components:{Empty:$,Recommended:z,PromotePopup:N},data:()=>({inArray:c,isLoading:!0,mode:"normal",list:[],total:null,checkedIds:[],totalPrice:"0.00"}),watch:{checkedIds:{handler(e){this.onCalcTotalPrice(),uni.setStorageSync("CartIds",e)},deep:!0,immediate:!1},total(e){p(e),m()}},onShow(){this.checkedIds=uni.getStorageSync("CartIds"),g()?this.getCartList():this.isLoading=!1},methods:{onCalcTotalPrice(){const e=this,t=e.list.filter((t=>c(t.id,e.checkedIds)));let s=0;t.forEach((e=>{const t=100*e.goods.skuInfo.goods_price;s+=t*e.goods_num})),e.totalPrice=(s/100).toFixed(2)},getCartList(){const e=this;e.isLoading=!0,w().then((t=>{e.list=t.data.list,e.total=t.data.cartTotal,e.onClearInvalidId()})).finally((()=>e.isLoading=!1))},onClearInvalidId(){const e=this,t=e.list.map((e=>e.id));e.checkedIds=f(t,e.checkedIds)},handleToggleMode(){this.mode="normal"==this.mode?"edit":"normal"},onChangeStepper({value:e},t){t.goods_num!=e&&(t.debounceHandle||(t.oldValue=t.goods_num,t.debounceHandle=b(this.onUpdateCartNum,500)),t.goods_num=e,t.debounceHandle(t,t.oldValue,e))},onUpdateCartNum(e,t,s){const i=this;B(e.goods_id,e.goods_sku_id,s).then((t=>{i.total=t.data.cartTotal,i.onCalcTotalPrice(),e.debounceHandle=null})).catch((s=>{e.goods_num=t,setTimeout((()=>i.$toast(s.errMsg)),10)}))},onTargetGoods(e){this.$navTo("pages/goods/detail",{goodsId:e})},onTargetIndex(){this.$navTo("pages/index/index")},handleCheckItem(e){const{checkedIds:t}=this,s=t.findIndex((t=>t===e));s<0?t.push(e):t.splice(s,1)},handleCheckAll(){const{checkedIds:e,list:t}=this;this.checkedIds=e.length===t.length?[]:t.map((e=>e.id))},handleOrder(){const e=this;if(e.checkedIds.length){const t=e.checkedIds.join();e.$navTo("pages/checkout/index",{mode:"cart",cartIds:t})}},handleDelete(){const e=this;if(!e.checkedIds.length)return!1;uni.showModal({title:"友情提示",content:"您确定要删除该商品吗？",showCancel:!0,success({confirm:t}){t&&e.onClearCart()}})},onClearCart(){const e=this;D(e.checkedIds).then((t=>{e.getCartList(),e.handleToggleMode()}))}}},[["render",function(o,r,u,c,p,m){const g=S,f=d,b=V(e("u-checkbox"),P),T=I,F=V(e("u-number-box"),j),$=_("empty"),z=_("recommended"),N=_("PromotePopup");return t(),s(f,{class:"container",style:n(o.appThemeStyle)},{default:i((()=>[p.list.length?(t(),s(f,{key:0,class:"head-info"},{default:i((()=>[a(f,{class:"cart-total"},{default:i((()=>[a(g,null,{default:i((()=>[h("共")])),_:1}),a(g,{class:"active"},{default:i((()=>[h(C(p.total),1)])),_:1}),a(g,null,{default:i((()=>[h("件商品")])),_:1})])),_:1}),a(f,{class:"cart-edit",onClick:r[0]||(r[0]=e=>m.handleToggleMode())},{default:i((()=>["normal"==p.mode?(t(),s(f,{key:0,class:"normal"},{default:i((()=>[a(g,{class:"icon iconfont icon-bianji"}),a(g,null,{default:i((()=>[h("编辑")])),_:1})])),_:1})):k("",!0),"edit"==p.mode?(t(),s(f,{key:1,class:"edit"},{default:i((()=>[a(g,null,{default:i((()=>[h("完成")])),_:1})])),_:1})):k("",!0)])),_:1})])),_:1})):k("",!0),p.list.length?(t(),s(f,{key:1,class:"cart-list"},{default:i((()=>[(t(!0),y(v,null,x(p.list,((e,l)=>(t(),s(f,{class:"cart-item",key:l},{default:i((()=>[a(f,{class:"item-radio",onClick:t=>m.handleCheckItem(e.id)},{default:i((()=>[a(b,{modelValue:p.inArray(e.id,p.checkedIds),shape:"circle",activeColor:o.appTheme.mainBg},null,8,["modelValue","activeColor"])])),_:2},1032,["onClick"]),a(f,{class:"goods-image",onClick:t=>m.onTargetGoods(e.goods_id)},{default:i((()=>[a(T,{class:"image",src:e.goods.goods_image,mode:"scaleToFill"},null,8,["src"])])),_:2},1032,["onClick"]),a(f,{class:"item-content"},{default:i((()=>[a(f,{class:"goods-title",onClick:t=>m.onTargetGoods(e.goods_id)},{default:i((()=>[a(g,{class:"twoline-hide"},{default:i((()=>[h(C(e.goods.goods_name),1)])),_:2},1024)])),_:2},1032,["onClick"]),a(f,{class:"goods-props clearfix"},{default:i((()=>[(t(!0),y(v,null,x(e.goods.skuInfo.goods_props,((e,l)=>(t(),s(f,{class:"goods-props-item",key:l},{default:i((()=>[a(g,null,{default:i((()=>[h(C(e.value.name),1)])),_:2},1024)])),_:2},1024)))),128))])),_:2},1024),a(f,{class:"item-foot"},{default:i((()=>[a(f,{class:"goods-price"},{default:i((()=>[a(g,{class:"unit"},{default:i((()=>[h("￥")])),_:1}),a(g,{class:"value"},{default:i((()=>[h(C(e.goods.skuInfo.goods_price),1)])),_:2},1024)])),_:2},1024),a(f,{class:"stepper"},{default:i((()=>[a(F,{min:1,modelValue:e.goods_num,step:1,onChange:t=>m.onChangeStepper(t,e)},null,8,["modelValue","onChange"])])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):k("",!0),p.list.length?k("",!0):(t(),s($,{key:2,isLoading:p.isLoading,"custom-style":{padding:"180rpx 50rpx"},tips:"您的购物车是空的, 快去逛逛吧"},{slot:i((()=>[a(f,{class:"empty-ipt",onClick:r[1]||(r[1]=e=>m.onTargetIndex())},{default:i((()=>[a(g,null,{default:i((()=>[h("去逛逛")])),_:1})])),_:1})])),_:1},8,["isLoading"])),a(z),a(N),p.list.length?(t(),s(f,{key:3,class:"footer-fixed"},{default:i((()=>[a(f,{class:"all-radio"},{default:i((()=>[a(b,{modelValue:p.checkedIds.length>0&&p.checkedIds.length===p.list.length,shape:"circle",activeColor:o.appTheme.mainBg,onChange:r[2]||(r[2]=e=>m.handleCheckAll())},{default:i((()=>[h("全选")])),_:1},8,["modelValue","activeColor"])])),_:1}),a(f,{class:"total-info"},{default:i((()=>[a(g,null,{default:i((()=>[h("合计：")])),_:1}),a(f,{class:"goods-price"},{default:i((()=>[a(g,{class:"unit"},{default:i((()=>[h("￥")])),_:1}),a(g,{class:"value"},{default:i((()=>[h(C(p.totalPrice),1)])),_:1})])),_:1})])),_:1}),a(f,{class:"cart-action"},{default:i((()=>[a(f,{class:"btn-wrapper"},{default:i((()=>["normal"==p.mode?(t(),s(f,{key:0,class:l(["btn-item btn-main",{disabled:""==p.checkedIds.join()}]),onClick:r[3]||(r[3]=e=>m.handleOrder())},{default:i((()=>[a(g,null,{default:i((()=>[h("去结算 "+C(p.checkedIds.length>0?`(${p.total})`:""),1)])),_:1})])),_:1},8,["class"])):k("",!0),"edit"==p.mode?(t(),s(f,{key:1,class:l(["btn-item btn-main",{disabled:!p.checkedIds.length}]),onClick:r[4]||(r[4]=e=>m.handleDelete())},{default:i((()=>[a(g,null,{default:i((()=>[h("删除")])),_:1})])),_:1},8,["class"])):k("",!0)])),_:1})])),_:1})])),_:1})):k("",!0)])),_:1},8,["style"])}],["__scopeId","data-v-ed553a25"]]);export{L as default};
