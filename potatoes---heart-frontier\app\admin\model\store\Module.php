<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\admin\model\store;

use app\common\model\store\Module as ModuleModel;

/**
 * 商家功能模块模型
 * Class Module
 * @package app\admin\model\store
 */
class Module extends ModuleModel
{
    /**
     * 获取指定商家的功能模块
     * @param int $storeId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getConfigValue(int $storeId): array
    {
        $detail = static::getDetail($storeId);
        if (empty($detail['config'])) {
            $defaultConfig = self::getDefaultConfig();
            return self::getDefaultConfigKeys($defaultConfig);
        }
        return $detail['config'];
    }

    /**
     * 获取功能模块详情
     * @param int $storeId
     * @return Module|array|static
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getDetail(int $storeId)
    {
        $detail = static::detail($storeId);
        if (!empty($detail)) {
            return $detail;
        }
        $model = new static();
        $model['store_id'] = $storeId;
        return $model;
    }

    /**
     * 更新记录
     * @param array $data
     * @return bool
     */
    public function edit(array $data): bool
    {
        return $this->save(['config' => $data['moduleKeys']]);
    }
}
