(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["market"],{"052a":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form-model",{ref:"myForm",staticClass:"my-form",attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-form-model-item",{attrs:{label:"开启商品推荐",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-radio-group",{model:{value:e.record.enabled,callback:function(t){e.$set(e.record,"enabled",t)},expression:"record.enabled"}},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("p",{staticClass:"form-item-help"},[t("small",[e._v("开启后将在用户端购物车页底部、个人中心页底部显示推荐的商品列表")])])],1),t("a-form-model-item",{attrs:{label:"商品来源",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-radio-group",{model:{value:e.record.params.source,callback:function(t){e.$set(e.record.params,"source",t)},expression:"record.params.source"}},[t("a-radio",{attrs:{value:"auto"}},[e._v("自动获取")]),t("a-radio",{attrs:{value:"choice"}},[e._v("手动选择")])],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"auto"===e.record.params.source,expression:"record.params.source === 'auto'"}]},[t("a-form-model-item",{attrs:{label:"商品分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("SelectCategory",{model:{value:e.record.params.auto.category,callback:function(t){e.$set(e.record.params.auto,"category",t)},expression:"record.params.auto.category"}})],1),t("a-form-model-item",{attrs:{label:"商品排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-radio-group",{model:{value:e.record.params.auto.goodsSort,callback:function(t){e.$set(e.record.params.auto,"goodsSort",t)},expression:"record.params.auto.goodsSort"}},[t("a-radio",{attrs:{value:"all"}},[e._v("默认")]),t("a-radio",{attrs:{value:"sales"}},[e._v("销量")]),t("a-radio",{attrs:{value:"price"}},[e._v("价格")])],1)],1),t("a-form-model-item",{attrs:{label:"显示数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input-number",{attrs:{min:0,max:50,autocomplete:"off"},model:{value:e.record.params.auto.showNum,callback:function(t){e.$set(e.record.params.auto,"showNum",t)},expression:"record.params.auto.showNum"}}),t("span",{staticClass:"ml-10"},[e._v("件")])],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"choice"===e.record.params.source,expression:"record.params.source === 'choice'"}]},[t("a-form-model-item",{attrs:{label:"选择的商品",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("SelectGoods",{attrs:{defaultList:e.choiceGoodsList},model:{value:e.record.params.goodsIds,callback:function(t){e.$set(e.record.params,"goodsIds",t)},expression:"record.params.goodsIds"}})],1)],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("样式设置")]),t("a-form-model-item",{attrs:{label:"标题内容",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-input",{model:{value:e.record.style.title,callback:function(t){e.$set(e.record.style,"title",t)},expression:"record.style.title"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("例如：商品推荐、精选好物、为你推荐")])])],1),t("a-form-model-item",{attrs:{label:"商品分列",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-radio-group",{model:{value:e.record.style.column,callback:function(t){e.$set(e.record.style,"column",t)},expression:"record.style.column"}},[t("a-radio",{attrs:{value:1}},[e._v("单列")]),t("a-radio",{attrs:{value:2}},[e._v("两列")]),t("a-radio",{attrs:{value:3}},[e._v("三列")])],1)],1),t("a-form-model-item",{attrs:{label:"商品内容",labelCol:e.labelCol,wrapperCol:e.wrapperCol,required:""}},[t("a-checkbox-group",{model:{value:e.record.style.show,callback:function(t){e.$set(e.record.style,"show",t)},expression:"record.style.show"}},[t("a-checkbox",{attrs:{value:"goodsName"}},[e._v("商品名称")]),t("a-checkbox",{attrs:{value:"goodsPrice"}},[e._v("商品价格")]),t("a-checkbox",{attrs:{value:"linePrice"}},[e._v("划线价格")]),t("a-checkbox",{directives:[{name:"show",rawName:"v-show",value:1===e.record.style.column,expression:"record.style.column === 1"}],attrs:{value:"sellingPoint"}},[e._v("商品卖点")]),t("a-checkbox",{directives:[{name:"show",rawName:"v-show",value:1===e.record.style.column,expression:"record.style.column === 1"}],attrs:{value:"goodsSales"}},[e._v("商品销量")])],1)],1),t("a-form-model-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary",loading:e.confirmLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)],1)},o=[],n=a("c7eb"),i=a("1da1"),s=(a("d3b7"),a("ddb0"),a("2ef0")),l=a("f585"),u=a("d084"),c=a("2af9"),d=a("35c4"),m={enabled:1,params:{source:"auto",auto:{category:0,goodsSort:"all",showNum:6},goodsIds:[]},style:{title:"",column:2,show:["goodsName","goodsPrice","linePrice","sellingPoint","goodsSales"]}},p={components:{SelectGoods:c["g"],SelectCategory:c["e"]},data:function(){return{key:d["a"].RECOMMENDED.value,labelCol:{span:4},wrapperCol:{span:12},isLoading:!1,confirmLoading:!1,record:Object(s["cloneDeep"])(m),choiceGoodsList:[]}},created:function(){var e=this;return Object(i["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getDetail();case 2:return t.next=4,e.getChoiceGoodsList();case 4:case"end":return t.stop()}}),t)})))()},methods:{getDetail:function(){var e=this;return Object(i["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,l["a"](e.key).then((function(t){return e.record=t.data.values})).finally((function(){return e.isLoading=!1}));case 3:case"end":return t.stop()}}),t)})))()},getChoiceGoodsList:function(){var e=this;return Object(i["a"])(Object(n["a"])().mark((function t(){var a;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=e.record.params.goodsIds,!(a.length>0)){t.next=5;break}return e.isLoading=!0,t.next=5,u["g"](a).then((function(t){return e.choiceGoodsList=t.data.list})).finally((function(){return e.isLoading=!1}));case 5:case"end":return t.stop()}}),t)})))()},handleSubmit:function(e){var t=this;this.confirmLoading=!0,l["b"](this.key,{form:this.record}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(e){return t.confirmLoading=!1}))}}},f=p,v=(a("7e12f"),a("2877")),b=Object(v["a"])(f,r,o,!1,null,"58595866",null);t["default"]=b.exports},"053e":function(e,t,a){},"072e":function(e,t,a){},"0ab7":function(e,t,a){},"0b60":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"优惠券名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入优惠券名称"}})],1),t("a-form-item",{attrs:{label:"优惠券类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["coupon_type",{initialValue:10,rules:[{required:!0}]}],expression:"['coupon_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("满减券")]),t("a-radio",{attrs:{value:20}},[e._v("折扣券")])],1)],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("coupon_type")==e.CouponTypeEnum.FULL_DISCOUNT.value,expression:"form.getFieldValue('coupon_type') ==  CouponTypeEnum.FULL_DISCOUNT.value"}],attrs:{label:"减免金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["reduce_price",{rules:[{required:!0,message:"请输入减免金额"}]}],expression:"['reduce_price', { rules: [{ required: true, message: '请输入减免金额' }] }]"}],attrs:{min:.01,precision:2}}),t("span",{staticClass:"ml-5"},[e._v("元")])],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:e.form.getFieldValue("coupon_type")==e.CouponTypeEnum.DISCOUNT.value,expression:"form.getFieldValue('coupon_type') == CouponTypeEnum.DISCOUNT.value"}],attrs:{label:"折扣率",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["discount",{initialValue:9.9,rules:[{required:!0,message:"请输入折扣率"}]}],expression:"['discount', { initialValue: 9.9, rules: [{ required: true, message: '请输入折扣率' }] }]"}],attrs:{min:0,max:9.9,precision:1}}),t("span",{staticClass:"ml-5"},[e._v("%")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("折扣率范围 0-9.9，8代表打8折，0代表不折扣")])])],1),t("a-form-item",{attrs:{label:"最低消费金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["min_price",{rules:[{required:!0,message:"请输入最低消费金额"}]}],expression:"['min_price', { rules: [{ required: true, message: '请输入最低消费金额' }] }]"}],attrs:{min:1,precision:2}}),t("span",{staticClass:"ml-5"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"到期类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["expire_type",{initialValue:10,rules:[{required:!0}]}],expression:"['expire_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("领取后生效")]),t("a-radio",{attrs:{value:20}},[e._v("固定时间")])],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:10==e.form.getFieldValue("expire_type"),expression:"form.getFieldValue('expire_type') == 10"}],staticClass:"expire_type-10"},[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["expire_day",{initialValue:7,rules:[{required:!0,message:"请输入有效期天数"}]}],expression:"['expire_day', { initialValue: 7, rules: [{ required: true, message: '请输入有效期天数' }] }]"}],attrs:{addonBefore:"有效期",addonAfter:"天",inputProps:{min:1,precision:0}}})],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:20==e.form.getFieldValue("expire_type"),expression:"form.getFieldValue('expire_type') == 20"}],staticClass:"expire_type-20"},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime",{initialValue:e.defaultDate,rules:[{required:!0,message:"请选择有效期范围"}]}],expression:"['betweenTime', { initialValue: defaultDate, rules: [{ required: true, message: '请选择有效期范围' }] }]"}],attrs:{format:"YYYY-MM-DD"}})],1)],1),t("a-form-item",{attrs:{label:"券适用范围",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["apply_range",{initialValue:10,rules:[{required:!0}]}],expression:"['apply_range', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("全场通用")]),t("a-radio",{attrs:{value:20}},[e._v("指定商品")])],1),20==e.form.getFieldValue("apply_range")?t("a-form-item",[t("SelectGoods",{directives:[{name:"decorator",rawName:"v-decorator",value:["apply_range_config.applyGoodsIds",{rules:[{required:!0,message:"请选择指定的商品"}]}],expression:"['apply_range_config.applyGoodsIds', { rules: [{ required: true, message: '请选择指定的商品' }] }]"}],attrs:{defaultList:e.containGoodsList}})],1):e._e()],1),t("a-form-item",{attrs:{label:"发放总数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["total_num",{initialValue:-1,rules:[{required:!0,message:"请输入发放总数量"}]}],expression:"['total_num', { initialValue: -1, rules: [{ required: true, message: '请输入发放总数量' }] }]"}],attrs:{min:-1,precision:0}}),t("span",{staticClass:"ml-5"},[e._v("张")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("发放的优惠券总数量，-1为不限制")])])],1),t("a-form-item",{attrs:{label:"显示状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("显示")]),t("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1),t("p",{staticClass:"form-item-help"},[t("small",[e._v("如果设为隐藏将不会展示在用户端页面")])])],1),t("a-form-item",{attrs:{label:"优惠券描述",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["describe"],expression:"['describe']"}],attrs:{autoSize:{minRows:4}}})],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1),t("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1)],1)],1)},o=[],n=a("c7eb"),i=a("1da1"),s=(a("d3b7"),a("c1df")),l=a.n(s),u=a("2ef0"),c=a("39ad9"),d=a("d084"),m=a("ca00"),p=a("2af9"),f=a("8fa3"),v={components:{SelectGoods:p["g"],InputNumberGroup:p["c"]},data:function(){return{ApplyRangeEnum:f["a"],CouponTypeEnum:f["b"],ExpireTypeEnum:f["c"],isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},form:this.$form.createForm(this),defaultDate:[l()(),l()()],couponId:null,record:{},containGoodsList:[]}},created:function(){var e=this;return Object(i["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.couponId=e.$route.query.couponId,t.next=3,e.getDetail();case 3:return t.next=5,e.getContainGoodsList();case 5:case"end":return t.stop()}}),t)})))()},methods:{getDetail:function(){var e=this;return Object(i["a"])(Object(n["a"])().mark((function t(){var a;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=e.couponId,e.isLoading=!0,t.next=4,c["c"]({couponId:a}).then((function(t){e.record=t.data.detail,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}));case 4:case"end":return t.stop()}}),t)})))()},getContainGoodsList:function(){var e=this;return Object(i["a"])(Object(n["a"])().mark((function t(){var a,r;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=e.record,r=Object(u["get"])(a,"apply_range_config.applyGoodsIds"),void 0===r||!r.length){t.next=6;break}return e.isLoading=!0,t.next=6,d["g"](r).then((function(t){e.containGoodsList=t.data.list})).finally((function(t){e.isLoading=!1}));case 6:case"end":return t.stop()}}),t)})))()},setFieldsValue:function(){var e=this,t=this.record,a=this.form,r=this.$nextTick;!Object(m["g"])(a.getFieldsValue())&&r((function(){var r=Object(u["pick"])(t,["name","coupon_type","reduce_price","discount","min_price","status","expire_type","expire_day","apply_range","total_num","describe","sort"]);r.betweenTime=e.getBetweenTime(t),a.setFieldsValue(r)}))},getBetweenTime:function(e){return e.expire_type===f["c"].FIXED_TIME.value?[l()(new Date(e.start_time)),l()(new Date(e.end_time))]:this.defaultDate},handleSubmit:function(e){e.preventDefault();var t=this.form.validateFields,a=this.onFormSubmit;t((function(e,t){!e&&a(t)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,this.isBtnLoading=!0,c["d"]({couponId:this.couponId,form:e}).then((function(e){t.$message.success(e.message,1.5),setTimeout((function(){t.$router.push("./index")}),1500)})).catch((function(){t.isBtnLoading=!1})).finally((function(){return t.isLoading=!1}))}}},b=v,h=(a("5a40"),a("2877")),g=Object(h["a"])(b,r,o,!1,null,"5cf7d6db",null);t["default"]=g.exports},"0e9c":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"是否开启满额包邮",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_open",{rules:[{required:!0}]}],expression:"['is_open', { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),t("a-form-item",{attrs:{label:"单笔订单满",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["money",{rules:[{required:!0,message:"请输入包邮的订单额度"}]}],expression:"['money', { rules: [{ required: true, message: '请输入包邮的订单额度' }] }]"}],attrs:{min:0,precision:2}}),t("span",{staticClass:"ml-10"},[e._v("元包邮")]),t("div",{staticClass:"form-item-help"},[t("small",[e._v("如设置0为全场包邮")])])],1),t("a-form-item",{attrs:{label:"不参与包邮的商品",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("SelectGoods",{directives:[{name:"decorator",rawName:"v-decorator",value:["excludedGoodsIds"],expression:"['excludedGoodsIds']"}],attrs:{defaultList:e.excludedGoodsList}})],1),t("a-form-item",{attrs:{label:"不参与包邮的地区",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-button",{on:{click:e.handleAreasModal}},[e._v("选择地区")]),t("p",{staticClass:"content"},e._l(e.excludedRegions.selectedText,(function(a,r){return t("span",{key:r},[t("span",[e._v(e._s(a.name))]),a.citys.length?[t("span",[e._v("[")]),e._l(a.citys,(function(r,o){return t("span",{key:o,staticClass:"city-name"},[e._v(e._s(r.name)+e._s(a.citys.length>o+1?"、":""))])})),t("span",[e._v("]")])]:e._e(),t("span",{staticClass:"mr-5"})],2)})),0)],1),t("a-form-item",{attrs:{label:"满额包邮说明",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["describe",{rules:[{required:!0,message:"请输入满额包邮说明"}]}],expression:"['describe', { rules: [{ required: true, message: '请输入满额包邮说明' }] }]"}],attrs:{autoSize:{minRows:4}}})],1),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1),t("AreasModal",{ref:"AreasModal",on:{handleSubmit:e.handleAreaSubmit}})],1)],1)},o=[],n=a("c7eb"),i=a("1da1"),s=(a("d3b7"),a("ddb0"),a("88bc")),l=a.n(s),u=a("f585"),c=a("d084"),d=a("2af9"),m=a("fd0d"),p={components:{SelectGoods:d["g"],AreasModal:m["a"]},data:function(){return{key:"full_free",labelCol:{span:4},wrapperCol:{span:12},isLoading:!1,form:this.$form.createForm(this),record:{},excludedRegions:{cityIds:[],selectedText:[]},excludedGoodsList:[]}},created:function(){var e=this;return Object(i["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getDetail();case 2:return t.next=4,e.getExcludedGoodsList();case 4:case"end":return t.stop()}}),t)})))()},methods:{getDetail:function(){var e=this;return Object(i["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,u["a"](e.key).then((function(t){e.record=t.data.values,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}));case 3:case"end":return t.stop()}}),t)})))()},getExcludedGoodsList:function(){var e=this;return Object(i["a"])(Object(n["a"])().mark((function t(){var a;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=e.record.excludedGoodsIds,!(a.length>0)){t.next=5;break}return e.isLoading=!0,t.next=5,c["g"](a).then((function(t){e.excludedGoodsList=t.data.list})).finally((function(t){e.isLoading=!1}));case 5:case"end":return t.stop()}}),t)})))()},setFieldsValue:function(){var e=this,t=this.record,a=this.$nextTick,r=this.form.setFieldsValue;a((function(){e.excludedRegions=t.excludedRegions,r(l()(t,["is_open","money","describe"]))}))},handleAreasModal:function(){this.$refs.AreasModal.handle({},this.excludedRegions.cityIds)},handleAreaSubmit:function(e){this.excludedRegions={cityIds:e.selectedCityIds,selectedText:e.selectedText}},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields,r=this.excludedRegions;a((function(e,a){e||(a.excludedRegions=r,t.onFormSubmit(a))}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,u["b"](this.key,{form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},f=p,v=(a("e0ac"),a("2877")),b=Object(v["a"])(f,r,o,!1,null,"c094d56a",null);t["default"]=b.exports},"164a":function(e,t,a){"use strict";a.r(t);a("ac1f"),a("841c"),a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-alert",{attrs:{message:"注：优惠券只能抵扣商品金额，最多优惠到0.01元，不能抵扣运费",banner:""}}),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{attrs:{span:6}},[e.$auth("/market/coupon/create")?t("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]):e._e()],1),t("a-col",{attrs:{span:8,offset:10}},[t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px",float:"right"},attrs:{placeholder:"请输入优惠券名称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"coupon_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"coupon_type",fn:function(a){return[t("a-tag",[e._v(e._s(e.CouponTypeEnum[a].name))])]}},{key:"min_price",fn:function(a){return[t("p",{staticClass:"c-p"},[e._v(e._s(a))])]}},{key:"discount",fn:function(a){return[10==a.coupon_type?[t("span",[e._v("立减")]),t("span",{staticClass:"c-p mlr-2"},[e._v(e._s(a.reduce_price))]),t("span",[e._v("元")])]:e._e(),20==a.coupon_type?[t("span",[e._v("打")]),t("span",{staticClass:"c-p mlr-2"},[e._v(e._s(a.discount))]),t("span",[e._v("折")])]:e._e()]}},{key:"duetime",fn:function(a){return[10==a.expire_type?[t("span",[e._v("领取")]),t("span",{staticClass:"c-p mlr-2"},[e._v(e._s(a.expire_day))]),t("span",[e._v("天内有效")])]:e._e(),20==a.expire_type?[t("span",[e._v(e._s(a.start_time)+" ~ "+e._s(a.end_time))])]:e._e()]}},{key:"status",fn:function(a){return[t("a-tag",{attrs:{color:a?"green":""}},[e._v(e._s(a?"显示":"隐藏"))])]}},{key:"action",fn:function(a){return t("span",{staticClass:"actions"},[e.$auth("/market/coupon/update")?t("a",{on:{click:function(t){return e.handleEdit(a)}}},[e._v("编辑")]):e._e(),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(a)}}},[e._v("删除")])])}}])})],1)},o=[],n=a("5530"),i=(a("d3b7"),a("39ad9")),s=a("2af9"),l=a("8fa3"),u={name:"Index",components:{STable:s["d"]},data:function(){var e=this;return{queryParam:{},ApplyRangeEnum:l["a"],CouponTypeEnum:l["b"],ExpireTypeEnum:l["c"],isLoading:!1,columns:[{title:"优惠券ID",dataIndex:"coupon_id"},{title:"优惠券名称",dataIndex:"name"},{title:"优惠券类型",dataIndex:"coupon_type",scopedSlots:{customRender:"coupon_type"}},{title:"最低消费金额 (元)",dataIndex:"min_price",scopedSlots:{customRender:"min_price"}},{title:"优惠方式",scopedSlots:{customRender:"discount"}},{title:"已发放/领取数量",dataIndex:"receive_num"},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"操作",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(t){return i["f"](Object(n["a"])(Object(n["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$router.push("./create")},handleEdit:function(e){this.$router.push({path:"./update",query:{couponId:e.coupon_id}})},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return i["b"]({couponId:e.coupon_id}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},c=u,d=a("2877"),m=Object(d["a"])(c,r,o,!1,null,null,null);t["default"]=m.exports},"1da1":function(e,t,a){"use strict";a.d(t,"a",(function(){return o}));a("d3b7");function r(e,t,a,r,o,n,i){try{var s=e[n](i),l=s.value}catch(u){return void a(u)}s.done?t(l):Promise.resolve(l).then(r,o)}function o(e){return function(){var t=this,a=arguments;return new Promise((function(o,n){var i=e.apply(t,a);function s(e){r(i,o,n,s,l,"next",e)}function l(e){r(i,o,n,s,l,"throw",e)}s(void 0)}))}}},2640:function(e,t,a){"use strict";a("9aea")},3095:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"是否开启会员充值",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_entrance",{rules:[{required:!0}]}],expression:"['is_entrance', { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("如设置关闭则用户端不显示充值按钮")])])],1),t("a-form-item",{attrs:{label:"充值自定义金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_custom",{rules:[{required:!0}]}],expression:"['is_custom', { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("允许")]),t("a-radio",{attrs:{value:0}},[e._v("不允许")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("是否允许用户填写自定义的充值金额")])])],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:1==e.form.getFieldValue("is_custom"),expression:"form.getFieldValue('is_custom') == 1"}],attrs:{label:"最低充值金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["lowest_money",{rules:[{required:!0,message:"请输入最低充值金额"}]}],expression:"['lowest_money', { rules: [{ required: true, message: '请输入最低充值金额' }] }]"}],attrs:{min:.01,precision:2}}),t("span",{staticClass:"ml-10"},[e._v("元")]),t("div",{staticClass:"form-item-help"},[t("small",[e._v("低于该设定金额时不允许充值")])])],1),t("a-form-item",{attrs:{label:"自动匹配套餐",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_match_plan",{rules:[{required:!0}]}],expression:"['is_match_plan', { rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("充值自定义金额时 是否自动匹配充值套餐，如不开启则不参与套餐金额赠送")])])],1),t("a-form-item",{attrs:{label:"充值说明",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["describe",{rules:[{required:!0,message:"请输入充值说明"}]}],expression:"['describe', { rules: [{ required: true, message: '请输入充值说明' }] }]"}],attrs:{autoSize:{minRows:4}}})],1),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},o=[],n=(a("d3b7"),a("ddb0"),a("88bc")),i=a.n(n),s=a("f585"),l={components:{},data:function(){return{key:"recharge",labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),record:{}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,s["a"](this.key).then((function(t){e.record=t.data.values,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,t=this.$nextTick,a=this.form.setFieldsValue;t((function(){a(i()(e,["is_entrance","is_custom","lowest_money","is_match_plan","describe"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,s["b"](this.key,{form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},u=l,c=(a("d36d"),a("2877")),d=Object(c["a"])(u,r,o,!1,null,"6c768cd3",null);t["default"]=d.exports},"374b":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",{staticClass:"row-item-search"},[t("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[t("a-form-item",{attrs:{label:"会员昵称"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["search"],expression:"['search']"}],attrs:{placeholder:"请输入会员昵称"}})],1),t("a-form-item",{attrs:{label:"变动时间"}},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime"],expression:"['betweenTime']"}],attrs:{format:"YYYY-MM-DD"}})],1),t("a-form-item",{staticClass:"search-btn"},[t("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"log_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"user",fn:function(e){return t("span",{},[t("UserItem",{attrs:{user:e}})],1)}},{key:"value",fn:function(a){return t("span",{},[t("p",{staticClass:"c-p"},[e._v(e._s(a>0?"+":"")+e._s(a))])])}}])})],1)},o=[],n=a("5530"),i=a("b775"),s={log:"/market.points/log"};function l(e){return Object(i["b"])({url:s.log,method:"get",params:e})}var u=a("ab09"),c=a("fe7e"),d={name:"Index",components:{STable:u["b"],UserItem:u["c"]},data:function(){var e=this;return{SceneEnum:c["a"],searchForm:this.$form.createForm(this),queryParam:{},isLoading:!1,columns:[{title:"ID",dataIndex:"log_id"},{title:"会员信息",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"变动数量",dataIndex:"value",scopedSlots:{customRender:"value"}},{title:"描述/说明",dataIndex:"describe"},{title:"管理员备注",dataIndex:"remark"},{title:"变动时间",dataIndex:"create_time"}],loadData:function(t){return l(Object(n["a"])(Object(n["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},handleSearch:function(e){var t=this;e.preventDefault(),this.searchForm.validateFields((function(e,a){e||(t.queryParam=Object(n["a"])(Object(n["a"])({},t.queryParam),a),t.handleRefresh(!0))}))}}},m=d,p=(a("701c"),a("2877")),f=Object(p["a"])(m,r,o,!1,null,"0d4db7ff",null);t["default"]=f.exports},"3a16":function(e,t,a){},"3d2f":function(e,t,a){"use strict";a.r(t);a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",{staticClass:"row-item-search"},[t("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[t("a-form-item",{attrs:{label:"优惠券名称"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["couponName"],expression:"['couponName']"}],attrs:{placeholder:"请输入优惠券名称"}})],1),t("a-form-item",{attrs:{label:"会员昵称"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["nickName"],expression:"['nickName']"}],attrs:{placeholder:"请输入会员昵称"}})],1),t("a-form-item",{attrs:{label:"领取时间"}},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime"],expression:"['betweenTime']"}],attrs:{format:"YYYY-MM-DD"}})],1),t("a-form-item",{staticClass:"search-btn"},[t("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"user_coupon_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"user",fn:function(e){return[t("UserItem",{attrs:{user:e}})]}},{key:"coupon_type",fn:function(a){return[t("a-tag",[e._v(e._s(e.CouponTypeEnum[a].name))])]}},{key:"min_price",fn:function(a){return[t("p",{staticClass:"c-p"},[e._v(e._s(a))])]}},{key:"discount",fn:function(a){return[a.coupon_type==e.CouponTypeEnum.FULL_DISCOUNT.value?[t("span",[e._v("立减")]),t("span",{staticClass:"c-p mlr-2"},[e._v(e._s(a.reduce_price))]),t("span",[e._v("元")])]:e._e(),a.coupon_type==e.CouponTypeEnum.DISCOUNT.value?[t("span",[e._v("打")]),t("span",{staticClass:"c-p mlr-2"},[e._v(e._s(a.discount))]),t("span",[e._v("折")])]:e._e()]}},{key:"duetime",fn:function(a){return[10==a.expire_type?[t("span",[e._v("领取")]),t("span",{staticClass:"c-p mlr-2"},[e._v(e._s(a.expire_day))]),t("span",[e._v("天内有效")])]:e._e(),20==a.expire_type?[t("span",[e._v(e._s(a.start_time)+" ~ "+e._s(a.end_time))])]:e._e()]}}])})],1)},o=[],n=a("5530"),i=a("39ad9"),s=a("ab09"),l=a("8fa3"),u={name:"Index",components:{STable:s["b"],UserItem:s["c"]},data:function(){var e=this;return{searchForm:this.$form.createForm(this),queryParam:{},CouponTypeEnum:l["b"],isLoading:!1,columns:[{title:"ID",dataIndex:"user_coupon_id"},{title:"会员信息",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"优惠券名称",dataIndex:"name"},{title:"优惠券类型",dataIndex:"coupon_type",scopedSlots:{customRender:"coupon_type"}},{title:"最低消费金额 (元)",dataIndex:"min_price",scopedSlots:{customRender:"min_price"}},{title:"优惠方式",scopedSlots:{customRender:"discount"}},{title:"有效期",scopedSlots:{customRender:"duetime"}},{title:"领取时间",dataIndex:"create_time"}],loadData:function(t){return i["g"](Object(n["a"])(Object(n["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},handleSearch:function(e){var t=this;e.preventDefault(),this.searchForm.validateFields((function(e,a){e||(t.queryParam=Object(n["a"])(Object(n["a"])({},t.queryParam),a),t.handleRefresh(!0))}))}}},c=u,d=a("2877"),m=Object(d["a"])(c,r,o,!1,null,null,null);t["default"]=m.exports},"43c9":function(e,t,a){"use strict";a.r(t);a("b0c0"),a("4e82");var r,o=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form-model",{ref:"myForm",staticClass:"my-form",attrs:{model:e.formData,labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-form-model-item",{attrs:{label:"活动名称",prop:"name",rules:{required:!0,message:"请输入活动名称"}}},[t("a-input",{model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1),t("a-form-model-item",{attrs:{label:"推送内容",prop:"content_type",rules:{required:!0,message:"请选择推送内容"}}},[t("a-radio-group",{model:{value:e.formData.content_type,callback:function(t){e.$set(e.formData,"content_type",t)},expression:"formData.content_type"}},e._l(e.ContentTypeEnum.data,(function(a,r){return t("a-radio",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1)],1),e.formData.content_type==e.ContentTypeEnum.SWIPER.value?t("a-form-model-item",{attrs:{label:"间隔时间",prop:"content_config[".concat(e.ContentTypeEnum.SWIPER.value,"].interval"),rules:{required:!0,message:"请输入间隔时间"}}},[t("a-input-number",{attrs:{min:1,max:120,precision:0},model:{value:e.formData.content_config[e.ContentTypeEnum.SWIPER.value].interval,callback:function(t){e.$set(e.formData.content_config[e.ContentTypeEnum.SWIPER.value],"interval",t)},expression:"formData.content_config[ContentTypeEnum.SWIPER.value].interval"}}),t("span",{staticClass:"ml-8"},[e._v("秒钟后切换")])],1):e._e(),t("a-form-model-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[e.formData.content_type==e.ContentTypeEnum.SINGLE.value?t("div",[t("AdPicture",{model:{value:e.formData.content_config[e.ContentTypeEnum.SINGLE.value],callback:function(t){e.$set(e.formData.content_config,e.ContentTypeEnum.SINGLE.value,t)},expression:"formData.content_config[ContentTypeEnum.SINGLE.value]"}})],1):e._e(),e.formData.content_type==e.ContentTypeEnum.SWIPER.value?t("div",[t("Swiper",{model:{value:e.formData.content_config[e.ContentTypeEnum.SWIPER.value].adList,callback:function(t){e.$set(e.formData.content_config[e.ContentTypeEnum.SWIPER.value],"adList",t)},expression:"formData.content_config[ContentTypeEnum.SWIPER.value].adList"}})],1):e._e()]),t("a-form-model-item",{attrs:{label:"弹窗关闭方式",prop:"close_mode",rules:{required:!0,message:"请选择弹窗关闭方式"}}},[t("a-radio-group",{model:{value:e.formData.close_mode,callback:function(t){e.$set(e.formData,"close_mode",t)},expression:"formData.close_mode"}},e._l(e.CloseModeEnum.data,(function(a,r){return t("a-radio",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1)],1),t("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:e.formData.close_mode==e.CloseModeEnum.AUTO.value,expression:"formData.close_mode == CloseModeEnum.AUTO.value"}],attrs:{label:"倒计时",prop:"seconds",rules:{required:!0,message:"请输入倒计时"}}},[t("a-input-number",{attrs:{min:2,max:120,precision:0},model:{value:e.formData.seconds,callback:function(t){e.$set(e.formData,"seconds",t)},expression:"formData.seconds"}}),t("span",{staticClass:"ml-8"},[e._v("秒钟后自动关闭弹窗")])],1),t("a-form-model-item",{attrs:{label:"投放的页面",prop:"pages",rules:{required:!0,message:"请至少选择1个投放的页面"}}},[t("a-checkbox-group",{model:{value:e.formData.pages,callback:function(t){e.$set(e.formData,"pages",t)},expression:"formData.pages"}},e._l(e.PagesEnum,(function(a,r){return t("a-checkbox",{key:r,attrs:{value:a.path}},[e._v(e._s(a.name))])})),1)],1),t("a-form-model-item",{attrs:{label:"推荐频次",prop:"frequency",rules:{required:!0,message:"请选择推荐频次"}}},[t("a-radio-group",{model:{value:e.formData.frequency,callback:function(t){e.$set(e.formData,"frequency",t)},expression:"formData.frequency"}},e._l(e.FrequencyEnum.data,(function(a,r){return t("a-radio",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1),t("div",{staticClass:"form-item-help"},[e.formData.frequency==e.FrequencyEnum.ONCE.value?t("small",[e._v("用户浏览过一次后，不再向该用户继续推送")]):e._e(),e.formData.frequency==e.FrequencyEnum.EVERY_DAY.value?t("small",[e._v("用户每天第一次进入店铺时推送")]):e._e()])],1),t("a-form-model-item",{attrs:{label:"活动时间",prop:"betweenTime",rules:[{required:!0,message:"请输入活动时间"}]}},[t("a-range-picker",{attrs:{format:"YYYY-MM-DD HH:mm:ss"},model:{value:e.formData.betweenTime,callback:function(t){e.$set(e.formData,"betweenTime",t)},expression:"formData.betweenTime"}})],1),t("a-form-model-item",{attrs:{label:"活动状态",prop:"status",rules:[{required:!0,message:"请选择活动状态"}]}},[t("a-radio-group",{model:{value:e.formData.status,callback:function(t){e.$set(e.formData,"status",t)},expression:"formData.status"}},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),t("a-form-model-item",{attrs:{label:"排序",prop:"sort",rules:[{required:!0,message:"请填写排序数值"}]}},[t("a-input-number",{attrs:{min:0,autocomplete:"off"},model:{value:e.formData.sort,callback:function(t){e.$set(e.formData,"sort",t)},expression:"formData.sort"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("数字越小越靠前")])])],1),t("a-form-model-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary",loading:e.confirmLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)],1)},n=[],i=a("ade3"),s=(a("d81d"),a("d3b7"),a("c1df"),a("2ef0")),l=a("ca00"),u=a("b1e9"),c=a("c180"),d=a("7984"),m=[{name:"首页",path:"pages/index/index"},{name:"分类页",path:"pages/category/index"},{name:"购物车页",path:"pages/cart/index"},{name:"个人中心",path:"pages/user/index"}],p=(r={},Object(i["a"])(r,c["b"].SINGLE.value,{imageUrl:null,link:null}),Object(i["a"])(r,c["b"].SWIPER.value,{adList:[{imageUrl:null,link:null}],interval:3}),r),f={name:"",content_type:c["b"].SINGLE.value,content_config:p,close_mode:c["a"].MANUAL.value,seconds:3,pages:m.map((function(e){return e.path})),frequency:c["c"].ONCE.value,betweenTime:null,status:1,sort:100},v={components:{AdPicture:d["a"],Swiper:d["b"]},data:function(){return{labelCol:{span:4},wrapperCol:{span:12},isLoading:!1,confirmLoading:!1,formData:Object(s["cloneDeep"])(f),promoteId:null}},created:function(){Object(l["b"])(this,{PagesEnum:m,ContentTypeEnum:c["b"],CloseModeEnum:c["a"],FrequencyEnum:c["c"]})},methods:{handleSubmit:function(e){var t=this,a=this;a.$refs.myForm.validate((function(e){e&&a.customValidate()&&(t.confirmLoading=!0,u["a"]({form:a.formData}).then((function(e){a.$message.success(e.message,1.5),setTimeout((function(){return t.$router.push("./list")}),1200)})).finally((function(e){return a.confirmLoading=!1})))}))},customValidate:function(){var e=this,t=e.formData;if(t.content_type==c["b"].SINGLE.value){var a=t.content_config[c["b"].SINGLE.value];if(!e.validateAdItem([a]))return!1}if(t.content_type==c["b"].SWIPER.value){var r=t.content_config[c["b"].SWIPER.value].adList;if(!e.validateAdItem(r))return!1}return!0},validateAdItem:function(e){for(var t in e)if(!e[t].imageUrl)return this.$message.info("请选择广告图片"),!1;return!0}}},b=v,h=(a("776c"),a("2877")),g=Object(h["a"])(b,o,n,!1,null,"ac5bc1ac",null);t["default"]=g.exports},"48ed":function(e,t,a){},"5a40":function(e,t,a){"use strict";a("053e")},"5f94":function(e,t,a){},"68a0":function(e,t,a){},"6ad7":function(e,t,a){"use strict";a.r(t);a("ac1f"),a("841c");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{attrs:{span:6}},[t("a-button",{directives:[{name:"action",rawName:"v-action:add",arg:"add"}],attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),t("a-col",{attrs:{span:8,offset:10}},[t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px",float:"right"},attrs:{placeholder:"请输入套餐名称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"plan_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"money",fn:function(a){return t("span",{},[t("p",{staticClass:"c-p"},[e._v(e._s(a))])])}},{key:"gift_money",fn:function(a){return t("span",{},[t("p",{staticClass:"c-p"},[e._v(e._s(a))])])}},{key:"action",fn:function(a,r){return t("span",{},[t("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],staticStyle:{"margin-right":"8px"},on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")])])}}])}),t("AddForm",{ref:"AddForm",on:{handleSubmit:e.handleRefresh}}),t("EditForm",{ref:"EditForm",on:{handleSubmit:e.handleRefresh}})],1)},o=[],n=a("5530"),i=(a("d3b7"),a("b775")),s={list:"/market.recharge.plan/list",add:"/market.recharge.plan/add",edit:"/market.recharge.plan/edit",delete:"/market.recharge.plan/delete"};function l(e){return Object(i["b"])({url:s.list,method:"get",params:e})}function u(e){return Object(i["b"])({url:s.add,method:"post",data:e})}function c(e){return Object(i["b"])({url:s.edit,method:"post",data:e})}function d(e){return Object(i["b"])({url:s.delete,method:"post",data:e})}var m=a("2af9"),p=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"套餐名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"便于后台查找，例如：充100元送10元"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["plan_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['plan_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),t("a-form-item",{attrs:{label:"充值金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"会员充值并支付的金额"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["money",{rules:[{required:!0,message:"请输入充值的金额"}]}],expression:"['money', { rules: [{ required: true, message: '请输入充值的金额' }] }]"}],attrs:{min:.01}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"赠送金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"充值成功后赠送的金额，不能大于充值金额"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["gift_money",{rules:[{required:!0,message:"请输入赠送的金额"}]}],expression:"['gift_money', { rules: [{ required: true, message: '请输入赠送的金额' }] }]"}],attrs:{min:0}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},f=[],v={data:function(){return{title:"新增充值套餐",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this)}},methods:{add:function(){this.visible=!0},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,u({form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){return t.confirmLoading=!1}))}}},b=v,h=a("2877"),g=Object(h["a"])(b,p,f,!1,null,null,null),_=g.exports,C=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:720,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"套餐名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"便于后台查找，例如：充100元送10元"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["plan_name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['plan_name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}]})],1),t("a-form-item",{attrs:{label:"充值金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"会员充值并支付的金额"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["money",{rules:[{required:!0,message:"请输入充值的金额"}]}],expression:"['money', { rules: [{ required: true, message: '请输入充值的金额' }] }]"}],attrs:{min:.01}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"赠送金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"充值成功后赠送的金额，不能大于充值金额"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["gift_money",{rules:[{required:!0,message:"请输入赠送的金额"}]}],expression:"['gift_money', { rules: [{ required: true, message: '请输入赠送的金额' }] }]"}],attrs:{min:0}}),t("span",{staticClass:"ml-10"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1)],1)],1)],1)},y=[],w=a("88bc"),x=a.n(w),k={data:function(){return{title:"编辑充值套餐",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{edit:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this,t=this.form.setFieldsValue;this.$nextTick((function(){t(x()(e.record,["plan_name","money","gift_money","sort"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,c({planId:this.record.plan_id,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){return t.confirmLoading=!1}))}}},S=k,D=Object(h["a"])(S,C,y,!1,null,null,null),q=D.exports,L={name:"Index",components:{STable:m["d"],AddForm:_,EditForm:q},data:function(){var e=this;return{queryParam:{},isLoading:!1,columns:[{title:"套餐ID",dataIndex:"plan_id"},{title:"套餐名称",dataIndex:"plan_name"},{title:"充值金额 (元)",dataIndex:"money",scopedSlots:{customRender:"money"}},{title:"赠送金额 (元)",dataIndex:"gift_money",scopedSlots:{customRender:"gift_money"}},{title:"排序",dataIndex:"sort"},{title:"添加时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(t){return l(Object(n["a"])(Object(n["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAdd:function(){this.$refs.AddForm.add()},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return d({planId:e.plan_id}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},E=L,I=Object(h["a"])(E,r,o,!1,null,null,null);t["default"]=I.exports},"701c":function(e,t,a){"use strict";a("0ab7")},"776c":function(e,t,a){"use strict";a("48ed")},7984:function(e,t,a){"use strict";a.d(t,"a",(function(){return m})),a.d(t,"b",(function(){return w}));a("9911");var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ad-picture"},[t("a-row",{staticClass:"form-item"},[t("a-col",{attrs:{span:4}},[t("div",{staticClass:"label require"},[e._v("广告图片")])]),t("a-col",{attrs:{span:12}},[t("div",{staticClass:"control"},[t("SImage",{attrs:{width:80,height:80},model:{value:e.sValue.imageUrl,callback:function(t){e.$set(e.sValue,"imageUrl",t)},expression:"sValue.imageUrl"}}),t("div",{staticClass:"form-item-help mt-10"},[t("small",[e._v("建议尺寸：750*1000 或等比图片")])])],1)])],1),t("a-row",{staticClass:"form-item"},[t("a-col",{attrs:{span:4}},[t("div",{staticClass:"label"},[e._v("跳转链接")])]),t("a-col",{attrs:{span:12}},[t("div",{staticClass:"control"},[t("SLink",{attrs:{fontSize:13},model:{value:e.sValue.link,callback:function(t){e.$set(e.sValue,"link",t)},expression:"sValue.link"}})],1)])],1)],1)},o=[],n=a("4d91"),i=a("2af9"),s=a("28d5"),l={components:{SelectImage:i["h"],SLink:s["g"],SImage:s["f"]},model:{prop:"value",event:"change"},props:{value:n["a"].any},data:function(){return{sValue:{imageUrl:null,link:null}}},watch:{value:{immediate:!0,handler:function(e){e&&(this.sValue=e)}},sValue:{deep:!0,handler:function(e){this.$emit("change",e)}}},methods:{}},u=l,c=(a("7cb5"),a("2877")),d=Object(c["a"])(u,r,o,!1,null,"6458b5c6",null),m=d.exports,p=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ad-swiper"},[t("draggable",e._b({attrs:{list:e.sValue}},"draggable",{animation:120,filter:"input",preventOnFilter:!1},!1),[e._l(e.sValue,(function(a,r){return t("div",{key:r,staticClass:"swiper-item"},[t("div",{staticClass:"ad-title"},[t("a-icon",{staticClass:"drag",attrs:{type:"menu"}}),t("span",[e._v("广告位"+e._s(r+1))]),e.sValue.length>1?t("a-icon",{staticClass:"delete",attrs:{type:"delete"},on:{click:function(t){return e.handleDelete(r)}}}):e._e()],1),t("AdPicture",{model:{value:e.sValue[r],callback:function(t){e.$set(e.sValue,r,t)},expression:"sValue[index]"}})],1)})),e.sValue.length<10?t("div",{staticClass:"data-add"},[t("a-button",{attrs:{icon:"plus"},on:{click:function(t){return e.handleAddData()}}},[e._v("添加广告位 (最多10个)")])],1):e._e()],2)],1)},f=[],v=(a("a434"),a("b76a")),b=a.n(v),h=a("2ef0"),g={imageUrl:null,link:null},_={components:{SelectImage:i["h"],draggable:b.a,AdPicture:m},model:{prop:"value",event:"change"},props:{value:n["a"].any},data:function(){return{sValue:{adList:[{imageUrl:null,link:null}],interval:3}}},watch:{value:{immediate:!0,handler:function(e){e&&(this.sValue=e)}},sValue:{deep:!0,handler:function(e){this.$emit("change",e)}}},methods:{handleAddData:function(){this.sValue.push(Object(h["cloneDeep"])(g))},handleDelete:function(e){this.sValue.splice(e,1)}}},C=_,y=(a("d963"),Object(c["a"])(C,p,f,!1,null,"10a90730",null)),w=y.exports},"7b6b":function(e,t,a){},"7cb5":function(e,t,a){"use strict";a("072e")},"7e12f":function(e,t,a){"use strict";a("3a16")},"85d6":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v("优惠券 - 手动发放")]),t("a-alert",{staticClass:"mb-30",attrs:{message:"注：选择指定的优惠券（仅一张）发放给指定的用户；发放成功后无法撤销，请谨慎操作",banner:""}}),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form-model",{ref:"myForm",staticClass:"my-form",attrs:{model:e.form,labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-form-model-item",{attrs:{label:"选择指定优惠券",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"couponId",rules:{required:!0,message:"请选择优惠券"}}},[t("SelectCoupon",{attrs:{multiple:!1},model:{value:e.form.couponId,callback:function(t){e.$set(e.form,"couponId",t)},expression:"form.couponId"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("请先确保优惠券剩余数量充足，否则将会导致发送失败")])])],1),t("a-form-model-item",{attrs:{label:"发送类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"type",rules:{required:!0,message:"请选择发送类型"}}},[t("a-radio-group",{model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},[t("a-radio",{attrs:{value:10}},[e._v("指定的会员")])],1)],1),t("a-form-model-item",{attrs:{label:"选择会员",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"target.userIds",rules:{required:!0,message:"请选择会员"}}},[t("SelectUser",{attrs:{multiple:!0},model:{value:e.form.target.userIds,callback:function(t){e.$set(e.form.target,"userIds",t)},expression:"form.target.userIds"}})],1),t("a-form-model-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary",loading:e.confirmLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)],1)},o=[],n=(a("d3b7"),a("2ef0")),i=a("2af9"),s=a("39ad9"),l={couponId:null,type:10,target:{userIds:[]}},u={components:{SelectCoupon:i["f"],SelectUser:i["l"]},data:function(){return{labelCol:{span:4},wrapperCol:{span:12},isLoading:!1,confirmLoading:!1,form:Object(n["cloneDeep"])(l)}},created:function(){},methods:{handleSubmit:function(e){var t=this,a=this;a.$refs.myForm.validate((function(e){e&&(t.confirmLoading=!0,s["e"]({form:a.form}).then((function(e){a.$message.success(e.message,1.5),a.form=Object(n["cloneDeep"])(l),setTimeout((function(){return t.$router.push("./receive/index")}),1200)})).finally((function(e){return a.confirmLoading=!1})))}))}}},c=u,d=(a("a40f"),a("2877")),m=Object(d["a"])(c,r,o,!1,null,"1f61d62d",null);t["default"]=m.exports},"88bc":function(e,t,a){(function(t){var a=1/0,r=9007199254740991,o="[object Arguments]",n="[object Function]",i="[object GeneratorFunction]",s="[object Symbol]",l="object"==typeof t&&t&&t.Object===Object&&t,u="object"==typeof self&&self&&self.Object===Object&&self,c=l||u||Function("return this")();function d(e,t,a){switch(a.length){case 0:return e.call(t);case 1:return e.call(t,a[0]);case 2:return e.call(t,a[0],a[1]);case 3:return e.call(t,a[0],a[1],a[2])}return e.apply(t,a)}function m(e,t){var a=-1,r=e?e.length:0,o=Array(r);while(++a<r)o[a]=t(e[a],a,e);return o}function p(e,t){var a=-1,r=t.length,o=e.length;while(++a<r)e[o+a]=t[a];return e}var f=Object.prototype,v=f.hasOwnProperty,b=f.toString,h=c.Symbol,g=f.propertyIsEnumerable,_=h?h.isConcatSpreadable:void 0,C=Math.max;function y(e,t,a,r,o){var n=-1,i=e.length;a||(a=S),o||(o=[]);while(++n<i){var s=e[n];t>0&&a(s)?t>1?y(s,t-1,a,r,o):p(o,s):r||(o[o.length]=s)}return o}function w(e,t){return e=Object(e),x(e,t,(function(t,a){return a in e}))}function x(e,t,a){var r=-1,o=t.length,n={};while(++r<o){var i=t[r],s=e[i];a(s,i)&&(n[i]=s)}return n}function k(e,t){return t=C(void 0===t?e.length-1:t,0),function(){var a=arguments,r=-1,o=C(a.length-t,0),n=Array(o);while(++r<o)n[r]=a[t+r];r=-1;var i=Array(t+1);while(++r<t)i[r]=a[r];return i[t]=n,d(e,this,i)}}function S(e){return L(e)||q(e)||!!(_&&e&&e[_])}function D(e){if("string"==typeof e||j(e))return e;var t=e+"";return"0"==t&&1/e==-a?"-0":t}function q(e){return I(e)&&v.call(e,"callee")&&(!g.call(e,"callee")||b.call(e)==o)}var L=Array.isArray;function E(e){return null!=e&&O(e.length)&&!N(e)}function I(e){return F(e)&&E(e)}function N(e){var t=$(e)?b.call(e):"";return t==n||t==i}function O(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=r}function $(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function F(e){return!!e&&"object"==typeof e}function j(e){return"symbol"==typeof e||F(e)&&b.call(e)==s}var V=k((function(e,t){return null==e?{}:w(e,m(y(t,1),D))}));e.exports=V}).call(this,a("c8ba"))},"9ab5":function(e,t,a){"use strict";a.r(t);a("ac1f"),a("841c"),a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{attrs:{span:6}},[e.$auth("/market/promote/create")?t("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:function(t){return e.handleAdd()}}},[e._v("新增")]):e._e()],1),t("a-col",{attrs:{span:8,offset:10}},[t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px",float:"right"},attrs:{placeholder:"请输入活动名称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"promote_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15,scroll:{x:1450}},scopedSlots:e._u([{key:"name",fn:function(a){return t("span",{},[t("p",{staticClass:"oneline-hide"},[e._v(e._s(a))])])}},{key:"content_type",fn:function(a){return t("span",{},[t("a-tag",[e._v(e._s(e.ContentTypeEnum[a].name))])],1)}},{key:"views",fn:function(a){return t("span",{},[t("p",[e._v(" 浏览： "),t("span",{staticClass:"c-p"},[e._v(e._s(a.views_num))])]),t("p",[e._v(" 点击： "),t("span",{staticClass:"c-p"},[e._v(e._s(a.click_num))])])])}},{key:"time",fn:function(a){return[t("p",[e._v("开始："+e._s(a.start_time))]),t("p",[e._v("结束："+e._s(a.end_time))])]}},{key:"status",fn:function(a){return[t("a-tag",{attrs:{color:a?"green":"orange"}},[e._v(e._s(a?"开启":"关闭"))])]}},{key:"action",fn:function(a){return t("span",{staticClass:"actions"},[e.$auth("/market/promote/update")?t("a",{on:{click:function(t){return e.handleEdit(a)}}},[e._v("编辑")]):e._e(),t("a",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],on:{click:function(t){return e.handleDelete(a)}}},[e._v("删除")])])}}])})],1)},o=[],n=a("5530"),i=(a("d3b7"),a("ca00")),s=a("2af9"),l=a("b1e9"),u=a("c180"),c={name:"Index",components:{STable:s["d"]},data:function(){var e=this;return{queryParam:{},isLoading:!1,columns:[{title:"活动ID",dataIndex:"promote_id"},{title:"活动名称",dataIndex:"name",width:"200px",scopedSlots:{customRender:"name"}},{title:"推送内容",dataIndex:"content_type",scopedSlots:{customRender:"content_type"}},{title:"展现次数",scopedSlots:{customRender:"views"}},{title:"活动时间",width:"220px",scopedSlots:{customRender:"time"}},{title:"活动状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"添加时间",dataIndex:"create_time"},{title:"操作",width:"180px",fixed:"right",scopedSlots:{customRender:"action"}}],loadData:function(t){return l["e"](Object(n["a"])(Object(n["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},beforeCreate:function(){Object(i["b"])(this,{ContentTypeEnum:u["b"],CloseModeEnum:u["a"],FrequencyEnum:u["c"]})},methods:{handleAdd:function(){this.$router.push("./create")},handleEdit:function(e){this.$router.push({path:"./update",query:{promoteId:e.promote_id}})},handleDelete:function(e){var t=this,a=this.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return l["b"]({promoteId:e.promote_id}).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},d=c,m=a("2877"),p=Object(m["a"])(d,r,o,!1,null,null,null);t["default"]=p.exports},"9aea":function(e,t,a){},a40f:function(e,t,a){"use strict";a("7b6b")},ae32:function(e,t,a){},b177:function(e,t,a){"use strict";a("5f94")},b1e9:function(e,t,a){"use strict";a.d(t,"e",(function(){return i})),a.d(t,"c",(function(){return s})),a.d(t,"a",(function(){return l})),a.d(t,"d",(function(){return u})),a.d(t,"b",(function(){return c}));var r=a("5530"),o=a("b775"),n={list:"/market.promote/list",detail:"/market.promote/detail",add:"/market.promote/add",edit:"/market.promote/edit",delete:"/market.promote/delete"};function i(e){return Object(o["b"])({url:n.list,method:"get",params:e})}function s(e,t){return Object(o["b"])({url:n.detail,method:"get",params:Object(r["a"])({promoteId:e},t)})}function l(e){return Object(o["b"])({url:n.add,method:"post",data:e})}function u(e){return Object(o["b"])({url:n.edit,method:"post",data:e})}function c(e){return Object(o["b"])({url:n.delete,method:"post",data:e})}},ba96:function(e,t,a){"use strict";a("eb7c")},c10a:function(e,t,a){},c180:function(e,t,a){"use strict";a.d(t,"b",(function(){return o})),a.d(t,"a",(function(){return n})),a.d(t,"c",(function(){return i}));var r=a("5c06"),o=new r["a"]([{key:"SINGLE",name:"单图广告",value:10},{key:"SWIPER",name:"多图轮播",value:20}]),n=new r["a"]([{key:"MANUAL",name:"仅用户手动关闭",value:10},{key:"AUTO",name:"自动关闭",value:20}]),i=new r["a"]([{key:"ONCE",name:"只推荐一次",value:10},{key:"EVERY_DAY",name:"每天推荐一次",value:20}])},c24e:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"优惠券名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,min:2,message:"请输入至少2个字符"}]}],expression:"['name', { rules: [{ required: true, min: 2, message: '请输入至少2个字符' }] }]"}],attrs:{placeholder:"请输入优惠券名称"}})],1),t("a-form-item",{attrs:{label:"优惠券类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["coupon_type",{initialValue:10,rules:[{required:!0}]}],expression:"['coupon_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("满减券")]),t("a-radio",{attrs:{value:20}},[e._v("折扣券")])],1)],1),10==e.form.getFieldValue("coupon_type")?t("a-form-item",{attrs:{label:"减免金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["reduce_price",{rules:[{required:!0,message:"请输入减免金额"}]}],expression:"['reduce_price', { rules: [{ required: true, message: '请输入减免金额' }] }]"}],attrs:{min:.01,precision:2}}),t("span",{staticClass:"ml-5"},[e._v("元")])],1):e._e(),20==e.form.getFieldValue("coupon_type")?t("a-form-item",{attrs:{label:"折扣率",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["discount",{initialValue:9.9,rules:[{required:!0,message:"请输入折扣率"}]}],expression:"['discount', { initialValue: 9.9, rules: [{ required: true, message: '请输入折扣率' }] }]"}],attrs:{min:0,max:9.9,precision:1}}),t("span",{staticClass:"ml-5"},[e._v("%")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("折扣率范围 0-9.9，8代表打8折，0代表不折扣")])])],1):e._e(),t("a-form-item",{attrs:{label:"最低消费金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["min_price",{rules:[{required:!0,message:"请输入最低消费金额"}]}],expression:"['min_price', { rules: [{ required: true, message: '请输入最低消费金额' }] }]"}],attrs:{min:1,precision:2}}),t("span",{staticClass:"ml-5"},[e._v("元")])],1),t("a-form-item",{attrs:{label:"到期类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["expire_type",{initialValue:10,rules:[{required:!0}]}],expression:"['expire_type', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("领取后生效")]),t("a-radio",{attrs:{value:20}},[e._v("固定时间")])],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:10==e.form.getFieldValue("expire_type"),expression:"form.getFieldValue('expire_type') == 10"}],staticClass:"expire_type-10"},[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["expire_day",{initialValue:7,rules:[{required:!0,message:"请输入有效期天数"}]}],expression:"['expire_day', { initialValue: 7, rules: [{ required: true, message: '请输入有效期天数' }] }]"}],attrs:{addonBefore:"有效期",addonAfter:"天",inputProps:{min:1,precision:0}}})],1),t("a-form-item",{directives:[{name:"show",rawName:"v-show",value:20==e.form.getFieldValue("expire_type"),expression:"form.getFieldValue('expire_type') == 20"}],staticClass:"expire_type-20"},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["betweenTime",{initialValue:e.defaultDate,rules:[{required:!0,message:"请选择有效期范围"}]}],expression:"['betweenTime', { initialValue: defaultDate, rules: [{ required: true, message: '请选择有效期范围' }] }]"}],attrs:{format:"YYYY-MM-DD"}})],1)],1),t("a-form-item",{attrs:{label:"券适用范围",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["apply_range",{initialValue:10,rules:[{required:!0}]}],expression:"['apply_range', { initialValue: 10, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:10}},[e._v("全场通用")]),t("a-radio",{attrs:{value:20}},[e._v("指定商品")])],1),20==e.form.getFieldValue("apply_range")?t("a-form-item",[t("SelectGoods",{directives:[{name:"decorator",rawName:"v-decorator",value:["apply_range_config.applyGoodsIds",{initialValue:[],rules:[{required:!0,message:"请选择指定的商品"}]}],expression:"['apply_range_config.applyGoodsIds', { initialValue: [], rules: [{ required: true, message: '请选择指定的商品' }] }]"}],attrs:{defaultList:e.containGoodsList}})],1):e._e()],1),t("a-form-item",{attrs:{label:"发放总数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["total_num",{initialValue:-1,rules:[{required:!0,message:"请输入发放总数量"}]}],expression:"['total_num', { initialValue: -1, rules: [{ required: true, message: '请输入发放总数量' }] }]"}],attrs:{min:-1,precision:0}}),t("span",{staticClass:"ml-5"},[e._v("张")]),t("p",{staticClass:"form-item-help"},[t("small",[e._v("发放的优惠券总数量，-1为不限制")])])],1),t("a-form-item",{attrs:{label:"显示状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:1,rules:[{required:!0}]}],expression:"['status', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("显示")]),t("a-radio",{attrs:{value:0}},[e._v("隐藏")])],1),t("p",{staticClass:"form-item-help"},[t("small",[e._v("如果设为隐藏将不会展示在用户端页面")])])],1),t("a-form-item",{attrs:{label:"优惠券描述",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["describe"],expression:"['describe']"}],attrs:{autoSize:{minRows:4}}})],1),t("a-form-item",{attrs:{label:"排序",labelCol:e.labelCol,wrapperCol:e.wrapperCol,extra:"数字越小越靠前"}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["sort",{initialValue:100,rules:[{required:!0,message:"请输入排序值"}]}],expression:"['sort', { initialValue: 100, rules: [{ required: true, message: '请输入排序值' }] }]"}],attrs:{min:0}})],1),t("a-form-item",{staticClass:"mt-20",attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit",loading:e.isBtnLoading}},[e._v("提交")])],1)],1)],1)],1)},o=[],n=(a("d3b7"),a("c1df")),i=a.n(n),s=a("39ad9"),l=a("2af9"),u=a("8fa3"),c={components:{SelectGoods:l["g"],InputNumberGroup:l["c"]},data:function(){return{ApplyRangeEnum:u["a"],CouponTypeEnum:u["b"],ExpireTypeEnum:u["c"],isLoading:!1,isBtnLoading:!1,labelCol:{span:3},wrapperCol:{span:10},form:this.$form.createForm(this),defaultDate:[i()(),i()()],containGoodsList:[]}},created:function(){var e=this;this.$nextTick((function(){e.$forceUpdate()}))},methods:{handleSubmit:function(e){e.preventDefault();var t=this.form.validateFields,a=this.onFormSubmit;t((function(e,t){!e&&a(t)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,this.isBtnLoading=!0,s["a"]({form:e}).then((function(e){t.$message.success(e.message,1.5),setTimeout((function(){t.$router.push("./index")}),1500)})).catch((function(){t.isBtnLoading=!1})).finally((function(){return t.isLoading=!1}))}}},d=c,m=(a("2640"),a("2877")),p=Object(m["a"])(d,r,o,!1,null,"6c9a6694",null);t["default"]=p.exports},c7eb:function(e,t,a){"use strict";a.d(t,"a",(function(){return o}));a("a4d3"),a("e01a"),a("d3b7"),a("d28b"),a("3ca3"),a("ddb0"),a("b636"),a("944a"),a("0c47"),a("23dc"),a("3410"),a("159b"),a("b0c0"),a("131a"),a("fb6a");var r=a("53ca");function o(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
o=function(){return e};var e={},t=Object.prototype,a=t.hasOwnProperty,n=Object.defineProperty||function(e,t,a){e[t]=a.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function c(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(I){c=function(e,t,a){return e[t]=a}}function d(e,t,a,r){var o=t&&t.prototype instanceof f?t:f,i=Object.create(o.prototype),s=new q(r||[]);return n(i,"_invoke",{value:x(e,a,s)}),i}function m(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(I){return{type:"throw",arg:I}}}e.wrap=d;var p={};function f(){}function v(){}function b(){}var h={};c(h,s,(function(){return this}));var g=Object.getPrototypeOf,_=g&&g(g(L([])));_&&_!==t&&a.call(_,s)&&(h=_);var C=b.prototype=f.prototype=Object.create(h);function y(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function o(n,i,s,l){var u=m(e[n],e,i);if("throw"!==u.type){var c=u.arg,d=c.value;return d&&"object"==Object(r["a"])(d)&&a.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,s,l)}),(function(e){o("throw",e,s,l)})):t.resolve(d).then((function(e){c.value=e,s(c)}),(function(e){return o("throw",e,s,l)}))}l(u.arg)}var i;n(this,"_invoke",{value:function(e,a){function r(){return new t((function(t,r){o(e,a,t,r)}))}return i=i?i.then(r,r):r()}})}function x(e,t,a){var r="suspendedStart";return function(o,n){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw n;return E()}for(a.method=o,a.arg=n;;){var i=a.delegate;if(i){var s=k(i,a);if(s){if(s===p)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===r)throw r="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r="executing";var l=m(e,t,a);if("normal"===l.type){if(r=a.done?"completed":"suspendedYield",l.arg===p)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(r="completed",a.method="throw",a.arg=l.arg)}}}function k(e,t){var a=t.method,r=e.iterator[a];if(void 0===r)return t.delegate=null,"throw"===a&&e.iterator["return"]&&(t.method="return",t.arg=void 0,k(e,t),"throw"===t.method)||"return"!==a&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+a+"' method")),p;var o=m(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,p;var n=o.arg;return n?n.done?(t[e.resultName]=n.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,p):n:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function q(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function L(e){if(e){var t=e[s];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(a.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:E}}function E(){return{value:void 0,done:!0}}return v.prototype=b,n(C,"constructor",{value:b,configurable:!0}),n(b,"constructor",{value:v,configurable:!0}),v.displayName=c(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,c(e,u,"GeneratorFunction")),e.prototype=Object.create(C),e},e.awrap=function(e){return{__await:e}},y(w.prototype),c(w.prototype,l,(function(){return this})),e.AsyncIterator=w,e.async=function(t,a,r,o,n){void 0===n&&(n=Promise);var i=new w(d(t,a,r,o),n);return e.isGeneratorFunction(a)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},y(C),c(C,u,"Generator"),c(C,s,(function(){return this})),c(C,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),a=[];for(var r in t)a.push(r);return a.reverse(),function e(){for(;a.length;){var r=a.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=L,q.prototype={constructor:q,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(D),!e)for(var t in this)"t"===t.charAt(0)&&a.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(a,r){return i.type="throw",i.arg=e,t.next=a,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var n=this.tryEntries[o],i=n.completion;if("root"===n.tryLoc)return r("end");if(n.tryLoc<=this.prev){var s=a.call(n,"catchLoc"),l=a.call(n,"finallyLoc");if(s&&l){if(this.prev<n.catchLoc)return r(n.catchLoc,!0);if(this.prev<n.finallyLoc)return r(n.finallyLoc)}else if(s){if(this.prev<n.catchLoc)return r(n.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return r(n.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&a.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var n=o;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var i=n?n.completion:{};return i.type=e,i.arg=t,n?(this.method="next",this.next=n.finallyLoc,p):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),D(a),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var r=a.completion;if("throw"===r.type){var o=r.arg;D(a)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,a){return this.delegate={iterator:L(e),resultName:t,nextLoc:a},"next"===this.method&&(this.arg=void 0),p}},e}},d36d:function(e,t,a){"use strict";a("c10a")},d963:function(e,t,a){"use strict";a("68a0")},da13:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-form-item",{attrs:{label:"积分名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["points_name",{rules:[{required:!0,message:"请输入积分名称"}]}],expression:"['points_name', { rules: [{ required: true, message: '请输入积分名称' }] }]"}]}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("注：商家使用自定义的积分名称来做品牌运营。如京东把积分称为“京豆”，淘宝把积分称为“淘金币”")])])],1),t("a-form-item",{attrs:{label:"积分说明",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["describe",{rules:[{required:!0,message:"请输入积分说明"}]}],expression:"['describe', { rules: [{ required: true, message: '请输入积分说明' }] }]"}],attrs:{autoSize:{minRows:4}}})],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("积分赠送")]),t("a-form-item",{attrs:{label:"购物送积分",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_shopping_gift",{initialValue:1,rules:[{required:!0}]}],expression:"['is_shopping_gift', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("积分赠送规则：1. 订单确认收货已完成； 2. 已完成订单超出后台设置的申请售后期限")])])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:1==e.form.getFieldValue("is_shopping_gift"),expression:"form.getFieldValue('is_shopping_gift') == 1"}]},[t("a-form-item",{attrs:{label:"积分赠送比例",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["gift_ratio",{rules:[{required:!0,message:"请输入积分赠送比例"}]}],expression:"['gift_ratio', { rules: [{ required: true, message: '请输入积分赠送比例' }] }]"}],attrs:{min:.1,max:100,precision:1}}),t("span",{staticClass:"ml-10"},[e._v("%")]),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("注：赠送比例请填写数字0~100；订单的运费不参与积分赠送")]),t("p",{staticClass:"extra"},[e._v("例：订单付款金额(100.00元) * 积分赠送比例(100%) = 实际赠送的积分(100积分)")])])],1)],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("积分抵扣")]),t("a-form-item",{attrs:{label:"下单使用积分抵扣",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["is_shopping_discount",{initialValue:1,rules:[{required:!0}]}],expression:"['is_shopping_discount', { initialValue: 1, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("注：如开启则用户下单时可选择使用积分抵扣订单金额")])])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:1==e.form.getFieldValue("is_shopping_discount"),expression:"form.getFieldValue('is_shopping_discount') == 1"}]},[t("a-form-item",{attrs:{label:"积分抵扣比例",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["discount.discount_ratio",{rules:[{required:!0,message:"积分抵扣比例不能为空"}]}],expression:"['discount.discount_ratio', { rules: [{ required: true, message: '积分抵扣比例不能为空' }] }]"}],attrs:{addonBefore:"1个积分可抵扣",addonAfter:"元",inputProps:{min:.01,precision:2}}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("例如：设置1个积分可抵扣0.1元，则10积分可抵扣1元，100积分可抵扣10元")])])],1),t("a-form-item",{attrs:{label:"最高可抵扣金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-form-item",[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["discount.full_order_price",{rules:[{required:!0,message:"抵扣条件不能为空"}]}],expression:"['discount.full_order_price', { rules: [{ required: true, message: '抵扣条件不能为空' }] }]"}],attrs:{addonBefore:"订单满",addonAfter:"元",inputProps:{min:.01,precision:2}}})],1),t("a-form-item",[t("InputNumberGroup",{directives:[{name:"decorator",rawName:"v-decorator",value:["discount.max_money_ratio",{rules:[{required:!0,message:"最高可抵扣金额不能为空"}]}],expression:"['discount.max_money_ratio', { rules: [{ required: true, message: '最高可抵扣金额不能为空' }] }]"}],attrs:{addonBefore:"最高可抵扣金额",addonAfter:"%",inputProps:{min:.1,max:90,precision:1}}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("例如：设置最高可抵扣10%，订单金额100元，此时用户可抵扣10元")])])],1)],1)],1),t("a-form-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary","html-type":"submit"}},[e._v("提交")])],1)],1)],1)],1)},o=[],n=(a("d3b7"),a("ddb0"),a("88bc")),i=a.n(n),s=a("f585"),l=a("2af9"),u={components:{InputNumberGroup:l["c"]},data:function(){return{key:"points",labelCol:{span:4},wrapperCol:{span:10},isLoading:!1,form:this.$form.createForm(this),record:{}}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.isLoading=!0,s["a"](this.key).then((function(t){e.record=t.data.values,e.setFieldsValue()})).finally((function(){return e.isLoading=!1}))},setFieldsValue:function(){var e=this.record,t=this.$nextTick,a=this.form.setFieldsValue;t((function(){a(i()(e,["points_name","describe","is_shopping_gift","gift_ratio","is_shopping_discount","discount"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,s["b"](this.key,{form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},c=u,d=(a("b177"),a("2877")),m=Object(d["a"])(c,r,o,!1,null,"41eeb0aa",null);t["default"]=m.exports},e0ac:function(e,t,a){"use strict";a("ae32")},e7f5:function(e,t,a){"use strict";a.r(t);a("b0c0"),a("4e82");var r,o=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-form-model",{ref:"myForm",staticClass:"my-form",attrs:{model:e.formData,labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-form-model-item",{attrs:{label:"活动名称",prop:"name",rules:{required:!0,message:"请输入活动名称"}}},[t("a-input",{model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1),t("a-form-model-item",{attrs:{label:"推送内容",prop:"content_type",rules:{required:!0,message:"请选择推送内容"}}},[t("a-radio-group",{model:{value:e.formData.content_type,callback:function(t){e.$set(e.formData,"content_type",t)},expression:"formData.content_type"}},e._l(e.ContentTypeEnum.data,(function(a,r){return t("a-radio",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1)],1),e.formData.content_type==e.ContentTypeEnum.SWIPER.value?t("a-form-model-item",{attrs:{label:"间隔时间",prop:"content_config[".concat(e.ContentTypeEnum.SWIPER.value,"].interval"),rules:{required:!0,message:"请输入间隔时间"}}},[t("a-input-number",{attrs:{min:1,max:120,precision:0},model:{value:e.formData.content_config[e.ContentTypeEnum.SWIPER.value].interval,callback:function(t){e.$set(e.formData.content_config[e.ContentTypeEnum.SWIPER.value],"interval",t)},expression:"formData.content_config[ContentTypeEnum.SWIPER.value].interval"}}),t("span",{staticClass:"ml-8"},[e._v("秒钟后切换")])],1):e._e(),t("a-form-model-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[e.formData.content_type==e.ContentTypeEnum.SINGLE.value?t("div",[t("AdPicture",{model:{value:e.formData.content_config[e.ContentTypeEnum.SINGLE.value],callback:function(t){e.$set(e.formData.content_config,e.ContentTypeEnum.SINGLE.value,t)},expression:"formData.content_config[ContentTypeEnum.SINGLE.value]"}})],1):e._e(),e.formData.content_type==e.ContentTypeEnum.SWIPER.value?t("div",[t("Swiper",{model:{value:e.formData.content_config[e.ContentTypeEnum.SWIPER.value].adList,callback:function(t){e.$set(e.formData.content_config[e.ContentTypeEnum.SWIPER.value],"adList",t)},expression:"formData.content_config[ContentTypeEnum.SWIPER.value].adList"}})],1):e._e()]),t("a-form-model-item",{attrs:{label:"弹窗关闭方式",prop:"close_mode",rules:{required:!0,message:"请选择弹窗关闭方式"}}},[t("a-radio-group",{model:{value:e.formData.close_mode,callback:function(t){e.$set(e.formData,"close_mode",t)},expression:"formData.close_mode"}},e._l(e.CloseModeEnum.data,(function(a,r){return t("a-radio",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1)],1),t("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:e.formData.close_mode==e.CloseModeEnum.AUTO.value,expression:"formData.close_mode == CloseModeEnum.AUTO.value"}],attrs:{label:"倒计时",prop:"seconds",rules:{required:!0,message:"请输入倒计时"}}},[t("a-input-number",{attrs:{min:2,max:120,precision:0},model:{value:e.formData.seconds,callback:function(t){e.$set(e.formData,"seconds",t)},expression:"formData.seconds"}}),t("span",{staticClass:"ml-8"},[e._v("秒钟后自动关闭弹窗")])],1),t("a-form-model-item",{attrs:{label:"投放的页面",prop:"pages",rules:{required:!0,message:"请至少选择1个投放的页面"}}},[t("a-checkbox-group",{model:{value:e.formData.pages,callback:function(t){e.$set(e.formData,"pages",t)},expression:"formData.pages"}},e._l(e.PagesEnum,(function(a,r){return t("a-checkbox",{key:r,attrs:{value:a.path}},[e._v(e._s(a.name))])})),1)],1),t("a-form-model-item",{attrs:{label:"推荐频次",prop:"frequency",rules:{required:!0,message:"请选择推荐频次"}}},[t("a-radio-group",{model:{value:e.formData.frequency,callback:function(t){e.$set(e.formData,"frequency",t)},expression:"formData.frequency"}},e._l(e.FrequencyEnum.data,(function(a,r){return t("a-radio",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1),t("div",{staticClass:"form-item-help"},[e.formData.frequency==e.FrequencyEnum.ONCE.value?t("small",[e._v("用户浏览过一次后，不再向该用户继续推送")]):e._e(),e.formData.frequency==e.FrequencyEnum.EVERY_DAY.value?t("small",[e._v("用户每天第一次进入店铺时推送")]):e._e()])],1),t("a-form-model-item",{attrs:{label:"活动时间",prop:"betweenTime",rules:[{required:!0,message:"请输入活动时间"}]}},[t("a-range-picker",{attrs:{format:"YYYY-MM-DD HH:mm:ss"},model:{value:e.formData.betweenTime,callback:function(t){e.$set(e.formData,"betweenTime",t)},expression:"formData.betweenTime"}})],1),t("a-form-model-item",{attrs:{label:"活动状态",prop:"status",rules:[{required:!0,message:"请选择活动状态"}]}},[t("a-radio-group",{model:{value:e.formData.status,callback:function(t){e.$set(e.formData,"status",t)},expression:"formData.status"}},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),t("a-form-model-item",{attrs:{label:"排序",prop:"sort",rules:[{required:!0,message:"请填写排序数值"}]}},[t("a-input-number",{attrs:{min:0,autocomplete:"off"},model:{value:e.formData.sort,callback:function(t){e.$set(e.formData,"sort",t)},expression:"formData.sort"}}),t("div",{staticClass:"form-item-help"},[t("small",[e._v("数字越小越靠前")])])],1),t("a-form-model-item",{attrs:{wrapperCol:{span:e.wrapperCol.span,offset:e.labelCol.span}}},[t("a-button",{attrs:{type:"primary",loading:e.confirmLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)],1)},n=[],i=a("ade3"),s=(a("d81d"),a("d3b7"),a("c1df")),l=a.n(s),u=a("2ef0"),c=a("ca00"),d=a("b1e9"),m=a("c180"),p=a("7984"),f=[{name:"首页",path:"pages/index/index"},{name:"分类页",path:"pages/category/index"},{name:"购物车页",path:"pages/cart/index"},{name:"个人中心",path:"pages/user/index"}],v=(r={},Object(i["a"])(r,m["b"].SINGLE.value,{imageUrl:null,link:null}),Object(i["a"])(r,m["b"].SWIPER.value,{adList:[{imageUrl:null,link:null}],interval:3}),r),b={name:"",content_type:m["b"].SINGLE.value,content_config:v,close_mode:m["a"].MANUAL.value,seconds:3,pages:f.map((function(e){return e.path})),frequency:m["c"].ONCE.value,betweenTime:null,status:1,sort:100},h={components:{AdPicture:p["a"],Swiper:p["b"]},data:function(){return{labelCol:{span:4},wrapperCol:{span:12},isLoading:!1,confirmLoading:!1,formData:Object(u["cloneDeep"])(b),promoteId:null}},watch:{"$route.query.promoteId":{immediate:!0,handler:function(e){this.promoteId=e,this.getDetail()}}},created:function(){Object(c["b"])(this,{PagesEnum:f,ContentTypeEnum:m["b"],CloseModeEnum:m["a"],FrequencyEnum:m["c"]})},methods:{getDetail:function(){var e=this;this.isLoading=!0,d["c"](this.promoteId).then((function(t){e.formData=Object(c["a"])(t.data.detail,b),e.formData.betweenTime=e.getBetweenTime(t.data.detail)})).finally((function(){return e.isLoading=!1}))},getBetweenTime:function(e){return[l()(new Date(e.start_time)),l()(new Date(e.end_time))]},handleSubmit:function(e){var t=this,a=this;a.$refs.myForm.validate((function(e){e&&a.customValidate()&&(t.confirmLoading=!0,d["d"]({promoteId:a.promoteId,form:a.formData}).then((function(e){a.$message.success(e.message,1.5),setTimeout((function(){return t.$router.push("./list")}),1200)})).finally((function(e){return a.confirmLoading=!1})))}))},customValidate:function(){var e=this,t=e.formData;if(t.content_type==m["b"].SINGLE.value){var a=t.content_config[m["b"].SINGLE.value];if(!e.validateAdItem([a]))return!1}if(t.content_type==m["b"].SWIPER.value){var r=t.content_config[m["b"].SWIPER.value].adList;if(!e.validateAdItem(r))return!1}return!0},validateAdItem:function(e){for(var t in e)if(!e[t].imageUrl)return this.$message.info("请选择广告图片"),!1;return!0}}},g=h,_=(a("ba96"),a("2877")),C=Object(_["a"])(g,o,n,!1,null,"579c1440",null);t["default"]=C.exports},eb7c:function(e,t,a){},fe7e:function(e,t,a){"use strict";var r=a("5c06");t["a"]=new r["a"]([{key:"RECHARGE",name:"用户充值",value:10},{key:"CONSUME",name:"用户消费",value:20},{key:"ADMIN",name:"管理员操作",value:30},{key:"REFUND",name:"订单退款",value:40}])}}]);