[2025-05-17 19:22:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480342
            [orderIds] => []
        )

)

[2025-05-17 19:22:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480353
            [orderIds] => []
        )

)

[2025-05-17 19:22:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480364
            [orderIds] => []
        )

)

[2025-05-17 19:22:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480375
            [orderIds] => []
        )

)

[2025-05-17 19:23:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480386
            [orderIds] => []
        )

)

[2025-05-17 19:23:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480397
            [orderIds] => []
        )

)

[2025-05-17 19:23:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480408
            [orderIds] => []
        )

)

[2025-05-17 19:23:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480419
            [orderIds] => []
        )

)

[2025-05-17 19:23:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480430
            [orderIds] => []
        )

)

[2025-05-17 19:24:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480441
            [orderIds] => []
        )

)

[2025-05-17 19:24:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480452
            [orderIds] => []
        )

)

[2025-05-17 19:24:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480463
            [orderIds] => []
        )

)

[2025-05-17 19:24:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480474
            [orderIds] => []
        )

)

[2025-05-17 19:24:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480485
            [orderIds] => []
        )

)

[2025-05-17 19:24:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480496
            [orderIds] => []
        )

)

[2025-05-17 19:25:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480507
            [orderIds] => []
        )

)

[2025-05-17 19:25:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480518
            [orderIds] => []
        )

)

[2025-05-17 19:25:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480529
            [orderIds] => []
        )

)

[2025-05-17 19:25:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480540
            [orderIds] => []
        )

)

[2025-05-17 19:25:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480551
            [orderIds] => []
        )

)

[2025-05-17 19:26:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480562
            [orderIds] => []
        )

)

[2025-05-17 19:26:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480573
            [orderIds] => []
        )

)

[2025-05-17 19:26:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 19:26:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 19:26:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 19:26:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 19:26:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480584
            [orderIds] => []
        )

)

[2025-05-17 19:26:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480595
            [orderIds] => []
        )

)

[2025-05-17 19:26:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480606
            [orderIds] => []
        )

)

[2025-05-17 19:26:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480617
            [orderIds] => []
        )

)

[2025-05-17 19:27:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480628
            [orderIds] => []
        )

)

[2025-05-17 19:27:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480639
            [orderIds] => []
        )

)

[2025-05-17 19:27:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480650
            [orderIds] => []
        )

)

[2025-05-17 19:27:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480661
            [orderIds] => []
        )

)

[2025-05-17 19:27:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480672
            [orderIds] => []
        )

)

[2025-05-17 19:28:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480683
            [orderIds] => []
        )

)

[2025-05-17 19:28:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480694
            [orderIds] => []
        )

)

[2025-05-17 19:28:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480705
            [orderIds] => []
        )

)

[2025-05-17 19:28:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480716
            [orderIds] => []
        )

)

[2025-05-17 19:28:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480727
            [orderIds] => []
        )

)

[2025-05-17 19:28:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480738
            [orderIds] => []
        )

)

[2025-05-17 19:29:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480749
            [orderIds] => []
        )

)

[2025-05-17 19:29:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480760
            [orderIds] => []
        )

)

[2025-05-17 19:29:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480771
            [orderIds] => []
        )

)

[2025-05-17 19:29:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480782
            [orderIds] => []
        )

)

[2025-05-17 19:29:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480793
            [orderIds] => []
        )

)

[2025-05-17 19:30:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480804
            [orderIds] => []
        )

)

[2025-05-17 19:30:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480815
            [orderIds] => []
        )

)

[2025-05-17 19:30:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480826
            [orderIds] => []
        )

)

[2025-05-17 19:30:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480837
            [orderIds] => []
        )

)

[2025-05-17 19:30:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480848
            [orderIds] => []
        )

)

[2025-05-17 19:30:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480859
            [orderIds] => []
        )

)

[2025-05-17 19:31:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480870
            [orderIds] => []
        )

)

[2025-05-17 19:31:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480881
            [orderIds] => []
        )

)

[2025-05-17 19:31:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480892
            [orderIds] => []
        )

)

[2025-05-17 19:31:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480903
            [orderIds] => []
        )

)

[2025-05-17 19:31:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480914
            [orderIds] => []
        )

)

[2025-05-17 19:32:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480925
            [orderIds] => []
        )

)

[2025-05-17 19:32:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480936
            [orderIds] => []
        )

)

[2025-05-17 19:32:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480947
            [orderIds] => []
        )

)

[2025-05-17 19:32:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480958
            [orderIds] => []
        )

)

[2025-05-17 19:32:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480969
            [orderIds] => []
        )

)

[2025-05-17 19:33:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480980
            [orderIds] => []
        )

)

[2025-05-17 19:33:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747480991
            [orderIds] => []
        )

)

[2025-05-17 19:33:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481002
            [orderIds] => []
        )

)

[2025-05-17 19:33:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481013
            [orderIds] => []
        )

)

[2025-05-17 19:33:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481024
            [orderIds] => []
        )

)

[2025-05-17 19:33:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481035
            [orderIds] => []
        )

)

[2025-05-17 19:34:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481046
            [orderIds] => []
        )

)

[2025-05-17 19:34:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481057
            [orderIds] => []
        )

)

[2025-05-17 19:34:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481068
            [orderIds] => []
        )

)

[2025-05-17 19:34:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481079
            [orderIds] => []
        )

)

[2025-05-17 19:34:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481090
            [orderIds] => []
        )

)

[2025-05-17 19:35:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481101
            [orderIds] => []
        )

)

[2025-05-17 19:35:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481112
            [orderIds] => []
        )

)

[2025-05-17 19:35:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481123
            [orderIds] => []
        )

)

[2025-05-17 19:35:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481134
            [orderIds] => []
        )

)

[2025-05-17 19:35:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481145
            [orderIds] => []
        )

)

[2025-05-17 19:35:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481156
            [orderIds] => []
        )

)

[2025-05-17 19:36:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481167
            [orderIds] => []
        )

)

[2025-05-17 19:36:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481178
            [orderIds] => []
        )

)

[2025-05-17 19:36:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 19:36:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 19:36:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 19:36:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 19:36:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481189
            [orderIds] => []
        )

)

[2025-05-17 19:36:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481200
            [orderIds] => []
        )

)

[2025-05-17 19:36:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481211
            [orderIds] => []
        )

)

[2025-05-17 19:37:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481222
            [orderIds] => []
        )

)

[2025-05-17 19:37:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481233
            [orderIds] => []
        )

)

[2025-05-17 19:37:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481244
            [orderIds] => []
        )

)

[2025-05-17 19:37:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481255
            [orderIds] => []
        )

)

[2025-05-17 19:37:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481266
            [orderIds] => []
        )

)

[2025-05-17 19:37:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481277
            [orderIds] => []
        )

)

[2025-05-17 19:38:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481288
            [orderIds] => []
        )

)

[2025-05-17 19:38:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747395492
            [orderIds] => []
        )

)

[2025-05-17 19:38:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1746877092
            [orderIds] => []
        )

)

[2025-05-17 19:38:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1746877092
            [orderIds] => []
        )

)

[2025-05-17 19:38:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-17 19:38:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-17 19:38:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-17 19:38:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-17 19:38:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-17 19:38:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481299
            [orderIds] => []
        )

)

[2025-05-17 19:38:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481310
            [orderIds] => []
        )

)

[2025-05-17 19:38:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481321
            [orderIds] => []
        )

)

[2025-05-17 19:38:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481332
            [orderIds] => []
        )

)

[2025-05-17 19:39:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481343
            [orderIds] => []
        )

)

[2025-05-17 19:39:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481354
            [orderIds] => []
        )

)

[2025-05-17 19:39:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481365
            [orderIds] => []
        )

)

[2025-05-17 19:39:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481376
            [orderIds] => []
        )

)

[2025-05-17 19:39:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481387
            [orderIds] => []
        )

)

[2025-05-17 19:39:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481398
            [orderIds] => []
        )

)

[2025-05-17 19:40:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481409
            [orderIds] => []
        )

)

[2025-05-17 19:40:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481420
            [orderIds] => []
        )

)

[2025-05-17 19:40:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481431
            [orderIds] => []
        )

)

[2025-05-17 19:40:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481442
            [orderIds] => []
        )

)

[2025-05-17 19:40:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481453
            [orderIds] => []
        )

)

[2025-05-17 19:41:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481464
            [orderIds] => []
        )

)

[2025-05-17 19:41:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481475
            [orderIds] => []
        )

)

[2025-05-17 19:41:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481486
            [orderIds] => []
        )

)

[2025-05-17 19:41:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481497
            [orderIds] => []
        )

)

[2025-05-17 19:41:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481508
            [orderIds] => []
        )

)

[2025-05-17 19:41:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481519
            [orderIds] => []
        )

)

[2025-05-17 19:42:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481530
            [orderIds] => []
        )

)

[2025-05-17 19:42:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481541
            [orderIds] => []
        )

)

[2025-05-17 19:42:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481552
            [orderIds] => []
        )

)

[2025-05-17 19:42:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481563
            [orderIds] => []
        )

)

[2025-05-17 19:42:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481574
            [orderIds] => []
        )

)

[2025-05-17 19:43:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481585
            [orderIds] => []
        )

)

[2025-05-17 19:43:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481596
            [orderIds] => []
        )

)

[2025-05-17 19:43:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481607
            [orderIds] => []
        )

)

[2025-05-17 19:43:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481618
            [orderIds] => []
        )

)

[2025-05-17 19:43:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481629
            [orderIds] => []
        )

)

[2025-05-17 19:44:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481640
            [orderIds] => []
        )

)

[2025-05-17 19:44:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481651
            [orderIds] => []
        )

)

[2025-05-17 19:44:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481662
            [orderIds] => []
        )

)

[2025-05-17 19:44:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481673
            [orderIds] => []
        )

)

[2025-05-17 19:44:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481684
            [orderIds] => []
        )

)

[2025-05-17 19:44:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481695
            [orderIds] => []
        )

)

[2025-05-17 19:45:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481706
            [orderIds] => []
        )

)

[2025-05-17 19:45:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481717
            [orderIds] => []
        )

)

[2025-05-17 19:45:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481728
            [orderIds] => []
        )

)

[2025-05-17 19:45:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481739
            [orderIds] => []
        )

)

[2025-05-17 19:45:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481750
            [orderIds] => []
        )

)

[2025-05-17 19:46:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481761
            [orderIds] => []
        )

)

[2025-05-17 19:46:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481772
            [orderIds] => []
        )

)

[2025-05-17 19:46:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 19:46:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 19:46:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 19:46:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 19:46:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481783
            [orderIds] => []
        )

)

[2025-05-17 19:46:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481794
            [orderIds] => []
        )

)

[2025-05-17 19:46:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481805
            [orderIds] => []
        )

)

[2025-05-17 19:46:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481816
            [orderIds] => []
        )

)

[2025-05-17 19:47:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481827
            [orderIds] => []
        )

)

[2025-05-17 19:47:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481838
            [orderIds] => []
        )

)

[2025-05-17 19:47:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481849
            [orderIds] => []
        )

)

[2025-05-17 19:47:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481860
            [orderIds] => []
        )

)

[2025-05-17 19:47:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481871
            [orderIds] => []
        )

)

[2025-05-17 19:48:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481882
            [orderIds] => []
        )

)

[2025-05-17 19:48:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481893
            [orderIds] => []
        )

)

[2025-05-17 19:48:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481904
            [orderIds] => []
        )

)

[2025-05-17 19:48:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481915
            [orderIds] => []
        )

)

[2025-05-17 19:48:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481926
            [orderIds] => []
        )

)

[2025-05-17 19:48:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481937
            [orderIds] => []
        )

)

[2025-05-17 19:49:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481948
            [orderIds] => []
        )

)

[2025-05-17 19:49:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481959
            [orderIds] => []
        )

)

[2025-05-17 19:49:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481970
            [orderIds] => []
        )

)

[2025-05-17 19:49:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481981
            [orderIds] => []
        )

)

[2025-05-17 19:49:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747481992
            [orderIds] => []
        )

)

[2025-05-17 19:50:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482003
            [orderIds] => []
        )

)

[2025-05-17 19:50:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482014
            [orderIds] => []
        )

)

[2025-05-17 19:50:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482025
            [orderIds] => []
        )

)

[2025-05-17 19:50:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482036
            [orderIds] => []
        )

)

[2025-05-17 19:50:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482047
            [orderIds] => []
        )

)

[2025-05-17 19:50:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482058
            [orderIds] => []
        )

)

[2025-05-17 19:51:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482069
            [orderIds] => []
        )

)

[2025-05-17 19:51:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482080
            [orderIds] => []
        )

)

[2025-05-17 19:51:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482091
            [orderIds] => []
        )

)

[2025-05-17 19:51:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482102
            [orderIds] => []
        )

)

[2025-05-17 19:51:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482113
            [orderIds] => []
        )

)

[2025-05-17 19:52:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482124
            [orderIds] => []
        )

)

[2025-05-17 19:52:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482135
            [orderIds] => []
        )

)

[2025-05-17 19:52:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482146
            [orderIds] => []
        )

)

[2025-05-17 19:52:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482157
            [orderIds] => []
        )

)

[2025-05-17 19:52:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482168
            [orderIds] => []
        )

)

[2025-05-17 19:52:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482179
            [orderIds] => []
        )

)

[2025-05-17 19:53:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482190
            [orderIds] => []
        )

)

[2025-05-17 19:53:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482201
            [orderIds] => []
        )

)

[2025-05-17 19:53:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482212
            [orderIds] => []
        )

)

[2025-05-17 19:53:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482223
            [orderIds] => []
        )

)

[2025-05-17 19:53:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482234
            [orderIds] => []
        )

)

[2025-05-17 19:54:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482245
            [orderIds] => []
        )

)

[2025-05-17 19:54:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482256
            [orderIds] => []
        )

)

[2025-05-17 19:54:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482267
            [orderIds] => []
        )

)

[2025-05-17 19:54:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482278
            [orderIds] => []
        )

)

[2025-05-17 19:54:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482289
            [orderIds] => []
        )

)

[2025-05-17 19:55:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482300
            [orderIds] => []
        )

)

[2025-05-17 19:55:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482311
            [orderIds] => []
        )

)

[2025-05-17 19:55:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482322
            [orderIds] => []
        )

)

[2025-05-17 19:55:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482333
            [orderIds] => []
        )

)

[2025-05-17 19:55:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482344
            [orderIds] => []
        )

)

[2025-05-17 19:55:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482355
            [orderIds] => []
        )

)

[2025-05-17 19:56:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482366
            [orderIds] => []
        )

)

[2025-05-17 19:56:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482377
            [orderIds] => []
        )

)

[2025-05-17 19:56:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 19:56:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 19:56:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 19:56:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 19:56:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482388
            [orderIds] => []
        )

)

[2025-05-17 19:56:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482399
            [orderIds] => []
        )

)

[2025-05-17 19:56:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482410
            [orderIds] => []
        )

)

[2025-05-17 19:57:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482421
            [orderIds] => []
        )

)

[2025-05-17 19:57:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482432
            [orderIds] => []
        )

)

[2025-05-17 19:57:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482443
            [orderIds] => []
        )

)

[2025-05-17 19:57:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482454
            [orderIds] => []
        )

)

[2025-05-17 19:57:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482465
            [orderIds] => []
        )

)

[2025-05-17 19:57:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482476
            [orderIds] => []
        )

)

[2025-05-17 19:58:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482487
            [orderIds] => []
        )

)

[2025-05-17 19:58:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482498
            [orderIds] => []
        )

)

[2025-05-17 19:58:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482509
            [orderIds] => []
        )

)

[2025-05-17 19:58:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482520
            [orderIds] => []
        )

)

[2025-05-17 19:58:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482531
            [orderIds] => []
        )

)

[2025-05-17 19:59:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482542
            [orderIds] => []
        )

)

[2025-05-17 19:59:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482553
            [orderIds] => []
        )

)

[2025-05-17 19:59:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482564
            [orderIds] => []
        )

)

[2025-05-17 19:59:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482575
            [orderIds] => []
        )

)

[2025-05-17 19:59:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482586
            [orderIds] => []
        )

)

[2025-05-17 19:59:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482597
            [orderIds] => []
        )

)

[2025-05-17 20:00:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482608
            [orderIds] => []
        )

)

[2025-05-17 20:00:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482619
            [orderIds] => []
        )

)

[2025-05-17 20:00:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482630
            [orderIds] => []
        )

)

[2025-05-17 20:00:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482641
            [orderIds] => []
        )

)

[2025-05-17 20:00:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482652
            [orderIds] => []
        )

)

[2025-05-17 20:01:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482663
            [orderIds] => []
        )

)

[2025-05-17 20:01:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482674
            [orderIds] => []
        )

)

[2025-05-17 20:01:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482685
            [orderIds] => []
        )

)

[2025-05-17 20:01:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482696
            [orderIds] => []
        )

)

[2025-05-17 20:01:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482707
            [orderIds] => []
        )

)

[2025-05-17 20:01:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482718
            [orderIds] => []
        )

)

[2025-05-17 20:02:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482729
            [orderIds] => []
        )

)

[2025-05-17 20:02:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482740
            [orderIds] => []
        )

)

[2025-05-17 20:02:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482751
            [orderIds] => []
        )

)

[2025-05-17 20:02:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482762
            [orderIds] => []
        )

)

[2025-05-17 20:02:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482773
            [orderIds] => []
        )

)

[2025-05-17 20:03:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482784
            [orderIds] => []
        )

)

[2025-05-17 20:03:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482795
            [orderIds] => []
        )

)

[2025-05-17 20:03:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482806
            [orderIds] => []
        )

)

[2025-05-17 20:03:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482817
            [orderIds] => []
        )

)

[2025-05-17 20:03:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482828
            [orderIds] => []
        )

)

[2025-05-17 20:03:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482839
            [orderIds] => []
        )

)

[2025-05-17 20:04:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482850
            [orderIds] => []
        )

)

[2025-05-17 20:04:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482861
            [orderIds] => []
        )

)

[2025-05-17 20:04:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482872
            [orderIds] => []
        )

)

[2025-05-17 20:04:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482883
            [orderIds] => []
        )

)

[2025-05-17 20:04:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482894
            [orderIds] => []
        )

)

[2025-05-17 20:05:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482905
            [orderIds] => []
        )

)

[2025-05-17 20:05:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482916
            [orderIds] => []
        )

)

[2025-05-17 20:05:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482927
            [orderIds] => []
        )

)

[2025-05-17 20:05:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482938
            [orderIds] => []
        )

)

[2025-05-17 20:05:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482949
            [orderIds] => []
        )

)

[2025-05-17 20:06:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482960
            [orderIds] => []
        )

)

[2025-05-17 20:06:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482971
            [orderIds] => []
        )

)

[2025-05-17 20:06:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482982
            [orderIds] => []
        )

)

[2025-05-17 20:06:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 20:06:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 20:06:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 20:06:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 20:06:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747482993
            [orderIds] => []
        )

)

[2025-05-17 20:06:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483004
            [orderIds] => []
        )

)

[2025-05-17 20:06:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483015
            [orderIds] => []
        )

)

[2025-05-17 20:07:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483026
            [orderIds] => []
        )

)

[2025-05-17 20:07:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483037
            [orderIds] => []
        )

)

[2025-05-17 20:07:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483048
            [orderIds] => []
        )

)

[2025-05-17 20:07:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483059
            [orderIds] => []
        )

)

[2025-05-17 20:07:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483070
            [orderIds] => []
        )

)

[2025-05-17 20:08:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483081
            [orderIds] => []
        )

)

[2025-05-17 20:08:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483092
            [orderIds] => []
        )

)

[2025-05-17 20:08:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747397293
            [orderIds] => []
        )

)

[2025-05-17 20:08:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1746878893
            [orderIds] => []
        )

)

[2025-05-17 20:08:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1746878893
            [orderIds] => []
        )

)

[2025-05-17 20:08:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-17 20:08:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-17 20:08:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-17 20:08:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-17 20:08:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-17 20:08:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483103
            [orderIds] => []
        )

)

[2025-05-17 20:08:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483114
            [orderIds] => []
        )

)

[2025-05-17 20:08:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483125
            [orderIds] => []
        )

)

[2025-05-17 20:08:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483136
            [orderIds] => []
        )

)

[2025-05-17 20:09:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483147
            [orderIds] => []
        )

)

[2025-05-17 20:09:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483158
            [orderIds] => []
        )

)

[2025-05-17 20:09:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483169
            [orderIds] => []
        )

)

[2025-05-17 20:09:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483180
            [orderIds] => []
        )

)

[2025-05-17 20:09:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483191
            [orderIds] => []
        )

)

[2025-05-17 20:10:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483202
            [orderIds] => []
        )

)

[2025-05-17 20:10:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483213
            [orderIds] => []
        )

)

[2025-05-17 20:10:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483224
            [orderIds] => []
        )

)

[2025-05-17 20:10:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483235
            [orderIds] => []
        )

)

[2025-05-17 20:10:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483246
            [orderIds] => []
        )

)

[2025-05-17 20:10:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483257
            [orderIds] => []
        )

)

[2025-05-17 20:11:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483268
            [orderIds] => []
        )

)

[2025-05-17 20:11:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483279
            [orderIds] => []
        )

)

[2025-05-17 20:11:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483290
            [orderIds] => []
        )

)

[2025-05-17 20:11:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483301
            [orderIds] => []
        )

)

[2025-05-17 20:11:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483312
            [orderIds] => []
        )

)

[2025-05-17 20:12:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483323
            [orderIds] => []
        )

)

[2025-05-17 20:12:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483334
            [orderIds] => []
        )

)

[2025-05-17 20:12:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483345
            [orderIds] => []
        )

)

[2025-05-17 20:12:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483356
            [orderIds] => []
        )

)

[2025-05-17 20:12:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483367
            [orderIds] => []
        )

)

[2025-05-17 20:12:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483378
            [orderIds] => []
        )

)

[2025-05-17 20:13:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483389
            [orderIds] => []
        )

)

[2025-05-17 20:13:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483400
            [orderIds] => []
        )

)

[2025-05-17 20:13:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483411
            [orderIds] => []
        )

)

[2025-05-17 20:13:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483422
            [orderIds] => []
        )

)

[2025-05-17 20:13:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483433
            [orderIds] => []
        )

)

[2025-05-17 20:14:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483444
            [orderIds] => []
        )

)

[2025-05-17 20:14:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483455
            [orderIds] => []
        )

)

[2025-05-17 20:14:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483466
            [orderIds] => []
        )

)

[2025-05-17 20:14:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483477
            [orderIds] => []
        )

)

[2025-05-17 20:14:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483488
            [orderIds] => []
        )

)

[2025-05-17 20:14:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483499
            [orderIds] => []
        )

)

[2025-05-17 20:15:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483510
            [orderIds] => []
        )

)

[2025-05-17 20:15:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483521
            [orderIds] => []
        )

)

[2025-05-17 20:15:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483532
            [orderIds] => []
        )

)

[2025-05-17 20:15:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483543
            [orderIds] => []
        )

)

[2025-05-17 20:15:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483554
            [orderIds] => []
        )

)

[2025-05-17 20:16:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483565
            [orderIds] => []
        )

)

[2025-05-17 20:16:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483576
            [orderIds] => []
        )

)

[2025-05-17 20:16:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 20:16:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 20:16:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 20:16:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 20:16:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483587
            [orderIds] => []
        )

)

[2025-05-17 20:16:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483598
            [orderIds] => []
        )

)

[2025-05-17 20:16:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483609
            [orderIds] => []
        )

)

[2025-05-17 20:17:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483620
            [orderIds] => []
        )

)

[2025-05-17 20:17:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483631
            [orderIds] => []
        )

)

[2025-05-17 20:17:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483642
            [orderIds] => []
        )

)

[2025-05-17 20:17:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483653
            [orderIds] => []
        )

)

[2025-05-17 20:17:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483664
            [orderIds] => []
        )

)

[2025-05-17 20:17:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483675
            [orderIds] => []
        )

)

[2025-05-17 20:18:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483686
            [orderIds] => []
        )

)

[2025-05-17 20:18:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483697
            [orderIds] => []
        )

)

[2025-05-17 20:18:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483708
            [orderIds] => []
        )

)

[2025-05-17 20:18:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483719
            [orderIds] => []
        )

)

[2025-05-17 20:18:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483730
            [orderIds] => []
        )

)

[2025-05-17 20:19:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483741
            [orderIds] => []
        )

)

[2025-05-17 20:19:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483752
            [orderIds] => []
        )

)

[2025-05-17 20:19:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483763
            [orderIds] => []
        )

)

[2025-05-17 20:19:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483774
            [orderIds] => []
        )

)

[2025-05-17 20:19:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483785
            [orderIds] => []
        )

)

[2025-05-17 20:19:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483796
            [orderIds] => []
        )

)

[2025-05-17 20:20:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483807
            [orderIds] => []
        )

)

[2025-05-17 20:20:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483818
            [orderIds] => []
        )

)

[2025-05-17 20:20:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483829
            [orderIds] => []
        )

)

[2025-05-17 20:20:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483840
            [orderIds] => []
        )

)

[2025-05-17 20:20:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483851
            [orderIds] => []
        )

)

[2025-05-17 20:21:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483862
            [orderIds] => []
        )

)

[2025-05-17 20:21:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483873
            [orderIds] => []
        )

)

[2025-05-17 20:21:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483884
            [orderIds] => []
        )

)

[2025-05-17 20:21:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483895
            [orderIds] => []
        )

)

[2025-05-17 20:21:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483906
            [orderIds] => []
        )

)

[2025-05-17 20:21:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483917
            [orderIds] => []
        )

)

[2025-05-17 20:22:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483928
            [orderIds] => []
        )

)

[2025-05-17 20:22:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483939
            [orderIds] => []
        )

)

[2025-05-17 20:22:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483950
            [orderIds] => []
        )

)

[2025-05-17 20:22:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483961
            [orderIds] => []
        )

)

[2025-05-17 20:22:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483972
            [orderIds] => []
        )

)

[2025-05-17 20:23:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483983
            [orderIds] => []
        )

)

[2025-05-17 20:23:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747483994
            [orderIds] => []
        )

)

[2025-05-17 20:23:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484005
            [orderIds] => []
        )

)

[2025-05-17 20:23:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484016
            [orderIds] => []
        )

)

[2025-05-17 20:23:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484027
            [orderIds] => []
        )

)

[2025-05-17 20:23:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484038
            [orderIds] => []
        )

)

[2025-05-17 20:24:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484049
            [orderIds] => []
        )

)

[2025-05-17 20:24:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484060
            [orderIds] => []
        )

)

[2025-05-17 20:24:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484071
            [orderIds] => []
        )

)

[2025-05-17 20:24:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484082
            [orderIds] => []
        )

)

[2025-05-17 20:24:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484093
            [orderIds] => []
        )

)

[2025-05-17 20:25:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484104
            [orderIds] => []
        )

)

[2025-05-17 20:25:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484115
            [orderIds] => []
        )

)

[2025-05-17 20:25:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484126
            [orderIds] => []
        )

)

[2025-05-17 20:25:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484137
            [orderIds] => []
        )

)

[2025-05-17 20:25:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484148
            [orderIds] => []
        )

)

[2025-05-17 20:25:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484159
            [orderIds] => []
        )

)

[2025-05-17 20:26:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484170
            [orderIds] => []
        )

)

[2025-05-17 20:26:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484181
            [orderIds] => []
        )

)

[2025-05-17 20:26:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 20:26:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 20:26:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 20:26:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 20:26:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484192
            [orderIds] => []
        )

)

[2025-05-17 20:26:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484203
            [orderIds] => []
        )

)

[2025-05-17 20:26:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484214
            [orderIds] => []
        )

)

[2025-05-17 20:27:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484225
            [orderIds] => []
        )

)

[2025-05-17 20:27:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484236
            [orderIds] => []
        )

)

[2025-05-17 20:27:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484247
            [orderIds] => []
        )

)

[2025-05-17 20:27:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484258
            [orderIds] => []
        )

)

[2025-05-17 20:27:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484269
            [orderIds] => []
        )

)

[2025-05-17 20:28:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484280
            [orderIds] => []
        )

)

[2025-05-17 20:28:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484291
            [orderIds] => []
        )

)

[2025-05-17 20:28:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484302
            [orderIds] => []
        )

)

[2025-05-17 20:28:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484313
            [orderIds] => []
        )

)

[2025-05-17 20:28:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484324
            [orderIds] => []
        )

)

[2025-05-17 20:28:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484335
            [orderIds] => []
        )

)

[2025-05-17 20:29:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484346
            [orderIds] => []
        )

)

[2025-05-17 20:29:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484357
            [orderIds] => []
        )

)

[2025-05-17 20:29:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484368
            [orderIds] => []
        )

)

[2025-05-17 20:29:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484379
            [orderIds] => []
        )

)

[2025-05-17 20:29:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484390
            [orderIds] => []
        )

)

[2025-05-17 20:30:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484401
            [orderIds] => []
        )

)

[2025-05-17 20:30:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484412
            [orderIds] => []
        )

)

[2025-05-17 20:30:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484423
            [orderIds] => []
        )

)

[2025-05-17 20:30:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484434
            [orderIds] => []
        )

)

[2025-05-17 20:30:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484445
            [orderIds] => []
        )

)

[2025-05-17 20:30:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484456
            [orderIds] => []
        )

)

[2025-05-17 20:31:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484467
            [orderIds] => []
        )

)

[2025-05-17 20:31:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484478
            [orderIds] => []
        )

)

[2025-05-17 20:31:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484489
            [orderIds] => []
        )

)

[2025-05-17 20:31:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484500
            [orderIds] => []
        )

)

[2025-05-17 20:31:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484511
            [orderIds] => []
        )

)

[2025-05-17 20:32:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484522
            [orderIds] => []
        )

)

[2025-05-17 20:32:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484533
            [orderIds] => []
        )

)

[2025-05-17 20:32:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484544
            [orderIds] => []
        )

)

[2025-05-17 20:32:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484555
            [orderIds] => []
        )

)

[2025-05-17 20:32:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484566
            [orderIds] => []
        )

)

[2025-05-17 20:32:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484577
            [orderIds] => []
        )

)

[2025-05-17 20:33:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484588
            [orderIds] => []
        )

)

[2025-05-17 20:33:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484599
            [orderIds] => []
        )

)

[2025-05-17 20:33:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484610
            [orderIds] => []
        )

)

[2025-05-17 20:33:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484621
            [orderIds] => []
        )

)

[2025-05-17 20:33:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484632
            [orderIds] => []
        )

)

[2025-05-17 20:34:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484643
            [orderIds] => []
        )

)

[2025-05-17 20:34:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484654
            [orderIds] => []
        )

)

[2025-05-17 20:34:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484665
            [orderIds] => []
        )

)

[2025-05-17 20:34:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484676
            [orderIds] => []
        )

)

[2025-05-17 20:34:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484687
            [orderIds] => []
        )

)

[2025-05-17 20:34:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484698
            [orderIds] => []
        )

)

[2025-05-17 20:35:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484709
            [orderIds] => []
        )

)

[2025-05-17 20:35:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484720
            [orderIds] => []
        )

)

[2025-05-17 20:35:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484731
            [orderIds] => []
        )

)

[2025-05-17 20:35:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484742
            [orderIds] => []
        )

)

[2025-05-17 20:35:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484753
            [orderIds] => []
        )

)

[2025-05-17 20:36:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484764
            [orderIds] => []
        )

)

[2025-05-17 20:36:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484775
            [orderIds] => []
        )

)

[2025-05-17 20:36:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 20:36:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 20:36:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 20:36:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 20:36:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484786
            [orderIds] => []
        )

)

[2025-05-17 20:36:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484797
            [orderIds] => []
        )

)

[2025-05-17 20:36:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484808
            [orderIds] => []
        )

)

[2025-05-17 20:36:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484819
            [orderIds] => []
        )

)

[2025-05-17 20:37:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484830
            [orderIds] => []
        )

)

[2025-05-17 20:37:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484841
            [orderIds] => []
        )

)

[2025-05-17 20:37:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484852
            [orderIds] => []
        )

)

[2025-05-17 20:37:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484863
            [orderIds] => []
        )

)

[2025-05-17 20:37:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484874
            [orderIds] => []
        )

)

[2025-05-17 20:38:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484885
            [orderIds] => []
        )

)

[2025-05-17 20:38:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747399094
            [orderIds] => []
        )

)

[2025-05-17 20:38:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1746880694
            [orderIds] => []
        )

)

[2025-05-17 20:38:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1746880694
            [orderIds] => []
        )

)

[2025-05-17 20:38:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-17 20:38:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-17 20:38:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-17 20:38:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-17 20:38:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-17 20:38:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484896
            [orderIds] => []
        )

)

[2025-05-17 20:38:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484907
            [orderIds] => []
        )

)

[2025-05-17 20:38:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484918
            [orderIds] => []
        )

)

[2025-05-17 20:38:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484929
            [orderIds] => []
        )

)

[2025-05-17 20:39:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484940
            [orderIds] => []
        )

)

[2025-05-17 20:39:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484951
            [orderIds] => []
        )

)

[2025-05-17 20:39:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484962
            [orderIds] => []
        )

)

[2025-05-17 20:39:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484973
            [orderIds] => []
        )

)

[2025-05-17 20:39:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484984
            [orderIds] => []
        )

)

[2025-05-17 20:39:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747484995
            [orderIds] => []
        )

)

[2025-05-17 20:40:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485006
            [orderIds] => []
        )

)

[2025-05-17 20:40:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485017
            [orderIds] => []
        )

)

[2025-05-17 20:40:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485028
            [orderIds] => []
        )

)

[2025-05-17 20:40:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485039
            [orderIds] => []
        )

)

[2025-05-17 20:40:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485050
            [orderIds] => []
        )

)

[2025-05-17 20:41:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485061
            [orderIds] => []
        )

)

[2025-05-17 20:41:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485072
            [orderIds] => []
        )

)

[2025-05-17 20:41:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485083
            [orderIds] => []
        )

)

[2025-05-17 20:41:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485094
            [orderIds] => []
        )

)

[2025-05-17 20:41:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485105
            [orderIds] => []
        )

)

[2025-05-17 20:41:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485116
            [orderIds] => []
        )

)

[2025-05-17 20:42:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485127
            [orderIds] => []
        )

)

[2025-05-17 20:42:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485138
            [orderIds] => []
        )

)

[2025-05-17 20:42:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485149
            [orderIds] => []
        )

)

[2025-05-17 20:42:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485160
            [orderIds] => []
        )

)

[2025-05-17 20:42:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485171
            [orderIds] => []
        )

)

[2025-05-17 20:43:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485182
            [orderIds] => []
        )

)

[2025-05-17 20:43:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485193
            [orderIds] => []
        )

)

[2025-05-17 20:43:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485204
            [orderIds] => []
        )

)

[2025-05-17 20:43:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485215
            [orderIds] => []
        )

)

[2025-05-17 20:43:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485226
            [orderIds] => []
        )

)

[2025-05-17 20:43:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485237
            [orderIds] => []
        )

)

[2025-05-17 20:44:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485248
            [orderIds] => []
        )

)

[2025-05-17 20:44:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485259
            [orderIds] => []
        )

)

[2025-05-17 20:44:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485270
            [orderIds] => []
        )

)

[2025-05-17 20:44:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485281
            [orderIds] => []
        )

)

[2025-05-17 20:44:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485292
            [orderIds] => []
        )

)

[2025-05-17 20:45:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485303
            [orderIds] => []
        )

)

[2025-05-17 20:45:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485314
            [orderIds] => []
        )

)

[2025-05-17 20:45:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485325
            [orderIds] => []
        )

)

[2025-05-17 20:45:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485336
            [orderIds] => []
        )

)

[2025-05-17 20:45:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485347
            [orderIds] => []
        )

)

[2025-05-17 20:45:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485358
            [orderIds] => []
        )

)

[2025-05-17 20:46:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485369
            [orderIds] => []
        )

)

[2025-05-17 20:46:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485380
            [orderIds] => []
        )

)

[2025-05-17 20:46:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 20:46:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 20:46:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 20:46:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 20:46:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485391
            [orderIds] => []
        )

)

[2025-05-17 20:46:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485402
            [orderIds] => []
        )

)

[2025-05-17 20:46:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485413
            [orderIds] => []
        )

)

[2025-05-17 20:47:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485424
            [orderIds] => []
        )

)

[2025-05-17 20:47:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485435
            [orderIds] => []
        )

)

[2025-05-17 20:47:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485446
            [orderIds] => []
        )

)

[2025-05-17 20:47:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485457
            [orderIds] => []
        )

)

[2025-05-17 20:47:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485468
            [orderIds] => []
        )

)

[2025-05-17 20:47:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485479
            [orderIds] => []
        )

)

[2025-05-17 20:48:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485490
            [orderIds] => []
        )

)

[2025-05-17 20:48:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485501
            [orderIds] => []
        )

)

[2025-05-17 20:48:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485512
            [orderIds] => []
        )

)

[2025-05-17 20:48:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485523
            [orderIds] => []
        )

)

[2025-05-17 20:48:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485534
            [orderIds] => []
        )

)

[2025-05-17 20:49:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485545
            [orderIds] => []
        )

)

[2025-05-17 20:49:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485556
            [orderIds] => []
        )

)

[2025-05-17 20:49:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485567
            [orderIds] => []
        )

)

[2025-05-17 20:49:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485578
            [orderIds] => []
        )

)

[2025-05-17 20:49:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485589
            [orderIds] => []
        )

)

[2025-05-17 20:50:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485600
            [orderIds] => []
        )

)

[2025-05-17 20:50:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485611
            [orderIds] => []
        )

)

[2025-05-17 20:50:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485622
            [orderIds] => []
        )

)

[2025-05-17 20:50:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485633
            [orderIds] => []
        )

)

[2025-05-17 20:50:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485644
            [orderIds] => []
        )

)

[2025-05-17 20:50:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485655
            [orderIds] => []
        )

)

[2025-05-17 20:51:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485666
            [orderIds] => []
        )

)

[2025-05-17 20:51:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485677
            [orderIds] => []
        )

)

[2025-05-17 20:51:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485688
            [orderIds] => []
        )

)

[2025-05-17 20:51:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485699
            [orderIds] => []
        )

)

[2025-05-17 20:51:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485710
            [orderIds] => []
        )

)

[2025-05-17 20:52:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485721
            [orderIds] => []
        )

)

[2025-05-17 20:52:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485732
            [orderIds] => []
        )

)

[2025-05-17 20:52:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485743
            [orderIds] => []
        )

)

[2025-05-17 20:52:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485754
            [orderIds] => []
        )

)

[2025-05-17 20:52:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485765
            [orderIds] => []
        )

)

[2025-05-17 20:52:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485776
            [orderIds] => []
        )

)

[2025-05-17 20:53:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485787
            [orderIds] => []
        )

)

[2025-05-17 20:53:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485798
            [orderIds] => []
        )

)

[2025-05-17 20:53:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485809
            [orderIds] => []
        )

)

[2025-05-17 20:53:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485820
            [orderIds] => []
        )

)

[2025-05-17 20:53:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485831
            [orderIds] => []
        )

)

[2025-05-17 20:54:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485842
            [orderIds] => []
        )

)

[2025-05-17 20:54:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485853
            [orderIds] => []
        )

)

[2025-05-17 20:54:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485864
            [orderIds] => []
        )

)

[2025-05-17 20:54:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485875
            [orderIds] => []
        )

)

[2025-05-17 20:54:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485886
            [orderIds] => []
        )

)

[2025-05-17 20:54:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485897
            [orderIds] => []
        )

)

[2025-05-17 20:55:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485908
            [orderIds] => []
        )

)

[2025-05-17 20:55:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485919
            [orderIds] => []
        )

)

[2025-05-17 20:55:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485930
            [orderIds] => []
        )

)

[2025-05-17 20:55:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485941
            [orderIds] => []
        )

)

[2025-05-17 20:55:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485952
            [orderIds] => []
        )

)

[2025-05-17 20:56:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485963
            [orderIds] => []
        )

)

[2025-05-17 20:56:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485974
            [orderIds] => []
        )

)

[2025-05-17 20:56:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485985
            [orderIds] => []
        )

)

[2025-05-17 20:56:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 20:56:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 20:56:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 20:56:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 20:56:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747485996
            [orderIds] => []
        )

)

[2025-05-17 20:56:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486007
            [orderIds] => []
        )

)

[2025-05-17 20:56:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486018
            [orderIds] => []
        )

)

[2025-05-17 20:57:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486029
            [orderIds] => []
        )

)

[2025-05-17 20:57:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486040
            [orderIds] => []
        )

)

[2025-05-17 20:57:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486051
            [orderIds] => []
        )

)

[2025-05-17 20:57:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486062
            [orderIds] => []
        )

)

[2025-05-17 20:57:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486073
            [orderIds] => []
        )

)

[2025-05-17 20:58:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486084
            [orderIds] => []
        )

)

[2025-05-17 20:58:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486095
            [orderIds] => []
        )

)

[2025-05-17 20:58:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486106
            [orderIds] => []
        )

)

[2025-05-17 20:58:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486117
            [orderIds] => []
        )

)

[2025-05-17 20:58:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486128
            [orderIds] => []
        )

)

[2025-05-17 20:58:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486139
            [orderIds] => []
        )

)

[2025-05-17 20:59:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486150
            [orderIds] => []
        )

)

[2025-05-17 20:59:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486161
            [orderIds] => []
        )

)

[2025-05-17 20:59:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486172
            [orderIds] => []
        )

)

[2025-05-17 20:59:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486183
            [orderIds] => []
        )

)

[2025-05-17 20:59:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486194
            [orderIds] => []
        )

)

[2025-05-17 21:00:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486205
            [orderIds] => []
        )

)

[2025-05-17 21:00:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486216
            [orderIds] => []
        )

)

[2025-05-17 21:00:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486227
            [orderIds] => []
        )

)

[2025-05-17 21:00:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486238
            [orderIds] => []
        )

)

[2025-05-17 21:00:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486249
            [orderIds] => []
        )

)

[2025-05-17 21:01:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486260
            [orderIds] => []
        )

)

[2025-05-17 21:01:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486271
            [orderIds] => []
        )

)

[2025-05-17 21:01:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486282
            [orderIds] => []
        )

)

[2025-05-17 21:01:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486293
            [orderIds] => []
        )

)

[2025-05-17 21:01:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486304
            [orderIds] => []
        )

)

[2025-05-17 21:01:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486315
            [orderIds] => []
        )

)

[2025-05-17 21:02:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486326
            [orderIds] => []
        )

)

[2025-05-17 21:02:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486337
            [orderIds] => []
        )

)

[2025-05-17 21:02:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486348
            [orderIds] => []
        )

)

[2025-05-17 21:02:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486359
            [orderIds] => []
        )

)

[2025-05-17 21:02:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486370
            [orderIds] => []
        )

)

[2025-05-17 21:03:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486381
            [orderIds] => []
        )

)

[2025-05-17 21:03:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486392
            [orderIds] => []
        )

)

[2025-05-17 21:03:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486403
            [orderIds] => []
        )

)

[2025-05-17 21:03:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486414
            [orderIds] => []
        )

)

[2025-05-17 21:03:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486425
            [orderIds] => []
        )

)

[2025-05-17 21:03:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486436
            [orderIds] => []
        )

)

[2025-05-17 21:04:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486447
            [orderIds] => []
        )

)

[2025-05-17 21:04:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486458
            [orderIds] => []
        )

)

[2025-05-17 21:04:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486469
            [orderIds] => []
        )

)

[2025-05-17 21:04:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486480
            [orderIds] => []
        )

)

[2025-05-17 21:04:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486491
            [orderIds] => []
        )

)

[2025-05-17 21:05:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486502
            [orderIds] => []
        )

)

[2025-05-17 21:05:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486513
            [orderIds] => []
        )

)

[2025-05-17 21:05:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486524
            [orderIds] => []
        )

)

[2025-05-17 21:05:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486535
            [orderIds] => []
        )

)

[2025-05-17 21:05:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486546
            [orderIds] => []
        )

)

[2025-05-17 21:05:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486557
            [orderIds] => []
        )

)

[2025-05-17 21:06:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486568
            [orderIds] => []
        )

)

[2025-05-17 21:06:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486579
            [orderIds] => []
        )

)

[2025-05-17 21:06:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 21:06:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 21:06:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 21:06:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 21:06:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486590
            [orderIds] => []
        )

)

[2025-05-17 21:06:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486601
            [orderIds] => []
        )

)

[2025-05-17 21:06:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486612
            [orderIds] => []
        )

)

[2025-05-17 21:07:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486623
            [orderIds] => []
        )

)

[2025-05-17 21:07:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486634
            [orderIds] => []
        )

)

[2025-05-17 21:07:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486645
            [orderIds] => []
        )

)

[2025-05-17 21:07:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486656
            [orderIds] => []
        )

)

[2025-05-17 21:07:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486667
            [orderIds] => []
        )

)

[2025-05-17 21:07:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486678
            [orderIds] => []
        )

)

[2025-05-17 21:08:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486689
            [orderIds] => []
        )

)

[2025-05-17 21:08:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747400895
            [orderIds] => []
        )

)

[2025-05-17 21:08:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1746882495
            [orderIds] => []
        )

)

[2025-05-17 21:08:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1746882495
            [orderIds] => []
        )

)

[2025-05-17 21:08:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-17 21:08:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-17 21:08:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-17 21:08:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-17 21:08:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-17 21:08:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486700
            [orderIds] => []
        )

)

[2025-05-17 21:08:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486711
            [orderIds] => []
        )

)

[2025-05-17 21:08:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486722
            [orderIds] => []
        )

)

[2025-05-17 21:08:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486733
            [orderIds] => []
        )

)

[2025-05-17 21:09:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486744
            [orderIds] => []
        )

)

[2025-05-17 21:09:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486755
            [orderIds] => []
        )

)

[2025-05-17 21:09:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486766
            [orderIds] => []
        )

)

[2025-05-17 21:09:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486777
            [orderIds] => []
        )

)

[2025-05-17 21:09:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486788
            [orderIds] => []
        )

)

[2025-05-17 21:09:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486799
            [orderIds] => []
        )

)

[2025-05-17 21:10:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486810
            [orderIds] => []
        )

)

[2025-05-17 21:10:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486821
            [orderIds] => []
        )

)

[2025-05-17 21:10:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486832
            [orderIds] => []
        )

)

[2025-05-17 21:10:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486843
            [orderIds] => []
        )

)

[2025-05-17 21:10:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486854
            [orderIds] => []
        )

)

[2025-05-17 21:11:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486865
            [orderIds] => []
        )

)

[2025-05-17 21:11:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486876
            [orderIds] => []
        )

)

[2025-05-17 21:11:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486887
            [orderIds] => []
        )

)

[2025-05-17 21:11:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486898
            [orderIds] => []
        )

)

[2025-05-17 21:11:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486909
            [orderIds] => []
        )

)

[2025-05-17 21:12:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486920
            [orderIds] => []
        )

)

[2025-05-17 21:12:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486931
            [orderIds] => []
        )

)

[2025-05-17 21:12:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486942
            [orderIds] => []
        )

)

[2025-05-17 21:12:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486953
            [orderIds] => []
        )

)

[2025-05-17 21:12:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486964
            [orderIds] => []
        )

)

[2025-05-17 21:12:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486975
            [orderIds] => []
        )

)

[2025-05-17 21:13:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486986
            [orderIds] => []
        )

)

[2025-05-17 21:13:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747486997
            [orderIds] => []
        )

)

[2025-05-17 21:13:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487008
            [orderIds] => []
        )

)

[2025-05-17 21:13:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487019
            [orderIds] => []
        )

)

[2025-05-17 21:13:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487030
            [orderIds] => []
        )

)

[2025-05-17 21:14:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487041
            [orderIds] => []
        )

)

[2025-05-17 21:14:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487052
            [orderIds] => []
        )

)

[2025-05-17 21:14:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487063
            [orderIds] => []
        )

)

[2025-05-17 21:14:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487074
            [orderIds] => []
        )

)

[2025-05-17 21:14:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487085
            [orderIds] => []
        )

)

[2025-05-17 21:14:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487096
            [orderIds] => []
        )

)

[2025-05-17 21:15:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487107
            [orderIds] => []
        )

)

[2025-05-17 21:15:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487118
            [orderIds] => []
        )

)

[2025-05-17 21:15:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487129
            [orderIds] => []
        )

)

[2025-05-17 21:15:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487140
            [orderIds] => []
        )

)

[2025-05-17 21:15:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487151
            [orderIds] => []
        )

)

[2025-05-17 21:16:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487162
            [orderIds] => []
        )

)

[2025-05-17 21:16:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487173
            [orderIds] => []
        )

)

[2025-05-17 21:16:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487184
            [orderIds] => []
        )

)

[2025-05-17 21:16:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 21:16:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 21:16:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 21:16:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 21:16:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487195
            [orderIds] => []
        )

)

[2025-05-17 21:16:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487206
            [orderIds] => []
        )

)

[2025-05-17 21:16:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487217
            [orderIds] => []
        )

)

[2025-05-17 21:17:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487228
            [orderIds] => []
        )

)

[2025-05-17 21:17:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487239
            [orderIds] => []
        )

)

[2025-05-17 21:17:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487250
            [orderIds] => []
        )

)

[2025-05-17 21:17:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487261
            [orderIds] => []
        )

)

[2025-05-17 21:17:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487272
            [orderIds] => []
        )

)

[2025-05-17 21:18:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487283
            [orderIds] => []
        )

)

[2025-05-17 21:18:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487294
            [orderIds] => []
        )

)

[2025-05-17 21:18:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487305
            [orderIds] => []
        )

)

[2025-05-17 21:18:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487316
            [orderIds] => []
        )

)

[2025-05-17 21:18:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487327
            [orderIds] => []
        )

)

[2025-05-17 21:18:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487338
            [orderIds] => []
        )

)

[2025-05-17 21:19:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487349
            [orderIds] => []
        )

)

[2025-05-17 21:19:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487360
            [orderIds] => []
        )

)

[2025-05-17 21:19:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487371
            [orderIds] => []
        )

)

[2025-05-17 21:19:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487382
            [orderIds] => []
        )

)

[2025-05-17 21:19:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487393
            [orderIds] => []
        )

)

[2025-05-17 21:20:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487404
            [orderIds] => []
        )

)

[2025-05-17 21:20:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487415
            [orderIds] => []
        )

)

[2025-05-17 21:20:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487426
            [orderIds] => []
        )

)

[2025-05-17 21:20:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487437
            [orderIds] => []
        )

)

[2025-05-17 21:20:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487448
            [orderIds] => []
        )

)

[2025-05-17 21:20:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487459
            [orderIds] => []
        )

)

[2025-05-17 21:21:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487470
            [orderIds] => []
        )

)

[2025-05-17 21:21:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487481
            [orderIds] => []
        )

)

[2025-05-17 21:21:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487492
            [orderIds] => []
        )

)

[2025-05-17 21:21:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487503
            [orderIds] => []
        )

)

[2025-05-17 21:21:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487514
            [orderIds] => []
        )

)

[2025-05-17 21:22:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487525
            [orderIds] => []
        )

)

[2025-05-17 21:22:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487536
            [orderIds] => []
        )

)

[2025-05-17 21:22:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487547
            [orderIds] => []
        )

)

[2025-05-17 21:22:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487558
            [orderIds] => []
        )

)

[2025-05-17 21:22:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487569
            [orderIds] => []
        )

)

[2025-05-17 21:23:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487580
            [orderIds] => []
        )

)

[2025-05-17 21:23:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487591
            [orderIds] => []
        )

)

[2025-05-17 21:23:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487602
            [orderIds] => []
        )

)

[2025-05-17 21:23:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487613
            [orderIds] => []
        )

)

[2025-05-17 21:23:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487624
            [orderIds] => []
        )

)

[2025-05-17 21:23:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487635
            [orderIds] => []
        )

)

[2025-05-17 21:24:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487646
            [orderIds] => []
        )

)

[2025-05-17 21:24:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487657
            [orderIds] => []
        )

)

[2025-05-17 21:24:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487668
            [orderIds] => []
        )

)

[2025-05-17 21:24:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487679
            [orderIds] => []
        )

)

[2025-05-17 21:24:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487690
            [orderIds] => []
        )

)

[2025-05-17 21:25:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487701
            [orderIds] => []
        )

)

[2025-05-17 21:25:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487712
            [orderIds] => []
        )

)

[2025-05-17 21:25:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487723
            [orderIds] => []
        )

)

[2025-05-17 21:25:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487734
            [orderIds] => []
        )

)

[2025-05-17 21:25:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487745
            [orderIds] => []
        )

)

[2025-05-17 21:25:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487756
            [orderIds] => []
        )

)

[2025-05-17 21:26:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487767
            [orderIds] => []
        )

)

[2025-05-17 21:26:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487778
            [orderIds] => []
        )

)

[2025-05-17 21:26:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487789
            [orderIds] => []
        )

)

[2025-05-17 21:26:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 21:26:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 21:26:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 21:26:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 21:26:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487800
            [orderIds] => []
        )

)

[2025-05-17 21:26:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487811
            [orderIds] => []
        )

)

[2025-05-17 21:27:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487822
            [orderIds] => []
        )

)

[2025-05-17 21:27:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487833
            [orderIds] => []
        )

)

[2025-05-17 21:27:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487844
            [orderIds] => []
        )

)

[2025-05-17 21:27:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487855
            [orderIds] => []
        )

)

[2025-05-17 21:27:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487866
            [orderIds] => []
        )

)

[2025-05-17 21:27:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487877
            [orderIds] => []
        )

)

[2025-05-17 21:28:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487888
            [orderIds] => []
        )

)

[2025-05-17 21:28:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487899
            [orderIds] => []
        )

)

[2025-05-17 21:28:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487910
            [orderIds] => []
        )

)

[2025-05-17 21:28:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487921
            [orderIds] => []
        )

)

[2025-05-17 21:28:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487932
            [orderIds] => []
        )

)

[2025-05-17 21:29:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487943
            [orderIds] => []
        )

)

[2025-05-17 21:29:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487954
            [orderIds] => []
        )

)

[2025-05-17 21:29:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487965
            [orderIds] => []
        )

)

[2025-05-17 21:29:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487976
            [orderIds] => []
        )

)

[2025-05-17 21:29:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487987
            [orderIds] => []
        )

)

[2025-05-17 21:29:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747487998
            [orderIds] => []
        )

)

[2025-05-17 21:30:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488009
            [orderIds] => []
        )

)

[2025-05-17 21:30:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488020
            [orderIds] => []
        )

)

[2025-05-17 21:30:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488031
            [orderIds] => []
        )

)

[2025-05-17 21:30:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488042
            [orderIds] => []
        )

)

[2025-05-17 21:30:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488053
            [orderIds] => []
        )

)

[2025-05-17 21:31:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488064
            [orderIds] => []
        )

)

[2025-05-17 21:31:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488075
            [orderIds] => []
        )

)

[2025-05-17 21:31:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488086
            [orderIds] => []
        )

)

[2025-05-17 21:31:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488097
            [orderIds] => []
        )

)

[2025-05-17 21:31:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488108
            [orderIds] => []
        )

)

[2025-05-17 21:31:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488119
            [orderIds] => []
        )

)

[2025-05-17 21:32:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488130
            [orderIds] => []
        )

)

[2025-05-17 21:32:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488141
            [orderIds] => []
        )

)

[2025-05-17 21:32:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488152
            [orderIds] => []
        )

)

[2025-05-17 21:32:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488163
            [orderIds] => []
        )

)

[2025-05-17 21:32:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488174
            [orderIds] => []
        )

)

[2025-05-17 21:33:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488185
            [orderIds] => []
        )

)

[2025-05-17 21:33:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488196
            [orderIds] => []
        )

)

[2025-05-17 21:33:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488207
            [orderIds] => []
        )

)

[2025-05-17 21:33:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488218
            [orderIds] => []
        )

)

[2025-05-17 21:33:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488229
            [orderIds] => []
        )

)

[2025-05-17 21:34:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488240
            [orderIds] => []
        )

)

[2025-05-17 21:34:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488251
            [orderIds] => []
        )

)

[2025-05-17 21:34:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488262
            [orderIds] => []
        )

)

[2025-05-17 21:34:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488273
            [orderIds] => []
        )

)

[2025-05-17 21:34:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488284
            [orderIds] => []
        )

)

[2025-05-17 21:34:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488295
            [orderIds] => []
        )

)

[2025-05-17 21:35:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488306
            [orderIds] => []
        )

)

[2025-05-17 21:35:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488317
            [orderIds] => []
        )

)

[2025-05-17 21:35:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488328
            [orderIds] => []
        )

)

[2025-05-17 21:35:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488339
            [orderIds] => []
        )

)

[2025-05-17 21:35:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488350
            [orderIds] => []
        )

)

[2025-05-17 21:36:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488361
            [orderIds] => []
        )

)

[2025-05-17 21:36:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488372
            [orderIds] => []
        )

)

[2025-05-17 21:36:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488383
            [orderIds] => []
        )

)

[2025-05-17 21:36:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 21:36:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 21:36:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 21:36:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 21:36:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488394
            [orderIds] => []
        )

)

[2025-05-17 21:36:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488405
            [orderIds] => []
        )

)

[2025-05-17 21:36:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488416
            [orderIds] => []
        )

)

[2025-05-17 21:37:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488427
            [orderIds] => []
        )

)

[2025-05-17 21:37:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488438
            [orderIds] => []
        )

)

[2025-05-17 21:37:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488449
            [orderIds] => []
        )

)

[2025-05-17 21:37:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488460
            [orderIds] => []
        )

)

[2025-05-17 21:37:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488471
            [orderIds] => []
        )

)

[2025-05-17 21:38:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488482
            [orderIds] => []
        )

)

[2025-05-17 21:38:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488493
            [orderIds] => []
        )

)

[2025-05-17 21:38:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747402696
            [orderIds] => []
        )

)

[2025-05-17 21:38:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1746884296
            [orderIds] => []
        )

)

[2025-05-17 21:38:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1746884296
            [orderIds] => []
        )

)

[2025-05-17 21:38:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-17 21:38:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-17 21:38:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-17 21:38:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-17 21:38:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-17 21:38:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488504
            [orderIds] => []
        )

)

[2025-05-17 21:38:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488515
            [orderIds] => []
        )

)

[2025-05-17 21:38:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488526
            [orderIds] => []
        )

)

[2025-05-17 21:38:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488537
            [orderIds] => []
        )

)

[2025-05-17 21:39:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488548
            [orderIds] => []
        )

)

[2025-05-17 21:39:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488559
            [orderIds] => []
        )

)

[2025-05-17 21:39:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488570
            [orderIds] => []
        )

)

[2025-05-17 21:39:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488581
            [orderIds] => []
        )

)

[2025-05-17 21:39:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488592
            [orderIds] => []
        )

)

[2025-05-17 21:40:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488603
            [orderIds] => []
        )

)

[2025-05-17 21:40:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488614
            [orderIds] => []
        )

)

[2025-05-17 21:40:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488625
            [orderIds] => []
        )

)

[2025-05-17 21:40:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488636
            [orderIds] => []
        )

)

[2025-05-17 21:40:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488647
            [orderIds] => []
        )

)

[2025-05-17 21:40:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488658
            [orderIds] => []
        )

)

[2025-05-17 21:41:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488669
            [orderIds] => []
        )

)

[2025-05-17 21:41:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488680
            [orderIds] => []
        )

)

[2025-05-17 21:41:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488691
            [orderIds] => []
        )

)

[2025-05-17 21:41:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488702
            [orderIds] => []
        )

)

[2025-05-17 21:41:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488713
            [orderIds] => []
        )

)

[2025-05-17 21:42:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488724
            [orderIds] => []
        )

)

[2025-05-17 21:42:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488735
            [orderIds] => []
        )

)

[2025-05-17 21:42:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488746
            [orderIds] => []
        )

)

[2025-05-17 21:42:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488757
            [orderIds] => []
        )

)

[2025-05-17 21:42:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488768
            [orderIds] => []
        )

)

[2025-05-17 21:42:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488779
            [orderIds] => []
        )

)

[2025-05-17 21:43:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488790
            [orderIds] => []
        )

)

[2025-05-17 21:43:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488801
            [orderIds] => []
        )

)

[2025-05-17 21:43:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488812
            [orderIds] => []
        )

)

[2025-05-17 21:43:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488823
            [orderIds] => []
        )

)

[2025-05-17 21:43:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488834
            [orderIds] => []
        )

)

[2025-05-17 21:44:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488845
            [orderIds] => []
        )

)

[2025-05-17 21:44:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488856
            [orderIds] => []
        )

)

[2025-05-17 21:44:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488867
            [orderIds] => []
        )

)

[2025-05-17 21:44:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488878
            [orderIds] => []
        )

)

[2025-05-17 21:44:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488889
            [orderIds] => []
        )

)

[2025-05-17 21:45:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488900
            [orderIds] => []
        )

)

[2025-05-17 21:45:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488911
            [orderIds] => []
        )

)

[2025-05-17 21:45:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488922
            [orderIds] => []
        )

)

[2025-05-17 21:45:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488933
            [orderIds] => []
        )

)

[2025-05-17 21:45:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488944
            [orderIds] => []
        )

)

[2025-05-17 21:45:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488955
            [orderIds] => []
        )

)

[2025-05-17 21:46:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488966
            [orderIds] => []
        )

)

[2025-05-17 21:46:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488977
            [orderIds] => []
        )

)

[2025-05-17 21:46:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488988
            [orderIds] => []
        )

)

[2025-05-17 21:46:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 21:46:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 21:46:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 21:46:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 21:46:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747488999
            [orderIds] => []
        )

)

[2025-05-17 21:46:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489010
            [orderIds] => []
        )

)

[2025-05-17 21:47:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489021
            [orderIds] => []
        )

)

[2025-05-17 21:47:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489032
            [orderIds] => []
        )

)

[2025-05-17 21:47:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489043
            [orderIds] => []
        )

)

[2025-05-17 21:47:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489054
            [orderIds] => []
        )

)

[2025-05-17 21:47:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489065
            [orderIds] => []
        )

)

[2025-05-17 21:47:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489076
            [orderIds] => []
        )

)

[2025-05-17 21:48:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489087
            [orderIds] => []
        )

)

[2025-05-17 21:48:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489098
            [orderIds] => []
        )

)

[2025-05-17 21:48:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489109
            [orderIds] => []
        )

)

[2025-05-17 21:48:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489120
            [orderIds] => []
        )

)

[2025-05-17 21:48:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489131
            [orderIds] => []
        )

)

[2025-05-17 21:49:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489142
            [orderIds] => []
        )

)

[2025-05-17 21:49:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489153
            [orderIds] => []
        )

)

[2025-05-17 21:49:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489164
            [orderIds] => []
        )

)

[2025-05-17 21:49:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489175
            [orderIds] => []
        )

)

[2025-05-17 21:49:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489186
            [orderIds] => []
        )

)

[2025-05-17 21:49:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489197
            [orderIds] => []
        )

)

[2025-05-17 21:50:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489208
            [orderIds] => []
        )

)

[2025-05-17 21:50:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489219
            [orderIds] => []
        )

)

[2025-05-17 21:50:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489230
            [orderIds] => []
        )

)

[2025-05-17 21:50:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489241
            [orderIds] => []
        )

)

[2025-05-17 21:50:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489252
            [orderIds] => []
        )

)

[2025-05-17 21:51:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489263
            [orderIds] => []
        )

)

[2025-05-17 21:51:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489274
            [orderIds] => []
        )

)

[2025-05-17 21:51:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489285
            [orderIds] => []
        )

)

[2025-05-17 21:51:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489296
            [orderIds] => []
        )

)

[2025-05-17 21:51:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489307
            [orderIds] => []
        )

)

[2025-05-17 21:51:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489318
            [orderIds] => []
        )

)

[2025-05-17 21:52:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489329
            [orderIds] => []
        )

)

[2025-05-17 21:52:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489340
            [orderIds] => []
        )

)

[2025-05-17 21:52:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489351
            [orderIds] => []
        )

)

[2025-05-17 21:52:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489362
            [orderIds] => []
        )

)

[2025-05-17 21:52:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489373
            [orderIds] => []
        )

)

[2025-05-17 21:53:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489384
            [orderIds] => []
        )

)

[2025-05-17 21:53:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489395
            [orderIds] => []
        )

)

[2025-05-17 21:53:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489406
            [orderIds] => []
        )

)

[2025-05-17 21:53:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489417
            [orderIds] => []
        )

)

[2025-05-17 21:53:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489428
            [orderIds] => []
        )

)

[2025-05-17 21:53:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489439
            [orderIds] => []
        )

)

[2025-05-17 21:54:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489450
            [orderIds] => []
        )

)

[2025-05-17 21:54:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489461
            [orderIds] => []
        )

)

[2025-05-17 21:54:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489472
            [orderIds] => []
        )

)

[2025-05-17 21:54:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489483
            [orderIds] => []
        )

)

[2025-05-17 21:54:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489494
            [orderIds] => []
        )

)

[2025-05-17 21:55:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489505
            [orderIds] => []
        )

)

[2025-05-17 21:55:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489516
            [orderIds] => []
        )

)

[2025-05-17 21:55:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489527
            [orderIds] => []
        )

)

[2025-05-17 21:55:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489538
            [orderIds] => []
        )

)

[2025-05-17 21:55:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489549
            [orderIds] => []
        )

)

[2025-05-17 21:56:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489560
            [orderIds] => []
        )

)

[2025-05-17 21:56:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489571
            [orderIds] => []
        )

)

[2025-05-17 21:56:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489582
            [orderIds] => []
        )

)

[2025-05-17 21:56:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489593
            [orderIds] => []
        )

)

[2025-05-17 21:56:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 21:56:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 21:56:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 21:56:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 21:56:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489604
            [orderIds] => []
        )

)

[2025-05-17 21:56:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489615
            [orderIds] => []
        )

)

[2025-05-17 21:57:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489626
            [orderIds] => []
        )

)

[2025-05-17 21:57:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489637
            [orderIds] => []
        )

)

[2025-05-17 21:57:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489648
            [orderIds] => []
        )

)

[2025-05-17 21:57:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489659
            [orderIds] => []
        )

)

[2025-05-17 21:57:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489670
            [orderIds] => []
        )

)

[2025-05-17 21:58:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489681
            [orderIds] => []
        )

)

[2025-05-17 21:58:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489692
            [orderIds] => []
        )

)

[2025-05-17 21:58:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489703
            [orderIds] => []
        )

)

[2025-05-17 21:58:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489714
            [orderIds] => []
        )

)

[2025-05-17 21:58:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489725
            [orderIds] => []
        )

)

[2025-05-17 21:58:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489736
            [orderIds] => []
        )

)

[2025-05-17 21:59:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489747
            [orderIds] => []
        )

)

[2025-05-17 21:59:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489758
            [orderIds] => []
        )

)

[2025-05-17 21:59:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489769
            [orderIds] => []
        )

)

[2025-05-17 21:59:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489780
            [orderIds] => []
        )

)

[2025-05-17 21:59:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489791
            [orderIds] => []
        )

)

[2025-05-17 22:00:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489802
            [orderIds] => []
        )

)

[2025-05-17 22:00:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489813
            [orderIds] => []
        )

)

[2025-05-17 22:00:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489824
            [orderIds] => []
        )

)

[2025-05-17 22:00:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489835
            [orderIds] => []
        )

)

[2025-05-17 22:00:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489846
            [orderIds] => []
        )

)

[2025-05-17 22:00:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489857
            [orderIds] => []
        )

)

[2025-05-17 22:01:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489868
            [orderIds] => []
        )

)

[2025-05-17 22:01:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489879
            [orderIds] => []
        )

)

[2025-05-17 22:01:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489890
            [orderIds] => []
        )

)

[2025-05-17 22:01:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489901
            [orderIds] => []
        )

)

[2025-05-17 22:01:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489912
            [orderIds] => []
        )

)

[2025-05-17 22:02:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489923
            [orderIds] => []
        )

)

[2025-05-17 22:02:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489934
            [orderIds] => []
        )

)

[2025-05-17 22:02:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489945
            [orderIds] => []
        )

)

[2025-05-17 22:02:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489956
            [orderIds] => []
        )

)

[2025-05-17 22:02:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489967
            [orderIds] => []
        )

)

[2025-05-17 22:02:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489978
            [orderIds] => []
        )

)

[2025-05-17 22:03:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747489989
            [orderIds] => []
        )

)

[2025-05-17 22:03:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490000
            [orderIds] => []
        )

)

[2025-05-17 22:03:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490011
            [orderIds] => []
        )

)

[2025-05-17 22:03:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490022
            [orderIds] => []
        )

)

[2025-05-17 22:03:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490033
            [orderIds] => []
        )

)

[2025-05-17 22:04:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490044
            [orderIds] => []
        )

)

[2025-05-17 22:04:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490055
            [orderIds] => []
        )

)

[2025-05-17 22:04:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490066
            [orderIds] => []
        )

)

[2025-05-17 22:04:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490077
            [orderIds] => []
        )

)

[2025-05-17 22:04:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490088
            [orderIds] => []
        )

)

[2025-05-17 22:04:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490099
            [orderIds] => []
        )

)

[2025-05-17 22:05:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490110
            [orderIds] => []
        )

)

[2025-05-17 22:05:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490121
            [orderIds] => []
        )

)

[2025-05-17 22:05:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490132
            [orderIds] => []
        )

)

[2025-05-17 22:05:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490143
            [orderIds] => []
        )

)

[2025-05-17 22:05:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490154
            [orderIds] => []
        )

)

[2025-05-17 22:06:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490165
            [orderIds] => []
        )

)

[2025-05-17 22:06:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490176
            [orderIds] => []
        )

)

[2025-05-17 22:06:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490187
            [orderIds] => []
        )

)

[2025-05-17 22:06:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 22:06:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 22:06:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 22:06:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 22:06:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490198
            [orderIds] => []
        )

)

[2025-05-17 22:06:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490209
            [orderIds] => []
        )

)

[2025-05-17 22:07:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490220
            [orderIds] => []
        )

)

[2025-05-17 22:07:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490231
            [orderIds] => []
        )

)

[2025-05-17 22:07:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490242
            [orderIds] => []
        )

)

[2025-05-17 22:07:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490253
            [orderIds] => []
        )

)

[2025-05-17 22:07:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490264
            [orderIds] => []
        )

)

[2025-05-17 22:07:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490275
            [orderIds] => []
        )

)

[2025-05-17 22:08:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490286
            [orderIds] => []
        )

)

[2025-05-17 22:08:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747404497
            [orderIds] => []
        )

)

[2025-05-17 22:08:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1746886097
            [orderIds] => []
        )

)

[2025-05-17 22:08:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1746886097
            [orderIds] => []
        )

)

[2025-05-17 22:08:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490297
            [orderIds] => []
        )

)

[2025-05-17 22:08:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-17 22:08:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-17 22:08:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-17 22:08:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-17 22:08:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-17 22:08:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490308
            [orderIds] => []
        )

)

[2025-05-17 22:08:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490319
            [orderIds] => []
        )

)

[2025-05-17 22:08:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490330
            [orderIds] => []
        )

)

[2025-05-17 22:09:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490341
            [orderIds] => []
        )

)

[2025-05-17 22:09:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490352
            [orderIds] => []
        )

)

[2025-05-17 22:09:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490363
            [orderIds] => []
        )

)

[2025-05-17 22:09:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490374
            [orderIds] => []
        )

)

[2025-05-17 22:09:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490385
            [orderIds] => []
        )

)

[2025-05-17 22:09:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490396
            [orderIds] => []
        )

)

[2025-05-17 22:10:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490407
            [orderIds] => []
        )

)

[2025-05-17 22:10:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490418
            [orderIds] => []
        )

)

[2025-05-17 22:10:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490429
            [orderIds] => []
        )

)

[2025-05-17 22:10:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490440
            [orderIds] => []
        )

)

[2025-05-17 22:10:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490451
            [orderIds] => []
        )

)

[2025-05-17 22:11:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490462
            [orderIds] => []
        )

)

[2025-05-17 22:11:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490473
            [orderIds] => []
        )

)

[2025-05-17 22:11:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490484
            [orderIds] => []
        )

)

[2025-05-17 22:11:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490495
            [orderIds] => []
        )

)

[2025-05-17 22:11:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490506
            [orderIds] => []
        )

)

[2025-05-17 22:11:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490517
            [orderIds] => []
        )

)

[2025-05-17 22:12:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490528
            [orderIds] => []
        )

)

[2025-05-17 22:12:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490539
            [orderIds] => []
        )

)

[2025-05-17 22:12:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490550
            [orderIds] => []
        )

)

[2025-05-17 22:12:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490561
            [orderIds] => []
        )

)

[2025-05-17 22:12:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490572
            [orderIds] => []
        )

)

[2025-05-17 22:13:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490583
            [orderIds] => []
        )

)

[2025-05-17 22:13:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490594
            [orderIds] => []
        )

)

[2025-05-17 22:13:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490605
            [orderIds] => []
        )

)

[2025-05-17 22:13:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490616
            [orderIds] => []
        )

)

[2025-05-17 22:13:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490627
            [orderIds] => []
        )

)

[2025-05-17 22:13:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490638
            [orderIds] => []
        )

)

[2025-05-17 22:14:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490649
            [orderIds] => []
        )

)

[2025-05-17 22:14:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490660
            [orderIds] => []
        )

)

[2025-05-17 22:14:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490671
            [orderIds] => []
        )

)

[2025-05-17 22:14:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490682
            [orderIds] => []
        )

)

[2025-05-17 22:14:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490693
            [orderIds] => []
        )

)

[2025-05-17 22:15:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490704
            [orderIds] => []
        )

)

[2025-05-17 22:15:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490715
            [orderIds] => []
        )

)

[2025-05-17 22:15:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490726
            [orderIds] => []
        )

)

[2025-05-17 22:15:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490737
            [orderIds] => []
        )

)

[2025-05-17 22:15:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490748
            [orderIds] => []
        )

)

[2025-05-17 22:15:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490759
            [orderIds] => []
        )

)

[2025-05-17 22:16:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490770
            [orderIds] => []
        )

)

[2025-05-17 22:16:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490781
            [orderIds] => []
        )

)

[2025-05-17 22:16:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490792
            [orderIds] => []
        )

)

[2025-05-17 22:16:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 22:16:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 22:16:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 22:16:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 22:16:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490803
            [orderIds] => []
        )

)

[2025-05-17 22:16:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490814
            [orderIds] => []
        )

)

[2025-05-17 22:17:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490825
            [orderIds] => []
        )

)

[2025-05-17 22:17:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490836
            [orderIds] => []
        )

)

[2025-05-17 22:17:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490847
            [orderIds] => []
        )

)

[2025-05-17 22:17:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490858
            [orderIds] => []
        )

)

[2025-05-17 22:17:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490869
            [orderIds] => []
        )

)

[2025-05-17 22:18:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490880
            [orderIds] => []
        )

)

[2025-05-17 22:18:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490891
            [orderIds] => []
        )

)

[2025-05-17 22:18:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490902
            [orderIds] => []
        )

)

[2025-05-17 22:18:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490913
            [orderIds] => []
        )

)

[2025-05-17 22:18:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490924
            [orderIds] => []
        )

)

[2025-05-17 22:18:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490935
            [orderIds] => []
        )

)

[2025-05-17 22:19:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490946
            [orderIds] => []
        )

)

[2025-05-17 22:19:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490957
            [orderIds] => []
        )

)

[2025-05-17 22:19:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490968
            [orderIds] => []
        )

)

[2025-05-17 22:19:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490979
            [orderIds] => []
        )

)

[2025-05-17 22:19:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747490990
            [orderIds] => []
        )

)

[2025-05-17 22:20:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491001
            [orderIds] => []
        )

)

[2025-05-17 22:20:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491012
            [orderIds] => []
        )

)

[2025-05-17 22:20:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491023
            [orderIds] => []
        )

)

[2025-05-17 22:20:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491034
            [orderIds] => []
        )

)

[2025-05-17 22:20:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491045
            [orderIds] => []
        )

)

[2025-05-17 22:20:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491056
            [orderIds] => []
        )

)

[2025-05-17 22:21:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491067
            [orderIds] => []
        )

)

[2025-05-17 22:21:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491078
            [orderIds] => []
        )

)

[2025-05-17 22:21:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491089
            [orderIds] => []
        )

)

[2025-05-17 22:21:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491100
            [orderIds] => []
        )

)

[2025-05-17 22:21:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491111
            [orderIds] => []
        )

)

[2025-05-17 22:22:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491122
            [orderIds] => []
        )

)

[2025-05-17 22:22:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491133
            [orderIds] => []
        )

)

[2025-05-17 22:22:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491144
            [orderIds] => []
        )

)

[2025-05-17 22:22:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491155
            [orderIds] => []
        )

)

[2025-05-17 22:22:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491166
            [orderIds] => []
        )

)

[2025-05-17 22:22:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491177
            [orderIds] => []
        )

)

[2025-05-17 22:23:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491188
            [orderIds] => []
        )

)

[2025-05-17 22:23:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491199
            [orderIds] => []
        )

)

[2025-05-17 22:23:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491210
            [orderIds] => []
        )

)

[2025-05-17 22:23:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491221
            [orderIds] => []
        )

)

[2025-05-17 22:23:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491232
            [orderIds] => []
        )

)

[2025-05-17 22:24:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491243
            [orderIds] => []
        )

)

[2025-05-17 22:24:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491254
            [orderIds] => []
        )

)

[2025-05-17 22:24:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491265
            [orderIds] => []
        )

)

[2025-05-17 22:24:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491276
            [orderIds] => []
        )

)

[2025-05-17 22:24:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491287
            [orderIds] => []
        )

)

[2025-05-17 22:24:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491298
            [orderIds] => []
        )

)

[2025-05-17 22:25:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491309
            [orderIds] => []
        )

)

[2025-05-17 22:25:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491320
            [orderIds] => []
        )

)

[2025-05-17 22:25:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491331
            [orderIds] => []
        )

)

[2025-05-17 22:25:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491342
            [orderIds] => []
        )

)

[2025-05-17 22:25:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491353
            [orderIds] => []
        )

)

[2025-05-17 22:26:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491364
            [orderIds] => []
        )

)

[2025-05-17 22:26:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491375
            [orderIds] => []
        )

)

[2025-05-17 22:26:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491386
            [orderIds] => []
        )

)

[2025-05-17 22:26:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 22:26:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 22:26:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 22:26:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 22:26:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491397
            [orderIds] => []
        )

)

[2025-05-17 22:26:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491408
            [orderIds] => []
        )

)

[2025-05-17 22:26:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491419
            [orderIds] => []
        )

)

[2025-05-17 22:27:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491430
            [orderIds] => []
        )

)

[2025-05-17 22:27:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491441
            [orderIds] => []
        )

)

[2025-05-17 22:27:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491452
            [orderIds] => []
        )

)

[2025-05-17 22:27:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491463
            [orderIds] => []
        )

)

[2025-05-17 22:27:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491474
            [orderIds] => []
        )

)

[2025-05-17 22:28:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491485
            [orderIds] => []
        )

)

[2025-05-17 22:28:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491496
            [orderIds] => []
        )

)

[2025-05-17 22:28:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491507
            [orderIds] => []
        )

)

[2025-05-17 22:28:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491518
            [orderIds] => []
        )

)

[2025-05-17 22:28:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491529
            [orderIds] => []
        )

)

[2025-05-17 22:29:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491540
            [orderIds] => []
        )

)

[2025-05-17 22:29:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491551
            [orderIds] => []
        )

)

[2025-05-17 22:29:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491562
            [orderIds] => []
        )

)

[2025-05-17 22:29:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491573
            [orderIds] => []
        )

)

[2025-05-17 22:29:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491584
            [orderIds] => []
        )

)

[2025-05-17 22:29:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491595
            [orderIds] => []
        )

)

[2025-05-17 22:30:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491606
            [orderIds] => []
        )

)

[2025-05-17 22:30:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491617
            [orderIds] => []
        )

)

[2025-05-17 22:30:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491628
            [orderIds] => []
        )

)

[2025-05-17 22:30:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491639
            [orderIds] => []
        )

)

[2025-05-17 22:30:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491650
            [orderIds] => []
        )

)

[2025-05-17 22:31:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491661
            [orderIds] => []
        )

)

[2025-05-17 22:31:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491672
            [orderIds] => []
        )

)

[2025-05-17 22:31:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491683
            [orderIds] => []
        )

)

[2025-05-17 22:31:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491694
            [orderIds] => []
        )

)

[2025-05-17 22:31:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491705
            [orderIds] => []
        )

)

[2025-05-17 22:31:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491716
            [orderIds] => []
        )

)

[2025-05-17 22:32:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491727
            [orderIds] => []
        )

)

[2025-05-17 22:32:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491738
            [orderIds] => []
        )

)

[2025-05-17 22:32:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491749
            [orderIds] => []
        )

)

[2025-05-17 22:32:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491760
            [orderIds] => []
        )

)

[2025-05-17 22:32:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491771
            [orderIds] => []
        )

)

[2025-05-17 22:33:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491782
            [orderIds] => []
        )

)

[2025-05-17 22:33:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491793
            [orderIds] => []
        )

)

[2025-05-17 22:33:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491804
            [orderIds] => []
        )

)

[2025-05-17 22:33:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491815
            [orderIds] => []
        )

)

[2025-05-17 22:33:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491826
            [orderIds] => []
        )

)

[2025-05-17 22:33:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491837
            [orderIds] => []
        )

)

[2025-05-17 22:34:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491848
            [orderIds] => []
        )

)

[2025-05-17 22:34:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491859
            [orderIds] => []
        )

)

[2025-05-17 22:34:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491870
            [orderIds] => []
        )

)

[2025-05-17 22:34:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491881
            [orderIds] => []
        )

)

[2025-05-17 22:34:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491892
            [orderIds] => []
        )

)

[2025-05-17 22:35:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491903
            [orderIds] => []
        )

)

[2025-05-17 22:35:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491914
            [orderIds] => []
        )

)

[2025-05-17 22:35:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491925
            [orderIds] => []
        )

)

[2025-05-17 22:35:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491936
            [orderIds] => []
        )

)

[2025-05-17 22:35:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491947
            [orderIds] => []
        )

)

[2025-05-17 22:35:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491958
            [orderIds] => []
        )

)

[2025-05-17 22:36:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491969
            [orderIds] => []
        )

)

[2025-05-17 22:36:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491980
            [orderIds] => []
        )

)

[2025-05-17 22:36:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747491991
            [orderIds] => []
        )

)

[2025-05-17 22:36:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 22:36:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 22:36:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 22:36:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 22:36:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492002
            [orderIds] => []
        )

)

[2025-05-17 22:36:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492013
            [orderIds] => []
        )

)

[2025-05-17 22:37:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492024
            [orderIds] => []
        )

)

[2025-05-17 22:37:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492035
            [orderIds] => []
        )

)

[2025-05-17 22:37:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492046
            [orderIds] => []
        )

)

[2025-05-17 22:37:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492057
            [orderIds] => []
        )

)

[2025-05-17 22:37:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492068
            [orderIds] => []
        )

)

[2025-05-17 22:37:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492079
            [orderIds] => []
        )

)

[2025-05-17 22:38:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492090
            [orderIds] => []
        )

)

[2025-05-17 22:38:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747406298
            [orderIds] => []
        )

)

[2025-05-17 22:38:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1746887898
            [orderIds] => []
        )

)

[2025-05-17 22:38:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1746887898
            [orderIds] => []
        )

)

[2025-05-17 22:38:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-17 22:38:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-17 22:38:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-17 22:38:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-17 22:38:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-17 22:38:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492101
            [orderIds] => []
        )

)

[2025-05-17 22:38:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492112
            [orderIds] => []
        )

)

[2025-05-17 22:38:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492123
            [orderIds] => []
        )

)

[2025-05-17 22:38:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492134
            [orderIds] => []
        )

)

[2025-05-17 22:39:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492145
            [orderIds] => []
        )

)

[2025-05-17 22:39:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492156
            [orderIds] => []
        )

)

[2025-05-17 22:39:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492167
            [orderIds] => []
        )

)

[2025-05-17 22:39:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492178
            [orderIds] => []
        )

)

[2025-05-17 22:39:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492189
            [orderIds] => []
        )

)

[2025-05-17 22:40:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492200
            [orderIds] => []
        )

)

[2025-05-17 22:40:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492211
            [orderIds] => []
        )

)

[2025-05-17 22:40:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492222
            [orderIds] => []
        )

)

[2025-05-17 22:40:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492233
            [orderIds] => []
        )

)

[2025-05-17 22:40:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492244
            [orderIds] => []
        )

)

[2025-05-17 22:40:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492255
            [orderIds] => []
        )

)

[2025-05-17 22:41:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492266
            [orderIds] => []
        )

)

[2025-05-17 22:41:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492277
            [orderIds] => []
        )

)

[2025-05-17 22:41:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492288
            [orderIds] => []
        )

)

[2025-05-17 22:41:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492299
            [orderIds] => []
        )

)

[2025-05-17 22:41:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492310
            [orderIds] => []
        )

)

[2025-05-17 22:42:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492321
            [orderIds] => []
        )

)

[2025-05-17 22:42:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492332
            [orderIds] => []
        )

)

[2025-05-17 22:42:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492343
            [orderIds] => []
        )

)

[2025-05-17 22:42:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492354
            [orderIds] => []
        )

)

[2025-05-17 22:42:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492365
            [orderIds] => []
        )

)

[2025-05-17 22:42:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492376
            [orderIds] => []
        )

)

[2025-05-17 22:43:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492387
            [orderIds] => []
        )

)

[2025-05-17 22:43:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492398
            [orderIds] => []
        )

)

[2025-05-17 22:43:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492409
            [orderIds] => []
        )

)

[2025-05-17 22:43:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492420
            [orderIds] => []
        )

)

[2025-05-17 22:43:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492431
            [orderIds] => []
        )

)

[2025-05-17 22:44:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492442
            [orderIds] => []
        )

)

[2025-05-17 22:44:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492453
            [orderIds] => []
        )

)

[2025-05-17 22:44:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492464
            [orderIds] => []
        )

)

[2025-05-17 22:44:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492475
            [orderIds] => []
        )

)

[2025-05-17 22:44:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492486
            [orderIds] => []
        )

)

[2025-05-17 22:44:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492497
            [orderIds] => []
        )

)

[2025-05-17 22:45:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492508
            [orderIds] => []
        )

)

[2025-05-17 22:45:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492519
            [orderIds] => []
        )

)

[2025-05-17 22:45:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492530
            [orderIds] => []
        )

)

[2025-05-17 22:45:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492541
            [orderIds] => []
        )

)

[2025-05-17 22:45:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492552
            [orderIds] => []
        )

)

[2025-05-17 22:46:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492563
            [orderIds] => []
        )

)

[2025-05-17 22:46:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492574
            [orderIds] => []
        )

)

[2025-05-17 22:46:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492585
            [orderIds] => []
        )

)

[2025-05-17 22:46:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492596
            [orderIds] => []
        )

)

[2025-05-17 22:46:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 22:46:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 22:46:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 22:46:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 22:46:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492607
            [orderIds] => []
        )

)

[2025-05-17 22:46:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492618
            [orderIds] => []
        )

)

[2025-05-17 22:47:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492629
            [orderIds] => []
        )

)

[2025-05-17 22:47:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492640
            [orderIds] => []
        )

)

[2025-05-17 22:47:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492651
            [orderIds] => []
        )

)

[2025-05-17 22:47:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492662
            [orderIds] => []
        )

)

[2025-05-17 22:47:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492673
            [orderIds] => []
        )

)

[2025-05-17 22:48:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492684
            [orderIds] => []
        )

)

[2025-05-17 22:48:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492695
            [orderIds] => []
        )

)

[2025-05-17 22:48:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492706
            [orderIds] => []
        )

)

[2025-05-17 22:48:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492717
            [orderIds] => []
        )

)

[2025-05-17 22:48:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492728
            [orderIds] => []
        )

)

[2025-05-17 22:48:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492739
            [orderIds] => []
        )

)

[2025-05-17 22:49:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492750
            [orderIds] => []
        )

)

[2025-05-17 22:49:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492761
            [orderIds] => []
        )

)

[2025-05-17 22:49:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492772
            [orderIds] => []
        )

)

[2025-05-17 22:49:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492783
            [orderIds] => []
        )

)

[2025-05-17 22:49:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492794
            [orderIds] => []
        )

)

[2025-05-17 22:50:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492805
            [orderIds] => []
        )

)

[2025-05-17 22:50:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492816
            [orderIds] => []
        )

)

[2025-05-17 22:50:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492827
            [orderIds] => []
        )

)

[2025-05-17 22:50:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492838
            [orderIds] => []
        )

)

[2025-05-17 22:50:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492849
            [orderIds] => []
        )

)

[2025-05-17 22:51:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492860
            [orderIds] => []
        )

)

[2025-05-17 22:51:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492871
            [orderIds] => []
        )

)

[2025-05-17 22:51:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492882
            [orderIds] => []
        )

)

[2025-05-17 22:51:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492893
            [orderIds] => []
        )

)

[2025-05-17 22:51:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492904
            [orderIds] => []
        )

)

[2025-05-17 22:51:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492915
            [orderIds] => []
        )

)

[2025-05-17 22:52:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492926
            [orderIds] => []
        )

)

[2025-05-17 22:52:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492937
            [orderIds] => []
        )

)

[2025-05-17 22:52:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492948
            [orderIds] => []
        )

)

[2025-05-17 22:52:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492959
            [orderIds] => []
        )

)

[2025-05-17 22:52:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492970
            [orderIds] => []
        )

)

[2025-05-17 22:53:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492981
            [orderIds] => []
        )

)

[2025-05-17 22:53:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747492992
            [orderIds] => []
        )

)

[2025-05-17 22:53:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493003
            [orderIds] => []
        )

)

[2025-05-17 22:53:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493014
            [orderIds] => []
        )

)

[2025-05-17 22:53:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493025
            [orderIds] => []
        )

)

[2025-05-17 22:53:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493036
            [orderIds] => []
        )

)

[2025-05-17 22:54:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493047
            [orderIds] => []
        )

)

[2025-05-17 22:54:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493058
            [orderIds] => []
        )

)

[2025-05-17 22:54:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493069
            [orderIds] => []
        )

)

[2025-05-17 22:54:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493080
            [orderIds] => []
        )

)

[2025-05-17 22:54:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493091
            [orderIds] => []
        )

)

[2025-05-17 22:55:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493102
            [orderIds] => []
        )

)

[2025-05-17 22:55:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493113
            [orderIds] => []
        )

)

[2025-05-17 22:55:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493124
            [orderIds] => []
        )

)

[2025-05-17 22:55:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493135
            [orderIds] => []
        )

)

[2025-05-17 22:55:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493146
            [orderIds] => []
        )

)

[2025-05-17 22:55:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493157
            [orderIds] => []
        )

)

[2025-05-17 22:56:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493168
            [orderIds] => []
        )

)

[2025-05-17 22:56:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493179
            [orderIds] => []
        )

)

[2025-05-17 22:56:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493190
            [orderIds] => []
        )

)

[2025-05-17 22:56:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 22:56:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 22:56:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 22:56:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 22:56:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493201
            [orderIds] => []
        )

)

[2025-05-17 22:56:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493212
            [orderIds] => []
        )

)

[2025-05-17 22:57:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493223
            [orderIds] => []
        )

)

[2025-05-17 22:57:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493234
            [orderIds] => []
        )

)

[2025-05-17 22:57:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493245
            [orderIds] => []
        )

)

[2025-05-17 22:57:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493256
            [orderIds] => []
        )

)

[2025-05-17 22:57:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493267
            [orderIds] => []
        )

)

[2025-05-17 22:57:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493278
            [orderIds] => []
        )

)

[2025-05-17 22:58:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493289
            [orderIds] => []
        )

)

[2025-05-17 22:58:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493300
            [orderIds] => []
        )

)

[2025-05-17 22:58:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493311
            [orderIds] => []
        )

)

[2025-05-17 22:58:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493322
            [orderIds] => []
        )

)

[2025-05-17 22:58:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493333
            [orderIds] => []
        )

)

[2025-05-17 22:59:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493344
            [orderIds] => []
        )

)

[2025-05-17 22:59:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493355
            [orderIds] => []
        )

)

[2025-05-17 22:59:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493366
            [orderIds] => []
        )

)

[2025-05-17 22:59:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493377
            [orderIds] => []
        )

)

[2025-05-17 22:59:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493388
            [orderIds] => []
        )

)

[2025-05-17 22:59:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493399
            [orderIds] => []
        )

)

[2025-05-17 23:00:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493410
            [orderIds] => []
        )

)

[2025-05-17 23:00:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493421
            [orderIds] => []
        )

)

[2025-05-17 23:00:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493432
            [orderIds] => []
        )

)

[2025-05-17 23:00:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493443
            [orderIds] => []
        )

)

[2025-05-17 23:00:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493454
            [orderIds] => []
        )

)

[2025-05-17 23:01:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493465
            [orderIds] => []
        )

)

[2025-05-17 23:01:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493476
            [orderIds] => []
        )

)

[2025-05-17 23:01:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493487
            [orderIds] => []
        )

)

[2025-05-17 23:01:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493498
            [orderIds] => []
        )

)

[2025-05-17 23:01:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493509
            [orderIds] => []
        )

)

[2025-05-17 23:02:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493520
            [orderIds] => []
        )

)

[2025-05-17 23:02:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493531
            [orderIds] => []
        )

)

[2025-05-17 23:02:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493542
            [orderIds] => []
        )

)

[2025-05-17 23:02:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493553
            [orderIds] => []
        )

)

[2025-05-17 23:02:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493564
            [orderIds] => []
        )

)

[2025-05-17 23:02:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493575
            [orderIds] => []
        )

)

[2025-05-17 23:03:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493586
            [orderIds] => []
        )

)

[2025-05-17 23:03:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493597
            [orderIds] => []
        )

)

[2025-05-17 23:03:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493608
            [orderIds] => []
        )

)

[2025-05-17 23:03:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493619
            [orderIds] => []
        )

)

[2025-05-17 23:03:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493630
            [orderIds] => []
        )

)

[2025-05-17 23:04:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493641
            [orderIds] => []
        )

)

[2025-05-17 23:04:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493652
            [orderIds] => []
        )

)

[2025-05-17 23:04:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493663
            [orderIds] => []
        )

)

[2025-05-17 23:04:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493674
            [orderIds] => []
        )

)

[2025-05-17 23:04:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493685
            [orderIds] => []
        )

)

[2025-05-17 23:04:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493696
            [orderIds] => []
        )

)

[2025-05-17 23:05:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493707
            [orderIds] => []
        )

)

[2025-05-17 23:05:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493718
            [orderIds] => []
        )

)

[2025-05-17 23:05:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493729
            [orderIds] => []
        )

)

[2025-05-17 23:05:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493740
            [orderIds] => []
        )

)

[2025-05-17 23:05:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493751
            [orderIds] => []
        )

)

[2025-05-17 23:06:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493762
            [orderIds] => []
        )

)

[2025-05-17 23:06:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493773
            [orderIds] => []
        )

)

[2025-05-17 23:06:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493784
            [orderIds] => []
        )

)

[2025-05-17 23:06:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493795
            [orderIds] => []
        )

)

[2025-05-17 23:06:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 23:06:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 23:06:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 23:06:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 23:06:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493806
            [orderIds] => []
        )

)

[2025-05-17 23:06:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493817
            [orderIds] => []
        )

)

[2025-05-17 23:07:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493828
            [orderIds] => []
        )

)

[2025-05-17 23:07:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493839
            [orderIds] => []
        )

)

[2025-05-17 23:07:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493850
            [orderIds] => []
        )

)

[2025-05-17 23:07:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493861
            [orderIds] => []
        )

)

[2025-05-17 23:07:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493872
            [orderIds] => []
        )

)

[2025-05-17 23:08:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493883
            [orderIds] => []
        )

)

[2025-05-17 23:08:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493894
            [orderIds] => []
        )

)

[2025-05-17 23:08:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747408099
            [orderIds] => []
        )

)

[2025-05-17 23:08:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1746889699
            [orderIds] => []
        )

)

[2025-05-17 23:08:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1746889699
            [orderIds] => []
        )

)

[2025-05-17 23:08:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-17 23:08:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-17 23:08:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-17 23:08:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-17 23:08:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-17 23:08:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493905
            [orderIds] => []
        )

)

[2025-05-17 23:08:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493916
            [orderIds] => []
        )

)

[2025-05-17 23:08:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493927
            [orderIds] => []
        )

)

[2025-05-17 23:08:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493938
            [orderIds] => []
        )

)

[2025-05-17 23:09:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493949
            [orderIds] => []
        )

)

[2025-05-17 23:09:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493960
            [orderIds] => []
        )

)

[2025-05-17 23:09:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493971
            [orderIds] => []
        )

)

[2025-05-17 23:09:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493982
            [orderIds] => []
        )

)

[2025-05-17 23:09:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747493993
            [orderIds] => []
        )

)

[2025-05-17 23:10:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494004
            [orderIds] => []
        )

)

[2025-05-17 23:10:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494015
            [orderIds] => []
        )

)

[2025-05-17 23:10:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494026
            [orderIds] => []
        )

)

[2025-05-17 23:10:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494037
            [orderIds] => []
        )

)

[2025-05-17 23:10:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494048
            [orderIds] => []
        )

)

[2025-05-17 23:10:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494059
            [orderIds] => []
        )

)

[2025-05-17 23:11:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494070
            [orderIds] => []
        )

)

[2025-05-17 23:11:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494081
            [orderIds] => []
        )

)

[2025-05-17 23:11:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494092
            [orderIds] => []
        )

)

[2025-05-17 23:11:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494103
            [orderIds] => []
        )

)

[2025-05-17 23:11:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494114
            [orderIds] => []
        )

)

[2025-05-17 23:12:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494125
            [orderIds] => []
        )

)

[2025-05-17 23:12:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494136
            [orderIds] => []
        )

)

[2025-05-17 23:12:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494147
            [orderIds] => []
        )

)

[2025-05-17 23:12:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494157
            [orderIds] => []
        )

)

[2025-05-17 23:12:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494167
            [orderIds] => []
        )

)

[2025-05-17 23:12:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494178
            [orderIds] => []
        )

)

[2025-05-17 23:13:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494189
            [orderIds] => []
        )

)

[2025-05-17 23:13:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494200
            [orderIds] => []
        )

)

[2025-05-17 23:13:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494211
            [orderIds] => []
        )

)

[2025-05-17 23:13:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494222
            [orderIds] => []
        )

)

[2025-05-17 23:13:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494233
            [orderIds] => []
        )

)

[2025-05-17 23:14:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494244
            [orderIds] => []
        )

)

[2025-05-17 23:14:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494255
            [orderIds] => []
        )

)

[2025-05-17 23:14:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494266
            [orderIds] => []
        )

)

[2025-05-17 23:14:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494277
            [orderIds] => []
        )

)

[2025-05-17 23:14:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494288
            [orderIds] => []
        )

)

[2025-05-17 23:14:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494299
            [orderIds] => []
        )

)

[2025-05-17 23:15:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494310
            [orderIds] => []
        )

)

[2025-05-17 23:15:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494321
            [orderIds] => []
        )

)

[2025-05-17 23:15:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494332
            [orderIds] => []
        )

)

[2025-05-17 23:15:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494343
            [orderIds] => []
        )

)

[2025-05-17 23:15:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494354
            [orderIds] => []
        )

)

[2025-05-17 23:16:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494365
            [orderIds] => []
        )

)

[2025-05-17 23:16:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494376
            [orderIds] => []
        )

)

[2025-05-17 23:16:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494387
            [orderIds] => []
        )

)

[2025-05-17 23:16:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494398
            [orderIds] => []
        )

)

[2025-05-17 23:16:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 23:16:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 23:16:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 23:16:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 23:16:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494409
            [orderIds] => []
        )

)

[2025-05-17 23:17:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494420
            [orderIds] => []
        )

)

[2025-05-17 23:17:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494431
            [orderIds] => []
        )

)

[2025-05-17 23:17:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494442
            [orderIds] => []
        )

)

[2025-05-17 23:17:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494453
            [orderIds] => []
        )

)

[2025-05-17 23:17:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494464
            [orderIds] => []
        )

)

[2025-05-17 23:17:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494475
            [orderIds] => []
        )

)

[2025-05-17 23:18:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494486
            [orderIds] => []
        )

)

[2025-05-17 23:18:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494497
            [orderIds] => []
        )

)

[2025-05-17 23:18:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494508
            [orderIds] => []
        )

)

[2025-05-17 23:18:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494519
            [orderIds] => []
        )

)

[2025-05-17 23:18:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494530
            [orderIds] => []
        )

)

[2025-05-17 23:19:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494541
            [orderIds] => []
        )

)

[2025-05-17 23:19:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494552
            [orderIds] => []
        )

)

[2025-05-17 23:19:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494563
            [orderIds] => []
        )

)

[2025-05-17 23:19:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494574
            [orderIds] => []
        )

)

[2025-05-17 23:19:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494585
            [orderIds] => []
        )

)

[2025-05-17 23:19:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494596
            [orderIds] => []
        )

)

[2025-05-17 23:20:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494607
            [orderIds] => []
        )

)

[2025-05-17 23:20:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494618
            [orderIds] => []
        )

)

[2025-05-17 23:20:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494629
            [orderIds] => []
        )

)

[2025-05-17 23:20:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494640
            [orderIds] => []
        )

)

[2025-05-17 23:20:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494651
            [orderIds] => []
        )

)

[2025-05-17 23:21:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494662
            [orderIds] => []
        )

)

[2025-05-17 23:21:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494673
            [orderIds] => []
        )

)

[2025-05-17 23:21:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494684
            [orderIds] => []
        )

)

[2025-05-17 23:21:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494695
            [orderIds] => []
        )

)

[2025-05-17 23:21:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494706
            [orderIds] => []
        )

)

[2025-05-17 23:21:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494717
            [orderIds] => []
        )

)

[2025-05-17 23:22:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494728
            [orderIds] => []
        )

)

[2025-05-17 23:22:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494739
            [orderIds] => []
        )

)

[2025-05-17 23:22:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494750
            [orderIds] => []
        )

)

[2025-05-17 23:22:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494761
            [orderIds] => []
        )

)

[2025-05-17 23:22:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494772
            [orderIds] => []
        )

)

[2025-05-17 23:23:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494783
            [orderIds] => []
        )

)

[2025-05-17 23:23:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494794
            [orderIds] => []
        )

)

[2025-05-17 23:23:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494805
            [orderIds] => []
        )

)

[2025-05-17 23:23:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494816
            [orderIds] => []
        )

)

[2025-05-17 23:23:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494827
            [orderIds] => []
        )

)

[2025-05-17 23:23:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494838
            [orderIds] => []
        )

)

[2025-05-17 23:24:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494849
            [orderIds] => []
        )

)

[2025-05-17 23:24:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494860
            [orderIds] => []
        )

)

[2025-05-17 23:24:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494871
            [orderIds] => []
        )

)

[2025-05-17 23:24:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494882
            [orderIds] => []
        )

)

[2025-05-17 23:24:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494893
            [orderIds] => []
        )

)

[2025-05-17 23:25:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494904
            [orderIds] => []
        )

)

[2025-05-17 23:25:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494915
            [orderIds] => []
        )

)

[2025-05-17 23:25:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494926
            [orderIds] => []
        )

)

[2025-05-17 23:25:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494937
            [orderIds] => []
        )

)

[2025-05-17 23:25:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494948
            [orderIds] => []
        )

)

[2025-05-17 23:25:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494959
            [orderIds] => []
        )

)

[2025-05-17 23:26:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494970
            [orderIds] => []
        )

)

[2025-05-17 23:26:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494981
            [orderIds] => []
        )

)

[2025-05-17 23:26:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747494992
            [orderIds] => []
        )

)

[2025-05-17 23:26:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 23:26:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 23:26:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 23:26:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 23:26:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495003
            [orderIds] => []
        )

)

[2025-05-17 23:26:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495014
            [orderIds] => []
        )

)

[2025-05-17 23:27:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495025
            [orderIds] => []
        )

)

[2025-05-17 23:27:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495036
            [orderIds] => []
        )

)

[2025-05-17 23:27:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495047
            [orderIds] => []
        )

)

[2025-05-17 23:27:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495058
            [orderIds] => []
        )

)

[2025-05-17 23:27:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495069
            [orderIds] => []
        )

)

[2025-05-17 23:28:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495080
            [orderIds] => []
        )

)

[2025-05-17 23:28:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495091
            [orderIds] => []
        )

)

[2025-05-17 23:28:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495102
            [orderIds] => []
        )

)

[2025-05-17 23:28:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495113
            [orderIds] => []
        )

)

[2025-05-17 23:28:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495124
            [orderIds] => []
        )

)

[2025-05-17 23:28:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495135
            [orderIds] => []
        )

)

[2025-05-17 23:29:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495146
            [orderIds] => []
        )

)

[2025-05-17 23:29:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495157
            [orderIds] => []
        )

)

[2025-05-17 23:29:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495168
            [orderIds] => []
        )

)

[2025-05-17 23:29:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495179
            [orderIds] => []
        )

)

[2025-05-17 23:29:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495190
            [orderIds] => []
        )

)

[2025-05-17 23:30:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495201
            [orderIds] => []
        )

)

[2025-05-17 23:30:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495212
            [orderIds] => []
        )

)

[2025-05-17 23:30:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495223
            [orderIds] => []
        )

)

[2025-05-17 23:30:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495234
            [orderIds] => []
        )

)

[2025-05-17 23:30:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495245
            [orderIds] => []
        )

)

[2025-05-17 23:30:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495256
            [orderIds] => []
        )

)

[2025-05-17 23:31:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495267
            [orderIds] => []
        )

)

[2025-05-17 23:31:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495278
            [orderIds] => []
        )

)

[2025-05-17 23:31:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495289
            [orderIds] => []
        )

)

[2025-05-17 23:31:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495300
            [orderIds] => []
        )

)

[2025-05-17 23:31:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495311
            [orderIds] => []
        )

)

[2025-05-17 23:32:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495322
            [orderIds] => []
        )

)

[2025-05-17 23:32:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495333
            [orderIds] => []
        )

)

[2025-05-17 23:32:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495344
            [orderIds] => []
        )

)

[2025-05-17 23:32:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495355
            [orderIds] => []
        )

)

[2025-05-17 23:32:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495366
            [orderIds] => []
        )

)

[2025-05-17 23:32:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495377
            [orderIds] => []
        )

)

[2025-05-17 23:33:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495388
            [orderIds] => []
        )

)

[2025-05-17 23:33:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495399
            [orderIds] => []
        )

)

[2025-05-17 23:33:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495410
            [orderIds] => []
        )

)

[2025-05-17 23:33:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495421
            [orderIds] => []
        )

)

[2025-05-17 23:33:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495432
            [orderIds] => []
        )

)

[2025-05-17 23:34:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495443
            [orderIds] => []
        )

)

[2025-05-17 23:34:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495454
            [orderIds] => []
        )

)

[2025-05-17 23:34:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495465
            [orderIds] => []
        )

)

[2025-05-17 23:34:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495476
            [orderIds] => []
        )

)

[2025-05-17 23:34:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495487
            [orderIds] => []
        )

)

[2025-05-17 23:34:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495498
            [orderIds] => []
        )

)

[2025-05-17 23:35:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495509
            [orderIds] => []
        )

)

[2025-05-17 23:35:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495520
            [orderIds] => []
        )

)

[2025-05-17 23:35:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495531
            [orderIds] => []
        )

)

[2025-05-17 23:35:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495542
            [orderIds] => []
        )

)

[2025-05-17 23:35:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495553
            [orderIds] => []
        )

)

[2025-05-17 23:36:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495564
            [orderIds] => []
        )

)

[2025-05-17 23:36:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495575
            [orderIds] => []
        )

)

[2025-05-17 23:36:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495586
            [orderIds] => []
        )

)

[2025-05-17 23:36:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495597
            [orderIds] => []
        )

)

[2025-05-17 23:36:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 23:36:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 23:36:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 23:36:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 23:36:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495608
            [orderIds] => []
        )

)

[2025-05-17 23:36:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495619
            [orderIds] => []
        )

)

[2025-05-17 23:37:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495630
            [orderIds] => []
        )

)

[2025-05-17 23:37:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495641
            [orderIds] => []
        )

)

[2025-05-17 23:37:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495652
            [orderIds] => []
        )

)

[2025-05-17 23:37:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495663
            [orderIds] => []
        )

)

[2025-05-17 23:37:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495674
            [orderIds] => []
        )

)

[2025-05-17 23:38:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495685
            [orderIds] => []
        )

)

[2025-05-17 23:38:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495696
            [orderIds] => []
        )

)

[2025-05-17 23:38:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747409900
            [orderIds] => []
        )

)

[2025-05-17 23:38:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1746891500
            [orderIds] => []
        )

)

[2025-05-17 23:38:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1746891500
            [orderIds] => []
        )

)

[2025-05-17 23:38:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-17 23:38:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-17 23:38:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-17 23:38:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-17 23:38:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-17 23:38:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495707
            [orderIds] => []
        )

)

[2025-05-17 23:38:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495718
            [orderIds] => []
        )

)

[2025-05-17 23:38:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495729
            [orderIds] => []
        )

)

[2025-05-17 23:39:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495740
            [orderIds] => []
        )

)

[2025-05-17 23:39:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495751
            [orderIds] => []
        )

)

[2025-05-17 23:39:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495762
            [orderIds] => []
        )

)

[2025-05-17 23:39:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495773
            [orderIds] => []
        )

)

[2025-05-17 23:39:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495784
            [orderIds] => []
        )

)

[2025-05-17 23:39:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495795
            [orderIds] => []
        )

)

[2025-05-17 23:40:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495806
            [orderIds] => []
        )

)

[2025-05-17 23:40:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495817
            [orderIds] => []
        )

)

[2025-05-17 23:40:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495828
            [orderIds] => []
        )

)

[2025-05-17 23:40:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495839
            [orderIds] => []
        )

)

[2025-05-17 23:40:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495850
            [orderIds] => []
        )

)

[2025-05-17 23:41:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495861
            [orderIds] => []
        )

)

[2025-05-17 23:41:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495872
            [orderIds] => []
        )

)

[2025-05-17 23:41:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495883
            [orderIds] => []
        )

)

[2025-05-17 23:41:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495894
            [orderIds] => []
        )

)

[2025-05-17 23:41:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495905
            [orderIds] => []
        )

)

[2025-05-17 23:41:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495916
            [orderIds] => []
        )

)

[2025-05-17 23:42:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495927
            [orderIds] => []
        )

)

[2025-05-17 23:42:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495938
            [orderIds] => []
        )

)

[2025-05-17 23:42:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495949
            [orderIds] => []
        )

)

[2025-05-17 23:42:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495960
            [orderIds] => []
        )

)

[2025-05-17 23:42:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495971
            [orderIds] => []
        )

)

[2025-05-17 23:43:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495982
            [orderIds] => []
        )

)

[2025-05-17 23:43:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747495993
            [orderIds] => []
        )

)

[2025-05-17 23:43:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496004
            [orderIds] => []
        )

)

[2025-05-17 23:43:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496015
            [orderIds] => []
        )

)

[2025-05-17 23:43:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496026
            [orderIds] => []
        )

)

[2025-05-17 23:43:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496037
            [orderIds] => []
        )

)

[2025-05-17 23:44:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496048
            [orderIds] => []
        )

)

[2025-05-17 23:44:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496059
            [orderIds] => []
        )

)

[2025-05-17 23:44:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496070
            [orderIds] => []
        )

)

[2025-05-17 23:44:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496081
            [orderIds] => []
        )

)

[2025-05-17 23:44:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496092
            [orderIds] => []
        )

)

[2025-05-17 23:45:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496103
            [orderIds] => []
        )

)

[2025-05-17 23:45:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496114
            [orderIds] => []
        )

)

[2025-05-17 23:45:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496125
            [orderIds] => []
        )

)

[2025-05-17 23:45:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496136
            [orderIds] => []
        )

)

[2025-05-17 23:45:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496147
            [orderIds] => []
        )

)

[2025-05-17 23:45:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496158
            [orderIds] => []
        )

)

[2025-05-17 23:46:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496169
            [orderIds] => []
        )

)

[2025-05-17 23:46:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496180
            [orderIds] => []
        )

)

[2025-05-17 23:46:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496191
            [orderIds] => []
        )

)

[2025-05-17 23:46:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496202
            [orderIds] => []
        )

)

[2025-05-17 23:46:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 23:46:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 23:46:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 23:46:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 23:46:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496213
            [orderIds] => []
        )

)

[2025-05-17 23:47:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496224
            [orderIds] => []
        )

)

[2025-05-17 23:47:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496235
            [orderIds] => []
        )

)

[2025-05-17 23:47:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496246
            [orderIds] => []
        )

)

[2025-05-17 23:47:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496257
            [orderIds] => []
        )

)

[2025-05-17 23:47:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496268
            [orderIds] => []
        )

)

[2025-05-17 23:47:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496279
            [orderIds] => []
        )

)

[2025-05-17 23:48:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496290
            [orderIds] => []
        )

)

[2025-05-17 23:48:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496301
            [orderIds] => []
        )

)

[2025-05-17 23:48:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496312
            [orderIds] => []
        )

)

[2025-05-17 23:48:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496323
            [orderIds] => []
        )

)

[2025-05-17 23:48:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496334
            [orderIds] => []
        )

)

[2025-05-17 23:49:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496345
            [orderIds] => []
        )

)

[2025-05-17 23:49:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496356
            [orderIds] => []
        )

)

[2025-05-17 23:49:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496367
            [orderIds] => []
        )

)

[2025-05-17 23:49:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496378
            [orderIds] => []
        )

)

[2025-05-17 23:49:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496389
            [orderIds] => []
        )

)

[2025-05-17 23:50:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496400
            [orderIds] => []
        )

)

[2025-05-17 23:50:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496411
            [orderIds] => []
        )

)

[2025-05-17 23:50:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496422
            [orderIds] => []
        )

)

[2025-05-17 23:50:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496433
            [orderIds] => []
        )

)

[2025-05-17 23:50:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496444
            [orderIds] => []
        )

)

[2025-05-17 23:50:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496455
            [orderIds] => []
        )

)

[2025-05-17 23:51:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496466
            [orderIds] => []
        )

)

[2025-05-17 23:51:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496477
            [orderIds] => []
        )

)

[2025-05-17 23:51:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496488
            [orderIds] => []
        )

)

[2025-05-17 23:51:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496499
            [orderIds] => []
        )

)

[2025-05-17 23:51:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496510
            [orderIds] => []
        )

)

[2025-05-17 23:52:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496521
            [orderIds] => []
        )

)

[2025-05-17 23:52:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496532
            [orderIds] => []
        )

)

[2025-05-17 23:52:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496543
            [orderIds] => []
        )

)

[2025-05-17 23:52:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496554
            [orderIds] => []
        )

)

[2025-05-17 23:52:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496565
            [orderIds] => []
        )

)

[2025-05-17 23:52:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496576
            [orderIds] => []
        )

)

[2025-05-17 23:53:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496587
            [orderIds] => []
        )

)

[2025-05-17 23:53:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496598
            [orderIds] => []
        )

)

[2025-05-17 23:53:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496609
            [orderIds] => []
        )

)

[2025-05-17 23:53:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496620
            [orderIds] => []
        )

)

[2025-05-17 23:53:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496631
            [orderIds] => []
        )

)

[2025-05-17 23:54:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496642
            [orderIds] => []
        )

)

[2025-05-17 23:54:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496653
            [orderIds] => []
        )

)

[2025-05-17 23:54:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496664
            [orderIds] => []
        )

)

[2025-05-17 23:54:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496675
            [orderIds] => []
        )

)

[2025-05-17 23:54:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496686
            [orderIds] => []
        )

)

[2025-05-17 23:54:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496697
            [orderIds] => []
        )

)

[2025-05-17 23:55:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496708
            [orderIds] => []
        )

)

[2025-05-17 23:55:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496719
            [orderIds] => []
        )

)

[2025-05-17 23:55:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496730
            [orderIds] => []
        )

)

[2025-05-17 23:55:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496741
            [orderIds] => []
        )

)

[2025-05-17 23:55:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496752
            [orderIds] => []
        )

)

[2025-05-17 23:56:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496763
            [orderIds] => []
        )

)

[2025-05-17 23:56:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496774
            [orderIds] => []
        )

)

[2025-05-17 23:56:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496785
            [orderIds] => []
        )

)

[2025-05-17 23:56:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496796
            [orderIds] => []
        )

)

[2025-05-17 23:56:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-17 23:56:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 23:56:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-17 23:56:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-17 23:56:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496807
            [orderIds] => []
        )

)

[2025-05-17 23:56:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496818
            [orderIds] => []
        )

)

[2025-05-17 23:57:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496829
            [orderIds] => []
        )

)

[2025-05-17 23:57:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496840
            [orderIds] => []
        )

)

[2025-05-17 23:57:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496851
            [orderIds] => []
        )

)

[2025-05-17 23:57:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496862
            [orderIds] => []
        )

)

[2025-05-17 23:57:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496873
            [orderIds] => []
        )

)

[2025-05-17 23:58:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496884
            [orderIds] => []
        )

)

[2025-05-17 23:58:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496895
            [orderIds] => []
        )

)

[2025-05-17 23:58:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496906
            [orderIds] => []
        )

)

[2025-05-17 23:58:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496917
            [orderIds] => []
        )

)

[2025-05-17 23:58:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496928
            [orderIds] => []
        )

)

[2025-05-17 23:58:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496939
            [orderIds] => []
        )

)

[2025-05-17 23:59:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496950
            [orderIds] => []
        )

)

[2025-05-17 23:59:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496961
            [orderIds] => []
        )

)

[2025-05-17 23:59:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496972
            [orderIds] => []
        )

)

[2025-05-17 23:59:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496983
            [orderIds] => []
        )

)

[2025-05-17 23:59:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747496994
            [orderIds] => []
        )

)

