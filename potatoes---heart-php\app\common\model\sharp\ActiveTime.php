<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\model\sharp;

use cores\BaseModel;
use think\model\relation\BelongsTo;
use think\model\relation\HasMany;

/**
 * 整点秒杀-活动会场场次模型
 * Class ActiveTime
 * @package app\common\model\sharp
 */
class ActiveTime extends BaseModel
{
    // 定义表名
    protected $name = 'sharp_active_time';

    // 定义主键
    protected $pk = 'active_time_id';

    /**
     * 关联活动会场表
     * @return BelongsTo
     */
    public function active(): BelongsTo
    {
        return $this->belongsTo('Active', 'active_id');
    }

    /**
     * 当前场次下秒杀商品的数量
     * @return HasMany
     */
    public function goods(): HasMany
    {
        return $this->hasMany('ActiveGoods', 'active_time_id');
    }

    /**
     * 获取器：活动场次时间
     * @param $value
     * @return string
     */
    public function getActiveTimeAttr($value): string
    {
        return pad_left((string)$value) . ':00';
    }

    /**
     * 活动会场场次详情
     * @param int $activeTimeId
     * @param array $with
     * @return static|array|null
     */
    public static function detail(int $activeTimeId, array $with = [])
    {
        return static::get($activeTimeId, $with);
    }
}