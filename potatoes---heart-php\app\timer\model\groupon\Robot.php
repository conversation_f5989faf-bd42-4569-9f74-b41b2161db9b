<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\timer\model\groupon;

use app\common\model\groupon\Robot as RobotModel;
use app\common\library\helper;

/**
 * 拼团机器人模型
 * Class Robot
 * @package app\timer\model\groupon
 */
class Robot extends RobotModel
{
    /**
     * 获取指定数量的随机机器人
     * @param int $num
     * @param int $storeId
     * @return array
     */
    public static function getRandomRobot(int $num, int $storeId): array
    {
        $allRobotIds = static::getRobotIds($storeId);
        if (empty($allRobotIds) || count($allRobotIds) < $num) {
            return [];
        }
        return helper::getArrayRand($allRobotIds, $num);
    }

    /**
     * 获取全部机器人ID集
     * @param int $storeId
     * @return array
     */
    private static function getRobotIds(int $storeId): array
    {
        return (new static)->cache('robotIds', 60 * 60, 'cache')
            ->where('store_id', '=', $storeId)
            ->where('is_delete', '=', 0)
            ->limit(500)
            ->column('robot_id');
    }
}