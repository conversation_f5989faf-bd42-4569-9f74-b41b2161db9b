import{E as e,a7 as a,a1 as n}from"./index-b4f0c008.js";const r=new e([{key:"WECHAT",name:"微信支付",value:"wechat"},{key:"ALIPAY",name:"支付宝支付",value:"alipay"},{key:"BALANCE",name:"余额支付",value:"balance"}]),t=new e([{key:"APP",name:"APP端",value:"APP"},{key:"H5",name:"H5端",value:"H5"},{key:"WXOFFICIAL",name:"微信公众号端",value:"WXOFFICIAL"},{key:"MP_WEIXIN",name:"微信小程序端",value:"MP-WEIXIN"},{key:"MP_ALIPAY",name:"支付宝小程序端",value:"MP-ALIPAY"}]),o=e=>{const a={formHtml:"",...e};return n.set("tempUnifyData_"+a.orderKey,{method:r.ALIPAY.value,outTradeNo:a.out_trade_no},3600),new Promise(((e,n)=>{if(a.formHtml){const e=document.createElement("div");e.innerHTML=a.formHtml,document.body.appendChild(e),document.forms[0].submit()}}))},i=e=>new Promise(((a,n)=>{uni.requestPayment({provider:"alipay",orderInfo:e.orderInfo,success(n){const r={isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"alipay"};a({res:n,option:r})},fail:e=>n(e)})})),s=e=>new Promise(((a,n)=>{uni.requestPayment({provider:"alipay",orderInfo:e.orderInfo,success(n){const r={isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"alipay"};a({res:n,option:r})},fail:e=>n(e)})})),u=e=>({[t.H5.value]:o,[t.APP.value]:i,[t.MP_ALIPAY.value]:s}[a](e)),p=()=>{const e={};return e.returnUrl=window.location.href,e},m=e=>{const a={timeStamp:"",nonceStr:"",package:"",signType:"",paySign:"",...e};return new Promise(((e,n)=>{uni.requestPayment({provider:"wxpay",timeStamp:a.timeStamp,nonceStr:a.nonceStr,package:a.package,signType:a.signType,paySign:a.paySign,success(n){const r={isRequireQuery:!0,outTradeNo:a.out_trade_no,method:"wechat"};e({res:n,option:r})},fail:e=>n(e)})}))},d=e=>{const a={orderKey:null,mweb_url:"",h5_url:"",...e};return n.set("tempUnifyData_"+a.orderKey,{method:r.WECHAT.value,outTradeNo:a.out_trade_no},3600),new Promise(((e,n)=>{const r=a.mweb_url||a.h5_url;r&&(window.location.href=r)}))},c=e=>{const a={appId:"",timeStamp:"",nonceStr:"",package:"",signType:"",paySign:"",...e};return l(a)},y=e=>new Promise(((a,n)=>{uni.requestPayment({provider:"wxpay",orderInfo:{partnerid:e.partnerid,appid:e.appid,package:"Sign=WXPay",noncestr:e.noncestr,sign:e.sign,prepayid:e.prepayid,timestamp:e.timestamp},success(n){a({res:n,option:{isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"wechat"}})},fail:e=>n(e)})})),l=e=>new Promise(((a,n)=>{WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:e.appId,timeStamp:e.timeStamp,nonceStr:e.nonceStr,package:e.package,signType:e.signType,paySign:e.paySign},(r=>{if("get_brand_wcpay_request:ok"==r.err_msg){const n={isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"wechat"};a({res:r,option:n})}else n(r)}))})),P=e=>({[t.H5.value]:d,[t.MP_WEIXIN.value]:m,[t.WXOFFICIAL.value]:c,[t.APP.value]:y}[a](e)),_=()=>({});export{r as P,_ as a,P as b,p as e,u as p};
