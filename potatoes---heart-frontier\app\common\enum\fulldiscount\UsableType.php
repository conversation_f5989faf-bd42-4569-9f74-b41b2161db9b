<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\enum\fulldiscount;

use app\common\enum\EnumBasics;

/**
 * 枚举类：满额立减活动 - 适用商品类型
 * Class UsableType
 * @package app\common\enum\fulldiscount
 */
class UsableType extends EnumBasics
{
    // 全部商品
    const ALL = 10;

    // 指定商品
    const APPOINT = 20;

    // 排除商品
    const EXCLUDE = 30;

    /**
     * 获取枚举类型值
     * @return array
     */
    public static function data(): array
    {
        return [
            self::ALL => [
                'name' => '全部商品',
                'value' => self::ALL,
            ],
            self::APPOINT => [
                'name' => '指定商品',
                'value' => self::APPOINT,
            ],
            self::EXCLUDE => [
                'name' => '排除商品',
                'value' => self::EXCLUDE,
            ]
        ];
    }
}
