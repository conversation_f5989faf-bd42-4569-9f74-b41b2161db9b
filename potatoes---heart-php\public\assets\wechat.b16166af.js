import{E as e,p as r,N as a}from"./index-86a01735.js";import{P as t}from"./Method.b1c118f9.js";const o=new e([{key:"APP",name:"APP端",value:"APP"},{key:"H5",name:"H5端",value:"H5"},{key:"WXOFFICIAL",name:"微信公众号端",value:"WXOFFICIAL"},{key:"MP_WEIXIN",name:"微信小程序端",value:"MP-WEIXIN"},{key:"MP_ALIPAY",name:"支付宝小程序端",value:"MP-ALIPAY"}]),n=e=>{const r={formHtml:"",...e};return a.set("tempUnifyData_"+r.orderKey,{method:t.ALIPAY.value,outTradeNo:r.out_trade_no},3600),new Promise(((e,a)=>{if(r.formHtml){const e=document.createElement("div");e.innerHTML=r.formHtml,document.body.appendChild(e),document.forms[0].submit()}}))},i=e=>new Promise(((r,a)=>{uni.requestPayment({provider:"alipay",orderInfo:e.orderInfo,success(a){const t={isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"alipay"};r({res:a,option:t})},fail:e=>a(e)})})),s=e=>new Promise(((r,a)=>{uni.requestPayment({provider:"alipay",orderInfo:e.orderInfo,success(a){const t={isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"alipay"};r({res:a,option:t})},fail:e=>a(e)})})),p=e=>({[o.H5.value]:n,[o.APP.value]:i,[o.MP_ALIPAY.value]:s}[r](e)),u=()=>{const e={};return e.returnUrl=window.location.href,e},m=e=>{const r={timeStamp:"",nonceStr:"",package:"",signType:"",paySign:"",...e};return new Promise(((e,a)=>{uni.requestPayment({provider:"wxpay",timeStamp:r.timeStamp,nonceStr:r.nonceStr,package:r.package,signType:r.signType,paySign:r.paySign,success(a){const t={isRequireQuery:!0,outTradeNo:r.out_trade_no,method:"wechat"};e({res:a,option:t})},fail:e=>a(e)})}))},d=e=>{const r={orderKey:null,mweb_url:"",h5_url:"",...e};return a.set("tempUnifyData_"+r.orderKey,{method:t.WECHAT.value,outTradeNo:r.out_trade_no},3600),new Promise(((e,a)=>{const t=r.mweb_url||r.h5_url;t&&(window.location.href=t)}))},c=e=>{const r={appId:"",timeStamp:"",nonceStr:"",package:"",signType:"",paySign:"",...e};return l(r)},y=e=>new Promise(((r,a)=>{uni.requestPayment({provider:"wxpay",orderInfo:{partnerid:e.partnerid,appid:e.appid,package:"Sign=WXPay",noncestr:e.noncestr,sign:e.sign,prepayid:e.prepayid,timestamp:e.timestamp},success(a){r({res:a,option:{isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"wechat"}})},fail:e=>a(e)})})),l=e=>new Promise(((r,a)=>{WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:e.appId,timeStamp:e.timeStamp,nonceStr:e.nonceStr,package:e.package,signType:e.signType,paySign:e.paySign},(t=>{if("get_brand_wcpay_request:ok"==t.err_msg){const a={isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"wechat"};r({res:t,option:a})}else a(t)}))})),P=e=>({[o.H5.value]:d,[o.MP_WEIXIN.value]:m,[o.WXOFFICIAL.value]:c,[o.APP.value]:y}[r](e)),_=()=>({});export{_ as a,P as b,u as e,p};
