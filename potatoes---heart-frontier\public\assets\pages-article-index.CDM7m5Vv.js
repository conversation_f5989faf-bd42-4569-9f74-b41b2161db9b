import{_ as t}from"./u-tabs.CGZQcvwv.js";import{$ as a,q as e,u as s,v as i,c as l,w as o,n as r,i as c,o as n,a as d,d as m,e as u,F as h,k as p,f as _,t as f,b as g,l as y,g as b}from"./index-Bo6hY7ZC.js";import{r as L}from"./uni-app.es.DtaL5fPi.js";import{M as w,_ as x}from"./mescroll-mixins.BtR42tWA.js";import{W as T}from"./wxofficial.CKoGItRK.js";import{l as v}from"./index.DxVSrP_D.js";import{_ as I}from"./_plugin-vue_export-helper.BCo6x5W8.js";const C="article.category/list";const S=I({mixins:[w,T],data:()=>({tabList:[],curTab:0,categoryId:0,articleList:e(),upOption:{auto:!0,page:{size:15},noMoreSize:3}}),onLoad(t){const a=this;a.categoryId=t.categoryId||0,a.getCategoryList(),a.setWxofficialShareData()},methods:{upCallback(t){const a=this;a.getArticleList(t.num).then((t=>{const e=t.data.length,s=t.data.total;a.mescroll.endBySize(e,s)})).catch((()=>a.mescroll.endErr()))},getCategoryList(){a.get(C).then((t=>{this.setTabList(t.data.list)}))},setTabList(t){const a=this;if(a.tabList=[{value:0,name:"全部"}],t.forEach((t=>{a.tabList.push({value:t.category_id,name:t.name})})),a.categoryId>0){const t=a.tabList.findIndex((t=>t.value==a.categoryId));a.curTab=t>-1?t:0}},getArticleList(t=1){const a=this;return new Promise(((e,i)=>{v({categoryId:a.categoryId,page:t},{load:!1}).then((i=>{const l=i.data.list;a.articleList.data=s(l,a.articleList,t),e(l)})).catch(i)}))},onChangeTab(t){this.curTab=t,this.categoryId=this.tabList[t].value,this.onRefreshList()},onRefreshList(){this.articleList=e(),setTimeout((()=>this.mescroll.resetUpScroll()),120)},onTargetDetail(t){this.$navTo("pages/article/detail",{articleId:t})},setWxofficialShareData(){this.updateShareCardData({title:"文章首页"})}},onShareAppMessage(){return{title:"文章首页",path:"/pages/article/index?"+this.$getShareUrlParams()}},onShareTimeline(){return{title:"文章首页",path:"/pages/article/index?"+this.$getShareUrlParams()}}},[["render",function(a,e,s,w,T,v){const I=L(i("u-tabs"),t),C=y,S=c,k=b,j=L(i("mescroll-body"),x);return n(),l(S,{class:"container",style:r(a.appThemeStyle)},{default:o((()=>[d(j,{ref:"mescrollRef",sticky:!0,onInit:a.mescrollInit,down:{use:!1},up:T.upOption,onUp:v.upCallback},{default:o((()=>[d(I,{list:T.tabList,"is-scroll":!0,modelValue:T.curTab,"onUpdate:modelValue":e[0]||(e[0]=t=>T.curTab=t),"active-color":a.appTheme.mainBg,duration:.2,onChange:v.onChangeTab},null,8,["list","modelValue","active-color","onChange"]),d(S,{class:"article-list"},{default:o((()=>[(n(!0),m(h,null,u(T.articleList.data,((t,a)=>(n(),l(S,{class:p(["article-item",[`show-type__${t.show_type}`]]),key:a,onClick:a=>v.onTargetDetail(t.article_id)},{default:o((()=>[10==t.show_type?(n(),m(h,{key:0},[d(S,{class:"article-item__left flex-box"},{default:o((()=>[d(S,{class:"article-item__title"},{default:o((()=>[d(C,{class:"twoline-hide"},{default:o((()=>[_(f(t.title),1)])),_:2},1024)])),_:2},1024),d(S,{class:"article-item__footer m-top10"},{default:o((()=>[d(C,{class:"article-views f-24 col-8"},{default:o((()=>[_(f(t.show_views)+"次浏览",1)])),_:2},1024)])),_:2},1024)])),_:2},1024),d(S,{class:"article-item__image"},{default:o((()=>[d(k,{class:"image",mode:"widthFix",src:t.image_url},null,8,["src"])])),_:2},1024)],64)):g("",!0),20==t.show_type?(n(),m(h,{key:1},[d(S,{class:"article-item__title"},{default:o((()=>[d(C,{class:"twoline-hide"},{default:o((()=>[_(f(t.title),1)])),_:2},1024)])),_:2},1024),d(S,{class:"article-item__image m-top20"},{default:o((()=>[d(k,{class:"image",mode:"widthFix",src:t.image_url},null,8,["src"])])),_:2},1024),d(S,{class:"article-item__footer m-top10"},{default:o((()=>[d(C,{class:"article-views f-24 col-8"},{default:o((()=>[_(f(t.show_views)+"次浏览",1)])),_:2},1024)])),_:2},1024)],64)):g("",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1},8,["onInit","up","onUp"])])),_:1},8,["style"])}],["__scopeId","data-v-13555183"]]);export{S as default};
