import{a as e,_ as a,b as o}from"./u-form.CqfAaEx7.js";import{a2 as t,v as l,c as r,w as s,n,i,o as d,a as m,f as u,b as p,k as f}from"./index-Dk2OK-Q2.js";import{r as c}from"./uni-app.es.Bfy34Oxr.js";import{_ as g}from"./select-region.D_92AsGQ.js";import{a as h}from"./verify.Ctz7vufY.js";import{a as b,b as _}from"./address.y5H5GI9v.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./u-icon.NnBtpP9Z.js";import"./emitter.DrjJCwnj.js";import"./u-loading.eYMslelP.js";import"./u-popup.CozEWTmO.js";import"./u-mask.C7x5h6JE.js";const V={name:[{required:!0,message:"请输入姓名",trigger:["blur","change"]}],phone:[{required:!0,message:"请输入手机号",trigger:["blur","change"]},{validator:(e,a,o)=>h(a),message:"手机号码不正确",trigger:["blur"]}],region:[{required:!0,message:"请选择省市区",trigger:["blur","change"],type:"array"}],detail:[{required:!0,message:"请输入详细地址",trigger:["blur","change"]}]};const v=y({components:{SelectRegion:g},data:()=>({form:{content:"",name:"",phone:"",region:[],detail:""},rules:V,disabled:!1}),onLoad(e){},onReady(){this.$refs.uForm.setRules(this.rules)},methods:{handleAnalysis(){const e=this;b(e.form.content).then((a=>{const o=a.data.detail;e.createFormData(o)}))},openAddress(){const{form:e,$refs:a}=this;t.openAddress().then((o=>{alert(JSON.stringify(o));const t=a.sRegion.getOptionItemByNames(o);e.name=o.userName,e.phone=o.telNumber,e.detail=o.detailInfo,e.region=t.length>0?t:[]}))},createFormData(e){const{form:a}=this;a.name=e.name,a.phone=e.phone,a.detail=e.detail,a.region=this.createRegion(e)},createRegion(e){return 0==e.province_id||0==e.city_id||0==e.region_id?(this.$toast("很抱歉，地区未能识别请手动选择",2e3),[]):[{label:e.region.province,value:e.province_id},{label:e.region.city,value:e.city_id},{label:e.region.region,value:e.region_id}]},handleSubmit(){const e=this;if(e.disabled)return!1;e.$refs.uForm.validate((a=>{a&&(e.disabled=!0,_(e.form).then((a=>{e.$toast(a.message),uni.navigateBack()})).finally((()=>e.disabled=!1)))}))}}},[["render",function(t,h,b,_,y,V){const v=c(l("u-input"),e),j=c(l("u-form-item"),a),x=i,k=c(l("select-region"),g),w=c(l("u-form"),o);return d(),r(x,{class:"container",style:n(t.appThemeStyle)},{default:s((()=>[m(x,{class:"form-analysis form-wrapper"},{default:s((()=>[m(j,{prop:"name","border-bottom":!1},{default:s((()=>[m(v,{modelValue:y.form.content,"onUpdate:modelValue":h[0]||(h[0]=e=>y.form.content=e),type:"textarea",placeholder:"粘贴地址信息，自动解析姓名、电话和地址","custom-style":{height:"150rpx"},"auto-height":!1},null,8,["modelValue"])])),_:1}),m(x,{class:"analysis-foot clearfix"},{default:s((()=>[m(x,{class:"analysis-btn",onClick:h[1]||(h[1]=e=>V.handleAnalysis())},{default:s((()=>[u("智能识别")])),_:1})])),_:1})])),_:1}),m(x,{class:"page-title"},{default:s((()=>[u("收货地址")])),_:1}),m(x,{class:"form-wrapper"},{default:s((()=>[m(w,{model:y.form,ref:"uForm","label-width":"140rpx"},{default:s((()=>[m(j,{label:"姓名",prop:"name"},{default:s((()=>[m(v,{modelValue:y.form.name,"onUpdate:modelValue":h[2]||(h[2]=e=>y.form.name=e),placeholder:"请输入收货人姓名"},null,8,["modelValue"])])),_:1}),m(j,{label:"电话",prop:"phone"},{default:s((()=>[m(v,{modelValue:y.form.phone,"onUpdate:modelValue":h[3]||(h[3]=e=>y.form.phone=e),placeholder:"请输入收货人手机号"},null,8,["modelValue"])])),_:1}),m(j,{label:"地区",prop:"region"},{default:s((()=>[m(k,{ref:"sRegion",modelValue:y.form.region,"onUpdate:modelValue":h[4]||(h[4]=e=>y.form.region=e)},null,8,["modelValue"])])),_:1}),m(j,{label:"详细地址",prop:"detail","border-bottom":!1},{default:s((()=>[m(v,{modelValue:y.form.detail,"onUpdate:modelValue":h[5]||(h[5]=e=>y.form.detail=e),placeholder:"街道门牌、楼层等信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1}),m(x,{class:"footer"},{default:s((()=>[m(x,{class:"btn-wrapper"},{default:s((()=>["WXOFFICIAL"===t.platform?(d(),r(x,{key:0,class:"btn-item btn-item-wechat",onClick:h[6]||(h[6]=e=>V.openAddress())},{default:s((()=>[u("选择微信收货地址")])),_:1})):p("",!0),m(x,{class:f(["btn-item btn-item-main",{disabled:y.disabled}]),onClick:h[7]||(h[7]=e=>V.handleSubmit())},{default:s((()=>[u("保存")])),_:1},8,["class"])])),_:1})])),_:1})])),_:1},8,["style"])}],["__scopeId","data-v-6fa04682"]]);export{v as default};
