<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\controller;

use think\response\Json;
use EasyWeChat\Kernel\Exceptions\RuntimeException;
use EasyWeChat\Kernel\Exceptions\InvalidConfigException;
use EasyWeChat\Kernel\Exceptions\InvalidArgumentException;
use think\db\exception\DbException;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use app\api\service\Wxofficial as WxofficialService;
use cores\exception\BaseException;

/**
 * 微信公众号
 * Class Wxofficial
 * @package app\api\controller
 */
class Wxofficial extends Controller
{
    /**
     * 获取授权登录地址
     * @param string $callbackUrl 回调的h5页面
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function oauthUrl(string $callbackUrl): Json
    {
        $service = new WxofficialService;
        $redirectUrl = $service->getOauthUrl($callbackUrl);
        return $this->renderSuccess(compact('redirectUrl'));
    }

    /**
     * 获取微信用户信息
     * @param string $code
     * @return Json
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function oauthUserInfo(string $code): Json
    {
        $service = new WxofficialService;
        $result = $service->getOauthUserInfo();
        return $this->renderSuccess($result);
    }

    /**
     * 获取微信公众号jssdk配置参数
     * @param string $url 用户端完整的url
     * @return Json
     * @throws InvalidArgumentException
     * @throws InvalidConfigException
     * @throws RuntimeException
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function jssdkConfig(string $url): Json
    {
        $service = new WxofficialService;
        $config = $service->getJssdkConfig($url);
        return $this->renderSuccess(compact('config'));
    }
}