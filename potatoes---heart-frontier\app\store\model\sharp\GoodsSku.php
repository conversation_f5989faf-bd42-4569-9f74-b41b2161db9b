<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\model\sharp;

use think\model\Collection;
use app\common\library\helper;
use app\store\model\GoodsSku as GoodsSkuModel;
use app\common\model\sharp\GoodsSku as SharpGoodsSkuModel;
use app\common\enum\goods\SpecType as SpecTypeEnum;

/**
 * 整点秒杀-秒杀商品sku模型
 * Class Goods
 * @package app\store\model\sharp
 */
class GoodsSku extends SharpGoodsSkuModel
{
    /**
     * 获取秒杀商品SKU列表(包含主商品sku的一些数据)
     * @param Collection $sharpSkuList 秒杀商品SKU列表
     * @param int $goodsId 主商品ID
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getCommonSkuList(Collection $sharpSkuList, int $goodsId): array
    {
        // 获取主商品SKU列表
        $mainSkuData = static::getMainSkuList($goodsId);
        // 合并整理成新的数据 (商品价格、库存数量、商品sku编码)
        $data = [];
        foreach ($sharpSkuList as &$item) {
            $mainItem = $mainSkuData[$item['goods_sku_id']];
            $item['goods_sku_no'] = $mainItem['goods_sku_no'];
            $item['goods_price'] = $mainItem['goods_price'];
            $item['stock_num'] = $mainItem['stock_num'];
        }
        return $data;
    }

    /**
     * 获取主商品SKU列表
     * @param int $goodsId 主商品ID
     * @return array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private static function getMainSkuList(int $goodsId)
    {
        // 获取主商品SKU列表
        $mainSkuList = GoodsSkuModel::getSkuList($goodsId);
        if ($mainSkuList->isEmpty()) {
            return [];
        }
        // 将列表数据主键设置为goods_sku_id
        return helper::arrayColumn2Key($mainSkuList->toArray(), 'goods_sku_id');
    }

    /**
     * 获取库存总数量 (根据sku列表数据)
     * @param array $skuList
     * @return float|int
     */
    public static function getStockTotal(array $skuList)
    {
        return (int)helper::getArrayColumnSum($skuList, 'seckill_stock');
    }

    /**
     * 获取商品价格高低区间 (根据sku列表数据)
     * @param array $skuList
     * @return array
     */
    public static function getGoodsPrices(array $skuList): array
    {
        $goodsPriceArr = helper::getArrayColumn($skuList, 'seckill_price');
        return [min($goodsPriceArr), max($goodsPriceArr)];
    }

    /**
     * 生成skuList数据(用于编辑秒杀商品)
     * @param int $goodsId 主商品ID
     * @param array $skuList 用户提交的SKU数据
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getNewSkuList(int $goodsId, array $skuList): array
    {
        // 获取主商品SKU列表
        $mainSkuData = static::getMainSkuList($goodsId);
        foreach ($skuList as &$skuItem) {
            $mainItem = $mainSkuData[$skuItem['goods_sku_id']];
            $skuItem['spec_value_ids'] = $mainItem['spec_value_ids'];
            $skuItem['goods_props'] = $mainItem['goods_props'];
        }
        return $skuList;
    }

    /**
     * 生成默认skuList数据(用于新增秒杀商品)
     * @param int $goodsId 主商品ID
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getDefaultSkuList(int $goodsId)
    {
        // 获取主商品SKU列表
        $data = static::getMainSkuList($goodsId);
        foreach ($data as &$skuItem) {
            $skuItem['seckill_price'] = $skuItem['goods_price'];
            $skuItem['seckill_stock'] = $skuItem['stock_num'];
            $skuItem['create_time'] = time();
            $skuItem['update_time'] = time();
        }
        return $data;
    }

    /**
     * 新增商品sku记录
     * @param int $sharpGoodsId 秒杀商品ID
     * @param array $newSkuList
     * @param int $specType
     * @return array|bool|false
     */
    public static function add(int $sharpGoodsId, int $specType = SpecTypeEnum::SINGLE, array $newSkuList = [])
    {
        // 单规格模式
        if ($specType === SpecTypeEnum::SINGLE) {
            return (new static)->save(array_merge($newSkuList, [
                'sharp_goods_id' => $sharpGoodsId,
                'goods_sku_id' => 0,
                'store_id' => self::$storeId
            ]));
        } // 多规格模式
        elseif ($specType === SpecTypeEnum::MULTI) {
            // 批量写入商品sku记录
            return static::increasedFroMulti($sharpGoodsId, $newSkuList);
        }
        return false;
    }

    /**
     * 更新商品sku记录
     * @param int $sharpGoodsId 秒杀商品ID
     * @param int $specType
     * @param array $skuList
     * @return array|bool|false
     */
    public static function edit(int $sharpGoodsId, int $specType = SpecTypeEnum::SINGLE, array $skuList = [])
    {
        // 删除所有的sku记录
        static::deleteAll(['sharp_goods_id' => $sharpGoodsId]);
        // 新增商品sku记录
        return static::add($sharpGoodsId, $specType, $skuList);
    }

    /**
     * 批量写入商品sku记录
     * @param int $sharpGoodsId 秒杀商品ID
     * @param array $skuList
     * @return array|false
     */
    private static function increasedFroMulti(int $sharpGoodsId, array $skuList)
    {
        $dataset = [];
        foreach ($skuList as $skuItem) {
            $dataset[] = array_merge($skuItem, [
                'id' => null,   // 此处的id必须是数据库自增
                'goods_sku_id' => $skuItem['goods_sku_id'],
                'seckill_price' => $skuItem['seckill_price'] ?: 0.01,
                'seckill_stock' => $skuItem['seckill_stock'] ?: 0,
                'goods_props' => $skuItem['goods_props'],
                'spec_value_ids' => $skuItem['spec_value_ids'],
                'sharp_goods_id' => $sharpGoodsId,
                'store_id' => self::$storeId
            ]);
        }
        return (new static)->addAll($dataset);
    }
}
