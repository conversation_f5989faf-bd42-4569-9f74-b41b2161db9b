<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\sharp;

use app\common\model\sharp\ActiveTime as ActiveTimeModel;

/**
 * 整点秒杀-活动会场场次模型
 * Class ActiveTime
 * @package app\api\model\sharp
 */
class ActiveTime extends ActiveTimeModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'store_id',
        'create_time',
        'update_time',
    ];

    /**
     * 获取当前进行中的活动场次
     * @param $activeId
     * @return array|null|static
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getNowActiveTime($activeId)
    {
        // 当前的时间点
        $nowTime = date('H');
        return $this->where('active_id', '=', $activeId)
            ->where('active_time', '=', $nowTime)
            ->where('status', '=', 1)
            ->find();
    }

    /**
     * 获取下一场活动场次
     * @param $activeId
     * @return \think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getNextActiveTimes($activeId): \think\Collection
    {
        // 当前的时间点
        $nowTime = date('H');
        return $this->where('active_id', '=', $activeId)
            ->where('active_time', '>', $nowTime)
            ->where('status', '=', 1)
            ->order(['active_time' => 'asc'])
            ->select();
    }

    /**
     * 获取指定日期最近的活动场次
     * @param $activeId
     * @return array|null|static
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getRecentActiveTime($activeId)
    {
        return $this->where('active_id', '=', $activeId)
            ->where('status', '=', 1)
            ->order(['active_time' => 'asc'])
            ->find();
    }
}