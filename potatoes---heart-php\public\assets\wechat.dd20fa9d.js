import{E as e,a8 as a,T as n,a2 as t}from"./index-b996d08a.js";const r=new e([{key:"WECHAT",name:"微信支付",value:"wechat"},{key:"ALIPAY",name:"支付宝支付",value:"alipay"},{key:"BALANCE",name:"余额支付",value:"balance"}]),o=new e([{key:"APP",name:"APP端",value:"APP"},{key:"H5",name:"H5端",value:"H5"},{key:"WXOFFICIAL",name:"微信公众号端",value:"WXOFFICIAL"},{key:"MP_WEIXIN",name:"微信小程序端",value:"MP-WEIXIN"},{key:"MP_ALIPAY",name:"支付宝小程序端",value:"MP-ALIPAY"}]),i=e=>{const a={formHtml:"",...e};return new Promise(((e,n)=>{if(a.formHtml){const e=document.createElement("div");e.innerHTML=a.formHtml,document.body.appendChild(e),document.forms[0].submit()}}))},s=e=>new Promise(((a,n)=>{uni.requestPayment({provider:"alipay",orderInfo:e.orderInfo,success(n){const t={isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"alipay"};a({res:n,option:t})},fail:e=>n(e)})})),u=e=>new Promise(((a,n)=>{uni.requestPayment({provider:"alipay",orderInfo:e.orderInfo,success(n){const t={isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"alipay"};a({res:n,option:t})},fail:e=>n(e)})})),p=e=>({[o.H5.value]:i,[o.APP.value]:s,[o.MP_ALIPAY.value]:u}[a](e)),m=()=>{const e={};return e.returnUrl=window.location.href,e},d=()=>{const e=c();return e.method&&"alipay.trade.wap.pay.return"===e.method?{method:r.ALIPAY.value,outTradeNo:e.out_trade_no}:null},c=()=>{const e=n();return e[e.length-1].$route.query},y=e=>{const a={timeStamp:"",nonceStr:"",package:"",signType:"",paySign:"",...e};return new Promise(((e,n)=>{uni.requestPayment({provider:"wxpay",timeStamp:a.timeStamp,nonceStr:a.nonceStr,package:a.package,signType:a.signType,paySign:a.paySign,success(n){const t={isRequireQuery:!0,outTradeNo:a.out_trade_no,method:"wechat"};e({res:n,option:t})},fail:e=>n(e)})}))},l=e=>{const a={orderKey:null,mweb_url:"",h5_url:"",...e};return t.set("tempUnifyData_"+a.orderKey,{method:r.WECHAT.value,outTradeNo:a.out_trade_no},3600),new Promise(((e,n)=>{const t=a.mweb_url||a.h5_url;t&&(window.location.href=t)}))},P=e=>{const a={appId:"",timeStamp:"",nonceStr:"",package:"",signType:"",paySign:"",...e};return _(a)},g=e=>new Promise(((a,n)=>{uni.requestPayment({provider:"wxpay",orderInfo:{partnerid:e.partnerid,appid:e.appid,package:"Sign=WXPay",noncestr:e.noncestr,sign:e.sign,prepayid:e.prepayid,timestamp:e.timestamp},success(n){a({res:n,option:{isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"wechat"}})},fail:e=>n(e)})})),_=e=>new Promise(((a,n)=>{WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:e.appId,timeStamp:e.timeStamp,nonceStr:e.nonceStr,package:e.package,signType:e.signType,paySign:e.paySign},(t=>{if("get_brand_wcpay_request:ok"==t.err_msg){const n={isRequireQuery:!0,outTradeNo:e.out_trade_no,method:"wechat"};a({res:t,option:n})}else n(t)}))})),w=e=>({[o.H5.value]:l,[o.MP_WEIXIN.value]:y,[o.WXOFFICIAL.value]:P,[o.APP.value]:g}[a](e)),f=()=>({}),v=e=>{if(window.performance&&2==window.performance.navigation.type){const a=t.get("tempUnifyData_"+e);if(a)return t.remove("tempUnifyData_"+e),a}return null};export{r as P,v as a,f as b,p as c,w as d,m as e,d as p};
