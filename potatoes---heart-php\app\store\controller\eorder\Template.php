<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\eorder;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\eorder\Template as TemplateModel;

/**
 * 电子面单模板管理
 * Class Template
 * @package app\store\controller\eorder
 */
class Template extends Controller
{
    /**
     * 模板列表
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function list(): Json
    {
        $model = new TemplateModel;
        $list = $model->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 获取模板记录
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function all(): Json
    {
        $model = new TemplateModel;
        $list = $model->getAll();
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 详情记录
     * @param int $templateId
     * @return Json
     */
    public function detail(int $templateId): Json
    {
        $detail = TemplateModel::detail($templateId);
        return $this->renderSuccess(compact('detail'));
    }

    /**
     * 添加模板
     * @return Json
     */
    public function add(): Json
    {
        // 新增记录
        $model = new TemplateModel;
        if ($model->add($this->postForm())) {
            return $this->renderSuccess('添加成功');
        }
        return $this->renderError($model->getError() ?: '添加失败');
    }

    /**
     * 编辑模板
     * @param int $templateId
     * @return Json
     */
    public function edit(int $templateId): Json
    {
        // 模板详情
        $model = TemplateModel::detail($templateId);
        // 更新记录
        if ($model->edit($this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }

    /**
     * 删除模板
     * @param int $templateId
     * @return Json
     */
    public function delete(int $templateId): Json
    {
        // 模板详情
        $model = TemplateModel::detail($templateId);
        if (!$model->setDelete()) {
            return $this->renderError($model->getError() ?: '删除失败');
        }
        return $this->renderSuccess('删除成功');
    }
}
