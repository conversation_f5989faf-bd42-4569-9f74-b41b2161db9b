function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["./pages-index-index.wHGch4eT.js","./index.BVaTJikY.js","./_plugin-vue_export-helper.BCo6x5W8.js","./u-icon.CShoJDMR.js","./u-icon-Mqr0L85N.css","./uni-app.es.DtaL5fPi.js","./index.CqUd2cYd.js","./mp-html.DG3qE9GO.js","./mp-html-D4Yj8P7O.css","./myCoupon.WsqtsnX5.js","./index.D7I1Gtis.js","./index-BatsFtxP.css","./index.BS2D6QL7.js","./index-DlWzn76m.css","./GoodsStatus.DAadsW_Q.js","./u-tag.B8U4SC5W.js","./u-tag-B5bWAhuI.css","./color.D-c1b2x3.js","./ActiveStatus.BijUcMF9.js","./index-D9hzmxRo.css","./index.DxQ5ifuH.js","./u-mask.CGKhbF2S.js","./u-mask-BZoKSU4H.css","./index-Dfppe-P2.css","./index-C9NW1mTW.css","./pages-category-index.B34wpMqV.js","./wxofficial.CKoGItRK.js","./index.VBPje8kf.js","./index-DogTPK_E.css","./index.A1qcsqR-.js","./index-Bq4AMFfm.css","./mescroll-mixins.BtR42tWA.js","./mescroll-mixins-WkwNM_ff.css","./GoodsSource.tmQ6w63S.js","./cart.B5hmo82F.js","./index.CN8oc61w.js","./index.By37mpu2.js","./index-6K1ChUPE.css","./index-Cl7YKBde.css","./pages-cart-index.DppOFtkD.js","./index.JFDGAkCh.js","./index-C6LDUivd.css","./index-D8h8V075.css","./pages-user-index.CbUOyf_F.js","./user.DHo0p4Xn.js","./order.cPtXU8nA.js","./index-CeC7AVmb.css","./pages-custom-index.B-63u_i6.js","./index-D7pGCOzP.css","./pages-search-index.CH4gjY2p.js","./index-DPuCw0tH.css","./pages-login-index.COfh4f06.js","./captcha.6m0Gwld5.js","./verify.Dx6_1eNm.js","./upload.BtsupBh-.js","./index-uRi-qJeX.css","./pages-user-bind-index.7NHjYX-Q.js","./index-DZIJm0uT.css","./pages-user-personal-index.YOvr86bJ.js","./u-form.DZGwuxgr.js","./emitter.DrjJCwnj.js","./u-form-UR6Rzm6L.css","./index-DzSVOqsT.css","./pages-article-index.CDM7m5Vv.js","./u-tabs.CGZQcvwv.js","./u-tabs-Qelqn_UA.css","./index.DxVSrP_D.js","./index-DqKB4jxo.css","./pages-article-detail.BnYe0OVP.js","./detail-Xg7fvaX_.css","./pages-help-index.Dbttj1KI.js","./index-CKz3gRF3.css","./pages-coupon-index.B2p7NJM4.js","./CouponType.urBvWZTt.js","./index-DRuzuQ3y.css","./pages-goods-list.BKpMUPVF.js","./list-CoUUFa2Y.css","./pages-goods-detail.CyUxkTsn.js","./Comment.BLJxH3Sb.js","./u-popup.qw0t8K2V.js","./u-popup-DVZyARSg.css","./comment.B7BZICMf.js","./comment-CdMKZYMo.css","./Comment-C3i_TV70.css","./Market.Dl5g-Z8u.js","./u-modal.6LKX92cH.js","./u-loading.CaILF3tN.js","./u-loading-sn_eCQd2.css","./u-modal-BCGVDa4l.css","./Market-BIvASV-b.css","./detail-Bppj_ose.css","./pages-comment-index.ClXt4OyW.js","./index-BIbpOQjx.css","./pages-my-coupon-index.Cs_mPgJm.js","./index-DZnmtlPT.css","./pages-address-index.CqeqMHFd.js","./address.S09TeRNZ.js","./index-hBR2R_02.css","./pages-address-create.CCSwocck.js","./select-region.ykKOKa70.js","./select-region-d1806F6J.css","./create-DC9ffuJw.css","./pages-address-update.ScgDqsH9.js","./update-DkTw95FI.css","./pages-points-log.Dvu_MoLj.js","./log-CRpF5HHY.css","./pages-wallet-index.NwL0474E.js","./index-UKd6FiDw.css","./pages-wallet-balance-log.iWhn-b7d.js","./log-DuO0Xvtj.css","./pages-wallet-recharge-index.rPfqOwEA.js","./Client.CV9Ggpbn.js","./wechat.DZFk-0s7.js","./index-Bqq32Od9.css","./pages-wallet-recharge-order.-5OD1twq.js","./order-Ba7PaC4U.css","./pages-checkout-index.Dxyndjdk.js","./OrderType.DUMSPI6P.js","./index-CxaIm1lE.css","./pages-checkout-cashier-index.CRDBjvsX.js","./index-CfCqcxRV.css","./pages-order-center.Dq03wBFC.js","./center-rMqovsi2.css","./pages-order-index.Bh7X68jP.js","./index-BcLwU3VM.css","./pages-order-detail.ByX1qZk1.js","./close.PJAgWdi4.js","./detail-Bo_r8n8K.css","./pages-order-express-index.CzruM6XP.js","./index-Da4WwMCC.css","./pages-order-extract-check.Bd77uSx2.js","./check-C-HVowr_.css","./pages-order-comment-index.D_6wDRUx.js","./index-CnjKKjJw.css","./pages-refund-index.BOBrtNeH.js","./refund.Du_jyXYl.js","./index-oKuZhHyO.css","./pages-refund-detail.A5oF2rw8.js","./RefundType.DNjqPG-j.js","./detail-0CcCHxQt.css","./pages-refund-apply.DAq7RycQ.js","./apply-B1f-i-Z7.css","./pages-shop-extract.DpeamToG.js","./shop.CNgHgxyR.js","./extract-CBxSgWKs.css","./pages-shop-detail.BQ9spDsi.js","./detail-B5Insczj.css","./pages-dealer-index.CWHuUEQc.js","./index.C0lwFTcc.js","./index-BF5Z5teY.css","./pages-dealer-apply.IsLKy4i-.js","./Setting.DaFhd55E.js","./apply-ClEEQYoN.css","./pages-dealer-withdraw-apply.ByWXkg8m.js","./ApplyStatus.CARNfBPe.js","./apply-B2EbCfz5.css","./pages-dealer-withdraw-list.DSqQau7k.js","./list-BUjARfYt.css","./pages-dealer-poster.DJfF8o78.js","./poster-CbgU6YQw.css","./pages-dealer-order.sa9YeNyY.js","./order-7ECT8iAx.css","./pages-dealer-team.btgvhkNe.js","./team-BBmlRd5h.css","./pages-bargain-index.CH8TnzDB.js","./task.CuYOfIfl.js","./index-nZUIQcyU.css","./pages-bargain-goods-index.DATUhzbs.js","./index-Bc3geE4K.css","./pages-bargain-task.BORQKUaO.js","./task-DZ3Mx93Q.css","./pages-sharp-index.CFXQKpCP.js","./goods.HnIgZRBT.js","./index-CCS22-I4.css","./pages-sharp-goods-index.Dze2ceZ_.js","./index-BSxQwUPz.css","./pages-groupon-index.DnTWujKP.js","./task.BvedSfMW.js","./goods.D2XB6ORY.js","./Setting.Bg4Kp9c0.js","./index-BnQdmqfL.css","./pages-groupon-goods-index.B-_Mp1xi.js","./SkuPopup.Dcm-7u_h.js","./SkuPopup-DeEUeuZo.css","./index-CTsHL3JS.css","./pages-groupon-task-index.CivXDKfg.js","./index-D1EEgw9q.css","./pages-live-index.Bbpk7E_I.js","./index-BvVMdvjT.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const e={},t=function(t,n,o){let r=Promise.resolve();if(n&&n.length>0){const t=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),a=(null==i?void 0:i.nonce)||(null==i?void 0:i.getAttribute("nonce"));r=Promise.all(n.map((n=>{if(n=function(e,t){return new URL(e,t).href}(n,o),n in e)return;e[n]=!0;const r=n.endsWith(".css"),i=r?'[rel="stylesheet"]':"";if(!!o)for(let e=t.length-1;e>=0;e--){const o=t[e];if(o.href===n&&(!r||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${n}"]${i}`))return;const s=document.createElement("link");return s.rel=r?"stylesheet":"modulepreload",r||(s.as="script",s.crossOrigin=""),s.href=n,a&&s.setAttribute("nonce",a),document.head.appendChild(s),r?new Promise(((e,t)=>{s.addEventListener("load",e),s.addEventListener("error",(()=>t(new Error(`Unable to preload CSS for ${n}`))))})):void 0})))}return r.then((()=>t())).catch((e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}))};
/**
* @vue/shared v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
function n(e,t){const n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}const o={},r=[],i=()=>{},a=()=>!1,s=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),l=e=>e.startsWith("onUpdate:"),c=Object.assign,u=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},d=Object.prototype.hasOwnProperty,p=(e,t)=>d.call(e,t),f=Array.isArray,h=e=>"[object Map]"===x(e),g=e=>"[object Set]"===x(e),m=e=>"function"==typeof e,v=e=>"string"==typeof e,y=e=>"symbol"==typeof e,b=e=>null!==e&&"object"==typeof e,_=e=>(b(e)||m(e))&&m(e.then)&&m(e.catch),w=Object.prototype.toString,x=e=>w.call(e),T=e=>"[object Object]"===x(e),S=e=>v(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,k=n(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),C=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},E=/-(\w)/g,A=C((e=>e.replace(E,((e,t)=>t?t.toUpperCase():"")))),M=/\B([A-Z])/g,P=C((e=>e.replace(M,"-$1").toLowerCase())),I=C((e=>e.charAt(0).toUpperCase()+e.slice(1))),L=C((e=>e?`on${I(e)}`:"")),O=(e,t)=>!Object.is(e,t),$=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},D=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},R=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let B;const N=()=>B||(B="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function q(e){if(f(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=v(o)?F(o):q(o);if(r)for(const e in r)t[e]=r[e]}return t}if(v(e)||b(e))return e}const j=/;(?![^(]*\))/g,z=/:([^]+)/,V=/\/\*[^]*?\*\//g;function F(e){const t={};return e.replace(V,"").split(j).forEach((e=>{if(e){const n=e.split(z);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function U(e){let t="";if(v(e))t=e;else if(f(e))for(let n=0;n<e.length;n++){const o=U(e[n]);o&&(t+=o+" ")}else if(b(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const W=n("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function H(e){return!!e||""===e}const Y=e=>v(e)?e:null==e?"":f(e)||b(e)&&(e.toString===w||!m(e.toString))?JSON.stringify(e,X,2):String(e),X=(e,t)=>t&&t.__v_isRef?X(e,t.value):h(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[G(t,o)+" =>"]=n,e)),{})}:g(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>G(e)))}:y(t)?G(t):!b(t)||f(t)||T(t)?t:String(t),G=(e,t="")=>{var n;return y(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},J=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view"].map((e=>"uni-"+e)),Q=["list-view","list-item","sticky-section","sticky-header","cloud-db-element"].map((e=>"uni-"+e)),K=["list-item"].map((e=>"uni-"+e));function Z(e){if(-1!==K.indexOf(e))return!1;const t="uni-"+e.replace("v-uni-","");return-1!==J.indexOf(t)||-1!==Q.indexOf(t)}const ee=["%","%"],te=/^([a-z-]+:)?\/\//i,ne=/^data:.*,.*/;function oe(e){return e&&(e.appContext?e.proxy:e)}function re(e){if(!e)return;let t=e.type.name;for(;t&&Z(P(t));)t=(e=e.parent).type.name;return e.proxy}function ie(e){return 1===e.nodeType}function ae(e){if(e instanceof Map){const t={};return e.forEach(((e,n)=>{t[n]=e})),q(t)}if(v(e))return F(e);if(f(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=v(o)?F(o):ae(o);if(r)for(const e in r)t[e]=r[e]}return t}return q(e)}function se(e){let t="";if(e instanceof Map)e.forEach(((e,n)=>{e&&(t+=n+" ")}));else if(f(e))for(let n=0;n<e.length;n++){const o=se(e[n]);o&&(t+=o+" ")}else t=U(e);return t.trim()}function le(e){return 0===e.indexOf("/")}function ce(e){return le(e)?e:"/"+e}function ue(e){return le(e)?e.slice(1):e}function de(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}const pe=e=>e>9?e:"0"+e;function fe({date:e=new Date,mode:t="date"}){return"time"===t?pe(e.getHours())+":"+pe(e.getMinutes()):e.getFullYear()+"-"+pe(e.getMonth()+1)+"-"+pe(e.getDate())}function he(e,t){e=e||{},v(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?m(e.success)&&e.success(t):m(e.fail)&&e.fail(t),m(e.complete)&&e.complete(t)}function ge(e){return A(e.substring(5))}const me=de((()=>{const e=HTMLElement.prototype,t=e.setAttribute;e.setAttribute=function(e,n){if(e.startsWith("data-")&&this.tagName.startsWith("UNI-")){(this.__uniDataset||(this.__uniDataset={}))[ge(e)]=n}t.call(this,e,n)};const n=e.removeAttribute;e.removeAttribute=function(e){this.__uniDataset&&e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&delete this.__uniDataset[ge(e)],n.call(this,e)}}));function ve(e){return c({},e.dataset,e.__uniDataset)}const ye=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function be(e){return{passive:e}}function _e(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:ve(e),offsetTop:n,offsetLeft:o}}function we(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function xe(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=we(e[n])}catch(yC){t[n]=e[n]}})),t}const Te=/\+/g;function Se(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Te," ");let r=e.indexOf("="),i=we(r<0?e:e.slice(0,r)),a=r<0?null:we(e.slice(r+1));if(i in t){let e=t[i];f(e)||(e=t[i]=[e]),e.push(a)}else t[i]=a}return t}function ke(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);const i=()=>e.apply(this,arguments);r=o(i,t)};return i.cancel=function(){n(r)},i}class Ce{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const Ee=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Ae=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];let Me;const Pe=[];const Ie=de(((e,t)=>{if(m(e._component.onError))return t(e)})),Le=function(){};Le.prototype={on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var i=o.length-1;i>=0;i--)if(o[i].fn===t||o[i].fn._===t){o.splice(i,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var Oe=Le;const $e={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function De(e,t,n){if(v(t)&&t.startsWith("@")){let r=e[t.replace("@","")]||t;switch(n){case"titleColor":r="black"===r?"#000000":"#ffffff";break;case"borderStyle":r=(o=r)&&o in $e?$e[o]:o}return r}var o;return t}function Re(e,t={},n="light"){const o=t[n],r={};return void 0===o?e:(Object.keys(e).forEach((i=>{const a=e[i];r[i]=T(a)?Re(a,t,n):f(a)?a.map((e=>"object"==typeof e?Re(e,t,n):De(o,e))):De(o,a,i)})),r)}
/**
* @dcloudio/uni-h5-vue v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Be,Ne;class qe{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Be,!e&&Be&&(this.index=(Be.scopes||(Be.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=Be;try{return Be=this,e()}finally{Be=t}}}on(){Be=this}off(){Be=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function je(e){return new qe(e)}class ze{constructor(e,t,n,o){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=Be){t&&t.active&&t.effects.push(e)}(this,o)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,Xe();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),Ge()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=We,t=Ne;try{return We=!0,Ne=this,this._runnings++,Ve(this),this.fn()}finally{Fe(this),this._runnings--,Ne=t,We=e}}stop(){var e;this.active&&(Ve(this),Fe(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function Ve(e){e._trackId++,e._depsLength=0}function Fe(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)Ue(e.deps[t],e);e.deps.length=e._depsLength}}function Ue(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let We=!0,He=0;const Ye=[];function Xe(){Ye.push(We),We=!1}function Ge(){const e=Ye.pop();We=void 0===e||e}function Je(){He++}function Qe(){for(He--;!He&&Ze.length;)Ze.shift()()}function Ke(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&Ue(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const Ze=[];function et(e,t,n){Je();for(const o of e.keys()){let n;o._dirtyLevel<t&&(null!=n?n:n=e.get(o)===o._trackId)&&(o._shouldSchedule||(o._shouldSchedule=0===o._dirtyLevel),o._dirtyLevel=t),o._shouldSchedule&&(null!=n?n:n=e.get(o)===o._trackId)&&(o.trigger(),o._runnings&&!o.allowRecurse||2===o._dirtyLevel||(o._shouldSchedule=!1,o.scheduler&&Ze.push(o.scheduler)))}Qe()}const tt=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},nt=new WeakMap,ot=Symbol(""),rt=Symbol("");function it(e,t,n){if(We&&Ne){let t=nt.get(e);t||nt.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=tt((()=>t.delete(n)))),Ke(Ne,o)}}function at(e,t,n,o,r,i){const a=nt.get(e);if(!a)return;let s=[];if("clear"===t)s=[...a.values()];else if("length"===n&&f(e)){const e=Number(o);a.forEach(((t,n)=>{("length"===n||!y(n)&&n>=e)&&s.push(t)}))}else switch(void 0!==n&&s.push(a.get(n)),t){case"add":f(e)?S(n)&&s.push(a.get("length")):(s.push(a.get(ot)),h(e)&&s.push(a.get(rt)));break;case"delete":f(e)||(s.push(a.get(ot)),h(e)&&s.push(a.get(rt)));break;case"set":h(e)&&s.push(a.get(ot))}Je();for(const l of s)l&&et(l,4);Qe()}const st=n("__proto__,__v_isRef,__isVue"),lt=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(y)),ct=ut();function ut(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=Kt(this);for(let t=0,r=this.length;t<r;t++)it(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(Kt)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){Xe(),Je();const n=Kt(this)[t].apply(this,e);return Qe(),Ge(),n}})),e}function dt(e){const t=Kt(this);return it(t,0,e),t.hasOwnProperty(e)}class pt{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?Vt:zt:r?jt:qt).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const i=f(e);if(!o){if(i&&p(ct,t))return Reflect.get(ct,t,n);if("hasOwnProperty"===t)return dt}const a=Reflect.get(e,t,n);return(y(t)?lt.has(t):st(t))?a:(o||it(e,0,t),r?a:an(a)?i&&S(t)?a:a.value:b(a)?o?Ht(a):Ut(a):a)}}class ft extends pt{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=Gt(r);if(Jt(n)||Gt(n)||(r=Kt(r),n=Kt(n)),!f(e)&&an(r)&&!an(n))return!t&&(r.value=n,!0)}const i=f(e)&&S(t)?Number(t)<e.length:p(e,t),a=Reflect.set(e,t,n,o);return e===Kt(o)&&(i?O(n,r)&&at(e,"set",t,n):at(e,"add",t,n)),a}deleteProperty(e,t){const n=p(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&at(e,"delete",t,void 0),o}has(e,t){const n=Reflect.has(e,t);return y(t)&&lt.has(t)||it(e,0,t),n}ownKeys(e){return it(e,0,f(e)?"length":ot),Reflect.ownKeys(e)}}class ht extends pt{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const gt=new ft,mt=new ht,vt=new ft(!0),yt=e=>e,bt=e=>Reflect.getPrototypeOf(e);function _t(e,t,n=!1,o=!1){const r=Kt(e=e.__v_raw),i=Kt(t);n||(O(t,i)&&it(r,0,t),it(r,0,i));const{has:a}=bt(r),s=o?yt:n?tn:en;return a.call(r,t)?s(e.get(t)):a.call(r,i)?s(e.get(i)):void(e!==r&&e.get(t))}function wt(e,t=!1){const n=this.__v_raw,o=Kt(n),r=Kt(e);return t||(O(e,r)&&it(o,0,e),it(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function xt(e,t=!1){return e=e.__v_raw,!t&&it(Kt(e),0,ot),Reflect.get(e,"size",e)}function Tt(e){e=Kt(e);const t=Kt(this);return bt(t).has.call(t,e)||(t.add(e),at(t,"add",e,e)),this}function St(e,t){t=Kt(t);const n=Kt(this),{has:o,get:r}=bt(n);let i=o.call(n,e);i||(e=Kt(e),i=o.call(n,e));const a=r.call(n,e);return n.set(e,t),i?O(t,a)&&at(n,"set",e,t):at(n,"add",e,t),this}function kt(e){const t=Kt(this),{has:n,get:o}=bt(t);let r=n.call(t,e);r||(e=Kt(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&at(t,"delete",e,void 0),i}function Ct(){const e=Kt(this),t=0!==e.size,n=e.clear();return t&&at(e,"clear",void 0,void 0),n}function Et(e,t){return function(n,o){const r=this,i=r.__v_raw,a=Kt(i),s=t?yt:e?tn:en;return!e&&it(a,0,ot),i.forEach(((e,t)=>n.call(o,s(e),s(t),r)))}}function At(e,t,n){return function(...o){const r=this.__v_raw,i=Kt(r),a=h(i),s="entries"===e||e===Symbol.iterator&&a,l="keys"===e&&a,c=r[e](...o),u=n?yt:t?tn:en;return!t&&it(i,0,l?rt:ot),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:s?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Mt(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function Pt(){const e={get(e){return _t(this,e)},get size(){return xt(this)},has:wt,add:Tt,set:St,delete:kt,clear:Ct,forEach:Et(!1,!1)},t={get(e){return _t(this,e,!1,!0)},get size(){return xt(this)},has:wt,add:Tt,set:St,delete:kt,clear:Ct,forEach:Et(!1,!0)},n={get(e){return _t(this,e,!0)},get size(){return xt(this,!0)},has(e){return wt.call(this,e,!0)},add:Mt("add"),set:Mt("set"),delete:Mt("delete"),clear:Mt("clear"),forEach:Et(!0,!1)},o={get(e){return _t(this,e,!0,!0)},get size(){return xt(this,!0)},has(e){return wt.call(this,e,!0)},add:Mt("add"),set:Mt("set"),delete:Mt("delete"),clear:Mt("clear"),forEach:Et(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=At(r,!1,!1),n[r]=At(r,!0,!1),t[r]=At(r,!1,!0),o[r]=At(r,!0,!0)})),[e,n,t,o]}const[It,Lt,Ot,$t]=Pt();function Dt(e,t){const n=t?e?$t:Ot:e?Lt:It;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(p(n,o)&&o in t?n:t,o,r)}const Rt={get:Dt(!1,!1)},Bt={get:Dt(!1,!0)},Nt={get:Dt(!0,!1)},qt=new WeakMap,jt=new WeakMap,zt=new WeakMap,Vt=new WeakMap;function Ft(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>x(e).slice(8,-1))(e))}function Ut(e){return Gt(e)?e:Yt(e,!1,gt,Rt,qt)}function Wt(e){return Yt(e,!1,vt,Bt,jt)}function Ht(e){return Yt(e,!0,mt,Nt,zt)}function Yt(e,t,n,o,r){if(!b(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const a=Ft(e);if(0===a)return e;const s=new Proxy(e,2===a?o:n);return r.set(e,s),s}function Xt(e){return Gt(e)?Xt(e.__v_raw):!(!e||!e.__v_isReactive)}function Gt(e){return!(!e||!e.__v_isReadonly)}function Jt(e){return!(!e||!e.__v_isShallow)}function Qt(e){return Xt(e)||Gt(e)}function Kt(e){const t=e&&e.__v_raw;return t?Kt(t):e}function Zt(e){return Object.isExtensible(e)&&D(e,"__v_skip",!0),e}const en=e=>b(e)?Ut(e):e,tn=e=>b(e)?Ht(e):e;class nn{constructor(e,t,n,o){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new ze((()=>e(this._value)),(()=>rn(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=Kt(this);return e._cacheable&&!e.effect.dirty||!O(e._value,e._value=e.effect.run())||rn(e,4),on(e),e.effect._dirtyLevel>=2&&rn(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function on(e){var t;We&&Ne&&(e=Kt(e),Ke(Ne,null!=(t=e.dep)?t:e.dep=tt((()=>e.dep=void 0),e instanceof nn?e:void 0)))}function rn(e,t=4,n){const o=(e=Kt(e)).dep;o&&et(o,t)}function an(e){return!(!e||!0!==e.__v_isRef)}function sn(e){return cn(e,!1)}function ln(e){return cn(e,!0)}function cn(e,t){return an(e)?e:new un(e,t)}class un{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Kt(e),this._value=t?e:en(e)}get value(){return on(this),this._value}set value(e){const t=this.__v_isShallow||Jt(e)||Gt(e);e=t?e:Kt(e),O(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:en(e),rn(this,4))}}function dn(e){return an(e)?e.value:e}const pn={get:(e,t,n)=>dn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return an(r)&&!an(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function fn(e){return Xt(e)?e:new Proxy(e,pn)}function hn(e,t,n,o){try{return o?e(...o):e()}catch(r){mn(r,t,n)}}function gn(e,t,n,o){if(m(e)){const r=hn(e,t,n,o);return r&&_(r)&&r.catch((e=>{mn(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(gn(e[i],t,n,o));return r}function mn(e,t,n,o=!0){t&&t.vnode;if(t){let o=t.parent;const r=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const a=t.appContext.config.errorHandler;if(a)return void hn(a,null,10,[e,r,i])}!function(e,t,n,o=!0){console.error(e)}(e,0,0,o)}let vn=!1,yn=!1;const bn=[];let _n=0;const wn=[];let xn=null,Tn=0;const Sn=Promise.resolve();let kn=null;function Cn(e){const t=kn||Sn;return e?t.then(this?e.bind(this):e):t}function En(e){bn.length&&bn.includes(e,vn&&e.allowRecurse?_n+1:_n)||(null==e.id?bn.push(e):bn.splice(function(e){let t=_n+1,n=bn.length;for(;t<n;){const o=t+n>>>1,r=bn[o],i=In(r);i<e||i===e&&r.pre?t=o+1:n=o}return t}(e.id),0,e),An())}function An(){vn||yn||(yn=!0,kn=Sn.then(On))}function Mn(e,t,n=(vn?_n+1:0)){for(;n<bn.length;n++){const t=bn[n];if(t&&t.pre){if(e&&t.id!==e.uid)continue;bn.splice(n,1),n--,t()}}}function Pn(e){if(wn.length){const e=[...new Set(wn)].sort(((e,t)=>In(e)-In(t)));if(wn.length=0,xn)return void xn.push(...e);for(xn=e,Tn=0;Tn<xn.length;Tn++)xn[Tn]();xn=null,Tn=0}}const In=e=>null==e.id?1/0:e.id,Ln=(e,t)=>{const n=In(e)-In(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function On(e){yn=!1,vn=!0,bn.sort(Ln);try{for(_n=0;_n<bn.length;_n++){const e=bn[_n];e&&!1!==e.active&&hn(e,null,14)}}finally{_n=0,bn.length=0,Pn(),vn=!1,kn=null,(bn.length||wn.length)&&On()}}function $n(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||o;let i=n;const a=t.startsWith("update:"),s=a&&t.slice(7);if(s&&s in r){const e=`${"modelValue"===s?"model":s}Modifiers`,{number:t,trim:a}=r[e]||o;a&&(i=n.map((e=>v(e)?e.trim():e))),t&&(i=n.map(R))}let l,c=r[l=L(t)]||r[l=L(A(t))];!c&&a&&(c=r[l=L(P(t))]),c&&gn(c,e,6,Dn(e,c,i));const u=r[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,gn(u,e,6,Dn(e,u,i))}}function Dn(e,t,n){if(1!==n.length)return n;if(m(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&p(o,"type")&&p(o,"timeStamp")&&p(o,"target")&&p(o,"currentTarget")&&p(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function Rn(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let a={},s=!1;if(!m(e)){const o=e=>{const n=Rn(e,t,!0);n&&(s=!0,c(a,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||s?(f(i)?i.forEach((e=>a[e]=null)):c(a,i),b(e)&&o.set(e,a),a):(b(e)&&o.set(e,null),null)}function Bn(e,t){return!(!e||!s(t))&&(t=t.slice(2).replace(/Once$/,""),p(e,t[0].toLowerCase()+t.slice(1))||p(e,P(t))||p(e,t))}let Nn=null,qn=null;function jn(e){const t=Nn;return Nn=e,qn=e&&e.type.__scopeId||null,t}function zn(e,t=Nn,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&Xr(-1);const r=jn(t);let i;try{i=e(...n)}finally{jn(r),o._d&&Xr(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function Vn(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[a],slots:s,attrs:c,emit:u,render:d,renderCache:p,data:f,setupState:h,ctx:g,inheritAttrs:m}=e;let v,y;const b=jn(e);try{if(4&n.shapeFlag){const e=r||o,t=e;v=li(d.call(t,e,p,i,h,f,g)),y=c}else{const e=t;0,v=li(e.length>1?e(i,{attrs:c,slots:s,emit:u}):e(i,null)),y=t.props?c:Fn(c)}}catch(w){Ur.length=0,mn(w,e,1),v=ri(Vr)}let _=v;if(y&&!1!==m){const e=Object.keys(y),{shapeFlag:t}=_;e.length&&7&t&&(a&&e.some(l)&&(y=Un(y,a)),_=ii(_,y))}return n.dirs&&(_=ii(_),_.dirs=_.dirs?_.dirs.concat(n.dirs):n.dirs),n.transition&&(_.transition=n.transition),v=_,jn(b),v}const Fn=e=>{let t;for(const n in e)("class"===n||"style"===n||s(n))&&((t||(t={}))[n]=e[n]);return t},Un=(e,t)=>{const n={};for(const o in e)l(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function Wn(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!Bn(n,i))return!0}return!1}function Hn(e,t){return Gn("components",e,!0,t)||e}const Yn=Symbol.for("v-ndc");function Xn(e){return v(e)?Gn("components",e,!1)||e:e||Yn}function Gn(e,t,n=!0,o=!1){const r=Nn||gi;if(r){const n=r.type;if("components"===e){const e=Ci(n,!1);if(e&&(e===t||e===A(t)||e===I(A(t))))return n}const i=Jn(r[e]||n[e],t)||Jn(r.appContext[e],t);return!i&&o?n:i}}function Jn(e,t){return e&&(e[t]||e[A(t)]||e[I(A(t))])}const Qn=e=>e.__isSuspense;const Kn=Symbol.for("v-scx");function Zn(e,t){return no(e,null,t)}const eo={};function to(e,t,n){return no(e,t,n)}function no(e,t,{immediate:n,deep:r,flush:a,once:s,onTrack:l,onTrigger:c}=o){if(t&&s){const e=t;t=(...t)=>{e(...t),C()}}const d=gi,p=e=>!0===r?e:io(e,!1===r?1:void 0);let h,g,v=!1,y=!1;if(an(e)?(h=()=>e.value,v=Jt(e)):Xt(e)?(h=()=>p(e),v=!0):f(e)?(y=!0,v=e.some((e=>Xt(e)||Jt(e))),h=()=>e.map((e=>an(e)?e.value:Xt(e)?p(e):m(e)?hn(e,d,2):void 0))):h=m(e)?t?()=>hn(e,d,2):()=>(g&&g(),gn(e,d,3,[_])):i,t&&r){const e=h;h=()=>io(e())}let b,_=e=>{g=S.onStop=()=>{hn(e,d,4),g=S.onStop=void 0}};if(xi){if(_=i,t?n&&gn(t,d,3,[h(),y?[]:void 0,_]):h(),"sync"!==a)return i;{const e=_r(Kn);b=e.__watcherHandles||(e.__watcherHandles=[])}}let w=y?new Array(e.length).fill(eo):eo;const x=()=>{if(S.active&&S.dirty)if(t){const e=S.run();(r||v||(y?e.some(((e,t)=>O(e,w[t]))):O(e,w)))&&(g&&g(),gn(t,d,3,[e,w===eo?void 0:y&&w[0]===eo?[]:w,_]),w=e)}else S.run()};let T;x.allowRecurse=!!t,"sync"===a?T=x:"post"===a?T=()=>$r(x,d&&d.suspense):(x.pre=!0,d&&(x.id=d.uid),T=()=>En(x));const S=new ze(h,i,T),k=Be,C=()=>{S.stop(),k&&u(k.effects,S)};return t?n?x():w=S.run():"post"===a?$r(S.run.bind(S),d&&d.suspense):S.run(),b&&b.push(C),C}function oo(e,t,n){const o=this.proxy,r=v(e)?e.includes(".")?ro(o,e):()=>o[e]:e.bind(o,o);let i;m(t)?i=t:(i=t.handler,n=t);const a=bi(this),s=no(r,i.bind(o),n);return a(),s}function ro(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function io(e,t,n=0,o){if(!b(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((o=o||new Set).has(e))return e;if(o.add(e),an(e))io(e.value,t,n,o);else if(f(e))for(let r=0;r<e.length;r++)io(e[r],t,n,o);else if(g(e)||h(e))e.forEach((e=>{io(e,t,n,o)}));else if(T(e))for(const r in e)io(e[r],t,n,o);return e}function ao(e,t){if(null===Nn)return e;const n=ki(Nn)||Nn.proxy,r=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[e,a,s,l=o]=t[i];e&&(m(e)&&(e={mounted:e,updated:e}),e.deep&&io(a),r.push({dir:e,instance:n,value:a,oldValue:void 0,arg:s,modifiers:l}))}return e}function so(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let a=0;a<r.length;a++){const s=r[a];i&&(s.oldValue=i[a].value);let l=s.dir[o];l&&(Xe(),gn(l,n,8,[e.el,s,e,t]),Ge())}}const lo=Symbol("_leaveCb"),co=Symbol("_enterCb");const uo=[Function,Array],po={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:uo,onEnter:uo,onAfterEnter:uo,onEnterCancelled:uo,onBeforeLeave:uo,onLeave:uo,onAfterLeave:uo,onLeaveCancelled:uo,onBeforeAppear:uo,onAppear:uo,onAfterAppear:uo,onAppearCancelled:uo},fo={name:"BaseTransition",props:po,setup(e,{slots:t}){const n=mi(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return jo((()=>{e.isMounted=!0})),Fo((()=>{e.isUnmounting=!0})),e}();return()=>{const r=t.default&&bo(t.default(),!0);if(!r||!r.length)return;let i=r[0];if(r.length>1)for(const e of r)if(e.type!==Vr){i=e;break}const a=Kt(e),{mode:s}=a;if(o.isLeaving)return mo(i);const l=vo(i);if(!l)return mo(i);const c=go(l,a,o,n);yo(l,c);const u=n.subTree,d=u&&vo(u);if(d&&d.type!==Vr&&!Zr(l,d)){const e=go(d,a,o,n);if(yo(d,e),"out-in"===s)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&(n.effect.dirty=!0,n.update())},mo(i);"in-out"===s&&l.type!==Vr&&(e.delayLeave=(e,t,n)=>{ho(o,d)[String(d.key)]=d,e[lo]=()=>{t(),e[lo]=void 0,delete c.delayedLeave},c.delayedLeave=n})}return i}}};function ho(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function go(e,t,n,o){const{appear:r,mode:i,persisted:a=!1,onBeforeEnter:s,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:p,onAfterLeave:h,onLeaveCancelled:g,onBeforeAppear:m,onAppear:v,onAfterAppear:y,onAppearCancelled:b}=t,_=String(e.key),w=ho(n,e),x=(e,t)=>{e&&gn(e,o,9,t)},T=(e,t)=>{const n=t[1];x(e,t),f(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},S={mode:i,persisted:a,beforeEnter(t){let o=s;if(!n.isMounted){if(!r)return;o=m||s}t[lo]&&t[lo](!0);const i=w[_];i&&Zr(e,i)&&i.el[lo]&&i.el[lo](),x(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=v||l,o=y||c,i=b||u}let a=!1;const s=e[co]=t=>{a||(a=!0,x(t?i:o,[e]),S.delayedLeave&&S.delayedLeave(),e[co]=void 0)};t?T(t,[e,s]):s()},leave(t,o){const r=String(e.key);if(t[co]&&t[co](!0),n.isUnmounting)return o();x(d,[t]);let i=!1;const a=t[lo]=n=>{i||(i=!0,o(),x(n?g:h,[t]),t[lo]=void 0,w[r]===e&&delete w[r])};w[r]=e,p?T(p,[t,a]):a()},clone:e=>go(e,t,n,o)};return S}function mo(e){if(So(e))return(e=ii(e)).children=null,e}function vo(e){return So(e)?e.children?e.children[0]:void 0:e}function yo(e,t){6&e.shapeFlag&&e.component?yo(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function bo(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let a=e[i];const s=null==n?a.key:String(n)+String(null!=a.key?a.key:i);a.type===jr?(128&a.patchFlag&&r++,o=o.concat(bo(a.children,t,s))):(t||a.type!==Vr)&&o.push(null!=s?ii(a,{key:s}):a)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}
/*! #__NO_SIDE_EFFECTS__ */function _o(e,t){return m(e)?(()=>c({name:e.name},t,{setup:e}))():e}const wo=e=>!!e.type.__asyncLoader
/*! #__NO_SIDE_EFFECTS__ */;function xo(e){m(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:a=!0,onError:s}=e;let l,c=null,u=0;const d=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),s)return new Promise(((t,n)=>{s(e,(()=>t((u++,c=null,d()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return _o({name:"AsyncComponentWrapper",__asyncLoader:d,get __asyncResolved(){return l},setup(){const e=gi;if(l)return()=>To(l,e);const t=t=>{c=null,mn(t,e,13,!o)};if(a&&e.suspense||xi)return d().then((t=>()=>To(t,e))).catch((e=>(t(e),()=>o?ri(o,{error:e}):null)));const s=sn(!1),u=sn(),p=sn(!!r);return r&&setTimeout((()=>{p.value=!1}),r),null!=i&&setTimeout((()=>{if(!s.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),d().then((()=>{s.value=!0,e.parent&&So(e.parent.vnode)&&(e.parent.effect.dirty=!0,En(e.parent.update))})).catch((e=>{t(e),u.value=e})),()=>s.value&&l?To(l,e):u.value&&o?ri(o,{error:u.value}):n&&!p.value?ri(n):void 0}})}function To(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,a=ri(e,o,r);return a.ref=n,a.ce=i,delete t.vnode.ce,a}const So=e=>e.type.__isKeepAlive;class ko{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,r=t.get(e);if(r)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return r}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const Co={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=mi(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=e.cache||new ko(e.max);r.pruneCacheEntry=a;let i=null;function a(t){var o;!i||!Zr(t,i)||"key"===e.matchBy&&t.key!==i.key?(Oo(o=t),u(o,n,s,!0)):i&&Oo(i)}const s=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:d}}}=o,p=d("div");function f(t){r.forEach(((n,o)=>{const i=Do(n,e.matchBy);!i||t&&t(i)||(r.delete(o),a(n))}))}o.activate=(e,t,n,o,r)=>{const i=e.component;if(i.ba){const e=i.isDeactivated;i.isDeactivated=!1,$(i.ba),i.isDeactivated=e}c(e,t,n,0,s),l(i.vnode,e,t,n,i,s,o,e.slotScopeIds,r),$r((()=>{i.isDeactivated=!1,i.a&&$(i.a);const t=e.props&&e.props.onVnodeMounted;t&&pi(t,i.parent,e)}),s)},o.deactivate=e=>{const t=e.component;t.bda&&Ro(t.bda),c(e,p,null,1,s),$r((()=>{t.bda&&t.bda.forEach((e=>e.__called=!1)),t.da&&$(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&pi(n,t.parent,e),t.isDeactivated=!0}),s)},to((()=>[e.include,e.exclude,e.matchBy]),(([e,t])=>{e&&f((t=>Ao(e,t))),t&&f((e=>!Ao(t,e)))}),{flush:"post",deep:!0});let h=null;const g=()=>{null!=h&&r.set(h,$o(n.subTree))};return jo(g),Vo(g),Fo((()=>{r.forEach(((t,o)=>{r.delete(o),a(t);const{subTree:i,suspense:s}=n,l=$o(i);if(t.type!==l.type||"key"===e.matchBy&&t.key!==l.key);else{l.component.bda&&$(l.component.bda),Oo(l);const e=l.component.da;e&&$r(e,s)}}))})),()=>{if(h=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return i=null,n;if(!Kr(o)||!(4&o.shapeFlag)&&!Qn(o.type))return i=null,o;let a=$o(o);const s=a.type,l=Do(a,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!Ao(c,l))||u&&l&&Ao(u,l))return i=a,o;const d=null==a.key?s:a.key,p=r.get(d);return a.el&&(a=ii(a),Qn(o.type)&&(o.ssContent=a)),h=d,p&&(a.el=p.el,a.component=p.component,a.transition&&yo(a,a.transition),a.shapeFlag|=512),a.shapeFlag|=256,i=a,Qn(o.type)?o:a}}},Eo=Co;function Ao(e,t){return f(e)?e.some((e=>Ao(e,t))):v(e)?e.split(",").includes(t):"[object RegExp]"===x(e)&&e.test(t)}function Mo(e,t){Io(e,"a",t)}function Po(e,t){Io(e,"da",t)}function Io(e,t,n=gi){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,Bo(t,o,n),n){let e=n.parent;for(;e&&e.parent;)So(e.parent.vnode)&&Lo(o,t,n,e),e=e.parent}}function Lo(e,t,n,o){const r=Bo(t,e,o,!0);Uo((()=>{u(o[t],r)}),n)}function Oo(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function $o(e){return Qn(e.type)?e.ssContent:e}function Do(e,t){if("name"===t){const t=e.type;return Ci(wo(e)?t.__asyncResolved||{}:t)}return String(e.key)}function Ro(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function Bo(e,t,n=gi,o=!1){if(n){if(r=e,Ee.indexOf(r)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return["onLoad","onShow"].indexOf(e)>-1}(e))){const o=n.proxy;gn(t.bind(o),n,e,"onLoad"===e?[o.$page.options]:[])}}const i=n[e]||(n[e]=[]),a=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;Xe();const r=bi(n),i=gn(t,n,e,o);return r(),Ge(),i});return o?i.unshift(a):i.push(a),a}var r}const No=e=>(t,n=gi)=>(!xi||"sp"===e)&&Bo(e,((...e)=>t(...e)),n),qo=No("bm"),jo=No("m"),zo=No("bu"),Vo=No("u"),Fo=No("bum"),Uo=No("um"),Wo=No("sp"),Ho=No("rtg"),Yo=No("rtc");function Xo(e,t=gi){Bo("ec",e,t)}function Go(e,t,n,o){let r;const i=n&&n[o];if(f(e)||v(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i&&i[n])}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i&&i[n])}else if(b(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,a=n.length;o<a;o++){const a=n[o];r[o]=t(e[a],a,o,i&&i[o])}}else r=[];return n&&(n[o]=r),r}function Jo(e,t,n={},o,r){if(Nn.isCE||Nn.parent&&wo(Nn.parent)&&Nn.parent.isCE)return"default"!==t&&(n.name=t),ri("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),Hr();const a=i&&Qo(i(n)),s=Qr(jr,{key:n.key||a&&a.key||`_${t}`},a||(o?o():[]),a&&1===e._?64:-2);return!r&&s.scopeId&&(s.slotScopeIds=[s.scopeId+"-s"]),i&&i._c&&(i._d=!0),s}function Qo(e){return e.some((e=>!Kr(e)||e.type!==Vr&&!(e.type===jr&&!Qo(e.children))))?e:null}const Ko=e=>{if(!e)return null;if(wi(e)){return ki(e)||e.proxy}return Ko(e.parent)},Zo=c(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ko(e.parent),$root:e=>Ko(e.root),$emit:e=>e.emit,$options:e=>sr(e),$forceUpdate:e=>e.f||(e.f=(e=>function(){e.effect.dirty=!0,En(e.update)})(e)),$nextTick:e=>e.n||(e.n=Cn.bind(e.proxy)),$watch:e=>oo.bind(e)}),er=(e,t)=>e!==o&&!e.__isScriptSetup&&p(e,t),tr={get({_:e},t){const{ctx:n,setupState:r,data:i,props:a,accessCache:s,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=s[t];if(void 0!==l)switch(l){case 1:return r[t];case 2:return i[t];case 4:return n[t];case 3:return a[t]}else{if(er(r,t))return s[t]=1,r[t];if(i!==o&&p(i,t))return s[t]=2,i[t];if((u=e.propsOptions[0])&&p(u,t))return s[t]=3,a[t];if(n!==o&&p(n,t))return s[t]=4,n[t];or&&(s[t]=0)}}const d=Zo[t];let f,h;return d?("$attrs"===t&&it(e,0,t),d(e)):(f=l.__cssModules)&&(f=f[t])?f:n!==o&&p(n,t)?(s[t]=4,n[t]):(h=c.config.globalProperties,p(h,t)?h[t]:void 0)},set({_:e},t,n){const{data:r,setupState:i,ctx:a}=e;return er(i,t)?(i[t]=n,!0):r!==o&&p(r,t)?(r[t]=n,!0):!p(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(a[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:i,propsOptions:a}},s){let l;return!!n[s]||e!==o&&p(e,s)||er(t,s)||(l=a[0])&&p(l,s)||p(r,s)||p(Zo,s)||p(i.config.globalProperties,s)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:p(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function nr(e){return f(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let or=!0;function rr(e){const t=sr(e),n=e.proxy,o=e.ctx;or=!1,t.beforeCreate&&ir(t.beforeCreate,e,"bc");const{data:r,computed:a,methods:s,watch:l,provide:c,inject:u,created:d,beforeMount:p,mounted:h,beforeUpdate:g,updated:v,activated:y,deactivated:_,beforeDestroy:w,beforeUnmount:x,destroyed:T,unmounted:S,render:k,renderTracked:C,renderTriggered:E,errorCaptured:A,serverPrefetch:M,expose:P,inheritAttrs:I,components:L,directives:O,filters:$}=t;if(u&&function(e,t,n=i){f(e)&&(e=dr(e));for(const o in e){const n=e[o];let r;r=b(n)?"default"in n?_r(n.from||o,n.default,!0):_r(n.from||o):_r(n),an(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[o]=r}}(u,o,null),s)for(const i in s){const e=s[i];m(e)&&(o[i]=e.bind(n))}if(r){const t=r.call(n,n);b(t)&&(e.data=Ut(t))}if(or=!0,a)for(const f in a){const e=a[f],t=m(e)?e.bind(n,n):m(e.get)?e.get.bind(n,n):i,r=!m(e)&&m(e.set)?e.set.bind(n):i,s=Ei({get:t,set:r});Object.defineProperty(o,f,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(l)for(const i in l)ar(l[i],o,n,i);if(c){const e=m(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{br(t,e[t])}))}function D(e,t){f(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&ir(d,e,"c"),D(qo,p),D(jo,h),D(zo,g),D(Vo,v),D(Mo,y),D(Po,_),D(Xo,A),D(Yo,C),D(Ho,E),D(Fo,x),D(Uo,S),D(Wo,M),f(P))if(P.length){const t=e.exposed||(e.exposed={});P.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});k&&e.render===i&&(e.render=k),null!=I&&(e.inheritAttrs=I),L&&(e.components=L),O&&(e.directives=O);const R=e.appContext.config.globalProperties.$applyOptions;R&&R(t,e,n)}function ir(e,t,n){gn(f(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function ar(e,t,n,o){const r=o.includes(".")?ro(n,o):()=>n[o];if(v(e)){const n=t[e];m(n)&&to(r,n)}else if(m(e))to(r,e.bind(n));else if(b(e))if(f(e))e.forEach((e=>ar(e,t,n,o)));else{const o=m(e.handler)?e.handler.bind(n):t[e.handler];m(o)&&to(r,o,e)}}function sr(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,s=i.get(t);let l;return s?l=s:r.length||n||o?(l={},r.length&&r.forEach((e=>lr(l,e,a,!0))),lr(l,t,a)):l=t,b(t)&&i.set(t,l),l}function lr(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&lr(e,i,n,!0),r&&r.forEach((t=>lr(e,t,n,!0)));for(const a in t)if(o&&"expose"===a);else{const o=cr[a]||n&&n[a];e[a]=o?o(e[a],t[a]):t[a]}return e}const cr={data:ur,props:hr,emits:hr,methods:fr,computed:fr,beforeCreate:pr,created:pr,beforeMount:pr,mounted:pr,beforeUpdate:pr,updated:pr,beforeDestroy:pr,beforeUnmount:pr,destroyed:pr,unmounted:pr,activated:pr,deactivated:pr,errorCaptured:pr,serverPrefetch:pr,components:fr,directives:fr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=c(Object.create(null),e);for(const o in t)n[o]=pr(e[o],t[o]);return n},provide:ur,inject:function(e,t){return fr(dr(e),dr(t))}};function ur(e,t){return t?e?function(){return c(m(e)?e.call(this,this):e,m(t)?t.call(this,this):t)}:t:e}function dr(e){if(f(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function pr(e,t){return e?[...new Set([].concat(e,t))]:t}function fr(e,t){return e?c(Object.create(null),e,t):t}function hr(e,t){return e?f(e)&&f(t)?[...new Set([...e,...t])]:c(Object.create(null),nr(e),nr(null!=t?t:{})):t}function gr(){return{app:null,config:{isNativeTag:a,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let mr=0;function vr(e,t){return function(n,o=null){m(n)||(n=c({},n)),null==o||b(o)||(o=null);const r=gr(),i=new WeakSet;let a=!1;const s=r.app={_uid:mr++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:Mi,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&m(e.install)?(i.add(e),e.install(s,...t)):m(e)&&(i.add(e),e(s,...t))),s),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),s),component:(e,t)=>t?(r.components[e]=t,s):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,s):r.directives[e],mount(i,l,c){if(!a){const u=ri(n,o);return u.appContext=r,!0===c?c="svg":!1===c&&(c=void 0),l&&t?t(u,i):e(u,i,c),a=!0,s._container=i,i.__vue_app__=s,s._instance=u.component,ki(u.component)||u.component.proxy}},unmount(){a&&(e(null,s._container),delete s._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,s),runWithContext(e){const t=yr;yr=s;try{return e()}finally{yr=t}}};return s}}let yr=null;function br(e,t){if(gi){let n=gi.provides;const o=gi.parent&&gi.parent.provides;o===n&&(n=gi.provides=Object.create(o)),n[e]=t,"app"===gi.type.mpType&&gi.appContext.app.provide(e,t)}else;}function _r(e,t,n=!1){const o=gi||Nn;if(o||yr){const r=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:yr._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&m(t)?t.call(o&&o.proxy):t}}function wr(e,t,n,r){const[i,a]=e.propsOptions;let s,l=!1;if(t)for(let o in t){if(k(o))continue;const c=t[o];let u;i&&p(i,u=A(o))?a&&a.includes(u)?(s||(s={}))[u]=c:n[u]=c:Bn(e.emitsOptions,o)||o in r&&c===r[o]||(r[o]=c,l=!0)}if(a){const t=Kt(n),r=s||o;for(let o=0;o<a.length;o++){const s=a[o];n[s]=xr(i,t,s,r[s],e,!p(r,s))}}return l}function xr(e,t,n,o,r,i){const a=e[n];if(null!=a){const e=p(a,"default");if(e&&void 0===o){const e=a.default;if(a.type!==Function&&!a.skipFactory&&m(e)){const{propsDefaults:i}=r;if(n in i)o=i[n];else{const a=bi(r);o=i[n]=e.call(null,t),a()}}else o=e}a[0]&&(i&&!e?o=!1:!a[1]||""!==o&&o!==P(n)||(o=!0))}return o}function Tr(e,t,n=!1){const i=t.propsCache,a=i.get(e);if(a)return a;const s=e.props,l={},u=[];let d=!1;if(!m(e)){const o=e=>{d=!0;const[n,o]=Tr(e,t,!0);c(l,n),o&&u.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!s&&!d)return b(e)&&i.set(e,r),r;if(f(s))for(let r=0;r<s.length;r++){const e=A(s[r]);Sr(e)&&(l[e]=o)}else if(s)for(const o in s){const e=A(o);if(Sr(e)){const t=s[o],n=l[e]=f(t)||m(t)?{type:t}:c({},t);if(n){const t=Er(Boolean,n.type),o=Er(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||p(n,"default"))&&u.push(e)}}}const h=[l,u];return b(e)&&i.set(e,h),h}function Sr(e){return"$"!==e[0]&&!k(e)}function kr(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function Cr(e,t){return kr(e)===kr(t)}function Er(e,t){return f(t)?t.findIndex((t=>Cr(t,e))):m(t)&&Cr(t,e)?0:-1}const Ar=e=>"_"===e[0]||"$stable"===e,Mr=e=>f(e)?e.map(li):[li(e)],Pr=(e,t,n)=>{if(t._n)return t;const o=zn(((...e)=>Mr(t(...e))),n);return o._c=!1,o},Ir=(e,t,n)=>{const o=e._ctx;for(const r in e){if(Ar(r))continue;const n=e[r];if(m(n))t[r]=Pr(0,n,o);else if(null!=n){const e=Mr(n);t[r]=()=>e}}},Lr=(e,t)=>{const n=Mr(t);e.slots.default=()=>n};function Or(e,t,n,r,i=!1){if(f(e))return void e.forEach(((e,o)=>Or(e,t&&(f(t)?t[o]:t),n,r,i)));if(wo(r)&&!i)return;const a=4&r.shapeFlag?ki(r.component)||r.component.proxy:r.el,s=i?null:a,{i:l,r:c}=e,d=t&&t.r,h=l.refs===o?l.refs={}:l.refs,g=l.setupState;if(null!=d&&d!==c&&(v(d)?(h[d]=null,p(g,d)&&(g[d]=null)):an(d)&&(d.value=null)),m(c))hn(c,l,12,[s,h]);else{const t=v(c),o=an(c);if(t||o){const r=()=>{if(e.f){const n=t?p(g,c)?g[c]:h[c]:c.value;i?f(n)&&u(n,a):f(n)?n.includes(a)||n.push(a):t?(h[c]=[a],p(g,c)&&(g[c]=h[c])):(c.value=[a],e.k&&(h[e.k]=c.value))}else t?(h[c]=s,p(g,c)&&(g[c]=s)):o&&(c.value=s,e.k&&(h[e.k]=s))};s?(r.id=-1,$r(r,n)):r()}}}const $r=function(e,t){var n;t&&t.pendingBranch?f(e)?t.effects.push(...e):t.effects.push(e):(f(n=e)?wn.push(...n):xn&&xn.includes(n,n.allowRecurse?Tn+1:Tn)||wn.push(n),An())};function Dr(e){return function(e,t){N().__VUE__=!0;const{insert:n,remove:a,patchProp:s,forcePatchProp:l,createElement:u,createText:d,createComment:f,setText:h,setElementText:g,parentNode:m,nextSibling:v,setScopeId:y=i,insertStaticContent:b}=e,w=(e,t,n,o=null,r=null,i=null,a,s=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!Zr(e,t)&&(o=te(e),J(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case zr:x(e,t,n,o);break;case Vr:T(e,t,n,o);break;case Fr:null==e&&S(t,n,o,a);break;case jr:j(e,t,n,o,r,i,a,s,l);break;default:1&d?M(e,t,n,o,r,i,a,s,l):6&d?z(e,t,n,o,r,i,a,s,l):(64&d||128&d)&&c.process(e,t,n,o,r,i,a,s,l,re)}null!=u&&r&&Or(u,e&&e.ref,i,t||e,!t)},x=(e,t,o,r)=>{if(null==e)n(t.el=d(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&h(n,t.children)}},T=(e,t,o,r)=>{null==e?n(t.el=f(t.children||""),o,r):t.el=e.el},S=(e,t,n,o)=>{[e.el,e.anchor]=b(e.children,t,n,o,e.el,e.anchor)},C=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=v(e),n(e,o,r),e=i;n(t,o,r)},E=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=v(e),a(e),e=n;a(t)},M=(e,t,n,o,r,i,a,s,l)=>{"svg"===t.type?a="svg":"math"===t.type&&(a="mathml"),null==e?I(t,n,o,r,i,a,s,l):R(e,t,r,i,a,s,l)},I=(e,t,o,r,i,a,l,c)=>{let d,p;const{props:f,shapeFlag:h,transition:m,dirs:v}=e;if(d=e.el=u(e.type,a,f&&f.is,f),8&h?g(d,e.children):16&h&&O(e.children,d,null,r,i,Rr(e,a),l,c),v&&so(e,null,r,"created"),L(d,e,e.scopeId,l,r),f){for(const t in f)"value"===t||k(t)||s(d,t,null,f[t],a,e.children,r,i,ee);"value"in f&&s(d,"value",null,f.value,a),(p=f.onVnodeBeforeMount)&&pi(p,r,e)}Object.defineProperty(d,"__vueParentComponent",{value:r,enumerable:!1}),v&&so(e,null,r,"beforeMount");const y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(i,m);y&&m.beforeEnter(d),n(d,t,o),((p=f&&f.onVnodeMounted)||y||v)&&$r((()=>{p&&pi(p,r,e),y&&m.enter(d),v&&so(e,null,r,"mounted")}),i)},L=(e,t,n,o,r)=>{if(n&&y(e,n),o)for(let i=0;i<o.length;i++)y(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;L(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},O=(e,t,n,o,r,i,a,s,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=s?ci(e[c]):li(e[c]);w(null,l,t,n,o,r,i,a,s)}},R=(e,t,n,r,i,a,c)=>{const u=t.el=e.el;let{patchFlag:d,dynamicChildren:p,dirs:f}=t;d|=16&e.patchFlag;const h=e.props||o,m=t.props||o;let v;if(n&&Br(n,!1),(v=m.onVnodeBeforeUpdate)&&pi(v,n,t,e),f&&so(t,e,n,"beforeUpdate"),n&&Br(n,!0),p?B(e.dynamicChildren,p,u,n,r,Rr(t,i),a):c||H(e,t,u,null,n,r,Rr(t,i),a,!1),d>0){if(16&d)q(u,t,h,m,n,r,i);else if(2&d&&h.class!==m.class&&s(u,"class",null,m.class,i),4&d&&s(u,"style",h.style,m.style,i),8&d){const o=t.dynamicProps;for(let t=0;t<o.length;t++){const a=o[t],c=h[a],d=m[a];(d!==c||"value"===a||l&&l(u,a))&&s(u,a,c,d,i,e.children,n,r,ee)}}1&d&&e.children!==t.children&&g(u,t.children)}else c||null!=p||q(u,t,h,m,n,r,i);((v=m.onVnodeUpdated)||f)&&$r((()=>{v&&pi(v,n,t,e),f&&so(t,e,n,"updated")}),r)},B=(e,t,n,o,r,i,a)=>{for(let s=0;s<t.length;s++){const l=e[s],c=t[s],u=l.el&&(l.type===jr||!Zr(l,c)||70&l.shapeFlag)?m(l.el):n;w(l,c,u,null,o,r,i,a,!0)}},q=(e,t,n,r,i,a,c)=>{if(n!==r){if(n!==o)for(const o in n)k(o)||o in r||s(e,o,n[o],null,c,t.children,i,a,ee);for(const o in r){if(k(o))continue;const u=r[o],d=n[o];(u!==d&&"value"!==o||l&&l(e,o))&&s(e,o,d,u,c,t.children,i,a,ee)}"value"in r&&s(e,"value",n.value,r.value,c)}},j=(e,t,o,r,i,a,s,l,c)=>{const u=t.el=e?e.el:d(""),p=t.anchor=e?e.anchor:d("");let{patchFlag:f,dynamicChildren:h,slotScopeIds:g}=t;g&&(l=l?l.concat(g):g),null==e?(n(u,o,r),n(p,o,r),O(t.children||[],o,p,i,a,s,l,c)):f>0&&64&f&&h&&e.dynamicChildren?(B(e.dynamicChildren,h,o,i,a,s,l),(null!=t.key||i&&t===i.subTree)&&Nr(e,t,!0)):H(e,t,o,p,i,a,s,l,c)},z=(e,t,n,o,r,i,a,s,l)=>{t.slotScopeIds=s,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,a,l):V(t,n,o,r,i,a,l):F(e,t,l)},V=(e,t,n,r,i,a,s)=>{const l=e.component=function(e,t,n){const r=e.type,i=(t?t.appContext:e.appContext)||fi,a={uid:hi++,vnode:e,type:r,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,scope:new qe(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Tr(r,i),emitsOptions:Rn(r,i),emit:null,emitted:null,propsDefaults:o,inheritAttrs:r.inheritAttrs,ctx:o,data:o,props:o,attrs:o,slots:o,refs:o,setupState:o,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};a.ctx={_:a},a.root=t?t.root:a,a.emit=$n.bind(null,a),a.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(a);return a}(e,r,i);if(So(e)&&(l.ctx.renderer=re),function(e,t=!1){t&&yi(t);const{props:n,children:o}=e.vnode,r=wi(e);(function(e,t,n,o=!1){const r={},i={};D(i,ei,1),e.propsDefaults=Object.create(null),wr(e,t,r,i);for(const a in e.propsOptions[0])a in r||(r[a]=void 0);n?e.props=o?r:Wt(r):e.type.props?e.props=r:e.props=i,e.attrs=i})(e,n,r,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=Kt(t),D(t,"_",n)):Ir(t,e.slots={})}else e.slots={},t&&Lr(e,t);D(e.slots,ei,1)})(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Zt(new Proxy(e.ctx,tr));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(it(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,r=bi(e);Xe();const i=hn(o,e,0,[e.props,n]);if(Ge(),r(),_(i)){if(i.then(_i,_i),t)return i.then((n=>{Ti(e,n,t)})).catch((t=>{mn(t,e,0)}));e.asyncDep=i}else Ti(e,i,t)}else Si(e,t)}(e,t):void 0;t&&yi(!1)}(l),l.asyncDep){if(i&&i.registerDep(l,U),!e.el){const e=l.subTree=ri(Vr);T(null,e,t,n)}}else U(l,e,t,n,i,a,s)},F=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:a,children:s,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!s||s&&s.$stable)||o!==a&&(o?!a||Wn(o,a,c):!!a);if(1024&l)return!0;if(16&l)return o?Wn(o,a,c):!!a;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(a[n]!==o[n]&&!Bn(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void W(o,t,n);o.next=t,function(e){const t=bn.indexOf(e);t>_n&&bn.splice(t,1)}(o.update),o.effect.dirty=!0,o.update()}else t.el=e.el,o.vnode=t},U=(e,t,n,o,r,a,s)=>{const l=()=>{if(e.isMounted){let{next:t,bu:n,u:o,parent:i,vnode:c}=e;{const n=qr(e);if(n)return t&&(t.el=c.el,W(e,t,s)),void n.asyncDep.then((()=>{e.isUnmounted||l()}))}let u,d=t;Br(e,!1),t?(t.el=c.el,W(e,t,s)):t=c,n&&$(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&pi(u,i,t,c),Br(e,!0);const p=Vn(e),f=e.subTree;e.subTree=p,w(f,p,m(f.el),te(f),e,r,a),t.el=p.el,null===d&&function({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,p.el),o&&$r(o,r),(u=t.props&&t.props.onVnodeUpdated)&&$r((()=>pi(u,i,t,c)),r)}else{let i;const{el:s,props:l}=t,{bm:c,m:u,parent:d}=e,p=wo(t);if(Br(e,!1),c&&$(c),!p&&(i=l&&l.onVnodeBeforeMount)&&pi(i,d,t),Br(e,!0),s&&ae){const n=()=>{e.subTree=Vn(e),ae(s,e.subTree,e,r,null)};p?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const i=e.subTree=Vn(e);w(null,i,n,o,e,r,a),t.el=i.el}if(u&&$r(u,r),!p&&(i=l&&l.onVnodeMounted)){const e=t;$r((()=>pi(i,d,e)),r)}(256&t.shapeFlag||d&&wo(d.vnode)&&256&d.vnode.shapeFlag)&&(e.ba&&Ro(e.ba),e.a&&$r(e.a,r)),e.isMounted=!0,t=n=o=null}},c=e.effect=new ze(l,i,(()=>En(u)),e.scope),u=e.update=()=>{c.dirty&&c.run()};u.id=e.uid,Br(e,!0),u()},W=(e,t,n)=>{t.component=e;const r=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:a}}=e,s=Kt(r),[l]=e.propsOptions;let c=!1;if(!(o||a>0)||16&a){let o;wr(e,t,r,i)&&(c=!0);for(const i in s)t&&(p(t,i)||(o=P(i))!==i&&p(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=xr(l,s,i,void 0,e,!0)):delete r[i]);if(i!==s)for(const e in i)t&&p(t,e)||(delete i[e],c=!0)}else if(8&a){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let a=n[o];if(Bn(e.emitsOptions,a))continue;const u=t[a];if(l)if(p(i,a))u!==i[a]&&(i[a]=u,c=!0);else{const t=A(a);r[t]=xr(l,s,t,u,e,!1)}else u!==i[a]&&(i[a]=u,c=!0)}}c&&at(e,"set","$attrs")}(e,t.props,r,n),((e,t,n)=>{const{vnode:r,slots:i}=e;let a=!0,s=o;if(32&r.shapeFlag){const e=t._;e?n&&1===e?a=!1:(c(i,t),n||1!==e||delete i._):(a=!t.$stable,Ir(t,i)),s=t}else t&&(Lr(e,t),s={default:1});if(a)for(const o in i)Ar(o)||null!=s[o]||delete i[o]})(e,t.children,n),Xe(),Mn(e),Ge()},H=(e,t,n,o,r,i,a,s,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:p,shapeFlag:f}=t;if(p>0){if(128&p)return void X(c,d,n,o,r,i,a,s,l);if(256&p)return void Y(c,d,n,o,r,i,a,s,l)}8&f?(16&u&&ee(c,r,i),d!==c&&g(n,d)):16&u?16&f?X(c,d,n,o,r,i,a,s,l):ee(c,r,i,!0):(8&u&&g(n,""),16&f&&O(d,n,o,r,i,a,s,l))},Y=(e,t,n,o,i,a,s,l,c)=>{t=t||r;const u=(e=e||r).length,d=t.length,p=Math.min(u,d);let f;for(f=0;f<p;f++){const o=t[f]=c?ci(t[f]):li(t[f]);w(e[f],o,n,null,i,a,s,l,c)}u>d?ee(e,i,a,!0,!1,p):O(t,n,o,i,a,s,l,c,p)},X=(e,t,n,o,i,a,s,l,c)=>{let u=0;const d=t.length;let p=e.length-1,f=d-1;for(;u<=p&&u<=f;){const o=e[u],r=t[u]=c?ci(t[u]):li(t[u]);if(!Zr(o,r))break;w(o,r,n,null,i,a,s,l,c),u++}for(;u<=p&&u<=f;){const o=e[p],r=t[f]=c?ci(t[f]):li(t[f]);if(!Zr(o,r))break;w(o,r,n,null,i,a,s,l,c),p--,f--}if(u>p){if(u<=f){const e=f+1,r=e<d?t[e].el:o;for(;u<=f;)w(null,t[u]=c?ci(t[u]):li(t[u]),n,r,i,a,s,l,c),u++}}else if(u>f)for(;u<=p;)J(e[u],i,a,!0),u++;else{const h=u,g=u,m=new Map;for(u=g;u<=f;u++){const e=t[u]=c?ci(t[u]):li(t[u]);null!=e.key&&m.set(e.key,u)}let v,y=0;const b=f-g+1;let _=!1,x=0;const T=new Array(b);for(u=0;u<b;u++)T[u]=0;for(u=h;u<=p;u++){const o=e[u];if(y>=b){J(o,i,a,!0);continue}let r;if(null!=o.key)r=m.get(o.key);else for(v=g;v<=f;v++)if(0===T[v-g]&&Zr(o,t[v])){r=v;break}void 0===r?J(o,i,a,!0):(T[r-g]=u+1,r>=x?x=r:_=!0,w(o,t[r],n,null,i,a,s,l,c),y++)}const S=_?function(e){const t=e.slice(),n=[0];let o,r,i,a,s;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,a=n.length-1;i<a;)s=i+a>>1,e[n[s]]<l?i=s+1:a=s;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,a=n[i-1];for(;i-- >0;)n[i]=a,a=t[a];return n}(T):r;for(v=S.length-1,u=b-1;u>=0;u--){const e=g+u,r=t[e],p=e+1<d?t[e+1].el:o;0===T[u]?w(null,r,n,p,i,a,s,l,c):_&&(v<0||u!==S[v]?G(r,n,p,2):v--)}}},G=(e,t,o,r,i=null)=>{const{el:a,type:s,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void G(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void s.move(e,t,o,re);if(s===jr){n(a,t,o);for(let e=0;e<c.length;e++)G(c[e],t,o,r);return void n(e.anchor,t,o)}if(s===Fr)return void C(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(a),n(a,t,o),$r((()=>l.enter(a)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,s=()=>n(a,t,o),c=()=>{e(a,(()=>{s(),i&&i()}))};r?r(a,s,c):c()}else n(a,t,o)},J=(e,t,n,o=!1,r=!1)=>{const{type:i,props:a,ref:s,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:p}=e;if(null!=s&&Or(s,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const f=1&u&&p,h=!wo(e);let g;if(h&&(g=a&&a.onVnodeBeforeUnmount)&&pi(g,t,e),6&u)Z(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);f&&so(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,re,o):c&&(i!==jr||d>0&&64&d)?ee(c,t,n,!1,!0):(i===jr&&384&d||!r&&16&u)&&ee(l,t,n),o&&Q(e)}(h&&(g=a&&a.onVnodeUnmounted)||f)&&$r((()=>{g&&pi(g,t,e),f&&so(e,null,t,"unmounted")}),n)},Q=e=>{const{type:t,el:n,anchor:o,transition:r}=e;if(t===jr)return void K(n,o);if(t===Fr)return void E(e);const i=()=>{a(n),r&&!r.persisted&&r.afterLeave&&r.afterLeave()};if(1&e.shapeFlag&&r&&!r.persisted){const{leave:t,delayLeave:o}=r,a=()=>t(n,i);o?o(e.el,i,a):a()}else i()},K=(e,t)=>{let n;for(;e!==t;)n=v(e),a(e),e=n;a(t)},Z=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:a,um:s}=e;o&&$(o),r.stop(),i&&(i.active=!1,J(a,e,t,n)),s&&$r(s,t),$r((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,r=!1,i=0)=>{for(let a=i;a<e.length;a++)J(e[a],t,n,o,r)},te=e=>6&e.shapeFlag?te(e.component.subTree):128&e.shapeFlag?e.suspense.next():v(e.anchor||e.el);let ne=!1;const oe=(e,t,n)=>{null==e?t._vnode&&J(t._vnode,null,null,!0):w(t._vnode||null,e,t,null,null,null,n),ne||(ne=!0,Mn(),Pn(),ne=!1),t._vnode=e},re={p:w,um:J,m:G,r:Q,mt:V,mc:O,pc:H,pbc:B,n:te,o:e};let ie,ae;t&&([ie,ae]=t(re));return{render:oe,hydrate:ie,createApp:vr(oe,ie)}}(e)}function Rr({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Br({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Nr(e,t,n=!1){const o=e.children,r=t.children;if(f(o)&&f(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=ci(r[i]),t.el=e.el),n||Nr(e,t)),t.type===zr&&(t.el=e.el)}}function qr(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:qr(t)}const jr=Symbol.for("v-fgt"),zr=Symbol.for("v-txt"),Vr=Symbol.for("v-cmt"),Fr=Symbol.for("v-stc"),Ur=[];let Wr=null;function Hr(e=!1){Ur.push(Wr=e?null:[])}let Yr=1;function Xr(e){Yr+=e}function Gr(e){return e.dynamicChildren=Yr>0?Wr||r:null,Ur.pop(),Wr=Ur[Ur.length-1]||null,Yr>0&&Wr&&Wr.push(e),e}function Jr(e,t,n,o,r,i){return Gr(oi(e,t,n,o,r,i,!0))}function Qr(e,t,n,o,r){return Gr(ri(e,t,n,o,r,!0))}function Kr(e){return!!e&&!0===e.__v_isVNode}function Zr(e,t){return e.type===t.type&&e.key===t.key}const ei="__vInternal",ti=({key:e})=>null!=e?e:null,ni=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?v(e)||an(e)||m(e)?{i:Nn,r:e,k:t,f:!!n}:e:null);function oi(e,t=null,n=null,o=0,r=null,i=(e===jr?0:1),a=!1,s=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ti(t),ref:t&&ni(t),scopeId:qn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Nn};return s?(ui(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=v(n)?8:16),Yr>0&&!a&&Wr&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&Wr.push(l),l}const ri=function(e,t=null,n=null,o=0,r=null,i=!1){e&&e!==Yn||(e=Vr);if(Kr(e)){const o=ii(e,t,!0);return n&&ui(o,n),Yr>0&&!i&&Wr&&(6&o.shapeFlag?Wr[Wr.indexOf(e)]=o:Wr.push(o)),o.patchFlag|=-2,o}a=e,m(a)&&"__vccOpts"in a&&(e=e.__vccOpts);var a;if(t){t=function(e){return e?Qt(e)||ei in e?c({},e):e:null}(t);let{class:e,style:n}=t;e&&!v(e)&&(t.class=se(e)),b(n)&&(Qt(n)&&!f(n)&&(n=c({},n)),t.style=ae(n))}const s=v(e)?1:Qn(e)?128:(e=>e.__isTeleport)(e)?64:b(e)?4:m(e)?2:0;return oi(e,t,n,o,r,s,i,!0)};function ii(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:a}=e,s=t?di(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:s,key:s&&ti(s),ref:t&&t.ref?n&&r?f(r)?r.concat(ni(t)):[r,ni(t)]:ni(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==jr?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ii(e.ssContent),ssFallback:e.ssFallback&&ii(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function ai(e=" ",t=0){return ri(zr,null,e,t)}function si(e="",t=!1){return t?(Hr(),Qr(Vr,null,e)):ri(Vr,null,e)}function li(e){return null==e||"boolean"==typeof e?ri(Vr):f(e)?ri(jr,null,e.slice()):"object"==typeof e?ci(e):ri(zr,null,String(e))}function ci(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:ii(e)}function ui(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(f(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),ui(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||ei in t?3===o&&Nn&&(1===Nn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Nn}}else m(t)?(t={default:t,_ctx:Nn},n=32):(t=String(t),64&o?(n=16,t=[ai(t)]):n=8);e.children=t,e.shapeFlag|=n}function di(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=se([t.class,o.class]));else if("style"===e)t.style=ae([t.style,o.style]);else if(s(e)){const n=t[e],r=o[e];!r||n===r||f(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function pi(e,t,n,o=null){gn(e,t,7,[n,o])}const fi=gr();let hi=0;let gi=null;const mi=()=>gi||Nn;let vi,yi;{const e=N(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach((t=>t(e))):o[0](e)}};vi=t("__VUE_INSTANCE_SETTERS__",(e=>gi=e)),yi=t("__VUE_SSR_SETTERS__",(e=>xi=e))}const bi=e=>{const t=gi;return vi(e),e.scope.on(),()=>{e.scope.off(),vi(t)}},_i=()=>{gi&&gi.scope.off(),vi(null)};function wi(e){return 4&e.vnode.shapeFlag}let xi=!1;function Ti(e,t,n){m(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:b(t)&&(e.setupState=fn(t)),Si(e,n)}function Si(e,t,n){const o=e.type;e.render||(e.render=o.render||i);{const t=bi(e);Xe();try{rr(e)}finally{Ge(),t()}}}function ki(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(fn(Zt(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Zo?Zo[n](e):void 0,has:(e,t)=>t in e||t in Zo}))}function Ci(e,t=!0){return m(e)?e.displayName||e.name:e.name||t&&e.__name}const Ei=(e,t)=>{const n=function(e,t,n=!1){let o,r;const a=m(e);return a?(o=e,r=i):(o=e.get,r=e.set),new nn(o,r,a||!r,n)}(e,0,xi);return n};function Ai(e,t,n){const o=arguments.length;return 2===o?b(t)&&!f(t)?Kr(t)?ri(e,null,[t]):ri(e,t):ri(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Kr(n)&&(n=[n]),ri(e,t,n))}const Mi="3.4.21",Pi="undefined"!=typeof document?document:null,Ii=Pi&&Pi.createElement("template"),Li={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r="svg"===t?Pi.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?Pi.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?Pi.createElement(e,{is:n}):Pi.createElement(e);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>Pi.createTextNode(e),createComment:e=>Pi.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Pi.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const a=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{Ii.innerHTML="svg"===o?`<svg>${e}</svg>`:"mathml"===o?`<math>${e}</math>`:e;const r=Ii.content;if("svg"===o||"mathml"===o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[a?a.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Oi="transition",$i=Symbol("_vtc"),Di=(e,{slots:t})=>Ai(fo,function(e){const t={};for(const c in e)c in Ri||(t[c]=e[c]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:a=`${n}-enter-active`,enterToClass:s=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:u=a,appearToClass:d=s,leaveFromClass:p=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,g=function(e){if(null==e)return null;if(b(e))return[qi(e.enter),qi(e.leave)];{const t=qi(e);return[t,t]}}(r),m=g&&g[0],v=g&&g[1],{onBeforeEnter:y,onEnter:_,onEnterCancelled:w,onLeave:x,onLeaveCancelled:T,onBeforeAppear:S=y,onAppear:k=_,onAppearCancelled:C=w}=t,E=(e,t,n)=>{zi(e,t?d:s),zi(e,t?u:a),n&&n()},A=(e,t)=>{e._isLeaving=!1,zi(e,p),zi(e,h),zi(e,f),t&&t()},M=e=>(t,n)=>{const r=e?k:_,a=()=>E(t,e,n);Bi(r,[t,a]),Vi((()=>{zi(t,e?l:i),ji(t,e?d:s),Ni(r)||Ui(t,o,m,a)}))};return c(t,{onBeforeEnter(e){Bi(y,[e]),ji(e,i),ji(e,a)},onBeforeAppear(e){Bi(S,[e]),ji(e,l),ji(e,u)},onEnter:M(!1),onAppear:M(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>A(e,t);ji(e,p),document.body.offsetHeight,ji(e,f),Vi((()=>{e._isLeaving&&(zi(e,p),ji(e,h),Ni(x)||Ui(e,o,v,n))})),Bi(x,[e,n])},onEnterCancelled(e){E(e,!1),Bi(w,[e])},onAppearCancelled(e){E(e,!0),Bi(C,[e])},onLeaveCancelled(e){A(e),Bi(T,[e])}})}(e),t);Di.displayName="Transition";const Ri={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};Di.props=c({},po,Ri);const Bi=(e,t=[])=>{f(e)?e.forEach((e=>e(...t))):e&&e(...t)},Ni=e=>!!e&&(f(e)?e.some((e=>e.length>1)):e.length>1);function qi(e){const t=(e=>{const t=v(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function ji(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e[$i]||(e[$i]=new Set)).add(t)}function zi(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const n=e[$i];n&&(n.delete(t),n.size||(e[$i]=void 0))}function Vi(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let Fi=0;function Ui(e,t,n,o){const r=e._endId=++Fi,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:a,timeout:s,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o("transitionDelay"),i=o("transitionDuration"),a=Wi(r,i),s=o("animationDelay"),l=o("animationDuration"),c=Wi(s,l);let u=null,d=0,p=0;t===Oi?a>0&&(u=Oi,d=a,p=i.length):"animation"===t?c>0&&(u="animation",d=c,p=l.length):(d=Math.max(a,c),u=d>0?a>c?Oi:"animation":null,p=u?u===Oi?i.length:l.length:0);const f=u===Oi&&/\b(transform|all)(,|$)/.test(o("transitionProperty").toString());return{type:u,timeout:d,propCount:p,hasTransform:f}}(e,t);if(!a)return o();const c=a+"end";let u=0;const d=()=>{e.removeEventListener(c,p),i()},p=t=>{t.target===e&&++u>=l&&d()};setTimeout((()=>{u<l&&d()}),s+1),e.addEventListener(c,p)}function Wi(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>Hi(t)+Hi(e[n]))))}function Hi(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}const Yi=Symbol("_vod"),Xi=Symbol("_vsh"),Gi={beforeMount(e,{value:t},{transition:n}){e[Yi]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Ji(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Ji(e,!0),o.enter(e)):o.leave(e,(()=>{Ji(e,!1)})):Ji(e,t))},beforeUnmount(e,{value:t}){Ji(e,t)}};function Ji(e,t){e.style.display=t?e[Yi]:"none",e[Xi]=!t}const Qi=Symbol(""),Ki=/(^|;)\s*display\s*:/;const Zi=/\s*!important$/;function ea(e,t,n){if(f(n))n.forEach((n=>ea(e,t,n)));else if(null==n&&(n=""),n=ua(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=na[t];if(n)return n;let o=A(t);if("filter"!==o&&o in e)return na[t]=o;o=I(o);for(let r=0;r<ta.length;r++){const n=ta[r]+o;if(n in e)return na[t]=n}return t}(e,t);Zi.test(n)?e.setProperty(P(o),n.replace(Zi,""),"important"):e[o]=n}}const ta=["Webkit","Moz","ms"],na={};const{unit:oa,unitRatio:ra,unitPrecision:ia}={unit:"rem",unitRatio:10/320,unitPrecision:5},aa=(sa=oa,la=ra,ca=ia,e=>e.replace(ye,((e,t)=>{if(!t)return e;if(1===la)return`${t}${sa}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*la,ca);return 0===n?"0":`${n}${sa}`})));var sa,la,ca;const ua=e=>v(e)?aa(e):e,da="http://www.w3.org/1999/xlink";const pa=Symbol("_vei");function fa(e,t,n,o,r=null){const i=e[pa]||(e[pa]={}),a=i[t];if(o&&a)a.value=o;else{const[n,s]=function(e){let t;if(ha.test(e)){let n;for(t={};n=e.match(ha);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):P(e.slice(2)),t]}(t);if(o){const a=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&f(i)){const n=va(e,i);for(let o=0;o<n.length;o++){const i=n[o];gn(i,t,5,i.__wwe?[e]:r(e))}}else gn(va(e,n.value),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=(()=>ga||(ma.then((()=>ga=0)),ga=Date.now()))(),n}(o,r);!function(e,t,n,o){e.addEventListener(t,n,o)}(e,n,a,s)}else a&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,a,s),i[t]=void 0)}}const ha=/(?:Once|Passive|Capture)$/;let ga=0;const ma=Promise.resolve();function va(e,t){if(f(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const ya=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const ba=["ctrl","shift","alt","meta"],_a={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>ba.some((n=>e[`${n}Key`]&&!t.includes(n)))},wa=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(n,...o)=>{for(let e=0;e<t.length;e++){const o=_a[t[e]];if(o&&o(n,t))return}return e(n,...o)})},xa=c({patchProp:(e,t,n,o,r,i,a,c,u)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,a=i[r],s=(e.__wxsProps||(e.__wxsProps={}))[r];if(s===a)return;e.__wxsProps[r]=a;const l=o.proxy;Cn((()=>{n(a,s,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,a);const d="svg"===r;"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e[$i];i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,d):"style"===t?function(e,t,n){const o=e.style,r=v(n);let i=!1;if(n&&!r){if(t)if(v(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&ea(o,t,"")}else for(const e in t)null==n[e]&&ea(o,e,"");for(const e in n)"display"===e&&(i=!0),ea(o,e,n[e])}else if(r){if(t!==n){const e=o[Qi];e&&(n+=";"+e),o.cssText=n,i=Ki.test(n)}}else t&&e.removeAttribute("style");Yi in e&&(e[Yi]=i?o.display:"",e[Xi]&&(o.display="none"));const{__wxsStyle:a}=e;if(a)for(const s in a)ea(o,s,a[s])}(e,n,o):s(t)?l(t)||fa(e,t,0,o,a):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&ya(t)&&m(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(ya(t)&&v(n))return!1;return t in e}(e,t,o,d))?function(e,t,n,o,r,i,a){if("innerHTML"===t||"textContent"===t)return o&&a(o,r,i),void(e[t]=null==n?"":n);const s=e.tagName;if("value"===t&&"PROGRESS"!==s&&!s.includes("-")){const o=null==n?"":n;return("OPTION"===s?e.getAttribute("value")||"":e.value)===o&&"_value"in e||(e.value=o),null==n&&e.removeAttribute(t),void(e._value=n)}let l=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=H(n):null==n&&"string"===o?(n="",l=!0):"number"===o&&(n=0,l=!0)}try{e[t]=n}catch(yC){}l&&e.removeAttribute(t)}(e,t,o,i,a,c,u):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o,r){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(da,t.slice(6,t.length)):e.setAttributeNS(da,t,n);else{const o=W(t);null==n||o&&!H(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,d))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},Li);let Ta;const Sa=(...e)=>{const t=(Ta||(Ta=Dr(xa))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(v(e)){return document.querySelector(e)}return e}
/*!
  * vue-router v4.3.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */(e);if(!o)return;const r=t._component;m(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const ka="undefined"!=typeof document;const Ca=Object.assign;function Ea(e,t){const n={};for(const o in t){const r=t[o];n[o]=Ma(r)?r.map(e):e(r)}return n}const Aa=()=>{},Ma=Array.isArray,Pa=/#/g,Ia=/&/g,La=/\//g,Oa=/=/g,$a=/\?/g,Da=/\+/g,Ra=/%5B/g,Ba=/%5D/g,Na=/%5E/g,qa=/%60/g,ja=/%7B/g,za=/%7C/g,Va=/%7D/g,Fa=/%20/g;function Ua(e){return encodeURI(""+e).replace(za,"|").replace(Ra,"[").replace(Ba,"]")}function Wa(e){return Ua(e).replace(Da,"%2B").replace(Fa,"+").replace(Pa,"%23").replace(Ia,"%26").replace(qa,"`").replace(ja,"{").replace(Va,"}").replace(Na,"^")}function Ha(e){return null==e?"":function(e){return Ua(e).replace(Pa,"%23").replace($a,"%3F")}(e).replace(La,"%2F")}function Ya(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Xa=/\/$/;function Ga(e,t,n="/"){let o,r={},i="",a="";const s=t.indexOf("#");let l=t.indexOf("?");return s<l&&s>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,s>-1?s:t.length),r=e(i)),s>-1&&(o=o||t.slice(0,s),a=t.slice(s,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let i,a,s=n.length-1;for(i=0;i<o.length;i++)if(a=o[i],"."!==a){if(".."!==a)break;s>1&&s--}return n.slice(0,s).join("/")+"/"+o.slice(i).join("/")}(null!=o?o:t,n),{fullPath:o+(i&&"?")+i+a,path:o,query:r,hash:Ya(a)}}function Ja(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Qa(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ka(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Za(e[n],t[n]))return!1;return!0}function Za(e,t){return Ma(e)?es(e,t):Ma(t)?es(t,e):e===t}function es(e,t){return Ma(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}var ts,ns,os,rs;function is(e){if(!e)if(ka){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Xa,"")}(ns=ts||(ts={})).pop="pop",ns.push="push",(rs=os||(os={})).back="back",rs.forward="forward",rs.unknown="";const as=/^[^#]+#/;function ss(e,t){return e.replace(as,"#")+t}const ls=()=>({left:window.scrollX,top:window.scrollY});function cs(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function us(e,t){return(history.state?history.state.position-t:-1)+e}const ds=new Map;function ps(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let t=r.includes(e.slice(i))?e.slice(i).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),Ja(n,"")}return Ja(n,e)+o+r}function fs(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?ls():null}}function hs(e){const{history:t,location:n}=window,o={value:ps(e,n)},r={value:t.state};function i(o,i,a){const s=e.indexOf("#"),l=s>-1?(n.host&&document.querySelector("base")?e:e.slice(s))+o:location.protocol+"//"+location.host+e+o;try{t[a?"replaceState":"pushState"](i,"",l),r.value=i}catch(c){console.error(c),n[a?"replace":"assign"](l)}}return r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const a=Ca({},r.value,t.state,{forward:e,scroll:ls()});i(a.current,a,!0),i(e,Ca({},fs(o.value,e,null),{position:a.position+1},n),!1),o.value=e},replace:function(e,n){i(e,Ca({},t.state,fs(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}function gs(e){const t=hs(e=is(e)),n=function(e,t,n,o){let r=[],i=[],a=null;const s=({state:i})=>{const s=ps(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=s,t.value=i,a&&a===l)return void(a=null);u=c?i.position-c.position:0}else o(s);r.forEach((e=>{e(n.value,l,{delta:u,type:ts.pop,direction:u?u>0?os.forward:os.back:os.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(Ca({},e.state,{scroll:ls()}),"")}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){a=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=Ca({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:ss.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function ms(e){return"string"==typeof e||"symbol"==typeof e}const vs={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},ys=Symbol("");var bs,_s;function ws(e,t){return Ca(new Error,{type:e,[ys]:!0},t)}function xs(e,t){return e instanceof Error&&ys in e&&(null==t||!!(e.type&t))}(_s=bs||(bs={}))[_s.aborted=4]="aborted",_s[_s.cancelled=8]="cancelled",_s[_s.duplicated=16]="duplicated";const Ts={sensitive:!1,strict:!1,start:!0,end:!0},Ss=/[.+*?^${}()[\]/\\]/g;function ks(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function Cs(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=ks(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(Es(o))return 1;if(Es(r))return-1}return r.length-o.length}function Es(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const As={type:0,value:""},Ms=/[a-zA-Z0-9_]/;function Ps(e,t,n){const o=function(e,t){const n=Ca({},Ts,t),o=[];let r=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let a=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(Ss,"\\$&"),a+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;i.push({name:e,repeatable:n,optional:c});const d=u||"[^/]+?";if("[^/]+?"!==d){a+=10;try{new RegExp(`(${d})`)}catch(s){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+s.message)}}let p=n?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(p=c&&l.length<2?`(?:/${p})`:"/"+p),c&&(p+="?"),r+=p,a+=20,c&&(a+=-8),n&&(a+=-20),".*"===d&&(a+=-50)}e.push(a)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const a=new RegExp(r,n.sensitive?"":"i");return{re:a,score:o,keys:i,parse:function(e){const t=e.match(a),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=i[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:a,optional:s}=e,l=i in t?t[i]:"";if(Ma(l)&&!a)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=Ma(l)?l.join("/"):l;if(!c){if(!s)throw new Error(`Missing required param "${i}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[As]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let i;function a(){i&&r.push(i),i=[]}let s,l=0,c="",u="";function d(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===s||"+"===s)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===s||"+"===s,optional:"*"===s||"?"===s})):t("Invalid state to consume buffer"),c="")}function p(){c+=s}for(;l<e.length;)if(s=e[l++],"\\"!==s||2===n)switch(n){case 0:"/"===s?(c&&d(),a()):":"===s?(d(),n=1):p();break;case 4:p(),n=o;break;case 1:"("===s?n=2:Ms.test(s)?p():(d(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--);break;case 2:")"===s?"\\"==u[u.length-1]?u=u.slice(0,-1)+s:n=3:u+=s;break;case 3:d(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),d(),a(),r}(e.path),n),r=Ca(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Is(e,t){const n=[],o=new Map;function r(e,n,o){const s=!o,l=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:Os(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}(e);l.aliasOf=o&&o.record;const c=Rs(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(Ca({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l}))}let d,p;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(d=Ps(t,n,c),o?o.alias.push(d):(p=p||d,p!==d&&p.alias.push(d),s&&e.name&&!$s(d)&&i(e.name)),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],d,o&&o.children[t])}o=o||d,(d.record.components&&Object.keys(d.record.components).length||d.record.name||d.record.redirect)&&a(d)}return p?()=>{i(p)}:Aa}function i(e){if(ms(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function a(e){let t=0;for(;t<n.length&&Cs(e,n[t])>=0&&(e.record.path!==n[t].record.path||!Bs(e,n[t]));)t++;n.splice(t,0,e),e.record.name&&!$s(e)&&o.set(e.record.name,e)}return t=Rs({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,i,a,s={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw ws(1,{location:e});a=r.record.name,s=Ca(Ls(t.params,r.keys.filter((e=>!e.optional)).concat(r.parent?r.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&Ls(e.params,r.keys.map((e=>e.name)))),i=r.stringify(s)}else if(null!=e.path)i=e.path,r=n.find((e=>e.re.test(i))),r&&(s=r.parse(i),a=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw ws(1,{location:e,currentLocation:t});a=r.record.name,s=Ca({},t.params,e.params),i=r.stringify(s)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:a,path:i,params:s,matched:l,meta:Ds(l)}},removeRoute:i,getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function Ls(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function Os(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"==typeof n?n[o]:n;return t}function $s(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Ds(e){return e.reduce(((e,t)=>Ca(e,t.meta)),{})}function Rs(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function Bs(e,t){return t.children.some((t=>t===e||Bs(e,t)))}function Ns(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Da," "),r=e.indexOf("="),i=Ya(r<0?e:e.slice(0,r)),a=r<0?null:Ya(e.slice(r+1));if(i in t){let e=t[i];Ma(e)||(e=t[i]=[e]),e.push(a)}else t[i]=a}return t}function qs(e){let t="";for(let n in e){const o=e[n];if(n=Wa(n).replace(Oa,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(Ma(o)?o.map((e=>e&&Wa(e))):[o&&Wa(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function js(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=Ma(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const zs=Symbol(""),Vs=Symbol(""),Fs=Symbol(""),Us=Symbol(""),Ws=Symbol("");function Hs(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function Ys(e,t,n,o,r,i=(e=>e())){const a=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((s,l)=>{const c=e=>{var i;!1===e?l(ws(4,{from:n,to:t})):e instanceof Error?l(e):"string"==typeof(i=e)||i&&"object"==typeof i?l(ws(2,{from:t,to:e})):(a&&o.enterCallbacks[r]===a&&"function"==typeof e&&a.push(e),s())},u=i((()=>e.call(o&&o.instances[r],t,n,c)));let d=Promise.resolve(u);e.length<3&&(d=d.then(c)),d.catch((e=>l(e)))}))}function Xs(e,t,n,o,r=(e=>e())){const i=[];for(const s of e)for(const e in s.components){let l=s.components[e];if("beforeRouteEnter"===t||s.instances[e])if("object"==typeof(a=l)||"displayName"in a||"props"in a||"__vccOpts"in a){const a=(l.__vccOpts||l)[t];a&&i.push(Ys(a,n,o,s,e,r))}else{let a=l();i.push((()=>a.then((i=>{if(!i)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${s.path}"`));const a=(l=i).__esModule||"Module"===l[Symbol.toStringTag]?i.default:i;var l;s.components[e]=a;const c=(a.__vccOpts||a)[t];return c&&Ys(c,n,o,s,e,r)()}))))}}var a;return i}function Gs(e){const t=_r(Fs),n=_r(Us),o=Ei((()=>t.resolve(dn(e.to)))),r=Ei((()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;const a=i.findIndex(Qa.bind(null,r));if(a>-1)return a;const s=Ks(e[t-2]);return t>1&&Ks(r)===s&&i[i.length-1].path!==s?i.findIndex(Qa.bind(null,e[t-2])):a})),i=Ei((()=>r.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!Ma(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}(n.params,o.value.params))),a=Ei((()=>r.value>-1&&r.value===n.matched.length-1&&Ka(n.params,o.value.params)));return{route:o,href:Ei((()=>o.value.href)),isActive:i,isExactActive:a,navigate:function(n={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)?t[dn(e.replace)?"replace":"push"](dn(e.to)).catch(Aa):Promise.resolve()}}}const Js=_o({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Gs,setup(e,{slots:t}){const n=Ut(Gs(e)),{options:o}=_r(Fs),r=Ei((()=>({[Zs(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[Zs(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:Ai("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),Qs=Js;function Ks(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Zs=(e,t,n)=>null!=e?e:null!=t?t:n;function el(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const tl=_o({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=_r(Ws),r=Ei((()=>e.route||o.value)),i=_r(Vs,0),a=Ei((()=>{let e=dn(i);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),s=Ei((()=>r.value.matched[a.value]));br(Vs,Ei((()=>a.value+1))),br(zs,s),br(Ws,r);const l=sn();return to((()=>[l.value,s.value,e.name]),(([e,t,n],[o,r,i])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&Qa(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,i=e.name,a=s.value,c=a&&a.components[i];if(!c)return el(n.default,{Component:c,route:o});const u=a.props[i],d=u?!0===u?o.params:"function"==typeof u?u(o):u:null,p=Ai(c,Ca({},d,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(a.instances[i]=null)},ref:l}));return el(n.default,{Component:p,route:o})||p}}});function nl(e){const t=Is(e.routes,e),n=e.parseQuery||Ns,o=e.stringifyQuery||qs,r=e.history,i=Hs(),a=Hs(),s=Hs(),l=ln(vs);let c=vs;ka&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Ea.bind(null,(e=>""+e)),d=Ea.bind(null,Ha),p=Ea.bind(null,Ya);function f(e,i){if(i=Ca({},i||l.value),"string"==typeof e){const o=Ga(n,e,i.path),a=t.resolve({path:o.path},i),s=r.createHref(o.fullPath);return Ca(o,a,{params:p(a.params),hash:Ya(o.hash),redirectedFrom:void 0,href:s})}let a;if(null!=e.path)a=Ca({},e,{path:Ga(n,e.path,i.path).path});else{const t=Ca({},e.params);for(const e in t)null==t[e]&&delete t[e];a=Ca({},e,{params:d(t)}),i.params=d(i.params)}const s=t.resolve(a,i),c=e.hash||"";s.params=u(p(s.params));const f=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,Ca({},e,{hash:(h=c,Ua(h).replace(ja,"{").replace(Va,"}").replace(Na,"^")),path:s.path}));var h;const g=r.createHref(f);return Ca({fullPath:f,hash:c,query:o===qs?js(e.query):e.query||{}},s,{redirectedFrom:void 0,href:g})}function h(e){return"string"==typeof e?Ga(n,e,l.value.path):Ca({},e)}function g(e,t){if(c!==e)return ws(8,{from:t,to:e})}function m(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=h(o):{path:o},o.params={}),Ca({query:e.query,hash:e.hash,params:null!=o.path?{}:e.params},o)}}function y(e,t){const n=c=f(e),r=l.value,i=e.state,a=e.force,s=!0===e.replace,u=v(n);if(u)return y(Ca(h(u),{state:"object"==typeof u?Ca({},i,u.state):i,force:a,replace:s}),t||n);const d=n;let p;return d.redirectedFrom=t,!a&&function(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&Qa(t.matched[o],n.matched[r])&&Ka(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(p=ws(16,{to:d,from:r}),I(r,r,!0,!1)),(p?Promise.resolve(p):w(d,r)).catch((e=>xs(e)?xs(e,2)?e:P(e):M(e,d,r))).then((e=>{if(e){if(xs(e,2))return y(Ca({replace:s},h(e.to),{state:"object"==typeof e.to?Ca({},i,e.to.state):i,force:a}),t||d)}else e=T(d,r,!0,s,i);return x(d,r,e),e}))}function b(e,t){const n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function _(e){const t=$.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function w(e,t){let n;const[o,r,s]=function(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let a=0;a<i;a++){const i=t.matched[a];i&&(e.matched.find((e=>Qa(e,i)))?o.push(i):n.push(i));const s=e.matched[a];s&&(t.matched.find((e=>Qa(e,s)))||r.push(s))}return[n,o,r]}(e,t);n=Xs(o.reverse(),"beforeRouteLeave",e,t);for(const i of o)i.leaveGuards.forEach((o=>{n.push(Ys(o,e,t))}));const l=b.bind(null,e,t);return n.push(l),R(n).then((()=>{n=[];for(const o of i.list())n.push(Ys(o,e,t));return n.push(l),R(n)})).then((()=>{n=Xs(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(Ys(o,e,t))}));return n.push(l),R(n)})).then((()=>{n=[];for(const o of s)if(o.beforeEnter)if(Ma(o.beforeEnter))for(const r of o.beforeEnter)n.push(Ys(r,e,t));else n.push(Ys(o.beforeEnter,e,t));return n.push(l),R(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=Xs(s,"beforeRouteEnter",e,t,_),n.push(l),R(n)))).then((()=>{n=[];for(const o of a.list())n.push(Ys(o,e,t));return n.push(l),R(n)})).catch((e=>xs(e,8)?e:Promise.reject(e)))}function x(e,t,n){s.list().forEach((o=>_((()=>o(e,t,n)))))}function T(e,t,n,o,i){const a=g(e,t);if(a)return a;const s=t===vs,c=ka?history.state:{};n&&(o||s?r.replace(e.fullPath,Ca({scroll:s&&c&&c.scroll},i)):r.push(e.fullPath,i)),l.value=e,I(e,t,n,s),P()}let S;function k(){S||(S=r.listen(((e,t,n)=>{if(!D.listening)return;const o=f(e),i=v(o);if(i)return void y(Ca(i,{replace:!0}),o).catch(Aa);c=o;const a=l.value;var s,u;ka&&(s=us(a.fullPath,n.delta),u=ls(),ds.set(s,u)),w(o,a).catch((e=>xs(e,12)?e:xs(e,2)?(y(e.to,o).then((e=>{xs(e,20)&&!n.delta&&n.type===ts.pop&&r.go(-1,!1)})).catch(Aa),Promise.reject()):(n.delta&&r.go(-n.delta,!1),M(e,o,a)))).then((e=>{(e=e||T(o,a,!1))&&(n.delta&&!xs(e,8)?r.go(-n.delta,!1):n.type===ts.pop&&xs(e,20)&&r.go(-1,!1)),x(o,a,e)})).catch(Aa)})))}let C,E=Hs(),A=Hs();function M(e,t,n){P(e);const o=A.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function P(e){return C||(C=!e,k(),E.list().forEach((([t,n])=>e?n(e):t())),E.reset()),e}function I(t,n,o,r){const{scrollBehavior:i}=e;if(!ka||!i)return Promise.resolve();const a=!o&&function(e){const t=ds.get(e);return ds.delete(e),t}(us(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return Cn().then((()=>i(t,n,a))).then((e=>e&&cs(e))).catch((e=>M(e,t,n)))}const L=e=>r.go(e);let O;const $=new Set,D={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return ms(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:f,options:e,push:m,replace:function(e){return m(Ca(h(e),{replace:!0}))},go:L,back:()=>L(-1),forward:()=>L(1),beforeEach:i.add,beforeResolve:a.add,afterEach:s.add,onError:A.add,isReady:function(){return C&&l.value!==vs?Promise.resolve():new Promise(((e,t)=>{E.add([e,t])}))},install(e){e.component("RouterLink",Qs),e.component("RouterView",tl),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>dn(l)}),ka&&!O&&l.value===vs&&(O=!0,m(r.location).catch((e=>{})));const t={};for(const o in vs)Object.defineProperty(t,o,{get:()=>l.value[o],enumerable:!0});e.provide(Fs,this),e.provide(Us,Wt(t)),e.provide(Ws,l);const n=e.unmount;$.add(e),e.unmount=function(){$.delete(e),$.size<1&&(c=vs,S&&S(),S=null,l.value=vs,O=!1,C=!1),n()}}};function R(e){return e.reduce(((e,t)=>e.then((()=>_(t)))),Promise.resolve())}return D}function ol(){return _r(Us)}const rl=["{","}"];const il=/^(?:\d)+/,al=/^(?:\w)+/;const sl=Object.prototype.hasOwnProperty,ll=(e,t)=>sl.call(e,t),cl=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=rl){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let a=e[r++];if(a===t){i&&o.push({type:"text",value:i}),i="";let t="";for(a=e[r++];void 0!==a&&a!==n;)t+=a,a=e[r++];const s=a===n,l=il.test(t)?"list":s&&al.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=a}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function ul(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class dl{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||cl,this.messages=n||{},this.setLocale(e||"en"),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=ul(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{ll(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=ul(t,this.messages))&&(o=this.messages[t]):n=t,ll(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function pl(e,t={},n,o){if("string"!=typeof e){const n=[t,e];e=n[0],t=n[1]}"string"!=typeof e&&(e="undefined"!=typeof uni&&uni.getLocale?uni.getLocale():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const r=new dl({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=uv().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}function fl(e,t){return e.indexOf(t[0])>-1}const hl=de((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let gl;function ml(e){return fl(e,ee)?bl().f(e,function(){const e=uni.getLocale(),t=__uniConfig.locales;return t[e]||t[__uniConfig.fallbackLocale]||t.en||{}}(),ee):e}function vl(e,t){if(1===t.length){if(e){const n=e=>v(e)&&fl(e,ee),o=t[0];let r=[];if(f(e)&&(r=e.filter((e=>n(e[o])))).length)return r;const i=e[t[0]];if(n(i))return e}return}const n=t.shift();return vl(e&&e[n],t)}function yl(e,t){const n=vl(e,t);if(!n)return!1;const o=t[t.length-1];if(f(n))n.forEach((e=>yl(e,[o])));else{let e=n[o];Object.defineProperty(n,o,{get:()=>ml(e),set(t){e=t}})}return!0}function bl(){if(!gl){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,gl=pl(e),hl()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>gl.add(e,__uniConfig.locales[e]))),gl.setLocale(e)}}return gl}function _l(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const wl=de((()=>{const e="uni.async.",t=["error"];bl().add("en",_l(e,t,["The connection timed out, click the screen to try again."]),!1),bl().add("es",_l(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),bl().add("fr",_l(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),bl().add("zh-Hans",_l(e,t,["连接服务器超时，点击屏幕重试"]),!1),bl().add("zh-Hant",_l(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),xl=de((()=>{const e="uni.showActionSheet.",t=["cancel"];bl().add("en",_l(e,t,["Cancel"]),!1),bl().add("es",_l(e,t,["Cancelar"]),!1),bl().add("fr",_l(e,t,["Annuler"]),!1),bl().add("zh-Hans",_l(e,t,["取消"]),!1),bl().add("zh-Hant",_l(e,t,["取消"]),!1)})),Tl=de((()=>{const e="uni.showToast.",t=["unpaired"];bl().add("en",_l(e,t,["Please note showToast must be paired with hideToast"]),!1),bl().add("es",_l(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),bl().add("fr",_l(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),bl().add("zh-Hans",_l(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),bl().add("zh-Hant",_l(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),Sl=de((()=>{const e="uni.showLoading.",t=["unpaired"];bl().add("en",_l(e,t,["Please note showLoading must be paired with hideLoading"]),!1),bl().add("es",_l(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),bl().add("fr",_l(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),bl().add("zh-Hans",_l(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),bl().add("zh-Hant",_l(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),kl=de((()=>{const e="uni.showModal.",t=["cancel","confirm"];bl().add("en",_l(e,t,["Cancel","OK"]),!1),bl().add("es",_l(e,t,["Cancelar","OK"]),!1),bl().add("fr",_l(e,t,["Annuler","OK"]),!1),bl().add("zh-Hans",_l(e,t,["取消","确定"]),!1),bl().add("zh-Hant",_l(e,t,["取消","確定"]),!1)})),Cl=de((()=>{const e="uni.chooseFile.",t=["notUserActivation"];bl().add("en",_l(e,t,["File chooser dialog can only be shown with a user activation"]),!1),bl().add("es",_l(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),bl().add("fr",_l(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),bl().add("zh-Hans",_l(e,t,["文件选择器对话框只能在由用户激活时显示"]),!1),bl().add("zh-Hant",_l(e,t,["文件選擇器對話框只能在由用戶激活時顯示"]),!1)})),El=de((()=>{const e="uni.setClipboardData.",t=["success","fail"];bl().add("en",_l(e,t,["Content copied","Copy failed, please copy manually"]),!1),bl().add("es",_l(e,t,["Contenido copiado","Error al copiar, copie manualmente"]),!1),bl().add("fr",_l(e,t,["Contenu copié","Échec de la copie, copiez manuellement"]),!1),bl().add("zh-Hans",_l(e,t,["内容已复制","复制失败，请手动复制"]),!1),bl().add("zh-Hant",_l(e,t,["內容已復制","復制失敗，請手動復製"]),!1)})),Al=de((()=>{const e="uni.getClipboardData.",t=["fail"];bl().add("en",_l(e,t,["Reading failed, please paste manually"]),!1),bl().add("es",_l(e,t,["Error de lectura, pegue manualmente"]),!1),bl().add("fr",_l(e,t,["Échec de la lecture, veuillez coller manuellement"]),!1),bl().add("zh-Hans",_l(e,t,["读取失败，请手动粘贴"]),!1),bl().add("zh-Hant",_l(e,t,["讀取失敗，請手動粘貼"]),!1)})),Ml=de((()=>{const e="uni.picker.",t=["done","cancel"];bl().add("en",_l(e,t,["Done","Cancel"]),!1),bl().add("es",_l(e,t,["OK","Cancelar"]),!1),bl().add("fr",_l(e,t,["OK","Annuler"]),!1),bl().add("zh-Hans",_l(e,t,["完成","取消"]),!1),bl().add("zh-Hant",_l(e,t,["完成","取消"]),!1)})),Pl=de((()=>{const e="uni.video.",t=["danmu","volume"];bl().add("en",_l(e,t,["Danmu","Volume"]),!1),bl().add("es",_l(e,t,["Danmu","Volumen"]),!1),bl().add("fr",_l(e,t,["Danmu","Le Volume"]),!1),bl().add("zh-Hans",_l(e,t,["弹幕","音量"]),!1),bl().add("zh-Hant",_l(e,t,["彈幕","音量"]),!1)})),Il=de((()=>{const e="uni.chooseLocation.",t=["search","cancel"];bl().add("en",_l(e,t,["Find Place","Cancel"]),!1),bl().add("es",_l(e,t,["Encontrar","Cancelar"]),!1),bl().add("fr",_l(e,t,["Trouve","Annuler"]),!1),bl().add("zh-Hans",_l(e,t,["搜索地点","取消"]),!1),bl().add("zh-Hant",_l(e,t,["搜索地點","取消"]),!1)}));function Ll(e){const t=new Oe;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}let Ol=1;const $l=Object.create(null);function Dl(e,t){return e+"."+t}function Rl(e,t,n){t=Dl(e,t),$l[t]||($l[t]=n)}function Bl({id:e,name:t,args:n},o){t=Dl(o,t);const r=t=>{e&&Sx.publishHandler("invokeViewApi."+e,t)},i=$l[t];i?i(n,r):r({})}const Nl=c(Ll("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=Sx,i=n?Ol++:0;n&&o("invokeServiceApi."+i,n,!0),r("invokeServiceApi",{id:i,name:e,args:t})}}),ql=be(!0);let jl;function zl(){jl&&(clearTimeout(jl),jl=null)}let Vl=0,Fl=0;function Ul(e){if(zl(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];Vl=t,Fl=n,jl=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}function Wl(e){if(!jl)return;if(1!==e.touches.length)return zl();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Vl)>10||Math.abs(n-Fl)>10?zl():void 0}function Hl(e,t){const n=Number(e);return isNaN(n)?t:n}function Yl(){const e=__uniConfig.globalStyle||{},t=Hl(e.rpxCalcMaxDeviceWidth,960),n=Hl(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function Xl(){Yl(),me(),window.addEventListener("touchstart",Ul,ql),window.addEventListener("touchmove",Wl,ql),window.addEventListener("touchend",zl,ql),window.addEventListener("touchcancel",zl,ql)}function Gl(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Jl,Ql,Kl=["top","left","right","bottom"],Zl={};function ec(){return Ql="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function tc(){if(Ql="string"==typeof Ql?Ql:ec()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(yC){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),Kl.forEach((function(e){a(o,e)})),document.body.appendChild(o),i(),Jl=!0}else Kl.forEach((function(e){Zl[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function a(e,n){var o=document.createElement("div"),a=document.createElement("div"),s=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Ql+"(safe-area-inset-"+n+")"};r(o,c),r(a,c),r(s,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(s),a.appendChild(l),e.appendChild(o),e.appendChild(a),i((function(){o.scrollTop=a.scrollTop=1e4;var e=o.scrollTop,r=a.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=a.scrollTop=1e4,e=o.scrollTop,r=a.scrollTop,function(e){oc.length||setTimeout((function(){var e={};oc.forEach((function(t){e[t]=Zl[t]})),oc.length=0,rc.forEach((function(t){t(e)}))}),0);oc.push(e)}(n))}o.addEventListener("scroll",i,t),a.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(Zl,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function nc(e){return Jl||tc(),Zl[e]}var oc=[];var rc=[];const ic=Gl({get support(){return 0!=("string"==typeof Ql?Ql:ec()).length},get top(){return nc("top")},get left(){return nc("left")},get right(){return nc("right")},get bottom(){return nc("bottom")},onChange:function(e){ec()&&(Jl||tc(),"function"==typeof e&&rc.push(e))},offChange:function(e){var t=rc.indexOf(e);t>=0&&rc.splice(t,1)}}),ac=wa((()=>{}),["prevent"]),sc=wa((e=>{}),["stop"]);function lc(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function cc(){const e=lc(document.documentElement.style,"--window-top");return e?e+ic.top:0}function uc(){const e=document.documentElement.style,t=cc(),n=lc(e,"--window-bottom"),o=lc(e,"--window-left"),r=lc(e,"--window-right"),i=lc(e,"--top-window-height");return{top:t,bottom:n?n+ic.bottom:0,left:o?o+ic.left:0,right:r?r+ic.right:0,topWindowHeight:i||0}}function dc(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function pc(e){return dc(e)}function fc(e){return Symbol(e)}function hc(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function gc(e,t=!1){if(t)return function(e){if(!hc(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>uni.upx2px(parseFloat(t))+"px"))}(e);if(v(e)){const t=parseInt(e)||0;return hc(e)?uni.upx2px(t):t}return e}const mc="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",vc="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z",yc="M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z",bc="M17.25 16.156l7.375-7.313q0.281-0.281 0.281-0.641t-0.281-0.641q-0.25-0.25-0.625-0.25t-0.625 0.25l-7.375 7.344-7.313-7.344q-0.25-0.25-0.625-0.25t-0.625 0.25q-0.281 0.25-0.281 0.625t0.281 0.625l7.313 7.344-7.375 7.344q-0.281 0.25-0.281 0.625t0.281 0.625q0.125 0.125 0.281 0.188t0.344 0.063q0.156 0 0.328-0.063t0.297-0.188l7.375-7.344 7.375 7.406q0.125 0.156 0.297 0.219t0.328 0.063q0.188 0 0.344-0.078t0.281-0.203q0.281-0.25 0.281-0.609t-0.281-0.641l-7.375-7.406z",_c="M31.562 4.9966666659375q0.435 0.399 0.435 0.87 0.036 0.58-0.399 0.98l-18.61 19.917q-0.145 0.145-0.327 0.217-0.073 0.037-0.145 0.11-0.254 0.035-0.472 0.035-0.29 0-0.544-0.036l-0.145-0.072q-0.109-0.073-0.217-0.182l-0.11-0.072L0.363 16.2786666659375q-0.327-0.399-0.363-0.907 0-0.544 0.363-1.016 0.435-0.326 0.961-0.362 0.527-0.036 0.962 0.362l9.722 9.542L29.712 5.0326666659375q0.399-0.363 0.943-0.363 0.544-0.036 0.907 0.327z";function wc(e,t="#000",n=27){return ri("svg",{width:n,height:n,viewBox:"0 0 32 32"},[ri("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function xc(){{const{$pageInstance:e}=mi();return e&&e.proxy.$page.id}}function Tc(e){const t=oe(e);if(t.$page)return t.$page.id;if(t.$){const{$pageInstance:e}=t.$;return e&&e.proxy.$page.id}}function Sc(){const e=qm(),t=e.length;if(t)return e[t-1]}function kc(){const e=Sc();if(e)return e.$page.meta}function Cc(){const e=kc();return e?e.id:-1}function Ec(){const e=Sc();if(e)return e.$vm}const Ac=["navigationBar","pullToRefresh"];function Mc(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=c({id:t},n,e);Ac.forEach((t=>{o[t]=c({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function Pc(e,t,n){if(v(e))n=t,t=e,e=Ec();else if("number"==typeof e){const t=qm().find((t=>t.$page.id===e));e=t?t.$vm:Ec()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function Ic(e){e.preventDefault()}let Lc,Oc=0;function $c({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const a=()=>{function a(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,a=Math.abs(e-Oc)>n;return!i||r&&!a?(!i&&r&&(r=!1),!1):(Oc=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(a()||(Lc=setTimeout(a,300))),o=!1};return function(){clearTimeout(Lc),o||requestAnimationFrame(a),o=!0}}function Dc(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return Dc(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),ce(i.concat(n).join("/"))}function Rc(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}class Bc{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(ie(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&ie(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=zc(this.$el.querySelector(e));return t?Nc(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=zc(n[o]);e&&t.push(Nc(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||v(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:P(n);(v(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(v(e)&&(e=F(e)),T(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];m(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&Sx.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function Nc(e,t=!0){if(t&&e&&(e=re(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Bc(e)),e.$el.__wxsComponentDescriptor}function qc(e,t){return Nc(e,t)}function jc(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>qc(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=re(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,qc(r,!1)]}}function zc(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function Vc(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e;let a,s;a=_e(t?r:function(e){for(;e&&0!==e.tagName.indexOf("UNI-");)e=e.parentElement;return e}(r)),s=_e(i);const l={type:n,timeStamp:o,target:a,detail:{},currentTarget:s};return e._stopped&&(l._stopped=!0),e.type.startsWith("touch")&&(l.touches=e.touches,l.changedTouches=e.changedTouches),function(e,t){c(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(l,e),l}function Fc(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function Uc(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:a,clientX:s,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:a-t,clientX:s,clientY:l-t,force:c||0})}return n}const Wc=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=0!==o.tagName.indexOf("UNI-");if(r)return jc(e,t,n,!1)||[e];const i=Vc(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=cc();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[Fc(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=cc();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Fc(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=cc();i.touches=Uc(e.touches,t),i.changedTouches=Uc(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(i,t,{get:()=>e[t]})}))}return jc(i,t,n)||[i]},createNativeEvent:Vc},Symbol.toStringTag,{value:"Module"});function Hc(e){!function(e){const t=e.globalProperties;c(t,Wc),t.$gcd=qc}(e._context.config)}let Yc=1;function Xc(e){return(e||Cc())+".invokeViewApi"}const Gc=c(Ll("view"),{invokeOnCallback:(e,t)=>Cx.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=Cx,a=o?Yc++:0;o&&r("invokeViewApi."+a,o,!0),i(Xc(n),{id:a,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:a}=Cx,s=Yc++,l="invokeViewApi."+s;return r(l,n),a(Xc(o),{id:s,name:e,args:t},o),()=>{i(l)}}});function Jc(e){Pc(Sc(),"onResize",e),Cx.invokeOnCallback("onWindowResize",e)}function Qc(e){const t=Sc();Pc(uv(),"onShow",e),Pc(t,"onShow")}function Kc(){Pc(uv(),"onHide"),Pc(Sc(),"onHide")}const Zc=["onPageScroll","onReachBottom"];function eu(){Zc.forEach((e=>Cx.subscribe(e,function(e){return(t,n)=>{Pc(parseInt(n),e,t)}}(e))))}function tu(){!function(){const{on:e}=Cx;e("onResize",Jc),e("onAppEnterForeground",Qc),e("onAppEnterBackground",Kc)}(),eu()}function nu(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new Ce(this.$page.id)),e.eventChannel}}function ou(e){e._context.config.globalProperties.getOpenerEventChannel=nu}function ru(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function iu(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${uni.upx2px(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function au(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],a=t.option.transition,s=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,a=e.option,s=a.transition,l={},c=[];return i.forEach((e=>{let i=e.type,a=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?a=a.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(a=a.map(iu)),n.indexOf(i)>=0&&(a.length=1),c.push(`${i}(${a.join(",")})`);else if(o.concat(r).includes(a[0])){i=a[0];const e=a[1];l[i]=r.includes(i)?iu(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${s.duration}ms ${s.timingFunction} ${s.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=a.transformOrigin,l}(t);Object.keys(s).forEach((t=>{e.$el.style[t]=s[t]})),n+=1,n<r&&setTimeout(i,a.duration+a.delay)}setTimeout((()=>{i()}),0)}const su={props:["animation"],watch:{animation:{deep:!0,handler(){au(this)}}},mounted(){au(this)}},lu=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(su),cu(e)},cu=e=>(e.__reserved=!0,e.compatConfig={MODE:3},_o(e));function uu(e){return e.__wwe=!0,e}function du(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){let r;return r=_e(n),{type:o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const pu={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function fu(e){const t=sn(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function a(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function s(){r=!1,t.value&&i()}function l(){s(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:uu((function(e){e.touches.length>1||a(e)})),onMousedown:uu((function(e){r||(a(e),window.addEventListener("mouseup",l))})),onTouchend:uu((function(){s()})),onMouseup:uu((function(){r&&l()})),onTouchcancel:uu((function(){r=!1,t.value=!1,clearTimeout(n)}))}}}function hu(e,t){return v(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}const gu=fc("uf"),mu=lu({name:"Form",emits:["submit","reset"],setup(e,{slots:t,emit:n}){const o=sn(null);return function(e){const t=[];br(gu,{addField(e){t.push(e)},removeField(e){t.splice(t.indexOf(e),1)},submit(n){e("submit",n,{value:t.reduce(((e,t)=>{if(t.submit){const[n,o]=t.submit();n&&(e[n]=o)}return e}),Object.create(null))})},reset(n){t.forEach((e=>e.reset&&e.reset())),e("reset",n)}})}(du(o,n)),()=>ri("uni-form",{ref:o},[ri("span",null,[t.default&&t.default()])],512)}});const vu=fc("ul");function yu(e,t,n){const o=xc();n&&!e||T(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&Sx.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?Sx.on(r,t[r]):e&&Sx.on(`uni-${r}-${o}-${e}`,t[r])}))}function bu(e,t,n){const o=xc();n&&!e||T(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&Sx.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?Sx.off(r,t[r]):e&&Sx.off(`uni-${r}-${o}-${e}`,t[r])}))}const _u=lu({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=sn(null),o=_r(gu,!1),{hovering:r,binding:i}=fu(e),a=uu(((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;})),s=_r(vu,!1);return s&&(s.addHandler(a),Fo((()=>{s.removeHandler(a)}))),function(e,t){yu(e.id,t),to((()=>e.id),((e,n)=>{bu(n,t,!0),yu(e,t,!0)})),Uo((()=>{bu(e.id,t)}))}(e,{"label-click":a}),()=>{const o=e.hoverClass,s=hu(e,"disabled"),l=hu(e,"loading"),c=hu(e,"plain"),u=o&&"none"!==o;return ri("uni-button",di({ref:n,onClick:a,id:e.id,class:u&&r.value?o:""},u&&i,s,l,c),[t.default&&t.default()],16,["onClick","id"])}}});function wu(e){return e.$el}function xu(e){const{base:t}=__uniConfig.router;return 0===ce(e).indexOf(t)?ce(e):t+e}function Tu(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0!==e.indexOf("./")||!e.includes("/static/")&&0!==e.indexOf("./"+(n||"assets")+"/")||(e=e.slice(1))),0===e.indexOf("/")){if(0!==e.indexOf("//"))return xu(e.slice(1));e="https:"+e}if(te.test(e)||ne.test(e)||0===e.indexOf("blob:"))return e;const o=qm();return o.length?xu(Dc(o[o.length-1].$page.route,e).slice(1)):e}const Su=navigator.userAgent,ku=/android/i.test(Su),Cu=/iphone|ipad|ipod/i.test(Su),Eu=Su.match(/Windows NT ([\d|\d.\d]*)/i),Au=/Macintosh|Mac/i.test(Su),Mu=/Linux|X11/i.test(Su),Pu=Au&&navigator.maxTouchPoints>0;function Iu(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function Lu(e){return e&&90===Math.abs(window.orientation)}function Ou(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function $u(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function Du(e,t,n,o){Cx.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function Ru(e,t){const n={},{top:o,topWindowHeight:r}=uc();if(t.node){const t=e.tagName.split("-")[1];t&&(n.node=e.querySelector(t))}if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=ve(e)),t.rect||t.size){const i=e.getBoundingClientRect();t.rect&&(n.left=i.left,n.right=i.right,n.top=i.top-o-r,n.bottom=i.bottom-o-r),t.size&&(n.width=i.width,n.height=i.height)}if(f(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){const t=e.children[0].children[0];n.scrollLeft=t.scrollLeft,n.scrollTop=t.scrollTop,n.scrollHeight=t.scrollHeight,n.scrollWidth=t.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(f(t.computedStyle)){const o=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=o[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function Bu(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){const t=this.parentElement.querySelectorAll(e);let n=t.length;for(;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}function Nu(e,t,n){const o=[];t.forEach((({component:t,selector:n,single:r,fields:i})=>{null===t?o.push(function(e){const t={};if(e.id&&(t.id=""),e.dataset&&(t.dataset={}),e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0),e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight),e.scrollOffset){const e=document.documentElement,n=document.body;t.scrollLeft=e.scrollLeft||n.scrollLeft||0,t.scrollTop=e.scrollTop||n.scrollTop||0,t.scrollHeight=e.scrollHeight||n.scrollHeight||0,t.scrollWidth=e.scrollWidth||n.scrollWidth||0}return t}(i)):o.push(function(e,t,n,o,r){const i=function(e,t){return e?e.$el:t.$el}(t,e),a=i.parentElement;if(!a)return o?null:[];const{nodeType:s}=i,l=3===s||8===s;if(o){const e=l?a.querySelector(n):Bu(i,n)?i:i.querySelector(n);return e?Ru(e,r):null}{let e=[];const t=(l?a:i).querySelectorAll(n);return t&&t.length&&[].forEach.call(t,(t=>{e.push(Ru(t,r))})),!l&&Bu(i,n)&&e.unshift(Ru(i,r)),e}}(e,t,n,r,i))})),n(o)}var qu="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ju=function(){const e=new Uint8Array(256);for(var t=0;t<qu.length;t++)e[qu.charCodeAt(t)]=t;return e}();const zu=["original","compressed"],Vu=["album","camera"],Fu=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function Uu(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function Wu(e,t){return!f(e)||0===e.length||e.find((e=>-1===t.indexOf(e)))?t:e}function Hu(e){return function(){try{return e.apply(e,arguments)}catch(yC){console.error(yC)}}}let Yu=1;const Xu={};function Gu(e,t,n,o=!1){return Xu[e]={name:t,keepAlive:o,callback:n},e}function Ju(e,t,n){if("number"==typeof e){const o=Xu[e];if(o)return o.keepAlive||delete Xu[e],o.callback(t,n)}return t}function Qu(e){for(const t in Xu)if(Xu[t].name===e)return!0;return!1}const Ku="success",Zu="fail",ed="complete";function td(e,t={},{beforeAll:n,beforeSuccess:o}={}){T(t)||(t={});const{success:r,fail:i,complete:a}=function(e){const t={};for(const n in e){const o=e[n];m(o)&&(t[n]=Hu(o),delete e[n])}return t}(t),s=m(r),l=m(i),c=m(a),u=Yu++;return Gu(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),m(n)&&n(u),u.errMsg===e+":ok"?(m(o)&&o(u,t),s&&r(u)):l&&i(u),c&&a(u)})),u}const nd="success",od="fail",rd="complete",id={},ad={};function sd(e,t){return function(n){return e(n,t)||n}}function ld(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(sd(i,n));else{const e=i(t,n);if(_(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function cd(e,t={}){return[nd,od,rd].forEach((n=>{const o=e[n];if(!f(o))return;const r=t[n];t[n]=function(e){ld(o,e,t).then((e=>m(r)&&r(e)||e))}})),t}function ud(e,t){const n=[];f(id.returnValue)&&n.push(...id.returnValue);const o=ad[e];return o&&f(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function dd(e){const t=Object.create(null);Object.keys(id).forEach((e=>{"returnValue"!==e&&(t[e]=id[e].slice())}));const n=ad[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function pd(e,t,n,o){const r=dd(e);if(r&&Object.keys(r).length){if(f(r.invoke)){return ld(r.invoke,n).then((n=>t(cd(dd(e),n),...o)))}return t(cd(r,n),...o)}return t(n,...o)}function fd(e,t){return(n={},...o)=>function(e){return!(!T(e)||![Ku,Zu,ed].find((t=>m(e[t]))))}(n)?ud(e,pd(e,t,n,o)):ud(e,new Promise(((r,i)=>{pd(e,t,c(n,{success:r,fail:i}),o)})))}function hd(e,t,n,o={}){const r=t+":fail"+(n?" "+n:"");return delete o.errCode,Ju(e,c({errMsg:r},o))}function gd(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(v(e))return e}const r=function(e,t){const n=e[0];if(!t||!t.formatArgs||!T(t.formatArgs)&&T(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],a=o[t];if(m(a)){const o=a(e[0][t],n);if(v(o))return o}else p(n,t)||(n[t]=a)}}(t,o);if(r)return r}function md(e){if(!m(e))throw new Error('Invalid args: type check failed for args "callback". Expected Function')}function vd(e,t,n){return o=>{md(o);const r=gd(0,[o],0,n);if(r)throw new Error(r);const i=!Qu(e);!function(e,t){Gu(Yu++,e,t,!0)}(e,o),i&&(!function(e){Cx.on("api."+e,(t=>{for(const n in Xu){const o=Xu[n];o.name===e&&o.callback(t)}}))}(e),t())}}function yd(e,t,n){return o=>{md(o);const r=gd(0,[o],0,n);if(r)throw new Error(r);!function(e,t){for(const n in Xu){const o=Xu[n];o.callback===t&&o.name===e&&delete Xu[n]}}(e=e.replace("off","on"),o);Qu(e)||(!function(e){Cx.off("api."+e)}(e),t())}}function bd(e,t,n,o){return n=>{const r=td(e,n,o),i=gd(0,[n],0,o);return i?hd(r,e,i):t(n,{resolve:t=>function(e,t,n){return Ju(e,c(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>hd(r,e,function(e){return!e||v(e)?e:e.stack?(console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function _d(e,t,n){return vd(e,t,n)}function wd(e,t,n){return yd(e,t,n)}function xd(e,t,n,o){return fd(e,bd(e,t,0,o))}function Td(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=gd(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function Sd(e,t,n,o){return fd(e,function(e,t,n,o){return bd(e,t,0,o)}(e,t,0,o))}function kd(e){return`method 'uni.${e}' not supported`}function Cd(e){return()=>{console.error(kd(e))}}const Ed=Cd;function Ad(e){return(t,{reject:n})=>n(kd(e))}const Md=Td(0,(e=>function(e){var t,n,o,r,i,a=.75*e.length,s=e.length,l=0;"="===e[e.length-1]&&(a--,"="===e[e.length-2]&&a--);var c=new ArrayBuffer(a),u=new Uint8Array(c);for(t=0;t<s;t+=4)n=ju[e.charCodeAt(t)],o=ju[e.charCodeAt(t+1)],r=ju[e.charCodeAt(t+2)],i=ju[e.charCodeAt(t+3)],u[l++]=n<<2|o>>4,u[l++]=(15&o)<<4|r>>2,u[l++]=(3&r)<<6|63&i;return c}(e))),Pd=Td(0,(e=>function(e){var t,n=new Uint8Array(e),o=n.length,r="";for(t=0;t<o;t+=3)r+=qu[n[t]>>2],r+=qu[(3&n[t])<<4|n[t+1]>>4],r+=qu[(15&n[t+1])<<2|n[t+2]>>6],r+=qu[63&n[t+2]];return o%3==2?r=r.substring(0,r.length-1)+"=":o%3==1&&(r=r.substring(0,r.length-2)+"=="),r}(e)));let Id=!1,Ld=0,Od=0,$d=960,Dd=375,Rd=750;function Bd(){const{platform:e,pixelRatio:t,windowWidth:n}=function(){const e=Iu(),t=$u(Ou(e,Lu(e)));return{platform:Cu?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();Ld=n,Od=t,Id="ios"===e}function Nd(e,t){const n=Number(e);return isNaN(n)?t:n}const qd=Td(0,((e,t)=>{if(0===Ld&&(Bd(),function(){const e=__uniConfig.globalStyle||{};$d=Nd(e.rpxCalcMaxDeviceWidth,960),Dd=Nd(e.rpxCalcBaseDeviceWidth,375),Rd=Nd(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||Ld;n=e===Rd||n<=$d?n:Dd;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==Od&&Id?.5:1),e<0?-o:o}));function jd(e,t){Object.keys(t).forEach((n=>{m(t[n])&&(e[n]=function(e,t){const n=t?e?e.concat(t):f(t)?t:[t]:e;return n?function(e){const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))}))}function zd(e,t){e&&t&&Object.keys(t).forEach((n=>{const o=e[n],r=t[n];f(o)&&m(r)&&u(o,r)}))}const Vd=Td(0,((e,t)=>{v(e)&&T(t)?jd(ad[e]||(ad[e]={}),t):T(e)&&jd(id,e)})),Fd=Td(0,((e,t)=>{v(e)?T(t)?zd(ad[e],t):delete ad[e]:T(e)&&zd(id,e)})),Ud=new Oe,Wd=Td(0,((e,t)=>(Ud.on(e,t),()=>Ud.off(e,t)))),Hd=Td(0,((e,t)=>(Ud.once(e,t),()=>Ud.off(e,t)))),Yd=Td(0,((e,t)=>{e?(f(e)||(e=[e]),e.forEach((e=>Ud.off(e,t)))):Ud.e={}})),Xd=Td(0,((e,...t)=>{Ud.emit(e,...t)})),Gd=[.5,.8,1,1.25,1.5,2];class Jd{constructor(e,t){this.id=e,this.pageId=t}play(){Du(this.id,this.pageId,"play")}pause(){Du(this.id,this.pageId,"pause")}stop(){Du(this.id,this.pageId,"stop")}seek(e){Du(this.id,this.pageId,"seek",{position:e})}sendDanmu(e){Du(this.id,this.pageId,"sendDanmu",e)}playbackRate(e){~Gd.indexOf(e)||(e=1),Du(this.id,this.pageId,"playbackRate",{rate:e})}requestFullScreen(e={}){Du(this.id,this.pageId,"requestFullScreen",e)}exitFullScreen(){Du(this.id,this.pageId,"exitFullScreen")}showStatusBar(){Du(this.id,this.pageId,"showStatusBar")}hideStatusBar(){Du(this.id,this.pageId,"hideStatusBar")}}const Qd=Td(0,((e,t)=>new Jd(e,Tc(t||Ec())))),Kd=(e,t,n,o)=>{!function(e,t,n,o,r){Cx.invokeViewMethod("map."+e,{type:n,data:o},t,r)}(e,t,n,o,(e=>{o&&((e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)})(o,e)}))};class Zd{constructor(e,t){this.id=e,this.pageId=t}getCenterLocation(e){Kd(this.id,this.pageId,"getCenterLocation",e)}moveToLocation(e){Kd(this.id,this.pageId,"moveToLocation",e)}getScale(e){Kd(this.id,this.pageId,"getScale",e)}getRegion(e){Kd(this.id,this.pageId,"getRegion",e)}includePoints(e){Kd(this.id,this.pageId,"includePoints",e)}translateMarker(e){Kd(this.id,this.pageId,"translateMarker",e)}$getAppMap(){}addCustomLayer(e){Kd(this.id,this.pageId,"addCustomLayer",e)}removeCustomLayer(e){Kd(this.id,this.pageId,"removeCustomLayer",e)}addGroundOverlay(e){Kd(this.id,this.pageId,"addGroundOverlay",e)}removeGroundOverlay(e){Kd(this.id,this.pageId,"removeGroundOverlay",e)}updateGroundOverlay(e){Kd(this.id,this.pageId,"updateGroundOverlay",e)}initMarkerCluster(e){Kd(this.id,this.pageId,"initMarkerCluster",e)}addMarkers(e){Kd(this.id,this.pageId,"addMarkers",e)}removeMarkers(e){Kd(this.id,this.pageId,"removeMarkers",e)}moveAlong(e){Kd(this.id,this.pageId,"moveAlong",e)}setLocMarkerIcon(e){Kd(this.id,this.pageId,"setLocMarkerIcon",e)}openMapApp(e){Kd(this.id,this.pageId,"openMapApp",e)}on(e,t){Kd(this.id,this.pageId,"on",{name:e,callback:t})}}const ep=Td(0,((e,t)=>new Zd(e,Tc(t||Ec()))));function tp(e,t){return function(n,o){n?o[e]=Math.round(n):void 0!==t&&(o[e]=t)}}const np=tp("width"),op=tp("height"),rp={formatArgs:{x:tp("x"),y:tp("y"),width:np,height:op}},ip={canvasId:{type:String,required:!0},x:{type:Number,required:!0},y:{type:Number,required:!0},width:{type:Number,required:!0},height:{type:Number,required:!0}},ap=rp,sp=(Uint8ClampedArray,{PNG:"png",JPG:"jpg",JPEG:"jpg"}),lp={formatArgs:{x:tp("x",0),y:tp("y",0),width:np,height:op,destWidth:tp("destWidth"),destHeight:tp("destHeight"),fileType(e,t){e=(e||"").toUpperCase();let n=sp[e];n||(n=sp.PNG),t.fileType=n},quality(e,t){t.quality=e&&e>0&&e<1?e:1}}};function cp(e,t,n,o,r){Cx.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,(e=>{r&&r(e)}))}var up=["scale","rotate","translate","setTransform","transform"],dp=["drawImage","fillText","fill","stroke","fillRect","strokeRect","clearRect","strokeText"],pp=["setFillStyle","setTextAlign","setStrokeStyle","setGlobalAlpha","setShadow","setFontSize","setLineCap","setLineJoin","setLineWidth","setMiterLimit","setTextBaseline","setLineDash"];const fp={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function hp(e){let t=null;if(null!=(t=/^#([0-9|A-F|a-f]{6})$/.exec(e=e||"#000000"))){return[parseInt(t[1].slice(0,2),16),parseInt(t[1].slice(2,4),16),parseInt(t[1].slice(4),16),255]}if(null!=(t=/^#([0-9|A-F|a-f]{3})$/.exec(e))){let e=t[1].slice(0,1),n=t[1].slice(1,2),o=t[1].slice(2,3);return e=parseInt(e+e,16),n=parseInt(n+n,16),o=parseInt(o+o,16),[e,n,o,255]}if(null!=(t=/^rgb\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e){return Math.min(255,parseInt(e.trim()))})).concat(255);if(null!=(t=/^rgba\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e,t){return 3===t?Math.floor(255*parseFloat(e.trim())):Math.min(255,parseInt(e.trim()))}));var n=e.toLowerCase();if(p(fp,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(fp[n]);const e=parseInt(t[1].slice(0,2),16),o=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4,6),16);let i=parseInt(t[1].slice(6,8),16);return i=i>=0?i:255,[e,o,r,i]}return console.error("unsupported color:"+e),[0,0,0,255]}class gp{constructor(e,t){this.type=e,this.data=t,this.colorStop=[]}addColorStop(e,t){this.colorStop.push([e,hp(t)])}}class mp{constructor(e,t){this.type="pattern",this.data=e,this.colorStop=t}}class vp{constructor(e){this.width=e}}class yp{constructor(e,t){this.id=e,this.pageId=t,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}setFillStyle(e){console.log("initCanvasContextProperty implemented.")}setStrokeStyle(e){console.log("initCanvasContextProperty implemented.")}setShadow(e,t,n,o){console.log("initCanvasContextProperty implemented.")}addColorStop(e,t){console.log("initCanvasContextProperty implemented.")}setLineWidth(e){console.log("initCanvasContextProperty implemented.")}setLineCap(e){console.log("initCanvasContextProperty implemented.")}setLineJoin(e){console.log("initCanvasContextProperty implemented.")}setLineDash(e,t){console.log("initCanvasContextProperty implemented.")}setMiterLimit(e){console.log("initCanvasContextProperty implemented.")}fillRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}strokeRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}clearRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}fill(){console.log("initCanvasContextProperty implemented.")}stroke(){console.log("initCanvasContextProperty implemented.")}scale(e,t){console.log("initCanvasContextProperty implemented.")}rotate(e){console.log("initCanvasContextProperty implemented.")}translate(e,t){console.log("initCanvasContextProperty implemented.")}setFontSize(e){console.log("initCanvasContextProperty implemented.")}fillText(e,t,n,o){console.log("initCanvasContextProperty implemented.")}setTextAlign(e){console.log("initCanvasContextProperty implemented.")}setTextBaseline(e){console.log("initCanvasContextProperty implemented.")}drawImage(e,t,n,o,r,i,a,s,l){console.log("initCanvasContextProperty implemented.")}setGlobalAlpha(e){console.log("initCanvasContextProperty implemented.")}strokeText(e,t,n,o){console.log("initCanvasContextProperty implemented.")}setTransform(e,t,n,o,r,i){console.log("initCanvasContextProperty implemented.")}draw(e=!1,t){var n=[...this.actions];this.actions=[],this.path=[],cp(this.id,this.pageId,"actionsChanged",{actions:n,reserve:e},t)}createLinearGradient(e,t,n,o){return new gp("linear",[e,t,n,o])}createCircularGradient(e,t,n){return new gp("radial",[e,t,n])}createPattern(e,t){if(void 0===t)console.error("Failed to execute 'createPattern' on 'CanvasContext': 2 arguments required, but only 1 present.");else{if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(t)<0))return new mp(e,t);console.error("Failed to execute 'createPattern' on 'CanvasContext': The provided type ('"+t+"') is not one of 'repeat', 'no-repeat', 'repeat-x', or 'repeat-y'.")}}measureText(e,t){let n=0;return n=function(e,t){const n=document.createElement("canvas").getContext("2d");return n.font=t,n.measureText(e).width||0}(e,this.state.font),new vp(n)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(e,t){this.path.push({method:"moveTo",data:[e,t]}),this.subpath=[[e,t]]}lineTo(e,t){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[e,t]}):this.path.push({method:"lineTo",data:[e,t]}),this.subpath.push([e,t])}quadraticCurveTo(e,t,n,o){this.path.push({method:"quadraticCurveTo",data:[e,t,n,o]}),this.subpath.push([n,o])}bezierCurveTo(e,t,n,o,r,i){this.path.push({method:"bezierCurveTo",data:[e,t,n,o,r,i]}),this.subpath.push([r,i])}arc(e,t,n,o,r,i=!1){this.path.push({method:"arc",data:[e,t,n,o,r,i]}),this.subpath.push([e,t])}rect(e,t,n,o){this.path.push({method:"rect",data:[e,t,n,o]}),this.subpath=[[e,t]]}arcTo(e,t,n,o,r){this.path.push({method:"arcTo",data:[e,t,n,o,r]}),this.subpath.push([n,o])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var e=[...this.actions];return this.clearActions(),e}set lineDashOffset(e){this.actions.push({method:"setLineDashOffset",data:[e]})}set globalCompositeOperation(e){this.actions.push({method:"setGlobalCompositeOperation",data:[e]})}set shadowBlur(e){this.actions.push({method:"setShadowBlur",data:[e]})}set shadowColor(e){this.actions.push({method:"setShadowColor",data:[e]})}set shadowOffsetX(e){this.actions.push({method:"setShadowOffsetX",data:[e]})}set shadowOffsetY(e){this.actions.push({method:"setShadowOffsetY",data:[e]})}set font(e){var t=this;this.state.font=e;var n=e.match(/^(([\w\-]+\s)*)(\d+r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var o=n[1].trim().split(/\s/),r=parseFloat(n[3]),i=n[7],a=[];o.forEach((function(e,n){["italic","oblique","normal"].indexOf(e)>-1?(a.push({method:"setFontStyle",data:[e]}),t.state.fontStyle=e):["bold","normal"].indexOf(e)>-1?(a.push({method:"setFontWeight",data:[e]}),t.state.fontWeight=e):0===n?(a.push({method:"setFontStyle",data:["normal"]}),t.state.fontStyle="normal"):1===n&&s()})),1===o.length&&s(),o=a.map((function(e){return e.data[0]})).join(" "),this.state.fontSize=r,this.state.fontFamily=i,this.actions.push({method:"setFont",data:[`${o} ${r}px ${i}`]})}else console.warn("Failed to set 'font' on 'CanvasContext': invalid format.");function s(){a.push({method:"setFontWeight",data:["normal"]}),t.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(e){this.setFillStyle(e)}set strokeStyle(e){this.setStrokeStyle(e)}set globalAlpha(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:"setGlobalAlpha",data:[e]})}set textAlign(e){this.actions.push({method:"setTextAlign",data:[e]})}set lineCap(e){this.actions.push({method:"setLineCap",data:[e]})}set lineJoin(e){this.actions.push({method:"setLineJoin",data:[e]})}set lineWidth(e){this.actions.push({method:"setLineWidth",data:[e]})}set miterLimit(e){this.actions.push({method:"setMiterLimit",data:[e]})}set textBaseline(e){this.actions.push({method:"setTextBaseline",data:[e]})}}const bp=de((()=>{[...up,...dp].forEach((function(e){yp.prototype[e]=function(e){switch(e){case"fill":case"stroke":return function(){this.actions.push({method:e+"Path",data:[...this.path]})};case"fillRect":return function(e,t,n,o){this.actions.push({method:"fillPath",data:[{method:"rect",data:[e,t,n,o]}]})};case"strokeRect":return function(e,t,n,o){this.actions.push({method:"strokePath",data:[{method:"rect",data:[e,t,n,o]}]})};case"fillText":case"strokeText":return function(t,n,o,r){var i=[t.toString(),n,o];"number"==typeof r&&i.push(r),this.actions.push({method:e,data:i})};case"drawImage":return function(t,n,o,r,i,a,s,l,c){var u;function d(e){return"number"==typeof e}void 0===c&&(a=n,s=o,l=r,c=i,n=void 0,o=void 0,r=void 0,i=void 0),u=d(n)&&d(o)&&d(r)&&d(i)?[t,a,s,l,c,n,o,r,i]:d(l)&&d(c)?[t,a,s,l,c]:[t,a,s],this.actions.push({method:e,data:u})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)})),pp.forEach((function(e){yp.prototype[e]=function(e){switch(e){case"setFillStyle":case"setStrokeStyle":return function(t){"object"!=typeof t?this.actions.push({method:e,data:["normal",hp(t)]}):this.actions.push({method:e,data:[t.type,t.data,t.colorStop]})};case"setGlobalAlpha":return function(t){t=Math.floor(255*parseFloat(t)),this.actions.push({method:e,data:[t]})};case"setShadow":return function(t,n,o,r){r=hp(r),this.actions.push({method:e,data:[t,n,o,r]}),this.state.shadowBlur=o,this.state.shadowColor=r,this.state.shadowOffsetX=t,this.state.shadowOffsetY=n};case"setLineDash":return function(t,n){t=t||[0,0],n=n||0,this.actions.push({method:e,data:[t,n]}),this.state.lineDash=t};case"setFontSize":return function(t){this.state.font=this.state.font.replace(/\d+\.?\d*px/,t+"px"),this.state.fontSize=t,this.actions.push({method:e,data:[t]})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)}))})),_p=Td(0,((e,t)=>{if(bp(),t)return new yp(e,Tc(t));const n=Tc(Ec());if(n)return new yp(e,n);Cx.emit("onError","createCanvasContext:fail")})),wp=Sd("canvasGetImageData",(({canvasId:e,x:t,y:n,width:o,height:r},{resolve:i,reject:a})=>{const s=Tc(Ec());s?cp(e,s,"getImageData",{x:t,y:n,width:o,height:r},(function(e){if(e.errMsg&&-1!==e.errMsg.indexOf("fail"))return void a("",e);let t=e.data;t&&t.length&&(e.data=new Uint8ClampedArray(t)),delete e.compressed,i(e)})):a()}),0,rp),xp=Sd("canvasPutImageData",(({canvasId:e,data:t,x:n,y:o,width:r,height:i},{resolve:a,reject:s})=>{var l=Tc(Ec());if(!l)return void s();t=Array.prototype.slice.call(t),cp(e,l,"putImageData",{data:t,x:n,y:o,width:r,height:i,compressed:void 0},(e=>{e.errMsg&&-1!==e.errMsg.indexOf("fail")?s():a(e)}))}),0,ap),Tp=Sd("canvasToTempFilePath",(({x:e=0,y:t=0,width:n,height:o,destWidth:r,destHeight:i,canvasId:a,fileType:s,quality:l},{resolve:c,reject:u})=>{var d=Tc(Ec());if(!d)return void u();cp(a,d,"toTempFilePath",{x:e,y:t,width:n,height:o,destWidth:r,destHeight:i,fileType:s,quality:l,dirname:"/canvas"},(e=>{e.errMsg&&-1!==e.errMsg.indexOf("fail")?u("",e):c(e)}))}),0,lp),Sp=["onCanplay","onPlay","onPause","onStop","onEnded","onTimeUpdate","onError","onWaiting","onSeeking","onSeeked"],kp=["offCanplay","offPlay","offPause","offStop","offEnded","offTimeUpdate","offError","offWaiting","offSeeking","offSeeked"],Cp={thresholds:[0],initialRatio:0,observeAll:!1},Ep=["top","right","bottom","left"];let Ap=1;function Mp(e={}){return Ep.map((t=>`${Number(e[t])||0}px`)).join(" ")}class Pp{constructor(e,t){this._pageId=Tc(e),this._component=e,this._options=c({},Cp,t)}relativeTo(e,t){return this._options.relativeToSelector=e,this._options.rootMargin=Mp(t),this}relativeToViewport(e){return this._options.relativeToSelector=void 0,this._options.rootMargin=Mp(e),this}observe(e,t){m(t)&&(this._options.selector=e,this._reqId=Ap++,function({reqId:e,component:t,options:n,callback:o},r){const i=wu(t);(i.__io||(i.__io={}))[e]=function(e,t,n){!function(){if("object"!=typeof window)return;if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)return void("isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}}));function e(e){try{return e.defaultView&&e.defaultView.frameElement||null}catch(yC){return null}}var t=function(t){for(var n=window.document,o=e(n);o;)o=e(n=o.ownerDocument);return n}(),n=[],o=null,r=null;function i(e){this.time=e.time,this.target=e.target,this.rootBounds=h(e.rootBounds),this.boundingClientRect=h(e.boundingClientRect),this.intersectionRect=h(e.intersectionRect||f()),this.isIntersecting=!!e.intersectionRect;var t=this.boundingClientRect,n=t.width*t.height,o=this.intersectionRect,r=o.width*o.height;this.intersectionRatio=n?Number((r/n).toFixed(4)):this.isIntersecting?1:0}function a(e,t){var n=t||{};if("function"!=typeof e)throw new Error("callback must be a function");if(n.root&&1!=n.root.nodeType&&9!=n.root.nodeType)throw new Error("root must be a Document or Element");this._checkForIntersections=l(this._checkForIntersections.bind(this),this.THROTTLE_TIMEOUT),this._callback=e,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(n.rootMargin),this.thresholds=this._initThresholds(n.threshold),this.root=n.root||null,this.rootMargin=this._rootMarginValues.map((function(e){return e.value+e.unit})).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}function s(){return window.performance&&performance.now&&performance.now()}function l(e,t){var n=null;return function(){n||(n=setTimeout((function(){e(),n=null}),t))}}function c(e,t,n,o){"function"==typeof e.addEventListener?e.addEventListener(t,n,o||!1):"function"==typeof e.attachEvent&&e.attachEvent("on"+t,n)}function u(e,t,n,o){"function"==typeof e.removeEventListener?e.removeEventListener(t,n,o||!1):"function"==typeof e.detatchEvent&&e.detatchEvent("on"+t,n)}function d(e,t){var n=Math.max(e.top,t.top),o=Math.min(e.bottom,t.bottom),r=Math.max(e.left,t.left),i=Math.min(e.right,t.right),a=i-r,s=o-n;return a>=0&&s>=0&&{top:n,bottom:o,left:r,right:i,width:a,height:s}||null}function p(e){var t;try{t=e.getBoundingClientRect()}catch(n){}return t?(t.width&&t.height||(t={top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:t.right-t.left,height:t.bottom-t.top}),t):f()}function f(){return{top:0,bottom:0,left:0,right:0,width:0,height:0}}function h(e){return!e||"x"in e?e:{top:e.top,y:e.top,bottom:e.bottom,left:e.left,x:e.left,right:e.right,width:e.width,height:e.height}}function g(e,t){var n=t.top-e.top,o=t.left-e.left;return{top:n,left:o,height:t.height,width:t.width,bottom:n+t.height,right:o+t.width}}function m(e,t){for(var n=t;n;){if(n==e)return!0;n=v(n)}return!1}function v(n){var o=n.parentNode;return 9==n.nodeType&&n!=t?e(n):(o&&o.assignedSlot&&(o=o.assignedSlot.parentNode),o&&11==o.nodeType&&o.host?o.host:o)}function y(e){return e&&9===e.nodeType}a.prototype.THROTTLE_TIMEOUT=100,a.prototype.POLL_INTERVAL=null,a.prototype.USE_MUTATION_OBSERVER=!0,a._setupCrossOriginUpdater=function(){return o||(o=function(e,t){r=e&&t?g(e,t):f(),n.forEach((function(e){e._checkForIntersections()}))}),o},a._resetCrossOriginUpdater=function(){o=null,r=null},a.prototype.observe=function(e){if(!this._observationTargets.some((function(t){return t.element==e}))){if(!e||1!=e.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:e,entry:null}),this._monitorIntersections(e.ownerDocument),this._checkForIntersections()}},a.prototype.unobserve=function(e){this._observationTargets=this._observationTargets.filter((function(t){return t.element!=e})),this._unmonitorIntersections(e.ownerDocument),0==this._observationTargets.length&&this._unregisterInstance()},a.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},a.prototype.takeRecords=function(){var e=this._queuedEntries.slice();return this._queuedEntries=[],e},a.prototype._initThresholds=function(e){var t=e||[0];return Array.isArray(t)||(t=[t]),t.sort().filter((function(e,t,n){if("number"!=typeof e||isNaN(e)||e<0||e>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return e!==n[t-1]}))},a.prototype._parseRootMargin=function(e){var t=(e||"0px").split(/\s+/).map((function(e){var t=/^(-?\d*\.?\d+)(px|%)$/.exec(e);if(!t)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(t[1]),unit:t[2]}}));return t[1]=t[1]||t[0],t[2]=t[2]||t[0],t[3]=t[3]||t[1],t},a.prototype._monitorIntersections=function(n){var o=n.defaultView;if(o&&-1==this._monitoringDocuments.indexOf(n)){var r=this._checkForIntersections,i=null,a=null;this.POLL_INTERVAL?i=o.setInterval(r,this.POLL_INTERVAL):(c(o,"resize",r,!0),c(n,"scroll",r,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in o&&(a=new o.MutationObserver(r)).observe(n,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this._monitoringDocuments.push(n),this._monitoringUnsubscribes.push((function(){var e=n.defaultView;e&&(i&&e.clearInterval(i),u(e,"resize",r,!0)),u(n,"scroll",r,!0),a&&a.disconnect()}));var s=this.root&&(this.root.ownerDocument||this.root)||t;if(n!=s){var l=e(n);l&&this._monitorIntersections(l.ownerDocument)}}},a.prototype._unmonitorIntersections=function(n){var o=this._monitoringDocuments.indexOf(n);if(-1!=o){var r=this.root&&(this.root.ownerDocument||this.root)||t;if(!this._observationTargets.some((function(t){var o=t.element.ownerDocument;if(o==n)return!0;for(;o&&o!=r;){var i=e(o);if((o=i&&i.ownerDocument)==n)return!0}return!1}))){var i=this._monitoringUnsubscribes[o];if(this._monitoringDocuments.splice(o,1),this._monitoringUnsubscribes.splice(o,1),i(),n!=r){var a=e(n);a&&this._unmonitorIntersections(a.ownerDocument)}}}},a.prototype._unmonitorAllIntersections=function(){var e=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0,this._monitoringUnsubscribes.length=0;for(var t=0;t<e.length;t++)e[t]()},a.prototype._checkForIntersections=function(){if(this.root||!o||r){var e=this._rootIsInDom(),t=e?this._getRootRect():f();this._observationTargets.forEach((function(n){var r=n.element,a=p(r),l=this._rootContainsTarget(r),c=n.entry,u=e&&l&&this._computeTargetAndRootIntersection(r,a,t),d=null;this._rootContainsTarget(r)?o&&!this.root||(d=t):d=f();var h=n.entry=new i({time:s(),target:r,boundingClientRect:a,rootBounds:d,intersectionRect:u});c?e&&l?this._hasCrossedThreshold(c,h)&&this._queuedEntries.push(h):c&&c.isIntersecting&&this._queuedEntries.push(h):this._queuedEntries.push(h)}),this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)}},a.prototype._computeTargetAndRootIntersection=function(e,n,i){if("none"!=window.getComputedStyle(e).display){for(var a=n,s=v(e),l=!1;!l&&s;){var c=null,u=1==s.nodeType?window.getComputedStyle(s):{};if("none"==u.display)return null;if(s==this.root||9==s.nodeType)if(l=!0,s==this.root||s==t)o&&!this.root?!r||0==r.width&&0==r.height?(s=null,c=null,a=null):c=r:c=i;else{var f=v(s),h=f&&p(f),m=f&&this._computeTargetAndRootIntersection(f,h,i);h&&m?(s=f,c=g(h,m)):(s=null,a=null)}else{var y=s.ownerDocument;s!=y.body&&s!=y.documentElement&&"visible"!=u.overflow&&(c=p(s))}if(c&&(a=d(c,a)),!a)break;s=s&&v(s)}return a}},a.prototype._getRootRect=function(){var e;if(this.root&&!y(this.root))e=p(this.root);else{var n=y(this.root)?this.root:t,o=n.documentElement,r=n.body;e={top:0,left:0,right:o.clientWidth||r.clientWidth,width:o.clientWidth||r.clientWidth,bottom:o.clientHeight||r.clientHeight,height:o.clientHeight||r.clientHeight}}return this._expandRectByRootMargin(e)},a.prototype._expandRectByRootMargin=function(e){var t=this._rootMarginValues.map((function(t,n){return"px"==t.unit?t.value:t.value*(n%2?e.width:e.height)/100})),n={top:e.top-t[0],right:e.right+t[1],bottom:e.bottom+t[2],left:e.left-t[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},a.prototype._hasCrossedThreshold=function(e,t){var n=e&&e.isIntersecting?e.intersectionRatio||0:-1,o=t.isIntersecting?t.intersectionRatio||0:-1;if(n!==o)for(var r=0;r<this.thresholds.length;r++){var i=this.thresholds[r];if(i==n||i==o||i<n!=i<o)return!0}},a.prototype._rootIsInDom=function(){return!this.root||m(t,this.root)},a.prototype._rootContainsTarget=function(e){var n=this.root&&(this.root.ownerDocument||this.root)||t;return m(n,e)&&(!this.root||n==e.ownerDocument)},a.prototype._registerInstance=function(){n.indexOf(this)<0&&n.push(this)},a.prototype._unregisterInstance=function(){var e=n.indexOf(this);-1!=e&&n.splice(e,1)},window.IntersectionObserver=a,window.IntersectionObserverEntry=i}();const o=t.relativeToSelector?e.querySelector(t.relativeToSelector):null,r=new IntersectionObserver((e=>{e.forEach((e=>{n({intersectionRatio:dh(e),intersectionRect:uh(e.intersectionRect),boundingClientRect:uh(e.boundingClientRect),relativeRect:uh(e.rootBounds),time:Date.now(),dataset:ve(e.target),id:e.target.id})}))}),{root:o,rootMargin:t.rootMargin,threshold:t.thresholds});if(t.observeAll){r.USE_MUTATION_OBSERVER=!0;const n=e.querySelectorAll(t.selector);for(let e=0;e<n.length;e++)r.observe(n[e])}else{r.USE_MUTATION_OBSERVER=!1;const n=e.querySelector(t.selector);n?r.observe(n):console.warn(`Node ${t.selector} is not found. Intersection observer will not trigger.`)}return r}(i,n,o)}({reqId:this._reqId,component:this._component,options:this._options,callback:t},this._pageId))}disconnect(){this._reqId&&function({reqId:e,component:t},n){const o=wu(t),r=o.__io&&o.__io[e];r&&(r.disconnect(),delete o.__io[e])}({reqId:this._reqId,component:this._component},this._pageId)}}const Ip=Td(0,((e,t)=>((e=oe(e))&&!Tc(e)&&(t=e,e=null),new Pp(e||Ec(),t))));let Lp=1;class Op{constructor(e){this._pageId=e.$page&&e.$page.id,this._component=e}observe(e,t){m(t)&&(this._reqId=Lp++,function({reqId:e,component:t,options:n,callback:o},r){const i=ph[e]=window.matchMedia(function(e){const t=[],n=["width","minWidth","maxWidth","height","minHeight","maxHeight","orientation"];for(const o of n)"orientation"!==o&&e[o]&&Number(e[o]>=0)&&t.push(`(${hh(o)}: ${Number(e[o])}px)`),"orientation"===o&&e[o]&&t.push(`(${hh(o)}: ${e[o]})`);return t.join(" and ")}(n)),a=fh[e]=e=>o(e.matches);a(i),i.addListener(a)}({reqId:this._reqId,component:this._component,options:e,callback:t},this._pageId))}disconnect(){this._reqId&&function({reqId:e,component:t},n){const o=fh[e],r=ph[e];r&&(r.removeListener(o),delete fh[e],delete ph[e])}({reqId:this._reqId,component:this._component},this._pageId)}}const $p=Td(0,(e=>((e=oe(e))&&!Tc(e)&&(e=null),new Op(e||Ec()))));let Dp=0,Rp={};const Bp={canvas:yp,map:Zd,video:Jd,editor:class{constructor(e,t){this.id=e,this.pageId=t}format(e,t){this._exec("format",{name:e,value:t})}insertDivider(){this._exec("insertDivider")}insertImage(e){this._exec("insertImage",e)}insertText(e){this._exec("insertText",e)}setContents(e){this._exec("setContents",e)}getContents(e){this._exec("getContents",e)}clear(e){this._exec("clear",e)}removeFormat(e){this._exec("removeFormat",e)}undo(e){this._exec("undo",e)}redo(e){this._exec("redo",e)}blur(e){this._exec("blur",e)}getSelectionText(e){this._exec("getSelectionText",e)}scrollIntoView(e){this._exec("scrollIntoView",e)}_exec(e,t){!function(e,t,n,o){const r={options:o},i=o&&("success"in o||"fail"in o||"complete"in o);if(i){const e=String(Dp++);r.callbackId=e,Rp[e]=o}Cx.invokeViewMethod(`editor.${e}`,{type:n,data:r},t,(({callbackId:e,data:t})=>{i&&(he(Rp[e],t),delete Rp[e])}))}(this.id,this.pageId,e,t)}}};function Np(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,r=Bp[n];e.context=new r(t,o),delete e.contextInfo}}class qp{constructor(e,t,n,o){this._selectorQuery=e,this._component=t,this._selector=n,this._single=o}boundingClientRect(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},e),this._selectorQuery}fields(e,t){return this._selectorQuery._push(this._selector,this._component,this._single,e,t),this._selectorQuery}scrollOffset(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},e),this._selectorQuery}context(e){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},e),this._selectorQuery}node(e){return this._selectorQuery._push(this._selector,this._component,this._single,{node:!0},e),this._selectorQuery}}class jp{constructor(e){this._component=void 0,this._page=e,this._queue=[],this._queueCb=[]}exec(e){return Nu(this._page,this._queue,(t=>{const n=this._queueCb;t.forEach(((e,t)=>{f(e)?e.forEach(Np):Np(e);const o=n[t];m(o)&&o.call(this,e)})),m(e)&&e.call(this,t)})),this._nodesRef}in(e){return this._component=oe(e),this}select(e){return this._nodesRef=new qp(this,this._component,e,!0)}selectAll(e){return this._nodesRef=new qp(this,this._component,e,!1)}selectViewport(){return this._nodesRef=new qp(this,null,"",!0)}_push(e,t,n,o,r){this._queue.push({component:t,selector:e,single:n,fields:o}),this._queueCb.push(r)}}const zp=Td(0,(e=>((e=oe(e))&&!Tc(e)&&(e=null),new jp(e||Ec())))),Vp={formatArgs:{}},Fp={duration:400,timingFunction:"linear",delay:0,transformOrigin:"50% 50% 0"};class Up{constructor(e){this.actions=[],this.currentTransform={},this.currentStepAnimates=[],this.option=c({},Fp,e)}_getOption(e){const t={transition:c({},this.option,e),transformOrigin:""};return t.transformOrigin=t.transition.transformOrigin,delete t.transition.transformOrigin,t}_pushAnimates(e,t){this.currentStepAnimates.push({type:e,args:t})}_converType(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}_getValue(e){return"number"==typeof e?`${e}px`:e}export(){const e=this.actions;return this.actions=[],{actions:e}}step(e){return this.currentStepAnimates.forEach((e=>{"style"!==e.type?this.currentTransform[e.type]=e:this.currentTransform[`${e.type}.${e.args[0]}`]=e})),this.actions.push({animates:Object.values(this.currentTransform),option:this._getOption(e)}),this.currentStepAnimates=[],this}}const Wp=de((()=>{const e=["opacity","backgroundColor"],t=["width","height","left","right","top","bottom"];["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"].concat(e,t).forEach((n=>{Up.prototype[n]=function(...o){return e.concat(t).includes(n)?this._pushAnimates("style",[this._converType(n),t.includes(n)?this._getValue(o[0]):o[0]]):this._pushAnimates(n,o),this}}))})),Hp=Td(0,(e=>(Wp(),new Up(e))),0,Vp),Yp=_d("onTabBarMidButtonTap",(()=>{})),Xp=_d("onWindowResize",(()=>{})),Gp=wd("offWindowResize",(()=>{})),Jp=Td(0,(()=>{const e=uv();return e&&e.$vm?e.$vm.$locale:bl().getLocale()})),Qp=_d("onLocaleChange",(()=>{})),Kp=Td(0,(e=>{const t=uv();if(!t)return!1;return t.$vm.$locale!==e&&(t.$vm.$locale=e,navigator.cookieEnabled&&window.localStorage&&(localStorage.UNI_LOCALE=e),Cx.invokeOnCallback("onLocaleChange",{locale:e}),!0)})),Zp=Sd("setPageMeta",((e,{resolve:t})=>{t(function(e,{pageStyle:t,rootFontSize:n}){t&&(document.querySelector("uni-page-body")||document.body).setAttribute("style",t);n&&document.documentElement.style.fontSize!==n&&(document.documentElement.style.fontSize=n)}(Ec(),e))})),ef=Sd("getSelectedTextRange",((e,{resolve:t,reject:n})=>{Cx.invokeViewMethod("getSelectedTextRange",{},Cc(),(e=>{void 0===e.end&&void 0===e.start?n("no focused"):t(e)}))})),tf={onUnhandledRejection:[],onPageNotFound:[],onError:[],onShow:[],onHide:[]};function nf(e,t){const n=uv();if(n&&n.$vm)return Bo(e,t,n.$vm.$);tf[e].push(t)}function of(e,t){const n=uv();if(n&&n.$vm)return function(e,t,n){const o=e.$[t];f(o)&&n.__weh&&u(o,n.__weh)}(n.$vm,e,t);u(tf[e],t)}const rf=Td(0,(()=>xh())),af=Td(0,(()=>c({},_h)));let sf,lf,cf;function uf(e){try{return JSON.parse(e)}catch(yC){}return e}const df=[];function pf(e,t){df.forEach((n=>{n(e,t)})),df.length=0}const ff=Sd("getPushClientId",((e,{resolve:t,reject:n})=>{Promise.resolve().then((()=>{void 0===cf&&(cf=!1,sf="",lf="uniPush is not enabled"),df.push(((e,o)=>{e?t({cid:e}):n(o)})),void 0!==sf&&pf(sf,lf)}))})),hf=[],gf={formatArgs:{showToast:!0},beforeInvoke(){El()},beforeSuccess(e,t){if(!t.showToast)return;const{t:n}=bl(),o=n("uni.setClipboardData.success");o&&uni.showToast({title:o,icon:"success",mask:!1})}},mf=(Boolean,{formatArgs:{filePath(e,t){t.filePath=Tu(e)}}}),vf={formatArgs:{filePath(e,t){t.filePath=Tu(e)}}},yf=["wgs84","gcj02"],bf={formatArgs:{type(e,t){e=(e||"").toLowerCase(),-1===yf.indexOf(e)?t.type=yf[0]:t.type=e},altitude(e,t){t.altitude=e||!1}}},_f=(Boolean,(e,t)=>{if(void 0===t)return`${e} should not be empty.`;if("number"!=typeof t){let e=typeof t;return e=e[0].toUpperCase()+e.substring(1),`Expected Number, got ${e} with value ${JSON.stringify(t)}.`}}),wf={formatArgs:{latitude(e,t){const n=_f("latitude",e);if(n)return n;t.latitude=e},longitude(e,t){const n=_f("longitude",e);if(n)return n;t.longitude=e},scale(e,t){e=Math.floor(e),t.scale=e>=5&&e<=18?e:18}}},xf={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=Wu(e,zu)},sourceType(e,t){t.sourceType=Wu(e,Vu)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Tf={formatArgs:{sourceType(e,t){t.sourceType=Wu(e,Vu)},compressed:!0,maxDuration:60,camera:"back",extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Sf=(Boolean,["all","image","video"]),kf={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=100)},sourceType(e,t){t.sourceType=Wu(e,Vu)},type(e,t){t.type=Uu(e,Sf)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=[""])}}},Cf={formatArgs:{src(e,t){t.src=Tu(e)}}},Ef={formatArgs:{urls(e,t){t.urls=e.map((e=>v(e)&&e?Tu(e):""))},current(e,t){"number"==typeof e?t.current=e>0&&e<t.urls.length?e:0:v(e)&&e&&(t.current=Tu(e))}}},Af={formatArgs:{src(e,t){t.src=Tu(e)}}},Mf="json",Pf=["text","arraybuffer"],If=encodeURIComponent;ArrayBuffer,Boolean;const Lf={formatArgs:{method(e,t){t.method=Uu((e||"").toUpperCase(),Fu)},data(e,t){t.data=e||""},url(e,t){t.method===Fu[0]&&T(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),a={};i.forEach((e=>{const t=e.split("=");a[t[0]]=t[1]}));for(const s in t)if(p(t,s)){let e=t[s];null==e?e="":T(e)&&(e=JSON.stringify(e)),a[If(s)]=If(e)}return r=Object.keys(a).map((e=>`${e}=${a[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==Fu[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||Mf).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===Pf.indexOf(t.responseType)&&(t.responseType="text")}}},Of={formatArgs:{header(e,t){t.header=e||{}}}},$f={formatArgs:{filePath(e,t){e&&(t.filePath=Tu(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}},Df={formatArgs:{header(e,t){t.header=e||{}},method(e,t){t.method=Uu((e||"").toUpperCase(),Fu)},protocols(e,t){v(e)&&(t.protocols=[e])}}},Rf=["wgs84","gcj02"],Bf={formatArgs:{type(e,t){e=(e||"").toLowerCase(),-1===Rf.indexOf(e)?t.type=Rf[1]:t.type=e}}};const Nf={url:{type:String,required:!0}},qf=(Uf(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),Uf(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),Yf("navigateTo")),jf=Yf("redirectTo"),zf=Yf("reLaunch"),Vf=Yf("switchTab"),Ff={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(qm().length-1,e)}}};function Uf(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let Wf;function Hf(){Wf=""}function Yf(e){return{formatArgs:{url:Xf(e)},beforeAll:Hf}}function Xf(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/"))return e;let t="";const n=qm();return n.length&&(t=n[n.length-1].$page.route),Dc(t,e)}(t)).split("?")[0],r=Rc(o,!0);if(!r)return"page `"+t+"` is not found";if("navigateTo"===e||"redirectTo"===e){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if("switchTab"===e&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if("switchTab"!==e&&"preloadPage"!==e||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!v(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))})),o.length?e+"?"+o.join("&"):e}(t),"unPreloadPage"!==e)if("preloadPage"!==e){if(Wf===t&&"appLaunch"!==n.openType)return`${Wf} locked`;__uniConfig.ready&&(Wf=t)}else if(r.meta.isTabBar){const e=qm(),t=r.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}const Gf={formatArgs:{animation(e,t){e||(e={duration:0,timingFunc:"linear"}),t.animation={duration:e.duration||0,timingFunc:e.timingFunc||"linear"}}}},Jf={formatArgs:{duration:300}},Qf={formatArgs:{itemColor:"#000"}},Kf=(Boolean,{formatArgs:{title:"",mask:!1}}),Zf=(Boolean,{beforeInvoke(){kl()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!p(t,"cancelText")){const{t:e}=bl();t.cancelText=e("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!p(t,"confirmText")){const{t:e}=bl();t.confirmText=e("uni.showModal.confirm")}},confirmColor:"#007aff"}}),eh=["success","loading","none","error"],th=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=Uu(e,eh)},image(e,t){t.image=e?Tu(e):""},duration:1500,mask:!1}}),nh={beforeInvoke(){const e=kc();if(e&&!e.isTabBar)return"not TabBar page"},formatArgs:{index(e){if(!__uniConfig.tabBar.list[e])return"tabbar item not found"}}},oh={beforeInvoke:nh.beforeInvoke,formatArgs:c({pagePath(e,t){e&&(t.pagePath=ue(e))}},nh.formatArgs)},rh=/^(linear|radial)-gradient\(.+?\);?$/,ih={beforeInvoke:nh.beforeInvoke,formatArgs:{backgroundImage(e,t){e&&!rh.test(e)&&(t.backgroundImage=Tu(e))},borderStyle(e,t){e&&(t.borderStyle="white"===e?"white":"black")}}},ah=nh,sh=nh,lh=nh,ch={beforeInvoke:nh.beforeInvoke,formatArgs:c({text(e,t){(function(e=""){return(""+e).replace(/[^\x00-\xff]/g,"**").length})(e)>=4&&(t.text="...")}},nh.formatArgs)};function uh(e){const{bottom:t,height:n,left:o,right:r,top:i,width:a}=e||{};return{bottom:t,height:n,left:o,right:r,top:i,width:a}}function dh(e){const{intersectionRatio:t,boundingClientRect:{height:n,width:o},intersectionRect:{height:r,width:i}}=e;return 0!==t?t:r===n?i/o:r/n}let ph={},fh={};function hh(e){return e.replace(/([A-Z])/g,"-$1").toLowerCase()}const gh={};function mh(e,t){const n=gh[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(function(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",r=atob(t[1]);let i=r.length;const a=new Uint8Array(i);for(;i--;)a[i]=r.charCodeAt(i);return vh(a,o)}(e)):t?Promise.reject(new Error("not find")):new Promise(((t,n)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){t(this.response)},o.onerror=n,o.send()}))}function vh(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const r=`${Date.now()}${function(e){const t=e.split("/")[1];return t?`.${t}`:""}(t)}`;try{n=new File([e],r,{type:t})}catch(o){n=e=e instanceof Blob?e:new Blob([e],{type:t}),n.name=n.name||r}}return n}function yh(e){for(const n in gh)if(p(gh,n)){if(gh[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return gh[t]=e,t}function bh(e){(window.URL||window.webkitURL).revokeObjectURL(e),delete gh[e]}const _h=ru(),wh=ru();function xh(){return c({},wh)}const Th=lu({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=sn(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=Ut({width:-1,height:-1});return to((()=>c({},o)),(e=>t("resize",e))),()=>{const t=e.value;t&&(o.width=t.offsetWidth,o.height=t.offsetHeight,n())}}(n,t,o);return function(e,t,n,o){Mo(o),jo((()=>{t.initial&&Cn(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,r,o),()=>ri("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[ri("div",{onScroll:r},[ri("div",null,null)],40,["onScroll"]),ri("div",{onScroll:r},[ri("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});function Sh(){}const kh={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function Ch(e,t,n){function o(e){const t=Ei((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(undefined),document.addEventListener("click",Sh,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",Sh,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}to((()=>t.value),(e=>e&&o(e)))}var Eh=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,Ah=/^<\/([-A-Za-z0-9_]+)[^>]*>/,Mh=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,Ph=Rh("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),Ih=Rh("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),Lh=Rh("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),Oh=Rh("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),$h=Rh("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),Dh=Rh("script,style");function Rh(e){for(var t={},n=e.split(","),o=0;o<n.length;o++)t[n[o]]=!0;return t}const Bh={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},Nh={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},qh={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},jh=lu({name:"Image",props:Bh,setup(e,{emit:t}){const n=sn(null),o=function(e,t){const n=sn(""),o=Ei((()=>{let e="auto",o="";const r=qh[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),r=Ut({rootEl:e,src:Ei((()=>t.src?Tu(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return jo((()=>{const t=e.value.style;r.origWidth=Number(t.width)||0,r.origHeight=Number(t.height)||0})),r}(n,e),r=du(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=Nh[o];if(!r)return;const{origWidth:i,origHeight:a}=n,s=i&&a?i/a:0;if(!s)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){zh&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,s))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return to((()=>t.mode),((e,t)=>{Nh[t]&&r(),Nh[e]&&o()})),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,a;const s=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void s();i=i||new Image,i.onload=e=>{const{width:u,height:d}=i;s(u,d,l),o(),i.draggable=t.draggable,a&&a.remove(),a=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:d})},i.onerror=t=>{s(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};to((()=>e.src),(e=>l(e))),to((()=>e.imgSrc),(e=>{!e&&a&&(a.remove(),a=null)})),jo((()=>l(e.src))),Fo((()=>c()))}(o,e,n,i,r),()=>ri("uni-image",{ref:n},[ri("div",{style:o.modeStyle},null,4),Nh[e.mode]?ri(Th,{onResize:i},null,8,["onResize"]):ri("span",null,null)],512)}});const zh="Google Inc."===navigator.vendor;const Vh=be(!0),Fh=[];let Uh=0,Wh=!1;const Hh=e=>Fh.forEach((t=>t.userAction=e));function Yh(e={userAction:!1}){if(!Wh){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!Uh&&Hh(!0),Uh++,setTimeout((()=>{!--Uh&&Hh(!1)}),0)}),Vh)})),Wh=!0}Fh.push(e)}const Xh=()=>!!Uh;function Gh(){const e=Ut({userAction:!1});return jo((()=>{Yh(e)})),Fo((()=>{!function(e){const t=Fh.indexOf(e);t>=0&&Fh.splice(t,1)}(e)})),{state:e}}function Jh(){const e=Ut({attrs:{}});return jo((()=>{let t=mi();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}function Qh(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}function Kh(e,t,n){"number"===t&&isNaN(Number(e))&&(e="");const o=null==e?"":String(e);return null==n?o:o.slice(0,n)}const Zh=["none","text","decimal","numeric","tel","search","email","url"],eg=c({},{name:{type:String,default:""},modelValue:{type:[String,Number]},value:{type:[String,Number]},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~Zh.indexOf(e)},cursorColor:{type:String,default:""}},kh),tg=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function ng(e,t,n,o){let r=null;r=ke((n=>{t.value=Kh(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout}),to((()=>e.modelValue),r),to((()=>e.value),r);const i=function(e,t){let n,o,r=0;const i=function(...i){const a=Date.now();clearTimeout(n),o=()=>{o=null,r=a,e.apply(this,i)},a-r<t?n=setTimeout(o,t-(a-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}(((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return qo((()=>{r.cancel(),i.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function og(e,t){Gh();const n=Ei((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}to((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),jo((()=>{n.value&&Cn(o)}))}function rg(e,t,n,o){Rl(Cc(),"getSelectedTextRange",Qh);const{fieldRef:r,state:i,trigger:a}=function(e,t,n){const o=sn(null),r=du(t,n),i=Ei((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),a=Ei((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),s=Ei((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=Ei((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t}));let c="";c=Kh(e.modelValue,e.type)||Kh(e.value,e.type);const u=Ut({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:a,cursor:s});return to((()=>u.focus),(e=>n("update:focus",e))),to((()=>u.maxlength),(e=>u.value=u.value.slice(0,e)),{immediate:!1}),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:s}=ng(e,i,n,a);og(e,r),Ch(0,r);const{state:l}=Jh();!function(e,t){const n=_r(gu,!1);if(!n)return;const o=mi(),r={submit(){const n=o.proxy;return[n[e],v(t)?n[t]:t.value]},reset(){v(t)?o.proxy[t]="":t.value=""}};n.addField(r),Fo((()=>{n.removeField(r)}))}("name",i),function(e,t,n,o,r,i){function a(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function s(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}to([()=>t.selectionStart,()=>t.selectionEnd],a),to((()=>t.cursor),s),to((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),m(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function d(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),a(),s()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,d(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),d(e)})),c.addEventListener("compositionupdate",d)}))}(r,i,e,a,s,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:a}}function ig(e,t,n,o,r){if(t.value)if("."===e.data){if("."===t.value.slice(-1))return n.value=o.value=t.value=t.value.slice(0,-1),!1;if(t.value&&!t.value.includes("."))return t.value+=".",r&&(r.fn=()=>{n.value=o.value=t.value=t.value.slice(0,-1),o.removeEventListener("blur",r.fn)},o.addEventListener("blur",r.fn)),!1}else if("deleteContentBackward"===e.inputType&&navigator.userAgent.includes("iPhone OS 16")&&"."===t.value.slice(-2,-1))return t.value=n.value=o.value=t.value.slice(0,-2),!0}const ag=lu({name:"Input",props:c({},eg,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),emits:["confirm",...tg],setup(e,{emit:t,expose:n}){const o=["text","number","idcard","digit","password","tel"],r=["off","one-time-code"],i=Ei((()=>{let t="";switch(e.type){case"text":"search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=~o.includes(e.type)?e.type:"text"}return e.password?"password":t})),a=Ei((()=>{const t=r.indexOf(e.textContentType),n=r.indexOf(P(e.textContentType));return r[-1!==t?t:-1!==n?n:0]}));let s=function(e,t){if("number"===t.value){const t=void 0===e.modelValue?e.value:e.modelValue,n=sn(void 0!==t?t.toLocaleString():"");return to((()=>e.modelValue),(e=>{n.value=void 0!==e?e.toLocaleString():""})),to((()=>e.value),(e=>{n.value=void 0!==e?e.toLocaleString():""})),n}return sn("")}(e,i),l={fn:null};const c=sn(null),{fieldRef:u,state:d,scopedAttrsState:p,fixDisabledColor:f,trigger:h}=rg(e,c,t,((e,t)=>{const n=e.target;if("number"===i.value){if(l.fn&&(n.removeEventListener("blur",l.fn),l.fn=null),n.validity&&!n.validity.valid){if((!s.value||!n.value)&&"-"===e.data||"-"===s.value[0]&&"deleteContentBackward"===e.inputType)return s.value="-",t.value="",l.fn=()=>{s.value=n.value=""},n.addEventListener("blur",l.fn),!1;const o=ig(e,s,t,n,l);return"boolean"==typeof o?o:(s.value=t.value=n.value="-"===s.value?"":s.value,!1)}{const o=ig(e,s,t,n,l);if("boolean"==typeof o)return o;s.value=n.value}const o=t.maxlength;if(o>0&&n.value.length>o)return n.value=n.value.slice(0,o),t.value=n.value,!1}}));to((()=>d.value),(t=>{"number"!==e.type||"-"===s.value&&""===t||(s.value=t.toString())}));const g=["number","digit"],m=Ei((()=>g.includes(e.type)?e.step:""));function v(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),h("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),d.value=e.value}}),()=>{let t=e.disabled&&f?ri("input",{key:"disabled-input",ref:u,value:d.value,tabindex:"-1",readonly:!!e.disabled,type:i.value,maxlength:d.maxlength,step:m.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:e=>e.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):ri("input",{key:"input",ref:u,value:d.value,onInput:e=>{d.value=e.target.value.toString()},disabled:!!e.disabled,type:i.value,maxlength:d.maxlength,step:m.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:a.value,onKeyup:v,inputmode:e.inputmode},null,44,["value","onInput","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return ri("uni-input",{ref:c},[ri("div",{class:"uni-input-wrapper"},[ao(ri("div",di(p.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Gi,!(d.value.length||"-"===s.value||s.value.includes("."))]]),"search"===e.confirmType?ri("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});const sg=["class","style"],lg=/^on[A-Z]+/,cg=(e={})=>{const{excludeListeners:t=!1,excludeKeys:n=[]}=e,o=mi(),r=ln({}),i=ln({}),a=ln({}),s=n.concat(sg);return o.attrs=Ut(o.attrs),Zn((()=>{const e=(n=o.attrs,Object.keys(n).map((e=>[e,n[e]]))).reduce(((e,[n,o])=>(s.includes(n)?e.exclude[n]=o:lg.test(n)?(t||(e.attrs[n]=o),e.listeners[n]=o):e.attrs[n]=o,e)),{exclude:{},attrs:{},listeners:{}});var n;r.value=e.attrs,i.value=e.listeners,a.value=e.exclude})),{$attrs:r,$listeners:i,$excludeAttrs:a}};function ug(e){const t=[];return f(e)&&e.forEach((e=>{Kr(e)?e.type===jr?t.push(...ug(e.children)):t.push(e):f(e)&&t.push(...ug(e))})),t}const dg=lu({inheritAttrs:!1,name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},setup(e,{slots:t}){const n=sn(null),o=sn(!1);let{setContexts:r,events:i}=function(e,t){const n=sn(0),o=sn(0),r=Ut({x:null,y:null}),i=sn(null);let a=null,s=[];function l(t){t&&1!==t&&(e.scaleArea?s.forEach((function(e){e._setScale(t)})):a&&a._setScale(t))}function c(e,n=s){let o=t.value;function r(e){for(let t=0;t<n.length;t++){const o=n[t];if(e===o.rootRef.value)return o}return e===o||e===document.body||e===document?null:r(e.parentNode)}return r(e)}const u=uu((t=>{let n=t.touches;if(n&&n.length>1){let t={x:n[1].pageX-n[0].pageX,y:n[1].pageY-n[0].pageY};if(i.value=pg(t),r.x=t.x,r.y=t.y,!e.scaleArea){let e=c(n[0].target),t=c(n[1].target);a=e&&e===t?e:null}}})),d=uu((e=>{let t=e.touches;if(t&&t.length>1){e.preventDefault();let n={x:t[1].pageX-t[0].pageX,y:t[1].pageY-t[0].pageY};if(null!==r.x&&i.value&&i.value>0){l(pg(n)/i.value)}r.x=n.x,r.y=n.y}})),p=uu((t=>{let n=t.touches;n&&n.length||t.changedTouches&&(r.x=0,r.y=0,i.value=null,e.scaleArea?s.forEach((function(e){e._endScale()})):a&&a._endScale())}));function f(){h(),s.forEach((function(e,t){e.setParent()}))}function h(){let e=window.getComputedStyle(t.value),r=t.value.getBoundingClientRect();n.value=r.width-["Left","Right"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0),o.value=r.height-["Top","Bottom"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0)}return br("movableAreaWidth",n),br("movableAreaHeight",o),{setContexts(e){s=e},events:{_onTouchstart:u,_onTouchmove:d,_onTouchend:p,_resize:f}}}(e,n);const{$listeners:a,$attrs:s,$excludeAttrs:l}=cg(),c=a.value;["onTouchstart","onTouchmove","onTouchend"].forEach((e=>{let t=c[e],n=i[`_${e}`];c[e]=t?[].concat(t,n):n})),jo((()=>{i._resize(),o.value=!0}));let u=[];const d=[];function p(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n=n.el;const o=d.find((e=>n===e.rootRef.value));o&&e.push(Zt(o))}r(e)}return br("_isMounted",o),br("movableAreaRootRef",n),br("addMovableViewContext",(e=>{d.push(e),p()})),br("removeMovableViewContext",(e=>{const t=d.indexOf(e);t>=0&&(d.splice(t,1),p())})),()=>{const e=t.default&&t.default();return u=ug(e),ri("uni-movable-area",di({ref:n},s.value,l.value,c),[ri(Th,{onResize:i._resize},null,8,["onResize"]),u],16)}}});function pg(e){return Math.sqrt(e.x*e.x+e.y*e.y)}const fg=function(e,t,n,o){e.addEventListener(t,(e=>{m(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};let hg,gg;function mg(e,t,n){Fo((()=>{document.removeEventListener("mousemove",hg),document.removeEventListener("mouseup",gg)}));let o=0,r=0,i=0,a=0;const s=function(e,n,s,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:s,y:l,dx:s-o,dy:l-r,ddx:s-i,ddy:l-a,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;fg(e,"touchstart",(function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=i=e.touches[0].pageX,r=a=e.touches[0].pageY,s(e,"start",o,r)})),fg(e,"mousedown",(function(e){if(c=!0,!l&&!u)return u=e,o=i=e.pageX,r=a=e.pageY,s(e,"start",o,r)})),fg(e,"touchmove",(function(e){if(1===e.touches.length&&u){const t=s(e,"move",e.touches[0].pageX,e.touches[0].pageY);return i=e.touches[0].pageX,a=e.touches[0].pageY,t}}));const d=hg=function(e){if(!l&&c&&u){const t=s(e,"move",e.pageX,e.pageY);return i=e.pageX,a=e.pageY,t}};document.addEventListener("mousemove",d),fg(e,"touchend",(function(e){if(0===e.touches.length&&u)return l=!1,u=null,s(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));const p=gg=function(e){if(c=!1,!l&&u)return u=null,s(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",p),fg(e,"touchcancel",(function(e){if(u){l=!1;const t=u;return u=null,s(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}function vg(e,t,n){return e>t-n&&e<t+n}function yg(e,t){return vg(e,0,t)}function bg(){}function _g(e,t){this._m=e,this._f=1e3*t,this._startTime=0,this._v=0}function wg(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}function xg(e,t,n){this._springX=new wg(e,t,n),this._springY=new wg(e,t,n),this._springScale=new wg(e,t,n),this._startTime=0}bg.prototype.x=function(e){return Math.sqrt(e)},_g.prototype.setV=function(e,t){const n=Math.pow(Math.pow(e,2)+Math.pow(t,2),.5);this._x_v=e,this._y_v=t,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(e/this._x_a)||Math.abs(t/this._y_a),this._lastDt=null,this._startTime=(new Date).getTime()},_g.prototype.setS=function(e,t){this._x_s=e,this._y_s=t},_g.prototype.s=function(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t,this._lastDt=e);let t=this._x_v*e+.5*this._x_a*Math.pow(e,2)+this._x_s,n=this._y_v*e+.5*this._y_a*Math.pow(e,2)+this._y_s;return(this._x_a>0&&t<this._endPositionX||this._x_a<0&&t>this._endPositionX)&&(t=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:t,y:n}},_g.prototype.ds=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t),{dx:this._x_v+this._x_a*e,dy:this._y_v+this._y_a*e}},_g.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},_g.prototype.dt=function(){return-this._x_v/this._x_a},_g.prototype.done=function(){const e=vg(this.s().x,this._endPositionX)||vg(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,e},_g.prototype.setEnd=function(e,t){this._endPositionX=e,this._endPositionY=t},_g.prototype.reconfigure=function(e,t){this._m=e,this._f=1e3*t},wg.prototype._solve=function(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,a=t/(r*e);return{x:function(e){return(i+a*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+a*e)*t+a*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),a=(-n+Math.sqrt(i))/(2*o),s=(t-r*e)/(a-r),l=e-s;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*t+s*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*r*t+s*a*n}}}const a=Math.sqrt(4*o*r-n*n)/(2*o),s=-n/2*o,l=e,c=(t-s*e)/a;return{x:function(e){return Math.pow(Math.E,s*e)*(l*Math.cos(a*e)+c*Math.sin(a*e))},dx:function(e){const t=Math.pow(Math.E,s*e),n=Math.cos(a*e),o=Math.sin(a*e);return t*(c*a*n-l*a*o)+s*t*(c*o+l*n)}}},wg.prototype.x=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0},wg.prototype.dx=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0},wg.prototype.setEnd=function(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!yg(t,.1)){t=t||0;let o=this._endPosition;this._solution&&(yg(t,.1)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),yg(t,.1)&&(t=0),yg(o,.1)&&(o=0),o+=this._endPosition),this._solution&&yg(o-e,.1)&&yg(t,.1)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}},wg.prototype.snap=function(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}},wg.prototype.done=function(e){return e||(e=(new Date).getTime()),vg(this.x(),this._endPosition,.1)&&yg(this.dx(),.1)},wg.prototype.reconfigure=function(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},wg.prototype.springConstant=function(){return this._k},wg.prototype.damping=function(){return this._c},wg.prototype.configuration=function(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]},xg.prototype.setEnd=function(e,t,n,o){const r=(new Date).getTime();this._springX.setEnd(e,o,r),this._springY.setEnd(t,o,r),this._springScale.setEnd(n,o,r),this._startTime=r},xg.prototype.x=function(){const e=((new Date).getTime()-this._startTime)/1e3;return{x:this._springX.x(e),y:this._springY.x(e),scale:this._springScale.x(e)}},xg.prototype.done=function(){const e=(new Date).getTime();return this._springX.done(e)&&this._springY.done(e)&&this._springScale.done(e)},xg.prototype.reconfigure=function(e,t,n){this._springX.reconfigure(e,t,n),this._springY.reconfigure(e,t,n),this._springScale.reconfigure(e,t,n)};function Tg(e,t){return+((1e3*e-1e3*t)/1e3).toFixed(1)}const Sg=lu({name:"MovableView",props:{direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.5},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}},emits:["change","scale"],setup(e,{slots:t,emit:n}){const o=sn(null),r=du(o,n),{setParent:i}=function(e,t,n){const o=_r("_isMounted",sn(!1)),r=_r("addMovableViewContext",(()=>{})),i=_r("removeMovableViewContext",(()=>{}));let a,s,l=sn(1),c=sn(1),u=sn(!1),d=sn(0),p=sn(0),f=null,h=null,g=!1,m=null,v=null;const y=new bg,b=new bg,_={historyX:[0,0],historyY:[0,0],historyT:[0,0]},w=Ei((()=>{let t=Number(e.friction);return isNaN(t)||t<=0?2:t})),x=new _g(1,w.value);to((()=>e.disabled),(()=>{W()}));const{_updateOldScale:T,_endScale:S,_setScale:k,scaleValueSync:C,_updateBoundary:E,_updateOffset:A,_updateWH:M,_scaleOffset:P,minX:I,minY:L,maxX:O,maxY:$,FAandSFACancel:D,_getLimitXY:R,_setTransform:B,_revise:N,dampingNumber:q,xMove:j,yMove:z,xSync:V,ySync:F,_STD:U}=function(e,t,n,o,r,i,a,s,l,c){const u=Ei((()=>{let t=Number(e.scaleMin);return isNaN(t)?.5:t})),d=Ei((()=>{let t=Number(e.scaleMax);return isNaN(t)?10:t})),p=sn(Number(e.scaleValue)||1);to(p,(e=>{B(e)})),to(u,(()=>{R()})),to(d,(()=>{R()})),to((()=>e.scaleValue),(e=>{p.value=Number(e)||0}));const{_updateBoundary:f,_updateOffset:h,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:b,maxY:_}=function(e,t,n){const o=_r("movableAreaWidth",sn(0)),r=_r("movableAreaHeight",sn(0)),i=_r("movableAreaRootRef"),a={x:0,y:0},s={x:0,y:0},l=sn(0),c=sn(0),u=sn(0),d=sn(0),p=sn(0),f=sn(0);function h(){let e=0-a.x+s.x,t=o.value-l.value-a.x-s.x;u.value=Math.min(e,t),p.value=Math.max(e,t);let n=0-a.y+s.y,i=r.value-c.value-a.y-s.y;d.value=Math.min(n,i),f.value=Math.max(n,i)}function g(){a.x=Eg(e.value,i.value),a.y=Ag(e.value,i.value)}function m(o){o=o||t.value,o=n(o);let r=e.value.getBoundingClientRect();c.value=r.height/t.value,l.value=r.width/t.value;let i=c.value*o,a=l.value*o;s.x=(a-l.value)/2,s.y=(i-c.value)/2}return{_updateBoundary:h,_updateOffset:g,_updateWH:m,_scaleOffset:s,minX:u,minY:d,maxX:p,maxY:f}}(t,o,D),{FAandSFACancel:w,_getLimitXY:x,_animationTo:T,_setTransform:S,_revise:k,dampingNumber:C,xMove:E,yMove:A,xSync:M,ySync:P,_STD:I}=function(e,t,n,o,r,i,a,s,l,c,u,d,p,f){const h=Ei((()=>{let e=Number(t.damping);return isNaN(e)?20:e})),g=Ei((()=>"all"===t.direction||"horizontal"===t.direction)),m=Ei((()=>"all"===t.direction||"vertical"===t.direction)),v=sn(Pg(t.x)),y=sn(Pg(t.y));to((()=>t.x),(e=>{v.value=Pg(e)})),to((()=>t.y),(e=>{y.value=Pg(e)})),to(v,(e=>{k(e)})),to(y,(e=>{C(e)}));const b=new xg(1,9*Math.pow(h.value,2)/40,h.value);function _(e,t){let n=!1;return e>r.value?(e=r.value,n=!0):e<a.value&&(e=a.value,n=!0),t>i.value?(t=i.value,n=!0):t<s.value&&(t=s.value,n=!0),{x:e,y:t,outOfBounds:n}}function w(){d&&d.cancel(),u&&u.cancel()}function x(e,n,r,i,a,s){w(),g.value||(e=l.value),m.value||(n=c.value),t.scale||(r=o.value);let d=_(e,n);e=d.x,n=d.y,t.animation?(b._springX._solution=null,b._springY._solution=null,b._springScale._solution=null,b._springX._endPosition=l.value,b._springY._endPosition=c.value,b._springScale._endPosition=o.value,b.setEnd(e,n,r,1),u=Mg(b,(function(){let e=b.x();T(e.x,e.y,e.scale,i,a,s)}),(function(){u.cancel()}))):T(e,n,r,i,a,s)}function T(r,i,a,s="",u,d){null!==r&&"NaN"!==r.toString()&&"number"==typeof r||(r=l.value||0),null!==i&&"NaN"!==i.toString()&&"number"==typeof i||(i=c.value||0),r=Number(r.toFixed(1)),i=Number(i.toFixed(1)),a=Number(a.toFixed(1)),l.value===r&&c.value===i||u||f("change",{},{x:Tg(r,n.x),y:Tg(i,n.y),source:s}),t.scale||(a=o.value),a=+(a=p(a)).toFixed(3),d&&a!==o.value&&f("scale",{},{x:r,y:i,scale:a});let h="translateX("+r+"px) translateY("+i+"px) translateZ(0px) scale("+a+")";e.value&&(e.value.style.transform=h,e.value.style.webkitTransform=h,l.value=r,c.value=i,o.value=a)}function S(e){let t=_(l.value,c.value),n=t.x,r=t.y,i=t.outOfBounds;return i&&x(n,r,o.value,e),i}function k(e){if(g.value){if(e+n.x===l.value)return l;u&&u.cancel(),x(e+n.x,y.value+n.y,o.value)}return e}function C(e){if(m.value){if(e+n.y===c.value)return c;u&&u.cancel(),x(v.value+n.x,e+n.y,o.value)}return e}return{FAandSFACancel:w,_getLimitXY:_,_animationTo:x,_setTransform:T,_revise:S,dampingNumber:h,xMove:g,yMove:m,xSync:v,ySync:y,_STD:b}}(t,e,m,o,b,_,v,y,a,s,l,c,D,n);function L(t,n){if(e.scale){t=D(t),g(t),f();const e=x(a.value,s.value),o=e.x,r=e.y;n?T(o,r,t,"",!0,!0):Cg((function(){S(o,r,t,"",!0,!0)}))}}function O(){i.value=!0}function $(e){r.value=e}function D(e){return e=Math.max(.5,u.value,e),e=Math.min(10,d.value,e)}function R(){if(!e.scale)return!1;L(o.value,!0),$(o.value)}function B(t){return!!e.scale&&(L(t=D(t),!0),$(t),t)}function N(){i.value=!1,$(o.value)}function q(e){e&&(e=r.value*e,O(),L(e))}return{_updateOldScale:$,_endScale:N,_setScale:q,scaleValueSync:p,_updateBoundary:f,_updateOffset:h,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:b,maxY:_,FAandSFACancel:w,_getLimitXY:x,_animationTo:T,_setTransform:S,_revise:k,dampingNumber:C,xMove:E,yMove:A,xSync:M,ySync:P,_STD:I}}(e,n,t,l,c,u,d,p,f,h);function W(){u.value||e.disabled||(D(),_.historyX=[0,0],_.historyY=[0,0],_.historyT=[0,0],j.value&&(a=d.value),z.value&&(s=p.value),n.value.style.willChange="transform",m=null,v=null,g=!0)}function H(t){if(!u.value&&!e.disabled&&g){let n=d.value,o=p.value;if(null===v&&(v=Math.abs(t.detail.dx/t.detail.dy)>1?"htouchmove":"vtouchmove"),j.value&&(n=t.detail.dx+a,_.historyX.shift(),_.historyX.push(n),z.value||null!==m||(m=Math.abs(t.detail.dx/t.detail.dy)<1)),z.value&&(o=t.detail.dy+s,_.historyY.shift(),_.historyY.push(o),j.value||null!==m||(m=Math.abs(t.detail.dy/t.detail.dx)<1)),_.historyT.shift(),_.historyT.push(t.detail.timeStamp),!m){t.preventDefault();let r="touch";n<I.value?e.outOfBounds?(r="touch-out-of-bounds",n=I.value-y.x(I.value-n)):n=I.value:n>O.value&&(e.outOfBounds?(r="touch-out-of-bounds",n=O.value+y.x(n-O.value)):n=O.value),o<L.value?e.outOfBounds?(r="touch-out-of-bounds",o=L.value-b.x(L.value-o)):o=L.value:o>$.value&&(e.outOfBounds?(r="touch-out-of-bounds",o=$.value+b.x(o-$.value)):o=$.value),Cg((function(){B(n,o,l.value,r)}))}}}function Y(){if(!u.value&&!e.disabled&&g&&(n.value.style.willChange="auto",g=!1,!m&&!N("out-of-bounds")&&e.inertia)){const e=1e3*(_.historyX[1]-_.historyX[0])/(_.historyT[1]-_.historyT[0]),t=1e3*(_.historyY[1]-_.historyY[0])/(_.historyT[1]-_.historyT[0]),n=d.value,o=p.value;x.setV(e,t),x.setS(n,o);const r=x.delta().x,i=x.delta().y;let a=r+n,s=i+o;a<I.value?(a=I.value,s=o+(I.value-n)*i/r):a>O.value&&(a=O.value,s=o+(O.value-n)*i/r),s<L.value?(s=L.value,a=n+(L.value-o)*r/i):s>$.value&&(s=$.value,a=n+($.value-o)*r/i),x.setEnd(a,s),h=Mg(x,(function(){let e=x.s(),t=e.x,n=e.y;B(t,n,l.value,"friction")}),(function(){h.cancel()}))}e.outOfBounds||e.inertia||D()}function X(){if(!o.value)return;D();let t=e.scale?C.value:1;A(),M(t),E();let n=R(V.value+P.x,F.value+P.y),r=n.x,i=n.y;B(r,i,t,"",!0),T(t)}return jo((()=>{mg(n.value,(e=>{switch(e.detail.state){case"start":W();break;case"move":H(e);break;case"end":Y()}})),X(),x.reconfigure(1,w.value),U.reconfigure(1,9*Math.pow(q.value,2)/40,q.value),n.value.style.transformOrigin="center";const e={rootRef:n,setParent:X,_endScale:S,_setScale:k};r(e),Uo((()=>{i(e)}))})),Uo((()=>{D()})),{setParent:X}}(e,r,o);return()=>ri("uni-movable-view",{ref:o},[ri(Th,{onResize:i},null,8,["onResize"]),t.default&&t.default()],512)}});let kg=!1;function Cg(e){kg||(kg=!0,requestAnimationFrame((function(){e(),kg=!1})))}function Eg(e,t){if(e===t)return 0;let n=e.offsetLeft;return e.offsetParent?n+=Eg(e.offsetParent,t):0}function Ag(e,t){if(e===t)return 0;let n=e.offsetTop;return e.offsetParent?n+=Ag(e.offsetParent,t):0}function Mg(e,t,n){let o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);let i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}function Pg(e){return/\d+[ur]px$/i.test(e)?uni.upx2px(parseFloat(e)):Number(e)||0}const Ig=lu({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return f(e)&&e.filter((e=>"number"==typeof e)).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,{slots:t,emit:n}){const o=sn(null),r=sn(null),i=du(o,n),a=function(e){const t=Ut([...e.value]),n=Ut({value:t,height:34});return to((()=>e.value),((e,t)=>{n.value.length=e.length,e.forEach(((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)}))})),n}(e),s=sn(null);jo((()=>{const e=s.value;e&&(a.height=e.$el.offsetHeight)}));let l=sn([]),c=sn([]);function u(e){let t=c.value;t=t.filter((e=>e.type!==Vr));let n=t.indexOf(e);return-1!==n?n:l.value.indexOf(e)}return br("getPickerViewColumn",(function(e){return Ei({get(){const t=u(e.vnode);return a.value[t]||0},set(t){const o=u(e.vnode);if(o<0)return;if(a.value[o]!==t){a.value[o]=t;const e=a.value.map((e=>e));n("update:value",e),i("change",{},{value:e})}}})})),br("pickerViewProps",e),br("pickerViewState",a),()=>{const e=t.default&&t.default();{const t=ug(e);l.value=t,Cn((()=>{c.value=t}))}return ri("uni-picker-view",{ref:o},[ri(Th,{ref:s,onResize:({height:e})=>a.height=e},null,8,["onResize"]),ri("div",{ref:r,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class Lg{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){const t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){const e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function Og(e,t,n){return e>t-n&&e<t+n}function $g(e,t){return Og(e,0,t)}class Dg{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,a=t/(r*e);return{x:function(e){return(i+a*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+a*e)*t+a*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),a=(-n+Math.sqrt(i))/(2*o),s=(t-r*e)/(a-r),l=e-s;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*t+s*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*r*t+s*a*n}}}const a=Math.sqrt(4*o*r-n*n)/(2*o),s=-n/2*o,l=e,c=(t-s*e)/a;return{x:function(e){return Math.pow(Math.E,s*e)*(l*Math.cos(a*e)+c*Math.sin(a*e))},dx:function(e){const t=Math.pow(Math.E,s*e),n=Math.cos(a*e),o=Math.sin(a*e);return t*(c*a*n-l*a*o)+s*t*(c*o+l*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!$g(t,.4)){t=t||0;let o=this._endPosition;this._solution&&($g(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),$g(t,.4)&&(t=0),$g(o,.4)&&(o=0),o+=this._endPosition),this._solution&&$g(o-e,.4)&&$g(t,.4)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),Og(this.x(),this._endPosition,.4)&&$g(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class Rg{constructor(e,t,n){this._extent=e,this._friction=t||new Lg(.01),this._spring=n||new Dg(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){let t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){const e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class Bg{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new Rg(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){let n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}let o;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){const e=this._scroll._friction.x(100),t=e%this._itemSize;o=Math.abs(t)>this._itemSize/2?e-(this._itemSize-Math.abs(t)):e-t,o<=0&&o>=-this._extent&&this._scroll.setVelocityByEnd(o)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=function(e,t,n){const o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);const i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}(this._scroll,(()=>{const e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();const o=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/o),this._lastTime=e)}),(()=>{this._enableSnap&&(o<=0&&o>=-this._extent&&(this._position=o,this.updatePosition()),m(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1}))}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),m(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(m(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){let o=0;const r=this._position;this._enableX?(o=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(o=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-o?this._position=-o:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),r!==this._position&&(this.dispatchScroll(),m(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=o,this._scroll._extent=o}updatePosition(){let e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}function Ng(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},o=new Bg(e,t);function r(e){const t=e,o=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:o.screenX-n.x,y:o.screenY-n.y}}return{scroller:o,handleTouchStart:function(e){const t=e,r=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=r.screenX,n.y=r.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||r.timeStamp],n.listener=o,o.onTouchStart&&o.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){const t=e,o=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();const i=r(e);if(i){for(n.maxDy=Math.max(n.maxDy,Math.abs(i.y)),n.maxDx=Math.max(n.maxDx,Math.abs(i.x)),n.historyX.push(i.x),n.historyY.push(i.y),n.historyTime.push(t.detail.timeStamp||o.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(i.x,i.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();const t=r(e);if(t){const e=n.listener;n.trackingID=-1,n.listener=null;const o={x:0,y:0};if(n.historyTime.length>2)for(let t=n.historyTime.length-1,r=n.historyTime[t],i=n.historyX[t],a=n.historyY[t];t>0;){t--;const e=r-n.historyTime[t];if(e>30&&e<50){o.x=(i-n.historyX[t])/(e/1e3),o.y=(a-n.historyY[t])/(e/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],e&&e.onTouchEnd&&e.onTouchEnd(t.x,t.y,o)}}}}}const qg=lu({name:"PickerViewColumn",setup(e,{slots:t,emit:n}){const o=sn(null),r=sn(null),i=_r("getPickerViewColumn"),a=mi(),s=i?i(a):sn(0),l=_r("pickerViewProps"),c=_r("pickerViewState"),u=sn(34),d=sn(null);jo((()=>{const e=d.value;u.value=e.$el.offsetHeight}));const p=Ei((()=>(c.height-u.value)/2)),{state:f}=Jh();let h;const g=Ut({current:s.value,length:0});let m;function v(){h&&!m&&(m=!0,Cn((()=>{m=!1;let e=Math.min(g.current,g.length-1);e=Math.max(e,0),h.update(e*u.value,void 0,u.value)})))}to((()=>s.value),(e=>{e!==g.current&&(g.current=e,v())})),to((()=>g.current),(e=>s.value=e)),to([()=>u.value,()=>g.length,()=>c.height],v);let y=0;function b(e){const t=y+e.deltaY;if(Math.abs(t)>10){y=0;let e=Math.min(g.current+(t<0?-1:1),g.length-1);g.current=e=Math.max(e,0),h.scrollTo(e*u.value)}else y=t;e.preventDefault()}function _({clientY:e}){const t=o.value;if(!h.isScrolling()){const n=e-t.getBoundingClientRect().top-c.height/2,o=u.value/2;if(!(Math.abs(n)<=o)){const e=Math.ceil((Math.abs(n)-o)/u.value),t=n<0?-e:e;let r=Math.min(g.current+t,g.length-1);g.current=r=Math.max(r,0),h.scrollTo(r*u.value)}}}return jo((()=>{const e=o.value,t=r.value,{scroller:n,handleTouchStart:i,handleTouchMove:a,handleTouchEnd:s}=Ng(t,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:u.value,friction:new Lg(1e-4),spring:new Dg(2,90,20),onSnap:e=>{isNaN(e)||e===g.current||(g.current=e)}});h=n,mg(e,(e=>{switch(e.detail.state){case"start":i(e);break;case"move":a(e),e.stopPropagation();break;case"end":case"cancel":s(e)}}),!0),function(e){let t=0,n=0;e.addEventListener("touchstart",(e=>{const o=e.changedTouches[0];t=o.clientX,n=o.clientY})),e.addEventListener("touchend",(e=>{const o=e.changedTouches[0];if(Math.abs(o.clientX-t)<20&&Math.abs(o.clientY-n)<20){const t={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},n=new CustomEvent("click",t);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{n[e]=o[e]})),e.target.dispatchEvent(n)}}))}(e),v()})),()=>{const e=t.default&&t.default();g.length=ug(e).length;const n=`${p.value}px 0`;return ri("uni-picker-view-column",{ref:o},[ri("div",{onWheel:b,onClick:_,class:"uni-picker-view-group"},[ri("div",di(f.attrs,{class:["uni-picker-view-mask",l.maskClass],style:`background-size: 100% ${p.value}px;${l.maskStyle}`}),null,16),ri("div",di(f.attrs,{class:["uni-picker-view-indicator",l.indicatorClass],style:l.indicatorStyle}),[ri(Th,{ref:d,onResize:({height:e})=>u.value=e},null,8,["onResize"])],16),ri("div",{ref:r,class:["uni-picker-view-content"],style:{padding:n,"--picker-view-column-indicator-height":`${u.value}px`}},[e],4)],40,["onWheel","onClick"])],512)}}}),jg={a:"",abbr:"",address:"",article:"",aside:"",b:"",bdi:"",bdo:["dir"],big:"",blockquote:"",br:"",caption:"",center:"",cite:"",code:"",col:["span","width"],colgroup:["span","width"],dd:"",del:"",div:"",dl:"",dt:"",em:"",fieldset:"",font:"",footer:"",h1:"",h2:"",h3:"",h4:"",h5:"",h6:"",header:"",hr:"",i:"",img:["alt","src","height","width"],ins:"",label:"",legend:"",li:"",mark:"",nav:"",ol:["start","type"],p:"",pre:"",q:"",rt:"",ruby:"",s:"",section:"",small:"",span:"",strong:"",sub:"",sup:"",table:["width"],tbody:"",td:["colspan","height","rowspan","width"],tfoot:"",th:["colspan","height","rowspan","width"],thead:"",tr:["colspan","height","rowspan","width"],tt:"",u:"",ul:""},zg={amp:"&",gt:">",lt:"<",nbsp:" ",quot:'"',apos:"'",ldquo:"“",rdquo:"”",yen:"￥",radic:"√",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",hellip:"…"};const Vg=(e,t,n)=>!n||f(n)&&!n.length?[]:n.map((n=>{var o;if(T(n)){if(!p(n,"type")||"node"===n.type){let r={[e]:""};const i=null==(o=n.name)?void 0:o.toLowerCase();if(!p(jg,i))return;return function(e,t){if(T(t))for(const n in t)if(p(t,n)){const o=t[n];"img"===e&&"src"===n&&(t[n]=Tu(o))}}(i,n.attrs),r=c(r,function(e,t){if(["a","img"].includes(e.name)&&t)return{onClick:n=>{t(n,{node:e}),n.stopPropagation(),n.preventDefault(),n.returnValue=!1}}}(n,t),n.attrs),Ai(n.name,r,Vg(e,t,n.children))}return"text"===n.type&&v(n.text)&&""!==n.text?ai((n.text||"").replace(/&(([a-zA-Z]+)|(#x{0,1}[\da-zA-Z]+));/gi,(function(e,t){return p(zg,t)&&zg[t]?zg[t]:/^#[0-9]{1,4}$/.test(t)?String.fromCharCode(t.slice(1)):/^#x[0-9a-f]{1,4}$/i.test(t)?String.fromCharCode(0+t.slice(1)):e}))):void 0}}));function Fg(e){e=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(e);const t=[],n={node:"root",children:[]};return function(e,t){var n,o,r,i=[],a=e;for(i.last=function(){return this[this.length-1]};e;){if(o=!0,i.last()&&Dh[i.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+i.last()+"[^>]*>"),(function(e,n){return n=n.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(n),""})),c("",i.last());else if(0==e.indexOf("\x3c!--")?(n=e.indexOf("--\x3e"))>=0&&(t.comment&&t.comment(e.substring(4,n)),e=e.substring(n+3),o=!1):0==e.indexOf("</")?(r=e.match(Ah))&&(e=e.substring(r[0].length),r[0].replace(Ah,c),o=!1):0==e.indexOf("<")&&(r=e.match(Eh))&&(e=e.substring(r[0].length),r[0].replace(Eh,l),o=!1),o){var s=(n=e.indexOf("<"))<0?e:e.substring(0,n);e=n<0?"":e.substring(n),t.chars&&t.chars(s)}if(e==a)throw"Parse Error: "+e;a=e}function l(e,n,o,r){if(n=n.toLowerCase(),Ih[n])for(;i.last()&&Lh[i.last()];)c("",i.last());if(Oh[n]&&i.last()==n&&c("",n),(r=Ph[n]||!!r)||i.push(n),t.start){var a=[];o.replace(Mh,(function(e,t){var n=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:$h[t]?t:"";a.push({name:t,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})})),t.start&&t.start(n,a,r)}}function c(e,n){if(n)for(o=i.length-1;o>=0&&i[o]!=n;o--);else var o=0;if(o>=0){for(var r=i.length-1;r>=o;r--)t.end&&t.end(i[r]);i.length=o}}c()}(e,{start:function(e,o,r){const i={name:e};if(0!==o.length&&(i.attrs=function(e){return e.reduce((function(e,t){let n=t.value;const o=t.name;return n.match(/ /)&&-1===["style","src"].indexOf(o)&&(n=n.split(" ")),e[o]?Array.isArray(e[o])?e[o].push(n):e[o]=[e[o],n]:e[o]=n,e}),{})}(o)),r){const e=t[0]||n;e.children||(e.children=[]),e.children.push(i)}else t.unshift(i)},end:function(e){const o=t.shift();if(o.name!==e&&console.error("invalid state: mismatch end tag"),0===t.length)n.children.push(o);else{const e=t[0];e.children||(e.children=[]),e.children.push(o)}},chars:function(e){const o={type:"text",text:e};if(0===t.length)n.children.push(o);else{const e=t[0];e.children||(e.children=[]),e.children.push(o)}},comment:function(e){const n={node:"comment",text:e},o=t[0];o&&(o.children||(o.children=[]),o.children.push(n))}}),n.children}const Ug=lu({name:"RichText",compatConfig:{MODE:3},props:{nodes:{type:[Array,String],default:function(){return[]}}},emits:["click","touchstart","touchmove","touchcancel","touchend","longpress","itemclick"],setup(e,{emit:t}){const n=mi(),o=n&&n.vnode.scopeId||"",r=sn(null),i=sn([]),a=du(r,t);function s(e,t={}){a("itemclick",e,t)}return to((()=>e.nodes),(function(){let t=e.nodes;v(t)&&(t=Fg(e.nodes)),i.value=Vg(o,s,t)}),{immediate:!0}),()=>Ai("uni-rich-text",{ref:r},Ai("div",{},i.value))}}),Wg=lu({name:"Refresher",props:{refreshState:{type:String,default:""},refresherHeight:{type:Number,default:0},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"}},setup(e,{slots:t}){const n=sn(null),o=Ei((()=>{const t={backgroundColor:e.refresherBackground};switch(e.refreshState){case"pulling":t.height=e.refresherHeight+"px";break;case"refreshing":t.height=e.refresherThreshold+"px",t.transition="height 0.3s";break;case"":case"refresherabort":case"restore":t.height="0px",t.transition="height 0.3s"}return t})),r=Ei((()=>{const t=e.refresherHeight/e.refresherThreshold;return 360*(t>1?1:t)}));return()=>{const{refreshState:i,refresherDefaultStyle:a,refresherThreshold:s}=e;return ri("div",{ref:n,style:o.value,class:"uni-scroll-view-refresher"},["none"!==a?ri("div",{class:"uni-scroll-view-refresh"},[ri("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==i?ri("svg",{key:"refresh__icon",style:{transform:"rotate("+r.value+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[ri("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),ri("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==i?ri("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[ri("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"===a?ri("div",{class:"uni-scroll-view-refresher-container",style:{height:`${s}px`}},[t.default&&t.default()]):null],4)}}}),Hg=be(!0),Yg=lu({name:"ScrollView",compatConfig:{MODE:3},props:{direction:{type:[String],default:"vertical"},scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},showScrollbar:{type:[Boolean,String],default:!0},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n,expose:o}){const r=sn(null),i=sn(null),a=sn(null),s=sn(null),l=du(r,t),{state:c,scrollTopNumber:u,scrollLeftNumber:d}=function(e){const t=Ei((()=>Number(e.scrollTop)||0)),n=Ei((()=>Number(e.scrollLeft)||0));return{state:Ut({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshState:""}),scrollTopNumber:t,scrollLeftNumber:n}}(e),{realScrollX:p,realScrollY:f,_scrollLeftChanged:h,_scrollTopChanged:g}=function(e,t,n,o,r,i,a,s,l){let c=!1,u=0,d=!1,p=()=>{};const f=Ei((()=>e.scrollX)),h=Ei((()=>e.scrollY)),g=Ei((()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t})),m=Ei((()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function v(e,t){const n=a.value;let o=0,r="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?o=n.scrollLeft-e:"y"===t&&(o=n.scrollTop-e),0===o)return;let i=s.value;i.style.transition="transform .3s ease-out",i.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?r="translateX("+o+"px) translateZ(0)":"y"===t&&(r="translateY("+o+"px) translateZ(0)"),i.removeEventListener("transitionend",p),i.removeEventListener("webkitTransitionEnd",p),p=()=>x(e,t),i.addEventListener("transitionend",p),i.addEventListener("webkitTransitionEnd",p),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),i.style.transform=r,i.style.webkitTransform=r}function y(e){const n=e.target;r("scroll",e,{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop,scrollHeight:n.scrollHeight,scrollWidth:n.scrollWidth,deltaX:t.lastScrollLeft-n.scrollLeft,deltaY:t.lastScrollTop-n.scrollTop}),h.value&&(n.scrollTop<=g.value&&t.lastScrollTop-n.scrollTop>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"top"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollTop+n.offsetHeight+m.value>=n.scrollHeight&&t.lastScrollTop-n.scrollTop<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"bottom"}),t.lastScrollToLowerTime=e.timeStamp)),f.value&&(n.scrollLeft<=g.value&&t.lastScrollLeft-n.scrollLeft>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"left"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollLeft+n.offsetWidth+m.value>=n.scrollWidth&&t.lastScrollLeft-n.scrollLeft<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"right"}),t.lastScrollToLowerTime=e.timeStamp)),t.lastScrollTop=n.scrollTop,t.lastScrollLeft=n.scrollLeft}function b(t){h.value&&(e.scrollWithAnimation?v(t,"y"):a.value.scrollTop=t)}function _(t){f.value&&(e.scrollWithAnimation?v(t,"x"):a.value.scrollLeft=t)}function w(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error(`id error: scroll-into-view=${t}`);let n=i.value.querySelector("#"+t);if(n){let t=a.value.getBoundingClientRect(),o=n.getBoundingClientRect();if(f.value){let n=o.left-t.left,r=a.value.scrollLeft+n;e.scrollWithAnimation?v(r,"x"):a.value.scrollLeft=r}if(h.value){let n=o.top-t.top,r=a.value.scrollTop+n;e.scrollWithAnimation?v(r,"y"):a.value.scrollTop=r}}}}function x(e,t){s.value.style.transition="",s.value.style.webkitTransition="",s.value.style.transform="",s.value.style.webkitTransform="";let n=a.value;"x"===t?(n.style.overflowX=f.value?"auto":"hidden",n.scrollLeft=e):"y"===t&&(n.style.overflowY=h.value?"auto":"hidden",n.scrollTop=e),s.value.removeEventListener("transitionend",p),s.value.removeEventListener("webkitTransitionEnd",p)}function T(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,r("refresherrefresh",{},{dy:k.y-S.y}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(d=!1,r("refresherrestore",{},{dy:k.y-S.y})),"refresherabort"===n&&d&&(d=!1,r("refresherabort",{},{dy:k.y-S.y}))}t.refreshState=n}}let S={x:0,y:0},k={x:0,y:e.refresherThreshold};return jo((()=>{Cn((()=>{b(n.value),_(o.value)})),w(e.scrollIntoView);let i=function(e){e.preventDefault(),e.stopPropagation(),y(e)},s=null,l=function(n){if(null===S)return;let o=n.touches[0].pageX,i=n.touches[0].pageY,l=a.value;if(Math.abs(o-S.x)>Math.abs(i-S.y))if(f.value){if(0===l.scrollLeft&&o>S.x)return void(s=!1);if(l.scrollWidth===l.offsetWidth+l.scrollLeft&&o<S.x)return void(s=!1);s=!0}else s=!1;else if(h.value)if(0===l.scrollTop&&i>S.y)s=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(l.scrollHeight===l.offsetHeight+l.scrollTop&&i<S.y)return void(s=!1);s=!0}else s=!1;if(s&&n.stopPropagation(),0===l.scrollTop&&1===n.touches.length&&T("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const o=i-S.y;0===u&&(u=i),c?(t.refresherHeight=o+e.refresherThreshold,d=!1):(t.refresherHeight=i-u,t.refresherHeight>0&&(d=!0,r("refresherpulling",n,{deltaY:o,dy:o})))}},p=function(e){1===e.touches.length&&(S={x:e.touches[0].pageX,y:e.touches[0].pageY})},g=function(n){k={x:n.changedTouches[0].pageX,y:n.changedTouches[0].pageY},t.refresherHeight>=e.refresherThreshold?T("refreshing"):T("refresherabort"),S={x:0,y:0},k={x:0,y:e.refresherThreshold}};a.value.addEventListener("touchstart",p,Hg),a.value.addEventListener("touchmove",l,be(!1)),a.value.addEventListener("scroll",i,be(!1)),a.value.addEventListener("touchend",g,Hg),Fo((()=>{a.value.removeEventListener("touchstart",p),a.value.removeEventListener("touchmove",l),a.value.removeEventListener("scroll",i),a.value.removeEventListener("touchend",g)}))})),Mo((()=>{h.value&&(a.value.scrollTop=t.lastScrollTop),f.value&&(a.value.scrollLeft=t.lastScrollLeft)})),to(n,(e=>{b(e)})),to(o,(e=>{_(e)})),to((()=>e.scrollIntoView),(e=>{w(e)})),to((()=>e.refresherTriggered),(e=>{!0===e?T("refreshing"):!1===e&&T("restore")})),{realScrollX:f,realScrollY:h,_scrollTopChanged:b,_scrollLeftChanged:_}}(e,c,u,d,l,r,i,s,t),m=Ei((()=>{let e="";return p.value?e+="overflow-x:auto;":e+="overflow-x:hidden;",f.value?e+="overflow-y:auto;":e+="overflow-y:hidden;",e})),v=Ei((()=>{let t="uni-scroll-view";return!1===e.showScrollbar&&(t+=" uni-scroll-view-scrollbar-hidden"),t}));return o({$getMain:()=>i.value}),()=>{const{refresherEnabled:t,refresherBackground:o,refresherDefaultStyle:l,refresherThreshold:u}=e,{refresherHeight:d,refreshState:p}=c;return ri("uni-scroll-view",{ref:r},[ri("div",{ref:a,class:"uni-scroll-view"},[ri("div",{ref:i,style:m.value,class:v.value},[t?ri(Wg,{refreshState:p,refresherHeight:d,refresherThreshold:u,refresherDefaultStyle:l,refresherBackground:o},{default:()=>["none"==l?n.refresher&&n.refresher():null]},8,["refreshState","refresherHeight","refresherThreshold","refresherDefaultStyle","refresherBackground"]):null,ri("div",{ref:s,class:"uni-scroll-view-content"},[n.default&&n.default()],512)],6)],512)],512)}}});function Xg(e,t,n,o,r,i){function a(){c&&(clearTimeout(c),c=null)}let s,l,c=null,u=!0,d=0,p=1,f=null,h=!1,g=0,m="";const v=Ei((()=>n.value.length>t.displayMultipleItems)),y=Ei((()=>e.circular&&v.value));function b(r){Math.floor(2*d)===Math.floor(2*r)&&Math.ceil(2*d)===Math.ceil(2*r)||y.value&&function(o){if(!u)for(let r=n.value,i=r.length,a=o+t.displayMultipleItems,s=0;s<i;s++){const t=r[s],n=Math.floor(o/i)*i+s,l=n+i,c=n-i,u=Math.max(o-(n+1),n-a,0),d=Math.max(o-(l+1),l-a,0),p=Math.max(o-(c+1),c-a,0),f=Math.min(u,d,p),h=[n,l,c][[u,d,p].indexOf(f)];t.updatePosition(h,e.vertical)}}(r);const a="translate("+(e.vertical?"0":100*-r*p+"%")+", "+(e.vertical?100*-r*p+"%":"0")+") translateZ(0)",l=o.value;if(l&&(l.style.webkitTransform=a,l.style.transform=a),d=r,!s){if(r%1==0)return;s=r}r-=Math.floor(s);const c=n.value;r<=-(c.length-1)?r+=c.length:r>=c.length&&(r-=c.length),r=s%1>.5||s<0?r-1:r,i("transition",{},{dx:e.vertical?0:r*l.offsetWidth,dy:e.vertical?r*l.offsetHeight:0})}function _(e){const o=n.value.length;if(!o)return-1;const r=(Math.round(e)%o+o)%o;if(y.value){if(o<=t.displayMultipleItems)return 0}else if(r>o-t.displayMultipleItems)return o-t.displayMultipleItems;return r}function w(){f=null}function x(){if(!f)return void(h=!1);const e=f,o=e.toPos,r=e.acc,a=e.endTime,c=e.source,u=a-Date.now();if(u<=0){b(o),f=null,h=!1,s=null;const e=n.value[t.current];if(e){const n=e.getItemId();i("animationfinish",{},{current:t.current,currentItemId:n,source:c})}return}b(o+r*u*u/2),l=requestAnimationFrame(x)}function T(e,o,r){w();const i=t.duration,a=n.value.length;let s=d;if(y.value)if(r<0){for(;s<e;)s+=a;for(;s-a>e;)s-=a}else if(r>0){for(;s>e;)s-=a;for(;s+a<e;)s+=a;s+a-e<e-s&&(s+=a)}else{for(;s+a<e;)s+=a;for(;s-a>e;)s-=a;s+a-e<e-s&&(s+=a)}else"click"===o&&(e=e+t.displayMultipleItems-1<a?e:0);f={toPos:e,acc:2*(s-e)/(i*i),endTime:Date.now()+i,source:o},h||(h=!0,l=requestAnimationFrame(x))}function S(){a();const e=n.value,o=function(){c=null,m="autoplay",y.value?t.current=_(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,T(t.current,"autoplay",y.value?1:0),c=setTimeout(o,t.interval)};u||e.length<=t.displayMultipleItems||(c=setTimeout(o,t.interval))}function k(e){e?S():a()}return to([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{let o=-1;if(e.currentItemId)for(let t=0,r=n.value;t<r.length;t++){if(r[t].getItemId()===e.currentItemId){o=t;break}}o<0&&(o=Math.round(e.current)||0),o=o<0?0:o,t.current!==o&&(m="",t.current=o)})),to([()=>e.vertical,()=>y.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){a(),f&&(b(f.toPos),f=null);const r=n.value;for(let t=0;t<r.length;t++)r[t].updatePosition(t,e.vertical);p=1;const i=o.value;if(1===t.displayMultipleItems&&r.length){const e=r[0].getBoundingClientRect(),t=i.getBoundingClientRect();p=e.width/t.width,p>0&&p<1||(p=1)}const s=d;d=-2;const l=t.current;l>=0?(u=!1,t.userTracking?(b(s+l-g),g=l):(b(l),e.autoplay&&S())):(u=!0,b(-t.displayMultipleItems-1))})),to((()=>t.interval),(()=>{c&&(a(),S())})),to((()=>t.current),((e,o)=>{!function(e,o){const r=m;m="";const a=n.value;if(!r){const t=a.length;T(e,"",y.value&&o+(t-e)%t>t/2?1:0)}const s=a[e];if(s){const e=t.currentItemId=s.getItemId();i("change",{},{current:t.current,currentItemId:e,source:r})}}(e,o),r("update:current",e)})),to((()=>t.currentItemId),(e=>{r("update:currentItemId",e)})),to((()=>e.autoplay&&!t.userTracking),k),k(e.autoplay&&!t.userTracking),jo((()=>{let r=!1,i=0,s=0;function l(e){t.userTracking=!1;const n=i/Math.abs(i);let o=0;!e&&Math.abs(i)>.2&&(o=.5*n);const r=_(d+o);e?b(g):(m="touch",t.current=r,T(r,"touch",0!==o?o:0===r&&y.value&&d>=1?1:0))}mg(o.value,(c=>{if(!e.disableTouch&&!u){if("start"===c.detail.state)return t.userTracking=!0,r=!1,a(),g=d,i=0,s=Date.now(),void w();if("end"===c.detail.state)return l(!1);if("cancel"===c.detail.state)return l(!0);if(t.userTracking){if(!r){r=!0;const n=Math.abs(c.detail.dx),o=Math.abs(c.detail.dy);if((n>=o&&e.vertical||n<=o&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&S())}return function(r){const a=s;s=Date.now();const l=n.value.length-t.displayMultipleItems;function c(e){return.5-.25/(e+.5)}function u(e,t){let n=g+e;i=.6*i+.4*t,y.value||(n<0||n>l)&&(n<0?n=-c(-n):n>l&&(n=l+c(n-l)),i=0),b(n)}const d=s-a||1,p=o.value;e.vertical?u(-r.dy/p.offsetHeight,-r.ddy/d):u(-r.dx/p.offsetWidth,-r.ddx/d)}(c.detail),!1}}}))})),Uo((()=>{a(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){T(t.current=e,m="click",y.value?1:0)},circularEnabled:y,swiperEnabled:v}}const Gg=lu({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const o=sn(null),r=du(o,n),i=sn(null),a=sn(null),s=function(e){return Ut({interval:Ei((()=>{const t=Number(e.interval);return isNaN(t)?5e3:t})),duration:Ei((()=>{const t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:Ei((()=>{const t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),l=Ei((()=>{let t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:gc(e.previousMargin,!0),bottom:gc(e.nextMargin,!0)}:{top:0,bottom:0,left:gc(e.previousMargin,!0),right:gc(e.nextMargin,!0)}),t})),c=Ei((()=>{const t=Math.abs(100/s.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}}));let u=[];const d=[],p=sn([]);function f(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n instanceof Element||(n=n.el);const o=d.find((e=>n===e.rootRef.value));o&&e.push(Zt(o))}p.value=e}br("addSwiperContext",(function(e){d.push(e),f()}));br("removeSwiperContext",(function(e){const t=d.indexOf(e);t>=0&&(d.splice(t,1),f())}));const{onSwiperDotClick:h,circularEnabled:g,swiperEnabled:m}=Xg(e,s,p,a,n,r);let v=()=>null;return v=Jg(o,e,s,h,p,g,m),()=>{const n=t.default&&t.default();return u=ug(n),ri("uni-swiper",{ref:o},[ri("div",{ref:i,class:"uni-swiper-wrapper"},[ri("div",{class:"uni-swiper-slides",style:l.value},[ri("div",{ref:a,class:"uni-swiper-slide-frame",style:c.value},[n],4)],4),e.indicatorDots&&ri("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[p.value.map(((t,n,o)=>ri("div",{onClick:()=>h(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<s.current+s.displayMultipleItems&&n>=s.current||n<s.current+s.displayMultipleItems-o.length},style:{background:n===s.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),v()],512)],512)}}}),Jg=(e,t,n,o,r,i,a)=>{let s=!1,l=!1,u=!1,d=sn(!1);function p(e,n){const o=e.currentTarget;o&&(o.style.backgroundColor="over"===n?t.navigationActiveColor:"")}Zn((()=>{s="auto"===t.navigation,d.value=!0!==t.navigation||s,b()})),Zn((()=>{const e=r.value.length,t=!i.value;l=0===n.current&&t,u=n.current===e-1&&t||t&&n.current+n.displayMultipleItems>=e,a.value||(l=!0,u=!0,s&&(d.value=!0))}));const f={onMouseover:e=>p(e,"over"),onMouseout:e=>p(e,"out")};function h(e,t,a){if(e.stopPropagation(),a)return;const s=r.value.length;let l=n.current;switch(t){case"prev":l--,l<0&&i.value&&(l=s-1);break;case"next":l++,l>=s&&i.value&&(l=0)}o(l)}const g=()=>wc(yc,t.navigationColor,26);let m;const v=n=>{clearTimeout(m);const{clientX:o,clientY:r}=n,{left:i,right:a,top:s,bottom:l,width:c,height:u}=e.value.getBoundingClientRect();let p=!1;if(p=t.vertical?!(r-s<u/3||l-r<u/3):!(o-i<c/3||a-o<c/3),p)return m=setTimeout((()=>{d.value=p}),300);d.value=p},y=()=>{d.value=!0};function b(){e.value&&(e.value.removeEventListener("mousemove",v),e.value.removeEventListener("mouseleave",y),s&&(e.value.addEventListener("mousemove",v),e.value.addEventListener("mouseleave",y)))}return jo(b),function(){const e={"uni-swiper-navigation-hide":d.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?ri(jr,null,[ri("div",di({class:["uni-swiper-navigation uni-swiper-navigation-prev",c({"uni-swiper-navigation-disabled":l},e)],onClick:e=>h(e,"prev",l)},f),[g()],16,["onClick"]),ri("div",di({class:["uni-swiper-navigation uni-swiper-navigation-next",c({"uni-swiper-navigation-disabled":u},e)],onClick:e=>h(e,"next",u)},f),[g()],16,["onClick"])]):null}},Qg=lu({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=sn(null),o={rootRef:n,getItemId:()=>e.itemId,getBoundingClientRect:()=>n.value.getBoundingClientRect(),updatePosition(e,t){const o=t?"0":100*e+"%",r=t?100*e+"%":"0",i=n.value,a=`translate(${o},${r}) translateZ(0)`;i&&(i.style.webkitTransform=a,i.style.transform=a)}};return jo((()=>{const e=_r("addSwiperContext");e&&e(o)})),Uo((()=>{const e=_r("removeSwiperContext");e&&e(o)})),()=>ri("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),Kg={ensp:" ",emsp:" ",nbsp:" "};function Zg(e,t){return function(e,{space:t,decode:n}){let o="",r=!1;for(let i of e)t&&Kg[t]&&" "===i&&(i=Kg[t]),r?(o+="n"===i?"\n":"\\"===i?"\\":"\\"+i,r=!1):"\\"===i?r=!0:o+=i;return n?o.replace(/&nbsp;/g,Kg.nbsp).replace(/&ensp;/g,Kg.ensp).replace(/&emsp;/g,Kg.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):o}(e,t).split("\n")}const em=lu({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=sn(null);return()=>{const o=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==Vr){const n=Zg(t.children,{space:e.space,decode:e.decode}),r=n.length-1;n.forEach(((e,t)=>{(0!==t||e)&&o.push(ai(e)),t!==r&&o.push(ri("br"))}))}else o.push(t)})),ri("uni-text",{ref:n,selectable:!!e.selectable||null},[ri("span",null,o)],8,["selectable"])}}}),tm=c({},eg,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator:e=>om.concat("return").includes(e)}});let nm=!1;const om=["done","go","next","search","send"];const rm=lu({name:"Textarea",props:tm,emits:["confirm","linechange",...tg],setup(e,{emit:t,expose:n}){const o=sn(null),r=sn(null),{fieldRef:i,state:a,scopedAttrsState:s,fixDisabledColor:l,trigger:c}=rg(e,o,t),u=Ei((()=>a.value.split("\n"))),d=Ei((()=>om.includes(e.confirmType))),p=sn(0),f=sn(null);function h({height:e}){p.value=e}function g(e){"Enter"===e.key&&d.value&&e.preventDefault()}function m(t){if("Enter"===t.key&&d.value){!function(e){c("confirm",e,{value:a.value})}(t);const n=t.target;!e.confirmHold&&n.blur()}}return to((()=>p.value),(t=>{const n=o.value,i=f.value,a=r.value;let s=parseFloat(getComputedStyle(n).lineHeight);isNaN(s)&&(s=i.offsetHeight);var l=Math.round(t/s);c("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:l}),e.autoHeight&&(n.style.height="auto",a.style.height=t+"px")})),function(){const e="(prefers-color-scheme: dark)";nm=0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(e).media!==e}(),n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),a.value=e.value}}),()=>{let t=e.disabled&&l?ri("textarea",{key:"disabled-textarea",ref:i,value:a.value,tabindex:"-1",readonly:!!e.disabled,maxlength:a.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":nm},style:{overflowY:e.autoHeight?"hidden":"auto",...e.cursorColor&&{caretColor:e.cursorColor}},onFocus:e=>e.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):ri("textarea",{key:"textarea",ref:i,value:a.value,disabled:!!e.disabled,maxlength:a.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":nm},style:{overflowY:e.autoHeight?"hidden":"auto",...e.cursorColor&&{caretColor:e.cursorColor}},onKeydown:g,onKeyup:m},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return ri("uni-textarea",{ref:o},[ri("div",{ref:r,class:"uni-textarea-wrapper"},[ao(ri("div",di(s.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Gi,!a.value.length]]),ri("div",{ref:f,class:"uni-textarea-line"},[" "],512),ri("div",{class:"uni-textarea-compute"},[u.value.map((e=>ri("div",null,[e.trim()?e:"."]))),ri(Th,{initial:!0,onResize:h},null,8,["initial","onResize"])]),"search"===e.confirmType?ri("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[t],40,["onSubmit"]):t],512)],512)}}}),im=lu({name:"View",props:c({},pu),setup(e,{slots:t}){const n=sn(null),{hovering:o,binding:r}=fu(e);return()=>{const i=e.hoverClass;return i&&"none"!==i?ri("uni-view",di({class:o.value?i:"",ref:n},r),[t.default&&t.default()],16):ri("uni-view",{ref:n},[t.default&&t.default()],512)}}});function am(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function sm(e,t,n){e&&Rl(n||Cc(),e,(({type:e,data:n},o)=>{t(e,n,o)}))}function lm(e,t){e&&function(e,t){t=Dl(e,t),delete $l[t]}(t||Cc(),e)}function cm(e,t,n,o){const r=mi().proxy;jo((()=>{sm(t||am(r),e,o),!n&&t||to((()=>r.id),((t,n)=>{sm(am(r,t),e,o),lm(n&&am(r,n))}))})),Fo((()=>{lm(t||am(r),o)}))}let um=0;function dm(e){const t=xc(),n=mi().proxy,o=n.$options.name.toLowerCase(),r=e||n.id||"context"+um++;return jo((()=>{n.$el.__uniContextInfo={id:r,type:o,page:t}})),`${o}.${r}`}function pm(e,t,n,o){m(t)&&Bo(e,t.bind(n),o)}function fm(e,t,n){var o;const r=e.mpType||n.$mpType;if(r&&"component"!==r&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!m(t))&&(Ae.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];f(r)?r.forEach((e=>pm(o,e,n,t))):pm(o,r,n,t)}})),"page"===r)){t.__isVisible=!0;try{const e=t.attrs.__pageQuery;0,Pc(n,"onLoad",e),delete t.attrs.__pageQuery,"preloadPage"!==(null==(o=n.$page)?void 0:o.openType)&&Pc(n,"onShow")}catch(yC){console.error(yC.message+"\n"+yC.stack)}}}function hm(e,t,n){fm(e,t,n)}function gm(e,t,n){return e[t]=n}function mm(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function vm(e){return function(t,n,o){if(!n)throw t;const r=e._instance;if(!r||!r.proxy)throw t;Pc(r.proxy,"onError",t)}}function ym(e,t){return e?[...new Set([].concat(e,t))]:t}function bm(e){const t=e._context.config;var n;t.errorHandler=Ie(e,vm),n=t.optionMergeStrategies,Ae.forEach((e=>{n[e]=ym}));const o=t.globalProperties;o.$set=gm,o.$applyOptions=hm,o.$callMethod=mm,function(e){Me=e,Pe.forEach((t=>t(e)))}(e)}const _m=fc("upm");function wm(){return _r(_m)}function xm(e){const t=function(e){return Ut(function(e){{const{enablePullDownRefresh:t,navigationBar:n}=e;if(t){const t=function(e){return e.offset&&(e.offset=gc(e.offset)),e.height&&(e.height=gc(e.height)),e.range&&(e.range=gc(e.range)),e}(c({support:!0,color:"#2BD009",style:"circle",height:70,range:150,offset:0},e.pullToRefresh)),{type:o,style:r}=n;"custom"!==r&&"transparent"!==o&&(t.offset+=44+ic.top),e.pullToRefresh=t}}{const{navigationBar:t}=e,{titleSize:n,titleColor:o,backgroundColor:r}=t;t.titleText=t.titleText||"",t.type=t.type||"default",t.titleSize=n||"16px",t.titleColor=o||"#000000",t.backgroundColor=r||"#F8F8F8"}if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==qm().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(Mc(ol().meta,e)))))}(e);return br(_m,t),t}function Tm(){return ol()}function Sm(){return history.state&&history.state.__id__||1}let km;function Cm(){var e;return km||(km=__uniConfig.tabBar&&Ut((e=__uniConfig.tabBar,hl()&&e.list&&e.list.forEach((e=>{yl(e,["text"])})),e))),km}const Em=window.CSS&&window.CSS.supports;function Am(e){return Em&&(Em(e)||Em.apply(window.CSS,e.split(":")))}const Mm=Am("--a:0"),Pm=Am("top:env(a)"),Im=Am("top:constant(a)"),Lm=Am("backdrop-filter:blur(10px)"),Om={"css.var":Mm,"css.env":Pm,"css.constant":Im,"css.backdrop-filter":Lm},$m=Td(0,(e=>!p(Om,e)||Om[e])),Dm=(()=>Pm?"env":Im?"constant":"")();function Rm(e){return Dm?`calc(${e}px + ${Dm}(safe-area-inset-bottom))`:`${e}px`}const Bm=new Map;function Nm(){return Bm}function qm(){const e=[],t=Bm.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function jm(e,t=!0){const n=Bm.get(e);n.$.__isUnload=!0,Pc(n,"onUnload"),Bm.delete(e),t&&function(e){const t=Wm.get(e);t&&(Wm.delete(e),Hm.pruneCacheEntry(t))}(e)}let zm=Sm();function Vm(e){const t=wm();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),function(e,t,n,o,r,i){const{id:a,route:s}=o,l=Re(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:a,path:ce(s),route:s,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#ffffff"===l?"light":"dark"}}("navigateTo",n,{},t)}function Fm(e){const t=Vm(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",e.$fontFamilySet=new Set,t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),Bm.set(Um(t.path,t.id),e)}function Um(e,t){return e+"$$"+t}const Wm=new Map,Hm={get:e=>Wm.get(e),set(e,t){!function(e){const t=parseInt(e.split("$$")[1]);if(!t)return;Hm.forEach(((e,n)=>{const o=parseInt(n.split("$$")[1]);if(o&&o>t){if(function(e){return"tabBar"===e.props.type}(e))return;Hm.delete(n),Hm.pruneCacheEntry(e),Cn((()=>{Bm.forEach(((e,t)=>{e.$.isUnmounted&&Bm.delete(t)}))}))}}))}(e),Wm.set(e,t)},delete(e){Wm.get(e)&&Wm.delete(e)},forEach(e){Wm.forEach(e)}};function Ym(e,t){!function(e){const t=Gm(e),{body:n}=document;Jm&&n.removeAttribute(Jm),t&&n.setAttribute(t,""),Jm=t}(e),function(e){let t=0,n=0;if("custom"!==e.navigationBar.style&&["default","float"].indexOf(e.navigationBar.type)>-1&&(t=44),e.isTabBar){const e=Cm();e.shown&&(n=parseInt(e.height))}var o;pc({"--window-top":(o=t,Dm?`calc(${o}px + ${Dm}(safe-area-inset-top))`:`${o}px`),"--window-bottom":Rm(n)})}(t),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),function(e,t){document.removeEventListener("touchmove",Ic),Qm&&document.removeEventListener("scroll",Qm);if(t.disableScroll)return document.addEventListener("touchmove",Ic);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!n&&!o&&!r)return;const i={},a=e.proxy.$page.id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&Sx.publishHandler("onPageScroll",{scrollTop:o},e),n&&Sx.emit(e+".onPageScroll",{scrollTop:o})}}(a,n,r));o&&(i.onReachBottomDistance=t.onReachBottomDistance||50,i.onReachBottom=()=>Sx.publishHandler("onReachBottom",{},a));Qm=$c(i),requestAnimationFrame((()=>document.addEventListener("scroll",Qm)))}(e,t)}function Xm(e){const t=Gm(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function Gm(e){return e.type.__scopeId}let Jm,Qm;function Km(e){const t=nl({history:tv(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:ev});t.beforeEach(((e,t)=>{var n;e&&t&&e.meta.isTabBar&&t.meta.isTabBar&&(n=t.meta.tabBarIndex,"undefined"!=typeof window&&(Zm[n]={left:window.pageXOffset,top:window.pageYOffset}))})),e.router=t,e.use(t)}let Zm=Object.create(null);const ev=(e,t,n)=>{if(n)return n;if(e&&t&&e.meta.isTabBar&&t.meta.isTabBar){const t=(o=e.meta.tabBarIndex,Zm[o]);if(t)return t}return{left:0,top:0};var o};function tv(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=(n=e,(n=location.host?n||location.pathname+location.search:"").includes("#")||(n+="#"),gs(n));var n;return t.listen(((e,t,n)=>{"back"===n.direction&&function(e=1){const t=qm(),n=t.length-1,o=n-e;for(let r=n;r>o;r--){const e=t[r].$page;jm(Um(e.path,e.id),!1)}}(Math.abs(n.delta))})),t}const nv={install(e){bm(e),Hc(e),ou(e),e.config.warnHandler||(e.config.warnHandler=ov),Km(e)}};function ov(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n),console.warn(...o)}const rv={class:"uni-async-loading"},iv=ri("i",{class:"uni-loading"},null,-1),av=cu({name:"AsyncLoading",render:()=>(Hr(),Qr("div",rv,[iv]))});function sv(){window.location.reload()}const lv=cu({name:"AsyncError",setup(){wl();const{t:e}=bl();return()=>ri("div",{class:"uni-async-error",onClick:sv},[e("uni.async.error")],8,["onClick"])}});let cv;function uv(){return cv}function dv(e){cv=e,Object.defineProperty(cv.$.ctx,"$children",{get:()=>qm().map((e=>e.$vm))});const t=cv.$.appContext.app;t.component(av.name)||t.component(av.name,av),t.component(lv.name)||t.component(lv.name,lv),function(e){e.$vm=e,e.$mpType="app";const t=sn(bl().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(cv),function(e,t){const n=e.$options||{};n.globalData=c(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(cv),tu(),Xl()}function pv(e,{clone:t,init:n,setup:o,before:r}){t&&(e=c({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=mi();n(r.proxy);const a=o(r);if(i)return i(a||e,t)},e}function fv(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?pv(e.default,t):pv(e,t)}function hv(e){return fv(e,{clone:!0,init:Fm,setup(e){e.$pageInstance=e;const t=Tm(),n=xe(t.query);e.attrs.__pageQuery=n,e.proxy.$page.options=n,e.proxy.options=n;const o=wm();var r,i,a;return qo((()=>{Ym(e,o)})),jo((()=>{Xm(e);const{onReady:n}=e;n&&$(n),yv(t)})),Io((()=>{if(!e.__isVisible){Ym(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&$(n),Cn((()=>{yv(t)}))}}),"ba",r),function(e,t){Io(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;const{onHide:t}=e;t&&$(t)}})),i=o.id,Sx.subscribe(Dl(i,"invokeViewApi"),a?a(Bl):Bl),Fo((()=>{!function(e){Sx.unsubscribe(Dl(e,"invokeViewApi")),Object.keys($l).forEach((t=>{0===t.indexOf(e+".")&&delete $l[t]}))}(o.id)})),n}})}function gv(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=uni.getSystemInfoSync(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";Cx.emit("onResize",{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function mv(e){T(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&Cx.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function vv(){const{emit:e}=Cx;"visible"===document.visibilityState?e("onAppEnterForeground",xh()):e("onAppEnterBackground")}function yv(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&Pc("onTabItemTap",{index:n,text:t,pagePath:o})}function bv(e){e=e>0&&e<1/0?e:0;const t=Math.floor(e/3600),n=Math.floor(e%3600/60),o=Math.floor(e%3600%60),r=(t<10?"0":"")+t;let i=(n<10?"0":"")+n+":"+((o<10?"0":"")+o);return"00"!==r&&(i=r+":"+i),i}function _v(e,t,n){const o=Ut({gestureType:"none",volumeOld:0,volumeNew:0,currentTimeOld:0,currentTimeNew:0}),r={x:0,y:0};return{state:o,onTouchstart:function(e){const t=e.targetTouches[0];r.x=t.pageX,r.y=t.pageY,o.gestureType="none",o.volumeOld=0,o.currentTimeOld=o.currentTimeNew=0},onTouchmove:function(i){function a(){i.stopPropagation(),i.preventDefault()}n.fullscreen&&a();const s=o.gestureType;if("stop"===s)return;const l=i.targetTouches[0],c=l.pageX,u=l.pageY,d=r,p=t.value;if("progress"===s?function(e){const n=t.value.duration;let r=e/600*n+o.currentTimeOld;r<0?r=0:r>n&&(r=n);o.currentTimeNew=r}(c-d.x):"volume"===s&&function(e){const n=t.value,r=o.volumeOld;let i;"number"==typeof r&&(i=r-e/200,i<0?i=0:i>1&&(i=1),n.volume=i,o.volumeNew=i)}(u-d.y),"none"===s)if(Math.abs(c-d.x)>Math.abs(u-d.y)){if(!e.enableProgressGesture)return void(o.gestureType="stop");o.gestureType="progress",o.currentTimeOld=o.currentTimeNew=p.currentTime,n.fullscreen||a()}else{if(!e.pageGesture)return void(o.gestureType="stop");o.gestureType="volume",o.volumeOld=p.volume,n.fullscreen||a()}},onTouchend:function(e){const n=t.value;"none"!==o.gestureType&&"stop"!==o.gestureType&&(e.stopPropagation(),e.preventDefault()),"progress"===o.gestureType&&o.currentTimeOld!==o.currentTimeNew&&(n.currentTime=o.currentTimeNew),o.gestureType="none"}}}const wv=lu({name:"Video",props:{id:{type:String,default:""},src:{type:String,default:""},duration:{type:[Number,String],default:""},controls:{type:[Boolean,String],default:!0},danmuList:{type:Array,default:()=>[]},danmuBtn:{type:[Boolean,String],default:!1},enableDanmu:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},loop:{type:[Boolean,String],default:!1},muted:{type:[Boolean,String],default:!1},objectFit:{type:String,default:"contain"},poster:{type:String,default:""},direction:{type:[String,Number],default:""},showProgress:{type:Boolean,default:!0},initialTime:{type:[String,Number],default:0},showFullscreenBtn:{type:[Boolean,String],default:!0},pageGesture:{type:[Boolean,String],default:!1},enableProgressGesture:{type:[Boolean,String],default:!0},showPlayBtn:{type:[Boolean,String],default:!0},showCenterPlayBtn:{type:[Boolean,String],default:!0}},emits:["fullscreenchange","progress","loadedmetadata","waiting","error","play","pause","ended","timeupdate"],setup(e,{emit:t,attrs:n,slots:o}){const r=sn(null),i=sn(null),a=du(r,t),{state:s}=Gh(),{$attrs:l}=cg({excludeListeners:!0}),{t:c}=bl();Pl();const{videoRef:u,state:d,play:p,pause:h,stop:g,seek:m,playbackRate:v,toggle:y,onDurationChange:b,onLoadedMetadata:_,onProgress:w,onWaiting:x,onVideoError:T,onPlay:S,onPause:k,onEnded:C,onTimeUpdate:E}=function(e,t,n){const o=sn(null),r=Ei((()=>Tu(e.src))),i=Ei((()=>"true"===e.muted||!0===e.muted)),a=Ut({start:!1,src:r,playing:!1,currentTime:0,duration:0,progress:0,buffered:0,muted:i});function s(e){const t=e.target,n=t.buffered;n.length&&(a.buffered=n.end(n.length-1)/t.duration*100)}function l(){o.value.pause()}function c(e){const t=o.value;"number"!=typeof(e=Number(e))||isNaN(e)||(t.currentTime=e)}return to((()=>r.value),(()=>{a.playing=!1,a.currentTime=0})),to((()=>a.buffered),(e=>{n("progress",{},{buffered:e})})),to((()=>i.value),(e=>{o.value.muted=e})),{videoRef:o,state:a,play:function(){const e=o.value;a.start=!0,e.play()},pause:l,stop:function(){c(0),l()},seek:c,playbackRate:function(e){o.value.playbackRate=e},toggle:function(){const e=o.value;a.playing?e.pause():e.play()},onDurationChange:function({target:e}){a.duration=e.duration},onLoadedMetadata:function(t){const o=Number(e.initialTime)||0,r=t.target;o>0&&(r.currentTime=o),n("loadedmetadata",t,{width:r.videoWidth,height:r.videoHeight,duration:r.duration}),s(t)},onProgress:s,onWaiting:function(e){n("waiting",e,{})},onVideoError:function(e){a.playing=!1,n("error",e,{})},onPlay:function(e){a.start=!0,a.playing=!0,n("play",e,{})},onPause:function(e){a.playing=!1,n("pause",e,{})},onEnded:function(e){a.playing=!1,n("ended",e,{})},onTimeUpdate:function(e){const t=e.target,o=a.currentTime=t.currentTime;n("timeupdate",e,{currentTime:o,duration:t.duration})}}}(e,0,a),{state:A,danmuRef:M,updateDanmu:P,toggleDanmu:I,sendDanmu:L}=function(e,t){const n=sn(null),o=Ut({enable:Boolean(e.enableDanmu)});let r={time:0,index:-1};const i=f(e.danmuList)?JSON.parse(JSON.stringify(e.danmuList)):[];function a(e){const t=document.createElement("p");t.className="uni-video-danmu-item",t.innerText=e.text;let o=`bottom: ${100*Math.random()}%;color: ${e.color};`;t.setAttribute("style",o),n.value.appendChild(t),setTimeout((function(){o+="left: 0;-webkit-transform: translateX(-100%);transform: translateX(-100%);",t.setAttribute("style",o),setTimeout((function(){t.remove()}),4e3)}),17)}return i.sort((function(e,t){return(e.time||0)-(t.time||0)})),{state:o,danmuRef:n,updateDanmu:function(e){const n=e.target.currentTime,s=r,l={time:n,index:s.index};if(n>s.time)for(let r=s.index+1;r<i.length;r++){const e=i[r];if(!(n>=(e.time||0)))break;l.index=r,t.playing&&o.enable&&a(e)}else if(n<s.time)for(let t=s.index-1;t>-1&&n<=(i[t].time||0);t--)l.index=t-1;r=l},toggleDanmu:function(){o.enable=!o.enable},sendDanmu:function(e){i.splice(r.index+1,0,{text:String(e.text),color:e.color,time:t.currentTime||0})}}}(e,d),{state:O,onFullscreenChange:$,emitFullscreenChange:D,toggleFullscreen:R,requestFullScreen:B,exitFullScreen:N}=function(e,t,n,o,r){const i=Ut({fullscreen:!1}),a=/^Apple/.test(navigator.vendor);function s(t){i.fullscreen=t,e("fullscreenchange",{},{fullScreen:t,direction:"vertical"})}function l(e){const i=r.value,l=t.value,c=n.value;let u;e?!document.fullscreenEnabled&&!document.webkitFullscreenEnabled||a&&!o.userAction?c.webkitEnterFullScreen?c.webkitEnterFullScreen():(u=!0,l.remove(),l.classList.add("uni-video-type-fullscreen"),document.body.appendChild(l)):l[document.fullscreenEnabled?"requestFullscreen":"webkitRequestFullscreen"]():document.fullscreenEnabled||document.webkitFullscreenEnabled?document.fullscreenElement?document.exitFullscreen():document.webkitFullscreenElement&&document.webkitExitFullscreen():c.webkitExitFullScreen?c.webkitExitFullScreen():(u=!0,l.remove(),l.classList.remove("uni-video-type-fullscreen"),i.appendChild(l)),u&&s(e)}function c(){l(!1)}return Fo(c),{state:i,onFullscreenChange:function(e,t){t&&document.fullscreenEnabled||s(!(!document.fullscreenElement&&!document.webkitFullscreenElement))},emitFullscreenChange:s,toggleFullscreen:l,requestFullScreen:function(){l(!0)},exitFullScreen:c}}(a,i,u,s,r),{state:q,onTouchstart:j,onTouchend:z,onTouchmove:V}=_v(e,u,O),{state:F,progressRef:U,ballRef:W,clickProgress:H,toggleControls:Y}=function(e,t,n){const o=sn(null),r=sn(null),i=Ei((()=>e.showCenterPlayBtn&&!t.start)),a=sn(!0),s=Ei((()=>!i.value&&e.controls&&a.value)),l=Ut({touching:!1,controlsTouching:!1,centerPlayBtnShow:i,controlsShow:s,controlsVisible:a});let c;function u(){c=setTimeout((()=>{l.controlsVisible=!1}),3e3)}function d(){c&&(clearTimeout(c),c=null)}return Fo((()=>{c&&clearTimeout(c)})),to((()=>l.controlsShow&&t.playing&&!l.controlsTouching),(e=>{e?u():d()})),to([()=>t.currentTime,()=>{e.duration}],(function(){l.touching||(t.progress=t.currentTime/t.duration*100)})),jo((()=>{const e=be(!1);let i,a,s,c=!0;const u=r.value;function d(e){const n=e.targetTouches[0],r=n.pageX,l=n.pageY;if(c&&Math.abs(r-i)<Math.abs(l-a))return void p(e);c=!1;const u=o.value.offsetWidth;let d=s+(r-i)/u*100;d<0?d=0:d>100&&(d=100),t.progress=d,e.preventDefault(),e.stopPropagation()}function p(o){l.controlsTouching=!1,l.touching&&(u.removeEventListener("touchmove",d,e),c||(o.preventDefault(),o.stopPropagation(),n(t.duration*t.progress/100)),l.touching=!1)}u.addEventListener("touchstart",(n=>{l.controlsTouching=!0;const o=n.targetTouches[0];i=o.pageX,a=o.pageY,s=t.progress,c=!0,l.touching=!0,u.addEventListener("touchmove",d,e)})),u.addEventListener("touchend",p),u.addEventListener("touchcancel",p)})),{state:l,progressRef:o,ballRef:r,clickProgress:function(e){const r=o.value;let i=e.target,a=e.offsetX;for(;i&&i!==r;)a+=i.offsetLeft,i=i.parentNode;const s=r.offsetWidth;let l=0;a>=0&&a<=s&&(l=a/s,n(t.duration*l))},toggleControls:function(){l.controlsVisible=!l.controlsVisible},autoHideStart:u,autoHideEnd:d}}(e,d,m);return function(e,t,n,o,r,i,a,s){const l={play:e,stop:n,pause:t,seek:o,sendDanmu:r,playbackRate:i,requestFullScreen:a,exitFullScreen:s};cm(((e,t)=>{let n;switch(e){case"seek":n=t.position;break;case"sendDanmu":n=t;break;case"playbackRate":n=t.rate}e in l&&l[e](n)}),dm(),!0)}(p,h,g,m,L,v,B,N),()=>ri("uni-video",{ref:r,id:e.id,onClick:Y},[ri("div",{ref:i,class:"uni-video-container",onTouchstart:j,onTouchend:z,onTouchmove:V,onFullscreenchange:wa($,["stop"]),onWebkitfullscreenchange:wa((e=>$(e,!0)),["stop"])},[ri("video",di({ref:u,style:{"object-fit":e.objectFit},muted:!!e.muted,loop:!!e.loop,src:d.src,poster:e.poster,autoplay:!!e.autoplay},l.value,{class:"uni-video-video","webkit-playsinline":!0,playsinline:!0,onDurationchange:b,onLoadedmetadata:_,onProgress:w,onWaiting:x,onError:T,onPlay:S,onPause:k,onEnded:C,onTimeupdate:e=>{E(e),P(e)},onWebkitbeginfullscreen:()=>D(!0),onX5videoenterfullscreen:()=>D(!0),onWebkitendfullscreen:()=>D(!1),onX5videoexitfullscreen:()=>D(!1)}),null,16,["muted","loop","src","poster","autoplay","webkit-playsinline","playsinline","onDurationchange","onLoadedmetadata","onProgress","onWaiting","onError","onPlay","onPause","onEnded","onTimeupdate","onWebkitbeginfullscreen","onX5videoenterfullscreen","onWebkitendfullscreen","onX5videoexitfullscreen"]),ao(ri("div",{class:"uni-video-bar uni-video-bar-full",onClick:wa((()=>{}),["stop"])},[ri("div",{class:"uni-video-controls"},[ao(ri("div",{class:{"uni-video-control-button":!0,"uni-video-control-button-play":!d.playing,"uni-video-control-button-pause":d.playing},onClick:wa(y,["stop"])},null,10,["onClick"]),[[Gi,e.showPlayBtn]]),ao(ri("div",{class:"uni-video-current-time"},[bv(d.currentTime)],512),[[Gi,e.showProgress]]),ao(ri("div",{ref:U,class:"uni-video-progress-container",onClick:wa(H,["stop"])},[ri("div",{class:"uni-video-progress"},[ri("div",{style:{width:d.buffered+"%"},class:"uni-video-progress-buffered"},null,4),ri("div",{ref:W,style:{left:d.progress+"%"},class:"uni-video-ball"},[ri("div",{class:"uni-video-inner"},null)],4)])],8,["onClick"]),[[Gi,e.showProgress]]),ao(ri("div",{class:"uni-video-duration"},[bv(Number(e.duration)||d.duration)],512),[[Gi,e.showProgress]])]),ao(ri("div",{class:{"uni-video-danmu-button":!0,"uni-video-danmu-button-active":A.enable},onClick:wa(I,["stop"])},[c("uni.video.danmu")],10,["onClick"]),[[Gi,e.danmuBtn]]),ao(ri("div",{class:{"uni-video-fullscreen":!0,"uni-video-type-fullscreen":O.fullscreen},onClick:wa((()=>R(!O.fullscreen)),["stop"])},null,10,["onClick"]),[[Gi,e.showFullscreenBtn]])],8,["onClick"]),[[Gi,F.controlsShow]]),ao(ri("div",{ref:M,style:"z-index: 0;",class:"uni-video-danmu"},null,512),[[Gi,d.start&&A.enable]]),F.centerPlayBtnShow&&ri("div",{class:"uni-video-cover",onClick:wa((()=>{}),["stop"])},[ri("div",{class:"uni-video-cover-play-button",onClick:wa(p,["stop"])},null,8,["onClick"]),ri("p",{class:"uni-video-cover-duration"},[bv(Number(e.duration)||d.duration)])],8,["onClick"]),ri("div",{class:{"uni-video-toast":!0,"uni-video-toast-volume":"volume"===q.gestureType}},[ri("div",{class:"uni-video-toast-title"},[c("uni.video.volume")]),ri("svg",{class:"uni-video-toast-icon",width:"200px",height:"200px",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},[ri("path",{d:"M475.400704 201.19552l0 621.674496q0 14.856192-10.856448 25.71264t-25.71264 10.856448-25.71264-10.856448l-190.273536-190.273536-149.704704 0q-14.856192 0-25.71264-10.856448t-10.856448-25.71264l0-219.414528q0-14.856192 10.856448-25.71264t25.71264-10.856448l149.704704 0 190.273536-190.273536q10.856448-10.856448 25.71264-10.856448t25.71264 10.856448 10.856448 25.71264zm219.414528 310.837248q0 43.425792-24.28416 80.851968t-64.2816 53.425152q-5.71392 2.85696-14.2848 2.85696-14.856192 0-25.71264-10.570752t-10.856448-25.998336q0-11.999232 6.856704-20.284416t16.570368-14.2848 19.427328-13.142016 16.570368-20.284416 6.856704-32.569344-6.856704-32.569344-16.570368-20.284416-19.427328-13.142016-16.570368-14.2848-6.856704-20.284416q0-15.427584 10.856448-25.998336t25.71264-10.570752q8.57088 0 14.2848 2.85696 39.99744 15.427584 64.2816 53.139456t24.28416 81.137664zm146.276352 0q0 87.422976-48.56832 161.41824t-128.5632 107.707392q-7.428096 2.85696-14.2848 2.85696-15.427584 0-26.284032-10.856448t-10.856448-25.71264q0-22.284288 22.284288-33.712128 31.997952-16.570368 43.425792-25.141248 42.283008-30.855168 65.995776-77.423616t23.712768-99.136512-23.712768-99.136512-65.995776-77.423616q-11.42784-8.57088-43.425792-25.141248-22.284288-11.42784-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 79.99488 33.712128 128.5632 107.707392t48.56832 161.41824zm146.276352 0q0 131.42016-72.566784 241.41312t-193.130496 161.989632q-7.428096 2.85696-14.856192 2.85696-14.856192 0-25.71264-10.856448t-10.856448-25.71264q0-20.570112 22.284288-33.712128 3.999744-2.285568 12.85632-5.999616t12.85632-5.999616q26.284032-14.2848 46.854144-29.140992 70.281216-51.996672 109.707264-129.705984t39.426048-165.132288-39.426048-165.132288-109.707264-129.705984q-20.570112-14.856192-46.854144-29.140992-3.999744-2.285568-12.85632-5.999616t-12.85632-5.999616q-22.284288-13.142016-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 120.563712 51.996672 193.130496 161.989632t72.566784 241.41312z"},null)]),ri("div",{class:"uni-video-toast-value"},[ri("div",{style:{width:100*q.volumeNew+"%"},class:"uni-video-toast-value-content"},[ri("div",{class:"uni-video-toast-volume-grids"},[Go(10,(()=>ri("div",{class:"uni-video-toast-volume-grids-item"},null)))])],4)])],2),ri("div",{class:{"uni-video-toast":!0,"uni-video-toast-progress":"progress"===q.gestureType}},[ri("div",{class:"uni-video-toast-title"},[bv(q.currentTimeNew)," / ",bv(d.duration)])],2),ri("div",{class:"uni-video-slots"},[o.default&&o.default()])],40,["onTouchstart","onTouchend","onTouchmove","onFullscreenchange","onWebkitfullscreenchange"])],8,["id","onClick"])}});let xv,Tv=0;function Sv(e,t,n,o){var r,i=document.createElement("script"),a=t.callback||"callback",s="__uni_jsonp_callback_"+Tv++,l=t.timeout||3e4;function c(){clearTimeout(r),delete window[s],i.remove()}window[s]=e=>{m(n)&&n(e),c()},i.onerror=()=>{m(o)&&o(),c()},r=setTimeout((function(){m(o)&&o(),c()}),l),i.src=e+(e.indexOf("?")>=0?"&":"?")+a+"="+s,document.body.appendChild(i)}function kv(e){function t(){const e=this.div;this.getPanes().floatPane.appendChild(e)}function n(){const e=this.div.parentNode;e&&e.removeChild(this.div)}function o(){const t=this.option;this.Text=new e.Text({text:t.content,anchor:"bottom-center",offset:new e.Pixel(0,t.offsetY-16),style:{padding:(t.padding||8)+"px","line-height":(t.fontSize||14)+"px","border-radius":(t.borderRadius||0)+"px","border-color":`${t.bgColor||"#fff"} transparent transparent`,"background-color":t.bgColor||"#fff","box-shadow":"0 2px 6px 0 rgba(114, 124, 245, .5)","text-align":"center","font-size":(t.fontSize||14)+"px",color:t.color||"#000"},position:t.position});(e.event||e.Event).addListener(this.Text,"click",(()=>{this.callback()})),this.Text.setMap(t.map)}function r(){}function i(){this.Text&&this.option.map.remove(this.Text)}function a(){this.Text&&this.option.map.remove(this.Text)}class s{constructor(e={},s){this.createAMapText=o,this.removeAMapText=i,this.createBMapText=r,this.removeBMapText=a,this.onAdd=t,this.construct=t,this.onRemove=n,this.destroy=n,this.option=e||{};const l=this.visible=this.alwaysVisible="ALWAYS"===e.display;if(Rv())this.callback=s,this.visible&&this.createAMapText();else if(Bv())this.visible&&this.createBMapText();else{const t=e.map;this.position=e.position,this.index=1;const n=this.div=document.createElement("div"),o=n.style;o.position="absolute",o.whiteSpace="nowrap",o.transform="translateX(-50%) translateY(-100%)",o.zIndex="1",o.boxShadow=e.boxShadow||"none",o.display=l?"block":"none";const r=this.triangle=document.createElement("div");r.setAttribute("style","position: absolute;white-space: nowrap;border-width: 4px;border-style: solid;border-color: #fff transparent transparent;border-image: initial;font-size: 12px;padding: 0px;background-color: transparent;width: 0px;height: 0px;transform: translate(-50%, 100%);left: 50%;bottom: 0;"),this.setStyle(e),n.appendChild(r),t&&this.setMap(t)}}set onclick(e){this.div.onclick=e}get onclick(){return this.div.onclick}setOption(e){this.option=e,"ALWAYS"===e.display?this.alwaysVisible=this.visible=!0:this.alwaysVisible=!1,Rv()?this.visible&&this.createAMapText():Bv()?this.visible&&this.createBMapText():(this.setPosition(e.position),this.setStyle(e))}setStyle(e){const t=this.div,n=t.style;t.innerText=e.content||"",n.lineHeight=(e.fontSize||14)+"px",n.fontSize=(e.fontSize||14)+"px",n.padding=(e.padding||8)+"px",n.color=e.color||"#000",n.borderRadius=(e.borderRadius||0)+"px",n.backgroundColor=e.bgColor||"#fff",n.marginTop="-"+((e.top||0)+5)+"px",this.triangle.style.borderColor=`${e.bgColor||"#fff"} transparent transparent`}setPosition(e){this.position=e,this.draw()}draw(){const e=this.getProjection();if(!this.position||!this.div||!e)return;const t=e.fromLatLngToDivPixel(this.position),n=this.div.style;n.left=t.x+"px",n.top=t.y+"px"}changed(){this.div.style.display=this.visible?"block":"none"}}if(!Rv()&&!Bv()){const t=new(e.OverlayView||e.Overlay);s.prototype.setMap=t.setMap,s.prototype.getMap=t.getMap,s.prototype.getPanes=t.getPanes,s.prototype.getProjection=t.getProjection,s.prototype.map_changed=t.map_changed,s.prototype.set=t.set,s.prototype.get=t.get,s.prototype.setOptions=t.setValues,s.prototype.bindTo=t.bindTo,s.prototype.bindsTo=t.bindsTo,s.prototype.notify=t.notify,s.prototype.setValues=t.setValues,s.prototype.unbind=t.unbind,s.prototype.unbindAll=t.unbindAll,s.prototype.addListener=t.addListener}return s}const Cv={};function Ev(e,t){const n=Ov();if(!n.key)return void console.error("Map key not configured.");const o=Cv[n.type]=Cv[n.type]||[];if(xv)t(xv);else if(window[n.type]&&window[n.type].maps)xv=Rv()||Bv()?window[n.type]:window[n.type].maps,xv.Callout=xv.Callout||kv(xv),t(xv);else if(o.length)o.push(t);else{o.push(t);const r=window,i="__map_callback__"+n.type;r[i]=function(){delete r[i],xv=Rv()||Bv()?window[n.type]:window[n.type].maps,xv.Callout=kv(xv),o.forEach((e=>e(xv))),o.length=0},Rv()&&function(e){window._AMapSecurityConfig={securityJsCode:e.securityJsCode||"",serviceHost:e.serviceHost||""}}(n);const a=document.createElement("script");let s=Av(n.type);n.type===Lv.QQ&&e.push("geometry"),e.length&&(s+=`libraries=${e.join("%2C")}&`),n.type===Lv.BMAP?a.src=`${s}ak=${n.key}&callback=${i}`:a.src=`${s}key=${n.key}&callback=${i}`,a.onerror=function(){console.error("Map load failed.")},document.body.appendChild(a)}}const Av=e=>({qq:"https://map.qq.com/api/js?v=2.exp&",google:"https://maps.googleapis.com/maps/api/js?",AMap:"https://webapi.amap.com/maps?v=2.0&",BMapGL:"https://api.map.baidu.com/api?type=webgl&v=1.0&"}[e]);const Mv="M13.3334375 16 q0.033125 1.1334375 0.783125 1.8834375 q0.75 0.75 1.8834375 0.75 q1.1334375 0 1.8834375 -0.75 q0.75 -0.75 0.75 -1.8834375 q0 -1.1334375 -0.75 -1.8834375 q-0.75 -0.75 -1.8834375 -0.75 q-1.1334375 0 -1.8834375 0.75 q-0.75 0.75 -0.783125 1.8834375 ZM30.9334375 14.9334375 l-1.1334375 0 q-0.5 -5.2 -4.0165625 -8.716875 q-3.516875 -3.5165625 -8.716875 -4.0165625 l0 -1.1334375 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 l0 1.1334375 q-5.2 0.5 -8.716875 4.0165625 q-3.5165625 3.516875 -4.0165625 8.716875 l-1.1334375 0 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 l1.1334375 0 q0.5 5.2 4.0165625 8.716875 q3.516875 3.5165625 8.716875 4.0165625 l0 1.1334375 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 l0 -1.1334375 q5.2 -0.5 8.716875 -4.0165625 q3.5165625 -3.516875 4.0165625 -8.716875 l1.1334375 0 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 ZM17.0665625 27.6665625 l0 -2.0665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 l0 2.0665625 q-4.3 -0.4665625 -7.216875 -3.383125 q-2.916875 -2.916875 -3.3834375 -7.216875 l2.0665625 0 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 l-2.0665625 0 q0.4665625 -4.3 3.3834375 -7.216875 q2.9165625 -2.916875 7.216875 -3.3834375 l0 2.0665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 l0 -2.0665625 q4.3 0.4665625 7.216875 3.3834375 q2.9165625 2.9165625 3.383125 7.216875 l-2.0665625 0 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 l2.0665625 0 q-0.4665625 4.3 -3.383125 7.216875 q-2.916875 2.9165625 -7.216875 3.383125 Z",Pv="data:image/png;base64,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",Iv="data:image/png;base64,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";var Lv=(e=>(e.QQ="qq",e.GOOGLE="google",e.AMAP="AMap",e.BMAP="BMapGL",e.UNKNOWN="",e))(Lv||{});function Ov(){return __uniConfig.bMapKey?{type:"BMapGL",key:__uniConfig.bMapKey}:__uniConfig.qqMapKey?{type:"qq",key:__uniConfig.qqMapKey}:__uniConfig.googleMapKey?{type:"google",key:__uniConfig.googleMapKey}:__uniConfig.aMapKey?{type:"AMap",key:__uniConfig.aMapKey,securityJsCode:__uniConfig.aMapSecurityJsCode,serviceHost:__uniConfig.aMapServiceHost}:{type:"",key:""}}let $v=!1,Dv=!1;const Rv=()=>Dv?$v:(Dv=!0,$v="AMap"===Ov().type),Bv=()=>"BMapGL"===Ov().type;function Nv(e,t,n){const o=Ov();return e&&"WGS84"===e.toUpperCase()||["google"].includes(o.type)||n?Promise.resolve(t):"qq"===o.type?new Promise((e=>{Sv(`https://apis.map.qq.com/ws/coord/v1/translate?type=1&locations=${t.latitude},${t.longitude}&key=${o.key}&output=jsonp`,{callback:"callback"},(n=>{if("locations"in n&&n.locations.length){const{lng:o,lat:r}=n.locations[0];e({longitude:o,latitude:r,altitude:t.altitude,accuracy:t.accuracy,altitudeAccuracy:t.altitudeAccuracy,heading:t.heading,speed:t.speed})}else e(t)}),(()=>e(t)))})):"AMap"===o.type?new Promise((e=>{Ev([],(()=>{window.AMap.convertFrom([t.longitude,t.latitude],"gps",((n,o)=>{if("ok"===o.info&&o.locations.length){const{lat:n,lng:r}=o.locations[0];e({longitude:r,latitude:n,altitude:t.altitude,accuracy:t.accuracy,altitudeAccuracy:t.altitudeAccuracy,heading:t.heading,speed:t.speed})}else e(t)}))}))})):Promise.reject(new Error("translate coordinate system faild"))}const qv=cu({name:"MapMarker",props:{id:{type:[Number,String],default:""},latitude:{type:[Number,String],require:!0},longitude:{type:[Number,String],require:!0},title:{type:String,default:""},iconPath:{type:String,require:!0},rotate:{type:[Number,String],default:0},alpha:{type:[Number,String],default:1},width:{type:[Number,String],default:""},height:{type:[Number,String],default:""},callout:{type:Object,default:null},label:{type:Object,default:null},anchor:{type:Object,default:null},clusterId:{type:[Number,String],default:""},customCallout:{type:Object,default:null},ariaLabel:{type:String,default:""}},setup(e){const t=String(isNaN(Number(e.id))?"":e.id),n=_r("onMapReady"),o=function(e){const t="uni-map-marker-label-"+e,n=document.createElement("style");return n.id=t,document.head.appendChild(n),Uo((()=>{n.remove()})),function(e){const o=Object.assign({},e,{position:"absolute",top:"70px",borderStyle:"solid"}),r=document.createElement("div");return Object.keys(o).forEach((e=>{r.style[e]=o[e]||""})),n.innerText=`.${t}{${r.getAttribute("style")}}`,t}}(t);let r;function i(e){Rv()?e.removeAMapText():e.setMap(null)}if(n(((n,a,s)=>{function l(e){const l=e.title;let c;c=Rv()?new a.LngLat(e.longitude,e.latitude):Bv()?new a.Point(e.longitude,e.latitude):new a.LatLng(e.latitude,e.longitude);const u=new Image;let d=0;u.onload=()=>{const p=e.anchor||{};let f,h,g,m,v="number"==typeof p.x?p.x:.5,y="number"==typeof p.y?p.y:1;e.iconPath&&(e.width||e.height)?(h=e.width||u.width/u.height*e.height,g=e.height||u.height/u.width*e.width):(h=u.width/2,g=u.height/2),d=g,m=g-(g-y*g),f="MarkerImage"in a?new a.MarkerImage(u.src,null,null,new a.Point(v*h,y*g),new a.Size(h,g)):"Icon"in a?new a.Icon({image:u.src,size:new a.Size(h,g),imageSize:new a.Size(h,g),imageOffset:new a.Pixel(v*h,y*g)}):{url:u.src,anchor:new a.Point(v,y),size:new a.Size(h,g)},Bv()?(r=new a.Marker(new a.Point(c.lng,c.lat)),n.addOverlay(r)):(r.setPosition(c),r.setIcon(f)),"setRotation"in r&&r.setRotation(e.rotate||0);const b=e.label||{};let _;if("label"in r&&(r.label.setMap(null),delete r.label),b.content){const e={borderColor:b.borderColor,borderWidth:(Number(b.borderWidth)||0)+"px",padding:(Number(b.padding)||0)+"px",borderRadius:(Number(b.borderRadius)||0)+"px",backgroundColor:b.bgColor,color:b.color,fontSize:(b.fontSize||14)+"px",lineHeight:(b.fontSize||14)+"px",marginLeft:(Number(b.anchorX||b.x)||0)+"px",marginTop:(Number(b.anchorY||b.y)||0)+"px"};if("Label"in a)_=new a.Label({position:c,map:n,clickable:!1,content:b.content,style:e}),r.label=_;else if("setLabel"in r)if(Rv()){const t=`<div style="\n                  margin-left:${e.marginLeft};\n                  margin-top:${e.marginTop};\n                  padding:${e.padding};\n                  background-color:${e.backgroundColor};\n                  border-radius:${e.borderRadius};\n                  line-height:${e.lineHeight};\n                  color:${e.color};\n                  font-size:${e.fontSize};\n\n                  ">\n                  ${b.content}\n                <div>`;r.setLabel({content:t,direction:"bottom-right"})}else{const t=o(e);r.setLabel({text:b.content,color:e.color,fontSize:e.fontSize,className:t})}}const w=e.callout||{};let x,T=r.callout;if(w.content||l){Rv()&&w.content&&(w.content=w.content.replaceAll("\n","<br/>"));const o="0px 0px 3px 1px rgba(0,0,0,0.5)";let i=-d/2;if((e.width||e.height)&&(i+=14-d/2),x=w.content?{position:c,map:n,top:m,offsetY:i,content:w.content,color:w.color,fontSize:w.fontSize,borderRadius:w.borderRadius,bgColor:w.bgColor,padding:w.padding,boxShadow:w.boxShadow||o,display:w.display}:{position:c,map:n,top:m,offsetY:i,content:l,boxShadow:o},T)T.setOption(x);else if(Rv()){const e=e=>{""!==e&&s("callouttap",{},{markerId:Number(e)})};T=r.callout=new a.Callout(x,e)}else T=r.callout=new a.Callout(x),T.div.onclick=function(e){""!==t&&s("callouttap",e,{markerId:Number(t)}),e.stopPropagation(),e.preventDefault()},Ov().type===Lv.GOOGLE&&(T.div.ontouchstart=function(e){e.stopPropagation()},T.div.onpointerdown=function(e){e.stopPropagation()})}else T&&(i(T),delete r.callout)},e.iconPath?u.src=Tu(e.iconPath):console.error("Marker.iconPath is required.")}!function(e){Bv()||(r=new a.Marker({map:n,flat:!0,autoRotation:!1})),l(e);const o=a.event||a.Event;Bv()||o.addListener(r,"click",(()=>{const n=r.callout;if(n&&!n.alwaysVisible)if(Rv())n.visible=!n.visible,n.visible?r.callout.createAMapText():r.callout.removeAMapText();else if(n.set("visible",!n.visible),n.visible){const e=n.div,t=e.parentNode;t.removeChild(e),t.appendChild(e)}t&&s("markertap",{},{markerId:Number(t),latitude:e.latitude,longitude:e.longitude})}))}(e),to(e,l)})),t){const e=_r("addMapChidlContext"),o=_r("removeMapChidlContext"),i={id:t,translate(e){n(((t,n,o)=>{const i=e.destination,a=e.duration,s=!!e.autoRotate;let l=Number(e.rotate)||0,c=0;"getRotation"in r&&(c=r.getRotation());const u=r.getPosition(),d=new n.LatLng(i.latitude,i.longitude),p=n.geometry.spherical.computeDistanceBetween(u,d)/1e3/(("number"==typeof a?a:1e3)/36e5),f=n.event||n.Event,h=f.addListener(r,"moving",(e=>{const t=e.latLng,n=r.label;n&&n.setPosition(t);const o=r.callout;o&&o.setPosition(t)})),g=f.addListener(r,"moveend",(()=>{g.remove(),h.remove(),r.lastPosition=u,r.setPosition(d);const t=r.label;t&&t.setPosition(d);const n=r.callout;n&&n.setPosition(d);const o=e.animationEnd;m(o)&&o()}));let v=0;s&&(r.lastPosition&&(v=n.geometry.spherical.computeHeading(r.lastPosition,u)),l=n.geometry.spherical.computeHeading(u,d)-v),"setRotation"in r&&r.setRotation(c+l),"moveTo"in r?r.moveTo(d,p):(r.setPosition(d),f.trigger(r,"moveend",{}))}))}};e(i),Uo((()=>o(i)))}return Uo((function(){r&&(r.label&&"setMap"in r.label&&r.label.setMap(null),r.callout&&i(r.callout),r.setMap(null))})),()=>null}});function jv(e){if(!e)return{r:0,g:0,b:0,a:0};let t=e.slice(1);const n=t.length;if(![3,4,6,8].includes(n))return{r:0,g:0,b:0,a:0};3!==n&&4!==n||(t=t.replace(/(\w{1})/g,"$1$1"));let[o,r,i,a]=t.match(/(\w{2})/g);const s=parseInt(o,16),l=parseInt(r,16),c=parseInt(i,16);return a?{r:s,g:l,b:c,a:(`0x100${a}`-65536)/255}:{r:s,g:l,b:c,a:1}}const zv={points:{type:Array,require:!0},color:{type:String,default:"#000000"},width:{type:[Number,String],default:""},dottedLine:{type:[Boolean,String],default:!1},arrowLine:{type:[Boolean,String],default:!1},arrowIconPath:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderWidth:{type:[Number,String],default:""},colorList:{type:Array,default:()=>[]},level:{type:String,default:""}},Vv=cu({name:"MapPolyline",props:zv,setup(e){let t,n;function o(){t&&t.setMap(null),n&&n.setMap(null)}return _r("onMapReady")(((r,i)=>{function a(e){const o=[];e.points.forEach((e=>{let t;t=Rv()?[e.longitude,e.latitude]:Bv()?new i.Point(e.longitude,e.latitude):new i.LatLng(e.latitude,e.longitude),o.push(t)}));const a=Number(e.width)||1,{r:s,g:l,b:c,a:u}=jv(e.color),{r:d,g:p,b:f,a:h}=jv(e.borderColor),g={map:r,clickable:!1,path:o,strokeWeight:a,strokeColor:e.color||void 0,strokeDashStyle:e.dottedLine?"dash":"solid"},m=Number(e.borderWidth)||0,v={map:r,clickable:!1,path:o,strokeWeight:a+2*m,strokeColor:e.borderColor||void 0,strokeDashStyle:e.dottedLine?"dash":"solid"};"Color"in i?(g.strokeColor=new i.Color(s,l,c,u),v.strokeColor=new i.Color(d,p,f,h)):(g.strokeColor=`rgb(${s}, ${l}, ${c})`,g.strokeOpacity=u,v.strokeColor=`rgb(${d}, ${p}, ${f})`,v.strokeOpacity=h),m&&(n=new i.Polyline(v)),Bv()?(t=new i.Polyline(g.path,g),r.addOverlay(t)):t=new i.Polyline(g)}a(e),to(e,(function(e){o(),a(e)}))})),Uo(o),()=>null}}),Fv=cu({name:"MapCircle",props:{latitude:{type:[Number,String],require:!0},longitude:{type:[Number,String],require:!0},color:{type:String,default:"#000000"},fillColor:{type:String,default:"#00000000"},radius:{type:[Number,String],require:!0},strokeWidth:{type:[Number,String],default:""},level:{type:String,default:""}},setup(e){let t;function n(){t&&t.setMap(null)}return _r("onMapReady")(((o,r)=>{function i(e){const n=Rv()||Bv()?[e.longitude,e.latitude]:new r.LatLng(e.latitude,e.longitude),i={map:o,center:n,clickable:!1,radius:e.radius,strokeWeight:Number(e.strokeWidth)||1,strokeDashStyle:"solid"};if(Bv())i.strokeColor=e.color,i.fillColor=e.fillColor||"#000",i.fillOpacity=1;else{const{r:t,g:n,b:o,a:a}=jv(e.fillColor),{r:s,g:l,b:c,a:u}=jv(e.color);"Color"in r?(i.fillColor=new r.Color(t,n,o,a),i.strokeColor=new r.Color(s,l,c,u)):(i.fillColor=`rgb(${t}, ${n}, ${o})`,i.fillOpacity=a,i.strokeColor=`rgb(${s}, ${l}, ${c})`,i.strokeOpacity=u)}if(Bv()){let e=new r.Point(i.center[0],i.center[1]);t=new r.Circle(e,i.radius,i),o.addOverlay(t)}else t=new r.Circle(i),Rv()&&o.add(t)}i(e),to(e,(function(e){n(),i(e)}))})),Uo(n),()=>null}}),Uv={id:{type:[Number,String],default:""},position:{type:Object,required:!0},iconPath:{type:String,required:!0},clickable:{type:[Boolean,String],default:""},trigger:{type:Function,required:!0}},Wv=cu({name:"MapControl",props:Uv,setup(e){const t=Ei((()=>Tu(e.iconPath))),n=Ei((()=>{let t=`top:${e.position.top||0}px;left:${e.position.left||0}px;`;return e.position.width&&(t+=`width:${e.position.width}px;`),e.position.height&&(t+=`height:${e.position.height}px;`),t})),o=t=>{e.clickable&&e.trigger("controltap",t,{controlId:e.id})};return()=>ri("div",{class:"uni-map-control"},[ri("img",{src:t.value,style:n.value,class:"uni-map-control-icon",onClick:o},null,12,["src","onClick"])])}}),Hv=de((()=>{Sp.forEach((e=>{Yv.prototype[e]=function(t){m(t)&&this._events[e].push(t)}})),kp.forEach((e=>{Yv.prototype[e]=function(t){var n=this._events[e.replace("off","on")],o=n.indexOf(t);o>=0&&n.splice(o,1)}}))}));class Yv{constructor(){this._src="";var e=this._audio=new Audio;this._stoping=!1;["src","autoplay","loop","duration","currentTime","paused","volume"].forEach((t=>{Object.defineProperty(this,t,{set:"src"===t?t=>(e.src=Tu(t),this._src=t,t):n=>(e[t]=n,n),get:"src"===t?()=>this._src:()=>e[t]})})),this.startTime=0,Object.defineProperty(this,"obeyMuteSwitch",{set:()=>!1,get:()=>!1}),Object.defineProperty(this,"buffered",{get(){var t=e.buffered;return t.length?t.end(t.length-1):0}}),this._events={},Sp.forEach((e=>{this._events[e]=[]})),e.addEventListener("loadedmetadata",(()=>{var t=Number(this.startTime)||0;t>0&&(e.currentTime=t)}));var t=["canplay","pause","seeking","seeked","timeUpdate"];t.concat(["play","ended","error","waiting"]).forEach((n=>{e.addEventListener(n.toLowerCase(),(()=>{if(this._stoping&&t.indexOf(n)>=0)return;const e=`on${n.slice(0,1).toUpperCase()}${n.slice(1)}`;this._events[e].forEach((e=>{e()}))}),!1)})),Hv()}play(){this._stoping=!1,this._audio.play()}pause(){this._audio.pause()}stop(){this._stoping=!0,this._audio.pause(),this._audio.currentTime=0,this._events.onStop.forEach((e=>{e()}))}seek(e){this._stoping=!1,"number"!=typeof(e=Number(e))||isNaN(e)||(this._audio.currentTime=e)}destroy(){this.stop()}}const Xv=Td(0,(()=>new Yv)),Gv=Sd("makePhoneCall",(({phoneNumber:e},{resolve:t})=>(window.location.href=`tel:${e}`,t()))),Jv=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let Qv;function Kv(){if(Qv=Qv||Jv.__DC_STAT_UUID,!Qv){Qv=Date.now()+""+Math.floor(1e7*Math.random());try{Jv.__DC_STAT_UUID=Qv}catch(e){}}return Qv}function Zv(){if(!0!==__uniConfig.darkmode)return v(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function ey(){let e,t="0",n="",o="phone";const r=navigator.language;if(Cu){e="iOS";const o=Su.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=Su.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(ku){e="Android";const o=Su.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=Su.match(/\((.+?)\)/),i=r?r[1].split(";"):Su.split(" "),a=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<a.length;e++)if(a[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(Pu)n="iPad",e="iOS",o="pad",t=m(window.BigInt)?"14.0":"13.0";else if(Eu||Au||Mu){n="PC",e="PC",o="pc",t="0";let r=Su.match(/\((.+?)\)/)[1];if(Eu){switch(e="Windows",Eu[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(Au){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(Mu){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,a=e.toLocaleLowerCase();let s="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)s="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(Su)&&(s=t[n],l=Su.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:a,browserName:s.toLocaleLowerCase(),browserVersion:l,language:r,deviceType:o,ua:Su,osname:e,osversion:t,theme:Zv()}}const ty=Td(0,(()=>{const e=window.devicePixelRatio,t=Iu(),n=Lu(t),o=Ou(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=$u(o);let a=window.innerHeight;const s=ic.top,l={left:ic.left,right:i-ic.right,top:ic.top,bottom:a-ic.bottom,width:i-ic.left-ic.right,height:a-ic.top-ic.bottom},{top:c,bottom:u}=uc();return a-=c,a-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:a,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:s,safeArea:l,safeAreaInsets:{top:ic.top,right:ic.right,bottom:ic.bottom,left:ic.left},screenTop:r-a}}));let ny,oy=!0;function ry(){oy&&(ny=ey())}const iy=Td(0,(()=>{ry();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:a,deviceType:s,osname:l,osversion:u}=ny;return c({brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:Kv(),deviceOrientation:a,deviceType:s,model:o,platform:r,system:i},{})})),ay=Td(0,(()=>{ry();const{theme:e,language:t,browserName:n,browserVersion:o}=ny;return c({appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:Jp?Jp():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:""},{})})),sy=Td(0,(()=>{oy=!0,ry(),oy=!1;const e=ty(),t=iy(),n=ay();oy=!0;const{ua:o,browserName:r,browserVersion:i,osname:a,osversion:s}=ny,l=c(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:a.toLocaleLowerCase(),osVersion:s,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return T(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)})),ly=Sd("getSystemInfo",((e,{resolve:t})=>t(sy())));function cy(){fy().then((({networkType:e})=>{Cx.invokeOnCallback("onNetworkStatusChange",{isConnected:"none"!==e,networkType:e})}))}function uy(){return navigator.connection||navigator.webkitConnection||navigator.mozConnection}const dy=_d("onNetworkStatusChange",(()=>{const e=uy();e?e.addEventListener("change",cy):(window.addEventListener("offline",cy),window.addEventListener("online",cy))})),py=wd("offNetworkStatusChange",(()=>{const e=uy();e?e.removeEventListener("change",cy):(window.removeEventListener("offline",cy),window.removeEventListener("online",cy))})),fy=Sd("getNetworkType",((e,{resolve:t})=>{const n=uy();let o="unknown";return n?(o=n.type,"cellular"===o&&n.effectiveType?o=n.effectiveType.replace("slow-",""):!o&&n.effectiveType?o=n.effectiveType:["none","wifi"].includes(o)||(o="unknown")):!1===navigator.onLine&&(o="none"),t({networkType:o})}));let hy=null;const gy=_d("onAccelerometer",(()=>{vy()})),my=wd("offAccelerometer",(()=>{yy()})),vy=Sd("startAccelerometer",((e,{resolve:t,reject:n})=>{if(window.DeviceMotionEvent){if(!hy){if(DeviceMotionEvent.requestPermission)return void DeviceMotionEvent.requestPermission().then((e=>{"granted"===e?(o(),t()):n(`${e}`)})).catch((e=>{n(`${e}`)}));o()}t()}else n();function o(){hy=function(e){const t=e.acceleration||e.accelerationIncludingGravity;Cx.invokeOnCallback("onAccelerometer",{x:t&&t.x||0,y:t&&t.y||0,z:t&&t.z||0})},window.addEventListener("devicemotion",hy,!1)}})),yy=Sd("stopAccelerometer",((e,{resolve:t})=>{hy&&(window.removeEventListener("devicemotion",hy,!1),hy=null),t()}));let by=null;const _y=_d("onCompass",(()=>{xy()})),wy=wd("offCompass",(()=>{Ty()})),xy=Sd("startCompass",((e,{resolve:t,reject:n})=>{if(window.DeviceOrientationEvent){if(!by){if(DeviceOrientationEvent.requestPermission)return void DeviceOrientationEvent.requestPermission().then((e=>{"granted"===e?(o(),t()):n(`${e}`)})).catch((e=>{n(`${e}`)}));o()}t()}else n();function o(){by=function(e){const t=360-(null!==e.alpha?e.alpha:360);Cx.invokeOnCallback("onCompass",{direction:t})},window.addEventListener("deviceorientation",by,!1)}})),Ty=Sd("stopCompass",((e,{resolve:t})=>{by&&(window.removeEventListener("deviceorientation",by,!1),by=null),t()})),Sy=!!window.navigator.vibrate,ky=Sd("vibrateShort",((e,{resolve:t,reject:n})=>{Sy&&window.navigator.vibrate(15)?t():n("vibrateLong:fail")})),Cy=Sd("vibrateLong",((e,{resolve:t,reject:n})=>{Sy&&window.navigator.vibrate(400)?t():n("vibrateLong:fail")}));var Ey=(e,t,n)=>new Promise(((o,r)=>{var i=e=>{try{s(n.next(e))}catch(yC){r(yC)}},a=e=>{try{s(n.throw(e))}catch(yC){r(yC)}},s=e=>e.done?o(e.value):Promise.resolve(e.value).then(i,a);s((n=n.apply(e,t)).next())}));const Ay=Sd("getClipboardData",((e,t)=>Ey(void 0,[e,t],(function*(e,{resolve:t,reject:n}){Al();const{t:o}=bl();try{t({data:yield navigator.clipboard.readText()})}catch(r){!function(e,t){const n=document.getElementById("#clipboard"),o=n?n.value:void 0;o?e({data:o}):t()}(t,(()=>{n(`${r} ${o("uni.getClipboardData.fail")}`)}))}})))),My=Sd("setClipboardData",((e,t)=>Ey(void 0,[e,t],(function*({data:e},{resolve:t,reject:n}){try{yield navigator.clipboard.writeText(e),t()}catch(o){!function(e,t,n){const o=document.getElementById("#clipboard");o&&o.remove();const r=document.createElement("textarea");r.id="#clipboard",r.style.position="fixed",r.style.top="-9999px",r.style.zIndex="-9999",document.body.appendChild(r),r.value=e,r.select(),r.setSelectionRange(0,r.value.length);const i=document.execCommand("Copy",!1);r.blur(),i?t():n()}(e,t,n)}}))),0,gf);const Py=e=>{Cx.invokeOnCallback("onThemeChange",e)},Iy=_d("onThemeChange",(()=>{Cx.on("onThemeChange",Py)})),Ly=wd("offThemeChange",(()=>{Cx.off("onThemeChange",Py)}));const Oy=Td(0,((e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)})),$y=Sd("setStorage",(({key:e,data:t},{resolve:n,reject:o})=>{try{Oy(e,t),n()}catch(r){o(r.message)}}));function Dy(e){const t=localStorage&&localStorage.getItem(e);if(!v(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=v(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const Ry=Td(0,(e=>{try{return Dy(e)}catch(t){return""}})),By=Sd("getStorage",(({key:e},{resolve:t,reject:n})=>{try{t({data:Dy(e)})}catch(o){n(o.message)}})),Ny=Td(0,(e=>{localStorage&&localStorage.removeItem(e)})),qy=Sd("removeStorage",(({key:e},{resolve:t})=>{Ny(e),t()})),jy=Td(0,(()=>{localStorage&&localStorage.clear()})),zy=Sd("clearStorage",((e,{resolve:t})=>{jy(),t()})),Vy=Td(0,(()=>{const e=localStorage&&localStorage.length||0,t=[];let n=0;for(let o=0;o<e;o++){const e=localStorage.key(o),r=localStorage.getItem(e)||"";n+=e.length+r.length,"uni-storage-keys"!==e&&t.push(e)}return{keys:t,currentSize:Math.ceil(2*n/1024),limitSize:Number.MAX_VALUE}})),Fy=Sd("getStorageInfo",((e,{resolve:t})=>{t(Vy())})),Uy=Sd("getFileInfo",(({filePath:e},{resolve:t,reject:n})=>{mh(e).then((e=>{t({size:e.size})})).catch((e=>{n(String(e))}))}),0,mf),Wy=Sd("openDocument",(({filePath:e},{resolve:t})=>(window.open(e),t())),0,vf),Hy=Sd("hideKeyboard",((e,{resolve:t,reject:n})=>{const o=document.activeElement;!o||"TEXTAREA"!==o.tagName&&"INPUT"!==o.tagName||(o.blur(),t())}));const Yy=Sd("getImageInfo",(({src:e},{resolve:t,reject:n})=>{const o=new Image;o.onload=function(){t({width:o.naturalWidth,height:o.naturalHeight,path:0===e.indexOf("/")?window.location.protocol+"//"+window.location.host+e:e})},o.onerror=function(){n()},o.src=e}),0,Cf),Xy=Sd("getVideoInfo",(({src:e},{resolve:t,reject:n})=>{mh(e,!0).then((e=>e)).catch((()=>null)).then((o=>{const r=document.createElement("video");if(void 0!==r.onloadedmetadata){const i=setTimeout((()=>{r.onloadedmetadata=null,r.onerror=null,n()}),e.startsWith("data:")||e.startsWith("blob:")?300:3e3);r.onloadedmetadata=function(){clearTimeout(i),r.onerror=null,t({size:Math.ceil((o?o.size:0)/1024),duration:r.duration||0,width:r.videoWidth||0,height:r.videoHeight||0})},r.onerror=function(){clearTimeout(i),r.onloadedmetadata=null,n()},r.src=e}else n()}))}),0,Af),Gy={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};function Jy({count:e,sourceType:t,type:n,extension:o}){Yh();const r=document.createElement("input");return r.type="file",function(e,t){for(const n in t)e.style[n]=t[n]}(r,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),r.accept=o.map((e=>{if("all"!==n){const t=e.replace(".","");return`${n}/${Gy[n][t]||t}`}return function(){const e=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!(!e||"micromessenger"!==e[0])}()?".":0===e.indexOf(".")?e:`.${e}`})).join(","),e&&e>1&&(r.multiple=!0),"all"!==n&&t instanceof Array&&1===t.length&&"camera"===t[0]&&r.setAttribute("capture","camera"),r}let Qy=null;const Ky=Sd("chooseFile",(({count:e,sourceType:t,type:n,extension:o},{resolve:r,reject:i})=>{Cl();const{t:a}=bl();Qy&&(document.body.removeChild(Qy),Qy=null),Qy=Jy({count:e,sourceType:t,type:n,extension:o}),document.body.appendChild(Qy),Qy.addEventListener("change",(function(t){const n=t.target,o=[];if(n&&n.files){const t=n.files.length;for(let r=0;r<t;r++){const t=n.files[r];let i;Object.defineProperty(t,"path",{get:()=>(i=i||yh(t),i)}),r<e&&o.push(t)}}r({get tempFilePaths(){return o.map((({path:e})=>e))},tempFiles:o})})),Qy.click(),Xh()||console.warn(a("uni.chooseFile.notUserActivation"))}),0,kf);let Zy=null;const eb=Sd("chooseImage",(({count:e,sourceType:t,extension:n},{resolve:o,reject:r})=>{Cl();const{t:i}=bl();Zy&&(document.body.removeChild(Zy),Zy=null),Zy=Jy({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(Zy),Zy.addEventListener("change",(function(t){const n=t.target,r=[];if(n&&n.files){const t=n.files.length;for(let o=0;o<t;o++){const t=n.files[o];let i;Object.defineProperty(t,"path",{get:()=>(i=i||yh(t),i)}),o<e&&r.push(t)}}o({get tempFilePaths(){return r.map((({path:e})=>e))},tempFiles:r})})),Zy.click(),Xh()||console.warn(i("uni.chooseFile.notUserActivation"))}),0,xf),tb={esc:["Esc","Escape"],enter:["Enter"]},nb=Object.keys(tb);function ob(){const e=sn(""),t=sn(!1),n=n=>{if(t.value)return;const o=nb.find((e=>-1!==tb[e].indexOf(n.key)));o&&(e.value=o),Cn((()=>e.value=""))};return jo((()=>{document.addEventListener("keyup",n)})),Fo((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}const rb=ri("div",{class:"uni-mask"},null,-1);function ib(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),Sa(_o({setup:()=>()=>(Hr(),Qr(e,t,null,16))}))}function ab(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function sb(e,{onEsc:t,onEnter:n}){const o=sn(e.visible),{key:r,disable:i}=ob();return to((()=>e.visible),(e=>o.value=e)),to((()=>o.value),(e=>i.value=!e)),Zn((()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}let lb=0,cb="";function ub(e){let t=lb;lb+=e?1:-1,lb=Math.max(0,lb),lb>0?0===t&&(cb=document.body.style.overflow,document.body.style.overflow="hidden"):(document.body.style.overflow=cb,cb="")}function db(){jo((()=>ub(!0))),Uo((()=>ub(!1)))}const pb=cu({name:"ImageView",props:{src:{type:String,default:""}},setup(e){const t=Ut({direction:"none"});let n=1,o=0,r=0,i=0,a=0;function s({detail:e}){n=e.scale}function l(e){const t=e.target.getBoundingClientRect();o=t.width,r=t.height}function c(e){const t=e.target.getBoundingClientRect();i=t.width,a=t.height,d(e)}function u(e){const s=n*o>i,l=n*r>a;t.direction=s&&l?"all":s?"horizontal":l?"vertical":"none",d(e)}function d(e){"all"!==t.direction&&"horizontal"!==t.direction||e.stopPropagation()}return()=>{const n={position:"absolute",left:"0",top:"0",width:"100%",height:"100%"};return ri(dg,{style:n,onTouchstart:uu(c),onTouchmove:uu(d),onTouchend:uu(u)},{default:()=>[ri(Sg,{style:n,direction:t.direction,inertia:!0,scale:!0,"scale-min":"1","scale-max":"4",onScale:s},{default:()=>[ri("img",{src:e.src,style:{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)",maxHeight:"100%",maxWidth:"100%"},onLoad:l},null,40,["src","onLoad"])]},8,["style","direction","inertia","scale","onScale"])]},8,["style","onTouchstart","onTouchmove","onTouchend"])}}});function fb(e){let t="number"==typeof e.current?e.current:e.urls.indexOf(e.current);return t=t<0?0:t,t}const hb=cu({name:"ImagePreview",props:{urls:{type:Array,default:()=>[]},current:{type:[Number,String],default:0}},emits:["close"],setup(e,{emit:t}){db();const n=sn(null),o=sn(fb(e));let r;function i(){r||Cn((()=>{t("close")}))}function a(e){o.value=e.detail.current}to((()=>e.current),(()=>o.value=fb(e))),jo((()=>{const e=n.value;let t=0,o=0;e.addEventListener("mousedown",(e=>{r=!1,t=e.clientX,o=e.clientY})),e.addEventListener("mouseup",(e=>{(Math.abs(e.clientX-t)>20||Math.abs(e.clientY-o)>20)&&(r=!0)}))}));const s={position:"absolute","box-sizing":"border-box",top:"0",right:"0",width:"60px",height:"44px",padding:"6px","line-height":"32px","font-size":"26px",color:"white","text-align":"center",cursor:"pointer"};return()=>{let t;return ri("div",{ref:n,style:{display:"block",position:"fixed",left:"0",top:"0",width:"100%",height:"100%",zIndex:999,background:"rgba(0,0,0,0.8)"},onClick:i},[ri(Gg,{navigation:"auto",current:o.value,onChange:a,"indicator-dots":!1,autoplay:!1,style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%"}},(r=t=e.urls.map((e=>ri(Qg,null,{default:()=>[ri(pb,{src:e},null,8,["src"])]}))),"function"==typeof r||"[object Object]"===Object.prototype.toString.call(r)&&!Kr(r)?t:{default:()=>[t],_:1}),8,["current","onChange"]),ri("div",{style:s},[wc(bc,"#ffffff",26)],4)],8,["onClick"]);var r}}});let gb,mb=null;const vb=()=>{mb=null,Cn((()=>{null==gb||gb.unmount(),gb=null}))},yb=Sd("previewImage",((e,{resolve:t})=>{mb?c(mb,e):(mb=Ut(e),Cn((()=>{gb=ib(hb,mb,vb),gb.mount(ab("u-a-p"))}))),t()}),0,Ef),bb=Sd("closePreviewImage",((e,{resolve:t,reject:n})=>{gb?(vb(),t()):n()}));let _b=null;const wb=Sd("chooseVideo",(({sourceType:e,extension:t},{resolve:n,reject:o})=>{Cl();const{t:r}=bl();_b&&(document.body.removeChild(_b),_b=null),_b=Jy({sourceType:e,extension:t,type:"video"}),document.body.appendChild(_b),_b.addEventListener("change",(function(e){const t=e.target.files[0];let o="";const r={tempFilePath:o,tempFile:t,size:t.size,duration:0,width:0,height:0,name:t.name};Object.defineProperty(r,"tempFilePath",{get(){return o=o||yh(this.tempFile),o}});const i=document.createElement("video");if(void 0!==i.onloadedmetadata){const e=yh(t);i.onloadedmetadata=function(){bh(e),n(c(r,{duration:i.duration||0,width:i.videoWidth||0,height:i.videoHeight||0}))},setTimeout((()=>{i.onloadedmetadata=null,bh(e),n(r)}),300),i.src=e}else n(r)})),_b.click(),Xh()||console.warn(r("uni.chooseFile.notUserActivation"))}),0,Tf),xb=xd("request",(({url:e,data:t,header:n={},method:o,dataType:r,responseType:i,withCredentials:a,timeout:s=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const d=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(v(t)||t instanceof ArrayBuffer)u=t;else if("json"===d)try{u=JSON.stringify(t)}catch(m){u=t.toString()}else if("urlencoded"===d){const e=[];for(const n in t)p(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));u=e.join("&")}else u=t.toString();const f=new XMLHttpRequest,h=new Tb(f);f.open(o,e);for(const v in n)p(n,v)&&f.setRequestHeader(v,n[v]);const g=setTimeout((function(){f.onload=f.onabort=f.onerror=null,h.abort(),c("timeout",{errCode:5})}),s);return f.responseType=i,f.onload=function(){clearTimeout(g);const e=f.status;let t="text"===i?f.responseText:f.response;if("text"===i&&"json"===r)try{t=JSON.parse(t)}catch(m){}l({data:t,statusCode:e,header:Sb(f.getAllResponseHeaders()),cookies:[]})},f.onabort=function(){clearTimeout(g),c("abort",{errCode:600003})},f.onerror=function(){clearTimeout(g),c(void 0,{errCode:5})},f.withCredentials=a,f.send(u),h}),0,Lf);class Tb{constructor(e){this._xhr=e}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}function Sb(e){const t={};return e.split("\n").forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}class kb{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){m(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const Cb=xd("downloadFile",(({url:e,header:t={},timeout:n=__uniConfig.networkTimeout.downloadFile},{resolve:o,reject:r})=>{var i,a=new XMLHttpRequest,s=new kb(a);return a.open("GET",e,!0),Object.keys(t).forEach((e=>{a.setRequestHeader(e,t[e])})),a.responseType="blob",a.onload=function(){clearTimeout(i);const t=a.status,n=this.response;let r;const s=a.getResponseHeader("content-disposition");if(s){const e=s.match(/filename="?(\S+)"?\b/);e&&(r=e[1])}n.name=r||function(e){const t=(e=e.split("#")[0].split("?")[0]).split("/");return t[t.length-1]}(e),o({statusCode:t,tempFilePath:yh(n)})},a.onabort=function(){clearTimeout(i),r("abort",{errCode:600003})},a.onerror=function(){clearTimeout(i),r("",{errCode:602001})},a.onprogress=function(e){s._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesWritten:n,totalBytesExpectedToWrite:o})}))},a.send(),i=setTimeout((function(){a.onprogress=a.onload=a.onabort=a.onerror=null,s.abort(),r("timeout",{errCode:5})}),n),s}),0,Of);class Eb{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){m(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const Ab=xd("uploadFile",(({url:e,file:t,filePath:n,name:o,files:r,header:i={},formData:a={},timeout:s=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new Eb;return f(r)&&r.length||(r=[{name:o,file:t,uri:n}]),Promise.all(r.map((({file:e,uri:t})=>e instanceof Blob?Promise.resolve(vh(e)):mh(t)))).then((function(t){var n,o=new XMLHttpRequest,d=new FormData;Object.keys(a).forEach((e=>{d.append(e,a[e])})),Object.values(r).forEach((({name:e},n)=>{const o=t[n];d.append(e||"file",o,o.name||`file-${Date.now()}`)})),o.open("POST",e),Object.keys(i).forEach((e=>{o.setRequestHeader(e,i[e])})),o.upload.onprogress=function(e){u._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesSent:n,totalBytesExpectedToSend:o})}))},o.onerror=function(){clearTimeout(n),c("",{errCode:602001})},o.onabort=function(){clearTimeout(n),c("abort",{errCode:600003})},o.onload=function(){clearTimeout(n);const e=o.status;l({statusCode:e,data:o.responseText||o.response})},u._isAbort?c("abort",{errCode:600003}):(n=setTimeout((function(){o.upload.onprogress=o.onload=o.onabort=o.onerror=null,u.abort(),c("timeout",{errCode:5})}),s),o.send(d),u._xhr=o)})).catch((()=>{setTimeout((()=>{c("file error")}),0)})),u}),0,$f),Mb=[],Pb={open:"",close:"",error:"",message:""};class Ib{constructor(e,t,n){let o;this._callbacks={open:[],close:[],error:[],message:[]};try{const n=this._webSocket=new WebSocket(e,t);n.binaryType="arraybuffer";["open","close","error","message"].forEach((e=>{this._callbacks[e]=[],n.addEventListener(e,(t=>{const{data:n,code:o,reason:r}=t,i="message"===e?{data:n}:"close"===e?{code:o,reason:r}:{};if(this._callbacks[e].forEach((t=>{try{t(i)}catch(yC){console.error(`thirdScriptError\n${yC};at socketTask.on${I(e)} callback function\n`,yC)}})),this===Mb[0]&&Pb[e]&&Cx.invokeOnCallback(Pb[e],i),"error"===e||"close"===e){const e=Mb.indexOf(this);e>=0&&Mb.splice(e,1)}}))}));["CLOSED","CLOSING","CONNECTING","OPEN","readyState"].forEach((e=>{Object.defineProperty(this,e,{get:()=>n[e]})}))}catch(yC){o=yC}n&&n(o,this)}send(e){const t=(e||{}).data,n=this._webSocket;try{if(n.readyState!==n.OPEN)throw he(e,{errMsg:"sendSocketMessage:fail SocketTask.readyState is not OPEN",errCode:10002}),new Error("SocketTask.readyState is not OPEN");n.send(t),he(e,"sendSocketMessage:ok")}catch(o){he(e,{errMsg:`sendSocketMessage:fail ${o}`,errCode:602001})}}close(e={}){const t=this._webSocket;try{const n=e.code||1e3,o=e.reason;v(o)?t.close(n,o):t.close(n),he(e,"closeSocket:ok")}catch(n){he(e,`closeSocket:fail ${n}`)}}onOpen(e){this._callbacks.open.push(e)}onMessage(e){this._callbacks.message.push(e)}onError(e){this._callbacks.error.push(e)}onClose(e){this._callbacks.close.push(e)}}const Lb=xd("connectSocket",(({url:e,protocols:t},{resolve:n,reject:o})=>new Ib(e,t,((e,t)=>{e?o(e.toString(),{errCode:600009}):(Mb.push(t),n())}))),0,Df);function Ob(e,t,n,o,r){const i=e[t];m(i)&&i.call(e,c({},n,{success(){o()},fail({errMsg:e}){r(e.replace("sendSocketMessage:fail ",""))},complete:void 0}))}const $b=Sd("sendSocketMessage",((e,{resolve:t,reject:n})=>{const o=Mb[0];o&&o.readyState===o.OPEN?Ob(o,"send",e,t,n):n("WebSocket is not connected")})),Db=Sd("closeSocket",((e,{resolve:t,reject:n})=>{const o=Mb[0];o?Ob(o,"close",e,t,n):n("WebSocket is not connected")}));function Rb(e){const t=`onSocket${I(e)}`;return _d(t,(()=>{Pb[e]=t}))}const Bb=Rb("open"),Nb=Rb("error"),qb=Rb("message"),jb=Rb("close"),zb=Sd("getLocation",(({type:e,altitude:t,highAccuracyExpireTime:n,isHighAccuracy:o},{resolve:r,reject:i})=>{const a=Ov();new Promise(((e,r)=>{navigator.geolocation?navigator.geolocation.getCurrentPosition((t=>e({coords:t.coords})),r,{enableHighAccuracy:o||t,timeout:n||1e5}):r(new Error("device nonsupport geolocation"))})).catch((e=>new Promise(((t,n)=>{a.type===Lv.QQ?Sv(`https://apis.map.qq.com/ws/location/v1/ip?output=jsonp&key=${a.key}`,{callback:"callback"},(e=>{if("result"in e&&e.result.location){const n=e.result.location;t({coords:{latitude:n.lat,longitude:n.lng},skip:!0})}else n(new Error(e.message||JSON.stringify(e)))}),(()=>n(new Error("network error")))):a.type===Lv.GOOGLE?xb({method:"POST",url:`https://www.googleapis.com/geolocation/v1/geolocate?key=${a.key}`,success(e){const o=e.data;"location"in o?t({coords:{latitude:o.location.lat,longitude:o.location.lng,accuracy:o.accuracy},skip:!0}):n(new Error(o.error&&o.error.message||JSON.stringify(e)))},fail(){n(new Error("network error"))}}):a.type===Lv.AMAP?Ev([],(()=>{window.AMap.plugin("AMap.Geolocation",(()=>{new window.AMap.Geolocation({enableHighAccuracy:!0,timeout:1e4}).getCurrentPosition(((e,o)=>{"complete"===e?t({coords:{latitude:o.position.lat,longitude:o.position.lng,accuracy:o.accuracy},skip:!0}):n(new Error(o.message))}))}))})):n(e)})))).then((({coords:t,skip:n})=>{Nv(e,t,n).then((e=>{r({latitude:e.latitude,longitude:e.longitude,accuracy:e.accuracy,speed:e.altitude||0,altitude:e.altitude||0,verticalAccuracy:e.altitudeAccuracy||0,horizontalAccuracy:e.accuracy||0})})).catch((e=>{i(e.message)}))})).catch((e=>{i(e.message||JSON.stringify(e))}))}),0,bf);const Vb=cu({name:"LocationView",props:{latitude:{type:Number},longitude:{type:Number},scale:{type:Number,default:18},name:{type:String,default:""},address:{type:String,default:""}},emits:["close"],setup(e,{emit:t}){const n=function(e){const t=Ut({center:{latitude:0,longitude:0},marker:{id:1,latitude:0,longitude:0,iconPath:Iv,width:32,height:52},location:{id:2,latitude:0,longitude:0,iconPath:Pv,width:44,height:44}});function n(){e.latitude&&e.longitude&&(t.center.latitude=e.latitude,t.center.longitude=e.longitude,t.marker.latitude=e.latitude,t.marker.longitude=e.longitude)}return to([()=>e.latitude,()=>e.longitude],n),n(),t}(e);function o(e){const t=e.detail.centerLocation;t&&(n.center.latitude=t.latitude,n.center.longitude=t.longitude)}function r(){const t=Ov();let o="";if(t.type===Lv.GOOGLE){o=`https://www.google.com/maps/dir/?api=1${n.location.latitude?`&origin=${n.location.latitude}%2C${n.location.longitude}`:""}&destination=${e.latitude}%2C${e.longitude}`}else if(t.type===Lv.QQ){o=`https://apis.map.qq.com/uri/v1/routeplan?type=drive${n.location.latitude?`&fromcoord=${n.location.latitude}%2C${n.location.longitude}&from=${encodeURIComponent("我的位置")}`:""}&tocoord=${e.latitude}%2C${e.longitude}&to=${encodeURIComponent(e.name||"目的地")}&ref=${t.key}`}else if(t.type===Lv.AMAP){o=`https://uri.amap.com/navigation?${n.location.latitude?`from=${n.location.longitude},${n.location.latitude},${encodeURIComponent("我的位置")}&`:""}to=${e.longitude},${e.latitude},${encodeURIComponent(e.name||"目的地")}`}window.open(o)}function i(){t("close")}function a({latitude:e,longitude:t}){n.center.latitude=e,n.center.longitude=t}return db(),zb({type:"gcj02",success:({latitude:e,longitude:t})=>{n.location.latitude=e,n.location.longitude=t}}),()=>ri("div",{class:"uni-system-open-location"},[ri(fx,{latitude:n.center.latitude,longitude:n.center.longitude,class:"map",markers:[n.marker,n.location],onRegionchange:o},{default:()=>[ri("div",{class:"map-move",onClick:()=>a(n.location)},[wc(Mv,"#000000",24)],8,["onClick"])]},8,["latitude","longitude","markers","onRegionchange"]),ri("div",{class:"info"},[ri("div",{class:"name",onClick:()=>a(n.marker)},[e.name],8,["onClick"]),ri("div",{class:"address",onClick:()=>a(n.marker)},[e.address],8,["onClick"]),ri("div",{class:"nav",onClick:r},[wc("M28 17c-6.49396875 0-12.13721875 2.57040625-15 6.34840625V5.4105l6.29859375 6.29859375c0.387875 0.387875 1.02259375 0.387875 1.4105 0 0.387875-0.387875 0.387875-1.02259375 0-1.4105L12.77853125 2.36803125a0.9978125 0.9978125 0 0 0-0.0694375-0.077125c-0.1944375-0.1944375-0.45090625-0.291375-0.70721875-0.290875l-0.00184375-0.0000625-0.00184375 0.0000625c-0.2563125-0.0005-0.51278125 0.09640625-0.70721875 0.290875a0.9978125 0.9978125 0 0 0-0.0694375 0.077125l-7.930625 7.9305625c-0.387875 0.387875-0.387875 1.02259375 0 1.4105 0.387875 0.387875 1.02259375 0.387875 1.4105 0L11 5.4105V29c0 0.55 0.45 1 1 1s1-0.45 1-1c0-5.52284375 6.71571875-10 15-10 0.55228125 0 1-0.44771875 1-1 0-0.55228125-0.44771875-1-1-1z","#ffffff",26)],8,["onClick"])]),ri("div",{class:"nav-btn-back",onClick:i},[wc(yc,"#ffffff",26)],8,["onClick"])])}});let Fb=null;const Ub=Sd("openLocation",((e,{resolve:t})=>{Fb?c(Fb,e):(Fb=Ut(e),Cn((()=>{const e=ib(Vb,Fb,(()=>{Fb=null,Cn((()=>{e.unmount()}))}));e.mount(ab("u-a-o"))}))),t()}),0,wf);const Wb=cu({name:"LoctaionPicker",props:{latitude:{type:Number},longitude:{type:Number}},emits:["close"],setup(e,{emit:t}){db(),Il();const{t:n}=bl(),o=function(e){const t=Ut({latitude:0,longitude:0,keyword:"",searching:!1});function n(){e.latitude&&e.longitude&&(t.latitude=e.latitude,t.longitude=e.longitude)}return to([()=>e.latitude,()=>e.longitude],n),n(),t}(e),{list:r,listState:i,loadMore:a,reset:s,getList:l}=function(e){const t=__uniConfig.qqMapKey,n=Ut([]),o=sn(-1),r=Ei((()=>n[o.value])),i=Ut({loading:!0,pageSize:20,pageIndex:1,hasNextPage:!0,nextPage:null,selectedIndex:o,selected:r}),a=sn(""),s=Ei((()=>a.value?`region(${a.value},1,${e.latitude},${e.longitude})`:`nearby(${e.latitude},${e.longitude},5000)`));function l(e){e.forEach((e=>{n.push({name:e.title||e.name,address:e.address,distance:e._distance||e.distance,latitude:e.location.lat,longitude:e.location.lng})}))}function c(){i.loading=!0;const o=Ov();if(o.type===Lv.GOOGLE){if(i.pageIndex>1&&i.nextPage)return void i.nextPage();new google.maps.places.PlacesService(document.createElement("div"))[e.searching?"textSearch":"nearbySearch"]({location:{lat:e.latitude,lng:e.longitude},query:e.keyword,radius:5e3},((e,t,o)=>{i.loading=!1,e&&e.length&&e.forEach((e=>{n.push({name:e.name||"",address:e.vicinity||e.formatted_address||"",distance:0,latitude:e.geometry.location.lat(),longitude:e.geometry.location.lng()})})),o&&(o.hasNextPage?i.nextPage=()=>{o.nextPage()}:i.hasNextPage=!1)}))}else o.type===Lv.QQ?Sv(e.searching?`https://apis.map.qq.com/ws/place/v1/search?output=jsonp&key=${t}&boundary=${s.value}&keyword=${e.keyword}&page_size=${i.pageSize}&page_index=${i.pageIndex}`:`https://apis.map.qq.com/ws/geocoder/v1/?output=jsonp&key=${t}&location=${e.latitude},${e.longitude}&get_poi=1&poi_options=page_size=${i.pageSize};page_index=${i.pageIndex}`,{callback:"callback"},(t=>{if(i.loading=!1,e.searching&&"data"in t&&t.data.length)l(t.data);else if("result"in t){const e=t.result;a.value=e.ad_info?e.ad_info.adcode:"",e.pois&&l(e.pois)}n.length===i.pageSize*i.pageIndex&&(i.hasNextPage=!1)}),(()=>{i.loading=!1})):o.type===Lv.AMAP&&window.AMap.plugin("AMap.PlaceSearch",(function(){const t=new window.AMap.PlaceSearch({city:"全国",pageSize:10,pageIndex:i.pageIndex}),n=e.searching?e.keyword:"",o=e.searching?5e4:5e3;t.searchNearBy(n,[e.longitude,e.latitude],o,(function(e,t){"error"===e?console.error(t):"no_data"===e?i.hasNextPage=!1:l(t.poiList.pois)})),i.loading=!1}))}return{listState:i,list:n,loadMore:function(){!i.loading&&i.hasNextPage&&(i.pageIndex++,c())},reset:function(){i.selectedIndex=-1,i.pageIndex=1,i.hasNextPage=!0,i.nextPage=null,n.splice(0,n.length)},getList:c}}(o),u=ke((()=>{s(),o.keyword&&l()}),1e3,{setTimeout:setTimeout,clearTimeout:clearTimeout});function d(e){o.keyword=e.detail.value,u()}function p(){t("close",c({},i.selected))}function f(){t("close")}function h(e){const t=e.detail.centerLocation;t&&m(t)}function g(){zb({type:"gcj02",success:m,fail:()=>{}})}function m({latitude:e,longitude:t}){o.latitude=e,o.longitude=t,o.searching||(s(),l())}return to((()=>o.searching),(e=>{s(),e||l()})),o.latitude&&o.longitude||g(),()=>{const e=r.map(((e,t)=>{return ri("div",{key:t,class:{"list-item":!0,selected:i.selectedIndex===t},onClick:()=>{i.selectedIndex=t,o.latitude=e.latitude,o.longitude=e.longitude}},[wc(_c,"#007aff",24),ri("div",{class:"list-item-title"},[e.name]),ri("div",{class:"list-item-detail"},[(n=e.distance,n>100?`${n>1e3?(n/1e3).toFixed(1)+"k":n.toFixed(0)}m | `:n>0?"<100m | ":""),e.address])],10,["onClick"]);var n}));return i.loading&&e.unshift(ri("div",{class:"list-loading"},[ri("i",{class:"uni-loading"},null)])),ri("div",{class:"uni-system-choose-location"},[ri(fx,{latitude:o.latitude,longitude:o.longitude,class:"map","show-location":!0,libraries:["places"],onUpdated:l,onRegionchange:h},{default:()=>[ri("div",{class:"map-location",style:`background-image: url("${Iv}")`},null),ri("div",{class:"map-move",onClick:g},[wc(Mv,"#000000",24)],8,["onClick"])],_:1},8,["latitude","longitude","show-location","onUpdated","onRegionchange"]),ri("div",{class:"nav"},[ri("div",{class:"nav-btn back",onClick:f},[wc(bc,"#ffffff",26)],8,["onClick"]),ri("div",{class:{"nav-btn":!0,confirm:!0,disable:!i.selected},onClick:p},[wc(_c,"#ffffff",26)],10,["onClick"])]),ri("div",{class:"menu"},[ri("div",{class:"search"},[ri(ag,{value:o.keyword,class:"search-input",placeholder:n("uni.chooseLocation.search"),onFocus:()=>o.searching=!0,onInput:d},null,8,["value","placeholder","onFocus","onInput"]),o.searching&&ri("div",{class:"search-btn",onClick:()=>{o.searching=!1,o.keyword=""}},[n("uni.chooseLocation.cancel")],8,["onClick"])]),ri(Yg,{"scroll-y":!0,class:"list",onScrolltolower:a},(t=e,"function"==typeof t||"[object Object]"===Object.prototype.toString.call(t)&&!Kr(t)?e:{default:()=>[e],_:2}),8,["scroll-y","onScrolltolower"])])]);var t}}});let Hb=null;const Yb=Sd("chooseLocation",((e,{resolve:t,reject:n})=>{Hb?n("cancel"):(Hb=Ut(e),Cn((()=>{const e=ib(Wb,Hb,(o=>{Hb=null,Cn((()=>{e.unmount()})),o?t(o):n("cancel")}));e.mount(ab("u-a-c"))})))}));let Xb=!1,Gb=0;const Jb=Sd("startLocationUpdate",((e,{resolve:t,reject:n})=>{navigator.geolocation?(Gb=Gb||navigator.geolocation.watchPosition((n=>{Xb=!0,Nv(null==e?void 0:e.type,n.coords).then((e=>{Cx.invokeOnCallback("onLocationChange",e),t()})).catch((e=>{Cx.invokeOnCallback("onLocationChangeError",{errMsg:`onLocationChange:fail ${e.message}`})}))}),(e=>{Xb||(n(e.message),Xb=!0),Cx.invokeOnCallback("onLocationChangeError",{errMsg:`onLocationChange:fail ${e.message}`})})),setTimeout(t,100)):n()}),0,Bf),Qb=Sd("stopLocationUpdate",((e,{resolve:t})=>{Gb&&(navigator.geolocation.clearWatch(Gb),Xb=!1,Gb=0),t()})),Kb=_d("onLocationChange",(()=>{})),Zb=wd("offLocationChange",(()=>{})),e_=_d("onLocationChangeError",(()=>{})),t_=wd("offLocationChangeError",(()=>{})),n_=Sd("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===Pc("onBackPress",{from:e.from||"navigateBack"})&&(o=!1),o?(uv().$router.go(-e.delta),t()):n("onBackPress")}),0,Ff);function o_({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:r},i){const a=uv().$router,{path:s,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:Se(n||"")}}(t);return new Promise(((t,c)=>{const u=function(e,t){return{__id__:t||++zm,__type__:e}}(e,i);a["navigateTo"===e?"push":"replace"]({path:s,query:l,state:u,force:!0}).then((i=>{if(xs(i))return c(i.message);if("switchTab"===e&&(a.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=a.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new Ce(u.__id__,o),t(r?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return r?t({__id__:u.__id__}):t()}))}))}const r_=Sd("navigateTo",(({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:r})=>o_({type:"navigateTo",url:e,events:t,isAutomatedTesting:n}).then(o).catch(r)),0,qf);const i_=Sd("redirectTo",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>(function(){const e=Sc();if(!e)return;const t=e.$page;jm(Um(t.path,t.id))}(),o_({type:"redirectTo",url:e,isAutomatedTesting:t}).then(n).catch(o))),0,jf);const a_=Sd("reLaunch",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>(function(){const e=Nm().keys();for(const t of e)jm(t)}(),o_({type:"reLaunch",url:e,isAutomatedTesting:t}).then(n).catch(o))),0,zf);function s_(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}const l_=Sd("switchTab",(({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:o,reject:r})=>(function(){const e=Ec();if(!e)return;const t=Nm(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:jm(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,Pc(e,"onHide"))}(),o_({type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},function(e){const t=Nm().values();for(const n of t){const t=n.$page;if(s_(e,t))return n.$.__isActive=!0,t.id}}(e)).then(o).catch(r))),0,Vf),c_=Sd("preloadPage",(({url:e},{resolve:t,reject:n})=>{const o=Rc(e.split("?")[0]);o?o.loader&&o.loader().then((()=>{t({url:e,errMsg:"preloadPage:ok"})})).catch((t=>{n(`${e} ${String(t)}`)})):n(`${e}}`)}));function u_(e){__uniConfig.darkmode&&Cx.on("onThemeChange",e)}function d_(e){Cx.off("onThemeChange",e)}function p_(e){let t={};return __uniConfig.darkmode&&(t=Re(e,__uniConfig.themeConfig,Zv())),__uniConfig.darkmode?t:e}function f_(e,t){const n=Xt(e),o=n?Ut(p_(e)):p_(e);return __uniConfig.darkmode&&n&&to(e,(e=>{const t=p_(e);for(const n in t)o[n]=t[n]})),t&&u_(t),o}const h_={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},g_=_o({props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},setup(e,{emit:t}){const n=sn(""),o=()=>a.value=!1,r=()=>(o(),t("close","cancel")),i=()=>(o(),t("close","confirm",n.value)),a=sb(e,{onEsc:r,onEnter:()=>{!e.editable&&i()}}),s=function(e){const t=sn(e.cancelColor),n=({theme:e})=>{((e,t)=>{t.value=h_[e].cancelColor})(e,t)};return Zn((()=>{e.visible?(t.value=e.cancelColor,"#000"===e.cancelColor&&("dark"===Zv()&&n({theme:"dark"}),u_(n))):d_(n)})),t}(e);return()=>{const{title:t,content:o,showCancel:l,confirmText:c,confirmColor:u,editable:d,placeholderText:p}=e;return n.value=o,ri(Di,{name:"uni-fade"},{default:()=>[ao(ri("uni-modal",{onTouchmove:ac},[rb,ri("div",{class:"uni-modal"},[t?ri("div",{class:"uni-modal__hd"},[ri("strong",{class:"uni-modal__title",textContent:t||""},null,8,["textContent"])]):null,d?ri("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:p,value:o,onInput:e=>n.value=e.target.value},null,40,["placeholder","value","onInput"]):ri("div",{class:"uni-modal__bd",onTouchmovePassive:sc,textContent:o},null,40,["onTouchmovePassive","textContent"]),ri("div",{class:"uni-modal__ft"},[l&&ri("div",{style:{color:s.value},class:"uni-modal__btn uni-modal__btn_default",onClick:r},[e.cancelText],12,["onClick"]),ri("div",{style:{color:u},class:"uni-modal__btn uni-modal__btn_primary",onClick:i},[c],12,["onClick"])])])],40,["onTouchmove"]),[[Gi,a.value]])]})}}});let m_;const v_=de((()=>{Cx.on("onHidePopup",(()=>m_.visible=!1))}));let y_;function b_(e,t){const n="confirm"===e,o={confirm:n,cancel:"cancel"===e};n&&m_.editable&&(o.content=t),y_&&y_(o)}const __=Sd("showModal",((e,{resolve:t})=>{v_(),y_=t,m_?(c(m_,e),m_.visible=!0):(m_=Ut(e),Cn((()=>(ib(g_,m_,b_).mount(ab("u-a-m")),Cn((()=>m_.visible=!0))))))}),0,Zf),w_={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==eh.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},x_={light:"#fff",dark:"rgba(255,255,255,0.9)"},T_=e=>x_[e],S_=_o({name:"Toast",props:w_,setup(e){Tl(),Sl();const{Icon:t}=function(e){const t=sn(T_(Zv())),n=({theme:e})=>t.value=T_(e);Zn((()=>{e.visible?u_(n):d_(n)}));return{Icon:Ei((()=>{switch(e.icon){case"success":return ri(wc(mc,t.value,38),{class:"uni-toast__icon"});case"error":return ri(wc(vc,t.value,38),{class:"uni-toast__icon"});case"loading":return ri("i",{class:["uni-toast__icon","uni-loading"]},null,2);default:return null}}))}}(e),n=sb(e,{});return()=>{const{mask:o,duration:r,title:i,image:a}=e;return ri(Di,{name:"uni-fade"},{default:()=>[ao(ri("uni-toast",{"data-duration":r},[o?ri("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:ac},null,40,["onTouchmove"]):"",a||t.value?ri("div",{class:"uni-toast"},[a?ri("img",{src:a,class:"uni-toast__icon"},null,10,["src"]):t.value,ri("p",{class:"uni-toast__content"},[i])]):ri("div",{class:"uni-sample-toast"},[ri("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[Gi,n.value]])]})}}});let k_,C_,E_="";const A_=je();function M_(e){k_?c(k_,e):(k_=Ut(c(e,{visible:!1})),Cn((()=>{A_.run((()=>{to([()=>k_.visible,()=>k_.duration],(([e,t])=>{if(e){if(C_&&clearTimeout(C_),"onShowLoading"===E_)return;C_=setTimeout((()=>{D_("onHideToast")}),t)}else C_&&clearTimeout(C_)}))})),Cx.on("onHidePopup",(()=>D_("onHidePopup"))),ib(S_,k_,(()=>{})).mount(ab("u-a-t"))}))),setTimeout((()=>{k_.visible=!0}),10)}const P_=Sd("showToast",((e,{resolve:t,reject:n})=>{M_(e),E_="onShowToast",t()}),0,th),I_={icon:"loading",duration:1e8,image:""},L_=Sd("showLoading",((e,{resolve:t,reject:n})=>{c(e,I_),M_(e),E_="onShowLoading",t()}),0,Kf),O_=Sd("hideToast",((e,{resolve:t,reject:n})=>{D_("onHideToast"),t()})),$_=Sd("hideLoading",((e,{resolve:t,reject:n})=>{D_("onHideLoading"),t()}));function D_(e){const{t:t}=bl();if(!E_)return;let n="";if("onHideToast"===e&&"onShowToast"!==E_?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==E_&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);E_="",setTimeout((()=>{k_.visible=!1}),10)}function R_(e){const t=sn(0),n=sn(0),o=Ei((()=>t.value>=500&&n.value>=500)),r=Ei((()=>{const t={content:{transform:"",left:"",top:"",bottom:""},triangle:{left:"",top:"",bottom:"","border-width":"","border-color":""}},r=t.content,i=t.triangle,a=e.popover;function s(e){return Number(e)||0}if(o.value&&a){c(i,{position:"absolute",width:"0",height:"0","margin-left":"-6px","border-style":"solid"});const e=s(a.left),t=s(a.width),o=s(a.top),l=s(a.height),u=e+t/2;r.transform="none !important";const d=Math.max(0,u-150);r.left=`${d}px`;let p=Math.max(12,u-d);p=Math.min(288,p),i.left=`${p}px`;const f=n.value/2;o+l-f>f-o?(r.top="auto",r.bottom=n.value-o+6+"px",i.bottom="-6px",i["border-width"]="6px 6px 0 6px",i["border-color"]="#fcfcfd transparent transparent transparent"):(r.top=`${o+l+6}px`,i.top="-6px",i["border-width"]="0 6px 6px 6px",i["border-color"]="transparent transparent #fcfcfd transparent")}return t}));return jo((()=>{const e=()=>{const{windowWidth:e,windowHeight:o,windowTop:r}=uni.getSystemInfoSync();t.value=e,n.value=o+(r||0)};window.addEventListener("resize",e),e(),Uo((()=>{window.removeEventListener("resize",e)}))})),{isDesktop:o,popupStyle:r}}const B_={light:{listItemColor:"#000000",cancelItemColor:"#000000"},dark:{listItemColor:"rgba(255, 255, 255, 0.8)",cancelItemColor:"rgba(255, 255, 255)"}};const N_=_o({name:"ActionSheet",props:{title:{type:String,default:""},itemList:{type:Array,default:()=>[]},itemColor:{type:String,default:"#000000"},popover:{type:Object,default:null},visible:{type:Boolean,default:!1}},emits:["close"],setup(e,{emit:t}){xl();const n=sn(260),o=sn(0),r=sn(0),i=sn(0),a=sn(0),s=sn(null),l=sn(null),{t:c}=bl(),{_close:u}=function(e,t){function n(e){t("close",e)}const{key:o,disable:r}=ob();return to((()=>e.visible),(e=>r.value=!e)),Zn((()=>{const{value:e}=o;"esc"===e&&n&&n(-1)})),{_close:n}}(e,t),{popupStyle:d}=R_(e);let p;function f(e){const t=i.value+e.deltaY;Math.abs(t)>10?(a.value+=t/3,a.value=a.value>=o.value?o.value:a.value<=0?0:a.value,p.scrollTo(a.value)):i.value=t,e.preventDefault()}jo((()=>{const{scroller:e,handleTouchStart:t,handleTouchMove:n,handleTouchEnd:o}=Ng(s.value,{enableY:!0,friction:new Lg(1e-4),spring:new Dg(2,90,20),onScroll:e=>{a.value=e.target.scrollTop}});p=e,mg(s.value,(r=>{if(e)switch(r.detail.state){case"start":t(r);break;case"move":n(r);break;case"end":case"cancel":o(r)}}),!0)})),to((()=>e.visible),(()=>{Cn((()=>{e.title&&(r.value=document.querySelector(".uni-actionsheet__title").offsetHeight),p.update(),s.value&&(o.value=s.value.clientHeight-n.value),document.querySelectorAll(".uni-actionsheet__cell").forEach((e=>{!function(e){const t=20;let n=0,o=0;e.addEventListener("touchstart",(e=>{const t=e.changedTouches[0];n=t.clientX,o=t.clientY})),e.addEventListener("touchend",(e=>{const r=e.changedTouches[0];if(Math.abs(r.clientX-n)<t&&Math.abs(r.clientY-o)<t){const t=e.target,n=e.currentTarget,o=new CustomEvent("click",{bubbles:!0,cancelable:!0,target:t,currentTarget:n});["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{o[e]=r[e]})),e.target.dispatchEvent(o)}}))}(e)}))}))}));const h=function(e){const t=Ut({listItemColor:"#000",cancelItemColor:"#000"}),n=({theme:e})=>{!function(e,t){["listItemColor","cancelItemColor"].forEach((n=>{t[n]=B_[e][n]}))}(e,t)};return Zn((()=>{e.visible?(t.listItemColor=t.cancelItemColor=e.itemColor,"#000"===e.itemColor&&(n({theme:Zv()}),u_(n))):d_(n)})),t}(e);return()=>ri("uni-actionsheet",{onTouchmove:ac},[ri(Di,{name:"uni-fade"},{default:()=>[ao(ri("div",{class:"uni-mask uni-actionsheet__mask",onClick:()=>u(-1)},null,8,["onClick"]),[[Gi,e.visible]])]}),ri("div",{class:["uni-actionsheet",{"uni-actionsheet_toggle":e.visible}],style:d.value.content},[ri("div",{ref:l,class:"uni-actionsheet__menu",onWheel:f},[e.title?ri(jr,null,[ri("div",{class:"uni-actionsheet__cell",style:{height:`${r.value}px`}},null),ri("div",{class:"uni-actionsheet__title"},[e.title])]):"",ri("div",{style:{maxHeight:`${n.value}px`,overflow:"hidden"}},[ri("div",{ref:s},[e.itemList.map(((e,t)=>ri("div",{key:t,style:{color:h.listItemColor},class:"uni-actionsheet__cell",onClick:()=>u(t)},[e],12,["onClick"])))],512)])],40,["onWheel"]),ri("div",{class:"uni-actionsheet__action"},[ri("div",{style:{color:h.cancelItemColor},class:"uni-actionsheet__cell",onClick:()=>u(-1)},[c("uni.showActionSheet.cancel")],12,["onClick"])]),ri("div",{style:d.value.triangle},null,4)],6)],40,["onTouchmove"])}});let q_,j_,z_;const V_=de((()=>{Cx.on("onHidePopup",(()=>z_.visible=!1))}));function F_(e){-1===e?j_&&j_("cancel"):q_&&q_({tapIndex:e})}const U_=Sd("showActionSheet",((e,{resolve:t,reject:n})=>{V_(),q_=t,j_=n,z_?(c(z_,e),z_.visible=!0):(z_=Ut(e),Cn((()=>(ib(N_,z_,F_).mount(ab("u-s-a-s")),Cn((()=>z_.visible=!0))))))}),0,Qf),W_=Sd("loadFontFace",(({family:e,source:t,desc:n},{resolve:o,reject:r})=>{(function(e,t,n){const o=document.fonts;if(o){const r=new FontFace(e,t,n);return r.load().then((()=>{o.add&&o.add(r)}))}return new Promise((o=>{const r=document.createElement("style"),i=[];if(n){const{style:e,weight:t,stretch:o,unicodeRange:r,variant:a,featureSettings:s}=n;e&&i.push(`font-style:${e}`),t&&i.push(`font-weight:${t}`),o&&i.push(`font-stretch:${o}`),r&&i.push(`unicode-range:${r}`),a&&i.push(`font-variant:${a}`),s&&i.push(`font-feature-settings:${s}`)}r.innerText=`@font-face{font-family:"${e}";src:${t};${i.join(";")}}`,document.head.appendChild(r),o()}))})(e,t=t.startsWith('url("')||t.startsWith("url('")?`url('${Tu(t.substring(5,t.length-2))}')`:t.startsWith("url(")?`url('${Tu(t.substring(4,t.length-1))}')`:Tu(t),n).then((()=>{o()})).catch((e=>{r(`loadFontFace:fail ${e}`)}))}));function H_(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,Cx.emit("onNavigationBarChange",{titleText:t})}Zn(t),Mo(t)}function Y_(e,t,n,o,r){if(!e)return r("page not found");const{navigationBar:i}=e;switch(t){case"setNavigationBarColor":const{frontColor:e,backgroundColor:t,animation:o}=n,{duration:r,timingFunc:a}=o;e&&(i.titleColor="#000000"===e?"#000000":"#ffffff"),t&&(i.backgroundColor=t),i.duration=r+"ms",i.timingFunc=a;break;case"showNavigationBarLoading":i.loading=!0;break;case"hideNavigationBarLoading":i.loading=!1;break;case"setNavigationBarTitle":const{title:s}=n;i.titleText=s}o()}const X_=Sd("setNavigationBarColor",((e,{resolve:t,reject:n})=>{Y_(kc(),"setNavigationBarColor",e,t,n)}),0,Gf),G_=Sd("showNavigationBarLoading",((e,{resolve:t,reject:n})=>{Y_(kc(),"showNavigationBarLoading",e||{},t,n)})),J_=Sd("hideNavigationBarLoading",((e,{resolve:t,reject:n})=>{Y_(kc(),"hideNavigationBarLoading",e||{},t,n)})),Q_=Sd("setNavigationBarTitle",((e,{resolve:t,reject:n})=>{Y_(kc(),"setNavigationBarTitle",e,t,n)})),K_=Sd("pageScrollTo",(({scrollTop:e,selector:t,duration:n},{resolve:o})=>{!function(e,t,n){if(v(e)){const t=document.querySelector(e);if(t){const{top:n}=t.getBoundingClientRect();e=n+window.pageYOffset;const o=document.querySelector("uni-page-head");o&&(e-=o.offsetHeight)}}e<0&&(e=0);const o=document.documentElement,{clientHeight:r,scrollHeight:i}=o;if(e=Math.min(e,i-r),0===t)return void(o.scrollTop=document.body.scrollTop=e);if(window.scrollY===e)return;const a=t=>{if(t<=0)return void window.scrollTo(0,e);const n=e-window.scrollY;requestAnimationFrame((function(){window.scrollTo(0,window.scrollY+n/t*10),a(t-10)}))};a(t)}(t||e||0,n),o()}),0,Jf),Z_=Sd("startPullDownRefresh",((e,{resolve:t})=>{Cx.invokeViewMethod("startPullDownRefresh",{},Cc()),t()})),ew=Sd("stopPullDownRefresh",((e,{resolve:t})=>{Cx.invokeViewMethod("stopPullDownRefresh",{},Cc()),t()})),tw=["text","iconPath","iconfont","selectedIconPath","visible"],nw=["color","selectedColor","backgroundColor","borderStyle","borderColor","midButton"],ow=["badge","redDot"];function rw(e,t,n){t.forEach((function(t){p(n,t)&&(e[t]=n[t])}))}function iw(e,t,n,o){var r;let i=!1;const a=qm();if(a.length&&a[a.length-1].$page.meta.isTabBar&&(i=!0),!i)return o("not TabBar page");const{index:s}=t;if("number"==typeof s){const e=null==(r=null==__uniConfig?void 0:__uniConfig.tabBar)?void 0:r.list.length;if(!e||s>=e)return o("tabbar item not found")}const l=Cm();switch(e){case"showTabBar":l.shown=!0;break;case"hideTabBar":l.shown=!1;break;case"setTabBarItem":const e=l.list[s],n=e.pagePath;rw(e,tw,t);const{pagePath:o}=t;if(o){const e=ce(o);e!==n&&function(e,t,n){const o=Rc(ce(t));if(o){const{meta:e}=o;delete e.tabBarIndex,e.isQuit=e.isTabBar=!1}const r=Rc(ce(n));if(r){const{meta:t}=r;t.tabBarIndex=e,t.isQuit=t.isTabBar=!0;const o=__uniConfig.tabBar;o&&o.list&&o.list[e]&&(o.list[e].pagePath=ue(n))}}(s,n,e)}break;case"setTabBarStyle":rw(l,nw,t);break;case"showTabBarRedDot":rw(l.list[s],ow,{badge:"",redDot:!0});break;case"setTabBarBadge":rw(l.list[s],ow,{badge:t.text,redDot:!0});break;case"hideTabBarRedDot":case"removeTabBarBadge":rw(l.list[s],ow,{badge:"",redDot:!1})}n()}const aw=Sd("setTabBarItem",((e,{resolve:t,reject:n})=>{iw("setTabBarItem",e,t,n)}),0,oh),sw=Sd("setTabBarStyle",((e,{resolve:t,reject:n})=>{iw("setTabBarStyle",e,t,n)}),0,ih),lw=Sd("hideTabBar",((e,{resolve:t,reject:n})=>{iw("hideTabBar",e||{},t,n)})),cw=Sd("showTabBar",((e,{resolve:t,reject:n})=>{iw("showTabBar",e||{},t,n)})),uw=Sd("hideTabBarRedDot",((e,{resolve:t,reject:n})=>{iw("hideTabBarRedDot",e,t,n)}),0,ah),dw=Sd("showTabBarRedDot",((e,{resolve:t,reject:n})=>{iw("showTabBarRedDot",e,t,n)}),0,sh),pw=Sd("removeTabBarBadge",((e,{resolve:t,reject:n})=>{iw("removeTabBarBadge",e,t,n)}),0,lh),fw=Sd("setTabBarBadge",((e,{resolve:t,reject:n})=>{iw("setTabBarBadge",e,t,n)}),0,ch),hw=cu({name:"TabBar",setup(){const e=sn([]),t=Cm(),n=f_(t,(()=>{const e=p_(t);n.backgroundColor=e.backgroundColor,n.borderStyle=e.borderStyle,n.color=e.color,n.selectedColor=e.selectedColor,n.blurEffect=e.blurEffect,n.midButton=e.midButton,e.list&&e.list.length&&e.list.forEach(((e,t)=>{n.list[t].iconPath=e.iconPath,n.list[t].selectedIconPath=e.selectedIconPath}))}));!function(e,t){function n(){let n=[];n=e.list.filter((e=>!1!==e.visible)),t.value=n}sn(c({type:"midButton"},e.midButton)),Zn(n)}(n,e),function(e){to((()=>e.shown),(t=>{pc({"--window-bottom":Rm(t?parseInt(e.height):0)})}))}(n);const o=function(e,t,n){return Zn((()=>{const o=e.meta;if(o.isTabBar){const e=o.route,r=n.value.findIndex((t=>t.pagePath===e));t.selectedIndex=r}})),(t,n)=>()=>{const{pagePath:o,text:r}=t;let i=ce(o);i===__uniRoutes[0].alias&&(i="/"),e.path!==i?uni.switchTab({from:"tabBar",url:i,tabBarText:r}):Pc("onTabItemTap",{index:n,text:r,pagePath:o})}}(ol(),n,e),{style:r,borderStyle:i,placeholderStyle:a}=function(e){const t=Ei((()=>{let t=e.backgroundColor;const n=e.blurEffect;return t||Lm&&n&&"none"!==n&&(t=gw[n]),{backgroundColor:t||"#f7f7fa",backdropFilter:"none"!==n?"blur(10px)":n}})),n=Ei((()=>{const{borderStyle:t,borderColor:n}=e;return n&&v(n)?{backgroundColor:n}:{backgroundColor:mw[t]||t}})),o=Ei((()=>({height:e.height})));return{style:t,borderStyle:n,placeholderStyle:o}}(n);return jo((()=>{n.iconfontSrc&&W_({family:"UniTabbarIconFont",source:`url("${n.iconfontSrc}")`})})),()=>{const t=function(e,t,n){const{selectedIndex:o,selectedColor:r,color:i}=e;return n.value.map(((n,a)=>{const s=o===a;return function(e,t,n,o,r,i,a,s){return ri("div",{key:a,class:"uni-tabbar__item",onClick:s(r,a)},[vw(e,t||"",n,o,r,i)],8,["onClick"])}(s?r:i,s&&n.selectedIconPath||n.iconPath||"",n.iconfont?s&&n.iconfont.selectedText||n.iconfont.text:void 0,n.iconfont?s&&n.iconfont.selectedColor||n.iconfont.color:void 0,n,e,a,t)}))}(n,o,e);return ri("uni-tabbar",{class:"uni-tabbar-"+n.position},[ri("div",{class:"uni-tabbar",style:r.value},[ri("div",{class:"uni-tabbar-border",style:i.value},null,4),t],4),ri("div",{class:"uni-placeholder",style:a.value},null,4)],2)}}});const gw={dark:"rgb(0, 0, 0, 0.8)",light:"rgb(250, 250, 250, 0.8)",extralight:"rgb(250, 250, 250, 0.8)"},mw={white:"rgba(255, 255, 255, 0.33)",black:"rgba(0, 0, 0, 0.33)"};function vw(e,t,n,o,r,i){const{height:a}=i;return ri("div",{class:"uni-tabbar__bd",style:{height:a}},[n?bw(n,o||"rgb(0, 0, 0, 0.8)",r,i):t&&yw(t,r,i),r.text&&_w(e,r,i),r.redDot&&ww(r.badge)],4)}function yw(e,t,n){const{type:o,text:r}=t,{iconWidth:i}=n;return ri("div",{class:"uni-tabbar__icon"+(r?" uni-tabbar__icon__diff":""),style:{width:i,height:i}},["midButton"!==o&&ri("img",{src:Tu(e)},null,8,["src"])],6)}function bw(e,t,n,o){var r;const{type:i,text:a}=n,{iconWidth:s}=o,l="uni-tabbar__icon"+(a?" uni-tabbar__icon__diff":""),c={width:s,height:s},u={fontSize:(null==(r=n.iconfont)?void 0:r.fontSize)||s,color:t};return ri("div",{class:l,style:c},["midButton"!==i&&ri("div",{class:"uni-tabbar__iconfont",style:u},[e],4)],6)}function _w(e,t,n){const{iconPath:o,text:r}=t,{fontSize:i,spacing:a}=n;return ri("div",{class:"uni-tabbar__label",style:{color:e,fontSize:i,lineHeight:o?"normal":1.8,marginTop:o?a:"inherit"}},[r],4)}function ww(e){return ri("div",{class:"uni-tabbar__reddot"+(e?" uni-tabbar__badge":"")},[e],2)}let xw;function Tw(){return xw}const Sw=cu({name:"Layout",setup(e,{emit:t}){const n=sn(null);dc({"--status-bar-height":"0px","--top-window-height":"0px","--window-left":"0px","--window-right":"0px","--window-margin":"0px","--tab-bar-height":"0px"});const o=function(){const e=ol();return{routeKey:Ei((()=>Um("/"+e.meta.route,Sm()))),isTabBar:Ei((()=>e.meta.isTabBar)),routeCache:Hm}}(),{layoutState:r,windowState:i}=function(){Tm();{const e=Ut({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return to((()=>e.marginWidth),(e=>dc({"--window-margin":e+"px"}))),to((()=>e.leftWindowWidth+e.marginWidth),(e=>{dc({"--window-left":e+"px"})})),to((()=>e.rightWindowWidth+e.marginWidth),(e=>{dc({"--window-right":e+"px"})})),{layoutState:e,windowState:Ei((()=>({})))}}}();!function(e,t){const n=Tm();function o(){const o=document.body.clientWidth,r=qm();let i={};if(r.length>0){i=r[r.length-1].$page.meta}else{const e=Rc(n.path,!0);e&&(i=e.meta)}const a=parseInt(String((p(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let s=!1;s=o>a,s&&a?(e.marginWidth=(o-a)/2,Cn((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+a+"px;margin:0 auto;")}))):(e.marginWidth=0,Cn((()=>{const e=t.value;e&&e.removeAttribute("style")})))}to([()=>n.path],o),jo((()=>{o(),window.addEventListener("resize",o)}))}(r,n);const a=function(e){const t=Tm(),n=Cm(),o=Ei((()=>t.meta.isTabBar&&n.shown));return dc({"--tab-bar-height":n.height}),o}(),s=function(e){const t=sn(!1);return Ei((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(a);return xw=r,()=>{const e=function(e,t,n,o,r,i){return function({routeKey:e,isTabBar:t,routeCache:n}){return ri(tl,null,{default:zn((({Component:o})=>[(Hr(),Qr(Eo,{matchBy:"key",cache:n},[(Hr(),Qr(Xn(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))])),_:1})}(e)}(o),t=function(e){return ao(ri(hw,null,null,512),[[Gi,e.value]])}(a);return ri("uni-app",{ref:n,class:s.value},[e,t],2)}}});const kw=Sd("showTopWindow",((e,{resolve:t,reject:n})=>{const o=Tw();o?(o.apiShowTopWindow=!0,Cn(t)):n()})),Cw=Sd("hideTopWindow",((e,{resolve:t,reject:n})=>{const o=Tw();o?(o.apiShowTopWindow=!1,Cn(t)):n()})),Ew=Sd("showLeftWindow",((e,{resolve:t,reject:n})=>{const o=Tw();o?(o.apiShowLeftWindow=!0,Cn(t)):n()})),Aw=Sd("hideLeftWindow",((e,{resolve:t,reject:n})=>{const o=Tw();o?(o.apiShowLeftWindow=!1,Cn(t)):n()})),Mw=Sd("showRightWindow",((e,{resolve:t,reject:n})=>{const o=Tw();o?(o.apiShowRightWindow=!0,Cn(t)):n()})),Pw=Sd("hideRightWindow",((e,{resolve:t,reject:n})=>{const o=Tw();o?(o.apiShowRightWindow=!1,Cn(t)):n()})),Iw=Td(0,(()=>{const e=Tw();return c({},e&&e.topWindowStyle)})),Lw=Td(0,(e=>{const t=Tw();t&&(t.topWindowStyle=e)})),Ow=Td(0,(()=>{const e=Tw();return c({},e&&e.leftWindowStyle)})),$w=Td(0,(e=>{const t=Tw();t&&(t.leftWindowStyle=e)})),Dw=Td(0,(()=>{const e=Tw();return c({},e&&e.rightWindowStyle)})),Rw=Td(0,(e=>{const t=Tw();t&&(t.rightWindowStyle=e)})),Bw=Td(0,(e=>{const t=document.querySelector("uni-page-body");return t?t.querySelector(`#${e}`):null})),Nw=Sd("saveImageToPhotosAlbum",Ad("saveImageToPhotosAlbum")),qw=Td(0,Cd("getRecorderManager")),jw=Sd("saveVideoToPhotosAlbum",Ad("saveVideoToPhotosAlbum")),zw=Td(0,Cd("createCameraContext")),Vw=Td(0,Cd("createLivePlayerContext")),Fw=Sd("saveFile",Ad("saveFile")),Uw=Sd("getSavedFileList",Ad("getSavedFileList")),Ww=Sd("getSavedFileInfo",Ad("getSavedFileInfo")),Hw=Sd("removeSavedFile",Ad("removeSavedFile")),Yw=_d("onMemoryWarning",Ed("onMemoryWarning")),Xw=_d("onGyroscopeChange",Ed("onGyroscopeChange")),Gw=Sd("startGyroscope",Ad("startGyroscope")),Jw=Sd("stopGyroscope",Ad("stopGyroscope")),Qw=Sd("scanCode",Ad("scanCode")),Kw=Sd("setScreenBrightness",Ad("setScreenBrightness")),Zw=Sd("getScreenBrightness",Ad("getScreenBrightness")),ex=Sd("setKeepScreenOn",Ad("setKeepScreenOn")),tx=_d("onUserCaptureScreen",Ed("onUserCaptureScreen")),nx=Sd("addPhoneContact",Ad("addPhoneContact")),ox=Sd("login",Ad("login")),rx=Sd("getProvider",Ad("getProvider")),ix=Object.defineProperty({__proto__:null,$emit:Xd,$off:Yd,$on:Wd,$once:Hd,addInterceptor:Vd,addPhoneContact:nx,arrayBufferToBase64:Pd,base64ToArrayBuffer:Md,canIUse:$m,canvasGetImageData:wp,canvasPutImageData:xp,canvasToTempFilePath:Tp,chooseFile:Ky,chooseImage:eb,chooseLocation:Yb,chooseVideo:wb,clearStorage:zy,clearStorageSync:jy,closePreviewImage:bb,closeSocket:Db,connectSocket:Lb,createAnimation:Hp,createCameraContext:zw,createCanvasContext:_p,createInnerAudioContext:Xv,createIntersectionObserver:Ip,createLivePlayerContext:Vw,createMapContext:ep,createMediaQueryObserver:$p,createSelectorQuery:zp,createVideoContext:Qd,cssBackdropFilter:Lm,cssConstant:Im,cssEnv:Pm,cssVar:Mm,downloadFile:Cb,getAppBaseInfo:ay,getClipboardData:Ay,getDeviceInfo:iy,getElementById:Bw,getEnterOptionsSync:rf,getFileInfo:Uy,getImageInfo:Yy,getLaunchOptionsSync:af,getLeftWindowStyle:Ow,getLocale:Jp,getLocation:zb,getNetworkType:fy,getProvider:rx,getPushClientId:ff,getRecorderManager:qw,getRightWindowStyle:Dw,getSavedFileInfo:Ww,getSavedFileList:Uw,getScreenBrightness:Zw,getSelectedTextRange:ef,getStorage:By,getStorageInfo:Fy,getStorageInfoSync:Vy,getStorageSync:Ry,getSystemInfo:ly,getSystemInfoSync:sy,getTopWindowStyle:Iw,getVideoInfo:Xy,getWindowInfo:ty,hideActionSheet:()=>{z_&&(z_.visible=!1)},hideKeyboard:Hy,hideLeftWindow:Aw,hideLoading:$_,hideModal:()=>{m_&&(m_.visible=!1)},hideNavigationBarLoading:J_,hideRightWindow:Pw,hideTabBar:lw,hideTabBarRedDot:uw,hideToast:O_,hideTopWindow:Cw,interceptors:{},invokePushCallback:function(e){if("enabled"===e.type)cf=!0;else if("clientId"===e.type)sf=e.cid,lf=e.errMsg,pf(sf,e.errMsg);else if("pushMsg"===e.type){const t={type:"receive",data:uf(e.message)};for(let e=0;e<hf.length;e++){if((0,hf[e])(t),t.stopped)break}}else"click"===e.type&&hf.forEach((t=>{t({type:"click",data:uf(e.message)})}))},loadFontFace:W_,login:ox,makePhoneCall:Gv,navigateBack:n_,navigateTo:r_,offAccelerometerChange:my,offAppHide:function(e){of("onHide",e)},offAppShow:function(e){of("onShow",e)},offCompassChange:wy,offError:function(e){of("onError",e)},offLocationChange:Zb,offLocationChangeError:t_,offNetworkStatusChange:py,offPageNotFound:function(e){of("onPageNotFound",e)},offPushMessage:e=>{if(e){const t=hf.indexOf(e);t>-1&&hf.splice(t,1)}else hf.length=0},offThemeChange:Ly,offUnhandledRejection:function(e){of("onUnhandledRejection",e)},offWindowResize:Gp,onAccelerometerChange:gy,onAppHide:function(e){nf("onHide",e)},onAppShow:function(e){nf("onShow",e)},onCompassChange:_y,onCreateVueApp:function(e){if(Me)return e(Me);Pe.push(e)},onError:function(e){nf("onError",e)},onGyroscopeChange:Xw,onLocaleChange:Qp,onLocationChange:Kb,onLocationChangeError:e_,onMemoryWarning:Yw,onNetworkStatusChange:dy,onPageNotFound:function(e){nf("onPageNotFound",e)},onPushMessage:e=>{-1===hf.indexOf(e)&&hf.push(e)},onSocketClose:jb,onSocketError:Nb,onSocketMessage:qb,onSocketOpen:Bb,onTabBarMidButtonTap:Yp,onThemeChange:Iy,onUnhandledRejection:function(e){nf("onUnhandledRejection",e)},onUserCaptureScreen:tx,onWindowResize:Xp,openDocument:Wy,openLocation:Ub,pageScrollTo:K_,preloadPage:c_,previewImage:yb,reLaunch:a_,redirectTo:i_,removeInterceptor:Fd,removeSavedFile:Hw,removeStorage:qy,removeStorageSync:Ny,removeTabBarBadge:pw,request:xb,rpx2px:qd,saveFile:Fw,saveImageToPhotosAlbum:Nw,saveVideoToPhotosAlbum:jw,scanCode:Qw,sendSocketMessage:$b,setClipboardData:My,setKeepScreenOn:ex,setLeftWindowStyle:$w,setLocale:Kp,setNavigationBarColor:X_,setNavigationBarTitle:Q_,setPageMeta:Zp,setRightWindowStyle:Rw,setScreenBrightness:Kw,setStorage:$y,setStorageSync:Oy,setTabBarBadge:fw,setTabBarItem:aw,setTabBarStyle:sw,setTopWindowStyle:Lw,showActionSheet:U_,showLeftWindow:Ew,showLoading:L_,showModal:__,showNavigationBarLoading:G_,showRightWindow:Mw,showTabBar:cw,showTabBarRedDot:dw,showToast:P_,showTopWindow:kw,startAccelerometer:vy,startCompass:xy,startGyroscope:Gw,startLocationUpdate:Jb,startPullDownRefresh:Z_,stopAccelerometer:yy,stopCompass:Ty,stopGyroscope:Jw,stopLocationUpdate:Qb,stopPullDownRefresh:ew,switchTab:l_,uploadFile:Ab,upx2px:qd,vibrateLong:Cy,vibrateShort:ky},Symbol.toStringTag,{value:"Module"}),ax=cu({name:"MapLocation",setup(){const e=Ut({latitude:0,longitude:0,rotate:0});{let t=function(t){e.rotate=t.direction},n=function(){zb({type:"gcj02",success:t=>{e.latitude=t.latitude,e.longitude=t.longitude},complete:()=>{i=setTimeout(n,3e4)}})},o=function(){i&&clearTimeout(i),wy(t)};const r=_r("onMapReady");let i;_y(t),r(n),Uo(o);const a=_r("addMapChidlContext"),s=_r("removeMapChidlContext"),l={id:"MAP_LOCATION",state:e};a(l),Uo((()=>s(l)))}return()=>e.latitude?ri(qv,di({anchor:{x:.5,y:.5},width:"44",height:"44",iconPath:Pv},e),null,16,["iconPath"]):null}}),sx=cu({name:"MapPolygon",props:{dashArray:{type:Array,default:()=>[0,0]},points:{type:Array,required:!0},strokeWidth:{type:Number,default:1},strokeColor:{type:String,default:"#000000"},fillColor:{type:String,default:"#00000000"},zIndex:{type:Number,default:0}},setup(e){let t;return _r("onMapReady")(((n,o,r)=>{function i(){const{points:r,strokeWidth:i,strokeColor:a,dashArray:s,fillColor:l,zIndex:c}=e,u=r.map((e=>{const{latitude:t,longitude:n}=e;return Rv()?[n,t]:Bv()?new o.Point(n,t):new o.LatLng(t,n)})),{r:d,g:p,b:f,a:h}=jv(l),{r:g,g:m,b:v,a:y}=jv(a),b={clickable:!0,cursor:"crosshair",editable:!1,map:n,fillColor:"",path:u,strokeColor:"",strokeDashStyle:s.some((e=>e>0))?"dash":"solid",strokeWeight:i,visible:!0,zIndex:c};o.Color?(b.fillColor=new o.Color(d,p,f,h),b.strokeColor=new o.Color(g,m,v,y)):(b.fillColor=`rgb(${d}, ${p}, ${f})`,b.fillOpacity=h,b.strokeColor=`rgb(${g}, ${m}, ${v})`,b.strokeOpacity=y),t?t.setOptions(b):Bv()?(t=new o.Polygon(b.path,b),n.addOverlay(t)):t=new o.Polygon(b)}i(),to(e,i)})),Uo((()=>{t.setMap(null)})),()=>null}});function lx(e){const t=[];return f(e)&&e.forEach((e=>{e&&e.latitude&&e.longitude&&t.push({latitude:e.latitude,longitude:e.longitude})})),t}function cx(e,t,n){return Bv()?function(e,t,n){return new e.Point(n,t)}(e,t,n):Rv()?function(e,t,n){return new e.LngLat(n,t)}(e,t,n):function(e,t,n){return new e.LatLng(t,n)}(e,t,n)}function ux(e){return"getLat"in e?e.getLat():Bv()?e.lat:e.lat()}function dx(e){return"getLng"in e?e.getLng():Bv()?e.lng:e.lng()}function px(e,t,n){const o=du(t,n),r=sn(null);let i,a;const s=Ut({latitude:Number(e.latitude),longitude:Number(e.longitude),includePoints:lx(e.includePoints)}),l=[];let u,d;function p(e){u?e(a,i,o):l.push(e)}const f=[];function h(e){d?e():l.push(e)}const g={};function m(){const e=a.getCenter();return{scale:a.getZoom(),centerLocation:{latitude:ux(e),longitude:dx(e)}}}function v(){if(Rv()){const e=[];s.includePoints.forEach((t=>{e.push([t.longitude,t.latitude])}));const t=new i.Bounds(...e);a.setBounds(t)}else if(Bv());else{const e=new i.LatLngBounds;s.includePoints.forEach((({latitude:t,longitude:n})=>{const o=new i.LatLng(t,n);e.extend(o)})),a.fitBounds(e)}}function y(){const t=r.value,l=cx(i,s.latitude,s.longitude),u=i.event||i.Event,p=new i.Map(t,{center:l,zoom:Number(e.scale),disableDoubleClickZoom:!0,mapTypeControl:!1,zoomControl:!1,scaleControl:!1,panControl:!1,fullscreenControl:!1,streetViewControl:!1,keyboardShortcuts:!1,minZoom:5,maxZoom:18,draggable:!0});if(Bv()&&(p.centerAndZoom(l,Number(e.scale)),p.enableScrollWheelZoom(),p._printLog&&p._printLog("uniapp")),to((()=>e.scale),(e=>{p.setZoom(Number(e)||16)})),h((()=>{s.includePoints.length&&(v(),function(){const e=cx(i,s.latitude,s.longitude);a.setCenter(e)}())})),Bv())p.addEventListener("click",(()=>{o("tap",{},{}),o("click",{},{})})),p.addEventListener("dragstart",(()=>{o("regionchange",{},{type:"begin",causedBy:"gesture"})})),p.addEventListener("dragend",(()=>{o("regionchange",{},c({type:"end",causedBy:"drag"},m()))}));else{const e=u.addListener(p,"bounds_changed",(()=>{e.remove(),d=!0,f.forEach((e=>e())),f.length=0}));u.addListener(p,"click",(()=>{o("tap",{},{}),o("click",{},{})})),u.addListener(p,"dragstart",(()=>{o("regionchange",{},{type:"begin",causedBy:"gesture"})})),u.addListener(p,"dragend",(()=>{o("regionchange",{},c({type:"end",causedBy:"drag"},m()))}));const t=()=>{n("update:scale",p.getZoom()),o("regionchange",{},c({type:"end",causedBy:"scale"},m()))};u.addListener(p,"zoom_changed",t),u.addListener(p,"zoomend",t),u.addListener(p,"center_changed",(()=>{const e=p.getCenter(),t=ux(e),o=dx(e);n("update:latitude",t),n("update:longitude",o)}))}return p}to([()=>e.latitude,()=>e.longitude],(([e,t])=>{const n=Number(e),o=Number(t);if((n!==s.latitude||o!==s.longitude)&&(s.latitude=n,s.longitude=o,a)){const e=cx(i,s.latitude,s.longitude);a.setCenter(e)}})),to((()=>e.includePoints),(e=>{s.includePoints=lx(e),d&&v()}),{deep:!0});try{cm(((e,t={})=>{switch(e){case"getCenterLocation":p((()=>{const n=a.getCenter();he(t,{latitude:ux(n),longitude:dx(n),errMsg:`${e}:ok`})}));break;case"moveToLocation":{let n=Number(t.latitude),o=Number(t.longitude);if(!n||!o){const e=g.MAP_LOCATION;e&&(n=e.state.latitude,o=e.state.longitude)}if(n&&o){if(s.latitude=n,s.longitude=o,a){const e=cx(i,n,o);a.setCenter(e)}p((()=>{he(t,`${e}:ok`)}))}else he(t,`${e}:fail`)}break;case"translateMarker":p((()=>{const n=g[t.markerId];if(n){try{n.translate(t)}catch(o){he(t,`${e}:fail ${o.message}`)}he(t,`${e}:ok`)}else he(t,`${e}:fail not found`)}));break;case"includePoints":s.includePoints=lx(t.includePoints),(d||Rv())&&v(),h((()=>{he(t,`${e}:ok`)}));break;case"getRegion":h((()=>{const n=a.getBounds(),o=n.getSouthWest(),r=n.getNorthEast();he(t,{southwest:{latitude:ux(o),longitude:dx(o)},northeast:{latitude:ux(r),longitude:dx(r)},errMsg:`${e}:ok`})}));break;case"getScale":p((()=>{he(t,{scale:a.getZoom(),errMsg:`${e}:ok`})}))}}),dm(),!0)}catch(b){}return jo((()=>{Ev(e.libraries,(e=>{i=e,a=y(),u=!0,l.forEach((e=>e(a,i,o))),l.length=0,o("updated",{},{})}))})),br("onMapReady",p),br("addMapChidlContext",(function(e){g[e.id]=e})),br("removeMapChidlContext",(function(e){delete g[e.id]})),{state:s,mapRef:r,trigger:o}}const fx=lu({name:"Map",props:{id:{type:String,default:""},latitude:{type:[String,Number],default:0},longitude:{type:[String,Number],default:0},scale:{type:[String,Number],default:16},markers:{type:Array,default:()=>[]},includePoints:{type:Array,default:()=>[]},polyline:{type:Array,default:()=>[]},circles:{type:Array,default:()=>[]},controls:{type:Array,default:()=>[]},showLocation:{type:[Boolean,String],default:!1},libraries:{type:Array,default:()=>[]},polygons:{type:Array,default:()=>[]}},emits:["markertap","labeltap","callouttap","controltap","regionchange","tap","click","updated","update:scale","update:latitude","update:longitude"],setup(e,{emit:t,slots:n}){const o=sn(null),{mapRef:r,trigger:i}=px(e,o,t);return()=>ri("uni-map",{ref:o,id:e.id},[ri("div",{ref:r,style:"width: 100%; height: 100%; position: relative; overflow: hidden"},null,512),e.markers.map((e=>ri(qv,di({key:e.id},e),null,16))),e.polyline.map((e=>ri(Vv,e,null,16))),e.circles.map((e=>ri(Fv,e,null,16))),e.controls.map((e=>ri(Wv,di(e,{trigger:i}),null,16,["trigger"]))),e.showLocation&&ri(ax,null,null),e.polygons.map((e=>ri(sx,e,null,16))),ri("div",{style:"position: absolute;top: 0;width: 100%;height: 100%;overflow: hidden;pointer-events: none;"},[n.default&&n.default()])],8,["id"])}});function hx(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!Kr(e)}function gx(e){if(e.mode===yx.TIME)return"00:00";if(e.mode===yx.DATE){const t=(new Date).getFullYear()-150;switch(e.fields){case bx.YEAR:return t.toString();case bx.MONTH:return t+"-01";default:return t+"-01-01"}}return""}function mx(e){if(e.mode===yx.TIME)return"23:59";if(e.mode===yx.DATE){const t=(new Date).getFullYear()+150;switch(e.fields){case bx.YEAR:return t.toString();case bx.MONTH:return t+"-12";default:return t+"-12-31"}}return""}function vx(e,t,n,o){const r=e.mode===yx.DATE?"-":":",i=e.mode===yx.DATE?t.dateArray:t.timeArray;let a;if(e.mode===yx.TIME)a=2;else switch(e.fields){case bx.YEAR:a=1;break;case bx.MONTH:a=2;break;default:a=3}const s=String(n).split(r);let l=[];for(let c=0;c<a;c++){const e=s[c];l.push(i[c].indexOf(e))}return l.indexOf(-1)>=0&&(l=o?vx(e,t,o):l.map((()=>0))),l}const yx={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date"},bx={YEAR:"year",MONTH:"month",DAY:"day"},_x={PICKER:"picker",SELECT:"select"},xx=lu({name:"Picker",compatConfig:{MODE:3},props:{name:{type:String,default:""},range:{type:Array,default:()=>[]},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:yx.SELECTOR,validator:e=>Object.values(yx).includes(e)},fields:{type:String,default:""},start:{type:String,default:e=>gx(e)},end:{type:String,default:e=>mx(e)},disabled:{type:[Boolean,String],default:!1},selectorType:{type:String,default:""}},emits:["change","cancel","columnchange"],setup(e,{emit:t,slots:n}){Ml();const{t:o}=bl(),r=sn(null),i=sn(null),a=sn(null),s=sn(null),l=sn(!1),{state:c,rangeArray:u}=function(e){const t=Ut({valueSync:void 0,visible:!1,contentVisible:!1,popover:null,valueChangeSource:"",timeArray:[],dateArray:[],valueArray:[],oldValueArray:[],isDesktop:!1,popupStyle:{content:{},triangle:{}}}),n=Ei((()=>{let n=e.range;switch(e.mode){case yx.SELECTOR:return[n];case yx.MULTISELECTOR:return n;case yx.TIME:return t.timeArray;case yx.DATE:{const n=t.dateArray;switch(e.fields){case bx.YEAR:return[n[0]];case bx.MONTH:return[n[0],n[1]];default:return[n[0],n[1],n[2]]}}}return[]}));return{state:t,rangeArray:n}}(e),d=du(r,t),{system:p,selectorTypeComputed:h,_show:g,_l10nColumn:m,_l10nItem:v,_input:y,_fixInputPosition:b,_pickerViewChange:_,_cancel:w,_change:x,_resetFormData:T,_getFormData:S,_createTime:k,_createDate:C,_setValueSync:E}=function(e,t,n,o,r,i,a){const s=function(){const e=sn(!1);return e.value=(()=>0===String(navigator.vendor).indexOf("Apple")&&navigator.maxTouchPoints>0)(),e}(),l=function(){const e=sn("");return e.value=(()=>{if(/win|mac/i.test(navigator.platform)){if("Google Inc."===navigator.vendor)return"chrome";if(/Firefox/.test(navigator.userAgent))return"firefox"}return""})(),e}(),c=Ei((()=>{const t=e.selectorType;return Object.values(_x).includes(t)?t:s.value?_x.PICKER:_x.SELECT})),u=Ei((()=>e.mode===yx.DATE&&!Object.values(bx).includes(e.fields)&&t.isDesktop?l.value:"")),d=Ei((()=>vx(e,t,e.start,gx(e)))),p=Ei((()=>vx(e,t,e.end,mx(e))));function h(n){if(e.disabled)return;t.valueChangeSource="";let o=r.value,i=n.currentTarget;o.remove(),(document.querySelector("uni-app")||document.body).appendChild(o),o.style.display="block";const a=i.getBoundingClientRect();t.popover={top:a.top,left:a.left,width:a.width,height:a.height},setTimeout((()=>{t.visible=!0}),20)}function g(){return{value:t.valueSync,key:e.name}}function m(){switch(e.mode){case yx.SELECTOR:t.valueSync=0;break;case yx.MULTISELECTOR:t.valueSync=e.value.map((e=>0));break;case yx.DATE:case yx.TIME:t.valueSync=""}}function v(){let e=[],n=[];for(let t=0;t<24;t++)e.push((t<10?"0":"")+t);for(let t=0;t<60;t++)n.push((t<10?"0":"")+t);t.timeArray.push(e,n)}function y(){let t=(new Date).getFullYear(),n=t-150,o=t+150;if(e.start){const t=new Date(e.start).getFullYear();!isNaN(t)&&t<n&&(n=t)}if(e.end){const t=new Date(e.end).getFullYear();!isNaN(t)&&t>o&&(o=t)}return{start:n,end:o}}function b(){let e=[];const n=y();for(let t=n.start,i=n.end;t<=i;t++)e.push(String(t));let o=[];for(let t=1;t<=12;t++)o.push((t<10?"0":"")+t);let r=[];for(let t=1;t<=31;t++)r.push((t<10?"0":"")+t);t.dateArray.push(e,o,r)}function _(e){return 60*e[0]+e[1]}function w(e){const t=31;return e[0]*t*12+(e[1]||0)*t+(e[2]||0)}function x(e,t){for(let n=0;n<e.length&&n<t.length;n++)e[n]=t[n]}function T(){let n=e.value;switch(e.mode){case yx.MULTISELECTOR:{f(n)||(n=t.valueArray),f(t.valueSync)||(t.valueSync=[]);const o=t.valueSync.length=Math.max(n.length,e.range.length);for(let r=0;r<o;r++){const o=Number(n[r]),i=Number(t.valueSync[r]),a=isNaN(o)?isNaN(i)?0:i:o,s=e.range[r]?e.range[r].length-1:0;t.valueSync.splice(r,1,a<0||a>s?0:a)}}break;case yx.TIME:case yx.DATE:t.valueSync=String(n);break;default:{const e=Number(n);t.valueSync=e<0?0:e;break}}}function S(){let n,o=t.valueSync;switch(e.mode){case yx.MULTISELECTOR:n=[...o];break;case yx.TIME:n=vx(e,t,o,fe({mode:yx.TIME}));break;case yx.DATE:n=vx(e,t,o,fe({mode:yx.DATE}));break;default:n=[o]}t.oldValueArray=[...n],t.valueArray=[...n]}function k(){let n=t.valueArray;switch(e.mode){case yx.SELECTOR:return n[0];case yx.MULTISELECTOR:return n.map((e=>e));case yx.TIME:return t.valueArray.map(((e,n)=>t.timeArray[n][e])).join(":");case yx.DATE:return t.valueArray.map(((e,n)=>t.dateArray[n][e])).join("-")}}function C(){A(),t.valueChangeSource="click";const e=k();t.valueSync=f(e)?e.map((e=>e)):e,n("change",{},{value:e})}function E(e){if("firefox"===u.value&&e){const{top:n,left:o,width:r,height:i}=t.popover,{pageX:a,pageY:s}=e;if(a>o&&a<o+r&&s>n&&s<n+i)return}A(),n("cancel",{},{})}function A(){t.visible=!1,setTimeout((()=>{let e=r.value;e.remove(),o.value.prepend(e),e.style.display="none"}),260)}function M(){e.mode===yx.SELECTOR&&c.value===_x.SELECT&&(i.value.scrollTop=34*t.valueArray[0])}function P(e){const n=e.target;t.valueSync=n.value,Cn((()=>{C()}))}function I(e){if("chrome"===u.value){const t=o.value.getBoundingClientRect(),n=32;a.value.style.left=e.clientX-t.left-1.5*n+"px",a.value.style.top=e.clientY-t.top-.5*n+"px"}}function L(e){t.valueArray=O(e.detail.value,!0)}function O(t,n){const{getLocale:o}=bl();if(e.mode===yx.DATE){const r=o();if(!r.startsWith("zh"))switch(e.fields){case bx.YEAR:return t;case bx.MONTH:return[t[1],t[0]];default:switch(r){case"es":case"fr":return[t[2],t[1],t[0]];default:return n?[t[2],t[0],t[1]]:[t[1],t[2],t[0]]}}}return t}function $(t,n){const{getLocale:o}=bl();if(e.mode===yx.DATE){const r=o();if(r.startsWith("zh")){return t+["年","月","日"][n]}if(e.fields!==bx.YEAR&&n===(e.fields===bx.MONTH||"es"!==r&&"fr"!==r?0:1)){let e;switch(r){case"es":e=["enero","febrero","marzo","abril","mayo","junio","​​julio","agosto","septiembre","octubre","noviembre","diciembre"];break;case"fr":e=["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"];break;default:e=["January","February","March","April","May","June","July","August","September","October","November","December"]}return e[Number(t)-1]}}return t}return to((()=>t.visible),(e=>{e?(clearTimeout(Tx),t.contentVisible=e,M()):Tx=setTimeout((()=>{t.contentVisible=e}),300)})),to([()=>e.mode,()=>e.value,()=>e.range],T,{deep:!0}),to((()=>t.valueSync),S,{deep:!0}),to((()=>t.valueArray),(o=>{if(e.mode===yx.TIME||e.mode===yx.DATE){const n=e.mode===yx.TIME?_:w,o=t.valueArray,r=d.value,i=p.value;if(e.mode===yx.DATE){const e=t.dateArray,n=e[2].length,r=Number(e[2][o[2]])||1,i=new Date(`${e[0][o[0]]}/${e[1][o[1]]}/${r}`).getDate();i<r&&(o[2]-=i+n-r)}n(o)<n(r)?x(o,r):n(o)>n(i)&&x(o,i)}o.forEach(((o,r)=>{o!==t.oldValueArray[r]&&(t.oldValueArray[r]=o,e.mode===yx.MULTISELECTOR&&n("columnchange",{},{column:r,value:o}))}))})),{selectorTypeComputed:c,system:u,_show:h,_cancel:E,_change:C,_l10nColumn:O,_l10nItem:$,_input:P,_resetFormData:m,_getFormData:g,_createTime:v,_createDate:b,_setValueSync:T,_fixInputPosition:I,_pickerViewChange:L}}(e,c,d,r,i,a,s);!function(e,t,n){const{key:o,disable:r}=ob();Zn((()=>{r.value=!e.visible})),to(o,(e=>{"esc"===e?t():"enter"===e&&n()}))}(c,w,x),function(e,t){const n=_r(gu,!1);if(n){const o={reset:e,submit:()=>{const e=["",null],{key:n,value:o}=t();return""!==n&&(e[0]=n,e[1]=o),e}};n.addField(o),Fo((()=>{n.removeField(o)}))}}(T,S),k(),C(),E();const A=R_(c);return Zn((()=>{c.isDesktop=A.isDesktop.value,c.popupStyle=A.popupStyle.value})),Fo((()=>{i.value&&i.value.remove()})),jo((()=>{l.value=!0})),()=>{let t;const{visible:d,contentVisible:f,valueArray:T,popupStyle:S,valueSync:k}=c,{rangeKey:C,mode:E,start:A,end:M}=e,P=hu(e,"disabled");return ri("uni-picker",di({ref:r},P,{onClick:uu(g)}),[l.value?ri("div",{ref:i,class:["uni-picker-container",`uni-${E}-${h.value}`],onWheel:ac,onTouchmove:ac},[ri(Di,{name:"uni-fade"},{default:()=>[ao(ri("div",{class:"uni-mask uni-picker-mask",onClick:uu(w),onMousemove:b},null,40,["onClick","onMousemove"]),[[Gi,d]])]}),p.value?null:ri("div",{class:[{"uni-picker-toggle":d},"uni-picker-custom"],style:S.content},[ri("div",{class:"uni-picker-header",onClick:sc},[ri("div",{class:"uni-picker-action uni-picker-action-cancel",onClick:uu(w)},[o("uni.picker.cancel")],8,["onClick"]),ri("div",{class:"uni-picker-action uni-picker-action-confirm",onClick:x},[o("uni.picker.done")],8,["onClick"])],8,["onClick"]),f?ri(Ig,{value:m(T),class:"uni-picker-content",onChange:_},hx(t=Go(m(u.value),((e,t)=>{let n;return ri(qg,{key:t},hx(n=Go(e,((e,n)=>ri("div",{key:n,class:"uni-picker-item"},["object"==typeof e?e[C]||"":v(e,t)]))))?n:{default:()=>[n],_:1})})))?t:{default:()=>[t],_:1},8,["value","onChange"]):null,ri("div",{ref:a,class:"uni-picker-select",onWheel:sc,onTouchmove:sc},[Go(u.value[0],((e,t)=>ri("div",{key:t,class:["uni-picker-item",{selected:T[0]===t}],onClick:()=>{T[0]=t,x()}},["object"==typeof e?e[C]||"":e],10,["onClick"])))],40,["onWheel","onTouchmove"]),ri("div",{style:S.triangle},null,4)],6)],40,["onWheel","onTouchmove"]):null,ri("div",null,[n.default&&n.default()]),p.value?ri("div",{class:"uni-picker-system",onMousemove:uu(b)},[ri("input",{class:["uni-picker-system_input",p.value],ref:s,value:k,type:E,tabindex:"-1",min:A,max:M,onChange:e=>{y(e),sc(e)}},null,42,["value","type","min","max","onChange"])],40,["onMousemove"]):null],16,["onClick"])}}});let Tx;const Sx=c(Nl,{publishHandler(e,t,n){Cx.subscribeHandler(e,t,n)}}),kx=ix,Cx=c(Gc,{publishHandler(e,t,n){Sx.subscribeHandler(e,t,n)}}),Ex=cu({name:"PageHead",setup(){const e=sn(null),t=wm(),n=f_(t.navigationBar,(()=>{const e=p_(t.navigationBar);n.backgroundColor=e.backgroundColor,n.titleColor=e.titleColor})),{clazz:o,style:r}=function(e){const t=Ei((()=>{const{type:t,titlePenetrate:n,shadowColorType:o}=e,r={"uni-page-head":!0,"uni-page-head-transparent":"transparent"===t,"uni-page-head-titlePenetrate":"YES"===n,"uni-page-head-shadow":!!o};return o&&(r[`uni-page-head-shadow-${o}`]=!0),r})),n=Ei((()=>({backgroundColor:e.backgroundColor,color:e.titleColor,transitionDuration:e.duration,transitionTimingFunction:e.timingFunc})));return{clazz:t,style:n}}(n);return()=>{const i=function(e,t){if(!t)return ri("div",{class:"uni-page-head-btn",onClick:Mx},[wc(yc,"transparent"===e.type?"#fff":e.titleColor,26)],8,["onClick"])}(n,t.isQuit),a=n.type||"default",s="transparent"!==a&&"float"!==a&&ri("div",{class:{"uni-placeholder":!0,"uni-placeholder-titlePenetrate":n.titlePenetrate}},null,2);return ri("uni-page-head",{"uni-page-head-type":a},[ri("div",{ref:e,class:o.value,style:r.value},[ri("div",{class:"uni-page-head-hd"},[i]),Ax(n),ri("div",{class:"uni-page-head-ft"},[])],6),s],8,["uni-page-head-type"])}}});function Ax(e,t){return function({type:e,loading:t,titleSize:n,titleText:o,titleImage:r}){return ri("div",{class:"uni-page-head-bd"},[ri("div",{style:{fontSize:n,opacity:"transparent"===e?0:1},class:"uni-page-head__title"},[t?ri("i",{class:"uni-loading"},null):r?ri("img",{src:r,class:"uni-page-head__title_image"},null,8,["src"]):o],4)])}(e)}function Mx(){1===qm().length?uni.reLaunch({url:"/"}):uni.navigateBack({from:"backbutton",success(){}})}const Px={name:"PageRefresh",setup(){const{pullToRefresh:e}=wm();return{offset:e.offset,color:e.color}}},Ix=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},Lx={class:"uni-page-refresh-inner"},Ox=["fill"],$x=[oi("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null,-1),oi("path",{d:"M0 0h24v24H0z",fill:"none"},null,-1)],Dx={class:"uni-page-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},Rx=["stroke"];const Bx=Ix(Px,[["render",function(e,t,n,o,r,i){return Hr(),Jr("uni-page-refresh",null,[oi("div",{style:ae({"margin-top":o.offset+"px"}),class:"uni-page-refresh"},[oi("div",Lx,[(Hr(),Jr("svg",{fill:o.color,class:"uni-page-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},$x,8,Ox)),(Hr(),Jr("svg",Dx,[oi("circle",{stroke:o.color,class:"uni-page-refresh__path",cx:"50",cy:"50",r:"20",fill:"none","stroke-width":"4","stroke-miterlimit":"10"},null,8,Rx)]))])],4)])}]]);function Nx(e,t,n){const o=Array.prototype.slice.call(e.changedTouches).filter((e=>e.identifier===t))[0];return!!o&&(e.deltaY=o.pageY-n,!0)}const qx="aborting",jx="refreshing",zx="restoring";function Vx(e){const t=wm(),{id:n,pullToRefresh:o}=t,{range:r,height:i}=o;let a,s,l,c,u,d,p,f;cm((()=>{t.enablePullDownRefresh&&(f||(f=jx,v(),setTimeout((()=>{x()}),50)))}),"startPullDownRefresh",!1,n),cm((()=>{t.enablePullDownRefresh&&f===jx&&(y(),f=zx,v(),function(e){if(!s)return;l.transition="-webkit-transform 0.3s",l.transform+=" scale(0.01)";const t=function(){n&&clearTimeout(n),s.removeEventListener("webkitTransitionEnd",t),l.transition="",l.transform="translate3d(-50%, 0, 0)",e()};s.addEventListener("webkitTransitionEnd",t);const n=setTimeout(t,350)}((()=>{y(),f=h=g=null})))}),"stopPullDownRefresh",!1,n),jo((()=>{a=e.value.$el,s=a.querySelector(".uni-page-refresh"),l=s.style,c=s.querySelector(".uni-page-refresh-inner").style}));let h=null,g=null;function m(e){f&&a&&a.classList[e]("uni-page-refresh--"+f)}function v(){m("add")}function y(){m("remove")}const b=uu((e=>{if(!t.enablePullDownRefresh)return;const n=e.changedTouches[0];u=n.identifier,d=n.pageY,p=!([qx,jx,zx].indexOf(f)>=0)})),_=uu((e=>{if(!t.enablePullDownRefresh)return;if(!p)return;if(!Nx(e,u,d))return;let{deltaY:n}=e;if(0!==(document.documentElement.scrollTop||document.body.scrollTop))return void(u=null);if(n<0&&!f)return;e.preventDefault(),null===h&&(g=n,f="pulling",v()),n-=g,n<0&&(n=0),h=n;(n>=r&&"reached"!==f||n<r&&"pulling"!==f)&&(y(),f="reached"===f?"pulling":"reached",v()),function(e){if(!s)return;let t=e/r;t>1?t=1:t*=t*t;const n=Math.round(e/(r/i))||0;c.transform="rotate("+360*t+"deg)",l.clip="rect("+(45-n)+"px,45px,45px,-5px)",l.transform="translate3d(-50%, "+n+"px, 0)"}(n)})),w=uu((e=>{t.enablePullDownRefresh&&Nx(e,u,d)&&null!==f&&("pulling"===f?(y(),f=qx,v(),function(e){if(!s)return;if(l.transform){l.transition="-webkit-transform 0.3s",l.transform="translate3d(-50%, 0, 0)";const t=function(){n&&clearTimeout(n),s.removeEventListener("webkitTransitionEnd",t),l.transition="",e()};s.addEventListener("webkitTransitionEnd",t);const n=setTimeout(t,350)}else e()}((()=>{y(),f=h=g=null}))):"reached"===f&&(y(),f=jx,v(),x()))}));function x(){s&&(l.transition="-webkit-transform 0.2s",l.transform="translate3d(-50%, "+i+"px, 0)",Pc(n,"onPullDownRefresh"))}return{onTouchstartPassive:b,onTouchmove:_,onTouchend:w,onTouchcancel:w}}const Fx=cu({name:"PageBody",setup(e,t){const n=wm(),o=sn(null),r=n.enablePullDownRefresh?Vx(o):null,i=sn(null);return to((()=>n.enablePullDownRefresh),(()=>{i.value=n.enablePullDownRefresh?r:null}),{immediate:!0}),()=>{const e=function(e,t){if(!t.enablePullDownRefresh)return null;return ri(Bx,{ref:e},null,512)}(o,n);return ri(jr,null,[e,ri("uni-page-wrapper",i.value,[ri("uni-page-body",null,[Jo(t.slots,"default")])],16)])}}});const Ux=cu({name:"Page",setup(e,t){const n=xm(Sm()),o=n.navigationBar,r={};return H_(n),()=>ri("uni-page",{"data-page":n.route,style:r},"custom"!==o.style?[ri(Ex),Wx(t)]:[Wx(t)])}});function Wx(e){return Hr(),Qr(Fx,{key:0},{default:zn((()=>[Jo(e.slots,"page")])),_:3})}const Hx={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.getApp=uv,window.getCurrentPages=qm,window.wx=kx,window.uni=kx,window.UniViewJSBridge=Sx,window.UniServiceJSBridge=Cx,window.rpx2px=qd,window.__setupPage=e=>hv(e);const Yx=Object.assign({}),Xx=Object.assign;window.__uniConfig=Xx({tabBar:{position:"bottom",color:"#000000",selectedColor:"#fa2209",borderStyle:"black",blurEffect:"none",fontSize:"10px",iconWidth:"24px",spacing:"3px",height:"50px",backgroundColor:"#ffffff",list:[{pagePath:"pages/index/index",iconPath:"/static/tabbar/home.png",selectedIconPath:"/static/tabbar/home-active.png",text:"首页"},{pagePath:"pages/category/index",iconPath:"/static/tabbar/cate.png",selectedIconPath:"/static/tabbar/cate-active.png",text:"分类"},{pagePath:"pages/cart/index",iconPath:"/static/tabbar/cart.png",selectedIconPath:"/static/tabbar/cart-active.png",text:"购物车"},{pagePath:"pages/user/index",iconPath:"/static/tabbar/user.png",selectedIconPath:"/static/tabbar/user-active.png",text:"我的"}],selectedIndex:0,shown:!0},globalStyle:{maxWidth:750,rpxCalcMaxDeviceWidth:750,rpxCalcBaseDeviceWidth:560,rpxCalcIncludeWidth:9999,backgroundTextStyle:"dark",navigationBar:{backgroundColor:"#ffffff",titleText:"",type:"default",titleColor:"#000000"},isNVue:!1},easycom:{autoscan:!0,custom:{}},compilerVersion:"4.24"},{appId:"__UNI__EBB6AD2",appName:"萤火商城2.0",appVersion:"2.4.3",appVersionCode:243,async:Hx,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{maps:{qqmap:{key:"ZWEBZ-R7N3U-BJSVH-4TCR3-66MDQ-S3FDJ"}}},qqMapKey:"ZWEBZ-R7N3U-BJSVH-4TCR3-66MDQ-S3FDJ",bMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"zh-Hans",fallbackLocale:"",locales:Object.keys(Yx).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return Xx(e[n]||(e[n]={}),Yx[t].default),e}),{}),router:{mode:"hash",base:"./",assets:"assets",routerBase:"/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const Gx={delay:Hx.delay,timeout:Hx.timeout,suspensible:Hx.suspensible};Hx.loading&&(Gx.loadingComponent={name:"SystemAsyncLoading",render:()=>ri(Hn(Hx.loading))}),Hx.error&&(Gx.errorComponent={name:"SystemAsyncError",render:()=>ri(Hn(Hx.error))});const Jx=()=>t((()=>import("./pages-index-index.wHGch4eT.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24]),import.meta.url).then((e=>hv(e.default||e))),Qx=xo(Xx({loader:Jx},Gx)),Kx=()=>t((()=>import("./pages-category-index.B34wpMqV.js")),__vite__mapDeps([25,26,27,2,28,20,5,3,4,21,22,23,29,30,31,32,17,33,34,35,36,37,38]),import.meta.url).then((e=>hv(e.default||e))),Zx=xo(Xx({loader:Kx},Gx)),eT=()=>t((()=>import("./pages-cart-index.DppOFtkD.js")),__vite__mapDeps([39,3,2,4,5,29,30,40,35,41,20,21,22,23,34,42]),import.meta.url).then((e=>hv(e.default||e))),tT=xo(Xx({loader:eT},Gx)),nT=()=>t((()=>import("./pages-user-index.CbUOyf_F.js")),__vite__mapDeps([43,10,2,11,40,35,41,6,20,5,3,4,21,22,23,44,45,46]),import.meta.url).then((e=>hv(e.default||e))),oT=xo(Xx({loader:nT},Gx)),rT=()=>t((()=>import("./pages-custom-index.B-63u_i6.js")),__vite__mapDeps([47,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,48]),import.meta.url).then((e=>hv(e.default||e))),iT=xo(Xx({loader:rT},Gx)),aT=()=>t((()=>import("./pages-search-index.CH4gjY2p.js")),__vite__mapDeps([49,2,50]),import.meta.url).then((e=>hv(e.default||e))),sT=xo(Xx({loader:aT},Gx)),lT=()=>t((()=>import("./pages-login-index.COfh4f06.js")),__vite__mapDeps([51,52,53,2,54,10,11,55]),import.meta.url).then((e=>hv(e.default||e))),cT=xo(Xx({loader:lT},Gx)),uT=()=>t((()=>import("./pages-user-bind-index.7NHjYX-Q.js")),__vite__mapDeps([56,44,52,53,2,57]),import.meta.url).then((e=>hv(e.default||e))),dT=xo(Xx({loader:uT},Gx)),pT=()=>t((()=>import("./pages-user-personal-index.YOvr86bJ.js")),__vite__mapDeps([58,59,3,2,4,5,60,61,10,11,44,54,62]),import.meta.url).then((e=>hv(e.default||e))),fT=xo(Xx({loader:pT},Gx)),hT=()=>t((()=>import("./pages-article-index.CDM7m5Vv.js")),__vite__mapDeps([63,64,2,5,65,31,32,26,66,67]),import.meta.url).then((e=>hv(e.default||e))),gT=xo(Xx({loader:hT},Gx)),mT=()=>t((()=>import("./pages-article-detail.BnYe0OVP.js")),__vite__mapDeps([68,7,2,8,5,26,66,69]),import.meta.url).then((e=>hv(e.default||e))),vT=xo(Xx({loader:mT},Gx)),yT=()=>t((()=>import("./pages-help-index.Dbttj1KI.js")),__vite__mapDeps([70,31,2,5,32,71]),import.meta.url).then((e=>hv(e.default||e))),bT=xo(Xx({loader:yT},Gx)),_T=()=>t((()=>import("./pages-coupon-index.B2p7NJM4.js")),__vite__mapDeps([72,9,73,29,2,30,74]),import.meta.url).then((e=>hv(e.default||e))),wT=xo(Xx({loader:_T},Gx)),xT=()=>t((()=>import("./pages-goods-list.BKpMUPVF.js")),__vite__mapDeps([75,31,2,5,32,26,35,27,28,76]),import.meta.url).then((e=>hv(e.default||e))),TT=xo(Xx({loader:xT},Gx)),ST=()=>t((()=>import("./pages-goods-detail.CyUxkTsn.js")),__vite__mapDeps([77,7,2,8,5,26,35,34,33,40,41,78,79,21,22,3,4,80,81,82,10,11,83,6,17,36,37,84,15,16,85,86,87,88,9,73,89,90]),import.meta.url).then((e=>hv(e.default||e))),kT=xo(Xx({loader:ST},Gx)),CT=()=>t((()=>import("./pages-comment-index.ClXt4OyW.js")),__vite__mapDeps([91,64,2,5,65,81,3,4,82,31,32,10,11,92]),import.meta.url).then((e=>hv(e.default||e))),ET=xo(Xx({loader:CT},Gx)),AT=()=>t((()=>import("./pages-my-coupon-index.Cs_mPgJm.js")),__vite__mapDeps([93,64,2,5,65,31,32,9,73,94]),import.meta.url).then((e=>hv(e.default||e))),MT=xo(Xx({loader:AT},Gx)),PT=()=>t((()=>import("./pages-address-index.CqeqMHFd.js")),__vite__mapDeps([95,3,2,4,5,60,96,29,30,97]),import.meta.url).then((e=>hv(e.default||e))),IT=xo(Xx({loader:PT},Gx)),LT=()=>t((()=>import("./pages-address-create.CCSwocck.js")),__vite__mapDeps([98,59,3,2,4,5,60,61,99,86,87,79,21,22,80,100,53,96,101]),import.meta.url).then((e=>hv(e.default||e))),OT=xo(Xx({loader:LT},Gx)),$T=()=>t((()=>import("./pages-address-update.ScgDqsH9.js")),__vite__mapDeps([102,59,3,2,4,5,60,61,99,86,87,79,21,22,80,100,53,96,103]),import.meta.url).then((e=>hv(e.default||e))),DT=xo(Xx({loader:$T},Gx)),RT=()=>t((()=>import("./pages-points-log.Dvu_MoLj.js")),__vite__mapDeps([104,31,2,5,32,105]),import.meta.url).then((e=>hv(e.default||e))),BT=xo(Xx({loader:RT},Gx)),NT=()=>t((()=>import("./pages-wallet-index.NwL0474E.js")),__vite__mapDeps([106,44,2,107]),import.meta.url).then((e=>hv(e.default||e))),qT=xo(Xx({loader:NT},Gx)),jT=()=>t((()=>import("./pages-wallet-balance-log.iWhn-b7d.js")),__vite__mapDeps([108,31,2,5,32,109]),import.meta.url).then((e=>hv(e.default||e))),zT=xo(Xx({loader:jT},Gx)),VT=()=>t((()=>import("./pages-wallet-recharge-index.rPfqOwEA.js")),__vite__mapDeps([110,85,86,2,87,5,79,21,22,3,4,80,88,111,112,113]),import.meta.url).then((e=>hv(e.default||e))),FT=xo(Xx({loader:VT},Gx)),UT=()=>t((()=>import("./pages-wallet-recharge-order.-5OD1twq.js")),__vite__mapDeps([114,31,2,5,32,115]),import.meta.url).then((e=>hv(e.default||e))),WT=xo(Xx({loader:UT},Gx)),HT=()=>t((()=>import("./pages-checkout-index.Dxyndjdk.js")),__vite__mapDeps([116,86,2,87,5,85,79,21,22,3,4,80,88,53,73,117,118]),import.meta.url).then((e=>hv(e.default||e))),YT=xo(Xx({loader:HT},Gx)),XT=()=>t((()=>import("./pages-checkout-cashier-index.CRDBjvsX.js")),__vite__mapDeps([119,85,86,2,87,5,79,21,22,3,4,80,88,112,111,12,13,117,120]),import.meta.url).then((e=>hv(e.default||e))),GT=xo(Xx({loader:XT},Gx)),JT=()=>t((()=>import("./pages-order-center.Dq03wBFC.js")),__vite__mapDeps([121,44,2,122]),import.meta.url).then((e=>hv(e.default||e))),QT=xo(Xx({loader:JT},Gx)),KT=()=>t((()=>import("./pages-order-index.Bh7X68jP.js")),__vite__mapDeps([123,64,2,5,65,31,32,79,21,22,3,4,80,117,111,45,124]),import.meta.url).then((e=>hv(e.default||e))),ZT=xo(Xx({loader:KT},Gx)),eS=()=>t((()=>import("./pages-order-detail.ByX1qZk1.js")),__vite__mapDeps([125,79,21,2,22,5,3,4,80,117,111,45,126,127]),import.meta.url).then((e=>hv(e.default||e))),tS=xo(Xx({loader:eS},Gx)),nS=()=>t((()=>import("./pages-order-express-index.CzruM6XP.js")),__vite__mapDeps([128,64,2,5,65,45,129]),import.meta.url).then((e=>hv(e.default||e))),oS=xo(Xx({loader:nS},Gx)),rS=()=>t((()=>import("./pages-order-extract-check.Bd77uSx2.js")),__vite__mapDeps([130,117,126,2,131]),import.meta.url).then((e=>hv(e.default||e))),iS=xo(Xx({loader:rS},Gx)),aS=()=>t((()=>import("./pages-order-comment-index.D_6wDRUx.js")),__vite__mapDeps([132,54,2,133]),import.meta.url).then((e=>hv(e.default||e))),sS=xo(Xx({loader:aS},Gx)),lS=()=>t((()=>import("./pages-refund-index.BOBrtNeH.js")),__vite__mapDeps([134,64,2,5,65,31,32,135,136]),import.meta.url).then((e=>hv(e.default||e))),cS=xo(Xx({loader:lS},Gx)),uS=()=>t((()=>import("./pages-refund-detail.A5oF2rw8.js")),__vite__mapDeps([137,138,135,2,139]),import.meta.url).then((e=>hv(e.default||e))),dS=xo(Xx({loader:uS},Gx)),pS=()=>t((()=>import("./pages-refund-apply.DAq7RycQ.js")),__vite__mapDeps([140,138,54,135,2,141]),import.meta.url).then((e=>hv(e.default||e))),fS=xo(Xx({loader:pS},Gx)),hS=()=>t((()=>import("./pages-shop-extract.DpeamToG.js")),__vite__mapDeps([142,143,29,2,30,144]),import.meta.url).then((e=>hv(e.default||e))),gS=xo(Xx({loader:hS},Gx)),mS=()=>t((()=>import("./pages-shop-detail.BQ9spDsi.js")),__vite__mapDeps([145,26,143,2,146]),import.meta.url).then((e=>hv(e.default||e))),vS=xo(Xx({loader:mS},Gx)),yS=()=>t((()=>import("./pages-dealer-index.CWHuUEQc.js")),__vite__mapDeps([147,10,2,11,148,149]),import.meta.url).then((e=>hv(e.default||e))),bS=xo(Xx({loader:yS},Gx)),_S=()=>t((()=>import("./pages-dealer-apply.IsLKy4i-.js")),__vite__mapDeps([150,85,86,2,87,5,79,21,22,3,4,80,88,151,152]),import.meta.url).then((e=>hv(e.default||e))),wS=xo(Xx({loader:_S},Gx)),xS=()=>t((()=>import("./pages-dealer-withdraw-apply.ByWXkg8m.js")),__vite__mapDeps([153,148,154,151,2,155]),import.meta.url).then((e=>hv(e.default||e))),TS=xo(Xx({loader:xS},Gx)),SS=()=>t((()=>import("./pages-dealer-withdraw-list.DSqQau7k.js")),__vite__mapDeps([156,64,2,5,65,31,32,85,86,87,79,21,22,3,4,80,88,154,151,157]),import.meta.url).then((e=>hv(e.default||e))),kS=xo(Xx({loader:SS},Gx)),CS=()=>t((()=>import("./pages-dealer-poster.DJfF8o78.js")),__vite__mapDeps([158,151,2,159]),import.meta.url).then((e=>hv(e.default||e))),ES=xo(Xx({loader:CS},Gx)),AS=()=>t((()=>import("./pages-dealer-order.sa9YeNyY.js")),__vite__mapDeps([160,64,2,5,65,31,32,10,11,151,161]),import.meta.url).then((e=>hv(e.default||e))),MS=xo(Xx({loader:AS},Gx)),PS=()=>t((()=>import("./pages-dealer-team.btgvhkNe.js")),__vite__mapDeps([162,64,2,5,65,31,32,10,11,148,151,163]),import.meta.url).then((e=>hv(e.default||e))),IS=xo(Xx({loader:PS},Gx)),LS=()=>t((()=>import("./pages-bargain-index.CH8TnzDB.js")),__vite__mapDeps([164,31,2,5,32,26,10,11,12,13,165,166]),import.meta.url).then((e=>hv(e.default||e))),OS=xo(Xx({loader:LS},Gx)),$S=()=>t((()=>import("./pages-bargain-goods-index.DATUhzbs.js")),__vite__mapDeps([167,7,2,8,5,85,86,87,79,21,22,3,4,80,88,26,78,81,82,10,11,83,6,17,165,36,37,12,13,35,34,168]),import.meta.url).then((e=>hv(e.default||e))),DS=xo(Xx({loader:$S},Gx)),RS=()=>t((()=>import("./pages-bargain-task.BORQKUaO.js")),__vite__mapDeps([169,85,86,2,87,5,79,21,22,3,4,80,88,26,10,11,12,13,35,165,170]),import.meta.url).then((e=>hv(e.default||e))),BS=xo(Xx({loader:RS},Gx)),NS=()=>t((()=>import("./pages-sharp-index.CFXQKpCP.js")),__vite__mapDeps([171,31,2,5,32,26,12,13,17,14,172,173]),import.meta.url).then((e=>hv(e.default||e))),qS=xo(Xx({loader:NS},Gx)),jS=()=>t((()=>import("./pages-sharp-goods-index.Dze2ceZ_.js")),__vite__mapDeps([174,7,2,8,5,26,78,79,21,22,3,4,80,81,82,10,11,83,6,17,36,37,12,13,172,34,14,175]),import.meta.url).then((e=>hv(e.default||e))),zS=xo(Xx({loader:jS},Gx)),VS=()=>t((()=>import("./pages-groupon-index.DnTWujKP.js")),__vite__mapDeps([176,15,3,2,4,5,16,31,32,26,17,18,177,178,179,180]),import.meta.url).then((e=>hv(e.default||e))),FS=xo(Xx({loader:VS},Gx)),US=()=>t((()=>import("./pages-groupon-goods-index.B-_Mp1xi.js")),__vite__mapDeps([181,7,2,8,5,85,86,87,79,21,22,3,4,80,88,26,78,81,82,10,11,83,6,182,17,18,183,12,13,177,84,15,16,9,73,89,178,34,33,184]),import.meta.url).then((e=>hv(e.default||e))),WS=xo(Xx({loader:US},Gx)),HS=()=>t((()=>import("./pages-groupon-task-index.CivXDKfg.js")),__vite__mapDeps([185,85,86,2,87,5,79,21,22,3,4,80,88,26,10,11,12,13,40,35,41,182,17,18,183,177,179,186]),import.meta.url).then((e=>hv(e.default||e))),YS=xo(Xx({loader:HS},Gx)),XS=()=>t((()=>import("./pages-live-index.Bbpk7E_I.js")),__vite__mapDeps([187,31,2,5,32,26,188]),import.meta.url).then((e=>hv(e.default||e))),GS=xo(Xx({loader:XS},Gx));function JS(e,t){return Hr(),Qr(Ux,null,{page:zn((()=>[ri(e,Xx({},t,{ref:"page"}),null,512)])),_:1})}function QS(){return"undefined"!=typeof navigator&&"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}}window.__uniRoutes=[{path:"/",alias:"/pages/index/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(Qx,t)}},loader:Jx,meta:{isQuit:!0,isEntry:!0,isTabBar:!0,tabBarIndex:0,enablePullDownRefresh:!0,navigationBar:{type:"default"},isNVue:!1}},{path:"/pages/category/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(Zx,t)}},loader:Kx,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:1,navigationBar:{titleText:"全部分类",type:"default"},isNVue:!1}},{path:"/pages/cart/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(tT,t)}},loader:eT,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:2,navigationBar:{titleText:"购物车",type:"default"},isNVue:!1}},{path:"/pages/user/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(oT,t)}},loader:nT,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:3,enablePullDownRefresh:!0,navigationBar:{titleText:"个人中心",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/custom/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(iT,t)}},loader:rT,meta:{enablePullDownRefresh:!0,navigationBar:{type:"default"},isNVue:!1}},{path:"/pages/search/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(sT,t)}},loader:aT,meta:{navigationBar:{titleText:"商品搜索",type:"default"},isNVue:!1}},{path:"/pages/login/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(cT,t)}},loader:lT,meta:{navigationBar:{titleText:"会员登录",type:"default"},isNVue:!1}},{path:"/pages/user/bind/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(dT,t)}},loader:uT,meta:{navigationBar:{titleText:"绑定手机",type:"default"},isNVue:!1}},{path:"/pages/user/personal/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(fT,t)}},loader:pT,meta:{navigationBar:{titleText:"个人信息",type:"default"},isNVue:!1}},{path:"/pages/article/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(gT,t)}},loader:hT,meta:{navigationBar:{titleText:"资讯列表",type:"default"},isNVue:!1}},{path:"/pages/article/detail",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(vT,t)}},loader:mT,meta:{navigationBar:{titleText:"资讯详情",type:"default"},isNVue:!1}},{path:"/pages/help/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(bT,t)}},loader:yT,meta:{navigationBar:{titleText:"帮助中心",type:"default"},isNVue:!1}},{path:"/pages/coupon/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(wT,t)}},loader:_T,meta:{navigationBar:{titleText:"领券中心",type:"default"},isNVue:!1}},{path:"/pages/goods/list",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(TT,t)}},loader:xT,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"商品列表",type:"default"},isNVue:!1}},{path:"/pages/goods/detail",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(kT,t)}},loader:ST,meta:{navigationBar:{titleText:"商品详情页",type:"default"},isNVue:!1}},{path:"/pages/comment/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(ET,t)}},loader:CT,meta:{navigationBar:{titleText:"商品评价页",type:"default"},isNVue:!1}},{path:"/pages/my-coupon/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(MT,t)}},loader:AT,meta:{navigationBar:{titleText:"我的优惠券",type:"default"},isNVue:!1}},{path:"/pages/address/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(IT,t)}},loader:PT,meta:{navigationBar:{titleText:"收货地址",type:"default"},isNVue:!1}},{path:"/pages/address/create",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(OT,t)}},loader:LT,meta:{navigationBar:{titleText:"新增收货地址",type:"default"},isNVue:!1}},{path:"/pages/address/update",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(DT,t)}},loader:$T,meta:{navigationBar:{titleText:"编辑收货地址",type:"default"},isNVue:!1}},{path:"/pages/points/log",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(BT,t)}},loader:RT,meta:{navigationBar:{titleText:"账单明细",type:"default"},isNVue:!1}},{path:"/pages/wallet/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(qT,t)}},loader:NT,meta:{navigationBar:{titleText:"我的钱包",type:"default"},isNVue:!1}},{path:"/pages/wallet/balance/log",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(zT,t)}},loader:jT,meta:{navigationBar:{titleText:"账单详情",type:"default"},isNVue:!1}},{path:"/pages/wallet/recharge/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(FT,t)}},loader:VT,meta:{navigationBar:{titleText:"充值中心",type:"default"},isNVue:!1}},{path:"/pages/wallet/recharge/order",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(WT,t)}},loader:UT,meta:{navigationBar:{titleText:"充值记录",type:"default"},isNVue:!1}},{path:"/pages/checkout/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(YT,t)}},loader:HT,meta:{navigationBar:{titleText:"订单结算台",type:"default"},isNVue:!1}},{path:"/pages/checkout/cashier/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(GT,t)}},loader:XT,meta:{navigationBar:{titleText:"支付订单",type:"default"},isNVue:!1}},{path:"/pages/order/center",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(QT,t)}},loader:JT,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"订单中心",type:"default"},isNVue:!1}},{path:"/pages/order/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(ZT,t)}},loader:KT,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"我的订单",type:"default"},isNVue:!1}},{path:"/pages/order/detail",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(tS,t)}},loader:eS,meta:{navigationBar:{backgroundColor:"#e8c269",titleText:"订单详情",type:"default",titleColor:"#ffffff"},isNVue:!1}},{path:"/pages/order/express/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(oS,t)}},loader:nS,meta:{navigationBar:{titleText:"物流跟踪",type:"default"},isNVue:!1}},{path:"/pages/order/extract/check",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(iS,t)}},loader:rS,meta:{navigationBar:{titleText:"订单自提核销",type:"default"},isNVue:!1}},{path:"/pages/order/comment/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(sS,t)}},loader:aS,meta:{navigationBar:{titleText:"订单评价",type:"default"},isNVue:!1}},{path:"/pages/refund/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(cS,t)}},loader:lS,meta:{navigationBar:{titleText:"退换/售后",type:"default"},isNVue:!1}},{path:"/pages/refund/detail",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(dS,t)}},loader:uS,meta:{navigationBar:{titleText:"售后详情",type:"default"},isNVue:!1}},{path:"/pages/refund/apply",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(fS,t)}},loader:pS,meta:{navigationBar:{titleText:"申请售后",type:"default"},isNVue:!1}},{path:"/pages/shop/extract",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(gS,t)}},loader:hS,meta:{navigationBar:{titleText:"选择自提门店",type:"default"},isNVue:!1}},{path:"/pages/shop/detail",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(vS,t)}},loader:mS,meta:{navigationBar:{titleText:"门店详情",type:"default"},isNVue:!1}},{path:"/pages/dealer/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(bS,t)}},loader:yS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/apply",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(wS,t)}},loader:_S,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/withdraw/apply",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(TS,t)}},loader:xS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/withdraw/list",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(kS,t)}},loader:SS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/poster",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(ES,t)}},loader:CS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/order",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(MS,t)}},loader:AS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/team",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(IS,t)}},loader:PS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/bargain/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(OS,t)}},loader:LS,meta:{navigationBar:{titleText:"砍价会场",type:"default"},isNVue:!1}},{path:"/pages/bargain/goods/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(DS,t)}},loader:$S,meta:{navigationBar:{titleText:"砍价商品",type:"default"},isNVue:!1}},{path:"/pages/bargain/task",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(BS,t)}},loader:RS,meta:{navigationBar:{titleText:"砍价任务",type:"default"},isNVue:!1}},{path:"/pages/sharp/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(qS,t)}},loader:NS,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"整点秒杀会场",type:"default"},isNVue:!1}},{path:"/pages/sharp/goods/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(zS,t)}},loader:jS,meta:{navigationBar:{titleText:"秒杀商品详情",type:"default"},isNVue:!1}},{path:"/pages/groupon/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(FS,t)}},loader:VS,meta:{navigationBar:{titleText:"拼团活动",type:"default"},isNVue:!1}},{path:"/pages/groupon/goods/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(WS,t)}},loader:US,meta:{navigationBar:{titleText:"拼团商品",type:"default"},isNVue:!1}},{path:"/pages/groupon/task/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(YS,t)}},loader:HS,meta:{navigationBar:{backgroundColor:"#FF5644",titleText:"拼团详情",type:"default",titleColor:"#ffffff"},isNVue:!1}},{path:"/pages/live/index",component:{setup(){const e=uv(),t=e&&e.$route&&e.$route.query||{};return()=>JS(GS,t)}},loader:XS,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"直播列表",type:"default"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const KS="function"==typeof Proxy;class ZS{constructor(e,t){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=e,this.hook=t;const n={};if(e.settings)for(const i in e.settings){const t=e.settings[i];n[i]=t.defaultValue}const o=`__vue-devtools-plugin-settings__${e.id}`;let r={...n};try{const e=localStorage.getItem(o),t=JSON.parse(e);Object.assign(r,t)}catch(yC){}this.fallbacks={getSettings:()=>r,setSettings(e){try{localStorage.setItem(o,JSON.stringify(e))}catch(yC){}r=e}},t.on("plugin:settings:set",((e,t)=>{e===this.plugin.id&&this.fallbacks.setSettings(t)})),this.proxiedOn=new Proxy({},{get:(e,t)=>this.target?this.target.on[t]:(...e)=>{this.onQueue.push({method:t,args:e})}}),this.proxiedTarget=new Proxy({},{get:(e,t)=>this.target?this.target[t]:"on"===t?this.proxiedOn:Object.keys(this.fallbacks).includes(t)?(...e)=>(this.targetQueue.push({method:t,args:e,resolve:()=>{}}),this.fallbacks[t](...e)):(...e)=>new Promise((n=>{this.targetQueue.push({method:t,args:e,resolve:n})}))})}async setRealTarget(e){this.target=e;for(const t of this.onQueue)this.target.on[t.method](...t.args);for(const t of this.targetQueue)t.resolve(await this.target[t.method](...t.args))}}function ek(e,t){const n=QS(),o=QS().__VUE_DEVTOOLS_GLOBAL_HOOK__,r=KS&&e.enableEarlyProxy;if(!o||!n.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__&&r){const i=r?new ZS(e,o):null;(n.__VUE_DEVTOOLS_PLUGINS__=n.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:e,setupFn:t,proxy:i}),i&&t(i.proxiedTarget)}else o.emit("devtools-plugin:setup",e,t)}
/*!
 * vuex v4.1.0
 * (c) 2022 Evan You
 * @license MIT
 */function tk(e,t){Object.keys(e).forEach((function(n){return t(e[n],n)}))}function nk(e,t,n){return t.indexOf(e)<0&&(n&&n.prepend?t.unshift(e):t.push(e)),function(){var n=t.indexOf(e);n>-1&&t.splice(n,1)}}function ok(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var n=e.state;ik(e,n,[],e._modules.root,!0),rk(e,n,t)}function rk(e,t,n){var o=e._state,r=e._scope;e.getters={},e._makeLocalGettersCache=Object.create(null);var i=e._wrappedGetters,a={},s={},l=je(!0);l.run((function(){tk(i,(function(t,n){a[n]=function(e,t){return function(){return e(t)}}(t,e),s[n]=Ei((function(){return a[n]()})),Object.defineProperty(e.getters,n,{get:function(){return s[n].value},enumerable:!0})}))})),e._state=Ut({data:t}),e._scope=l,e.strict&&function(e){to((function(){return e._state.data}),(function(){}),{deep:!0,flush:"sync"})}(e),o&&n&&e._withCommit((function(){o.data=null})),r&&r.stop()}function ik(e,t,n,o,r){var i=!n.length,a=e._modules.getNamespace(n);if(o.namespaced&&(e._modulesNamespaceMap[a],e._modulesNamespaceMap[a]=o),!i&&!r){var s=sk(t,n.slice(0,-1)),l=n[n.length-1];e._withCommit((function(){s[l]=o.state}))}var c=o.context=function(e,t,n){var o=""===t,r={dispatch:o?e.dispatch:function(n,o,r){var i=lk(n,o,r),a=i.payload,s=i.options,l=i.type;return s&&s.root||(l=t+l),e.dispatch(l,a)},commit:o?e.commit:function(n,o,r){var i=lk(n,o,r),a=i.payload,s=i.options,l=i.type;s&&s.root||(l=t+l),e.commit(l,a,s)}};return Object.defineProperties(r,{getters:{get:o?function(){return e.getters}:function(){return ak(e,t)}},state:{get:function(){return sk(e.state,n)}}}),r}(e,a,n);o.forEachMutation((function(t,n){!function(e,t,n,o){(e._mutations[t]||(e._mutations[t]=[])).push((function(t){n.call(e,o.state,t)}))}(e,a+n,t,c)})),o.forEachAction((function(t,n){var o=t.root?n:a+n,r=t.handler||t;!function(e,t,n,o){(e._actions[t]||(e._actions[t]=[])).push((function(t){var r,i=n.call(e,{dispatch:o.dispatch,commit:o.commit,getters:o.getters,state:o.state,rootGetters:e.getters,rootState:e.state},t);return(r=i)&&"function"==typeof r.then||(i=Promise.resolve(i)),e._devtoolHook?i.catch((function(t){throw e._devtoolHook.emit("vuex:error",t),t})):i}))}(e,o,r,c)})),o.forEachGetter((function(t,n){!function(e,t,n,o){if(e._wrappedGetters[t])return;e._wrappedGetters[t]=function(e){return n(o.state,o.getters,e.state,e.getters)}}(e,a+n,t,c)})),o.forEachChild((function(o,i){ik(e,t,n.concat(i),o,r)}))}function ak(e,t){if(!e._makeLocalGettersCache[t]){var n={},o=t.length;Object.keys(e.getters).forEach((function(r){if(r.slice(0,o)===t){var i=r.slice(o);Object.defineProperty(n,i,{get:function(){return e.getters[r]},enumerable:!0})}})),e._makeLocalGettersCache[t]=n}return e._makeLocalGettersCache[t]}function sk(e,t){return t.reduce((function(e,t){return e[t]}),e)}function lk(e,t,n){var o;return null!==(o=e)&&"object"==typeof o&&e.type&&(n=t,t=e,e=e.type),{type:e,payload:t,options:n}}var ck=0;function uk(e,t){ek({id:"org.vuejs.vuex",app:e,label:"Vuex",homepage:"https://next.vuex.vuejs.org/",logo:"https://vuejs.org/images/icons/favicon-96x96.png",packageName:"vuex",componentStateTypes:["vuex bindings"]},(function(n){n.addTimelineLayer({id:"vuex:mutations",label:"Vuex Mutations",color:dk}),n.addTimelineLayer({id:"vuex:actions",label:"Vuex Actions",color:dk}),n.addInspector({id:"vuex",label:"Vuex",icon:"storage",treeFilterPlaceholder:"Filter stores..."}),n.on.getInspectorTree((function(n){if(n.app===e&&"vuex"===n.inspectorId)if(n.filter){var o=[];gk(o,t._modules.root,n.filter,""),n.rootNodes=o}else n.rootNodes=[hk(t._modules.root,"")]})),n.on.getInspectorState((function(n){if(n.app===e&&"vuex"===n.inspectorId){var o=n.nodeId;ak(t,o),n.state=function(e,t,n){t="root"===n?t:t[n];var o=Object.keys(t),r={state:Object.keys(e.state).map((function(t){return{key:t,editable:!0,value:e.state[t]}}))};if(o.length){var i=function(e){var t={};return Object.keys(e).forEach((function(n){var o=n.split("/");if(o.length>1){var r=t,i=o.pop();o.forEach((function(e){r[e]||(r[e]={_custom:{value:{},display:e,tooltip:"Module",abstract:!0}}),r=r[e]._custom.value})),r[i]=mk((function(){return e[n]}))}else t[n]=mk((function(){return e[n]}))})),t}(t);r.getters=Object.keys(i).map((function(e){return{key:e.endsWith("/")?fk(e):e,editable:!1,value:mk((function(){return i[e]}))}}))}return r}((r=t._modules,(a=(i=o).split("/").filter((function(e){return e}))).reduce((function(e,t,n){var o=e[t];if(!o)throw new Error('Missing module "'+t+'" for path "'+i+'".');return n===a.length-1?o:o._children}),"root"===i?r:r.root._children)),"root"===o?t.getters:t._makeLocalGettersCache,o)}var r,i,a})),n.on.editInspectorState((function(n){if(n.app===e&&"vuex"===n.inspectorId){var o=n.nodeId,r=n.path;"root"!==o&&(r=o.split("/").filter(Boolean).concat(r)),t._withCommit((function(){n.set(t._state.data,r,n.state.value)}))}})),t.subscribe((function(e,t){var o={};e.payload&&(o.payload=e.payload),o.state=t,n.notifyComponentUpdate(),n.sendInspectorTree("vuex"),n.sendInspectorState("vuex"),n.addTimelineEvent({layerId:"vuex:mutations",event:{time:Date.now(),title:e.type,data:o}})})),t.subscribeAction({before:function(e,t){var o={};e.payload&&(o.payload=e.payload),e._id=ck++,e._time=Date.now(),o.state=t,n.addTimelineEvent({layerId:"vuex:actions",event:{time:e._time,title:e.type,groupId:e._id,subtitle:"start",data:o}})},after:function(e,t){var o={},r=Date.now()-e._time;o.duration={_custom:{type:"duration",display:r+"ms",tooltip:"Action duration",value:r}},e.payload&&(o.payload=e.payload),o.state=t,n.addTimelineEvent({layerId:"vuex:actions",event:{time:Date.now(),title:e.type,groupId:e._id,subtitle:"end",data:o}})}})}))}var dk=8702998,pk={label:"namespaced",textColor:16777215,backgroundColor:6710886};function fk(e){return e&&"root"!==e?e.split("/").slice(-2,-1)[0]:"Root"}function hk(e,t){return{id:t||"root",label:fk(t),tags:e.namespaced?[pk]:[],children:Object.keys(e._children).map((function(n){return hk(e._children[n],t+n+"/")}))}}function gk(e,t,n,o){o.includes(n)&&e.push({id:o||"root",label:o.endsWith("/")?o.slice(0,o.length-1):o||"Root",tags:t.namespaced?[pk]:[]}),Object.keys(t._children).forEach((function(r){gk(e,t._children[r],n,o+r+"/")}))}function mk(e){try{return e()}catch(yC){return yC}}var vk=function(e,t){this.runtime=t,this._children=Object.create(null),this._rawModule=e;var n=e.state;this.state=("function"==typeof n?n():n)||{}},yk={namespaced:{configurable:!0}};yk.namespaced.get=function(){return!!this._rawModule.namespaced},vk.prototype.addChild=function(e,t){this._children[e]=t},vk.prototype.removeChild=function(e){delete this._children[e]},vk.prototype.getChild=function(e){return this._children[e]},vk.prototype.hasChild=function(e){return e in this._children},vk.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},vk.prototype.forEachChild=function(e){tk(this._children,e)},vk.prototype.forEachGetter=function(e){this._rawModule.getters&&tk(this._rawModule.getters,e)},vk.prototype.forEachAction=function(e){this._rawModule.actions&&tk(this._rawModule.actions,e)},vk.prototype.forEachMutation=function(e){this._rawModule.mutations&&tk(this._rawModule.mutations,e)},Object.defineProperties(vk.prototype,yk);var bk=function(e){this.register([],e,!1)};function _k(e,t,n){if(t.update(n),n.modules)for(var o in n.modules){if(!t.getChild(o))return;_k(e.concat(o),t.getChild(o),n.modules[o])}}bk.prototype.get=function(e){return e.reduce((function(e,t){return e.getChild(t)}),this.root)},bk.prototype.getNamespace=function(e){var t=this.root;return e.reduce((function(e,n){return e+((t=t.getChild(n)).namespaced?n+"/":"")}),"")},bk.prototype.update=function(e){_k([],this.root,e)},bk.prototype.register=function(e,t,n){var o=this;void 0===n&&(n=!0);var r=new vk(t,n);0===e.length?this.root=r:this.get(e.slice(0,-1)).addChild(e[e.length-1],r);t.modules&&tk(t.modules,(function(t,r){o.register(e.concat(r),t,n)}))},bk.prototype.unregister=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1],o=t.getChild(n);o&&o.runtime&&t.removeChild(n)},bk.prototype.isRegistered=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1];return!!t&&t.hasChild(n)};var wk=function(e){var t=this;void 0===e&&(e={});var n=e.plugins;void 0===n&&(n=[]);var o=e.strict;void 0===o&&(o=!1);var r=e.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new bk(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._scope=null,this._devtools=r;var i=this,a=this.dispatch,s=this.commit;this.dispatch=function(e,t){return a.call(i,e,t)},this.commit=function(e,t,n){return s.call(i,e,t,n)},this.strict=o;var l=this._modules.root.state;ik(this,l,[],this._modules.root),rk(this,l),n.forEach((function(e){return e(t)}))},xk={state:{configurable:!0}};wk.prototype.install=function(e,t){e.provide(t||"store",this),e.config.globalProperties.$store=this,void 0!==this._devtools&&this._devtools&&uk(e,this)},xk.state.get=function(){return this._state.data},xk.state.set=function(e){},wk.prototype.commit=function(e,t,n){var o=this,r=lk(e,t,n),i=r.type,a=r.payload,s={type:i,payload:a},l=this._mutations[i];l&&(this._withCommit((function(){l.forEach((function(e){e(a)}))})),this._subscribers.slice().forEach((function(e){return e(s,o.state)})))},wk.prototype.dispatch=function(e,t){var n=this,o=lk(e,t),r=o.type,i=o.payload,a={type:r,payload:i},s=this._actions[r];if(s){try{this._actionSubscribers.slice().filter((function(e){return e.before})).forEach((function(e){return e.before(a,n.state)}))}catch(yC){}var l=s.length>1?Promise.all(s.map((function(e){return e(i)}))):s[0](i);return new Promise((function(e,t){l.then((function(t){try{n._actionSubscribers.filter((function(e){return e.after})).forEach((function(e){return e.after(a,n.state)}))}catch(yC){}e(t)}),(function(e){try{n._actionSubscribers.filter((function(e){return e.error})).forEach((function(t){return t.error(a,n.state,e)}))}catch(yC){}t(e)}))}))}},wk.prototype.subscribe=function(e,t){return nk(e,this._subscribers,t)},wk.prototype.subscribeAction=function(e,t){return nk("function"==typeof e?{before:e}:e,this._actionSubscribers,t)},wk.prototype.watch=function(e,t,n){var o=this;return to((function(){return e(o.state,o.getters)}),t,Object.assign({},n))},wk.prototype.replaceState=function(e){var t=this;this._withCommit((function(){t._state.data=e}))},wk.prototype.registerModule=function(e,t,n){void 0===n&&(n={}),"string"==typeof e&&(e=[e]),this._modules.register(e,t),ik(this,this.state,e,this._modules.get(e),n.preserveState),rk(this,this.state)},wk.prototype.unregisterModule=function(e){var t=this;"string"==typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit((function(){delete sk(t.state,e.slice(0,-1))[e[e.length-1]]})),ok(this)},wk.prototype.hasModule=function(e){return"string"==typeof e&&(e=[e]),this._modules.isRegistered(e)},wk.prototype.hotUpdate=function(e){this._modules.update(e),ok(this,!0)},wk.prototype._withCommit=function(e){var t=this._committing;this._committing=!0,e(),this._committing=t},Object.defineProperties(wk.prototype,xk);const Tk={set(e,t,n){uni.setStorageSync(e,t);const o=parseInt(n);if(o>0){let t=Date.parse(new Date);t=t/1e3+o,uni.setStorageSync(e+"_expiry",t+"")}else uni.removeStorageSync(e+"_expiry")},get(e,t){const n=parseInt(uni.getStorageSync(e+"_expiry"));if(n&&parseInt(n)<Date.parse(new Date)/1e3)return t||!1;const o=uni.getStorageSync(e);return o||(null!=t&&""!=t||(t=!1),t)},remove(e){uni.removeStorageSync(e),uni.removeStorageSync(e+"_expiry")},clear(){uni.clearStorageSync()}},Sk={state:{storeId:null,platform:"",refereeId:null,modules:[]},mutations:{SET_STORE_ID:(e,t)=>{e.storeId=t},SET_PLATFORM:(e,t)=>{e.platform=t},SET_REFEREE_ID:(e,t)=>{e.refereeId=t},SET_MODULES:(e,t)=>{e.modules=t}},actions:{setRefereeId({commit:e},t){const n=this,o=parseInt(t);return new Promise(((t,r)=>{o>0&&n.getters.userId!=o&&(Tk.set("refereeId",o),e("SET_REFEREE_ID",o),t())}))},SetModules:({commit:e},t)=>new Promise(((n,o)=>{Tk.set("modules",t),e("SET_MODULES",t),n()}))}},kk=(e,t)=>{let n=/^(http|https):\/\//.test(t.url),o=Object.assign({timeout:e.timeout},e.config,t);return"FILE"==t.method?o.url=n?t.url:e.fileUrl+t.url:o.url=n?t.url:e.baseUrl+t.url,t.header?o.header=Object.assign({},e.header,t.header):o.header=Object.assign({},e.header),o};var Ck={qiniuRegion:"",qiniuImageURLPrefix:"",qiniuUploadToken:"",qiniuUploadTokenURL:"",qiniuUploadTokenFunction:null,qiniuShouldUseQiniuFileName:!1};function Ek(e,t,n,o,r,i){var a;if(null!=e)if(o&&function(e){e.region?Ck.qiniuRegion=e.region:console.error("qiniu uploader need your bucket region"),e.uptoken?Ck.qiniuUploadToken=e.uptoken:e.uptokenURL?Ck.qiniuUploadTokenURL=e.uptokenURL:e.uptokenFunc&&(Ck.qiniuUploadTokenFunction=e.uptokenFunc),e.domain&&(Ck.qiniuImageURLPrefix=e.domain),Ck.qiniuShouldUseQiniuFileName=e.shouldUseQiniuFileName}(o),Ck.qiniuUploadToken)Ak(e,t,n,o,r,i);else if(Ck.qiniuUploadTokenURL)a=function(){Ak(e,t,n,o,r,i)},wx.request({url:Ck.qiniuUploadTokenURL,success:function(e){var t=e.data.uptoken;t&&t.length>0?(Ck.qiniuUploadToken=t,a&&a()):console.error("qiniuUploader cannot get your token, please check the uptokenURL or server")},fail:function(e){console.error("qiniu UploadToken is null, please check the init config or networking: "+e)}});else{if(!Ck.qiniuUploadTokenFunction)return void console.error("qiniu uploader need one of [uptoken, uptokenURL, uptokenFunc]");if(Ck.qiniuUploadToken=Ck.qiniuUploadTokenFunction(),null==Ck.qiniuUploadToken&&Ck.qiniuUploadToken.length>0)return void console.error("qiniu UploadTokenFunction result is null, please check the return value");Ak(e,t,n,o,r,i)}else console.error("qiniu uploader need filePath to upload")}function Ak(e,t,n,o,r,i){if(null==Ck.qiniuUploadToken&&Ck.qiniuUploadToken.length>0)console.error("qiniu UploadToken is null, please check the init config or networking");else{var a=function(e){var t=null;switch(e){case"ECN":t="https://up.qbox.me";break;case"NCN":t="https://up-z1.qbox.me";break;case"SCN":t="https://up-z2.qbox.me";break;case"NA":t="https://up-na0.qbox.me";break;case"ASG":t="https://up-as0.qbox.me";break;default:console.error("please make the region is with one of [ECN, SCN, NCN, NA, ASG]")}return t}(Ck.qiniuRegion),s=e.split("//")[1];o&&o.key&&(s=o.key);var l={token:Ck.qiniuUploadToken};Ck.qiniuShouldUseQiniuFileName||(l.key=s);var c=wx.uploadFile({url:a,filePath:e,name:"file",formData:l,success:function(e){var o=e.data;e.data.hasOwnProperty("type")&&"Buffer"===e.data.type&&(o=String.fromCharCode.apply(null,e.data.data));try{var r=JSON.parse(o),i=Ck.qiniuImageURLPrefix+"/"+r.key;r.imageURL=i,t&&t(r)}catch(yC){console.log("parse JSON failed, origin String is: "+o),n&&n(yC)}},fail:function(e){console.error(e),n&&n(e)}});c.onProgressUpdate((e=>{r&&r(e)})),i&&i((()=>{c.abort()}))}}const Mk=function(e){return new Promise(((t,n)=>{uni.chooseImage({count:e.count||9,sizeType:e.sizeType||["original","compressed"],sourceType:e.sourceType||["album","camera"],success:function(e){t(e.tempFiles)},fail:e=>{n({errMsg:e.errMsg,errCode:e.errCode,statusCode:0})}})}))},Pk=function(e){return new Promise(((t,n)=>{uni.chooseVideo({sourceType:e.sourceType||["album","camera"],compressed:e.compressed||!1,maxDuration:e.maxDuration||60,camera:e.camera||"back",success:function(e){let n=[{path:e.tempFilePath}];n[0].duration=e.duration,n[0].size=e.size,n[0].height=e.height,n[0].width=e.width,n[0].name=e.name,t(n)},fail:e=>{n({errMsg:e.errMsg,errCode:e.errCode,statusCode:0})}})}))},Ik=function(e,t){return new Promise(((n,o)=>{if(Array.isArray(e.files)){let r=e.files.length,i=new Array;t?t((t=>{let a=t.visitPrefix.length;"/"==t.visitPrefix.charAt(a-1)&&(t.visitPrefix=t.visitPrefix.substring(0,a-1)),function a(s){let l=e.files[s],c=function(e,t=""){const n="0123456789qwertyuioplkjhgfdsazxcvbnm";let o="",r=new Date;for(let i=0;i<e;i++)o+=n.charAt(Math.ceil(1e8*Math.random())%n.length);return"file/"+t+r.getTime()+o}(10,t.folderPath),u={fileIndex:s,files:e.files,...l};if(l.name){u.name=l.name;let e=l.name.split(".");c+="."+e[e.length-1]}Ek(l.path||l,(t=>{u.url=t.imageURL,e.onEachUpdate&&e.onEachUpdate({url:t.imageURL,...u}),i.push(t.imageURL),r-1>s?a(s+1):n(i)}),(e=>{o(e)}),{region:t.region||"SCN",domain:t.visitPrefix,key:c,uptoken:t.token,uptokenURL:"UpTokenURL.com/uptoken"},(t=>{console.log(e),e.onProgressUpdate&&e.onProgressUpdate(Object.assign({},u,t))}))}(0)})):o({errMsg:"请添加七牛云回调方法：getQnToken",statusCode:0})}else o({errMsg:"files 必须是数组类型",statusCode:0})}))};const Lk=Object.assign({},{name:"萤火商城2.0",apiUrl:"./index.php?s=/api/",storeId:10001,enabledSettingCache:!0,enabledAppShareWeixin:!1,enabledH5Multi:!1,domainIdRegex:/shop[\-]?(\d+)\./},{name:"萤火商城2.0",apiUrl:"./index.php?s=/api/",storeId:10001,enabledSettingCache:!0,enabledH5Multi:!0}),Ok={all:()=>Lk,get:(e,t)=>Lk.hasOwnProperty(e)?Lk[e]:(console.error(`检测到不存在的配置项: ${e}`),t),getStoreId(){if(this.get("enabledH5Multi")){const e=window.location.hostname,t=this.get("domainIdRegex");if(e.match(t)){return t.exec(e)[1].trim()}}return this.get("storeId")}},$k=Ok.get("apiUrl"),Dk=new class extends class{constructor(e){this.baseUrl=e.baseUrl||"",this.fileUrl=e.fileUrl||"",this.timeout=e.timeout||6e3,this.defaultUploadUrl=e.defaultUploadUrl||"",this.header=e.header||{},this.config=e.config||{isPrompt:!0,load:!0,isFactory:!0,resend:0}}post(e="",t={},n={}){return this.request({method:"POST",data:t,url:e,...n})}get(e="",t={},n={}){return this.request({method:"GET",data:t,url:e,...n})}put(e="",t={},n={}){return this.request({method:"PUT",data:t,url:e,...n})}delete(e="",t={},n={}){return this.request({method:"DELETE",data:t,url:e,...n})}jsonp(e="",t={},n={}){return this.request({method:"JSONP",data:t,url:e,...n})}async request(e){let t,n=!1;try{if(!e.url)throw{errMsg:"【request】缺失数据url",statusCode:0};if(t=kk(this,e),n=!0,this.requestStart){let e=this.requestStart(t);if("object"!=typeof e)throw{errMsg:"【request】请求开始拦截器未通过",statusCode:0,data:t.data,method:t.method,header:t.header,url:t.url};["data","header","isPrompt","load","isFactory"].forEach((n=>{t[n]=e[n]}))}let o={};if(o="JSONP"==t.method?await(e=>new Promise(((t,n)=>{let o="";Object.keys(e.data).forEach((t=>{o+=t+"="+e.data[t]+"&"})),""!==o&&(o=o.substr(0,o.lastIndexOf("&"))),e.url=e.url+"?"+o;let r="callback"+Math.ceil(1e6*Math.random());window[r]=e=>{t(e)};let i=document.createElement("script");i.src=e.url+"&callback="+r,document.head.appendChild(i),document.head.removeChild(i)})))(t):await(e=>new Promise(((t,n)=>{let o=!0,r={url:e.url,header:e.header,success:e=>{o=!1,t(e)},fail:e=>{o=!1,"request:fail abort"==e.errMsg?n({errMsg:"请求超时，请重新尝试",statusCode:0}):n(e)}};e.method&&(r.method=e.method),e.data&&(r.data=e.data),e.dataType&&(r.dataType=e.dataType),e.responseType&&(r.responseType=e.responseType),e.withCredentials&&(r.withCredentials=e.withCredentials);let i=uni.request(r);setTimeout((()=>{o&&i.abort()}),e.timeout)})))(t),t.isFactory&&this.dataFactory){let e=await this.dataFactory({...t,response:o});return Promise.resolve(e)}return Promise.resolve(o)}catch(o){return this.requestError&&this.requestError(o),Promise.reject(o)}finally{n&&this.requestEnd&&this.requestEnd(t)}}}{constructor(e){super(e)}async qnImgUpload(e={}){let t;try{t=await Mk(e),e.onSelectComplete&&e.onSelectComplete(t)}catch(n){return this.requestError&&this.requestError(n),Promise.reject(n)}if(t)return this.qnFileUpload({...e,files:t})}async qnVideoUpload(e={}){let t;try{t=await Pk(e),e.onSelectComplete&&e.onSelectComplete(t)}catch(n){return this.requestError&&this.requestError(n),Promise.reject(n)}if(t)return this.qnFileUpload({...e,files:t})}async qnFileUpload(e={}){let t;try{if(t={...this.config,...e,header:{},method:"FILE"},this.requestStart){let e=this.requestStart(t);if("object"!=typeof e)throw{errMsg:"【request】请求开始拦截器未通过",statusCode:0,data:t.data,method:t.method,header:t.header,url:t.url};["load","files"].forEach((n=>{t[n]=e[n]}))}let n=await Ik(t,this.getQnToken);return Promise.resolve(n)}catch(n){return this.requestError&&this.requestError(n),Promise.reject(n)}finally{this.requestEnd&&this.requestEnd(t)}}async urlImgUpload(){let e={};arguments[0]&&("string"==typeof arguments[0]?e.url=arguments[0]:"object"==typeof arguments[0]&&(e=Object.assign(e,arguments[0]))),arguments[1]&&"object"==typeof arguments[1]&&(e=Object.assign(e,arguments[1]));try{e.files=await Mk(e),e.onSelectComplete&&e.onSelectComplete(e.files)}catch(t){return this.requestError&&this.requestError(t),Promise.reject(t)}if(e.files)return this.urlFileUpload(e)}async urlVideoUpload(){let e={};arguments[0]&&("string"==typeof arguments[0]?e.url=arguments[0]:"object"==typeof arguments[0]&&(e=Object.assign(e,arguments[0]))),arguments[1]&&"object"==typeof arguments[1]&&(e=Object.assign(e,arguments[1]));try{e.files=await Pk(e),e.onSelectComplete&&e.onSelectComplete(e.files)}catch(t){return this.requestError&&this.requestError(t),Promise.reject(t)}if(e.files)return this.urlFileUpload(e)}async urlFileUpload(){let e={method:"FILE"};arguments[0]&&("string"==typeof arguments[0]?e.url=arguments[0]:"object"==typeof arguments[0]&&(e=Object.assign(e,arguments[0]))),arguments[1]&&"object"==typeof arguments[1]&&(e=Object.assign(e,arguments[1])),!e.url&&this.defaultUploadUrl&&(e.url=this.defaultUploadUrl);let t=!1;try{if(!e.url)throw{errMsg:"【request】文件上传缺失数据url",statusCode:0,data:e.data,method:e.method,header:e.header,url:e.url};if(e=kk(this,e),t=!0,this.requestStart){let t=this.requestStart(e);if("object"!=typeof t)throw{errMsg:"【request】请求开始拦截器未通过",statusCode:0,data:e.data,method:e.method,header:e.header,url:e.url};["data","header","isPrompt","load","isFactory","files"].forEach((n=>{e[n]=t[n]}))}let n=await function(e,t){return new Promise(((n,o)=>{if(e.header["Content-Type"]&&delete e.header["Content-Type"],e.header["content-type"]&&delete e.header["content-type"],Array.isArray(e.files)){let r=function(s){let l=e.files[s],c={fileIndex:s,files:e.files,...l},u={url:e.url,filePath:l.path,header:e.header,name:e.name||"file",success:l=>{e.isFactory&&t?t({...e,response:l}).then((t=>{a.push(t),e.onEachUpdate&&e.onEachUpdate({data:t,...c}),i<=s?n(a):r(s+1)}),(e=>{o(e)})):(e.onEachUpdate&&e.onEachUpdate({data:l,...c}),a.push(l),i<=s?n(a):r(s+1))},fail:e=>{o(e)}};e.data&&(u.formData=e.data),uni.uploadFile(u).onProgressUpdate((t=>{e.onProgressUpdate&&e.onProgressUpdate(Object.assign({},c,t))}))};const i=e.files.length-1;let a=new Array;r(0)}else o({errMsg:"files 必须是数组类型",statusCode:0})}))}(e,this.dataFactory);return Promise.resolve(n)}catch(n){return this.requestError&&this.requestError(n),Promise.reject(n)}finally{t&&this.requestEnd&&this.requestEnd(e)}}}({baseUrl:$k,fileUrl:$k,defaultUploadUrl:"upload/image",header:{"content-type":"application/json;charset=utf-8"},timeout:15e3,config:{isPrompt:!0,load:!0,isFactory:!0}});let Rk=0;Dk.requestStart=e=>{if(e.load&&(Rk<=0&&uni.showLoading({title:"加载中",mask:!0}),Rk+=1),"FILE"==e.method&&e.maxSize){const t=e.maxSize;for(let n of e.files)if(n.size>t)return setTimeout((()=>{uni.showToast({title:"图片过大，请重新上传",icon:"none"})}),10),!1}return e.header.storeId=iC.getters.storeId,e.header.platform=iC.getters.platform,e.header["Access-Token"]=iC.getters.token,e},Dk.requestEnd=e=>{e.load&&(Rk-=1,Rk<=0&&uni.hideLoading())};let Bk=!1;Dk.dataFactory=async e=>{if(!e.response.statusCode||200!=e.response.statusCode)return Promise.reject({statusCode:e.response.statusCode,errMsg:"http状态码错误"});let t=e.response.data;if("string"==typeof t)try{t=JSON.parse(t)}catch(n){t=!1}return!1===t||"object"!=typeof t?Promise.reject({statusCode:e.response.statusCode,errMsg:"请检查api地址能否访问正常"}):200==t.status?Promise.resolve(t):401==t.status?(iC.dispatch("Logout"),Bk||(Bk=!0,uni.showModal({title:"温馨提示",content:"此时此刻需要您登录喔~",confirmText:"去登录",cancelText:"再逛会",success:e=>{e.confirm&&uni.navigateTo({url:"/pages/login/index"}),e.cancel&&qm().length>1&&uni.navigateBack(),Bk=!1}})),Promise.reject({statusCode:0,errMsg:t.message,result:t})):500==t.status?(e.isPrompt&&setTimeout((()=>{uni.showToast({title:t.message,icon:"none",duration:2500})}),10),Promise.reject({statusCode:0,errMsg:t.message,result:t})):void 0},Dk.requestError=e=>{if(0===e.statusCode)throw e;setTimeout((()=>Nk(e)),10)};const Nk=e=>{let t=`网络请求出错：${e.errMsg}`;"request:fail"===e.errMsg&&(t="网络请求错误：请检查api地址能否访问正常"),uni.showToast({title:t,icon:"none",duration:3500})},qk="passport/login",jk="passport/loginMpWx",zk="passport/loginWxOfficial",Vk="passport/loginMpWxMobile",Fk="passport/loginMpAlipay",Uk="passport/isPersonalMpweixin";function Wk(e,t){return Dk.post(Uk,e,t)}const Hk=(e,{token:t,userId:n})=>{const o=2592e3;Tk.set("userId",n,o),Tk.set("AccessToken",t,o),e("SET_TOKEN",t),e("SET_USER_ID",n)},Yk=e=>e.replace(/\-/g,"/"),Xk=(e={})=>{const t=[];for(const n in e){const o=e[n];o&&(Zk(o)?o.forEach((e=>{t.push(n+"="+e)})):t.push(n+"="+o))}return t.join("&")},Gk=(e="")=>{var t=new Object;if(e)for(var n=e.split("&"),o=0;o<n.length;o++)t[n[o].split("=")[0]]=n[o].split("=")[1]||"";return t},Jk=(e,t)=>{for(var n in t)if(t[n]==e)return!0;return!1},Qk=e=>0===Object.keys(e).length,Kk=e=>"[object Object]"===Object.prototype.toString.call(e),Zk=e=>"[object Array]"===Object.prototype.toString.call(e),eC=e=>Zk(e)?0===e.length:Kk(e)?Qk(e):!e,tC=e=>{let t=Zk(e)?[]:{};if("object"==typeof e){for(let n in e)t[n]="object"==typeof e[n]?tC(e[n]):e[n];return t}};function nC(e,t=100){let n;return function(){const o=this,r=arguments;n&&clearTimeout(n),n=setTimeout((function(){e.apply(o,r)}),t)}}const oC=(e,t)=>e.filter((e=>t.indexOf(e)>-1)),rC=e=>e*(()=>{const{windowWidth:e}=uni.getSystemInfoSync();return(e>750?560:e)/750})();const iC=new wk({modules:{app:Sk,user:{state:{token:"",userId:null},mutations:{SET_TOKEN:(e,t)=>{e.token=t},SET_USER_ID:(e,t)=>{e.userId=t}},actions:{Login:({commit:e},t)=>new Promise(((n,o)=>{(function(e){return Dk.post(qk,e)})({form:t}).then((t=>{const o=t.data;Hk(e,o),n(t)})).catch(o)})),LoginMpWx:({commit:e},t)=>new Promise(((n,o)=>{(function(e,t){return Dk.post(jk,e,t)})({form:t},{isPrompt:!1}).then((t=>{const o=t.data;Hk(e,o),n(t)})).catch(o)})),LoginWxOfficial:({commit:e},t)=>new Promise(((n,o)=>{(function(e,t){return Dk.post(zk,e,t)})({form:t},{isPrompt:!1}).then((t=>{const o=t.data;Hk(e,o),n(t)})).catch(o)})),LoginMpWxMobile:({commit:e},t)=>new Promise(((n,o)=>{(function(e,t){return Dk.post(Vk,e,t)})({form:t},{isPrompt:!1}).then((t=>{const o=t.data;Hk(e,o),n(t)})).catch(o)})),LoginMpAlipay:({commit:e},t)=>new Promise(((n,o)=>{(function(e,t){return Dk.post(Fk,e,t)})({form:t},{isPrompt:!1}).then((t=>{const o=t.data;Hk(e,o),n(t)})).catch(o)})),Logout({commit:e},t){const n=this;return new Promise(((t,o)=>{n.getters.userId>0&&(Tk.remove("userId"),Tk.remove("AccessToken"),e("SET_TOKEN",""),e("SET_USER_ID",null),t())}))}}},theme:{state:{appTheme:{mainBg:"#fa2209",mainBg2:"#ff6335",mainText:"#ffffff",viceBg:"#ffb100",viceBg2:"#ffb900",viceText:"#ffffff"}},mutations:{SET_APP_THEME:(e,t)=>{eC(t)||(e.appTheme=t)}},actions:{SetAppTheme:({commit:e},t)=>new Promise(((n,o)=>{Tk.set("appTheme",t),e("SET_APP_THEME",t),n()}))}}},state:{},mutations:{},actions:{},getters:{storeId:e=>e.app.storeId,platform:e=>e.app.platform,token:e=>e.user.token,userId:e=>e.user.userId,refereeId:e=>e.app.refereeId,appTheme:e=>e.theme.appTheme,modules:e=>e.app.modules}}),aC="store/data",sC=()=>Dk.get(aC),lC=(()=>{const e=window.navigator.userAgent.toLowerCase();return"micromessenger"===String(e.match(/MicroMessenger/i))})()?"WXOFFICIAL":"H5",cC="WXOFFICIAL"===lC,uC="setting/data";class dC{constructor(e){const t=[],n=[];if(!Array.isArray(e))throw new Error("param is not an array!");e.map((e=>{e.key&&e.name&&(t.push(e.key),n.push(e.value),this[e.key]=e,e.key!==e.value&&(this[e.value]=e))})),this.data=e,this.keyArr=t,this.valueArr=n}keyOf(e){return this.data[this.keyArr.indexOf(e)]}valueOf(e){return this.data[this.valueArr.indexOf(e)]}getNameByKey(e){const t=this.keyOf(e);if(!t)throw new Error("No enum constant"+e);return t.name}getNameByValue(e){const t=this.valueOf(e);if(!t)throw new Error("No enum constant"+e);return t.name}getValueByKey(e){const t=this.keyOf(e);if(!t)throw new Error("No enum constant"+e);return t.key}getData(){return this.data}}const pC=new dC([{key:"REGISTER",name:"账户注册设置",value:"register"},{key:"APP_THEME",name:"店铺页面风格",value:"app_theme"},{key:"PAGE_CATEGORY_TEMPLATE",name:"分类页模板",value:"page_category_template"},{key:"POINTS",name:"积分设置",value:"points"},{key:"RECHARGE",name:"充值设置",value:"recharge"},{key:"RECOMMENDED",name:"商品推荐设置",value:"recommended"},{key:"CUSTOMER",name:"商城客服设置",value:"customer"}]),fC=e=>{Tk.set("Setting",e,600)},hC=()=>new Promise(((e,t)=>{Dk.get(uC).then((t=>{e(t.data.setting)}))})),gC=e=>(null==e&&(e=Ok.get("enabledSettingCache")),new Promise(((t,n)=>{const o=Tk.get("Setting");e&&o?t(o):hC().then((e=>{fC(e),t(e)}))}))),mC=(e,t)=>new Promise(((n,o)=>{gC(t).then((t=>n(t[e])))})),vC={setStorage:fC,data:gC,item:mC,setAppTheme:()=>new Promise(((e,t)=>{mC(pC.APP_THEME.value).then((t=>{iC.dispatch("SetAppTheme",t),e()}))})),isShowCustomerBtn:async()=>{const e=await mC(pC.CUSTOMER.value,!0);return!!e.enabled&&("wxqykf"===e.provider||"mpwxkf"===e.provider&&"MP-WEIXIN"===lC)}};var yC,bC={exports:{}};yC=window,bC.exports=function(e,t){if(!e.jWeixin){var n,o={config:"preVerifyJSAPI",onMenuShareTimeline:"menu:share:timeline",onMenuShareAppMessage:"menu:share:appmessage",onMenuShareQQ:"menu:share:qq",onMenuShareWeibo:"menu:share:weiboApp",onMenuShareQZone:"menu:share:QZone",previewImage:"imagePreview",getLocation:"geoLocation",openProductSpecificView:"openProductViewWithPid",addCard:"batchAddCard",openCard:"batchViewCard",chooseWXPay:"getBrandWCPayRequest",openEnterpriseRedPacket:"getRecevieBizHongBaoRequest",startSearchBeacons:"startMonitoringBeacons",stopSearchBeacons:"stopMonitoringBeacons",onSearchBeacons:"onBeaconsInRange",consumeAndShareCard:"consumedShareCard",openAddress:"editAddress"},r=function(){var e={};for(var t in o)e[o[t]]=t;return e}(),i=e.document,a=i.title,s=navigator.userAgent.toLowerCase(),l=navigator.platform.toLowerCase(),c=!(!l.match("mac")&&!l.match("win")),u=-1!=s.indexOf("wxdebugger"),d=-1!=s.indexOf("micromessenger"),p=-1!=s.indexOf("android"),f=-1!=s.indexOf("iphone")||-1!=s.indexOf("ipad"),h=(n=s.match(/micromessenger\/(\d+\.\d+\.\d+)/)||s.match(/micromessenger\/(\d+\.\d+)/))?n[1]:"",g={initStartTime:L(),initEndTime:0,preVerifyStartTime:0,preVerifyEndTime:0},m={version:1,appId:"",initTime:0,preVerifyTime:0,networkType:"",isPreVerifyOk:1,systemType:f?1:p?2:-1,clientVersion:h,url:encodeURIComponent(location.href)},v={},y={_completes:[]},b={state:0,data:{}};O((function(){g.initEndTime=L()}));var _=!1,w=[],x={config:function(t){I("config",v=t);var n=!1!==v.check;O((function(){if(n)k(o.config,{verifyJsApiList:P(v.jsApiList),verifyOpenTagList:P(v.openTagList)},function(){y._complete=function(e){g.preVerifyEndTime=L(),b.state=1,b.data=e},y.success=function(e){m.isPreVerifyOk=0},y.fail=function(e){y._fail?y._fail(e):b.state=-1};var e=y._completes;return e.push((function(){!function(){if(!(c||u||v.debug||h<"6.0.2"||m.systemType<0)){var e=new Image;m.appId=v.appId,m.initTime=g.initEndTime-g.initStartTime,m.preVerifyTime=g.preVerifyEndTime-g.preVerifyStartTime,x.getNetworkType({isInnerInvoke:!0,success:function(t){m.networkType=t.networkType;var n="https://open.weixin.qq.com/sdk/report?v="+m.version+"&o="+m.isPreVerifyOk+"&s="+m.systemType+"&c="+m.clientVersion+"&a="+m.appId+"&n="+m.networkType+"&i="+m.initTime+"&p="+m.preVerifyTime+"&u="+m.url;e.src=n}})}}()})),y.complete=function(t){for(var n=0,o=e.length;n<o;++n)e[n]();y._completes=[]},y}()),g.preVerifyStartTime=L();else{b.state=1;for(var e=y._completes,t=0,r=e.length;t<r;++t)e[t]();y._completes=[]}})),x.invoke||(x.invoke=function(t,n,o){e.WeixinJSBridge&&WeixinJSBridge.invoke(t,E(n),o)},x.on=function(t,n){e.WeixinJSBridge&&WeixinJSBridge.on(t,n)})},ready:function(e){0!=b.state?e():(y._completes.push(e),!d&&v.debug&&e())},error:function(e){h<"6.0.2"||(-1==b.state?e(b.data):y._fail=e)},checkJsApi:function(e){k("checkJsApi",{jsApiList:P(e.jsApiList)},(e._complete=function(e){if(p){var t=e.checkResult;t&&(e.checkResult=JSON.parse(t))}e=function(e){var t=e.checkResult;for(var n in t){var o=r[n];o&&(t[o]=t[n],delete t[n])}return e}(e)},e))},onMenuShareTimeline:function(e){C(o.onMenuShareTimeline,{complete:function(){k("shareTimeline",{title:e.title||a,desc:e.title||a,img_url:e.imgUrl||"",link:e.link||location.href,type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareAppMessage:function(e){C(o.onMenuShareAppMessage,{complete:function(t){"favorite"===t.scene?k("sendAppMessage",{title:e.title||a,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""}):k("sendAppMessage",{title:e.title||a,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareQQ:function(e){C(o.onMenuShareQQ,{complete:function(){k("shareQQ",{title:e.title||a,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareWeibo:function(e){C(o.onMenuShareWeibo,{complete:function(){k("shareWeiboApp",{title:e.title||a,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareQZone:function(e){C(o.onMenuShareQZone,{complete:function(){k("shareQZone",{title:e.title||a,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},updateTimelineShareData:function(e){k("updateTimelineShareData",{title:e.title,link:e.link,imgUrl:e.imgUrl},e)},updateAppMessageShareData:function(e){k("updateAppMessageShareData",{title:e.title,desc:e.desc,link:e.link,imgUrl:e.imgUrl},e)},startRecord:function(e){k("startRecord",{},e)},stopRecord:function(e){k("stopRecord",{},e)},onVoiceRecordEnd:function(e){C("onVoiceRecordEnd",e)},playVoice:function(e){k("playVoice",{localId:e.localId},e)},pauseVoice:function(e){k("pauseVoice",{localId:e.localId},e)},stopVoice:function(e){k("stopVoice",{localId:e.localId},e)},onVoicePlayEnd:function(e){C("onVoicePlayEnd",e)},uploadVoice:function(e){k("uploadVoice",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},downloadVoice:function(e){k("downloadVoice",{serverId:e.serverId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},translateVoice:function(e){k("translateVoice",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},chooseImage:function(e){k("chooseImage",{scene:"1|2",count:e.count||9,sizeType:e.sizeType||["original","compressed"],sourceType:e.sourceType||["album","camera"]},(e._complete=function(e){if(p){var t=e.localIds;try{t&&(e.localIds=JSON.parse(t))}catch(n){}}},e))},getLocation:function(e){},previewImage:function(e){k(o.previewImage,{current:e.current,urls:e.urls},e)},uploadImage:function(e){k("uploadImage",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},downloadImage:function(e){k("downloadImage",{serverId:e.serverId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},getLocalImgData:function(e){!1===_?(_=!0,k("getLocalImgData",{localId:e.localId},(e._complete=function(e){if(_=!1,0<w.length){var t=w.shift();wx.getLocalImgData(t)}},e))):w.push(e)},getNetworkType:function(e){k("getNetworkType",{},(e._complete=function(e){e=function(e){var t=e.errMsg;e.errMsg="getNetworkType:ok";var n=e.subtype;if(delete e.subtype,n)e.networkType=n;else{var o=t.indexOf(":"),r=t.substring(o+1);switch(r){case"wifi":case"edge":case"wwan":e.networkType=r;break;default:e.errMsg="getNetworkType:fail"}}return e}(e)},e))},openLocation:function(e){k("openLocation",{latitude:e.latitude,longitude:e.longitude,name:e.name||"",address:e.address||"",scale:e.scale||28,infoUrl:e.infoUrl||""},e)},getLocation:function(e){k(o.getLocation,{type:(e=e||{}).type||"wgs84"},(e._complete=function(e){delete e.type},e))},hideOptionMenu:function(e){k("hideOptionMenu",{},e)},showOptionMenu:function(e){k("showOptionMenu",{},e)},closeWindow:function(e){k("closeWindow",{},e=e||{})},hideMenuItems:function(e){k("hideMenuItems",{menuList:e.menuList},e)},showMenuItems:function(e){k("showMenuItems",{menuList:e.menuList},e)},hideAllNonBaseMenuItem:function(e){k("hideAllNonBaseMenuItem",{},e)},showAllNonBaseMenuItem:function(e){k("showAllNonBaseMenuItem",{},e)},scanQRCode:function(e){k("scanQRCode",{needResult:(e=e||{}).needResult||0,scanType:e.scanType||["qrCode","barCode"]},(e._complete=function(e){if(f){var t=e.resultStr;if(t){var n=JSON.parse(t);e.resultStr=n&&n.scan_code&&n.scan_code.scan_result}}},e))},openAddress:function(e){k(o.openAddress,{},(e._complete=function(e){var t;(t=e).postalCode=t.addressPostalCode,delete t.addressPostalCode,t.provinceName=t.proviceFirstStageName,delete t.proviceFirstStageName,t.cityName=t.addressCitySecondStageName,delete t.addressCitySecondStageName,t.countryName=t.addressCountiesThirdStageName,delete t.addressCountiesThirdStageName,t.detailInfo=t.addressDetailInfo,delete t.addressDetailInfo,e=t},e))},openProductSpecificView:function(e){k(o.openProductSpecificView,{pid:e.productId,view_type:e.viewType||0,ext_info:e.extInfo},e)},addCard:function(e){for(var t=e.cardList,n=[],r=0,i=t.length;r<i;++r){var a=t[r],s={card_id:a.cardId,card_ext:a.cardExt};n.push(s)}k(o.addCard,{card_list:n},(e._complete=function(e){var t=e.card_list;if(t){for(var n=0,o=(t=JSON.parse(t)).length;n<o;++n){var r=t[n];r.cardId=r.card_id,r.cardExt=r.card_ext,r.isSuccess=!!r.is_succ,delete r.card_id,delete r.card_ext,delete r.is_succ}e.cardList=t,delete e.card_list}},e))},chooseCard:function(e){k("chooseCard",{app_id:v.appId,location_id:e.shopId||"",sign_type:e.signType||"SHA1",card_id:e.cardId||"",card_type:e.cardType||"",card_sign:e.cardSign,time_stamp:e.timestamp+"",nonce_str:e.nonceStr},(e._complete=function(e){e.cardList=e.choose_card_info,delete e.choose_card_info},e))},openCard:function(e){for(var t=e.cardList,n=[],r=0,i=t.length;r<i;++r){var a=t[r],s={card_id:a.cardId,code:a.code};n.push(s)}k(o.openCard,{card_list:n},e)},consumeAndShareCard:function(e){k(o.consumeAndShareCard,{consumedCardId:e.cardId,consumedCode:e.code},e)},chooseWXPay:function(e){k(o.chooseWXPay,A(e),e)},openEnterpriseRedPacket:function(e){k(o.openEnterpriseRedPacket,A(e),e)},startSearchBeacons:function(e){k(o.startSearchBeacons,{ticket:e.ticket},e)},stopSearchBeacons:function(e){k(o.stopSearchBeacons,{},e)},onSearchBeacons:function(e){C(o.onSearchBeacons,e)},openEnterpriseChat:function(e){k("openEnterpriseChat",{useridlist:e.userIds,chatname:e.groupName},e)},launchMiniProgram:function(e){k("launchMiniProgram",{targetAppId:e.targetAppId,path:function(e){if("string"==typeof e&&0<e.length){var t=e.split("?")[0],n=e.split("?")[1];return t+=".html",void 0!==n?t+"?"+n:t}}(e.path),envVersion:e.envVersion},e)},openBusinessView:function(e){k("openBusinessView",{businessType:e.businessType,queryString:e.queryString||"",envVersion:e.envVersion},(e._complete=function(e){if(p){var t=e.extraData;if(t)try{e.extraData=JSON.parse(t)}catch(n){e.extraData={}}}},e))},miniProgram:{navigateBack:function(e){e=e||{},O((function(){k("invokeMiniProgramAPI",{name:"navigateBack",arg:{delta:e.delta||1}},e)}))},navigateTo:function(e){O((function(){k("invokeMiniProgramAPI",{name:"navigateTo",arg:{url:e.url}},e)}))},redirectTo:function(e){O((function(){k("invokeMiniProgramAPI",{name:"redirectTo",arg:{url:e.url}},e)}))},switchTab:function(e){O((function(){k("invokeMiniProgramAPI",{name:"switchTab",arg:{url:e.url}},e)}))},reLaunch:function(e){O((function(){k("invokeMiniProgramAPI",{name:"reLaunch",arg:{url:e.url}},e)}))},postMessage:function(e){O((function(){k("invokeMiniProgramAPI",{name:"postMessage",arg:e.data||{}},e)}))},getEnv:function(t){O((function(){t({miniprogram:"miniprogram"===e.__wxjs_environment})}))}}},T=1,S={};return i.addEventListener("error",(function(e){if(!p){var t=e.target,n=t.tagName,o=t.src;if(("IMG"==n||"VIDEO"==n||"AUDIO"==n||"SOURCE"==n)&&-1!=o.indexOf("wxlocalresource://")){e.preventDefault(),e.stopPropagation();var r=t["wx-id"];if(r||(r=T++,t["wx-id"]=r),S[r])return;S[r]=!0,wx.ready((function(){wx.getLocalImgData({localId:o,success:function(e){t.src=e.localData}})}))}}}),!0),i.addEventListener("load",(function(e){if(!p){var t=e.target,n=t.tagName;if(t.src,"IMG"==n||"VIDEO"==n||"AUDIO"==n||"SOURCE"==n){var o=t["wx-id"];o&&(S[o]=!1)}}}),!0),t&&(e.wx=e.jWeixin=x),x}function k(t,n,o){e.WeixinJSBridge?WeixinJSBridge.invoke(t,E(n),(function(e){M(t,e,o)})):I(t,o)}function C(t,n,o){e.WeixinJSBridge?WeixinJSBridge.on(t,(function(e){o&&o.trigger&&o.trigger(e),M(t,e,n)})):I(t,o||n)}function E(e){return(e=e||{}).appId=v.appId,e.verifyAppId=v.appId,e.verifySignType="sha1",e.verifyTimestamp=v.timestamp+"",e.verifyNonceStr=v.nonceStr,e.verifySignature=v.signature,e}function A(e){return{timeStamp:e.timestamp+"",nonceStr:e.nonceStr,package:e.package,paySign:e.paySign,signType:e.signType||"SHA1"}}function M(e,t,n){"openEnterpriseChat"!=e&&"openBusinessView"!==e||(t.errCode=t.err_code),delete t.err_code,delete t.err_desc,delete t.err_detail;var o=t.errMsg;o||(o=t.err_msg,delete t.err_msg,o=function(e,t){var n=e,o=r[n];o&&(n=o);var i="ok";if(t){var a=t.indexOf(":");"confirm"==(i=t.substring(a+1))&&(i="ok"),"failed"==i&&(i="fail"),-1!=i.indexOf("failed_")&&(i=i.substring(7)),-1!=i.indexOf("fail_")&&(i=i.substring(5)),"access denied"!=(i=(i=i.replace(/_/g," ")).toLowerCase())&&"no permission to execute"!=i||(i="permission denied"),"config"==n&&"function not exist"==i&&(i="ok"),""==i&&(i="fail")}return n+":"+i}(e,o),t.errMsg=o),(n=n||{})._complete&&(n._complete(t),delete n._complete),o=t.errMsg||"",v.debug&&!n.isInnerInvoke&&alert(JSON.stringify(t));var i=o.indexOf(":");switch(o.substring(i+1)){case"ok":n.success&&n.success(t);break;case"cancel":n.cancel&&n.cancel(t);break;default:n.fail&&n.fail(t)}n.complete&&n.complete(t)}function P(e){if(e){for(var t=0,n=e.length;t<n;++t){var r=e[t],i=o[r];i&&(e[t]=i)}return e}}function I(e,t){if(!(!v.debug||t&&t.isInnerInvoke)){var n=r[e];n&&(e=n),t&&t._complete&&delete t._complete,console.log('"'+e+'",',t||"")}}function L(){return(new Date).getTime()}function O(t){d&&(e.WeixinJSBridge?t():i.addEventListener&&i.addEventListener("WeixinJSBridgeReady",t,!1))}}(yC);var _C=bC.exports;const wC={data:[],current_page:1,last_page:1,per_page:15,total:0},xC=(e,t)=>{uni.showToast({title:e,icon:"success",mask:!0,duration:1500,success(){t&&t()}})},TC=(e,t)=>{uni.showModal({title:"友情提示",content:e,showCancel:!1,success(e){t&&t()}})},SC=(e,t=1500,n=!0)=>{uni.showToast({title:e,icon:"none",mask:n,duration:t})},kC=()=>["pages/index/index","pages/category/index","pages/cart/index","pages/user/index"],CC=(e,t,n)=>{let o=e;if(!eC(t)){o+="#/"+t;const e=EC(n);eC(e)||(o+="?"+e)}return o},EC=e=>Xk(AC(e)),AC=e=>({refereeId:iC.getters.userId,...e}),MC=(e,t={},n="navigateTo")=>{if(!e||0==e.length)return!1;if(Jk(e,["pages/index/index","pages/category/index","pages/cart/index","pages/user/index"]))return uni.switchTab({url:`/${e}`}),!0;const o=eC(t)?"":"?"+Xk(t);return"navigateTo"===n&&uni.navigateTo({url:`/${e}${o}`}),"redirectTo"===n&&uni.redirectTo({url:`/${e}${o}`}),!0},PC=()=>{const e=qm(),t=e[e.length-1].$page.fullPath.split("?");return{path:t[0].slice(1),query:Gk(t[1])}},IC=e=>{uni.setStorageSync("cartTotalNum",Number(e))},LC=()=>{const e=(e=>{const t=uni.getStorageSync("cartTotalNum")||0;return OC()?t:0})();e>0?uni.setTabBarBadge({index:2,text:`${e}`}):uni.removeTabBarBadge({index:2})},OC=()=>!!iC.getters.userId,$C=()=>tC(wC),DC=(e,t,n)=>(1==n&&(t.data=[]),t.data.concat(e.data)),RC=e=>{return n="scene",Kk(t=e)&&void 0!==t[n]?(e=>{if(void 0===e)return{};const t={},n=decodeURIComponent(e).split(",");for(const o in n){const e=n[o].split(":");e.length>0&&e[0]&&(t[e[0]]=e[1]||null)}return t})(e.scene):{};var t,n},BC=e=>Jk(e,iC.getters.modules),NC=e=>e.filter((e=>BC(e))).length>0,qC=e=>e.filter((e=>!e.moduleKey||BC(e.moduleKey))),jC=e=>!!e&&("PAGE"===e.type&&MC(e.param.path,e.param.query),"CUSTOM"===e.type&&MC(e.param.path,Gk(e.param.queryStr)),"URL"===e.type&&window.open(e.param.url),!0),zC="wxofficial/jssdkConfig",VC="wxofficial/oauthUrl",FC="wxofficial/oauthUserInfo",UC={jssdkConfig:e=>Dk.get(zC,{url:e}),oauthUrl:e=>Dk.get(VC,{callbackUrl:e}),oauthUserInfo:e=>Dk.get(FC,{code:e})};let WC=!1;const HC=async()=>{if(WC)return;const e=window.location.href.split("#")[0],t=await UC.jssdkConfig(e);_C.config(t.data.config),WC=!0,_C.error((e=>{console.error("jWeixin.error",e),TC(`微信链接分享配置错误：${e.errMsg}`)}))},YC=async()=>{const{path:e,query:t}=PC();return new Promise(((n,o)=>{oE.h5Url().then((o=>{const r=CC(o,e,t);n(r)}))}))},XC=async e=>{const t=await eE.isWxofficialLinkShareCard();cC&&t&&(e.link=await YC(),HC(),_C.ready((()=>{console.log("jWeixin.ready",e),_C.updateAppMessageShareData(e),_C.updateTimelineShareData(e)})))},GC=()=>new Promise(((e,t)=>{console.log("openAddress"),HC(),_C.ready((()=>{_C.openAddress({success(t){t.countyName=t.countryName,e(t)}})}))}));let JC=!1;const QC=()=>new Promise(((e,t)=>{oE.data().then((t=>{e(t.clientData.wxofficial.setting)}))})),KC=e=>new Promise(((t,n)=>{QC().then((n=>t(n[e])))})),ZC=async()=>await KC("share"),eE={data:QC,item:KC,isWxofficialLinkShareCard:()=>new Promise(((e,t)=>{KC("share").then((t=>e(Boolean(t.enabled))))})),updateShareData:async e=>{JC=!0;const t=await ZC(),n=Object.assign({},t,e);console.log("options",n),XC(n)},setGlobalShareCard:async(e=!1)=>{const t=await ZC();cC&&t.enabled&&(JC&&!e||XC(t))},openAddress:async(e=!1)=>await GC()},tE=e=>{(e=>{Tk.set("Store",e,600)})(e),vC.setStorage(e.setting),vC.setAppTheme(),cC&&eE.setGlobalShareCard(!1),iC.dispatch("SetModules",e.modules)},nE=(e=!0)=>new Promise(((t,n)=>{const o=Tk.get("Store");e&&o?t(o):new Promise(((e,t)=>{sC().then((t=>{tE(t.data),e(t.data)}))})).then((e=>{t(e)}))})),oE={data:nE,storeInfo:()=>new Promise(((e,t)=>{nE().then((t=>e(t.storeInfo)))})),h5Url:()=>new Promise(((e,t)=>{nE().then((t=>{const n=t.clientData.h5.setting.baseUrl;e(n)}))})),isEnabledDealer:()=>new Promise(((e,t)=>{nE().then((t=>{const n=Boolean(t.dealer.setting.is_open);e(n)}))}))},rE={globalData:{},onLaunch({path:e,query:t,scene:n}){this.onStartupQuery(Kk(t)?t:{}),this.getStoreInfo()},methods:{onStartupQuery(e){const t=RC(e),n=e.refereeId?e.refereeId:t.uid;n>0&&this.setRefereeId(n)},setRefereeId(e){iC.dispatch("setRefereeId",e)},getStoreInfo(){oE.data(!1)},updateManager(){const e=uni.getUpdateManager();e.onCheckForUpdate((e=>{})),e.onUpdateReady((()=>{uni.showModal({title:"更新提示",content:"新版本已经准备好，即将重启应用",showCancel:!1,success(t){t.confirm&&e.applyUpdate()}})})),e.onUpdateFailed((()=>{uni.showModal({title:"更新提示",content:"新版本下载失败",showCancel:!1})}))}}};function iE(){iC.commit("SET_STORE_ID",Ok.getStoreId()),iC.commit("SET_PLATFORM",lC),iC.commit("SET_TOKEN",Tk.get("AccessToken")),iC.commit("SET_USER_ID",Tk.get("userId")),iC.commit("SET_REFEREE_ID",Tk.get("refereeId")),iC.commit("SET_APP_THEME",Tk.get("appTheme")),iC.commit("SET_MODULES",Tk.get("modules"))}fv(rE,{init:dv,setup(e){const t=Tm(),n=()=>{var n;n=e,Object.keys(tf).forEach((e=>{tf[e].forEach((t=>{Bo(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i,onError:a}=e,s=function({path:e,query:t}){return c(_h,{path:e,query:t}),c(wh,_h),c({},_h)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:xe(t.query)});if(o&&$(o,s),r&&$(r,s),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};i&&$(i,e)}a&&(e.appContext.config.errorHandler=e=>{$(a,e)})};return _r(Fs).isReady().then(n),jo((()=>{window.addEventListener("resize",ke(gv,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",mv),document.addEventListener("visibilitychange",vv),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{Cx.emit("onThemeChange",{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(Hr(),Qr(Sw));e.setup=(e,o)=>{const r=t&&t(e,o);return m(r)?n:r},e.render=n}});const aE={data:()=>({platform:lC}),computed:{appTheme:()=>iC.getters.appTheme,appThemeStyle:()=>(e=>{let t="";for(const n in e)t+=`--${n.replace(/([A-Z])/g,"-$1").toLowerCase()}:${e[n]};`;return t})(iC.getters.appTheme)},mounted(){"WXOFFICIAL"===this.platform&&this.hideNavigationBar()},methods:{hideNavigationBar(){this.$nextTick((()=>{const e=document.getElementsByTagName("uni-page-head");e.length&&(e[0].style.display="none",document.body.style.setProperty("--window-top","0px"))}))}}},sE={data:()=>({}),onLoad(){this.$u.getRect=this.$uGetRect},methods:{$uGetRect(e,t){return new Promise((n=>{uni.createSelectorQuery().in(this)[t?"selectAll":"select"](e).boundingClientRect((e=>{t&&Array.isArray(e)&&e.length&&n(e),!t&&e&&n(e)})).exec()}))},getParentData(e=""){this.parent||(this.parent=!1),this.parent=this.$u.$parent.call(this,e),this.parent&&(Object.keys(this.parentData).map((e=>{this.parentData[e]=this.parent[e]})),this.parentData.value=this.parent.modelValue)},preventEvent(e){e&&e.stopPropagation&&e.stopPropagation()}},onReachBottom(){uni.$emit("uOnReachBottom")},beforeUnmount(){if(this.parent&&uni.$u.test.array(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}}};function lE(e){if([null,void 0,NaN,!1].includes(e))return e;if("object"!=typeof e&&"function"!=typeof e)return e;var t,n=(t=e,"[object Array]"===Object.prototype.toString.call(t)?[]:{});for(let o in e)e.hasOwnProperty(o)&&(n[o]="object"==typeof e[o]?lE(e[o]):e[o]);return n}function cE(e={},t={}){if("object"!=typeof(e=lE(e))||"object"!=typeof t)return!1;for(var n in t)t.hasOwnProperty(n)&&(n in e?"object"!=typeof e[n]||"object"!=typeof t[n]?e[n]=t[n]:e[n].concat&&t[n].concat?e[n]=e[n].concat(t[n]):e[n]=cE(e[n],t[n]):e[n]=t[n]);return e}function uE(e){switch(typeof e){case"undefined":return!0;case"string":if(0==e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(0===e||isNaN(e))return!0;break;case"object":if(null===e||0===e.length)return!0;for(var t in e)return!1;return!0}return!1}const dE={email:function(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)},mobile:function(e){return/^1[23456789]\d{9}$/.test(e)},url:function(e){return/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w-.\/?%&=]*)?/.test(e)},date:function(e){return!/Invalid|NaN/.test(new Date(e).toString())},dateISO:function(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},number:function(e){return/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(e)},digits:function(e){return/^\d+$/.test(e)},idCard:function(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)},carNo:function(e){const t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===e.length?n.test(e):8===e.length&&t.test(e)},amount:function(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)},chinese:function(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)},letter:function(e){return/^[a-zA-Z]*$/.test(e)},enOrNum:function(e){return/^[0-9a-zA-Z]*$/g.test(e)},contains:function(e,t){return e.indexOf(t)>=0},range:function(e,t){return e>=t[0]&&e<=t[1]},rangeLength:function(e,t){return e.length>=t[0]&&e.length<=t[1]},empty:uE,isEmpty:uE,jsonString:function(e){if("string"==typeof e)try{var t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(yC){return!1}return!1},landline:function(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)},object:function(e){return"[object Object]"===Object.prototype.toString.call(e)},array:function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)},code:function(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)}};const pE=new class{setConfig(e){this.config=cE(this.config,e)}request(e={}){if(this.interceptor.request&&"function"==typeof this.interceptor.request){let t=this.interceptor.request(e);if(!1===t)return new Promise((()=>{}));this.options=t}return e.dataType=e.dataType||this.config.dataType,e.responseType=e.responseType||this.config.responseType,e.url=e.url||"",e.params=e.params||{},e.header=Object.assign({},this.config.header,e.header),e.method=e.method||this.config.method,new Promise(((t,n)=>{e.complete=e=>{if(uni.hideLoading(),clearTimeout(this.config.timer),this.config.timer=null,this.config.originalData)if(this.interceptor.response&&"function"==typeof this.interceptor.response){let o=this.interceptor.response(e);!1!==o?t(o):n(e)}else t(e);else if(200==e.statusCode)if(this.interceptor.response&&"function"==typeof this.interceptor.response){let o=this.interceptor.response(e.data);!1!==o?t(o):n(e.data)}else t(e.data);else n(e)},e.url=dE.url(e.url)?e.url:this.config.baseUrl+(0==e.url.indexOf("/")?e.url:"/"+e.url),this.config.showLoading&&!this.config.timer&&(this.config.timer=setTimeout((()=>{uni.showLoading({title:this.config.loadingText,mask:this.config.loadingMask}),this.config.timer=null}),this.config.loadingTime)),uni.request(e)}))}constructor(){this.config={baseUrl:"",header:{},method:"POST",dataType:"json",responseType:"text",showLoading:!0,loadingText:"请求中...",loadingTime:800,timer:null,originalData:!1,loadingMask:!0},this.interceptor={request:null,response:null},this.get=(e,t={},n={})=>this.request({method:"GET",url:e,header:n,data:t}),this.post=(e,t={},n={})=>this.request({url:e,method:"POST",header:n,data:t}),this.put=(e,t={},n={})=>this.request({url:e,method:"PUT",header:n,data:t}),this.delete=(e,t={},n={})=>this.request({url:e,method:"DELETE",header:n,data:t})}};const fE=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,t){e=e&&this.addRootPath(e);let n="";return/.*\/.*\?.*=.*/.test(e)?(n=uni.$u.queryParams(t,!1),e+"&"+n):(n=uni.$u.queryParams(t),e+n)}async route(e={},t={}){let n={};if("string"==typeof e?(n.url=this.mixinParam(e,t),n.type="navigateTo"):(n=uni.$u.deepClone(e,this.config),n.url=this.mixinParam(e.url,e.params)),t.intercept&&(this.config.intercept=t.intercept),n.params=t,n=uni.$u.deepMerge(this.config,n),"function"==typeof uni.$u.routeIntercept){await new Promise(((e,t)=>{uni.$u.routeIntercept(n,e)}))&&this.openPage(n)}else this.openPage(n)}openPage(e){const{url:t,type:n,delta:o,animationType:r,animationDuration:i}=e;"navigateTo"!=e.type&&"to"!=e.type||uni.navigateTo({url:t,animationType:r,animationDuration:i}),"redirectTo"!=e.type&&"redirect"!=e.type||uni.redirectTo({url:t}),"switchTab"!=e.type&&"tab"!=e.type||uni.switchTab({url:t}),"reLaunch"!=e.type&&"launch"!=e.type||uni.reLaunch({url:t}),"navigateBack"!=e.type&&"back"!=e.type||uni.navigateBack({delta:o})}}).route;function hE(e=null,t="yyyy-mm-dd"){e||(e=Number(new Date)),10==e.toString().length&&(e*=1e3);let n,o=new Date(e),r={"y+":o.getFullYear().toString(),"m+":(o.getMonth()+1).toString(),"d+":o.getDate().toString(),"h+":o.getHours().toString(),"M+":o.getMinutes().toString(),"s+":o.getSeconds().toString()};for(let i in r)n=new RegExp("("+i+")").exec(t),n&&(t=t.replace(n[1],1==n[1].length?r[i]:r[i].padStart(n[1].length,"0")));return t}function gE(e,t=!0){if((e=e.toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e)){if(4===e.length){let t="#";for(let n=1;n<4;n+=1)t+=e.slice(n,n+1).concat(e.slice(n,n+1));e=t}let n=[];for(let t=1;t<7;t+=2)n.push(parseInt("0x"+e.slice(t,t+2)));return t?`rgb(${n[0]},${n[1]},${n[2]})`:n}if(/^(rgb|RGB)/.test(e)){return e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map((e=>Number(e)))}return e}function mE(e){let t=e;if(/^(rgb|RGB)/.test(t)){let e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(","),n="#";for(let t=0;t<e.length;t++){let o=Number(e[t]).toString(16);o=1==String(o).length?"0"+o:o,"0"===o&&(o+=o),n+=o}return 7!==n.length&&(n=t),n}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t))return t;{let e=t.replace(/#/,"").split("");if(6===e.length)return t;if(3===e.length){let t="#";for(let n=0;n<e.length;n+=1)t+=e[n]+e[n];return t}}}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if("[object String]"!==Object.prototype.toString.call(t))throw new TypeError("fillString must be String");let n=this;if(n.length>=e)return String(n);let o=e-n.length,r=Math.ceil(o/t.length);for(;r>>=1;)t+=t,1===r&&(t+=t);return t.slice(0,o)+n});const vE={colorGradient:function(e="rgb(0, 0, 0)",t="rgb(255, 255, 255)",n=10){let o=gE(e,!1),r=o[0],i=o[1],a=o[2],s=gE(t,!1),l=(s[0]-r)/n,c=(s[1]-i)/n,u=(s[2]-a)/n,d=[];for(let p=0;p<n;p++){let e=mE("rgb("+Math.round(l*p+r)+","+Math.round(c*p+i)+","+Math.round(u*p+a)+")");d.push(e)}return d},hexToRgb:gE,rgbToHex:mE,colorToRgba:function(e,t=.3){let n=(e=mE(e)).toLowerCase();if(n&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(n)){if(4===n.length){var o="#";for(let e=1;e<4;e+=1)o+=n.slice(e,e+1).concat(n.slice(e,e+1));n=o}var r=[];for(let e=1;e<7;e+=2)r.push(parseInt("0x"+n.slice(e,e+2)));return"rgba("+r.join(",")+","+t+")"}return n}};let yE=null;let bE=[],_E=[];const wE={v:"1.10.1",version:"1.10.1",type:["primary","success","info","error","warning"]};const xE={queryParams:function(e={},t=!0,n="brackets"){let o=t?"?":"",r=[];-1==["indices","brackets","repeat","comma"].indexOf(n)&&(n="brackets");for(let i in e){let t=e[i];if(!(["",void 0,null].indexOf(t)>=0))if(t.constructor===Array)switch(n){case"indices":for(let n=0;n<t.length;n++)r.push(i+"["+n+"]="+t[n]);break;case"brackets":default:t.forEach((e=>{r.push(i+"[]="+e)}));break;case"repeat":t.forEach((e=>{r.push(i+"="+e)}));break;case"comma":let e="";t.forEach((t=>{e+=(e?",":"")+t})),r.push(i+"="+e)}else r.push(i+"="+t)}return r.length?o+r.join("&"):""},route:fE,timeFormat:hE,date:hE,timeFrom:function(e=null,t="yyyy-mm-dd"){e||(e=Number(new Date)),10==e.toString().length&&(e*=1e3);let n=+new Date(Number(e)),o=(Number(new Date)-n)/1e3,r="";switch(!0){case o<300:r="刚刚";break;case o>=300&&o<3600:r=parseInt(o/60)+"分钟前";break;case o>=3600&&o<86400:r=parseInt(o/3600)+"小时前";break;case o>=86400&&o<2592e3:r=parseInt(o/86400)+"天前";break;default:r=!1===t?o>=2592e3&&o<31536e3?parseInt(o/2592e3)+"个月前":parseInt(o/31536e3)+"年前":hE(n,t)}return r},colorGradient:vE.colorGradient,colorToRgba:vE.colorToRgba,guid:function(e=32,t=!0,n=null){let o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),r=[];if(n=n||o.length,e)for(let i=0;i<e;i++)r[i]=o[0|Math.random()*n];else{let e;r[8]=r[13]=r[18]=r[23]="-",r[14]="4";for(let t=0;t<36;t++)r[t]||(e=0|16*Math.random(),r[t]=o[19==t?3&e|8:e])}return t?(r.shift(),"u"+r.join("")):r.join("")},color:{primary:"#2979ff",primaryDark:"#2b85e4",primaryDisabled:"#a0cfff",primaryLight:"#ecf5ff",bgColor:"#f3f4f6",info:"#909399",infoDark:"#82848a",infoDisabled:"#c8c9cc",infoLight:"#f4f4f5",warning:"#ff9900",warningDark:"#f29100",warningDisabled:"#fcbd71",warningLight:"#fdf6ec",error:"#fa3534",errorDark:"#dd6161",errorDisabled:"#fab6b6",errorLight:"#fef0f0",success:"#19be6b",successDark:"#18b566",successDisabled:"#71d5a1",successLight:"#dbf1e1",mainColor:"#303133",contentColor:"#606266",tipsColor:"#909399",lightColor:"#c0c4cc",borderColor:"#e4e7ed"},sys:function(){return uni.getSystemInfoSync()},os:function(){return uni.getSystemInfoSync().platform},type2icon:function(e="success",t=!1){-1==["primary","info","error","warning","success"].indexOf(e)&&(e="success");let n="";switch(e){case"primary":case"info":n="info-circle";break;case"error":n="close-circle";break;case"warning":n="error-circle";break;default:n="checkmark-circle"}return t&&(n+="-fill"),n},randomArray:function(e=[]){return e.sort((()=>Math.random()-.5))},wranning:function(e){},get:pE.get,post:pE.post,put:pE.put,delete:pE.delete,hexToRgb:vE.hexToRgb,rgbToHex:vE.rgbToHex,test:dE,random:function(e,t){if(e>=0&&t>0&&t>=e){let n=t-e+1;return Math.floor(Math.random()*n+e)}return 0},deepClone:lE,deepMerge:cE,getParent:function(e,t){let n=this.$parent;for(;n;){if(n.$options.name===e){let e={};if(Array.isArray(t))t.map((t=>{e[t]=n[t]?n[t]:""}));else for(let o in t)Array.isArray(t[o])?t[o].length?e[o]=t[o]:e[o]=n[o]:t[o].constructor===Object?Object.keys(t[o]).length?e[o]=t[o]:e[o]=n[o]:e[o]=t[o]||!1===t[o]?t[o]:n[o];return e}n=n.$parent}return{}},$parent:function(e){let t=this.$parent;for(;t;){if(!t.$options||t.$options.name===e)return t;t=t.$parent}return!1},addUnit:function(e="auto",t="rpx"){return e=String(e),dE.number(e)?`${e}${t}`:e},trim:function(e,t="both"){return"both"==t?e.replace(/^\s+|\s+$/g,""):"left"==t?e.replace(/^\s*/,""):"right"==t?e.replace(/(\s*$)/g,""):"all"==t?e.replace(/\s+/g,""):e},type:["primary","success","error","warning","info"],http:pE,toast:function(e,t=1500){uni.showToast({title:e,icon:"none",duration:t})},config:wE,zIndex:{toast:10090,noNetwork:10080,popup:10075,mask:10070,navbar:980,topTips:975,sticky:970,indexListSticky:965},debounce:function(e,t=500,n=!1){if(null!==yE&&clearTimeout(yE),n){var o=!yE;yE=setTimeout((function(){yE=null}),t),o&&"function"==typeof e&&e()}else yE=setTimeout((function(){"function"==typeof e&&e()}),t)},throttle:function(e,t=500,n=!0,o="default"){bE[o]||(bE[o]=null),n?_E[o]||(_E[o]=!0,"function"==typeof e&&e(),bE[o]=setTimeout((()=>{_E[o]=!1}),t)):_E[o]||(_E[o]=!0,bE[o]=setTimeout((()=>{_E[o]=!1,"function"==typeof e&&e()}),t))}};uni.$u=xE;const TE={install:e=>{e.mixin(sE),e.config.globalProperties.$u=xE}};(function(){const e=Sa({...rE,store:iC,created:iE});return e.config.globalProperties.$toast=SC,e.config.globalProperties.$success=xC,e.config.globalProperties.$error=TC,e.config.globalProperties.$navTo=MC,e.config.globalProperties.$getShareUrlParams=EC,e.config.globalProperties.$checkModule=BC,e.config.globalProperties.$checkModules=NC,e.use(TE),e.mixin(aE),{app:e}})().app.use(nv).mount("#app");export{Dk as $,pC as A,Jo as B,Jk as C,OC as D,dC as E,jr as F,oC as G,nC as H,ag as I,qC as J,iC as K,Qg as L,Gg as M,Tk as N,PC as O,jC as P,wv as Q,rC as R,oE as S,oi as T,tC as U,eC as V,qm as W,_u as X,Wk as Y,Xk as Z,UC as _,ri as a,kC as a0,RC as a1,eE as a2,qg as a3,Ig as a4,rm as a5,mu as a6,Zk as a7,Kk as a8,Qk as a9,Ug as aa,Yk as ab,cC as ac,Ok as ad,CC as ae,xx as af,AC as ag,si as b,Qr as c,Jr as d,Go as e,ai as f,jh as g,wa as h,im as i,Yg as j,se as k,em as l,IC as m,ae as n,Hr as o,lC as p,$C as q,Hn as r,LC as s,Y as t,DC as u,Xn as v,zn as w,ao as x,Gi as y,vC as z};
