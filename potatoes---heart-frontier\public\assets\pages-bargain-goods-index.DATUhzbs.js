import{r as e,o as a,c as s,z as t,a1 as o,v as l,x as i,y as d,w as n,n as c,i as u,b as r,a as f,f as m,t as p,d as g,e as _,F as h,l as k,X as x,j as S}from"./index-Bo6hY7ZC.js";import{_ as v}from"./mp-html.DG3qE9GO.js";import{r as y}from"./uni-app.es.DtaL5fPi.js";import{_ as C}from"./u-modal.6LKX92cH.js";import{W as w}from"./wxofficial.CKoGItRK.js";import{S as b,a as I,C as P}from"./Comment.BLJxH3Sb.js";import{C as T}from"./index.CqUd2cYd.js";import{h as B}from"./color.D-c1b2x3.js";import{p as j,b as L,d as V}from"./task.CuYOfIfl.js";import{G as N}from"./index.By37mpu2.js";import{_ as A}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{C as R}from"./index.BS2D6QL7.js";import{d as D}from"./index.CN8oc61w.js";import{t as M}from"./cart.B5hmo82F.js";import"./u-loading.CaILF3tN.js";import"./u-popup.qw0t8K2V.js";import"./u-mask.CGKhbF2S.js";import"./u-icon.CShoJDMR.js";import"./comment.B7BZICMf.js";import"./index.D7I1Gtis.js";const U=A({components:{ShareSheet:b,CustomerBtn:T,SlideImage:I,SkuPopup:A({components:{GoodsSkuPopup:N},emits:["update:modelValue"],props:{modelValue:{Type:Boolean,default:!1},skuMode:{type:Number,default:1},active:{type:Object,default:{}},goods:{type:Object,default:{}}},data:()=>({goodsInfo:{}}),computed:{activedBtnBackgroundColor(){return B(this.appTheme.mainBg,.1)}},created(){const e=this,{goods:a}=e;e.goodsInfo={_id:a.goods_id,name:a.goods_name,goods_thumb:a.goods_image,sku_list:e.getSkuList(),spec_list:e.getSpecList()}},methods:{onChangeValue(e){this.$emit("update:modelValue",e)},getSkuList(){const e=this,{goods:{goods_name:a,goods_image:s,skuList:t}}=e,o=[];return t.forEach((t=>{o.push({_id:t.id,goods_sku_id:t.goods_sku_id,goods_id:t.goods_id,goods_name:a,image:t.image_url?t.image_url:s,price:100*t.goods_price,stock:t.stock_num,spec_value_ids:t.spec_value_ids,sku_name_arr:e.getSkuNameArr(t.spec_value_ids)})})),o},getSkuNameArr(e){const a=this,s=[];return e&&e.forEach(((e,t)=>{const o=a.getSpecValueName(e,t);s.push(o)})),s.length?s:["默认"]},getSpecValueName(e,a){const{goods:{specList:s}}=this;return s[a].valueList.find((a=>a.spec_value_id==e)).spec_value},getSpecList(){const{goods:{specList:e}}=this,a=[];return e.forEach((e=>{const s=[];e.valueList.forEach((e=>{s.push({name:e.spec_value})})),a.push({name:e.spec_name,list:s})})),a.length?a:[{name:"默认",list:[{name:"默认"}]}]},openSkuPopup(){},closeSkuPopup(){},buyNow(e){j({activeId:this.active.active_id,goodsSkuId:e.goods_sku_id}).then((e=>{const a=e.data.taskId;this.$navTo("pages/bargain/task",{taskId:a})})),this.onChangeValue(!1)}}},[["render",function(t,o,l,i,d,n){const c=e("goods-sku-popup");return a(),s(c,{modelValue:l.modelValue,onInput:n.onChangeValue,"border-radius":"20",localdata:d.goodsInfo,mode:l.skuMode,maskCloseAble:!0,priceColor:t.appTheme.mainBg,buyNowBackgroundColor:t.appTheme.mainBg,addCartColor:t.appTheme.viceText,addCartBackgroundColor:t.appTheme.viceBg,activedStyle:{color:t.appTheme.mainBg,borderColor:t.appTheme.mainBg,backgroundColor:n.activedBtnBackgroundColor},onOpen:n.openSkuPopup,onClose:n.closeSkuPopup,onBuyNow:n.buyNow,buyNowText:"立即砍价",maxBuyNum:1},null,8,["modelValue","onInput","localdata","mode","priceColor","buyNowBackgroundColor","addCartColor","addCartBackgroundColor","activedStyle","onOpen","onClose","onBuyNow"])}]]),Comment:P,CountDown:R},mixins:[w],data:()=>({isLoading:!0,showSkuPopup:!1,skuMode:3,showShareSheet:!1,showRules:!1,posterApiCall:L,activeId:null,goodsId:null,active:{},goods:{},setting:null,isPartake:null,taskId:null,cartTotal:0,isShowCustomerBtn:!1}),computed:{pagePath(){return`/pages/bargain/goods/index?${this.$getShareUrlParams({activeId:this.activeId,goodsId:this.goodsId})}`}},async onLoad(e){this.onRecordQuery(e),this.onRefreshPage(),this.isShowCustomerBtn=await t.isShowCustomerBtn()},methods:{onRecordQuery(e){const a=o(e);this.activeId=e.activeId?parseInt(e.activeId):parseInt(a.aid),this.goodsId=e.goodsId?parseInt(e.goodsId):parseInt(a.gid)},onRefreshPage(){const e=this;e.isLoading=!0,Promise.all([e.getActiveDetail(),e.getGoodsDetail(),e.getCartTotal()]).then((()=>e.setWxofficialShareData())).finally((()=>e.isLoading=!1))},getActiveDetail(){const e=this;return new Promise(((a,s)=>{V(e.activeId).then((s=>{e.active=s.data.active,e.setting=s.data.setting,e.isPartake=s.data.isPartake,e.taskId=s.data.taskId,a(s)})).catch(s)}))},getGoodsDetail(){const e=this;return new Promise(((a,s)=>{D(e.goodsId,!1).then((s=>{e.goods=s.data.detail,a(s)})).catch(s)}))},getCartTotal(){const e=this;return new Promise(((a,s)=>{M().then((s=>{e.cartTotal=s.data.cartTotal,a(s)})).catch(s)}))},onShowSkuPopup(){this.showSkuPopup=!this.showSkuPopup},onShowShareSheet(){this.showShareSheet=!this.showShareSheet},handleShowRules(){this.showRules=!0},onTargetHome(e){this.$navTo("pages/index/index")},onTargetCart(){this.$navTo("pages/cart/index")},handleMainBtn(){const e=this;if(!e.isPartake)return e.onShowSkuPopup();e.$navTo("pages/bargain/task",{taskId:e.taskId})},setWxofficialShareData(){const{goods:e}=this;this.updateShareCardData({title:e.goods_name,desc:e.selling_point,imgUrl:e.goods_image})}},onShareAppMessage(){return{title:this.goods.goods_name,path:this.pagePath}},onShareTimeline(){return{title:this.goods.goods_name,path:this.pagePath}}},[["render",function(t,o,w,b,I,P){const T=e("SlideImage"),B=k,j=u,L=x,V=e("count-down"),N=e("SkuPopup"),A=e("Comment"),R=y(l("mp-html"),v),D=e("customer-btn"),M=e("share-sheet"),U=S,$=y(l("u-modal"),C);return i((a(),s(j,{class:"container",style:c(t.appThemeStyle)},{default:n((()=>[I.isLoading?r("",!0):(a(),s(T,{key:0,video:I.goods.video,videoCover:I.goods.videoCover,images:I.goods.goods_images},null,8,["video","videoCover","images"])),I.isLoading?r("",!0):(a(),s(j,{key:1,class:"goods-info m-top20"},{default:n((()=>[f(j,{class:"info-item info-item__top dis-flex flex-x-between flex-y-end"},{default:n((()=>[f(j,{class:"block-left dis-flex flex-y-center"},{default:n((()=>[f(j,{class:"active-tag"},{default:n((()=>[f(B,null,{default:n((()=>[m("限时砍价")])),_:1})])),_:1}),f(B,{class:"floor-price__samll"},{default:n((()=>[m("￥")])),_:1}),f(B,{class:"floor-price"},{default:n((()=>[m(p(I.active.floor_price),1)])),_:1}),f(B,{class:"original-price"},{default:n((()=>[m("￥"+p(I.goods.goods_price_min),1)])),_:1})])),_:1}),f(j,{class:"block-right dis-flex"},{default:n((()=>[f(j,{class:"goods-sales"},{default:n((()=>[f(B,null,{default:n((()=>[m("已砍成"+p(I.active.active_sales)+"件",1)])),_:1})])),_:1})])),_:1})])),_:1}),f(j,{class:"info-item info-item__name dis-flex flex-y-center"},{default:n((()=>[f(j,{class:"goods-name flex-box"},{default:n((()=>[f(B,{class:"twoline-hide"},{default:n((()=>[m(p(I.goods.goods_name),1)])),_:1})])),_:1}),f(j,{class:"goods-share__line"}),f(j,{class:"goods-share"},{default:n((()=>[f(L,{class:"share-btn dis-flex flex-dir-column",onClick:o[0]||(o[0]=e=>P.onShowShareSheet())},{default:n((()=>[f(B,{class:"share__icon iconfont icon-fenxiang"}),f(B,{class:"f-24"},{default:n((()=>[m("分享")])),_:1})])),_:1})])),_:1})])),_:1}),I.goods.selling_point?(a(),s(j,{key:0,class:"info-item info-item_selling-point"},{default:n((()=>[f(B,null,{default:n((()=>[m(p(I.goods.selling_point),1)])),_:1})])),_:1})):r("",!0),0==I.active.is_end?(a(),s(j,{key:1,class:"info-item info-item_status info-item_countdown dis-flex flex-y-center"},{default:n((()=>[f(B,{class:"countdown-icon iconfont icon-naozhong"}),f(B,null,{default:n((()=>[m("距离活动结束")])),_:1}),f(B,{class:"m-r-10"},{default:n((()=>[m("还剩")])),_:1}),f(V,{date:I.active.end_time,separator:"zh",theme:"text"},null,8,["date"])])),_:1})):r("",!0),1==I.active.is_end?(a(),s(j,{key:2,class:"info-item info-item_status info-item_end"},{default:n((()=>[f(B,{class:"countdown-icon iconfont icon-naozhong"}),f(B,null,{default:n((()=>[m("砍价活动已结束，下次记得早点来哦~")])),_:1})])),_:1})):r("",!0)])),_:1})),f(j,{class:"bargain-rules m-top20 b-f",onClick:o[1]||(o[1]=e=>P.handleShowRules())},{default:n((()=>[f(j,{class:"item-title dis-flex"},{default:n((()=>[f(j,{class:"block-left flex-box"},{default:n((()=>[f(B,null,{default:n((()=>[m("砍价玩法")])),_:1})])),_:1}),f(j,{class:"block-right"},{default:n((()=>[f(B,{class:"show-more col-9"},{default:n((()=>[m("查看规则")])),_:1})])),_:1})])),_:1}),f(j,{class:"rule-simple dis-flex flex-x-around"},{default:n((()=>[f(j,{class:"simple-item dis-flex flex-dir-column flex-y-center"},{default:n((()=>[f(j,{class:"i-number dis-flex flex-x-center flex-y-center"},{default:n((()=>[f(B,{class:"f-30"},{default:n((()=>[m("1")])),_:1})])),_:1}),f(j,{class:"i-text f-28"},{default:n((()=>[m("点击砍价")])),_:1})])),_:1}),f(j,{class:"simple-item dis-flex flex-dir-column flex-y-center"},{default:n((()=>[f(j,{class:"i-number dis-flex flex-x-center flex-y-center"},{default:n((()=>[f(B,{class:"f-30"},{default:n((()=>[m("2")])),_:1})])),_:1}),f(j,{class:"i-text f-28"},{default:n((()=>[m("找人帮砍")])),_:1})])),_:1}),f(j,{class:"simple-item dis-flex flex-dir-column flex-y-center"},{default:n((()=>[f(j,{class:"i-number dis-flex flex-x-center flex-y-center"},{default:n((()=>[f(B,{class:"f-30"},{default:n((()=>[m("3")])),_:1})])),_:1}),f(j,{class:"i-text f-28"},{default:n((()=>[m("砍到最低")])),_:1})])),_:1}),f(j,{class:"simple-item dis-flex flex-dir-column flex-y-center"},{default:n((()=>[f(j,{class:"i-number dis-flex flex-x-center flex-y-center"},{default:n((()=>[f(B,{class:"f-30"},{default:n((()=>[m("4")])),_:1})])),_:1}),f(j,{class:"i-text f-28"},{default:n((()=>[m("优惠购买")])),_:1})])),_:1})])),_:1})])),_:1}),20==I.goods.spec_type?(a(),s(j,{key:2,class:"goods-choice m-top20 b-f",onClick:o[2]||(o[2]=e=>P.onShowSkuPopup())},{default:n((()=>[f(j,{class:"spec-list"},{default:n((()=>[f(j,{class:"flex-box"},{default:n((()=>[f(B,{class:"col-8"},{default:n((()=>[m("选择：")])),_:1}),(a(!0),g(h,null,_(I.goods.specList,((e,t)=>(a(),s(B,{class:"spec-name",key:t},{default:n((()=>[m(p(e.spec_name),1)])),_:2},1024)))),128))])),_:1}),f(j,{class:"f-26 col-9 t-r"},{default:n((()=>[f(B,{class:"iconfont icon-arrow-right"})])),_:1})])),_:1})])),_:1})):r("",!0),I.isLoading?r("",!0):(a(),s(N,{key:3,modelValue:I.showSkuPopup,"onUpdate:modelValue":o[3]||(o[3]=e=>I.showSkuPopup=e),skuMode:I.skuMode,active:I.active,goods:I.goods},null,8,["modelValue","skuMode","active","goods"])),I.isLoading?r("",!0):(a(),s(A,{key:4,"goods-id":I.goodsId,limit:2},null,8,["goods-id"])),I.isLoading?r("",!0):(a(),s(j,{key:5,class:"goods-content m-top20"},{default:n((()=>[f(j,{class:"item-title b-f"},{default:n((()=>[f(B,null,{default:n((()=>[m("商品描述")])),_:1})])),_:1}),""!=I.goods.content?(a(),s(j,{key:0,class:"goods-content__detail b-f"},{default:n((()=>[f(R,{content:I.goods.content},null,8,["content"])])),_:1})):r("",!0)])),_:1})),f(j,{class:"footer-fixed"},{default:n((()=>[f(j,{class:"footer-container"},{default:n((()=>[f(j,{class:"foo-item-fast"},{default:n((()=>[f(j,{class:"fast-item fast-item--home",onClick:P.onTargetHome},{default:n((()=>[f(j,{class:"fast-icon"},{default:n((()=>[f(B,{class:"iconfont icon-shouye"})])),_:1}),f(j,{class:"fast-text"},{default:n((()=>[f(B,null,{default:n((()=>[m("首页")])),_:1})])),_:1})])),_:1},8,["onClick"]),I.isShowCustomerBtn?(a(),s(D,{key:0,showCard:!0,cardTitle:I.goods.goods_name,cardImage:I.goods.goods_image,cardPath:P.pagePath},{default:n((()=>[f(j,{class:"fast-item"},{default:n((()=>[f(j,{class:"fast-icon"},{default:n((()=>[f(B,{class:"iconfont icon-kefu1"})])),_:1}),f(j,{class:"fast-text"},{default:n((()=>[f(B,null,{default:n((()=>[m("客服")])),_:1})])),_:1})])),_:1})])),_:1},8,["cardTitle","cardImage","cardPath"])):r("",!0),I.isShowCustomerBtn?r("",!0):(a(),s(j,{key:1,class:"fast-item fast-item--cart",onClick:P.onTargetCart},{default:n((()=>[I.cartTotal>0?(a(),s(j,{key:0,class:"fast-badge fast-badge--fixed"},{default:n((()=>[m(p(I.cartTotal>99?"99+":I.cartTotal),1)])),_:1})):r("",!0),f(j,{class:"fast-icon"},{default:n((()=>[f(B,{class:"iconfont icon-gouwuche"})])),_:1}),f(j,{class:"fast-text"},{default:n((()=>[f(B,null,{default:n((()=>[m("购物车")])),_:1})])),_:1})])),_:1},8,["onClick"]))])),_:1}),f(j,{class:"foo-item-btn"},{default:n((()=>[f(j,{class:"btn-wrapper"},{default:n((()=>[I.active.is_start&&!I.active.is_end?(a(),s(j,{key:0,class:"btn-item btn--main",onClick:o[4]||(o[4]=e=>P.handleMainBtn(3))},{default:n((()=>[f(B,null,{default:n((()=>[m(p(I.isPartake?"继续砍价":"立即砍价"),1)])),_:1})])),_:1})):(a(),s(L,{key:1,class:"btn-item btn--gray"},{default:n((()=>[f(B,null,{default:n((()=>[m(p(I.active.is_end?"活动已结束":"活动未开启"),1)])),_:1})])),_:1}))])),_:1})])),_:1})])),_:1})])),_:1}),f(M,{modelValue:I.showShareSheet,"onUpdate:modelValue":o[5]||(o[5]=e=>I.showShareSheet=e),shareTitle:I.goods.goods_name,shareImageUrl:I.goods.goods_image,posterApiCall:I.posterApiCall,posterApiParam:{activeId:I.activeId}},null,8,["modelValue","shareTitle","shareImageUrl","posterApiCall","posterApiParam"]),I.isLoading?r("",!0):(a(),s($,{key:6,modelValue:I.showRules,"onUpdate:modelValue":o[6]||(o[6]=e=>I.showRules=e),title:"砍价规则"},{default:n((()=>[f(U,{style:{height:"610rpx"},"scroll-y":!0},{default:n((()=>[f(j,{class:"pops-content"},{default:n((()=>[f(B,null,{default:n((()=>[m(p(I.setting.rulesDesc),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"]))])),_:1},8,["style"])),[[d,!I.isLoading]])}],["__scopeId","data-v-8123a353"]]);export{U as default};
