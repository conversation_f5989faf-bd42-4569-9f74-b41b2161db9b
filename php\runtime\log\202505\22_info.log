[2025-05-22 19:22:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912346
            [orderIds] => []
        )

)

[2025-05-22 19:22:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912357
            [orderIds] => []
        )

)

[2025-05-22 19:22:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912368
            [orderIds] => []
        )

)

[2025-05-22 19:22:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912379
            [orderIds] => []
        )

)

[2025-05-22 19:23:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912390
            [orderIds] => []
        )

)

[2025-05-22 19:23:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912401
            [orderIds] => []
        )

)

[2025-05-22 19:23:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912412
            [orderIds] => []
        )

)

[2025-05-22 19:23:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912423
            [orderIds] => []
        )

)

[2025-05-22 19:23:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912434
            [orderIds] => []
        )

)

[2025-05-22 19:24:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912445
            [orderIds] => []
        )

)

[2025-05-22 19:24:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912456
            [orderIds] => []
        )

)

[2025-05-22 19:24:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912467
            [orderIds] => []
        )

)

[2025-05-22 19:24:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912478
            [orderIds] => []
        )

)

[2025-05-22 19:24:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912489
            [orderIds] => []
        )

)

[2025-05-22 19:25:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912500
            [orderIds] => []
        )

)

[2025-05-22 19:25:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912511
            [orderIds] => []
        )

)

[2025-05-22 19:25:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912522
            [orderIds] => []
        )

)

[2025-05-22 19:25:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912533
            [orderIds] => []
        )

)

[2025-05-22 19:25:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912544
            [orderIds] => []
        )

)

[2025-05-22 19:25:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912555
            [orderIds] => []
        )

)

[2025-05-22 19:26:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912566
            [orderIds] => []
        )

)

[2025-05-22 19:26:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912577
            [orderIds] => []
        )

)

[2025-05-22 19:26:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912588
            [orderIds] => []
        )

)

[2025-05-22 19:26:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912599
            [orderIds] => []
        )

)

[2025-05-22 19:26:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912610
            [orderIds] => []
        )

)

[2025-05-22 19:27:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912621
            [orderIds] => []
        )

)

[2025-05-22 19:27:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912632
            [orderIds] => []
        )

)

[2025-05-22 19:27:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912642
            [orderIds] => []
        )

)

[2025-05-22 19:27:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912652
            [orderIds] => []
        )

)

[2025-05-22 19:27:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912663
            [orderIds] => []
        )

)

[2025-05-22 19:27:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912674
            [orderIds] => []
        )

)

[2025-05-22 19:28:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912685
            [orderIds] => []
        )

)

[2025-05-22 19:28:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912696
            [orderIds] => []
        )

)

[2025-05-22 19:28:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 19:28:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 19:28:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 19:28:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 19:28:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912707
            [orderIds] => []
        )

)

[2025-05-22 19:28:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912718
            [orderIds] => []
        )

)

[2025-05-22 19:28:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912729
            [orderIds] => []
        )

)

[2025-05-22 19:29:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912740
            [orderIds] => []
        )

)

[2025-05-22 19:29:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912751
            [orderIds] => []
        )

)

[2025-05-22 19:29:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912762
            [orderIds] => []
        )

)

[2025-05-22 19:29:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912773
            [orderIds] => []
        )

)

[2025-05-22 19:29:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912784
            [orderIds] => []
        )

)

[2025-05-22 19:29:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912795
            [orderIds] => []
        )

)

[2025-05-22 19:30:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912806
            [orderIds] => []
        )

)

[2025-05-22 19:30:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912817
            [orderIds] => []
        )

)

[2025-05-22 19:30:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912828
            [orderIds] => []
        )

)

[2025-05-22 19:30:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912839
            [orderIds] => []
        )

)

[2025-05-22 19:30:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912850
            [orderIds] => []
        )

)

[2025-05-22 19:31:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912861
            [orderIds] => []
        )

)

[2025-05-22 19:31:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912872
            [orderIds] => []
        )

)

[2025-05-22 19:31:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912883
            [orderIds] => []
        )

)

[2025-05-22 19:31:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912894
            [orderIds] => []
        )

)

[2025-05-22 19:31:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912905
            [orderIds] => []
        )

)

[2025-05-22 19:31:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912916
            [orderIds] => []
        )

)

[2025-05-22 19:32:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912927
            [orderIds] => []
        )

)

[2025-05-22 19:32:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912938
            [orderIds] => []
        )

)

[2025-05-22 19:32:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912949
            [orderIds] => []
        )

)

[2025-05-22 19:32:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912960
            [orderIds] => []
        )

)

[2025-05-22 19:32:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912971
            [orderIds] => []
        )

)

[2025-05-22 19:33:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912982
            [orderIds] => []
        )

)

[2025-05-22 19:33:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747912993
            [orderIds] => []
        )

)

[2025-05-22 19:33:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913004
            [orderIds] => []
        )

)

[2025-05-22 19:33:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913015
            [orderIds] => []
        )

)

[2025-05-22 19:33:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913026
            [orderIds] => []
        )

)

[2025-05-22 19:33:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913037
            [orderIds] => []
        )

)

[2025-05-22 19:34:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913048
            [orderIds] => []
        )

)

[2025-05-22 19:34:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913059
            [orderIds] => []
        )

)

[2025-05-22 19:34:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913070
            [orderIds] => []
        )

)

[2025-05-22 19:34:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913081
            [orderIds] => []
        )

)

[2025-05-22 19:34:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913092
            [orderIds] => []
        )

)

[2025-05-22 19:35:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913103
            [orderIds] => []
        )

)

[2025-05-22 19:35:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913114
            [orderIds] => []
        )

)

[2025-05-22 19:35:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913125
            [orderIds] => []
        )

)

[2025-05-22 19:35:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913136
            [orderIds] => []
        )

)

[2025-05-22 19:35:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913147
            [orderIds] => []
        )

)

[2025-05-22 19:35:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913158
            [orderIds] => []
        )

)

[2025-05-22 19:36:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913169
            [orderIds] => []
        )

)

[2025-05-22 19:36:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913180
            [orderIds] => []
        )

)

[2025-05-22 19:36:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913191
            [orderIds] => []
        )

)

[2025-05-22 19:36:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913202
            [orderIds] => []
        )

)

[2025-05-22 19:36:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913213
            [orderIds] => []
        )

)

[2025-05-22 19:37:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913224
            [orderIds] => []
        )

)

[2025-05-22 19:37:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913235
            [orderIds] => []
        )

)

[2025-05-22 19:37:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913246
            [orderIds] => []
        )

)

[2025-05-22 19:37:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913257
            [orderIds] => []
        )

)

[2025-05-22 19:37:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913268
            [orderIds] => []
        )

)

[2025-05-22 19:37:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913279
            [orderIds] => []
        )

)

[2025-05-22 19:38:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913290
            [orderIds] => []
        )

)

[2025-05-22 19:38:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 19:38:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 19:38:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 19:38:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 19:38:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913301
            [orderIds] => []
        )

)

[2025-05-22 19:38:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913312
            [orderIds] => []
        )

)

[2025-05-22 19:38:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913323
            [orderIds] => []
        )

)

[2025-05-22 19:38:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913334
            [orderIds] => []
        )

)

[2025-05-22 19:39:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913345
            [orderIds] => []
        )

)

[2025-05-22 19:39:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913356
            [orderIds] => []
        )

)

[2025-05-22 19:39:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913367
            [orderIds] => []
        )

)

[2025-05-22 19:39:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913378
            [orderIds] => []
        )

)

[2025-05-22 19:39:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913389
            [orderIds] => []
        )

)

[2025-05-22 19:40:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913400
            [orderIds] => []
        )

)

[2025-05-22 19:40:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913411
            [orderIds] => []
        )

)

[2025-05-22 19:40:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913422
            [orderIds] => []
        )

)

[2025-05-22 19:40:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913433
            [orderIds] => []
        )

)

[2025-05-22 19:40:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913444
            [orderIds] => []
        )

)

[2025-05-22 19:40:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913455
            [orderIds] => []
        )

)

[2025-05-22 19:41:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913466
            [orderIds] => []
        )

)

[2025-05-22 19:41:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913477
            [orderIds] => []
        )

)

[2025-05-22 19:41:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913488
            [orderIds] => []
        )

)

[2025-05-22 19:41:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913499
            [orderIds] => []
        )

)

[2025-05-22 19:41:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913510
            [orderIds] => []
        )

)

[2025-05-22 19:42:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913521
            [orderIds] => []
        )

)

[2025-05-22 19:42:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747827732
            [orderIds] => []
        )

)

[2025-05-22 19:42:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1747309332
            [orderIds] => []
        )

)

[2025-05-22 19:42:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1747309332
            [orderIds] => []
        )

)

[2025-05-22 19:42:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913532
            [orderIds] => []
        )

)

[2025-05-22 19:42:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-22 19:42:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-22 19:42:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-22 19:42:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-22 19:42:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-22 19:42:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913543
            [orderIds] => []
        )

)

[2025-05-22 19:42:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913554
            [orderIds] => []
        )

)

[2025-05-22 19:42:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913565
            [orderIds] => []
        )

)

[2025-05-22 19:42:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913576
            [orderIds] => []
        )

)

[2025-05-22 19:43:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913587
            [orderIds] => []
        )

)

[2025-05-22 19:43:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913598
            [orderIds] => []
        )

)

[2025-05-22 19:43:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913609
            [orderIds] => []
        )

)

[2025-05-22 19:43:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913620
            [orderIds] => []
        )

)

[2025-05-22 19:43:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913631
            [orderIds] => []
        )

)

[2025-05-22 19:44:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913642
            [orderIds] => []
        )

)

[2025-05-22 19:44:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913653
            [orderIds] => []
        )

)

[2025-05-22 19:44:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913664
            [orderIds] => []
        )

)

[2025-05-22 19:44:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913675
            [orderIds] => []
        )

)

[2025-05-22 19:44:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913686
            [orderIds] => []
        )

)

[2025-05-22 19:44:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913697
            [orderIds] => []
        )

)

[2025-05-22 19:45:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913708
            [orderIds] => []
        )

)

[2025-05-22 19:45:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913719
            [orderIds] => []
        )

)

[2025-05-22 19:45:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913730
            [orderIds] => []
        )

)

[2025-05-22 19:45:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913741
            [orderIds] => []
        )

)

[2025-05-22 19:45:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913752
            [orderIds] => []
        )

)

[2025-05-22 19:46:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913763
            [orderIds] => []
        )

)

[2025-05-22 19:46:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913774
            [orderIds] => []
        )

)

[2025-05-22 19:46:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913785
            [orderIds] => []
        )

)

[2025-05-22 19:46:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913796
            [orderIds] => []
        )

)

[2025-05-22 19:46:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913807
            [orderIds] => []
        )

)

[2025-05-22 19:46:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913818
            [orderIds] => []
        )

)

[2025-05-22 19:47:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913829
            [orderIds] => []
        )

)

[2025-05-22 19:47:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913840
            [orderIds] => []
        )

)

[2025-05-22 19:47:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913851
            [orderIds] => []
        )

)

[2025-05-22 19:47:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913862
            [orderIds] => []
        )

)

[2025-05-22 19:47:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913873
            [orderIds] => []
        )

)

[2025-05-22 19:48:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913884
            [orderIds] => []
        )

)

[2025-05-22 19:48:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913895
            [orderIds] => []
        )

)

[2025-05-22 19:48:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 19:48:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 19:48:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 19:48:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 19:48:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913906
            [orderIds] => []
        )

)

[2025-05-22 19:48:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913917
            [orderIds] => []
        )

)

[2025-05-22 19:48:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913928
            [orderIds] => []
        )

)

[2025-05-22 19:48:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913939
            [orderIds] => []
        )

)

[2025-05-22 19:49:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913950
            [orderIds] => []
        )

)

[2025-05-22 19:49:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913961
            [orderIds] => []
        )

)

[2025-05-22 19:49:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913972
            [orderIds] => []
        )

)

[2025-05-22 19:49:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913983
            [orderIds] => []
        )

)

[2025-05-22 19:49:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747913994
            [orderIds] => []
        )

)

[2025-05-22 19:50:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914005
            [orderIds] => []
        )

)

[2025-05-22 19:50:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914016
            [orderIds] => []
        )

)

[2025-05-22 19:50:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914027
            [orderIds] => []
        )

)

[2025-05-22 19:50:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914038
            [orderIds] => []
        )

)

[2025-05-22 19:50:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914049
            [orderIds] => []
        )

)

[2025-05-22 19:51:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914060
            [orderIds] => []
        )

)

[2025-05-22 19:51:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914071
            [orderIds] => []
        )

)

[2025-05-22 19:51:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914082
            [orderIds] => []
        )

)

[2025-05-22 19:51:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914093
            [orderIds] => []
        )

)

[2025-05-22 19:51:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914104
            [orderIds] => []
        )

)

[2025-05-22 19:51:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914115
            [orderIds] => []
        )

)

[2025-05-22 19:52:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914126
            [orderIds] => []
        )

)

[2025-05-22 19:52:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914137
            [orderIds] => []
        )

)

[2025-05-22 19:52:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914148
            [orderIds] => []
        )

)

[2025-05-22 19:52:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914159
            [orderIds] => []
        )

)

[2025-05-22 19:52:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914170
            [orderIds] => []
        )

)

[2025-05-22 19:53:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914181
            [orderIds] => []
        )

)

[2025-05-22 19:53:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914192
            [orderIds] => []
        )

)

[2025-05-22 19:53:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914203
            [orderIds] => []
        )

)

[2025-05-22 19:53:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914214
            [orderIds] => []
        )

)

[2025-05-22 19:53:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914225
            [orderIds] => []
        )

)

[2025-05-22 19:53:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914236
            [orderIds] => []
        )

)

[2025-05-22 19:54:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914247
            [orderIds] => []
        )

)

[2025-05-22 19:54:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914258
            [orderIds] => []
        )

)

[2025-05-22 19:54:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914269
            [orderIds] => []
        )

)

[2025-05-22 19:54:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914280
            [orderIds] => []
        )

)

[2025-05-22 19:54:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914291
            [orderIds] => []
        )

)

[2025-05-22 19:55:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914302
            [orderIds] => []
        )

)

[2025-05-22 19:55:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914313
            [orderIds] => []
        )

)

[2025-05-22 19:55:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914324
            [orderIds] => []
        )

)

[2025-05-22 19:55:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914335
            [orderIds] => []
        )

)

[2025-05-22 19:55:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914346
            [orderIds] => []
        )

)

[2025-05-22 19:55:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914357
            [orderIds] => []
        )

)

[2025-05-22 19:56:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914368
            [orderIds] => []
        )

)

[2025-05-22 19:56:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914379
            [orderIds] => []
        )

)

[2025-05-22 19:56:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914390
            [orderIds] => []
        )

)

[2025-05-22 19:56:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914401
            [orderIds] => []
        )

)

[2025-05-22 19:56:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914412
            [orderIds] => []
        )

)

[2025-05-22 19:57:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914423
            [orderIds] => []
        )

)

[2025-05-22 19:57:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914434
            [orderIds] => []
        )

)

[2025-05-22 19:57:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914445
            [orderIds] => []
        )

)

[2025-05-22 19:57:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914456
            [orderIds] => []
        )

)

[2025-05-22 19:57:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914467
            [orderIds] => []
        )

)

[2025-05-22 19:57:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914478
            [orderIds] => []
        )

)

[2025-05-22 19:58:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914489
            [orderIds] => []
        )

)

[2025-05-22 19:58:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914500
            [orderIds] => []
        )

)

[2025-05-22 19:58:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 19:58:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 19:58:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 19:58:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 19:58:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914511
            [orderIds] => []
        )

)

[2025-05-22 19:58:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914522
            [orderIds] => []
        )

)

[2025-05-22 19:58:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914533
            [orderIds] => []
        )

)

[2025-05-22 19:59:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914544
            [orderIds] => []
        )

)

[2025-05-22 19:59:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914555
            [orderIds] => []
        )

)

[2025-05-22 19:59:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914566
            [orderIds] => []
        )

)

[2025-05-22 19:59:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914577
            [orderIds] => []
        )

)

[2025-05-22 19:59:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914588
            [orderIds] => []
        )

)

[2025-05-22 19:59:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914599
            [orderIds] => []
        )

)

[2025-05-22 20:00:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914610
            [orderIds] => []
        )

)

[2025-05-22 20:00:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914621
            [orderIds] => []
        )

)

[2025-05-22 20:00:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914632
            [orderIds] => []
        )

)

[2025-05-22 20:00:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914643
            [orderIds] => []
        )

)

[2025-05-22 20:00:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914654
            [orderIds] => []
        )

)

[2025-05-22 20:01:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914665
            [orderIds] => []
        )

)

[2025-05-22 20:01:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914676
            [orderIds] => []
        )

)

[2025-05-22 20:01:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914687
            [orderIds] => []
        )

)

[2025-05-22 20:01:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914698
            [orderIds] => []
        )

)

[2025-05-22 20:01:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914709
            [orderIds] => []
        )

)

[2025-05-22 20:02:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914720
            [orderIds] => []
        )

)

[2025-05-22 20:02:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914731
            [orderIds] => []
        )

)

[2025-05-22 20:02:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914742
            [orderIds] => []
        )

)

[2025-05-22 20:02:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914753
            [orderIds] => []
        )

)

[2025-05-22 20:02:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914764
            [orderIds] => []
        )

)

[2025-05-22 20:02:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914775
            [orderIds] => []
        )

)

[2025-05-22 20:03:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914786
            [orderIds] => []
        )

)

[2025-05-22 20:03:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914797
            [orderIds] => []
        )

)

[2025-05-22 20:03:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914808
            [orderIds] => []
        )

)

[2025-05-22 20:03:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914819
            [orderIds] => []
        )

)

[2025-05-22 20:03:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914830
            [orderIds] => []
        )

)

[2025-05-22 20:04:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914841
            [orderIds] => []
        )

)

[2025-05-22 20:04:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914852
            [orderIds] => []
        )

)

[2025-05-22 20:04:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914863
            [orderIds] => []
        )

)

[2025-05-22 20:04:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914874
            [orderIds] => []
        )

)

[2025-05-22 20:04:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914885
            [orderIds] => []
        )

)

[2025-05-22 20:04:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914896
            [orderIds] => []
        )

)

[2025-05-22 20:05:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914907
            [orderIds] => []
        )

)

[2025-05-22 20:05:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914918
            [orderIds] => []
        )

)

[2025-05-22 20:05:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914929
            [orderIds] => []
        )

)

[2025-05-22 20:05:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914940
            [orderIds] => []
        )

)

[2025-05-22 20:05:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914951
            [orderIds] => []
        )

)

[2025-05-22 20:06:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914962
            [orderIds] => []
        )

)

[2025-05-22 20:06:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914973
            [orderIds] => []
        )

)

[2025-05-22 20:06:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914984
            [orderIds] => []
        )

)

[2025-05-22 20:06:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747914995
            [orderIds] => []
        )

)

[2025-05-22 20:06:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915006
            [orderIds] => []
        )

)

[2025-05-22 20:06:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915017
            [orderIds] => []
        )

)

[2025-05-22 20:07:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915028
            [orderIds] => []
        )

)

[2025-05-22 20:07:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915039
            [orderIds] => []
        )

)

[2025-05-22 20:07:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915050
            [orderIds] => []
        )

)

[2025-05-22 20:07:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915061
            [orderIds] => []
        )

)

[2025-05-22 20:07:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915072
            [orderIds] => []
        )

)

[2025-05-22 20:08:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915083
            [orderIds] => []
        )

)

[2025-05-22 20:08:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915094
            [orderIds] => []
        )

)

[2025-05-22 20:08:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 20:08:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 20:08:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 20:08:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 20:08:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915105
            [orderIds] => []
        )

)

[2025-05-22 20:08:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915116
            [orderIds] => []
        )

)

[2025-05-22 20:08:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915127
            [orderIds] => []
        )

)

[2025-05-22 20:08:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915138
            [orderIds] => []
        )

)

[2025-05-22 20:09:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915149
            [orderIds] => []
        )

)

[2025-05-22 20:09:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915160
            [orderIds] => []
        )

)

[2025-05-22 20:09:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915171
            [orderIds] => []
        )

)

[2025-05-22 20:09:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915182
            [orderIds] => []
        )

)

[2025-05-22 20:09:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915193
            [orderIds] => []
        )

)

[2025-05-22 20:10:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915204
            [orderIds] => []
        )

)

[2025-05-22 20:10:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915215
            [orderIds] => []
        )

)

[2025-05-22 20:10:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915226
            [orderIds] => []
        )

)

[2025-05-22 20:10:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915237
            [orderIds] => []
        )

)

[2025-05-22 20:10:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915248
            [orderIds] => []
        )

)

[2025-05-22 20:10:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915259
            [orderIds] => []
        )

)

[2025-05-22 20:11:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915270
            [orderIds] => []
        )

)

[2025-05-22 20:11:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915281
            [orderIds] => []
        )

)

[2025-05-22 20:11:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915292
            [orderIds] => []
        )

)

[2025-05-22 20:11:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915303
            [orderIds] => []
        )

)

[2025-05-22 20:11:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915314
            [orderIds] => []
        )

)

[2025-05-22 20:12:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915325
            [orderIds] => []
        )

)

[2025-05-22 20:12:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747829533
            [orderIds] => []
        )

)

[2025-05-22 20:12:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1747311133
            [orderIds] => []
        )

)

[2025-05-22 20:12:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1747311133
            [orderIds] => []
        )

)

[2025-05-22 20:12:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-22 20:12:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-22 20:12:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-22 20:12:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-22 20:12:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-22 20:12:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915336
            [orderIds] => []
        )

)

[2025-05-22 20:12:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915347
            [orderIds] => []
        )

)

[2025-05-22 20:12:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915358
            [orderIds] => []
        )

)

[2025-05-22 20:12:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915369
            [orderIds] => []
        )

)

[2025-05-22 20:13:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915380
            [orderIds] => []
        )

)

[2025-05-22 20:13:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915391
            [orderIds] => []
        )

)

[2025-05-22 20:13:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915402
            [orderIds] => []
        )

)

[2025-05-22 20:13:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915413
            [orderIds] => []
        )

)

[2025-05-22 20:13:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915424
            [orderIds] => []
        )

)

[2025-05-22 20:13:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915435
            [orderIds] => []
        )

)

[2025-05-22 20:14:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915446
            [orderIds] => []
        )

)

[2025-05-22 20:14:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915457
            [orderIds] => []
        )

)

[2025-05-22 20:14:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915468
            [orderIds] => []
        )

)

[2025-05-22 20:14:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915479
            [orderIds] => []
        )

)

[2025-05-22 20:14:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915490
            [orderIds] => []
        )

)

[2025-05-22 20:15:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915501
            [orderIds] => []
        )

)

[2025-05-22 20:15:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915512
            [orderIds] => []
        )

)

[2025-05-22 20:15:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915523
            [orderIds] => []
        )

)

[2025-05-22 20:15:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915534
            [orderIds] => []
        )

)

[2025-05-22 20:15:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915545
            [orderIds] => []
        )

)

[2025-05-22 20:15:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915556
            [orderIds] => []
        )

)

[2025-05-22 20:16:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915567
            [orderIds] => []
        )

)

[2025-05-22 20:16:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915578
            [orderIds] => []
        )

)

[2025-05-22 20:16:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915589
            [orderIds] => []
        )

)

[2025-05-22 20:16:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915600
            [orderIds] => []
        )

)

[2025-05-22 20:16:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915611
            [orderIds] => []
        )

)

[2025-05-22 20:17:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915622
            [orderIds] => []
        )

)

[2025-05-22 20:17:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915633
            [orderIds] => []
        )

)

[2025-05-22 20:17:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915644
            [orderIds] => []
        )

)

[2025-05-22 20:17:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915655
            [orderIds] => []
        )

)

[2025-05-22 20:17:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915666
            [orderIds] => []
        )

)

[2025-05-22 20:17:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915677
            [orderIds] => []
        )

)

[2025-05-22 20:18:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915688
            [orderIds] => []
        )

)

[2025-05-22 20:18:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915699
            [orderIds] => []
        )

)

[2025-05-22 20:18:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 20:18:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 20:18:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 20:18:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 20:18:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915710
            [orderIds] => []
        )

)

[2025-05-22 20:18:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915721
            [orderIds] => []
        )

)

[2025-05-22 20:18:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915732
            [orderIds] => []
        )

)

[2025-05-22 20:19:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915743
            [orderIds] => []
        )

)

[2025-05-22 20:19:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915754
            [orderIds] => []
        )

)

[2025-05-22 20:19:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915765
            [orderIds] => []
        )

)

[2025-05-22 20:19:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915776
            [orderIds] => []
        )

)

[2025-05-22 20:19:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915787
            [orderIds] => []
        )

)

[2025-05-22 20:19:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915798
            [orderIds] => []
        )

)

[2025-05-22 20:20:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915809
            [orderIds] => []
        )

)

[2025-05-22 20:20:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915820
            [orderIds] => []
        )

)

[2025-05-22 20:20:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915831
            [orderIds] => []
        )

)

[2025-05-22 20:20:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915842
            [orderIds] => []
        )

)

[2025-05-22 20:20:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915853
            [orderIds] => []
        )

)

[2025-05-22 20:21:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915864
            [orderIds] => []
        )

)

[2025-05-22 20:21:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915875
            [orderIds] => []
        )

)

[2025-05-22 20:21:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915886
            [orderIds] => []
        )

)

[2025-05-22 20:21:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915897
            [orderIds] => []
        )

)

[2025-05-22 20:21:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915908
            [orderIds] => []
        )

)

[2025-05-22 20:21:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915919
            [orderIds] => []
        )

)

[2025-05-22 20:22:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915930
            [orderIds] => []
        )

)

[2025-05-22 20:22:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915941
            [orderIds] => []
        )

)

[2025-05-22 20:22:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915952
            [orderIds] => []
        )

)

[2025-05-22 20:22:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915963
            [orderIds] => []
        )

)

[2025-05-22 20:22:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915974
            [orderIds] => []
        )

)

[2025-05-22 20:23:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915985
            [orderIds] => []
        )

)

[2025-05-22 20:23:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747915996
            [orderIds] => []
        )

)

[2025-05-22 20:23:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916007
            [orderIds] => []
        )

)

[2025-05-22 20:23:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916018
            [orderIds] => []
        )

)

[2025-05-22 20:23:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916029
            [orderIds] => []
        )

)

[2025-05-22 20:24:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916040
            [orderIds] => []
        )

)

[2025-05-22 20:24:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916051
            [orderIds] => []
        )

)

[2025-05-22 20:24:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916062
            [orderIds] => []
        )

)

[2025-05-22 20:24:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916073
            [orderIds] => []
        )

)

[2025-05-22 20:24:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916084
            [orderIds] => []
        )

)

[2025-05-22 20:24:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916095
            [orderIds] => []
        )

)

[2025-05-22 20:25:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916106
            [orderIds] => []
        )

)

[2025-05-22 20:25:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916117
            [orderIds] => []
        )

)

[2025-05-22 20:25:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916128
            [orderIds] => []
        )

)

[2025-05-22 20:25:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916139
            [orderIds] => []
        )

)

[2025-05-22 20:25:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916150
            [orderIds] => []
        )

)

[2025-05-22 20:26:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916161
            [orderIds] => []
        )

)

[2025-05-22 20:26:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916172
            [orderIds] => []
        )

)

[2025-05-22 20:26:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916183
            [orderIds] => []
        )

)

[2025-05-22 20:26:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916194
            [orderIds] => []
        )

)

[2025-05-22 20:26:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916205
            [orderIds] => []
        )

)

[2025-05-22 20:26:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916216
            [orderIds] => []
        )

)

[2025-05-22 20:27:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916227
            [orderIds] => []
        )

)

[2025-05-22 20:27:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916238
            [orderIds] => []
        )

)

[2025-05-22 20:27:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916249
            [orderIds] => []
        )

)

[2025-05-22 20:27:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916260
            [orderIds] => []
        )

)

[2025-05-22 20:27:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916271
            [orderIds] => []
        )

)

[2025-05-22 20:28:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916282
            [orderIds] => []
        )

)

[2025-05-22 20:28:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916293
            [orderIds] => []
        )

)

[2025-05-22 20:28:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916304
            [orderIds] => []
        )

)

[2025-05-22 20:28:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 20:28:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 20:28:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 20:28:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 20:28:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916315
            [orderIds] => []
        )

)

[2025-05-22 20:28:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916326
            [orderIds] => []
        )

)

[2025-05-22 20:28:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916337
            [orderIds] => []
        )

)

[2025-05-22 20:29:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916348
            [orderIds] => []
        )

)

[2025-05-22 20:29:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916359
            [orderIds] => []
        )

)

[2025-05-22 20:29:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916370
            [orderIds] => []
        )

)

[2025-05-22 20:29:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916381
            [orderIds] => []
        )

)

[2025-05-22 20:29:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916392
            [orderIds] => []
        )

)

[2025-05-22 20:30:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916403
            [orderIds] => []
        )

)

[2025-05-22 20:30:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916414
            [orderIds] => []
        )

)

[2025-05-22 20:30:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916425
            [orderIds] => []
        )

)

[2025-05-22 20:30:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916436
            [orderIds] => []
        )

)

[2025-05-22 20:30:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916447
            [orderIds] => []
        )

)

[2025-05-22 20:30:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916458
            [orderIds] => []
        )

)

[2025-05-22 20:31:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916469
            [orderIds] => []
        )

)

[2025-05-22 20:31:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916480
            [orderIds] => []
        )

)

[2025-05-22 20:31:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916491
            [orderIds] => []
        )

)

[2025-05-22 20:31:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916502
            [orderIds] => []
        )

)

[2025-05-22 20:31:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916513
            [orderIds] => []
        )

)

[2025-05-22 20:32:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916524
            [orderIds] => []
        )

)

[2025-05-22 20:32:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916535
            [orderIds] => []
        )

)

[2025-05-22 20:32:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916546
            [orderIds] => []
        )

)

[2025-05-22 20:32:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916557
            [orderIds] => []
        )

)

[2025-05-22 20:32:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916568
            [orderIds] => []
        )

)

[2025-05-22 20:32:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916579
            [orderIds] => []
        )

)

[2025-05-22 20:33:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916590
            [orderIds] => []
        )

)

[2025-05-22 20:33:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916601
            [orderIds] => []
        )

)

[2025-05-22 20:33:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916612
            [orderIds] => []
        )

)

[2025-05-22 20:33:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916623
            [orderIds] => []
        )

)

[2025-05-22 20:33:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916634
            [orderIds] => []
        )

)

[2025-05-22 20:34:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916645
            [orderIds] => []
        )

)

[2025-05-22 20:34:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916656
            [orderIds] => []
        )

)

[2025-05-22 20:34:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916667
            [orderIds] => []
        )

)

[2025-05-22 20:34:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916678
            [orderIds] => []
        )

)

[2025-05-22 20:34:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916689
            [orderIds] => []
        )

)

[2025-05-22 20:35:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916700
            [orderIds] => []
        )

)

[2025-05-22 20:35:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916711
            [orderIds] => []
        )

)

[2025-05-22 20:35:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916722
            [orderIds] => []
        )

)

[2025-05-22 20:35:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916733
            [orderIds] => []
        )

)

[2025-05-22 20:35:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916744
            [orderIds] => []
        )

)

[2025-05-22 20:35:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916755
            [orderIds] => []
        )

)

[2025-05-22 20:36:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916766
            [orderIds] => []
        )

)

[2025-05-22 20:36:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916777
            [orderIds] => []
        )

)

[2025-05-22 20:36:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916788
            [orderIds] => []
        )

)

[2025-05-22 20:36:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916799
            [orderIds] => []
        )

)

[2025-05-22 20:36:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916810
            [orderIds] => []
        )

)

[2025-05-22 20:37:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916821
            [orderIds] => []
        )

)

[2025-05-22 20:37:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916832
            [orderIds] => []
        )

)

[2025-05-22 20:37:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916843
            [orderIds] => []
        )

)

[2025-05-22 20:37:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916854
            [orderIds] => []
        )

)

[2025-05-22 20:37:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916865
            [orderIds] => []
        )

)

[2025-05-22 20:37:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916876
            [orderIds] => []
        )

)

[2025-05-22 20:38:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916887
            [orderIds] => []
        )

)

[2025-05-22 20:38:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916898
            [orderIds] => []
        )

)

[2025-05-22 20:38:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 20:38:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 20:38:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 20:38:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 20:38:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916909
            [orderIds] => []
        )

)

[2025-05-22 20:38:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916920
            [orderIds] => []
        )

)

[2025-05-22 20:38:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916931
            [orderIds] => []
        )

)

[2025-05-22 20:39:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916942
            [orderIds] => []
        )

)

[2025-05-22 20:39:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916953
            [orderIds] => []
        )

)

[2025-05-22 20:39:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916964
            [orderIds] => []
        )

)

[2025-05-22 20:39:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916975
            [orderIds] => []
        )

)

[2025-05-22 20:39:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916986
            [orderIds] => []
        )

)

[2025-05-22 20:39:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747916997
            [orderIds] => []
        )

)

[2025-05-22 20:40:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917008
            [orderIds] => []
        )

)

[2025-05-22 20:40:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917019
            [orderIds] => []
        )

)

[2025-05-22 20:40:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917030
            [orderIds] => []
        )

)

[2025-05-22 20:40:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917041
            [orderIds] => []
        )

)

[2025-05-22 20:40:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917052
            [orderIds] => []
        )

)

[2025-05-22 20:41:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917063
            [orderIds] => []
        )

)

[2025-05-22 20:41:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917074
            [orderIds] => []
        )

)

[2025-05-22 20:41:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917085
            [orderIds] => []
        )

)

[2025-05-22 20:41:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917096
            [orderIds] => []
        )

)

[2025-05-22 20:41:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917107
            [orderIds] => []
        )

)

[2025-05-22 20:41:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917118
            [orderIds] => []
        )

)

[2025-05-22 20:42:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917129
            [orderIds] => []
        )

)

[2025-05-22 20:42:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747831334
            [orderIds] => []
        )

)

[2025-05-22 20:42:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1747312934
            [orderIds] => []
        )

)

[2025-05-22 20:42:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1747312934
            [orderIds] => []
        )

)

[2025-05-22 20:42:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-22 20:42:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-22 20:42:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-22 20:42:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-22 20:42:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-22 20:42:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917140
            [orderIds] => []
        )

)

[2025-05-22 20:42:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917151
            [orderIds] => []
        )

)

[2025-05-22 20:42:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917162
            [orderIds] => []
        )

)

[2025-05-22 20:42:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917173
            [orderIds] => []
        )

)

[2025-05-22 20:43:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917184
            [orderIds] => []
        )

)

[2025-05-22 20:43:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917195
            [orderIds] => []
        )

)

[2025-05-22 20:43:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917206
            [orderIds] => []
        )

)

[2025-05-22 20:43:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917217
            [orderIds] => []
        )

)

[2025-05-22 20:43:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917228
            [orderIds] => []
        )

)

[2025-05-22 20:43:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917239
            [orderIds] => []
        )

)

[2025-05-22 20:44:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917250
            [orderIds] => []
        )

)

[2025-05-22 20:44:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917261
            [orderIds] => []
        )

)

[2025-05-22 20:44:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917272
            [orderIds] => []
        )

)

[2025-05-22 20:44:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917283
            [orderIds] => []
        )

)

[2025-05-22 20:44:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917294
            [orderIds] => []
        )

)

[2025-05-22 20:45:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917305
            [orderIds] => []
        )

)

[2025-05-22 20:45:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917316
            [orderIds] => []
        )

)

[2025-05-22 20:45:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917327
            [orderIds] => []
        )

)

[2025-05-22 20:45:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917338
            [orderIds] => []
        )

)

[2025-05-22 20:45:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917349
            [orderIds] => []
        )

)

[2025-05-22 20:46:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917360
            [orderIds] => []
        )

)

[2025-05-22 20:46:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917371
            [orderIds] => []
        )

)

[2025-05-22 20:46:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917382
            [orderIds] => []
        )

)

[2025-05-22 20:46:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917393
            [orderIds] => []
        )

)

[2025-05-22 20:46:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917404
            [orderIds] => []
        )

)

[2025-05-22 20:46:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917415
            [orderIds] => []
        )

)

[2025-05-22 20:47:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917426
            [orderIds] => []
        )

)

[2025-05-22 20:47:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917437
            [orderIds] => []
        )

)

[2025-05-22 20:47:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917448
            [orderIds] => []
        )

)

[2025-05-22 20:47:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917459
            [orderIds] => []
        )

)

[2025-05-22 20:47:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917470
            [orderIds] => []
        )

)

[2025-05-22 20:48:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917481
            [orderIds] => []
        )

)

[2025-05-22 20:48:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917492
            [orderIds] => []
        )

)

[2025-05-22 20:48:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917503
            [orderIds] => []
        )

)

[2025-05-22 20:48:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 20:48:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 20:48:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 20:48:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 20:48:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917514
            [orderIds] => []
        )

)

[2025-05-22 20:48:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917525
            [orderIds] => []
        )

)

[2025-05-22 20:48:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917536
            [orderIds] => []
        )

)

[2025-05-22 20:49:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917547
            [orderIds] => []
        )

)

[2025-05-22 20:49:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917558
            [orderIds] => []
        )

)

[2025-05-22 20:49:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917569
            [orderIds] => []
        )

)

[2025-05-22 20:49:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917580
            [orderIds] => []
        )

)

[2025-05-22 20:49:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917591
            [orderIds] => []
        )

)

[2025-05-22 20:50:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917602
            [orderIds] => []
        )

)

[2025-05-22 20:50:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917613
            [orderIds] => []
        )

)

[2025-05-22 20:50:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917624
            [orderIds] => []
        )

)

[2025-05-22 20:50:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917635
            [orderIds] => []
        )

)

[2025-05-22 20:50:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917646
            [orderIds] => []
        )

)

[2025-05-22 20:50:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917657
            [orderIds] => []
        )

)

[2025-05-22 20:51:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917668
            [orderIds] => []
        )

)

[2025-05-22 20:51:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917679
            [orderIds] => []
        )

)

[2025-05-22 20:51:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917690
            [orderIds] => []
        )

)

[2025-05-22 20:51:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917701
            [orderIds] => []
        )

)

[2025-05-22 20:51:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917712
            [orderIds] => []
        )

)

[2025-05-22 20:52:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917723
            [orderIds] => []
        )

)

[2025-05-22 20:52:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917734
            [orderIds] => []
        )

)

[2025-05-22 20:52:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917745
            [orderIds] => []
        )

)

[2025-05-22 20:52:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917756
            [orderIds] => []
        )

)

[2025-05-22 20:52:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917767
            [orderIds] => []
        )

)

[2025-05-22 20:52:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917778
            [orderIds] => []
        )

)

[2025-05-22 20:53:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917789
            [orderIds] => []
        )

)

[2025-05-22 20:53:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917800
            [orderIds] => []
        )

)

[2025-05-22 20:53:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917811
            [orderIds] => []
        )

)

[2025-05-22 20:53:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917822
            [orderIds] => []
        )

)

[2025-05-22 20:53:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917833
            [orderIds] => []
        )

)

[2025-05-22 20:54:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917844
            [orderIds] => []
        )

)

[2025-05-22 20:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917855
            [orderIds] => []
        )

)

[2025-05-22 20:54:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917866
            [orderIds] => []
        )

)

[2025-05-22 20:54:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917877
            [orderIds] => []
        )

)

[2025-05-22 20:54:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917888
            [orderIds] => []
        )

)

[2025-05-22 20:54:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917899
            [orderIds] => []
        )

)

[2025-05-22 20:55:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917910
            [orderIds] => []
        )

)

[2025-05-22 20:55:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917921
            [orderIds] => []
        )

)

[2025-05-22 20:55:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917932
            [orderIds] => []
        )

)

[2025-05-22 20:55:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917943
            [orderIds] => []
        )

)

[2025-05-22 20:55:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917954
            [orderIds] => []
        )

)

[2025-05-22 20:56:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917965
            [orderIds] => []
        )

)

[2025-05-22 20:56:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917976
            [orderIds] => []
        )

)

[2025-05-22 20:56:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917987
            [orderIds] => []
        )

)

[2025-05-22 20:56:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747917998
            [orderIds] => []
        )

)

[2025-05-22 20:56:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918009
            [orderIds] => []
        )

)

[2025-05-22 20:57:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918020
            [orderIds] => []
        )

)

[2025-05-22 20:57:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918031
            [orderIds] => []
        )

)

[2025-05-22 20:57:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918042
            [orderIds] => []
        )

)

[2025-05-22 20:57:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918053
            [orderIds] => []
        )

)

[2025-05-22 20:57:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918064
            [orderIds] => []
        )

)

[2025-05-22 20:57:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918075
            [orderIds] => []
        )

)

[2025-05-22 20:58:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918086
            [orderIds] => []
        )

)

[2025-05-22 20:58:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918097
            [orderIds] => []
        )

)

[2025-05-22 20:58:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 20:58:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 20:58:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 20:58:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 20:58:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918108
            [orderIds] => []
        )

)

[2025-05-22 20:58:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918119
            [orderIds] => []
        )

)

[2025-05-22 20:58:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918130
            [orderIds] => []
        )

)

[2025-05-22 20:59:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918141
            [orderIds] => []
        )

)

[2025-05-22 20:59:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918152
            [orderIds] => []
        )

)

[2025-05-22 20:59:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918163
            [orderIds] => []
        )

)

[2025-05-22 20:59:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918174
            [orderIds] => []
        )

)

[2025-05-22 20:59:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918185
            [orderIds] => []
        )

)

[2025-05-22 20:59:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918196
            [orderIds] => []
        )

)

[2025-05-22 21:00:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918207
            [orderIds] => []
        )

)

[2025-05-22 21:00:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918218
            [orderIds] => []
        )

)

[2025-05-22 21:00:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918229
            [orderIds] => []
        )

)

[2025-05-22 21:00:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918240
            [orderIds] => []
        )

)

[2025-05-22 21:00:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918251
            [orderIds] => []
        )

)

[2025-05-22 21:01:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918262
            [orderIds] => []
        )

)

[2025-05-22 21:01:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918273
            [orderIds] => []
        )

)

[2025-05-22 21:01:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918284
            [orderIds] => []
        )

)

[2025-05-22 21:01:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918295
            [orderIds] => []
        )

)

[2025-05-22 21:01:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918306
            [orderIds] => []
        )

)

[2025-05-22 21:01:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918317
            [orderIds] => []
        )

)

[2025-05-22 21:02:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918328
            [orderIds] => []
        )

)

[2025-05-22 21:02:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918339
            [orderIds] => []
        )

)

[2025-05-22 21:02:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918350
            [orderIds] => []
        )

)

[2025-05-22 21:02:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918361
            [orderIds] => []
        )

)

[2025-05-22 21:02:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918372
            [orderIds] => []
        )

)

[2025-05-22 21:03:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918383
            [orderIds] => []
        )

)

[2025-05-22 21:03:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918394
            [orderIds] => []
        )

)

[2025-05-22 21:03:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918405
            [orderIds] => []
        )

)

[2025-05-22 21:03:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918416
            [orderIds] => []
        )

)

[2025-05-22 21:03:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918427
            [orderIds] => []
        )

)

[2025-05-22 21:03:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918438
            [orderIds] => []
        )

)

[2025-05-22 21:04:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918449
            [orderIds] => []
        )

)

[2025-05-22 21:04:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918460
            [orderIds] => []
        )

)

[2025-05-22 21:04:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918471
            [orderIds] => []
        )

)

[2025-05-22 21:04:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918482
            [orderIds] => []
        )

)

[2025-05-22 21:04:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918493
            [orderIds] => []
        )

)

[2025-05-22 21:05:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918504
            [orderIds] => []
        )

)

[2025-05-22 21:05:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918515
            [orderIds] => []
        )

)

[2025-05-22 21:05:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918526
            [orderIds] => []
        )

)

[2025-05-22 21:05:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918537
            [orderIds] => []
        )

)

[2025-05-22 21:05:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918548
            [orderIds] => []
        )

)

[2025-05-22 21:05:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918559
            [orderIds] => []
        )

)

[2025-05-22 21:06:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918570
            [orderIds] => []
        )

)

[2025-05-22 21:06:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918581
            [orderIds] => []
        )

)

[2025-05-22 21:06:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918592
            [orderIds] => []
        )

)

[2025-05-22 21:06:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918603
            [orderIds] => []
        )

)

[2025-05-22 21:06:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918614
            [orderIds] => []
        )

)

[2025-05-22 21:07:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918625
            [orderIds] => []
        )

)

[2025-05-22 21:07:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918636
            [orderIds] => []
        )

)

[2025-05-22 21:07:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918647
            [orderIds] => []
        )

)

[2025-05-22 21:07:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918658
            [orderIds] => []
        )

)

[2025-05-22 21:07:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918669
            [orderIds] => []
        )

)

[2025-05-22 21:08:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918680
            [orderIds] => []
        )

)

[2025-05-22 21:08:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918691
            [orderIds] => []
        )

)

[2025-05-22 21:08:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918702
            [orderIds] => []
        )

)

[2025-05-22 21:08:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 21:08:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 21:08:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 21:08:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 21:08:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918713
            [orderIds] => []
        )

)

[2025-05-22 21:08:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918724
            [orderIds] => []
        )

)

[2025-05-22 21:08:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918735
            [orderIds] => []
        )

)

[2025-05-22 21:09:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918746
            [orderIds] => []
        )

)

[2025-05-22 21:09:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918757
            [orderIds] => []
        )

)

[2025-05-22 21:09:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918768
            [orderIds] => []
        )

)

[2025-05-22 21:09:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918779
            [orderIds] => []
        )

)

[2025-05-22 21:09:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918790
            [orderIds] => []
        )

)

[2025-05-22 21:10:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918801
            [orderIds] => []
        )

)

[2025-05-22 21:10:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918812
            [orderIds] => []
        )

)

[2025-05-22 21:10:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918823
            [orderIds] => []
        )

)

[2025-05-22 21:10:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918834
            [orderIds] => []
        )

)

[2025-05-22 21:10:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918845
            [orderIds] => []
        )

)

[2025-05-22 21:10:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918856
            [orderIds] => []
        )

)

[2025-05-22 21:11:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918867
            [orderIds] => []
        )

)

[2025-05-22 21:11:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918878
            [orderIds] => []
        )

)

[2025-05-22 21:11:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918889
            [orderIds] => []
        )

)

[2025-05-22 21:11:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918900
            [orderIds] => []
        )

)

[2025-05-22 21:11:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918911
            [orderIds] => []
        )

)

[2025-05-22 21:12:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918922
            [orderIds] => []
        )

)

[2025-05-22 21:12:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918933
            [orderIds] => []
        )

)

[2025-05-22 21:12:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747833135
            [orderIds] => []
        )

)

[2025-05-22 21:12:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1747314735
            [orderIds] => []
        )

)

[2025-05-22 21:12:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1747314735
            [orderIds] => []
        )

)

[2025-05-22 21:12:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-22 21:12:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-22 21:12:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-22 21:12:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-22 21:12:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-22 21:12:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918944
            [orderIds] => []
        )

)

[2025-05-22 21:12:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918955
            [orderIds] => []
        )

)

[2025-05-22 21:12:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918966
            [orderIds] => []
        )

)

[2025-05-22 21:12:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918977
            [orderIds] => []
        )

)

[2025-05-22 21:13:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918988
            [orderIds] => []
        )

)

[2025-05-22 21:13:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747918999
            [orderIds] => []
        )

)

[2025-05-22 21:13:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919010
            [orderIds] => []
        )

)

[2025-05-22 21:13:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919021
            [orderIds] => []
        )

)

[2025-05-22 21:13:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919032
            [orderIds] => []
        )

)

[2025-05-22 21:14:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919043
            [orderIds] => []
        )

)

[2025-05-22 21:14:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919054
            [orderIds] => []
        )

)

[2025-05-22 21:14:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919065
            [orderIds] => []
        )

)

[2025-05-22 21:14:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919076
            [orderIds] => []
        )

)

[2025-05-22 21:14:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919087
            [orderIds] => []
        )

)

[2025-05-22 21:14:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919098
            [orderIds] => []
        )

)

[2025-05-22 21:15:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919109
            [orderIds] => []
        )

)

[2025-05-22 21:15:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919120
            [orderIds] => []
        )

)

[2025-05-22 21:15:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919131
            [orderIds] => []
        )

)

[2025-05-22 21:15:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919142
            [orderIds] => []
        )

)

[2025-05-22 21:15:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919153
            [orderIds] => []
        )

)

[2025-05-22 21:16:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919164
            [orderIds] => []
        )

)

[2025-05-22 21:16:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919175
            [orderIds] => []
        )

)

[2025-05-22 21:16:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919186
            [orderIds] => []
        )

)

[2025-05-22 21:16:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919197
            [orderIds] => []
        )

)

[2025-05-22 21:16:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919208
            [orderIds] => []
        )

)

[2025-05-22 21:16:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919219
            [orderIds] => []
        )

)

[2025-05-22 21:17:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919230
            [orderIds] => []
        )

)

[2025-05-22 21:17:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919241
            [orderIds] => []
        )

)

[2025-05-22 21:17:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919252
            [orderIds] => []
        )

)

[2025-05-22 21:17:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919263
            [orderIds] => []
        )

)

[2025-05-22 21:17:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919274
            [orderIds] => []
        )

)

[2025-05-22 21:18:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919285
            [orderIds] => []
        )

)

[2025-05-22 21:18:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919296
            [orderIds] => []
        )

)

[2025-05-22 21:18:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919307
            [orderIds] => []
        )

)

[2025-05-22 21:18:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 21:18:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 21:18:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 21:18:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 21:18:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919318
            [orderIds] => []
        )

)

[2025-05-22 21:18:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919329
            [orderIds] => []
        )

)

[2025-05-22 21:19:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919340
            [orderIds] => []
        )

)

[2025-05-22 21:19:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919351
            [orderIds] => []
        )

)

[2025-05-22 21:19:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919362
            [orderIds] => []
        )

)

[2025-05-22 21:19:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919373
            [orderIds] => []
        )

)

[2025-05-22 21:19:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919384
            [orderIds] => []
        )

)

[2025-05-22 21:19:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919395
            [orderIds] => []
        )

)

[2025-05-22 21:20:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919406
            [orderIds] => []
        )

)

[2025-05-22 21:20:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919417
            [orderIds] => []
        )

)

[2025-05-22 21:20:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919428
            [orderIds] => []
        )

)

[2025-05-22 21:20:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919439
            [orderIds] => []
        )

)

[2025-05-22 21:20:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919450
            [orderIds] => []
        )

)

[2025-05-22 21:21:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919461
            [orderIds] => []
        )

)

[2025-05-22 21:21:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919472
            [orderIds] => []
        )

)

[2025-05-22 21:21:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919483
            [orderIds] => []
        )

)

[2025-05-22 21:21:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919494
            [orderIds] => []
        )

)

[2025-05-22 21:21:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919505
            [orderIds] => []
        )

)

[2025-05-22 21:21:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919516
            [orderIds] => []
        )

)

[2025-05-22 21:22:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919527
            [orderIds] => []
        )

)

[2025-05-22 21:22:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919538
            [orderIds] => []
        )

)

[2025-05-22 21:22:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919549
            [orderIds] => []
        )

)

[2025-05-22 21:22:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919560
            [orderIds] => []
        )

)

[2025-05-22 21:22:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919571
            [orderIds] => []
        )

)

[2025-05-22 21:23:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919582
            [orderIds] => []
        )

)

[2025-05-22 21:23:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919593
            [orderIds] => []
        )

)

[2025-05-22 21:23:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919604
            [orderIds] => []
        )

)

[2025-05-22 21:23:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919615
            [orderIds] => []
        )

)

[2025-05-22 21:23:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919626
            [orderIds] => []
        )

)

[2025-05-22 21:23:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919637
            [orderIds] => []
        )

)

[2025-05-22 21:24:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919648
            [orderIds] => []
        )

)

[2025-05-22 21:24:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919659
            [orderIds] => []
        )

)

[2025-05-22 21:24:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919670
            [orderIds] => []
        )

)

[2025-05-22 21:24:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919681
            [orderIds] => []
        )

)

[2025-05-22 21:24:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919692
            [orderIds] => []
        )

)

[2025-05-22 21:25:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919703
            [orderIds] => []
        )

)

[2025-05-22 21:25:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919714
            [orderIds] => []
        )

)

[2025-05-22 21:25:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919725
            [orderIds] => []
        )

)

[2025-05-22 21:25:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919736
            [orderIds] => []
        )

)

[2025-05-22 21:25:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919747
            [orderIds] => []
        )

)

[2025-05-22 21:25:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919758
            [orderIds] => []
        )

)

[2025-05-22 21:26:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919769
            [orderIds] => []
        )

)

[2025-05-22 21:26:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919780
            [orderIds] => []
        )

)

[2025-05-22 21:26:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919791
            [orderIds] => []
        )

)

[2025-05-22 21:26:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919802
            [orderIds] => []
        )

)

[2025-05-22 21:26:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919813
            [orderIds] => []
        )

)

[2025-05-22 21:27:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919824
            [orderIds] => []
        )

)

[2025-05-22 21:27:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919835
            [orderIds] => []
        )

)

[2025-05-22 21:27:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919846
            [orderIds] => []
        )

)

[2025-05-22 21:27:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919857
            [orderIds] => []
        )

)

[2025-05-22 21:27:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919868
            [orderIds] => []
        )

)

[2025-05-22 21:27:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919879
            [orderIds] => []
        )

)

[2025-05-22 21:28:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919890
            [orderIds] => []
        )

)

[2025-05-22 21:28:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919901
            [orderIds] => []
        )

)

[2025-05-22 21:28:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 21:28:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 21:28:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 21:28:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 21:28:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919912
            [orderIds] => []
        )

)

[2025-05-22 21:28:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919923
            [orderIds] => []
        )

)

[2025-05-22 21:28:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919934
            [orderIds] => []
        )

)

[2025-05-22 21:29:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919945
            [orderIds] => []
        )

)

[2025-05-22 21:29:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919956
            [orderIds] => []
        )

)

[2025-05-22 21:29:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919967
            [orderIds] => []
        )

)

[2025-05-22 21:29:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919978
            [orderIds] => []
        )

)

[2025-05-22 21:29:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747919989
            [orderIds] => []
        )

)

[2025-05-22 21:30:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920000
            [orderIds] => []
        )

)

[2025-05-22 21:30:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920011
            [orderIds] => []
        )

)

[2025-05-22 21:30:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920022
            [orderIds] => []
        )

)

[2025-05-22 21:30:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920033
            [orderIds] => []
        )

)

[2025-05-22 21:30:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920044
            [orderIds] => []
        )

)

[2025-05-22 21:30:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920055
            [orderIds] => []
        )

)

[2025-05-22 21:31:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920066
            [orderIds] => []
        )

)

[2025-05-22 21:31:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920077
            [orderIds] => []
        )

)

[2025-05-22 21:31:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920088
            [orderIds] => []
        )

)

[2025-05-22 21:31:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920099
            [orderIds] => []
        )

)

[2025-05-22 21:31:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920110
            [orderIds] => []
        )

)

[2025-05-22 21:32:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920121
            [orderIds] => []
        )

)

[2025-05-22 21:32:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920132
            [orderIds] => []
        )

)

[2025-05-22 21:32:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920143
            [orderIds] => []
        )

)

[2025-05-22 21:32:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920154
            [orderIds] => []
        )

)

[2025-05-22 21:32:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920165
            [orderIds] => []
        )

)

[2025-05-22 21:32:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920176
            [orderIds] => []
        )

)

[2025-05-22 21:33:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920187
            [orderIds] => []
        )

)

[2025-05-22 21:33:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920198
            [orderIds] => []
        )

)

[2025-05-22 21:33:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920209
            [orderIds] => []
        )

)

[2025-05-22 21:33:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920220
            [orderIds] => []
        )

)

[2025-05-22 21:33:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920231
            [orderIds] => []
        )

)

[2025-05-22 21:34:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920242
            [orderIds] => []
        )

)

[2025-05-22 21:34:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920253
            [orderIds] => []
        )

)

[2025-05-22 21:34:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920264
            [orderIds] => []
        )

)

[2025-05-22 21:34:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920275
            [orderIds] => []
        )

)

[2025-05-22 21:34:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920286
            [orderIds] => []
        )

)

[2025-05-22 21:34:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920297
            [orderIds] => []
        )

)

[2025-05-22 21:35:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920308
            [orderIds] => []
        )

)

[2025-05-22 21:35:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920319
            [orderIds] => []
        )

)

[2025-05-22 21:35:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920330
            [orderIds] => []
        )

)

[2025-05-22 21:35:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920341
            [orderIds] => []
        )

)

[2025-05-22 21:35:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920352
            [orderIds] => []
        )

)

[2025-05-22 21:36:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920363
            [orderIds] => []
        )

)

[2025-05-22 21:36:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920374
            [orderIds] => []
        )

)

[2025-05-22 21:36:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920385
            [orderIds] => []
        )

)

[2025-05-22 21:36:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920396
            [orderIds] => []
        )

)

[2025-05-22 21:36:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920407
            [orderIds] => []
        )

)

[2025-05-22 21:36:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920418
            [orderIds] => []
        )

)

[2025-05-22 21:37:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920429
            [orderIds] => []
        )

)

[2025-05-22 21:37:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920440
            [orderIds] => []
        )

)

[2025-05-22 21:37:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920451
            [orderIds] => []
        )

)

[2025-05-22 21:37:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920462
            [orderIds] => []
        )

)

[2025-05-22 21:37:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920473
            [orderIds] => []
        )

)

[2025-05-22 21:38:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920484
            [orderIds] => []
        )

)

[2025-05-22 21:38:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920495
            [orderIds] => []
        )

)

[2025-05-22 21:38:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920506
            [orderIds] => []
        )

)

[2025-05-22 21:38:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 21:38:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 21:38:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 21:38:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 21:38:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920517
            [orderIds] => []
        )

)

[2025-05-22 21:38:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920528
            [orderIds] => []
        )

)

[2025-05-22 21:38:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920539
            [orderIds] => []
        )

)

[2025-05-22 21:39:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920550
            [orderIds] => []
        )

)

[2025-05-22 21:39:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920561
            [orderIds] => []
        )

)

[2025-05-22 21:39:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920572
            [orderIds] => []
        )

)

[2025-05-22 21:39:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920583
            [orderIds] => []
        )

)

[2025-05-22 21:39:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920594
            [orderIds] => []
        )

)

[2025-05-22 21:40:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920605
            [orderIds] => []
        )

)

[2025-05-22 21:40:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920616
            [orderIds] => []
        )

)

[2025-05-22 21:40:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920627
            [orderIds] => []
        )

)

[2025-05-22 21:40:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920638
            [orderIds] => []
        )

)

[2025-05-22 21:40:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920649
            [orderIds] => []
        )

)

[2025-05-22 21:41:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920660
            [orderIds] => []
        )

)

[2025-05-22 21:41:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920671
            [orderIds] => []
        )

)

[2025-05-22 21:41:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920682
            [orderIds] => []
        )

)

[2025-05-22 21:41:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920693
            [orderIds] => []
        )

)

[2025-05-22 21:41:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920704
            [orderIds] => []
        )

)

[2025-05-22 21:41:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920715
            [orderIds] => []
        )

)

[2025-05-22 21:42:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920726
            [orderIds] => []
        )

)

[2025-05-22 21:42:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747834936
            [orderIds] => []
        )

)

[2025-05-22 21:42:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1747316536
            [orderIds] => []
        )

)

[2025-05-22 21:42:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1747316536
            [orderIds] => []
        )

)

[2025-05-22 21:42:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-22 21:42:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-22 21:42:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920737
            [orderIds] => []
        )

)

[2025-05-22 21:42:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-22 21:42:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-22 21:42:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-22 21:42:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920748
            [orderIds] => []
        )

)

[2025-05-22 21:42:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920759
            [orderIds] => []
        )

)

[2025-05-22 21:42:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920770
            [orderIds] => []
        )

)

[2025-05-22 21:43:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920781
            [orderIds] => []
        )

)

[2025-05-22 21:43:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920792
            [orderIds] => []
        )

)

[2025-05-22 21:43:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920803
            [orderIds] => []
        )

)

[2025-05-22 21:43:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920814
            [orderIds] => []
        )

)

[2025-05-22 21:43:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920825
            [orderIds] => []
        )

)

[2025-05-22 21:43:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920836
            [orderIds] => []
        )

)

[2025-05-22 21:44:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920847
            [orderIds] => []
        )

)

[2025-05-22 21:44:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920858
            [orderIds] => []
        )

)

[2025-05-22 21:44:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920869
            [orderIds] => []
        )

)

[2025-05-22 21:44:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920880
            [orderIds] => []
        )

)

[2025-05-22 21:44:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920891
            [orderIds] => []
        )

)

[2025-05-22 21:45:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920902
            [orderIds] => []
        )

)

[2025-05-22 21:45:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920913
            [orderIds] => []
        )

)

[2025-05-22 21:45:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920924
            [orderIds] => []
        )

)

[2025-05-22 21:45:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920935
            [orderIds] => []
        )

)

[2025-05-22 21:45:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920946
            [orderIds] => []
        )

)

[2025-05-22 21:45:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920957
            [orderIds] => []
        )

)

[2025-05-22 21:46:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920968
            [orderIds] => []
        )

)

[2025-05-22 21:46:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920979
            [orderIds] => []
        )

)

[2025-05-22 21:46:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747920990
            [orderIds] => []
        )

)

[2025-05-22 21:46:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921001
            [orderIds] => []
        )

)

[2025-05-22 21:46:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921012
            [orderIds] => []
        )

)

[2025-05-22 21:47:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921023
            [orderIds] => []
        )

)

[2025-05-22 21:47:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921034
            [orderIds] => []
        )

)

[2025-05-22 21:47:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921045
            [orderIds] => []
        )

)

[2025-05-22 21:47:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921056
            [orderIds] => []
        )

)

[2025-05-22 21:47:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921067
            [orderIds] => []
        )

)

[2025-05-22 21:47:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921078
            [orderIds] => []
        )

)

[2025-05-22 21:48:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921089
            [orderIds] => []
        )

)

[2025-05-22 21:48:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921100
            [orderIds] => []
        )

)

[2025-05-22 21:48:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921111
            [orderIds] => []
        )

)

[2025-05-22 21:48:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 21:48:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 21:48:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 21:48:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 21:48:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921122
            [orderIds] => []
        )

)

[2025-05-22 21:48:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921133
            [orderIds] => []
        )

)

[2025-05-22 21:49:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921144
            [orderIds] => []
        )

)

[2025-05-22 21:49:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921155
            [orderIds] => []
        )

)

[2025-05-22 21:49:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921166
            [orderIds] => []
        )

)

[2025-05-22 21:49:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921177
            [orderIds] => []
        )

)

[2025-05-22 21:49:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921188
            [orderIds] => []
        )

)

[2025-05-22 21:49:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921199
            [orderIds] => []
        )

)

[2025-05-22 21:50:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921210
            [orderIds] => []
        )

)

[2025-05-22 21:50:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921221
            [orderIds] => []
        )

)

[2025-05-22 21:50:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921232
            [orderIds] => []
        )

)

[2025-05-22 21:50:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921243
            [orderIds] => []
        )

)

[2025-05-22 21:50:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921254
            [orderIds] => []
        )

)

[2025-05-22 21:51:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921265
            [orderIds] => []
        )

)

[2025-05-22 21:51:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921276
            [orderIds] => []
        )

)

[2025-05-22 21:51:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921287
            [orderIds] => []
        )

)

[2025-05-22 21:51:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921298
            [orderIds] => []
        )

)

[2025-05-22 21:51:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921309
            [orderIds] => []
        )

)

[2025-05-22 21:52:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921320
            [orderIds] => []
        )

)

[2025-05-22 21:52:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921331
            [orderIds] => []
        )

)

[2025-05-22 21:52:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921342
            [orderIds] => []
        )

)

[2025-05-22 21:52:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921353
            [orderIds] => []
        )

)

[2025-05-22 21:52:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921364
            [orderIds] => []
        )

)

[2025-05-22 21:52:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921375
            [orderIds] => []
        )

)

[2025-05-22 21:53:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921386
            [orderIds] => []
        )

)

[2025-05-22 21:53:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921397
            [orderIds] => []
        )

)

[2025-05-22 21:53:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921408
            [orderIds] => []
        )

)

[2025-05-22 21:53:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921419
            [orderIds] => []
        )

)

[2025-05-22 21:53:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921430
            [orderIds] => []
        )

)

[2025-05-22 21:54:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921441
            [orderIds] => []
        )

)

[2025-05-22 21:54:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921452
            [orderIds] => []
        )

)

[2025-05-22 21:54:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921463
            [orderIds] => []
        )

)

[2025-05-22 21:54:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921474
            [orderIds] => []
        )

)

[2025-05-22 21:54:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921485
            [orderIds] => []
        )

)

[2025-05-22 21:54:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921496
            [orderIds] => []
        )

)

[2025-05-22 21:55:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921507
            [orderIds] => []
        )

)

[2025-05-22 21:55:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921518
            [orderIds] => []
        )

)

[2025-05-22 21:55:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921529
            [orderIds] => []
        )

)

[2025-05-22 21:55:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921540
            [orderIds] => []
        )

)

[2025-05-22 21:55:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921551
            [orderIds] => []
        )

)

[2025-05-22 21:56:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921562
            [orderIds] => []
        )

)

[2025-05-22 21:56:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921573
            [orderIds] => []
        )

)

[2025-05-22 21:56:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921584
            [orderIds] => []
        )

)

[2025-05-22 21:56:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921595
            [orderIds] => []
        )

)

[2025-05-22 21:56:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921606
            [orderIds] => []
        )

)

[2025-05-22 21:56:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921617
            [orderIds] => []
        )

)

[2025-05-22 21:57:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921628
            [orderIds] => []
        )

)

[2025-05-22 21:57:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921639
            [orderIds] => []
        )

)

[2025-05-22 21:57:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921650
            [orderIds] => []
        )

)

[2025-05-22 21:57:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921661
            [orderIds] => []
        )

)

[2025-05-22 21:57:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921672
            [orderIds] => []
        )

)

[2025-05-22 21:58:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921683
            [orderIds] => []
        )

)

[2025-05-22 21:58:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921694
            [orderIds] => []
        )

)

[2025-05-22 21:58:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921705
            [orderIds] => []
        )

)

[2025-05-22 21:58:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 21:58:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 21:58:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 21:58:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 21:58:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921716
            [orderIds] => []
        )

)

[2025-05-22 21:58:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921727
            [orderIds] => []
        )

)

[2025-05-22 21:58:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921738
            [orderIds] => []
        )

)

[2025-05-22 21:59:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921749
            [orderIds] => []
        )

)

[2025-05-22 21:59:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921760
            [orderIds] => []
        )

)

[2025-05-22 21:59:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921771
            [orderIds] => []
        )

)

[2025-05-22 21:59:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921782
            [orderIds] => []
        )

)

[2025-05-22 21:59:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921793
            [orderIds] => []
        )

)

[2025-05-22 22:00:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921804
            [orderIds] => []
        )

)

[2025-05-22 22:00:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921815
            [orderIds] => []
        )

)

[2025-05-22 22:00:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921826
            [orderIds] => []
        )

)

[2025-05-22 22:00:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921837
            [orderIds] => []
        )

)

[2025-05-22 22:00:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921848
            [orderIds] => []
        )

)

[2025-05-22 22:00:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921859
            [orderIds] => []
        )

)

[2025-05-22 22:01:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921870
            [orderIds] => []
        )

)

[2025-05-22 22:01:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921881
            [orderIds] => []
        )

)

[2025-05-22 22:01:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921892
            [orderIds] => []
        )

)

[2025-05-22 22:01:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921903
            [orderIds] => []
        )

)

[2025-05-22 22:01:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921914
            [orderIds] => []
        )

)

[2025-05-22 22:02:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921925
            [orderIds] => []
        )

)

[2025-05-22 22:02:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921936
            [orderIds] => []
        )

)

[2025-05-22 22:02:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921947
            [orderIds] => []
        )

)

[2025-05-22 22:02:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921958
            [orderIds] => []
        )

)

[2025-05-22 22:02:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921969
            [orderIds] => []
        )

)

[2025-05-22 22:03:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921980
            [orderIds] => []
        )

)

[2025-05-22 22:03:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747921991
            [orderIds] => []
        )

)

[2025-05-22 22:03:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922002
            [orderIds] => []
        )

)

[2025-05-22 22:03:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922013
            [orderIds] => []
        )

)

[2025-05-22 22:03:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922024
            [orderIds] => []
        )

)

[2025-05-22 22:03:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922035
            [orderIds] => []
        )

)

[2025-05-22 22:04:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922046
            [orderIds] => []
        )

)

[2025-05-22 22:04:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922057
            [orderIds] => []
        )

)

[2025-05-22 22:04:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922068
            [orderIds] => []
        )

)

[2025-05-22 22:04:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922079
            [orderIds] => []
        )

)

[2025-05-22 22:04:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922090
            [orderIds] => []
        )

)

[2025-05-22 22:05:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922101
            [orderIds] => []
        )

)

[2025-05-22 22:05:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922112
            [orderIds] => []
        )

)

[2025-05-22 22:05:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922123
            [orderIds] => []
        )

)

[2025-05-22 22:05:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922134
            [orderIds] => []
        )

)

[2025-05-22 22:05:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922145
            [orderIds] => []
        )

)

[2025-05-22 22:05:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922156
            [orderIds] => []
        )

)

[2025-05-22 22:06:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922167
            [orderIds] => []
        )

)

[2025-05-22 22:06:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922178
            [orderIds] => []
        )

)

[2025-05-22 22:06:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922189
            [orderIds] => []
        )

)

[2025-05-22 22:06:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922200
            [orderIds] => []
        )

)

[2025-05-22 22:06:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922211
            [orderIds] => []
        )

)

[2025-05-22 22:07:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922221
            [orderIds] => []
        )

)

[2025-05-22 22:07:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922231
            [orderIds] => []
        )

)

[2025-05-22 22:07:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922242
            [orderIds] => []
        )

)

[2025-05-22 22:07:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922253
            [orderIds] => []
        )

)

[2025-05-22 22:07:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922264
            [orderIds] => []
        )

)

[2025-05-22 22:07:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922275
            [orderIds] => []
        )

)

[2025-05-22 22:08:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922286
            [orderIds] => []
        )

)

[2025-05-22 22:08:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922297
            [orderIds] => []
        )

)

[2025-05-22 22:08:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922308
            [orderIds] => []
        )

)

[2025-05-22 22:08:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 22:08:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 22:08:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 22:08:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 22:08:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922319
            [orderIds] => []
        )

)

[2025-05-22 22:08:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922330
            [orderIds] => []
        )

)

[2025-05-22 22:09:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922341
            [orderIds] => []
        )

)

[2025-05-22 22:09:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922352
            [orderIds] => []
        )

)

[2025-05-22 22:09:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922363
            [orderIds] => []
        )

)

[2025-05-22 22:09:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922374
            [orderIds] => []
        )

)

[2025-05-22 22:09:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922385
            [orderIds] => []
        )

)

[2025-05-22 22:09:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922396
            [orderIds] => []
        )

)

[2025-05-22 22:10:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922407
            [orderIds] => []
        )

)

[2025-05-22 22:10:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922418
            [orderIds] => []
        )

)

[2025-05-22 22:10:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922429
            [orderIds] => []
        )

)

[2025-05-22 22:10:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922440
            [orderIds] => []
        )

)

[2025-05-22 22:10:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922451
            [orderIds] => []
        )

)

[2025-05-22 22:11:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922462
            [orderIds] => []
        )

)

[2025-05-22 22:11:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922473
            [orderIds] => []
        )

)

[2025-05-22 22:11:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922484
            [orderIds] => []
        )

)

[2025-05-22 22:11:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922495
            [orderIds] => []
        )

)

[2025-05-22 22:11:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922506
            [orderIds] => []
        )

)

[2025-05-22 22:11:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922517
            [orderIds] => []
        )

)

[2025-05-22 22:12:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922528
            [orderIds] => []
        )

)

[2025-05-22 22:12:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747836737
            [orderIds] => []
        )

)

[2025-05-22 22:12:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1747318337
            [orderIds] => []
        )

)

[2025-05-22 22:12:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1747318337
            [orderIds] => []
        )

)

[2025-05-22 22:12:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-22 22:12:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-22 22:12:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-22 22:12:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-22 22:12:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-22 22:12:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922539
            [orderIds] => []
        )

)

[2025-05-22 22:12:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922550
            [orderIds] => []
        )

)

[2025-05-22 22:12:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922561
            [orderIds] => []
        )

)

[2025-05-22 22:12:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922572
            [orderIds] => []
        )

)

[2025-05-22 22:13:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922583
            [orderIds] => []
        )

)

[2025-05-22 22:13:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922594
            [orderIds] => []
        )

)

[2025-05-22 22:13:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922605
            [orderIds] => []
        )

)

[2025-05-22 22:13:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922616
            [orderIds] => []
        )

)

[2025-05-22 22:13:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922627
            [orderIds] => []
        )

)

[2025-05-22 22:13:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922638
            [orderIds] => []
        )

)

[2025-05-22 22:14:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922649
            [orderIds] => []
        )

)

[2025-05-22 22:14:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922660
            [orderIds] => []
        )

)

[2025-05-22 22:14:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922671
            [orderIds] => []
        )

)

[2025-05-22 22:14:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922682
            [orderIds] => []
        )

)

[2025-05-22 22:14:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922693
            [orderIds] => []
        )

)

[2025-05-22 22:15:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922704
            [orderIds] => []
        )

)

[2025-05-22 22:15:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922715
            [orderIds] => []
        )

)

[2025-05-22 22:15:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922726
            [orderIds] => []
        )

)

[2025-05-22 22:15:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922737
            [orderIds] => []
        )

)

[2025-05-22 22:15:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922748
            [orderIds] => []
        )

)

[2025-05-22 22:15:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922759
            [orderIds] => []
        )

)

[2025-05-22 22:16:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922770
            [orderIds] => []
        )

)

[2025-05-22 22:16:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922781
            [orderIds] => []
        )

)

[2025-05-22 22:16:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922792
            [orderIds] => []
        )

)

[2025-05-22 22:16:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922803
            [orderIds] => []
        )

)

[2025-05-22 22:16:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922814
            [orderIds] => []
        )

)

[2025-05-22 22:17:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922825
            [orderIds] => []
        )

)

[2025-05-22 22:17:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922836
            [orderIds] => []
        )

)

[2025-05-22 22:17:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922847
            [orderIds] => []
        )

)

[2025-05-22 22:17:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922858
            [orderIds] => []
        )

)

[2025-05-22 22:17:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922869
            [orderIds] => []
        )

)

[2025-05-22 22:18:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922880
            [orderIds] => []
        )

)

[2025-05-22 22:18:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922891
            [orderIds] => []
        )

)

[2025-05-22 22:18:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922902
            [orderIds] => []
        )

)

[2025-05-22 22:18:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922913
            [orderIds] => []
        )

)

[2025-05-22 22:18:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 22:18:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 22:18:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 22:18:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 22:18:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922924
            [orderIds] => []
        )

)

[2025-05-22 22:18:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922935
            [orderIds] => []
        )

)

[2025-05-22 22:19:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922946
            [orderIds] => []
        )

)

[2025-05-22 22:19:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922957
            [orderIds] => []
        )

)

[2025-05-22 22:19:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922968
            [orderIds] => []
        )

)

[2025-05-22 22:19:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922979
            [orderIds] => []
        )

)

[2025-05-22 22:19:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747922990
            [orderIds] => []
        )

)

[2025-05-22 22:20:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923001
            [orderIds] => []
        )

)

[2025-05-22 22:20:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923012
            [orderIds] => []
        )

)

[2025-05-22 22:20:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923023
            [orderIds] => []
        )

)

[2025-05-22 22:20:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923034
            [orderIds] => []
        )

)

[2025-05-22 22:20:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923045
            [orderIds] => []
        )

)

[2025-05-22 22:20:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923056
            [orderIds] => []
        )

)

[2025-05-22 22:21:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923067
            [orderIds] => []
        )

)

[2025-05-22 22:21:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923078
            [orderIds] => []
        )

)

[2025-05-22 22:21:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923089
            [orderIds] => []
        )

)

[2025-05-22 22:21:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923100
            [orderIds] => []
        )

)

[2025-05-22 22:21:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923111
            [orderIds] => []
        )

)

[2025-05-22 22:22:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923122
            [orderIds] => []
        )

)

[2025-05-22 22:22:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923133
            [orderIds] => []
        )

)

[2025-05-22 22:22:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923144
            [orderIds] => []
        )

)

[2025-05-22 22:22:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923155
            [orderIds] => []
        )

)

[2025-05-22 22:22:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923166
            [orderIds] => []
        )

)

[2025-05-22 22:22:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923177
            [orderIds] => []
        )

)

[2025-05-22 22:23:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923188
            [orderIds] => []
        )

)

[2025-05-22 22:23:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923199
            [orderIds] => []
        )

)

[2025-05-22 22:23:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923210
            [orderIds] => []
        )

)

[2025-05-22 22:23:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923221
            [orderIds] => []
        )

)

[2025-05-22 22:23:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923232
            [orderIds] => []
        )

)

[2025-05-22 22:24:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923243
            [orderIds] => []
        )

)

[2025-05-22 22:24:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923254
            [orderIds] => []
        )

)

[2025-05-22 22:24:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923265
            [orderIds] => []
        )

)

[2025-05-22 22:24:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923276
            [orderIds] => []
        )

)

[2025-05-22 22:24:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923287
            [orderIds] => []
        )

)

[2025-05-22 22:24:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923298
            [orderIds] => []
        )

)

[2025-05-22 22:25:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923309
            [orderIds] => []
        )

)

[2025-05-22 22:25:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923320
            [orderIds] => []
        )

)

[2025-05-22 22:25:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923331
            [orderIds] => []
        )

)

[2025-05-22 22:25:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923342
            [orderIds] => []
        )

)

[2025-05-22 22:25:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923353
            [orderIds] => []
        )

)

[2025-05-22 22:26:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923364
            [orderIds] => []
        )

)

[2025-05-22 22:26:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923375
            [orderIds] => []
        )

)

[2025-05-22 22:26:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923386
            [orderIds] => []
        )

)

[2025-05-22 22:26:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923397
            [orderIds] => []
        )

)

[2025-05-22 22:26:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923408
            [orderIds] => []
        )

)

[2025-05-22 22:26:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923419
            [orderIds] => []
        )

)

[2025-05-22 22:27:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923430
            [orderIds] => []
        )

)

[2025-05-22 22:27:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923441
            [orderIds] => []
        )

)

[2025-05-22 22:27:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923452
            [orderIds] => []
        )

)

[2025-05-22 22:27:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923463
            [orderIds] => []
        )

)

[2025-05-22 22:27:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923474
            [orderIds] => []
        )

)

[2025-05-22 22:28:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923485
            [orderIds] => []
        )

)

[2025-05-22 22:28:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923496
            [orderIds] => []
        )

)

[2025-05-22 22:28:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923507
            [orderIds] => []
        )

)

[2025-05-22 22:28:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 22:28:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 22:28:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 22:28:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 22:28:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923518
            [orderIds] => []
        )

)

[2025-05-22 22:28:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923529
            [orderIds] => []
        )

)

[2025-05-22 22:29:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923540
            [orderIds] => []
        )

)

[2025-05-22 22:29:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923551
            [orderIds] => []
        )

)

[2025-05-22 22:29:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923562
            [orderIds] => []
        )

)

[2025-05-22 22:29:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923573
            [orderIds] => []
        )

)

[2025-05-22 22:29:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923584
            [orderIds] => []
        )

)

[2025-05-22 22:29:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923595
            [orderIds] => []
        )

)

[2025-05-22 22:30:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923606
            [orderIds] => []
        )

)

[2025-05-22 22:30:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923617
            [orderIds] => []
        )

)

[2025-05-22 22:30:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923628
            [orderIds] => []
        )

)

[2025-05-22 22:30:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923639
            [orderIds] => []
        )

)

[2025-05-22 22:30:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923650
            [orderIds] => []
        )

)

[2025-05-22 22:31:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923661
            [orderIds] => []
        )

)

[2025-05-22 22:31:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923672
            [orderIds] => []
        )

)

[2025-05-22 22:31:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923683
            [orderIds] => []
        )

)

[2025-05-22 22:31:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923694
            [orderIds] => []
        )

)

[2025-05-22 22:31:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923705
            [orderIds] => []
        )

)

[2025-05-22 22:31:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923716
            [orderIds] => []
        )

)

[2025-05-22 22:32:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923727
            [orderIds] => []
        )

)

[2025-05-22 22:32:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923738
            [orderIds] => []
        )

)

[2025-05-22 22:32:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923749
            [orderIds] => []
        )

)

[2025-05-22 22:32:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923760
            [orderIds] => []
        )

)

[2025-05-22 22:32:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923771
            [orderIds] => []
        )

)

[2025-05-22 22:33:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923782
            [orderIds] => []
        )

)

[2025-05-22 22:33:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923793
            [orderIds] => []
        )

)

[2025-05-22 22:33:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923804
            [orderIds] => []
        )

)

[2025-05-22 22:33:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923815
            [orderIds] => []
        )

)

[2025-05-22 22:33:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923826
            [orderIds] => []
        )

)

[2025-05-22 22:33:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923837
            [orderIds] => []
        )

)

[2025-05-22 22:34:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923848
            [orderIds] => []
        )

)

[2025-05-22 22:34:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923859
            [orderIds] => []
        )

)

[2025-05-22 22:34:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923870
            [orderIds] => []
        )

)

[2025-05-22 22:34:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923881
            [orderIds] => []
        )

)

[2025-05-22 22:34:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923892
            [orderIds] => []
        )

)

[2025-05-22 22:35:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923903
            [orderIds] => []
        )

)

[2025-05-22 22:35:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923914
            [orderIds] => []
        )

)

[2025-05-22 22:35:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923925
            [orderIds] => []
        )

)

[2025-05-22 22:35:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923936
            [orderIds] => []
        )

)

[2025-05-22 22:35:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923947
            [orderIds] => []
        )

)

[2025-05-22 22:35:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923958
            [orderIds] => []
        )

)

[2025-05-22 22:36:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923969
            [orderIds] => []
        )

)

[2025-05-22 22:36:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923980
            [orderIds] => []
        )

)

[2025-05-22 22:36:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747923991
            [orderIds] => []
        )

)

[2025-05-22 22:36:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924002
            [orderIds] => []
        )

)

[2025-05-22 22:36:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924013
            [orderIds] => []
        )

)

[2025-05-22 22:37:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924024
            [orderIds] => []
        )

)

[2025-05-22 22:37:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924035
            [orderIds] => []
        )

)

[2025-05-22 22:37:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924046
            [orderIds] => []
        )

)

[2025-05-22 22:37:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924057
            [orderIds] => []
        )

)

[2025-05-22 22:37:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924068
            [orderIds] => []
        )

)

[2025-05-22 22:37:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924079
            [orderIds] => []
        )

)

[2025-05-22 22:38:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924090
            [orderIds] => []
        )

)

[2025-05-22 22:38:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924101
            [orderIds] => []
        )

)

[2025-05-22 22:38:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924112
            [orderIds] => []
        )

)

[2025-05-22 22:38:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 22:38:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 22:38:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 22:38:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 22:38:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924123
            [orderIds] => []
        )

)

[2025-05-22 22:38:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924134
            [orderIds] => []
        )

)

[2025-05-22 22:39:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924145
            [orderIds] => []
        )

)

[2025-05-22 22:39:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924156
            [orderIds] => []
        )

)

[2025-05-22 22:39:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924167
            [orderIds] => []
        )

)

[2025-05-22 22:39:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924178
            [orderIds] => []
        )

)

[2025-05-22 22:39:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924189
            [orderIds] => []
        )

)

[2025-05-22 22:40:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924200
            [orderIds] => []
        )

)

[2025-05-22 22:40:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924211
            [orderIds] => []
        )

)

[2025-05-22 22:40:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924222
            [orderIds] => []
        )

)

[2025-05-22 22:40:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924233
            [orderIds] => []
        )

)

[2025-05-22 22:40:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924244
            [orderIds] => []
        )

)

[2025-05-22 22:40:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924255
            [orderIds] => []
        )

)

[2025-05-22 22:41:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924266
            [orderIds] => []
        )

)

[2025-05-22 22:41:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924277
            [orderIds] => []
        )

)

[2025-05-22 22:41:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924288
            [orderIds] => []
        )

)

[2025-05-22 22:41:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924299
            [orderIds] => []
        )

)

[2025-05-22 22:41:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924310
            [orderIds] => []
        )

)

[2025-05-22 22:42:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924321
            [orderIds] => []
        )

)

[2025-05-22 22:42:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924332
            [orderIds] => []
        )

)

[2025-05-22 22:42:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747838538
            [orderIds] => []
        )

)

[2025-05-22 22:42:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1747320138
            [orderIds] => []
        )

)

[2025-05-22 22:42:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1747320138
            [orderIds] => []
        )

)

[2025-05-22 22:42:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-22 22:42:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-22 22:42:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-22 22:42:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-22 22:42:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-22 22:42:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924343
            [orderIds] => []
        )

)

[2025-05-22 22:42:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924354
            [orderIds] => []
        )

)

[2025-05-22 22:42:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924365
            [orderIds] => []
        )

)

[2025-05-22 22:42:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924376
            [orderIds] => []
        )

)

[2025-05-22 22:43:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924387
            [orderIds] => []
        )

)

[2025-05-22 22:43:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924398
            [orderIds] => []
        )

)

[2025-05-22 22:43:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924409
            [orderIds] => []
        )

)

[2025-05-22 22:43:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924420
            [orderIds] => []
        )

)

[2025-05-22 22:43:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924431
            [orderIds] => []
        )

)

[2025-05-22 22:44:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924442
            [orderIds] => []
        )

)

[2025-05-22 22:44:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924453
            [orderIds] => []
        )

)

[2025-05-22 22:44:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924464
            [orderIds] => []
        )

)

[2025-05-22 22:44:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924475
            [orderIds] => []
        )

)

[2025-05-22 22:44:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924486
            [orderIds] => []
        )

)

[2025-05-22 22:44:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924497
            [orderIds] => []
        )

)

[2025-05-22 22:45:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924508
            [orderIds] => []
        )

)

[2025-05-22 22:45:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924519
            [orderIds] => []
        )

)

[2025-05-22 22:45:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924530
            [orderIds] => []
        )

)

[2025-05-22 22:45:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924541
            [orderIds] => []
        )

)

[2025-05-22 22:45:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924552
            [orderIds] => []
        )

)

[2025-05-22 22:46:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924563
            [orderIds] => []
        )

)

[2025-05-22 22:46:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924574
            [orderIds] => []
        )

)

[2025-05-22 22:46:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924585
            [orderIds] => []
        )

)

[2025-05-22 22:46:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924596
            [orderIds] => []
        )

)

[2025-05-22 22:46:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924607
            [orderIds] => []
        )

)

[2025-05-22 22:46:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924618
            [orderIds] => []
        )

)

[2025-05-22 22:47:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924629
            [orderIds] => []
        )

)

[2025-05-22 22:47:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924640
            [orderIds] => []
        )

)

[2025-05-22 22:47:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924651
            [orderIds] => []
        )

)

[2025-05-22 22:47:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924662
            [orderIds] => []
        )

)

[2025-05-22 22:47:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924673
            [orderIds] => []
        )

)

[2025-05-22 22:48:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924684
            [orderIds] => []
        )

)

[2025-05-22 22:48:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924695
            [orderIds] => []
        )

)

[2025-05-22 22:48:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924706
            [orderIds] => []
        )

)

[2025-05-22 22:48:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924717
            [orderIds] => []
        )

)

[2025-05-22 22:48:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 22:48:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 22:48:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 22:48:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 22:48:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924728
            [orderIds] => []
        )

)

[2025-05-22 22:48:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924739
            [orderIds] => []
        )

)

[2025-05-22 22:49:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924750
            [orderIds] => []
        )

)

[2025-05-22 22:49:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924761
            [orderIds] => []
        )

)

[2025-05-22 22:49:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924772
            [orderIds] => []
        )

)

[2025-05-22 22:49:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924783
            [orderIds] => []
        )

)

[2025-05-22 22:49:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924794
            [orderIds] => []
        )

)

[2025-05-22 22:50:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924805
            [orderIds] => []
        )

)

[2025-05-22 22:50:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924816
            [orderIds] => []
        )

)

[2025-05-22 22:50:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924827
            [orderIds] => []
        )

)

[2025-05-22 22:50:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924838
            [orderIds] => []
        )

)

[2025-05-22 22:50:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924849
            [orderIds] => []
        )

)

[2025-05-22 22:51:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924860
            [orderIds] => []
        )

)

[2025-05-22 22:51:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924871
            [orderIds] => []
        )

)

[2025-05-22 22:51:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924882
            [orderIds] => []
        )

)

[2025-05-22 22:51:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924893
            [orderIds] => []
        )

)

[2025-05-22 22:51:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924904
            [orderIds] => []
        )

)

[2025-05-22 22:51:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924915
            [orderIds] => []
        )

)

[2025-05-22 22:52:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924926
            [orderIds] => []
        )

)

[2025-05-22 22:52:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924937
            [orderIds] => []
        )

)

[2025-05-22 22:52:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924948
            [orderIds] => []
        )

)

[2025-05-22 22:52:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924959
            [orderIds] => []
        )

)

[2025-05-22 22:52:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924970
            [orderIds] => []
        )

)

[2025-05-22 22:53:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924981
            [orderIds] => []
        )

)

[2025-05-22 22:53:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747924992
            [orderIds] => []
        )

)

[2025-05-22 22:53:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925003
            [orderIds] => []
        )

)

[2025-05-22 22:53:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925014
            [orderIds] => []
        )

)

[2025-05-22 22:53:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925025
            [orderIds] => []
        )

)

[2025-05-22 22:53:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925036
            [orderIds] => []
        )

)

[2025-05-22 22:54:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925047
            [orderIds] => []
        )

)

[2025-05-22 22:54:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925058
            [orderIds] => []
        )

)

[2025-05-22 22:54:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925069
            [orderIds] => []
        )

)

[2025-05-22 22:54:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925080
            [orderIds] => []
        )

)

[2025-05-22 22:54:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925091
            [orderIds] => []
        )

)

[2025-05-22 22:55:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925102
            [orderIds] => []
        )

)

[2025-05-22 22:55:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925113
            [orderIds] => []
        )

)

[2025-05-22 22:55:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925124
            [orderIds] => []
        )

)

[2025-05-22 22:55:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925135
            [orderIds] => []
        )

)

[2025-05-22 22:55:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925146
            [orderIds] => []
        )

)

[2025-05-22 22:55:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925157
            [orderIds] => []
        )

)

[2025-05-22 22:56:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925168
            [orderIds] => []
        )

)

[2025-05-22 22:56:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925179
            [orderIds] => []
        )

)

[2025-05-22 22:56:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925190
            [orderIds] => []
        )

)

[2025-05-22 22:56:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925201
            [orderIds] => []
        )

)

[2025-05-22 22:56:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925212
            [orderIds] => []
        )

)

[2025-05-22 22:57:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925223
            [orderIds] => []
        )

)

[2025-05-22 22:57:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925234
            [orderIds] => []
        )

)

[2025-05-22 22:57:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925245
            [orderIds] => []
        )

)

[2025-05-22 22:57:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925256
            [orderIds] => []
        )

)

[2025-05-22 22:57:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925267
            [orderIds] => []
        )

)

[2025-05-22 22:57:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925278
            [orderIds] => []
        )

)

[2025-05-22 22:58:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925289
            [orderIds] => []
        )

)

[2025-05-22 22:58:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925300
            [orderIds] => []
        )

)

[2025-05-22 22:58:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925311
            [orderIds] => []
        )

)

[2025-05-22 22:58:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 22:58:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 22:58:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 22:58:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 22:58:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925322
            [orderIds] => []
        )

)

[2025-05-22 22:58:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925333
            [orderIds] => []
        )

)

[2025-05-22 22:59:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925344
            [orderIds] => []
        )

)

[2025-05-22 22:59:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925355
            [orderIds] => []
        )

)

[2025-05-22 22:59:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925366
            [orderIds] => []
        )

)

[2025-05-22 22:59:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925377
            [orderIds] => []
        )

)

[2025-05-22 22:59:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925388
            [orderIds] => []
        )

)

[2025-05-22 22:59:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925399
            [orderIds] => []
        )

)

[2025-05-22 23:00:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925410
            [orderIds] => []
        )

)

[2025-05-22 23:00:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925421
            [orderIds] => []
        )

)

[2025-05-22 23:00:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925432
            [orderIds] => []
        )

)

[2025-05-22 23:00:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925443
            [orderIds] => []
        )

)

[2025-05-22 23:00:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925454
            [orderIds] => []
        )

)

[2025-05-22 23:01:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925465
            [orderIds] => []
        )

)

[2025-05-22 23:01:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925476
            [orderIds] => []
        )

)

[2025-05-22 23:01:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925487
            [orderIds] => []
        )

)

[2025-05-22 23:01:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925498
            [orderIds] => []
        )

)

[2025-05-22 23:01:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925509
            [orderIds] => []
        )

)

[2025-05-22 23:02:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925520
            [orderIds] => []
        )

)

[2025-05-22 23:02:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925531
            [orderIds] => []
        )

)

[2025-05-22 23:02:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925542
            [orderIds] => []
        )

)

[2025-05-22 23:02:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925553
            [orderIds] => []
        )

)

[2025-05-22 23:02:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925564
            [orderIds] => []
        )

)

[2025-05-22 23:02:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925575
            [orderIds] => []
        )

)

[2025-05-22 23:03:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925586
            [orderIds] => []
        )

)

[2025-05-22 23:03:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925597
            [orderIds] => []
        )

)

[2025-05-22 23:03:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925608
            [orderIds] => []
        )

)

[2025-05-22 23:03:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925619
            [orderIds] => []
        )

)

[2025-05-22 23:03:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925630
            [orderIds] => []
        )

)

[2025-05-22 23:04:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925641
            [orderIds] => []
        )

)

[2025-05-22 23:04:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925652
            [orderIds] => []
        )

)

[2025-05-22 23:04:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925663
            [orderIds] => []
        )

)

[2025-05-22 23:04:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925674
            [orderIds] => []
        )

)

[2025-05-22 23:04:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925685
            [orderIds] => []
        )

)

[2025-05-22 23:04:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925696
            [orderIds] => []
        )

)

[2025-05-22 23:05:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925707
            [orderIds] => []
        )

)

[2025-05-22 23:05:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925718
            [orderIds] => []
        )

)

[2025-05-22 23:05:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925729
            [orderIds] => []
        )

)

[2025-05-22 23:05:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925740
            [orderIds] => []
        )

)

[2025-05-22 23:05:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925751
            [orderIds] => []
        )

)

[2025-05-22 23:06:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925762
            [orderIds] => []
        )

)

[2025-05-22 23:06:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925773
            [orderIds] => []
        )

)

[2025-05-22 23:06:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925784
            [orderIds] => []
        )

)

[2025-05-22 23:06:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925795
            [orderIds] => []
        )

)

[2025-05-22 23:06:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925806
            [orderIds] => []
        )

)

[2025-05-22 23:06:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925817
            [orderIds] => []
        )

)

[2025-05-22 23:07:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925828
            [orderIds] => []
        )

)

[2025-05-22 23:07:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925839
            [orderIds] => []
        )

)

[2025-05-22 23:07:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925850
            [orderIds] => []
        )

)

[2025-05-22 23:07:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925861
            [orderIds] => []
        )

)

[2025-05-22 23:07:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925872
            [orderIds] => []
        )

)

[2025-05-22 23:08:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925883
            [orderIds] => []
        )

)

[2025-05-22 23:08:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925894
            [orderIds] => []
        )

)

[2025-05-22 23:08:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925905
            [orderIds] => []
        )

)

[2025-05-22 23:08:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925916
            [orderIds] => []
        )

)

[2025-05-22 23:08:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 23:08:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 23:08:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 23:08:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 23:08:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925927
            [orderIds] => []
        )

)

[2025-05-22 23:08:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925938
            [orderIds] => []
        )

)

[2025-05-22 23:09:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925949
            [orderIds] => []
        )

)

[2025-05-22 23:09:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925960
            [orderIds] => []
        )

)

[2025-05-22 23:09:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925971
            [orderIds] => []
        )

)

[2025-05-22 23:09:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925982
            [orderIds] => []
        )

)

[2025-05-22 23:09:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747925993
            [orderIds] => []
        )

)

[2025-05-22 23:10:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926004
            [orderIds] => []
        )

)

[2025-05-22 23:10:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926015
            [orderIds] => []
        )

)

[2025-05-22 23:10:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926026
            [orderIds] => []
        )

)

[2025-05-22 23:10:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926037
            [orderIds] => []
        )

)

[2025-05-22 23:10:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926048
            [orderIds] => []
        )

)

[2025-05-22 23:10:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926059
            [orderIds] => []
        )

)

[2025-05-22 23:11:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926070
            [orderIds] => []
        )

)

[2025-05-22 23:11:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926081
            [orderIds] => []
        )

)

[2025-05-22 23:11:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926092
            [orderIds] => []
        )

)

[2025-05-22 23:11:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926103
            [orderIds] => []
        )

)

[2025-05-22 23:11:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926114
            [orderIds] => []
        )

)

[2025-05-22 23:12:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926125
            [orderIds] => []
        )

)

[2025-05-22 23:12:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926136
            [orderIds] => []
        )

)

[2025-05-22 23:12:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747840339
            [orderIds] => []
        )

)

[2025-05-22 23:12:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1747321939
            [orderIds] => []
        )

)

[2025-05-22 23:12:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1747321939
            [orderIds] => []
        )

)

[2025-05-22 23:12:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-22 23:12:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-22 23:12:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-22 23:12:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-22 23:12:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-22 23:12:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926147
            [orderIds] => []
        )

)

[2025-05-22 23:12:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926158
            [orderIds] => []
        )

)

[2025-05-22 23:12:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926169
            [orderIds] => []
        )

)

[2025-05-22 23:13:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926180
            [orderIds] => []
        )

)

[2025-05-22 23:13:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926191
            [orderIds] => []
        )

)

[2025-05-22 23:13:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926202
            [orderIds] => []
        )

)

[2025-05-22 23:13:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926213
            [orderIds] => []
        )

)

[2025-05-22 23:13:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926224
            [orderIds] => []
        )

)

[2025-05-22 23:13:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926235
            [orderIds] => []
        )

)

[2025-05-22 23:14:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926246
            [orderIds] => []
        )

)

[2025-05-22 23:14:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926257
            [orderIds] => []
        )

)

[2025-05-22 23:14:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926268
            [orderIds] => []
        )

)

[2025-05-22 23:14:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926279
            [orderIds] => []
        )

)

[2025-05-22 23:14:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926290
            [orderIds] => []
        )

)

[2025-05-22 23:15:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926301
            [orderIds] => []
        )

)

[2025-05-22 23:15:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926312
            [orderIds] => []
        )

)

[2025-05-22 23:15:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926323
            [orderIds] => []
        )

)

[2025-05-22 23:15:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926334
            [orderIds] => []
        )

)

[2025-05-22 23:15:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926345
            [orderIds] => []
        )

)

[2025-05-22 23:15:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926356
            [orderIds] => []
        )

)

[2025-05-22 23:16:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926367
            [orderIds] => []
        )

)

[2025-05-22 23:16:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926378
            [orderIds] => []
        )

)

[2025-05-22 23:16:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926389
            [orderIds] => []
        )

)

[2025-05-22 23:16:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926400
            [orderIds] => []
        )

)

[2025-05-22 23:16:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926411
            [orderIds] => []
        )

)

[2025-05-22 23:17:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926422
            [orderIds] => []
        )

)

[2025-05-22 23:17:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926433
            [orderIds] => []
        )

)

[2025-05-22 23:17:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926444
            [orderIds] => []
        )

)

[2025-05-22 23:17:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926455
            [orderIds] => []
        )

)

[2025-05-22 23:17:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926466
            [orderIds] => []
        )

)

[2025-05-22 23:17:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926477
            [orderIds] => []
        )

)

[2025-05-22 23:18:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926488
            [orderIds] => []
        )

)

[2025-05-22 23:18:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926499
            [orderIds] => []
        )

)

[2025-05-22 23:18:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926510
            [orderIds] => []
        )

)

[2025-05-22 23:18:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926521
            [orderIds] => []
        )

)

[2025-05-22 23:18:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 23:18:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 23:18:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 23:18:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 23:18:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926532
            [orderIds] => []
        )

)

[2025-05-22 23:19:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926543
            [orderIds] => []
        )

)

[2025-05-22 23:19:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926554
            [orderIds] => []
        )

)

[2025-05-22 23:19:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926565
            [orderIds] => []
        )

)

[2025-05-22 23:19:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926576
            [orderIds] => []
        )

)

[2025-05-22 23:19:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926587
            [orderIds] => []
        )

)

[2025-05-22 23:19:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926598
            [orderIds] => []
        )

)

[2025-05-22 23:20:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926609
            [orderIds] => []
        )

)

[2025-05-22 23:20:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926620
            [orderIds] => []
        )

)

[2025-05-22 23:20:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926631
            [orderIds] => []
        )

)

[2025-05-22 23:20:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926642
            [orderIds] => []
        )

)

[2025-05-22 23:20:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926653
            [orderIds] => []
        )

)

[2025-05-22 23:21:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926664
            [orderIds] => []
        )

)

[2025-05-22 23:21:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926675
            [orderIds] => []
        )

)

[2025-05-22 23:21:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926686
            [orderIds] => []
        )

)

[2025-05-22 23:21:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926697
            [orderIds] => []
        )

)

[2025-05-22 23:21:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926708
            [orderIds] => []
        )

)

[2025-05-22 23:21:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926719
            [orderIds] => []
        )

)

[2025-05-22 23:22:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926730
            [orderIds] => []
        )

)

[2025-05-22 23:22:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926741
            [orderIds] => []
        )

)

[2025-05-22 23:22:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926752
            [orderIds] => []
        )

)

[2025-05-22 23:22:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926763
            [orderIds] => []
        )

)

[2025-05-22 23:22:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926774
            [orderIds] => []
        )

)

[2025-05-22 23:23:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926785
            [orderIds] => []
        )

)

[2025-05-22 23:23:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926796
            [orderIds] => []
        )

)

[2025-05-22 23:23:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926807
            [orderIds] => []
        )

)

[2025-05-22 23:23:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926818
            [orderIds] => []
        )

)

[2025-05-22 23:23:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926829
            [orderIds] => []
        )

)

[2025-05-22 23:24:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926840
            [orderIds] => []
        )

)

[2025-05-22 23:24:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926851
            [orderIds] => []
        )

)

[2025-05-22 23:24:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926862
            [orderIds] => []
        )

)

[2025-05-22 23:24:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926873
            [orderIds] => []
        )

)

[2025-05-22 23:24:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926884
            [orderIds] => []
        )

)

[2025-05-22 23:24:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926895
            [orderIds] => []
        )

)

[2025-05-22 23:25:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926906
            [orderIds] => []
        )

)

[2025-05-22 23:25:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926917
            [orderIds] => []
        )

)

[2025-05-22 23:25:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926928
            [orderIds] => []
        )

)

[2025-05-22 23:25:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926939
            [orderIds] => []
        )

)

[2025-05-22 23:25:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926950
            [orderIds] => []
        )

)

[2025-05-22 23:26:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926961
            [orderIds] => []
        )

)

[2025-05-22 23:26:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926972
            [orderIds] => []
        )

)

[2025-05-22 23:26:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926983
            [orderIds] => []
        )

)

[2025-05-22 23:26:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747926994
            [orderIds] => []
        )

)

[2025-05-22 23:26:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927005
            [orderIds] => []
        )

)

[2025-05-22 23:26:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927016
            [orderIds] => []
        )

)

[2025-05-22 23:27:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927027
            [orderIds] => []
        )

)

[2025-05-22 23:27:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927038
            [orderIds] => []
        )

)

[2025-05-22 23:27:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927049
            [orderIds] => []
        )

)

[2025-05-22 23:27:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927060
            [orderIds] => []
        )

)

[2025-05-22 23:27:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927071
            [orderIds] => []
        )

)

[2025-05-22 23:28:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927082
            [orderIds] => []
        )

)

[2025-05-22 23:28:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927093
            [orderIds] => []
        )

)

[2025-05-22 23:28:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927104
            [orderIds] => []
        )

)

[2025-05-22 23:28:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927115
            [orderIds] => []
        )

)

[2025-05-22 23:28:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 23:28:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 23:28:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 23:28:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 23:28:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927126
            [orderIds] => []
        )

)

[2025-05-22 23:28:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927137
            [orderIds] => []
        )

)

[2025-05-22 23:29:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927148
            [orderIds] => []
        )

)

[2025-05-22 23:29:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927159
            [orderIds] => []
        )

)

[2025-05-22 23:29:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927170
            [orderIds] => []
        )

)

[2025-05-22 23:29:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927181
            [orderIds] => []
        )

)

[2025-05-22 23:29:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927192
            [orderIds] => []
        )

)

[2025-05-22 23:30:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927203
            [orderIds] => []
        )

)

[2025-05-22 23:30:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927214
            [orderIds] => []
        )

)

[2025-05-22 23:30:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927225
            [orderIds] => []
        )

)

[2025-05-22 23:30:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927236
            [orderIds] => []
        )

)

[2025-05-22 23:30:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927247
            [orderIds] => []
        )

)

[2025-05-22 23:30:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927258
            [orderIds] => []
        )

)

[2025-05-22 23:31:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927269
            [orderIds] => []
        )

)

[2025-05-22 23:31:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927280
            [orderIds] => []
        )

)

[2025-05-22 23:31:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927291
            [orderIds] => []
        )

)

[2025-05-22 23:31:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927302
            [orderIds] => []
        )

)

[2025-05-22 23:31:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927313
            [orderIds] => []
        )

)

[2025-05-22 23:32:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927324
            [orderIds] => []
        )

)

[2025-05-22 23:32:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927335
            [orderIds] => []
        )

)

[2025-05-22 23:32:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927346
            [orderIds] => []
        )

)

[2025-05-22 23:32:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927357
            [orderIds] => []
        )

)

[2025-05-22 23:32:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927368
            [orderIds] => []
        )

)

[2025-05-22 23:32:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927379
            [orderIds] => []
        )

)

[2025-05-22 23:33:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927390
            [orderIds] => []
        )

)

[2025-05-22 23:33:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927401
            [orderIds] => []
        )

)

[2025-05-22 23:33:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927412
            [orderIds] => []
        )

)

[2025-05-22 23:33:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927423
            [orderIds] => []
        )

)

[2025-05-22 23:33:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927434
            [orderIds] => []
        )

)

[2025-05-22 23:34:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927445
            [orderIds] => []
        )

)

[2025-05-22 23:34:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927456
            [orderIds] => []
        )

)

[2025-05-22 23:34:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927467
            [orderIds] => []
        )

)

[2025-05-22 23:34:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927478
            [orderIds] => []
        )

)

[2025-05-22 23:34:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927489
            [orderIds] => []
        )

)

[2025-05-22 23:35:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927500
            [orderIds] => []
        )

)

[2025-05-22 23:35:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927511
            [orderIds] => []
        )

)

[2025-05-22 23:35:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927522
            [orderIds] => []
        )

)

[2025-05-22 23:35:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927533
            [orderIds] => []
        )

)

[2025-05-22 23:35:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927544
            [orderIds] => []
        )

)

[2025-05-22 23:35:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927555
            [orderIds] => []
        )

)

[2025-05-22 23:36:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927566
            [orderIds] => []
        )

)

[2025-05-22 23:36:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927577
            [orderIds] => []
        )

)

[2025-05-22 23:36:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927588
            [orderIds] => []
        )

)

[2025-05-22 23:36:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927599
            [orderIds] => []
        )

)

[2025-05-22 23:36:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927610
            [orderIds] => []
        )

)

[2025-05-22 23:37:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927621
            [orderIds] => []
        )

)

[2025-05-22 23:37:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927632
            [orderIds] => []
        )

)

[2025-05-22 23:37:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927643
            [orderIds] => []
        )

)

[2025-05-22 23:37:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927654
            [orderIds] => []
        )

)

[2025-05-22 23:37:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927665
            [orderIds] => []
        )

)

[2025-05-22 23:37:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927676
            [orderIds] => []
        )

)

[2025-05-22 23:38:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927687
            [orderIds] => []
        )

)

[2025-05-22 23:38:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927698
            [orderIds] => []
        )

)

[2025-05-22 23:38:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927709
            [orderIds] => []
        )

)

[2025-05-22 23:38:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927720
            [orderIds] => []
        )

)

[2025-05-22 23:38:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 23:38:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 23:38:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 23:38:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 23:38:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927731
            [orderIds] => []
        )

)

[2025-05-22 23:39:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927742
            [orderIds] => []
        )

)

[2025-05-22 23:39:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927753
            [orderIds] => []
        )

)

[2025-05-22 23:39:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927764
            [orderIds] => []
        )

)

[2025-05-22 23:39:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927775
            [orderIds] => []
        )

)

[2025-05-22 23:39:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927786
            [orderIds] => []
        )

)

[2025-05-22 23:39:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927797
            [orderIds] => []
        )

)

[2025-05-22 23:40:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927808
            [orderIds] => []
        )

)

[2025-05-22 23:40:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927819
            [orderIds] => []
        )

)

[2025-05-22 23:40:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927830
            [orderIds] => []
        )

)

[2025-05-22 23:40:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927841
            [orderIds] => []
        )

)

[2025-05-22 23:40:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927852
            [orderIds] => []
        )

)

[2025-05-22 23:41:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927863
            [orderIds] => []
        )

)

[2025-05-22 23:41:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927874
            [orderIds] => []
        )

)

[2025-05-22 23:41:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927885
            [orderIds] => []
        )

)

[2025-05-22 23:41:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927896
            [orderIds] => []
        )

)

[2025-05-22 23:41:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927907
            [orderIds] => []
        )

)

[2025-05-22 23:41:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927918
            [orderIds] => []
        )

)

[2025-05-22 23:42:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927929
            [orderIds] => []
        )

)

[2025-05-22 23:42:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1747842140
            [orderIds] => []
        )

)

[2025-05-22 23:42:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1747323740
            [orderIds] => []
        )

)

[2025-05-22 23:42:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1747323740
            [orderIds] => []
        )

)

[2025-05-22 23:42:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927940
            [orderIds] => []
        )

)

[2025-05-22 23:42:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-05-22 23:42:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-05-22 23:42:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-05-22 23:42:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-05-22 23:42:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-05-22 23:42:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927951
            [orderIds] => []
        )

)

[2025-05-22 23:42:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927962
            [orderIds] => []
        )

)

[2025-05-22 23:42:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927973
            [orderIds] => []
        )

)

[2025-05-22 23:43:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927984
            [orderIds] => []
        )

)

[2025-05-22 23:43:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747927995
            [orderIds] => []
        )

)

[2025-05-22 23:43:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928006
            [orderIds] => []
        )

)

[2025-05-22 23:43:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928017
            [orderIds] => []
        )

)

[2025-05-22 23:43:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928028
            [orderIds] => []
        )

)

[2025-05-22 23:43:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928039
            [orderIds] => []
        )

)

[2025-05-22 23:44:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928050
            [orderIds] => []
        )

)

[2025-05-22 23:44:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928061
            [orderIds] => []
        )

)

[2025-05-22 23:44:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928072
            [orderIds] => []
        )

)

[2025-05-22 23:44:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928083
            [orderIds] => []
        )

)

[2025-05-22 23:44:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928094
            [orderIds] => []
        )

)

[2025-05-22 23:45:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928105
            [orderIds] => []
        )

)

[2025-05-22 23:45:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928116
            [orderIds] => []
        )

)

[2025-05-22 23:45:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928127
            [orderIds] => []
        )

)

[2025-05-22 23:45:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928138
            [orderIds] => []
        )

)

[2025-05-22 23:45:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928149
            [orderIds] => []
        )

)

[2025-05-22 23:46:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928160
            [orderIds] => []
        )

)

[2025-05-22 23:46:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928171
            [orderIds] => []
        )

)

[2025-05-22 23:46:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928182
            [orderIds] => []
        )

)

[2025-05-22 23:46:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928193
            [orderIds] => []
        )

)

[2025-05-22 23:46:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928204
            [orderIds] => []
        )

)

[2025-05-22 23:46:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928215
            [orderIds] => []
        )

)

[2025-05-22 23:47:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928226
            [orderIds] => []
        )

)

[2025-05-22 23:47:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928237
            [orderIds] => []
        )

)

[2025-05-22 23:47:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928248
            [orderIds] => []
        )

)

[2025-05-22 23:47:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928259
            [orderIds] => []
        )

)

[2025-05-22 23:47:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928270
            [orderIds] => []
        )

)

[2025-05-22 23:48:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928281
            [orderIds] => []
        )

)

[2025-05-22 23:48:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928292
            [orderIds] => []
        )

)

[2025-05-22 23:48:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928303
            [orderIds] => []
        )

)

[2025-05-22 23:48:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928314
            [orderIds] => []
        )

)

[2025-05-22 23:48:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 23:48:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 23:48:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 23:48:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 23:48:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928325
            [orderIds] => []
        )

)

[2025-05-22 23:48:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928336
            [orderIds] => []
        )

)

[2025-05-22 23:49:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928347
            [orderIds] => []
        )

)

[2025-05-22 23:49:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928358
            [orderIds] => []
        )

)

[2025-05-22 23:49:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928369
            [orderIds] => []
        )

)

[2025-05-22 23:49:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928380
            [orderIds] => []
        )

)

[2025-05-22 23:49:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928391
            [orderIds] => []
        )

)

[2025-05-22 23:50:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928402
            [orderIds] => []
        )

)

[2025-05-22 23:50:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928413
            [orderIds] => []
        )

)

[2025-05-22 23:50:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928424
            [orderIds] => []
        )

)

[2025-05-22 23:50:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928435
            [orderIds] => []
        )

)

[2025-05-22 23:50:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928446
            [orderIds] => []
        )

)

[2025-05-22 23:50:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928457
            [orderIds] => []
        )

)

[2025-05-22 23:51:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928468
            [orderIds] => []
        )

)

[2025-05-22 23:51:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928479
            [orderIds] => []
        )

)

[2025-05-22 23:51:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928490
            [orderIds] => []
        )

)

[2025-05-22 23:51:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928501
            [orderIds] => []
        )

)

[2025-05-22 23:51:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928512
            [orderIds] => []
        )

)

[2025-05-22 23:52:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928523
            [orderIds] => []
        )

)

[2025-05-22 23:52:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928534
            [orderIds] => []
        )

)

[2025-05-22 23:52:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928545
            [orderIds] => []
        )

)

[2025-05-22 23:52:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928556
            [orderIds] => []
        )

)

[2025-05-22 23:52:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928567
            [orderIds] => []
        )

)

[2025-05-22 23:52:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928578
            [orderIds] => []
        )

)

[2025-05-22 23:53:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928589
            [orderIds] => []
        )

)

[2025-05-22 23:53:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928600
            [orderIds] => []
        )

)

[2025-05-22 23:53:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928611
            [orderIds] => []
        )

)

[2025-05-22 23:53:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928622
            [orderIds] => []
        )

)

[2025-05-22 23:53:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928633
            [orderIds] => []
        )

)

[2025-05-22 23:54:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928644
            [orderIds] => []
        )

)

[2025-05-22 23:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928655
            [orderIds] => []
        )

)

[2025-05-22 23:54:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928666
            [orderIds] => []
        )

)

[2025-05-22 23:54:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928677
            [orderIds] => []
        )

)

[2025-05-22 23:54:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928688
            [orderIds] => []
        )

)

[2025-05-22 23:54:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928699
            [orderIds] => []
        )

)

[2025-05-22 23:55:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928710
            [orderIds] => []
        )

)

[2025-05-22 23:55:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928721
            [orderIds] => []
        )

)

[2025-05-22 23:55:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928732
            [orderIds] => []
        )

)

[2025-05-22 23:55:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928743
            [orderIds] => []
        )

)

[2025-05-22 23:55:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928754
            [orderIds] => []
        )

)

[2025-05-22 23:56:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928765
            [orderIds] => []
        )

)

[2025-05-22 23:56:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928776
            [orderIds] => []
        )

)

[2025-05-22 23:56:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928787
            [orderIds] => []
        )

)

[2025-05-22 23:56:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928798
            [orderIds] => []
        )

)

[2025-05-22 23:56:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928809
            [orderIds] => []
        )

)

[2025-05-22 23:57:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928820
            [orderIds] => []
        )

)

[2025-05-22 23:57:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928831
            [orderIds] => []
        )

)

[2025-05-22 23:57:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928842
            [orderIds] => []
        )

)

[2025-05-22 23:57:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928853
            [orderIds] => []
        )

)

[2025-05-22 23:57:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928864
            [orderIds] => []
        )

)

[2025-05-22 23:57:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928875
            [orderIds] => []
        )

)

[2025-05-22 23:58:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928886
            [orderIds] => []
        )

)

[2025-05-22 23:58:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928897
            [orderIds] => []
        )

)

[2025-05-22 23:58:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928908
            [orderIds] => []
        )

)

[2025-05-22 23:58:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928919
            [orderIds] => []
        )

)

[2025-05-22 23:58:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-05-22 23:58:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 23:58:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-05-22 23:58:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-05-22 23:58:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928930
            [orderIds] => []
        )

)

[2025-05-22 23:59:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928941
            [orderIds] => []
        )

)

[2025-05-22 23:59:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928952
            [orderIds] => []
        )

)

[2025-05-22 23:59:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928963
            [orderIds] => []
        )

)

[2025-05-22 23:59:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928974
            [orderIds] => []
        )

)

[2025-05-22 23:59:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928985
            [orderIds] => []
        )

)

[2025-05-22 23:59:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1747928996
            [orderIds] => []
        )

)

