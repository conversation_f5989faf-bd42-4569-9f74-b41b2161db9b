[2025-07-14 19:24:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491695
            [orderIds] => []
        )

)

[2025-07-14 19:25:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491706
            [orderIds] => []
        )

)

[2025-07-14 19:25:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 19:25:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 19:25:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 19:25:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 19:25:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491717
            [orderIds] => []
        )

)

[2025-07-14 19:25:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491728
            [orderIds] => []
        )

)

[2025-07-14 19:25:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491739
            [orderIds] => []
        )

)

[2025-07-14 19:25:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491750
            [orderIds] => []
        )

)

[2025-07-14 19:26:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491761
            [orderIds] => []
        )

)

[2025-07-14 19:26:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491772
            [orderIds] => []
        )

)

[2025-07-14 19:26:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491783
            [orderIds] => []
        )

)

[2025-07-14 19:26:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491794
            [orderIds] => []
        )

)

[2025-07-14 19:26:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491805
            [orderIds] => []
        )

)

[2025-07-14 19:26:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491816
            [orderIds] => []
        )

)

[2025-07-14 19:27:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491827
            [orderIds] => []
        )

)

[2025-07-14 19:27:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491838
            [orderIds] => []
        )

)

[2025-07-14 19:27:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491849
            [orderIds] => []
        )

)

[2025-07-14 19:27:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491860
            [orderIds] => []
        )

)

[2025-07-14 19:27:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491871
            [orderIds] => []
        )

)

[2025-07-14 19:28:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491882
            [orderIds] => []
        )

)

[2025-07-14 19:28:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491893
            [orderIds] => []
        )

)

[2025-07-14 19:28:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491904
            [orderIds] => []
        )

)

[2025-07-14 19:28:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491915
            [orderIds] => []
        )

)

[2025-07-14 19:28:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491926
            [orderIds] => []
        )

)

[2025-07-14 19:28:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491937
            [orderIds] => []
        )

)

[2025-07-14 19:29:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491948
            [orderIds] => []
        )

)

[2025-07-14 19:29:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491959
            [orderIds] => []
        )

)

[2025-07-14 19:29:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491970
            [orderIds] => []
        )

)

[2025-07-14 19:29:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491981
            [orderIds] => []
        )

)

[2025-07-14 19:29:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752491992
            [orderIds] => []
        )

)

[2025-07-14 19:30:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492003
            [orderIds] => []
        )

)

[2025-07-14 19:30:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492014
            [orderIds] => []
        )

)

[2025-07-14 19:30:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492025
            [orderIds] => []
        )

)

[2025-07-14 19:30:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492036
            [orderIds] => []
        )

)

[2025-07-14 19:30:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492047
            [orderIds] => []
        )

)

[2025-07-14 19:30:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492058
            [orderIds] => []
        )

)

[2025-07-14 19:31:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492069
            [orderIds] => []
        )

)

[2025-07-14 19:31:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492080
            [orderIds] => []
        )

)

[2025-07-14 19:31:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492091
            [orderIds] => []
        )

)

[2025-07-14 19:31:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492102
            [orderIds] => []
        )

)

[2025-07-14 19:31:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492113
            [orderIds] => []
        )

)

[2025-07-14 19:32:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492124
            [orderIds] => []
        )

)

[2025-07-14 19:32:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492135
            [orderIds] => []
        )

)

[2025-07-14 19:32:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492146
            [orderIds] => []
        )

)

[2025-07-14 19:32:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492157
            [orderIds] => []
        )

)

[2025-07-14 19:32:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492168
            [orderIds] => []
        )

)

[2025-07-14 19:32:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492179
            [orderIds] => []
        )

)

[2025-07-14 19:33:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492190
            [orderIds] => []
        )

)

[2025-07-14 19:33:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492201
            [orderIds] => []
        )

)

[2025-07-14 19:33:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492212
            [orderIds] => []
        )

)

[2025-07-14 19:33:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492223
            [orderIds] => []
        )

)

[2025-07-14 19:33:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492234
            [orderIds] => []
        )

)

[2025-07-14 19:34:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492245
            [orderIds] => []
        )

)

[2025-07-14 19:34:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492256
            [orderIds] => []
        )

)

[2025-07-14 19:34:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492267
            [orderIds] => []
        )

)

[2025-07-14 19:34:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492278
            [orderIds] => []
        )

)

[2025-07-14 19:34:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492289
            [orderIds] => []
        )

)

[2025-07-14 19:35:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492300
            [orderIds] => []
        )

)

[2025-07-14 19:35:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492311
            [orderIds] => []
        )

)

[2025-07-14 19:35:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 19:35:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 19:35:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 19:35:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 19:35:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492322
            [orderIds] => []
        )

)

[2025-07-14 19:35:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492333
            [orderIds] => []
        )

)

[2025-07-14 19:35:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492344
            [orderIds] => []
        )

)

[2025-07-14 19:35:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492355
            [orderIds] => []
        )

)

[2025-07-14 19:36:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492366
            [orderIds] => []
        )

)

[2025-07-14 19:36:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492377
            [orderIds] => []
        )

)

[2025-07-14 19:36:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492388
            [orderIds] => []
        )

)

[2025-07-14 19:36:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492399
            [orderIds] => []
        )

)

[2025-07-14 19:36:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492410
            [orderIds] => []
        )

)

[2025-07-14 19:37:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492421
            [orderIds] => []
        )

)

[2025-07-14 19:37:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492432
            [orderIds] => []
        )

)

[2025-07-14 19:37:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492443
            [orderIds] => []
        )

)

[2025-07-14 19:37:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492454
            [orderIds] => []
        )

)

[2025-07-14 19:37:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492465
            [orderIds] => []
        )

)

[2025-07-14 19:37:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492476
            [orderIds] => []
        )

)

[2025-07-14 19:38:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492487
            [orderIds] => []
        )

)

[2025-07-14 19:38:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492498
            [orderIds] => []
        )

)

[2025-07-14 19:38:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492509
            [orderIds] => []
        )

)

[2025-07-14 19:38:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492520
            [orderIds] => []
        )

)

[2025-07-14 19:38:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492531
            [orderIds] => []
        )

)

[2025-07-14 19:39:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492542
            [orderIds] => []
        )

)

[2025-07-14 19:39:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492553
            [orderIds] => []
        )

)

[2025-07-14 19:39:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492564
            [orderIds] => []
        )

)

[2025-07-14 19:39:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492575
            [orderIds] => []
        )

)

[2025-07-14 19:39:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492586
            [orderIds] => []
        )

)

[2025-07-14 19:39:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492597
            [orderIds] => []
        )

)

[2025-07-14 19:40:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492608
            [orderIds] => []
        )

)

[2025-07-14 19:40:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492619
            [orderIds] => []
        )

)

[2025-07-14 19:40:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492630
            [orderIds] => []
        )

)

[2025-07-14 19:40:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492641
            [orderIds] => []
        )

)

[2025-07-14 19:40:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492652
            [orderIds] => []
        )

)

[2025-07-14 19:41:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492663
            [orderIds] => []
        )

)

[2025-07-14 19:41:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492674
            [orderIds] => []
        )

)

[2025-07-14 19:41:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492685
            [orderIds] => []
        )

)

[2025-07-14 19:41:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492696
            [orderIds] => []
        )

)

[2025-07-14 19:41:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492707
            [orderIds] => []
        )

)

[2025-07-14 19:41:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492718
            [orderIds] => []
        )

)

[2025-07-14 19:42:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492729
            [orderIds] => []
        )

)

[2025-07-14 19:42:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492740
            [orderIds] => []
        )

)

[2025-07-14 19:42:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492751
            [orderIds] => []
        )

)

[2025-07-14 19:42:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492762
            [orderIds] => []
        )

)

[2025-07-14 19:42:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492773
            [orderIds] => []
        )

)

[2025-07-14 19:43:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492784
            [orderIds] => []
        )

)

[2025-07-14 19:43:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492795
            [orderIds] => []
        )

)

[2025-07-14 19:43:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492806
            [orderIds] => []
        )

)

[2025-07-14 19:43:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492817
            [orderIds] => []
        )

)

[2025-07-14 19:43:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492828
            [orderIds] => []
        )

)

[2025-07-14 19:43:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492839
            [orderIds] => []
        )

)

[2025-07-14 19:44:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492850
            [orderIds] => []
        )

)

[2025-07-14 19:44:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492861
            [orderIds] => []
        )

)

[2025-07-14 19:44:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492872
            [orderIds] => []
        )

)

[2025-07-14 19:44:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492883
            [orderIds] => []
        )

)

[2025-07-14 19:44:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492894
            [orderIds] => []
        )

)

[2025-07-14 19:45:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492905
            [orderIds] => []
        )

)

[2025-07-14 19:45:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492916
            [orderIds] => []
        )

)

[2025-07-14 19:45:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 19:45:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 19:45:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 19:45:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 19:45:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492927
            [orderIds] => []
        )

)

[2025-07-14 19:45:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492938
            [orderIds] => []
        )

)

[2025-07-14 19:45:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492949
            [orderIds] => []
        )

)

[2025-07-14 19:46:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492960
            [orderIds] => []
        )

)

[2025-07-14 19:46:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492971
            [orderIds] => []
        )

)

[2025-07-14 19:46:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492982
            [orderIds] => []
        )

)

[2025-07-14 19:46:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752492993
            [orderIds] => []
        )

)

[2025-07-14 19:46:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493004
            [orderIds] => []
        )

)

[2025-07-14 19:46:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493015
            [orderIds] => []
        )

)

[2025-07-14 19:47:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493026
            [orderIds] => []
        )

)

[2025-07-14 19:47:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493037
            [orderIds] => []
        )

)

[2025-07-14 19:47:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493048
            [orderIds] => []
        )

)

[2025-07-14 19:47:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493059
            [orderIds] => []
        )

)

[2025-07-14 19:47:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493070
            [orderIds] => []
        )

)

[2025-07-14 19:48:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493081
            [orderIds] => []
        )

)

[2025-07-14 19:48:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493092
            [orderIds] => []
        )

)

[2025-07-14 19:48:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493103
            [orderIds] => []
        )

)

[2025-07-14 19:48:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493114
            [orderIds] => []
        )

)

[2025-07-14 19:48:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493125
            [orderIds] => []
        )

)

[2025-07-14 19:48:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493136
            [orderIds] => []
        )

)

[2025-07-14 19:49:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493147
            [orderIds] => []
        )

)

[2025-07-14 19:49:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493158
            [orderIds] => []
        )

)

[2025-07-14 19:49:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493169
            [orderIds] => []
        )

)

[2025-07-14 19:49:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493180
            [orderIds] => []
        )

)

[2025-07-14 19:49:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493191
            [orderIds] => []
        )

)

[2025-07-14 19:50:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493202
            [orderIds] => []
        )

)

[2025-07-14 19:50:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493213
            [orderIds] => []
        )

)

[2025-07-14 19:50:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493224
            [orderIds] => []
        )

)

[2025-07-14 19:50:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493235
            [orderIds] => []
        )

)

[2025-07-14 19:50:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493246
            [orderIds] => []
        )

)

[2025-07-14 19:50:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493257
            [orderIds] => []
        )

)

[2025-07-14 19:51:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493268
            [orderIds] => []
        )

)

[2025-07-14 19:51:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493279
            [orderIds] => []
        )

)

[2025-07-14 19:51:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493290
            [orderIds] => []
        )

)

[2025-07-14 19:51:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493301
            [orderIds] => []
        )

)

[2025-07-14 19:51:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493312
            [orderIds] => []
        )

)

[2025-07-14 19:52:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493323
            [orderIds] => []
        )

)

[2025-07-14 19:52:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493334
            [orderIds] => []
        )

)

[2025-07-14 19:52:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493345
            [orderIds] => []
        )

)

[2025-07-14 19:52:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493356
            [orderIds] => []
        )

)

[2025-07-14 19:52:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493367
            [orderIds] => []
        )

)

[2025-07-14 19:52:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493378
            [orderIds] => []
        )

)

[2025-07-14 19:53:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493389
            [orderIds] => []
        )

)

[2025-07-14 19:53:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493400
            [orderIds] => []
        )

)

[2025-07-14 19:53:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493411
            [orderIds] => []
        )

)

[2025-07-14 19:53:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493422
            [orderIds] => []
        )

)

[2025-07-14 19:53:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493433
            [orderIds] => []
        )

)

[2025-07-14 19:54:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493444
            [orderIds] => []
        )

)

[2025-07-14 19:54:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493455
            [orderIds] => []
        )

)

[2025-07-14 19:54:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493466
            [orderIds] => []
        )

)

[2025-07-14 19:54:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1752407674
            [orderIds] => []
        )

)

[2025-07-14 19:54:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1751889274
            [orderIds] => []
        )

)

[2025-07-14 19:54:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1751889274
            [orderIds] => []
        )

)

[2025-07-14 19:54:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-07-14 19:54:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-07-14 19:54:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-07-14 19:54:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-07-14 19:54:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493477
            [orderIds] => []
        )

)

[2025-07-14 19:54:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-07-14 19:54:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493488
            [orderIds] => []
        )

)

[2025-07-14 19:54:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493499
            [orderIds] => []
        )

)

[2025-07-14 19:55:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493510
            [orderIds] => []
        )

)

[2025-07-14 19:55:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 19:55:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 19:55:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 19:55:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 19:55:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493521
            [orderIds] => []
        )

)

[2025-07-14 19:55:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493532
            [orderIds] => []
        )

)

[2025-07-14 19:55:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493543
            [orderIds] => []
        )

)

[2025-07-14 19:55:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493554
            [orderIds] => []
        )

)

[2025-07-14 19:56:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493565
            [orderIds] => []
        )

)

[2025-07-14 19:56:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493576
            [orderIds] => []
        )

)

[2025-07-14 19:56:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493587
            [orderIds] => []
        )

)

[2025-07-14 19:56:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493598
            [orderIds] => []
        )

)

[2025-07-14 19:56:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493609
            [orderIds] => []
        )

)

[2025-07-14 19:57:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493620
            [orderIds] => []
        )

)

[2025-07-14 19:57:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493631
            [orderIds] => []
        )

)

[2025-07-14 19:57:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493642
            [orderIds] => []
        )

)

[2025-07-14 19:57:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493653
            [orderIds] => []
        )

)

[2025-07-14 19:57:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493664
            [orderIds] => []
        )

)

[2025-07-14 19:57:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493675
            [orderIds] => []
        )

)

[2025-07-14 19:58:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493686
            [orderIds] => []
        )

)

[2025-07-14 19:58:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493697
            [orderIds] => []
        )

)

[2025-07-14 19:58:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493708
            [orderIds] => []
        )

)

[2025-07-14 19:58:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493719
            [orderIds] => []
        )

)

[2025-07-14 19:58:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493730
            [orderIds] => []
        )

)

[2025-07-14 19:59:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493741
            [orderIds] => []
        )

)

[2025-07-14 19:59:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493752
            [orderIds] => []
        )

)

[2025-07-14 19:59:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493763
            [orderIds] => []
        )

)

[2025-07-14 19:59:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493774
            [orderIds] => []
        )

)

[2025-07-14 19:59:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493785
            [orderIds] => []
        )

)

[2025-07-14 19:59:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493796
            [orderIds] => []
        )

)

[2025-07-14 20:00:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493807
            [orderIds] => []
        )

)

[2025-07-14 20:00:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493818
            [orderIds] => []
        )

)

[2025-07-14 20:00:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493829
            [orderIds] => []
        )

)

[2025-07-14 20:00:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493840
            [orderIds] => []
        )

)

[2025-07-14 20:00:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493851
            [orderIds] => []
        )

)

[2025-07-14 20:01:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493862
            [orderIds] => []
        )

)

[2025-07-14 20:01:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493873
            [orderIds] => []
        )

)

[2025-07-14 20:01:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493884
            [orderIds] => []
        )

)

[2025-07-14 20:01:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493895
            [orderIds] => []
        )

)

[2025-07-14 20:01:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493906
            [orderIds] => []
        )

)

[2025-07-14 20:01:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493917
            [orderIds] => []
        )

)

[2025-07-14 20:02:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493928
            [orderIds] => []
        )

)

[2025-07-14 20:02:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493939
            [orderIds] => []
        )

)

[2025-07-14 20:02:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493950
            [orderIds] => []
        )

)

[2025-07-14 20:02:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493961
            [orderIds] => []
        )

)

[2025-07-14 20:02:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493972
            [orderIds] => []
        )

)

[2025-07-14 20:03:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493983
            [orderIds] => []
        )

)

[2025-07-14 20:03:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752493994
            [orderIds] => []
        )

)

[2025-07-14 20:03:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494005
            [orderIds] => []
        )

)

[2025-07-14 20:03:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494016
            [orderIds] => []
        )

)

[2025-07-14 20:03:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494027
            [orderIds] => []
        )

)

[2025-07-14 20:03:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494038
            [orderIds] => []
        )

)

[2025-07-14 20:04:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494049
            [orderIds] => []
        )

)

[2025-07-14 20:04:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494060
            [orderIds] => []
        )

)

[2025-07-14 20:04:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494071
            [orderIds] => []
        )

)

[2025-07-14 20:04:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494082
            [orderIds] => []
        )

)

[2025-07-14 20:04:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494093
            [orderIds] => []
        )

)

[2025-07-14 20:05:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494104
            [orderIds] => []
        )

)

[2025-07-14 20:05:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494115
            [orderIds] => []
        )

)

[2025-07-14 20:05:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 20:05:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 20:05:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 20:05:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 20:05:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494126
            [orderIds] => []
        )

)

[2025-07-14 20:05:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494137
            [orderIds] => []
        )

)

[2025-07-14 20:05:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494148
            [orderIds] => []
        )

)

[2025-07-14 20:05:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494159
            [orderIds] => []
        )

)

[2025-07-14 20:06:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494170
            [orderIds] => []
        )

)

[2025-07-14 20:06:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494181
            [orderIds] => []
        )

)

[2025-07-14 20:06:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494192
            [orderIds] => []
        )

)

[2025-07-14 20:06:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494203
            [orderIds] => []
        )

)

[2025-07-14 20:06:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494214
            [orderIds] => []
        )

)

[2025-07-14 20:07:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494225
            [orderIds] => []
        )

)

[2025-07-14 20:07:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494236
            [orderIds] => []
        )

)

[2025-07-14 20:07:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494247
            [orderIds] => []
        )

)

[2025-07-14 20:07:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494258
            [orderIds] => []
        )

)

[2025-07-14 20:07:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494269
            [orderIds] => []
        )

)

[2025-07-14 20:08:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494280
            [orderIds] => []
        )

)

[2025-07-14 20:08:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494291
            [orderIds] => []
        )

)

[2025-07-14 20:08:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494302
            [orderIds] => []
        )

)

[2025-07-14 20:08:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494313
            [orderIds] => []
        )

)

[2025-07-14 20:08:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494324
            [orderIds] => []
        )

)

[2025-07-14 20:08:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494335
            [orderIds] => []
        )

)

[2025-07-14 20:09:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494346
            [orderIds] => []
        )

)

[2025-07-14 20:09:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494357
            [orderIds] => []
        )

)

[2025-07-14 20:09:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494368
            [orderIds] => []
        )

)

[2025-07-14 20:09:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494379
            [orderIds] => []
        )

)

[2025-07-14 20:09:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494390
            [orderIds] => []
        )

)

[2025-07-14 20:10:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494401
            [orderIds] => []
        )

)

[2025-07-14 20:10:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494412
            [orderIds] => []
        )

)

[2025-07-14 20:10:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494423
            [orderIds] => []
        )

)

[2025-07-14 20:10:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494434
            [orderIds] => []
        )

)

[2025-07-14 20:10:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494445
            [orderIds] => []
        )

)

[2025-07-14 20:10:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494456
            [orderIds] => []
        )

)

[2025-07-14 20:11:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494467
            [orderIds] => []
        )

)

[2025-07-14 20:11:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494478
            [orderIds] => []
        )

)

[2025-07-14 20:11:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494489
            [orderIds] => []
        )

)

[2025-07-14 20:11:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494500
            [orderIds] => []
        )

)

[2025-07-14 20:11:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494511
            [orderIds] => []
        )

)

[2025-07-14 20:12:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494522
            [orderIds] => []
        )

)

[2025-07-14 20:12:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494533
            [orderIds] => []
        )

)

[2025-07-14 20:12:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494544
            [orderIds] => []
        )

)

[2025-07-14 20:12:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494555
            [orderIds] => []
        )

)

[2025-07-14 20:12:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494566
            [orderIds] => []
        )

)

[2025-07-14 20:12:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494577
            [orderIds] => []
        )

)

[2025-07-14 20:13:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494588
            [orderIds] => []
        )

)

[2025-07-14 20:13:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494599
            [orderIds] => []
        )

)

[2025-07-14 20:13:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494610
            [orderIds] => []
        )

)

[2025-07-14 20:13:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494621
            [orderIds] => []
        )

)

[2025-07-14 20:13:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494632
            [orderIds] => []
        )

)

[2025-07-14 20:14:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494643
            [orderIds] => []
        )

)

[2025-07-14 20:14:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494654
            [orderIds] => []
        )

)

[2025-07-14 20:14:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494665
            [orderIds] => []
        )

)

[2025-07-14 20:14:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494676
            [orderIds] => []
        )

)

[2025-07-14 20:14:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494687
            [orderIds] => []
        )

)

[2025-07-14 20:14:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494698
            [orderIds] => []
        )

)

[2025-07-14 20:15:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494709
            [orderIds] => []
        )

)

[2025-07-14 20:15:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494720
            [orderIds] => []
        )

)

[2025-07-14 20:15:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 20:15:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 20:15:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 20:15:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 20:15:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494731
            [orderIds] => []
        )

)

[2025-07-14 20:15:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494742
            [orderIds] => []
        )

)

[2025-07-14 20:15:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494753
            [orderIds] => []
        )

)

[2025-07-14 20:16:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494764
            [orderIds] => []
        )

)

[2025-07-14 20:16:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494775
            [orderIds] => []
        )

)

[2025-07-14 20:16:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494786
            [orderIds] => []
        )

)

[2025-07-14 20:16:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494797
            [orderIds] => []
        )

)

[2025-07-14 20:16:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494808
            [orderIds] => []
        )

)

[2025-07-14 20:16:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494819
            [orderIds] => []
        )

)

[2025-07-14 20:17:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494830
            [orderIds] => []
        )

)

[2025-07-14 20:17:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494841
            [orderIds] => []
        )

)

[2025-07-14 20:17:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494852
            [orderIds] => []
        )

)

[2025-07-14 20:17:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494863
            [orderIds] => []
        )

)

[2025-07-14 20:17:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494874
            [orderIds] => []
        )

)

[2025-07-14 20:18:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494885
            [orderIds] => []
        )

)

[2025-07-14 20:18:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494896
            [orderIds] => []
        )

)

[2025-07-14 20:18:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494907
            [orderIds] => []
        )

)

[2025-07-14 20:18:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494918
            [orderIds] => []
        )

)

[2025-07-14 20:18:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494929
            [orderIds] => []
        )

)

[2025-07-14 20:19:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494940
            [orderIds] => []
        )

)

[2025-07-14 20:19:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494951
            [orderIds] => []
        )

)

[2025-07-14 20:19:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494962
            [orderIds] => []
        )

)

[2025-07-14 20:19:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494973
            [orderIds] => []
        )

)

[2025-07-14 20:19:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494984
            [orderIds] => []
        )

)

[2025-07-14 20:19:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752494995
            [orderIds] => []
        )

)

[2025-07-14 20:20:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495006
            [orderIds] => []
        )

)

[2025-07-14 20:20:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495017
            [orderIds] => []
        )

)

[2025-07-14 20:20:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495028
            [orderIds] => []
        )

)

[2025-07-14 20:20:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495039
            [orderIds] => []
        )

)

[2025-07-14 20:20:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495050
            [orderIds] => []
        )

)

[2025-07-14 20:21:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495061
            [orderIds] => []
        )

)

[2025-07-14 20:21:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495072
            [orderIds] => []
        )

)

[2025-07-14 20:21:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495083
            [orderIds] => []
        )

)

[2025-07-14 20:21:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495094
            [orderIds] => []
        )

)

[2025-07-14 20:21:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495105
            [orderIds] => []
        )

)

[2025-07-14 20:21:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495116
            [orderIds] => []
        )

)

[2025-07-14 20:22:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495127
            [orderIds] => []
        )

)

[2025-07-14 20:22:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495138
            [orderIds] => []
        )

)

[2025-07-14 20:22:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495149
            [orderIds] => []
        )

)

[2025-07-14 20:22:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495160
            [orderIds] => []
        )

)

[2025-07-14 20:22:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495171
            [orderIds] => []
        )

)

[2025-07-14 20:23:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495182
            [orderIds] => []
        )

)

[2025-07-14 20:23:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495193
            [orderIds] => []
        )

)

[2025-07-14 20:23:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495204
            [orderIds] => []
        )

)

[2025-07-14 20:23:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495215
            [orderIds] => []
        )

)

[2025-07-14 20:23:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495226
            [orderIds] => []
        )

)

[2025-07-14 20:23:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495237
            [orderIds] => []
        )

)

[2025-07-14 20:24:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495248
            [orderIds] => []
        )

)

[2025-07-14 20:24:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495259
            [orderIds] => []
        )

)

[2025-07-14 20:24:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495270
            [orderIds] => []
        )

)

[2025-07-14 20:24:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1752409475
            [orderIds] => []
        )

)

[2025-07-14 20:24:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1751891075
            [orderIds] => []
        )

)

[2025-07-14 20:24:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1751891075
            [orderIds] => []
        )

)

[2025-07-14 20:24:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-07-14 20:24:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-07-14 20:24:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-07-14 20:24:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-07-14 20:24:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-07-14 20:24:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495281
            [orderIds] => []
        )

)

[2025-07-14 20:24:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495292
            [orderIds] => []
        )

)

[2025-07-14 20:25:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495303
            [orderIds] => []
        )

)

[2025-07-14 20:25:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495314
            [orderIds] => []
        )

)

[2025-07-14 20:25:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 20:25:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 20:25:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 20:25:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 20:25:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495325
            [orderIds] => []
        )

)

[2025-07-14 20:25:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495336
            [orderIds] => []
        )

)

[2025-07-14 20:25:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495347
            [orderIds] => []
        )

)

[2025-07-14 20:25:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495358
            [orderIds] => []
        )

)

[2025-07-14 20:26:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495369
            [orderIds] => []
        )

)

[2025-07-14 20:26:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495380
            [orderIds] => []
        )

)

[2025-07-14 20:26:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495391
            [orderIds] => []
        )

)

[2025-07-14 20:26:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495402
            [orderIds] => []
        )

)

[2025-07-14 20:26:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495413
            [orderIds] => []
        )

)

[2025-07-14 20:27:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495424
            [orderIds] => []
        )

)

[2025-07-14 20:27:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495435
            [orderIds] => []
        )

)

[2025-07-14 20:27:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495446
            [orderIds] => []
        )

)

[2025-07-14 20:27:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495457
            [orderIds] => []
        )

)

[2025-07-14 20:27:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495468
            [orderIds] => []
        )

)

[2025-07-14 20:27:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495479
            [orderIds] => []
        )

)

[2025-07-14 20:28:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495490
            [orderIds] => []
        )

)

[2025-07-14 20:28:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495501
            [orderIds] => []
        )

)

[2025-07-14 20:28:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495512
            [orderIds] => []
        )

)

[2025-07-14 20:28:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495523
            [orderIds] => []
        )

)

[2025-07-14 20:28:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495534
            [orderIds] => []
        )

)

[2025-07-14 20:29:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495545
            [orderIds] => []
        )

)

[2025-07-14 20:29:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495556
            [orderIds] => []
        )

)

[2025-07-14 20:29:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495567
            [orderIds] => []
        )

)

[2025-07-14 20:29:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495578
            [orderIds] => []
        )

)

[2025-07-14 20:29:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495589
            [orderIds] => []
        )

)

[2025-07-14 20:30:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495600
            [orderIds] => []
        )

)

[2025-07-14 20:30:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495611
            [orderIds] => []
        )

)

[2025-07-14 20:30:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495622
            [orderIds] => []
        )

)

[2025-07-14 20:30:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495633
            [orderIds] => []
        )

)

[2025-07-14 20:30:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495644
            [orderIds] => []
        )

)

[2025-07-14 20:30:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495655
            [orderIds] => []
        )

)

[2025-07-14 20:31:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495666
            [orderIds] => []
        )

)

[2025-07-14 20:31:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495677
            [orderIds] => []
        )

)

[2025-07-14 20:31:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495688
            [orderIds] => []
        )

)

[2025-07-14 20:31:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495699
            [orderIds] => []
        )

)

[2025-07-14 20:31:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495710
            [orderIds] => []
        )

)

[2025-07-14 20:32:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495721
            [orderIds] => []
        )

)

[2025-07-14 20:32:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495732
            [orderIds] => []
        )

)

[2025-07-14 20:32:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495743
            [orderIds] => []
        )

)

[2025-07-14 20:32:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495754
            [orderIds] => []
        )

)

[2025-07-14 20:32:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495765
            [orderIds] => []
        )

)

[2025-07-14 20:32:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495776
            [orderIds] => []
        )

)

[2025-07-14 20:33:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495787
            [orderIds] => []
        )

)

[2025-07-14 20:33:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495798
            [orderIds] => []
        )

)

[2025-07-14 20:33:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495809
            [orderIds] => []
        )

)

[2025-07-14 20:33:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495820
            [orderIds] => []
        )

)

[2025-07-14 20:33:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495831
            [orderIds] => []
        )

)

[2025-07-14 20:34:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495842
            [orderIds] => []
        )

)

[2025-07-14 20:34:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495853
            [orderIds] => []
        )

)

[2025-07-14 20:34:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495864
            [orderIds] => []
        )

)

[2025-07-14 20:34:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495875
            [orderIds] => []
        )

)

[2025-07-14 20:34:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495886
            [orderIds] => []
        )

)

[2025-07-14 20:34:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495897
            [orderIds] => []
        )

)

[2025-07-14 20:35:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495908
            [orderIds] => []
        )

)

[2025-07-14 20:35:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495919
            [orderIds] => []
        )

)

[2025-07-14 20:35:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 20:35:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 20:35:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 20:35:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 20:35:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495930
            [orderIds] => []
        )

)

[2025-07-14 20:35:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495941
            [orderIds] => []
        )

)

[2025-07-14 20:35:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495952
            [orderIds] => []
        )

)

[2025-07-14 20:36:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495963
            [orderIds] => []
        )

)

[2025-07-14 20:36:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495974
            [orderIds] => []
        )

)

[2025-07-14 20:36:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495985
            [orderIds] => []
        )

)

[2025-07-14 20:36:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752495996
            [orderIds] => []
        )

)

[2025-07-14 20:36:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496007
            [orderIds] => []
        )

)

[2025-07-14 20:36:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496018
            [orderIds] => []
        )

)

[2025-07-14 20:37:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496029
            [orderIds] => []
        )

)

[2025-07-14 20:37:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496040
            [orderIds] => []
        )

)

[2025-07-14 20:37:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496051
            [orderIds] => []
        )

)

[2025-07-14 20:37:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496062
            [orderIds] => []
        )

)

[2025-07-14 20:37:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496073
            [orderIds] => []
        )

)

[2025-07-14 20:38:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496084
            [orderIds] => []
        )

)

[2025-07-14 20:38:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496095
            [orderIds] => []
        )

)

[2025-07-14 20:38:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496106
            [orderIds] => []
        )

)

[2025-07-14 20:38:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496117
            [orderIds] => []
        )

)

[2025-07-14 20:38:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496128
            [orderIds] => []
        )

)

[2025-07-14 20:38:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496139
            [orderIds] => []
        )

)

[2025-07-14 20:39:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496150
            [orderIds] => []
        )

)

[2025-07-14 20:39:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496161
            [orderIds] => []
        )

)

[2025-07-14 20:39:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496172
            [orderIds] => []
        )

)

[2025-07-14 20:39:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496183
            [orderIds] => []
        )

)

[2025-07-14 20:39:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496194
            [orderIds] => []
        )

)

[2025-07-14 20:40:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496205
            [orderIds] => []
        )

)

[2025-07-14 20:40:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496216
            [orderIds] => []
        )

)

[2025-07-14 20:40:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496227
            [orderIds] => []
        )

)

[2025-07-14 20:40:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496238
            [orderIds] => []
        )

)

[2025-07-14 20:40:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496249
            [orderIds] => []
        )

)

[2025-07-14 20:41:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496260
            [orderIds] => []
        )

)

[2025-07-14 20:41:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496271
            [orderIds] => []
        )

)

[2025-07-14 20:41:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496282
            [orderIds] => []
        )

)

[2025-07-14 20:41:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496293
            [orderIds] => []
        )

)

[2025-07-14 20:41:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496304
            [orderIds] => []
        )

)

[2025-07-14 20:41:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496315
            [orderIds] => []
        )

)

[2025-07-14 20:42:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496326
            [orderIds] => []
        )

)

[2025-07-14 20:42:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496337
            [orderIds] => []
        )

)

[2025-07-14 20:42:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496348
            [orderIds] => []
        )

)

[2025-07-14 20:42:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496359
            [orderIds] => []
        )

)

[2025-07-14 20:42:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496370
            [orderIds] => []
        )

)

[2025-07-14 20:43:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496381
            [orderIds] => []
        )

)

[2025-07-14 20:43:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496392
            [orderIds] => []
        )

)

[2025-07-14 20:43:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496403
            [orderIds] => []
        )

)

[2025-07-14 20:43:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496414
            [orderIds] => []
        )

)

[2025-07-14 20:43:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496425
            [orderIds] => []
        )

)

[2025-07-14 20:43:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496436
            [orderIds] => []
        )

)

[2025-07-14 20:44:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496447
            [orderIds] => []
        )

)

[2025-07-14 20:44:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496458
            [orderIds] => []
        )

)

[2025-07-14 20:44:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496469
            [orderIds] => []
        )

)

[2025-07-14 20:44:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496480
            [orderIds] => []
        )

)

[2025-07-14 20:44:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496491
            [orderIds] => []
        )

)

[2025-07-14 20:45:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496502
            [orderIds] => []
        )

)

[2025-07-14 20:45:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496513
            [orderIds] => []
        )

)

[2025-07-14 20:45:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 20:45:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 20:45:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 20:45:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 20:45:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496524
            [orderIds] => []
        )

)

[2025-07-14 20:45:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496535
            [orderIds] => []
        )

)

[2025-07-14 20:45:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496546
            [orderIds] => []
        )

)

[2025-07-14 20:45:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496557
            [orderIds] => []
        )

)

[2025-07-14 20:46:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496568
            [orderIds] => []
        )

)

[2025-07-14 20:46:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496579
            [orderIds] => []
        )

)

[2025-07-14 20:46:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496590
            [orderIds] => []
        )

)

[2025-07-14 20:46:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496601
            [orderIds] => []
        )

)

[2025-07-14 20:46:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496612
            [orderIds] => []
        )

)

[2025-07-14 20:47:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496623
            [orderIds] => []
        )

)

[2025-07-14 20:47:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496634
            [orderIds] => []
        )

)

[2025-07-14 20:47:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496645
            [orderIds] => []
        )

)

[2025-07-14 20:47:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496656
            [orderIds] => []
        )

)

[2025-07-14 20:47:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496667
            [orderIds] => []
        )

)

[2025-07-14 20:47:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496678
            [orderIds] => []
        )

)

[2025-07-14 20:48:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496689
            [orderIds] => []
        )

)

[2025-07-14 20:48:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496700
            [orderIds] => []
        )

)

[2025-07-14 20:48:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496711
            [orderIds] => []
        )

)

[2025-07-14 20:48:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496722
            [orderIds] => []
        )

)

[2025-07-14 20:48:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496733
            [orderIds] => []
        )

)

[2025-07-14 20:49:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496744
            [orderIds] => []
        )

)

[2025-07-14 20:49:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496755
            [orderIds] => []
        )

)

[2025-07-14 20:49:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496766
            [orderIds] => []
        )

)

[2025-07-14 20:49:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496777
            [orderIds] => []
        )

)

[2025-07-14 20:49:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496788
            [orderIds] => []
        )

)

[2025-07-14 20:49:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496799
            [orderIds] => []
        )

)

[2025-07-14 20:50:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496810
            [orderIds] => []
        )

)

[2025-07-14 20:50:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496821
            [orderIds] => []
        )

)

[2025-07-14 20:50:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496832
            [orderIds] => []
        )

)

[2025-07-14 20:50:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496843
            [orderIds] => []
        )

)

[2025-07-14 20:50:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496854
            [orderIds] => []
        )

)

[2025-07-14 20:51:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496865
            [orderIds] => []
        )

)

[2025-07-14 20:51:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496876
            [orderIds] => []
        )

)

[2025-07-14 20:51:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496887
            [orderIds] => []
        )

)

[2025-07-14 20:51:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496898
            [orderIds] => []
        )

)

[2025-07-14 20:51:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496909
            [orderIds] => []
        )

)

[2025-07-14 20:52:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496920
            [orderIds] => []
        )

)

[2025-07-14 20:52:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496931
            [orderIds] => []
        )

)

[2025-07-14 20:52:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496942
            [orderIds] => []
        )

)

[2025-07-14 20:52:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496953
            [orderIds] => []
        )

)

[2025-07-14 20:52:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496964
            [orderIds] => []
        )

)

[2025-07-14 20:52:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496975
            [orderIds] => []
        )

)

[2025-07-14 20:53:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496986
            [orderIds] => []
        )

)

[2025-07-14 20:53:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752496997
            [orderIds] => []
        )

)

[2025-07-14 20:53:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497008
            [orderIds] => []
        )

)

[2025-07-14 20:53:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497019
            [orderIds] => []
        )

)

[2025-07-14 20:53:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497030
            [orderIds] => []
        )

)

[2025-07-14 20:54:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497041
            [orderIds] => []
        )

)

[2025-07-14 20:54:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497052
            [orderIds] => []
        )

)

[2025-07-14 20:54:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497063
            [orderIds] => []
        )

)

[2025-07-14 20:54:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497074
            [orderIds] => []
        )

)

[2025-07-14 20:54:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1752411276
            [orderIds] => []
        )

)

[2025-07-14 20:54:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1751892876
            [orderIds] => []
        )

)

[2025-07-14 20:54:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1751892876
            [orderIds] => []
        )

)

[2025-07-14 20:54:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-07-14 20:54:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-07-14 20:54:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-07-14 20:54:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-07-14 20:54:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-07-14 20:54:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497085
            [orderIds] => []
        )

)

[2025-07-14 20:54:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497096
            [orderIds] => []
        )

)

[2025-07-14 20:55:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497107
            [orderIds] => []
        )

)

[2025-07-14 20:55:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497118
            [orderIds] => []
        )

)

[2025-07-14 20:55:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 20:55:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 20:55:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 20:55:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 20:55:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497129
            [orderIds] => []
        )

)

[2025-07-14 20:55:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497140
            [orderIds] => []
        )

)

[2025-07-14 20:55:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497151
            [orderIds] => []
        )

)

[2025-07-14 20:56:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497162
            [orderIds] => []
        )

)

[2025-07-14 20:56:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497173
            [orderIds] => []
        )

)

[2025-07-14 20:56:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497184
            [orderIds] => []
        )

)

[2025-07-14 20:56:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497195
            [orderIds] => []
        )

)

[2025-07-14 20:56:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497206
            [orderIds] => []
        )

)

[2025-07-14 20:56:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497217
            [orderIds] => []
        )

)

[2025-07-14 20:57:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497228
            [orderIds] => []
        )

)

[2025-07-14 20:57:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497239
            [orderIds] => []
        )

)

[2025-07-14 20:57:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497250
            [orderIds] => []
        )

)

[2025-07-14 20:57:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497261
            [orderIds] => []
        )

)

[2025-07-14 20:57:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497272
            [orderIds] => []
        )

)

[2025-07-14 20:58:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497283
            [orderIds] => []
        )

)

[2025-07-14 20:58:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497294
            [orderIds] => []
        )

)

[2025-07-14 20:58:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497305
            [orderIds] => []
        )

)

[2025-07-14 20:58:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497316
            [orderIds] => []
        )

)

[2025-07-14 20:58:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497327
            [orderIds] => []
        )

)

[2025-07-14 20:58:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497338
            [orderIds] => []
        )

)

[2025-07-14 20:59:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497349
            [orderIds] => []
        )

)

[2025-07-14 20:59:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497360
            [orderIds] => []
        )

)

[2025-07-14 20:59:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497371
            [orderIds] => []
        )

)

[2025-07-14 20:59:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497382
            [orderIds] => []
        )

)

[2025-07-14 20:59:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497393
            [orderIds] => []
        )

)

[2025-07-14 21:00:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497404
            [orderIds] => []
        )

)

[2025-07-14 21:00:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497415
            [orderIds] => []
        )

)

[2025-07-14 21:00:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497426
            [orderIds] => []
        )

)

[2025-07-14 21:00:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497437
            [orderIds] => []
        )

)

[2025-07-14 21:00:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497448
            [orderIds] => []
        )

)

[2025-07-14 21:00:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497459
            [orderIds] => []
        )

)

[2025-07-14 21:01:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497470
            [orderIds] => []
        )

)

[2025-07-14 21:01:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497481
            [orderIds] => []
        )

)

[2025-07-14 21:01:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497492
            [orderIds] => []
        )

)

[2025-07-14 21:01:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497503
            [orderIds] => []
        )

)

[2025-07-14 21:01:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497514
            [orderIds] => []
        )

)

[2025-07-14 21:02:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497525
            [orderIds] => []
        )

)

[2025-07-14 21:02:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497536
            [orderIds] => []
        )

)

[2025-07-14 21:02:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497547
            [orderIds] => []
        )

)

[2025-07-14 21:02:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497558
            [orderIds] => []
        )

)

[2025-07-14 21:02:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497569
            [orderIds] => []
        )

)

[2025-07-14 21:03:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497580
            [orderIds] => []
        )

)

[2025-07-14 21:03:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497591
            [orderIds] => []
        )

)

[2025-07-14 21:03:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497602
            [orderIds] => []
        )

)

[2025-07-14 21:03:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497613
            [orderIds] => []
        )

)

[2025-07-14 21:03:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497624
            [orderIds] => []
        )

)

[2025-07-14 21:03:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497635
            [orderIds] => []
        )

)

[2025-07-14 21:04:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497646
            [orderIds] => []
        )

)

[2025-07-14 21:04:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497657
            [orderIds] => []
        )

)

[2025-07-14 21:04:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497668
            [orderIds] => []
        )

)

[2025-07-14 21:04:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497679
            [orderIds] => []
        )

)

[2025-07-14 21:04:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497690
            [orderIds] => []
        )

)

[2025-07-14 21:05:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497701
            [orderIds] => []
        )

)

[2025-07-14 21:05:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497712
            [orderIds] => []
        )

)

[2025-07-14 21:05:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497723
            [orderIds] => []
        )

)

[2025-07-14 21:05:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 21:05:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 21:05:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 21:05:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 21:05:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497734
            [orderIds] => []
        )

)

[2025-07-14 21:05:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497745
            [orderIds] => []
        )

)

[2025-07-14 21:05:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497756
            [orderIds] => []
        )

)

[2025-07-14 21:06:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497767
            [orderIds] => []
        )

)

[2025-07-14 21:06:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497778
            [orderIds] => []
        )

)

[2025-07-14 21:06:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497789
            [orderIds] => []
        )

)

[2025-07-14 21:06:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497800
            [orderIds] => []
        )

)

[2025-07-14 21:06:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497811
            [orderIds] => []
        )

)

[2025-07-14 21:07:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497822
            [orderIds] => []
        )

)

[2025-07-14 21:07:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497833
            [orderIds] => []
        )

)

[2025-07-14 21:07:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497844
            [orderIds] => []
        )

)

[2025-07-14 21:07:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497855
            [orderIds] => []
        )

)

[2025-07-14 21:07:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497866
            [orderIds] => []
        )

)

[2025-07-14 21:07:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497877
            [orderIds] => []
        )

)

[2025-07-14 21:08:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497888
            [orderIds] => []
        )

)

[2025-07-14 21:08:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497899
            [orderIds] => []
        )

)

[2025-07-14 21:08:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497910
            [orderIds] => []
        )

)

[2025-07-14 21:08:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497921
            [orderIds] => []
        )

)

[2025-07-14 21:08:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497932
            [orderIds] => []
        )

)

[2025-07-14 21:09:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497943
            [orderIds] => []
        )

)

[2025-07-14 21:09:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497954
            [orderIds] => []
        )

)

[2025-07-14 21:09:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497965
            [orderIds] => []
        )

)

[2025-07-14 21:09:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497976
            [orderIds] => []
        )

)

[2025-07-14 21:09:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497987
            [orderIds] => []
        )

)

[2025-07-14 21:09:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752497998
            [orderIds] => []
        )

)

[2025-07-14 21:10:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498009
            [orderIds] => []
        )

)

[2025-07-14 21:10:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498020
            [orderIds] => []
        )

)

[2025-07-14 21:10:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498031
            [orderIds] => []
        )

)

[2025-07-14 21:10:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498042
            [orderIds] => []
        )

)

[2025-07-14 21:10:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498053
            [orderIds] => []
        )

)

[2025-07-14 21:11:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498064
            [orderIds] => []
        )

)

[2025-07-14 21:11:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498075
            [orderIds] => []
        )

)

[2025-07-14 21:11:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498086
            [orderIds] => []
        )

)

[2025-07-14 21:11:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498097
            [orderIds] => []
        )

)

[2025-07-14 21:11:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498108
            [orderIds] => []
        )

)

[2025-07-14 21:11:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498119
            [orderIds] => []
        )

)

[2025-07-14 21:12:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498130
            [orderIds] => []
        )

)

[2025-07-14 21:12:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498141
            [orderIds] => []
        )

)

[2025-07-14 21:12:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498152
            [orderIds] => []
        )

)

[2025-07-14 21:12:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498163
            [orderIds] => []
        )

)

[2025-07-14 21:12:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498174
            [orderIds] => []
        )

)

[2025-07-14 21:13:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498185
            [orderIds] => []
        )

)

[2025-07-14 21:13:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498196
            [orderIds] => []
        )

)

[2025-07-14 21:13:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498207
            [orderIds] => []
        )

)

[2025-07-14 21:13:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498218
            [orderIds] => []
        )

)

[2025-07-14 21:13:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498229
            [orderIds] => []
        )

)

[2025-07-14 21:14:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498240
            [orderIds] => []
        )

)

[2025-07-14 21:14:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498251
            [orderIds] => []
        )

)

[2025-07-14 21:14:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498262
            [orderIds] => []
        )

)

[2025-07-14 21:14:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498273
            [orderIds] => []
        )

)

[2025-07-14 21:14:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498284
            [orderIds] => []
        )

)

[2025-07-14 21:14:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498295
            [orderIds] => []
        )

)

[2025-07-14 21:15:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498306
            [orderIds] => []
        )

)

[2025-07-14 21:15:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498317
            [orderIds] => []
        )

)

[2025-07-14 21:15:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 21:15:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 21:15:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 21:15:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 21:15:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498328
            [orderIds] => []
        )

)

[2025-07-14 21:15:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498339
            [orderIds] => []
        )

)

[2025-07-14 21:15:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498350
            [orderIds] => []
        )

)

[2025-07-14 21:16:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498361
            [orderIds] => []
        )

)

[2025-07-14 21:16:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498372
            [orderIds] => []
        )

)

[2025-07-14 21:16:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498383
            [orderIds] => []
        )

)

[2025-07-14 21:16:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498394
            [orderIds] => []
        )

)

[2025-07-14 21:16:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498405
            [orderIds] => []
        )

)

[2025-07-14 21:16:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498416
            [orderIds] => []
        )

)

[2025-07-14 21:17:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498427
            [orderIds] => []
        )

)

[2025-07-14 21:17:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498438
            [orderIds] => []
        )

)

[2025-07-14 21:17:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498449
            [orderIds] => []
        )

)

[2025-07-14 21:17:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498460
            [orderIds] => []
        )

)

[2025-07-14 21:17:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498471
            [orderIds] => []
        )

)

[2025-07-14 21:18:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498482
            [orderIds] => []
        )

)

[2025-07-14 21:18:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498493
            [orderIds] => []
        )

)

[2025-07-14 21:18:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498504
            [orderIds] => []
        )

)

[2025-07-14 21:18:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498515
            [orderIds] => []
        )

)

[2025-07-14 21:18:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498526
            [orderIds] => []
        )

)

[2025-07-14 21:18:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498537
            [orderIds] => []
        )

)

[2025-07-14 21:19:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498548
            [orderIds] => []
        )

)

[2025-07-14 21:19:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498559
            [orderIds] => []
        )

)

[2025-07-14 21:19:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498570
            [orderIds] => []
        )

)

[2025-07-14 21:19:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498581
            [orderIds] => []
        )

)

[2025-07-14 21:19:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498592
            [orderIds] => []
        )

)

[2025-07-14 21:20:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498603
            [orderIds] => []
        )

)

[2025-07-14 21:20:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498614
            [orderIds] => []
        )

)

[2025-07-14 21:20:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498625
            [orderIds] => []
        )

)

[2025-07-14 21:20:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498636
            [orderIds] => []
        )

)

[2025-07-14 21:20:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498647
            [orderIds] => []
        )

)

[2025-07-14 21:20:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498658
            [orderIds] => []
        )

)

[2025-07-14 21:21:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498669
            [orderIds] => []
        )

)

[2025-07-14 21:21:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498680
            [orderIds] => []
        )

)

[2025-07-14 21:21:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498691
            [orderIds] => []
        )

)

[2025-07-14 21:21:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498702
            [orderIds] => []
        )

)

[2025-07-14 21:21:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498713
            [orderIds] => []
        )

)

[2025-07-14 21:22:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498724
            [orderIds] => []
        )

)

[2025-07-14 21:22:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498735
            [orderIds] => []
        )

)

[2025-07-14 21:22:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498746
            [orderIds] => []
        )

)

[2025-07-14 21:22:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498757
            [orderIds] => []
        )

)

[2025-07-14 21:22:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498768
            [orderIds] => []
        )

)

[2025-07-14 21:22:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498779
            [orderIds] => []
        )

)

[2025-07-14 21:23:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498790
            [orderIds] => []
        )

)

[2025-07-14 21:23:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498801
            [orderIds] => []
        )

)

[2025-07-14 21:23:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498812
            [orderIds] => []
        )

)

[2025-07-14 21:23:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498823
            [orderIds] => []
        )

)

[2025-07-14 21:23:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498834
            [orderIds] => []
        )

)

[2025-07-14 21:24:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498845
            [orderIds] => []
        )

)

[2025-07-14 21:24:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498856
            [orderIds] => []
        )

)

[2025-07-14 21:24:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498867
            [orderIds] => []
        )

)

[2025-07-14 21:24:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1752413077
            [orderIds] => []
        )

)

[2025-07-14 21:24:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1751894677
            [orderIds] => []
        )

)

[2025-07-14 21:24:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1751894677
            [orderIds] => []
        )

)

[2025-07-14 21:24:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-07-14 21:24:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498878
            [orderIds] => []
        )

)

[2025-07-14 21:24:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-07-14 21:24:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-07-14 21:24:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-07-14 21:24:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-07-14 21:24:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498889
            [orderIds] => []
        )

)

[2025-07-14 21:25:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498900
            [orderIds] => []
        )

)

[2025-07-14 21:25:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498911
            [orderIds] => []
        )

)

[2025-07-14 21:25:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498922
            [orderIds] => []
        )

)

[2025-07-14 21:25:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 21:25:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 21:25:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 21:25:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 21:25:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498933
            [orderIds] => []
        )

)

[2025-07-14 21:25:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498944
            [orderIds] => []
        )

)

[2025-07-14 21:25:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498955
            [orderIds] => []
        )

)

[2025-07-14 21:26:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498966
            [orderIds] => []
        )

)

[2025-07-14 21:26:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498977
            [orderIds] => []
        )

)

[2025-07-14 21:26:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498988
            [orderIds] => []
        )

)

[2025-07-14 21:26:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752498999
            [orderIds] => []
        )

)

[2025-07-14 21:26:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499010
            [orderIds] => []
        )

)

[2025-07-14 21:27:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499021
            [orderIds] => []
        )

)

[2025-07-14 21:27:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499032
            [orderIds] => []
        )

)

[2025-07-14 21:27:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499043
            [orderIds] => []
        )

)

[2025-07-14 21:27:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499054
            [orderIds] => []
        )

)

[2025-07-14 21:27:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499065
            [orderIds] => []
        )

)

[2025-07-14 21:27:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499076
            [orderIds] => []
        )

)

[2025-07-14 21:28:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499087
            [orderIds] => []
        )

)

[2025-07-14 21:28:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499098
            [orderIds] => []
        )

)

[2025-07-14 21:28:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499109
            [orderIds] => []
        )

)

[2025-07-14 21:28:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499120
            [orderIds] => []
        )

)

[2025-07-14 21:28:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499131
            [orderIds] => []
        )

)

[2025-07-14 21:29:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499142
            [orderIds] => []
        )

)

[2025-07-14 21:29:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499153
            [orderIds] => []
        )

)

[2025-07-14 21:29:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499164
            [orderIds] => []
        )

)

[2025-07-14 21:29:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499175
            [orderIds] => []
        )

)

[2025-07-14 21:29:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499186
            [orderIds] => []
        )

)

[2025-07-14 21:29:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499197
            [orderIds] => []
        )

)

[2025-07-14 21:30:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499208
            [orderIds] => []
        )

)

[2025-07-14 21:30:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499219
            [orderIds] => []
        )

)

[2025-07-14 21:30:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499230
            [orderIds] => []
        )

)

[2025-07-14 21:30:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499241
            [orderIds] => []
        )

)

[2025-07-14 21:30:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499252
            [orderIds] => []
        )

)

[2025-07-14 21:31:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499263
            [orderIds] => []
        )

)

[2025-07-14 21:31:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499274
            [orderIds] => []
        )

)

[2025-07-14 21:31:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499285
            [orderIds] => []
        )

)

[2025-07-14 21:31:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499296
            [orderIds] => []
        )

)

[2025-07-14 21:31:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499307
            [orderIds] => []
        )

)

[2025-07-14 21:31:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499318
            [orderIds] => []
        )

)

[2025-07-14 21:32:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499329
            [orderIds] => []
        )

)

[2025-07-14 21:32:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499340
            [orderIds] => []
        )

)

[2025-07-14 21:32:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499351
            [orderIds] => []
        )

)

[2025-07-14 21:32:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499362
            [orderIds] => []
        )

)

[2025-07-14 21:32:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499373
            [orderIds] => []
        )

)

[2025-07-14 21:33:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499384
            [orderIds] => []
        )

)

[2025-07-14 21:33:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499395
            [orderIds] => []
        )

)

[2025-07-14 21:33:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499406
            [orderIds] => []
        )

)

[2025-07-14 21:33:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499417
            [orderIds] => []
        )

)

[2025-07-14 21:33:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499428
            [orderIds] => []
        )

)

[2025-07-14 21:33:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499439
            [orderIds] => []
        )

)

[2025-07-14 21:34:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499450
            [orderIds] => []
        )

)

[2025-07-14 21:34:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499461
            [orderIds] => []
        )

)

[2025-07-14 21:34:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499472
            [orderIds] => []
        )

)

[2025-07-14 21:34:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499483
            [orderIds] => []
        )

)

[2025-07-14 21:34:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499494
            [orderIds] => []
        )

)

[2025-07-14 21:35:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499505
            [orderIds] => []
        )

)

[2025-07-14 21:35:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499516
            [orderIds] => []
        )

)

[2025-07-14 21:35:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499527
            [orderIds] => []
        )

)

[2025-07-14 21:35:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 21:35:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 21:35:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 21:35:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 21:35:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499538
            [orderIds] => []
        )

)

[2025-07-14 21:35:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499549
            [orderIds] => []
        )

)

[2025-07-14 21:36:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499560
            [orderIds] => []
        )

)

[2025-07-14 21:36:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499571
            [orderIds] => []
        )

)

[2025-07-14 21:36:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499582
            [orderIds] => []
        )

)

[2025-07-14 21:36:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499593
            [orderIds] => []
        )

)

[2025-07-14 21:36:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499604
            [orderIds] => []
        )

)

[2025-07-14 21:36:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499615
            [orderIds] => []
        )

)

[2025-07-14 21:37:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499626
            [orderIds] => []
        )

)

[2025-07-14 21:37:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499637
            [orderIds] => []
        )

)

[2025-07-14 21:37:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499648
            [orderIds] => []
        )

)

[2025-07-14 21:37:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499659
            [orderIds] => []
        )

)

[2025-07-14 21:37:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499670
            [orderIds] => []
        )

)

[2025-07-14 21:38:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499681
            [orderIds] => []
        )

)

[2025-07-14 21:38:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499692
            [orderIds] => []
        )

)

[2025-07-14 21:38:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499703
            [orderIds] => []
        )

)

[2025-07-14 21:38:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499714
            [orderIds] => []
        )

)

[2025-07-14 21:38:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499725
            [orderIds] => []
        )

)

[2025-07-14 21:38:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499736
            [orderIds] => []
        )

)

[2025-07-14 21:39:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499747
            [orderIds] => []
        )

)

[2025-07-14 21:39:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499758
            [orderIds] => []
        )

)

[2025-07-14 21:39:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499769
            [orderIds] => []
        )

)

[2025-07-14 21:39:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499780
            [orderIds] => []
        )

)

[2025-07-14 21:39:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499791
            [orderIds] => []
        )

)

[2025-07-14 21:40:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499802
            [orderIds] => []
        )

)

[2025-07-14 21:40:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499813
            [orderIds] => []
        )

)

[2025-07-14 21:40:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499824
            [orderIds] => []
        )

)

[2025-07-14 21:40:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499835
            [orderIds] => []
        )

)

[2025-07-14 21:40:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499846
            [orderIds] => []
        )

)

[2025-07-14 21:40:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499857
            [orderIds] => []
        )

)

[2025-07-14 21:41:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499868
            [orderIds] => []
        )

)

[2025-07-14 21:41:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499879
            [orderIds] => []
        )

)

[2025-07-14 21:41:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499890
            [orderIds] => []
        )

)

[2025-07-14 21:41:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499901
            [orderIds] => []
        )

)

[2025-07-14 21:41:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499912
            [orderIds] => []
        )

)

[2025-07-14 21:42:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499923
            [orderIds] => []
        )

)

[2025-07-14 21:42:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499934
            [orderIds] => []
        )

)

[2025-07-14 21:42:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499945
            [orderIds] => []
        )

)

[2025-07-14 21:42:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499956
            [orderIds] => []
        )

)

[2025-07-14 21:42:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499967
            [orderIds] => []
        )

)

[2025-07-14 21:42:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499978
            [orderIds] => []
        )

)

[2025-07-14 21:43:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752499989
            [orderIds] => []
        )

)

[2025-07-14 21:43:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500000
            [orderIds] => []
        )

)

[2025-07-14 21:43:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500011
            [orderIds] => []
        )

)

[2025-07-14 21:43:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500022
            [orderIds] => []
        )

)

[2025-07-14 21:43:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500033
            [orderIds] => []
        )

)

[2025-07-14 21:44:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500044
            [orderIds] => []
        )

)

[2025-07-14 21:44:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500055
            [orderIds] => []
        )

)

[2025-07-14 21:44:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500066
            [orderIds] => []
        )

)

[2025-07-14 21:44:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500077
            [orderIds] => []
        )

)

[2025-07-14 21:44:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500088
            [orderIds] => []
        )

)

[2025-07-14 21:44:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500099
            [orderIds] => []
        )

)

[2025-07-14 21:45:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500110
            [orderIds] => []
        )

)

[2025-07-14 21:45:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500121
            [orderIds] => []
        )

)

[2025-07-14 21:45:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 21:45:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 21:45:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 21:45:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 21:45:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500132
            [orderIds] => []
        )

)

[2025-07-14 21:45:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500143
            [orderIds] => []
        )

)

[2025-07-14 21:45:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500154
            [orderIds] => []
        )

)

[2025-07-14 21:46:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500165
            [orderIds] => []
        )

)

[2025-07-14 21:46:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500176
            [orderIds] => []
        )

)

[2025-07-14 21:46:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500187
            [orderIds] => []
        )

)

[2025-07-14 21:46:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500198
            [orderIds] => []
        )

)

[2025-07-14 21:46:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500209
            [orderIds] => []
        )

)

[2025-07-14 21:47:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500220
            [orderIds] => []
        )

)

[2025-07-14 21:47:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500231
            [orderIds] => []
        )

)

[2025-07-14 21:47:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500242
            [orderIds] => []
        )

)

[2025-07-14 21:47:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500253
            [orderIds] => []
        )

)

[2025-07-14 21:47:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500264
            [orderIds] => []
        )

)

[2025-07-14 21:47:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500275
            [orderIds] => []
        )

)

[2025-07-14 21:48:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500286
            [orderIds] => []
        )

)

[2025-07-14 21:48:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500297
            [orderIds] => []
        )

)

[2025-07-14 21:48:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500308
            [orderIds] => []
        )

)

[2025-07-14 21:48:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500319
            [orderIds] => []
        )

)

[2025-07-14 21:48:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500330
            [orderIds] => []
        )

)

[2025-07-14 21:49:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500341
            [orderIds] => []
        )

)

[2025-07-14 21:49:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500352
            [orderIds] => []
        )

)

[2025-07-14 21:49:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500363
            [orderIds] => []
        )

)

[2025-07-14 21:49:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500374
            [orderIds] => []
        )

)

[2025-07-14 21:49:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500385
            [orderIds] => []
        )

)

[2025-07-14 21:49:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500396
            [orderIds] => []
        )

)

[2025-07-14 21:50:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500407
            [orderIds] => []
        )

)

[2025-07-14 21:50:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500418
            [orderIds] => []
        )

)

[2025-07-14 21:50:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500429
            [orderIds] => []
        )

)

[2025-07-14 21:50:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500440
            [orderIds] => []
        )

)

[2025-07-14 21:50:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500451
            [orderIds] => []
        )

)

[2025-07-14 21:51:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500462
            [orderIds] => []
        )

)

[2025-07-14 21:51:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500473
            [orderIds] => []
        )

)

[2025-07-14 21:51:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500484
            [orderIds] => []
        )

)

[2025-07-14 21:51:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500495
            [orderIds] => []
        )

)

[2025-07-14 21:51:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500506
            [orderIds] => []
        )

)

[2025-07-14 21:51:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500517
            [orderIds] => []
        )

)

[2025-07-14 21:52:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500528
            [orderIds] => []
        )

)

[2025-07-14 21:52:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500539
            [orderIds] => []
        )

)

[2025-07-14 21:52:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500550
            [orderIds] => []
        )

)

[2025-07-14 21:52:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500561
            [orderIds] => []
        )

)

[2025-07-14 21:52:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500572
            [orderIds] => []
        )

)

[2025-07-14 21:53:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500583
            [orderIds] => []
        )

)

[2025-07-14 21:53:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500594
            [orderIds] => []
        )

)

[2025-07-14 21:53:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500605
            [orderIds] => []
        )

)

[2025-07-14 21:53:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500616
            [orderIds] => []
        )

)

[2025-07-14 21:53:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500627
            [orderIds] => []
        )

)

[2025-07-14 21:53:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500638
            [orderIds] => []
        )

)

[2025-07-14 21:54:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500649
            [orderIds] => []
        )

)

[2025-07-14 21:54:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500660
            [orderIds] => []
        )

)

[2025-07-14 21:54:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500671
            [orderIds] => []
        )

)

[2025-07-14 21:54:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1752414878
            [orderIds] => []
        )

)

[2025-07-14 21:54:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1751896478
            [orderIds] => []
        )

)

[2025-07-14 21:54:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1751896478
            [orderIds] => []
        )

)

[2025-07-14 21:54:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-07-14 21:54:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-07-14 21:54:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-07-14 21:54:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-07-14 21:54:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-07-14 21:54:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500682
            [orderIds] => []
        )

)

[2025-07-14 21:54:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500693
            [orderIds] => []
        )

)

[2025-07-14 21:55:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500704
            [orderIds] => []
        )

)

[2025-07-14 21:55:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500715
            [orderIds] => []
        )

)

[2025-07-14 21:55:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500726
            [orderIds] => []
        )

)

[2025-07-14 21:55:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 21:55:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 21:55:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 21:55:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 21:55:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500737
            [orderIds] => []
        )

)

[2025-07-14 21:55:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500748
            [orderIds] => []
        )

)

[2025-07-14 21:55:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500759
            [orderIds] => []
        )

)

[2025-07-14 21:56:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500770
            [orderIds] => []
        )

)

[2025-07-14 21:56:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500781
            [orderIds] => []
        )

)

[2025-07-14 21:56:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500792
            [orderIds] => []
        )

)

[2025-07-14 21:56:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500803
            [orderIds] => []
        )

)

[2025-07-14 21:56:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500814
            [orderIds] => []
        )

)

[2025-07-14 21:57:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500825
            [orderIds] => []
        )

)

[2025-07-14 21:57:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500836
            [orderIds] => []
        )

)

[2025-07-14 21:57:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500847
            [orderIds] => []
        )

)

[2025-07-14 21:57:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500858
            [orderIds] => []
        )

)

[2025-07-14 21:57:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500869
            [orderIds] => []
        )

)

[2025-07-14 21:58:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500880
            [orderIds] => []
        )

)

[2025-07-14 21:58:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500891
            [orderIds] => []
        )

)

[2025-07-14 21:58:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500902
            [orderIds] => []
        )

)

[2025-07-14 21:58:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500913
            [orderIds] => []
        )

)

[2025-07-14 21:58:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500924
            [orderIds] => []
        )

)

[2025-07-14 21:58:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500935
            [orderIds] => []
        )

)

[2025-07-14 21:59:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500946
            [orderIds] => []
        )

)

[2025-07-14 21:59:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500957
            [orderIds] => []
        )

)

[2025-07-14 21:59:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500968
            [orderIds] => []
        )

)

[2025-07-14 21:59:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500979
            [orderIds] => []
        )

)

[2025-07-14 21:59:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752500990
            [orderIds] => []
        )

)

[2025-07-14 22:00:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501001
            [orderIds] => []
        )

)

[2025-07-14 22:00:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501012
            [orderIds] => []
        )

)

[2025-07-14 22:00:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501023
            [orderIds] => []
        )

)

[2025-07-14 22:00:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501034
            [orderIds] => []
        )

)

[2025-07-14 22:00:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501045
            [orderIds] => []
        )

)

[2025-07-14 22:00:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501056
            [orderIds] => []
        )

)

[2025-07-14 22:01:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501067
            [orderIds] => []
        )

)

[2025-07-14 22:01:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501078
            [orderIds] => []
        )

)

[2025-07-14 22:01:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501089
            [orderIds] => []
        )

)

[2025-07-14 22:01:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501100
            [orderIds] => []
        )

)

[2025-07-14 22:01:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501111
            [orderIds] => []
        )

)

[2025-07-14 22:02:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501122
            [orderIds] => []
        )

)

[2025-07-14 22:02:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501133
            [orderIds] => []
        )

)

[2025-07-14 22:02:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501144
            [orderIds] => []
        )

)

[2025-07-14 22:02:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501155
            [orderIds] => []
        )

)

[2025-07-14 22:02:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501166
            [orderIds] => []
        )

)

[2025-07-14 22:02:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501177
            [orderIds] => []
        )

)

[2025-07-14 22:03:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501188
            [orderIds] => []
        )

)

[2025-07-14 22:03:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501199
            [orderIds] => []
        )

)

[2025-07-14 22:03:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501210
            [orderIds] => []
        )

)

[2025-07-14 22:03:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501221
            [orderIds] => []
        )

)

[2025-07-14 22:03:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501232
            [orderIds] => []
        )

)

[2025-07-14 22:04:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501243
            [orderIds] => []
        )

)

[2025-07-14 22:04:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501254
            [orderIds] => []
        )

)

[2025-07-14 22:04:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501265
            [orderIds] => []
        )

)

[2025-07-14 22:04:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501276
            [orderIds] => []
        )

)

[2025-07-14 22:04:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501287
            [orderIds] => []
        )

)

[2025-07-14 22:04:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501298
            [orderIds] => []
        )

)

[2025-07-14 22:05:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501309
            [orderIds] => []
        )

)

[2025-07-14 22:05:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501320
            [orderIds] => []
        )

)

[2025-07-14 22:05:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501331
            [orderIds] => []
        )

)

[2025-07-14 22:05:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 22:05:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 22:05:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 22:05:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 22:05:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501342
            [orderIds] => []
        )

)

[2025-07-14 22:05:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501353
            [orderIds] => []
        )

)

[2025-07-14 22:06:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501364
            [orderIds] => []
        )

)

[2025-07-14 22:06:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501375
            [orderIds] => []
        )

)

[2025-07-14 22:06:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501386
            [orderIds] => []
        )

)

[2025-07-14 22:06:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501397
            [orderIds] => []
        )

)

[2025-07-14 22:06:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501408
            [orderIds] => []
        )

)

[2025-07-14 22:06:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501419
            [orderIds] => []
        )

)

[2025-07-14 22:07:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501430
            [orderIds] => []
        )

)

[2025-07-14 22:07:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501441
            [orderIds] => []
        )

)

[2025-07-14 22:07:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501452
            [orderIds] => []
        )

)

[2025-07-14 22:07:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501463
            [orderIds] => []
        )

)

[2025-07-14 22:07:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501474
            [orderIds] => []
        )

)

[2025-07-14 22:08:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501485
            [orderIds] => []
        )

)

[2025-07-14 22:08:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501496
            [orderIds] => []
        )

)

[2025-07-14 22:08:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501507
            [orderIds] => []
        )

)

[2025-07-14 22:08:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501518
            [orderIds] => []
        )

)

[2025-07-14 22:08:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501529
            [orderIds] => []
        )

)

[2025-07-14 22:09:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501540
            [orderIds] => []
        )

)

[2025-07-14 22:09:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501551
            [orderIds] => []
        )

)

[2025-07-14 22:09:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501562
            [orderIds] => []
        )

)

[2025-07-14 22:09:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501573
            [orderIds] => []
        )

)

[2025-07-14 22:09:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501584
            [orderIds] => []
        )

)

[2025-07-14 22:09:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501595
            [orderIds] => []
        )

)

[2025-07-14 22:10:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501606
            [orderIds] => []
        )

)

[2025-07-14 22:10:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501617
            [orderIds] => []
        )

)

[2025-07-14 22:10:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501628
            [orderIds] => []
        )

)

[2025-07-14 22:10:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501639
            [orderIds] => []
        )

)

[2025-07-14 22:10:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501650
            [orderIds] => []
        )

)

[2025-07-14 22:11:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501661
            [orderIds] => []
        )

)

[2025-07-14 22:11:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501672
            [orderIds] => []
        )

)

[2025-07-14 22:11:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501683
            [orderIds] => []
        )

)

[2025-07-14 22:11:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501694
            [orderIds] => []
        )

)

[2025-07-14 22:11:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501705
            [orderIds] => []
        )

)

[2025-07-14 22:11:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501716
            [orderIds] => []
        )

)

[2025-07-14 22:12:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501727
            [orderIds] => []
        )

)

[2025-07-14 22:12:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501738
            [orderIds] => []
        )

)

[2025-07-14 22:12:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501749
            [orderIds] => []
        )

)

[2025-07-14 22:12:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501760
            [orderIds] => []
        )

)

[2025-07-14 22:12:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501771
            [orderIds] => []
        )

)

[2025-07-14 22:13:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501782
            [orderIds] => []
        )

)

[2025-07-14 22:13:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501793
            [orderIds] => []
        )

)

[2025-07-14 22:13:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501804
            [orderIds] => []
        )

)

[2025-07-14 22:13:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501815
            [orderIds] => []
        )

)

[2025-07-14 22:13:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501826
            [orderIds] => []
        )

)

[2025-07-14 22:13:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501837
            [orderIds] => []
        )

)

[2025-07-14 22:14:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501848
            [orderIds] => []
        )

)

[2025-07-14 22:14:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501859
            [orderIds] => []
        )

)

[2025-07-14 22:14:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501870
            [orderIds] => []
        )

)

[2025-07-14 22:14:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501881
            [orderIds] => []
        )

)

[2025-07-14 22:14:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501892
            [orderIds] => []
        )

)

[2025-07-14 22:15:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501903
            [orderIds] => []
        )

)

[2025-07-14 22:15:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501914
            [orderIds] => []
        )

)

[2025-07-14 22:15:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501925
            [orderIds] => []
        )

)

[2025-07-14 22:15:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 22:15:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 22:15:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 22:15:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 22:15:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501936
            [orderIds] => []
        )

)

[2025-07-14 22:15:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501947
            [orderIds] => []
        )

)

[2025-07-14 22:15:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501958
            [orderIds] => []
        )

)

[2025-07-14 22:16:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501969
            [orderIds] => []
        )

)

[2025-07-14 22:16:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501980
            [orderIds] => []
        )

)

[2025-07-14 22:16:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752501991
            [orderIds] => []
        )

)

[2025-07-14 22:16:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502002
            [orderIds] => []
        )

)

[2025-07-14 22:16:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502013
            [orderIds] => []
        )

)

[2025-07-14 22:17:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502025
            [orderIds] => []
        )

)

[2025-07-14 22:17:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502035
            [orderIds] => []
        )

)

[2025-07-14 22:17:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502046
            [orderIds] => []
        )

)

[2025-07-14 22:17:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502057
            [orderIds] => []
        )

)

[2025-07-14 22:17:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502068
            [orderIds] => []
        )

)

[2025-07-14 22:17:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502079
            [orderIds] => []
        )

)

[2025-07-14 22:18:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502090
            [orderIds] => []
        )

)

[2025-07-14 22:18:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502101
            [orderIds] => []
        )

)

[2025-07-14 22:18:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502112
            [orderIds] => []
        )

)

[2025-07-14 22:18:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502123
            [orderIds] => []
        )

)

[2025-07-14 22:18:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502134
            [orderIds] => []
        )

)

[2025-07-14 22:19:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502145
            [orderIds] => []
        )

)

[2025-07-14 22:19:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502156
            [orderIds] => []
        )

)

[2025-07-14 22:19:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502167
            [orderIds] => []
        )

)

[2025-07-14 22:19:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502178
            [orderIds] => []
        )

)

[2025-07-14 22:19:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502189
            [orderIds] => []
        )

)

[2025-07-14 22:20:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502200
            [orderIds] => []
        )

)

[2025-07-14 22:20:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502211
            [orderIds] => []
        )

)

[2025-07-14 22:20:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502222
            [orderIds] => []
        )

)

[2025-07-14 22:20:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502233
            [orderIds] => []
        )

)

[2025-07-14 22:20:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502244
            [orderIds] => []
        )

)

[2025-07-14 22:20:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502255
            [orderIds] => []
        )

)

[2025-07-14 22:21:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502266
            [orderIds] => []
        )

)

[2025-07-14 22:21:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502277
            [orderIds] => []
        )

)

[2025-07-14 22:21:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502288
            [orderIds] => []
        )

)

[2025-07-14 22:21:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502299
            [orderIds] => []
        )

)

[2025-07-14 22:21:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502310
            [orderIds] => []
        )

)

[2025-07-14 22:22:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502321
            [orderIds] => []
        )

)

[2025-07-14 22:22:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502332
            [orderIds] => []
        )

)

[2025-07-14 22:22:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502343
            [orderIds] => []
        )

)

[2025-07-14 22:22:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502354
            [orderIds] => []
        )

)

[2025-07-14 22:22:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502365
            [orderIds] => []
        )

)

[2025-07-14 22:22:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502376
            [orderIds] => []
        )

)

[2025-07-14 22:23:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502387
            [orderIds] => []
        )

)

[2025-07-14 22:23:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502398
            [orderIds] => []
        )

)

[2025-07-14 22:23:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502409
            [orderIds] => []
        )

)

[2025-07-14 22:23:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502420
            [orderIds] => []
        )

)

[2025-07-14 22:23:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502431
            [orderIds] => []
        )

)

[2025-07-14 22:24:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502442
            [orderIds] => []
        )

)

[2025-07-14 22:24:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502453
            [orderIds] => []
        )

)

[2025-07-14 22:24:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502464
            [orderIds] => []
        )

)

[2025-07-14 22:24:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502475
            [orderIds] => []
        )

)

[2025-07-14 22:24:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1752416679
            [orderIds] => []
        )

)

[2025-07-14 22:24:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1751898279
            [orderIds] => []
        )

)

[2025-07-14 22:24:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1751898279
            [orderIds] => []
        )

)

[2025-07-14 22:24:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-07-14 22:24:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-07-14 22:24:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-07-14 22:24:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-07-14 22:24:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-07-14 22:24:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502486
            [orderIds] => []
        )

)

[2025-07-14 22:24:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502497
            [orderIds] => []
        )

)

[2025-07-14 22:25:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502508
            [orderIds] => []
        )

)

[2025-07-14 22:25:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502519
            [orderIds] => []
        )

)

[2025-07-14 22:25:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502530
            [orderIds] => []
        )

)

[2025-07-14 22:25:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 22:25:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 22:25:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 22:25:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 22:25:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502541
            [orderIds] => []
        )

)

[2025-07-14 22:25:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502552
            [orderIds] => []
        )

)

[2025-07-14 22:26:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502563
            [orderIds] => []
        )

)

[2025-07-14 22:26:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502574
            [orderIds] => []
        )

)

[2025-07-14 22:26:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502585
            [orderIds] => []
        )

)

[2025-07-14 22:26:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502596
            [orderIds] => []
        )

)

[2025-07-14 22:26:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502607
            [orderIds] => []
        )

)

[2025-07-14 22:26:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502618
            [orderIds] => []
        )

)

[2025-07-14 22:27:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502629
            [orderIds] => []
        )

)

[2025-07-14 22:27:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502640
            [orderIds] => []
        )

)

[2025-07-14 22:27:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502651
            [orderIds] => []
        )

)

[2025-07-14 22:27:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502662
            [orderIds] => []
        )

)

[2025-07-14 22:27:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502673
            [orderIds] => []
        )

)

[2025-07-14 22:28:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502684
            [orderIds] => []
        )

)

[2025-07-14 22:28:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502695
            [orderIds] => []
        )

)

[2025-07-14 22:28:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502706
            [orderIds] => []
        )

)

[2025-07-14 22:28:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502717
            [orderIds] => []
        )

)

[2025-07-14 22:28:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502728
            [orderIds] => []
        )

)

[2025-07-14 22:28:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502739
            [orderIds] => []
        )

)

[2025-07-14 22:29:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502750
            [orderIds] => []
        )

)

[2025-07-14 22:29:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502761
            [orderIds] => []
        )

)

[2025-07-14 22:29:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502772
            [orderIds] => []
        )

)

[2025-07-14 22:29:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502783
            [orderIds] => []
        )

)

[2025-07-14 22:29:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502794
            [orderIds] => []
        )

)

[2025-07-14 22:30:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502805
            [orderIds] => []
        )

)

[2025-07-14 22:30:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502816
            [orderIds] => []
        )

)

[2025-07-14 22:30:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502827
            [orderIds] => []
        )

)

[2025-07-14 22:30:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502838
            [orderIds] => []
        )

)

[2025-07-14 22:30:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502849
            [orderIds] => []
        )

)

[2025-07-14 22:31:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502860
            [orderIds] => []
        )

)

[2025-07-14 22:31:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502871
            [orderIds] => []
        )

)

[2025-07-14 22:31:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502882
            [orderIds] => []
        )

)

[2025-07-14 22:31:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502893
            [orderIds] => []
        )

)

[2025-07-14 22:31:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502904
            [orderIds] => []
        )

)

[2025-07-14 22:31:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502915
            [orderIds] => []
        )

)

[2025-07-14 22:32:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502926
            [orderIds] => []
        )

)

[2025-07-14 22:32:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502937
            [orderIds] => []
        )

)

[2025-07-14 22:32:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502948
            [orderIds] => []
        )

)

[2025-07-14 22:32:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502959
            [orderIds] => []
        )

)

[2025-07-14 22:32:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502970
            [orderIds] => []
        )

)

[2025-07-14 22:33:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502981
            [orderIds] => []
        )

)

[2025-07-14 22:33:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752502992
            [orderIds] => []
        )

)

[2025-07-14 22:33:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503003
            [orderIds] => []
        )

)

[2025-07-14 22:33:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503014
            [orderIds] => []
        )

)

[2025-07-14 22:33:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503025
            [orderIds] => []
        )

)

[2025-07-14 22:33:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503036
            [orderIds] => []
        )

)

[2025-07-14 22:34:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503047
            [orderIds] => []
        )

)

[2025-07-14 22:34:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503058
            [orderIds] => []
        )

)

[2025-07-14 22:34:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503069
            [orderIds] => []
        )

)

[2025-07-14 22:34:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503080
            [orderIds] => []
        )

)

[2025-07-14 22:34:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503091
            [orderIds] => []
        )

)

[2025-07-14 22:35:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503102
            [orderIds] => []
        )

)

[2025-07-14 22:35:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503113
            [orderIds] => []
        )

)

[2025-07-14 22:35:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503124
            [orderIds] => []
        )

)

[2025-07-14 22:35:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 22:35:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 22:35:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 22:35:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 22:35:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503135
            [orderIds] => []
        )

)

[2025-07-14 22:35:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503146
            [orderIds] => []
        )

)

[2025-07-14 22:35:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503157
            [orderIds] => []
        )

)

[2025-07-14 22:36:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503168
            [orderIds] => []
        )

)

[2025-07-14 22:36:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503179
            [orderIds] => []
        )

)

[2025-07-14 22:36:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503190
            [orderIds] => []
        )

)

[2025-07-14 22:36:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503201
            [orderIds] => []
        )

)

[2025-07-14 22:36:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503212
            [orderIds] => []
        )

)

[2025-07-14 22:37:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503223
            [orderIds] => []
        )

)

[2025-07-14 22:37:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503234
            [orderIds] => []
        )

)

[2025-07-14 22:37:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503245
            [orderIds] => []
        )

)

[2025-07-14 22:37:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503256
            [orderIds] => []
        )

)

[2025-07-14 22:37:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503267
            [orderIds] => []
        )

)

[2025-07-14 22:37:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503278
            [orderIds] => []
        )

)

[2025-07-14 22:38:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503289
            [orderIds] => []
        )

)

[2025-07-14 22:38:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503300
            [orderIds] => []
        )

)

[2025-07-14 22:38:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503311
            [orderIds] => []
        )

)

[2025-07-14 22:38:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503322
            [orderIds] => []
        )

)

[2025-07-14 22:38:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503333
            [orderIds] => []
        )

)

[2025-07-14 22:39:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503344
            [orderIds] => []
        )

)

[2025-07-14 22:39:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503355
            [orderIds] => []
        )

)

[2025-07-14 22:39:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503366
            [orderIds] => []
        )

)

[2025-07-14 22:39:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503377
            [orderIds] => []
        )

)

[2025-07-14 22:39:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503388
            [orderIds] => []
        )

)

[2025-07-14 22:39:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503399
            [orderIds] => []
        )

)

[2025-07-14 22:40:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503410
            [orderIds] => []
        )

)

[2025-07-14 22:40:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503421
            [orderIds] => []
        )

)

[2025-07-14 22:40:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503432
            [orderIds] => []
        )

)

[2025-07-14 22:40:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503443
            [orderIds] => []
        )

)

[2025-07-14 22:40:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503454
            [orderIds] => []
        )

)

[2025-07-14 22:41:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503465
            [orderIds] => []
        )

)

[2025-07-14 22:41:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503476
            [orderIds] => []
        )

)

[2025-07-14 22:41:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503487
            [orderIds] => []
        )

)

[2025-07-14 22:41:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503498
            [orderIds] => []
        )

)

[2025-07-14 22:41:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503509
            [orderIds] => []
        )

)

[2025-07-14 22:42:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503520
            [orderIds] => []
        )

)

[2025-07-14 22:42:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503531
            [orderIds] => []
        )

)

[2025-07-14 22:42:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503542
            [orderIds] => []
        )

)

[2025-07-14 22:42:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503553
            [orderIds] => []
        )

)

[2025-07-14 22:42:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503564
            [orderIds] => []
        )

)

[2025-07-14 22:42:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503575
            [orderIds] => []
        )

)

[2025-07-14 22:43:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503586
            [orderIds] => []
        )

)

[2025-07-14 22:43:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503597
            [orderIds] => []
        )

)

[2025-07-14 22:43:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503608
            [orderIds] => []
        )

)

[2025-07-14 22:43:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503619
            [orderIds] => []
        )

)

[2025-07-14 22:43:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503630
            [orderIds] => []
        )

)

[2025-07-14 22:44:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503641
            [orderIds] => []
        )

)

[2025-07-14 22:44:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503652
            [orderIds] => []
        )

)

[2025-07-14 22:44:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503663
            [orderIds] => []
        )

)

[2025-07-14 22:44:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503674
            [orderIds] => []
        )

)

[2025-07-14 22:44:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503685
            [orderIds] => []
        )

)

[2025-07-14 22:44:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503696
            [orderIds] => []
        )

)

[2025-07-14 22:45:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503707
            [orderIds] => []
        )

)

[2025-07-14 22:45:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503718
            [orderIds] => []
        )

)

[2025-07-14 22:45:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503729
            [orderIds] => []
        )

)

[2025-07-14 22:45:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 22:45:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 22:45:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 22:45:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 22:45:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503740
            [orderIds] => []
        )

)

[2025-07-14 22:45:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503751
            [orderIds] => []
        )

)

[2025-07-14 22:46:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503762
            [orderIds] => []
        )

)

[2025-07-14 22:46:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503773
            [orderIds] => []
        )

)

[2025-07-14 22:46:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503784
            [orderIds] => []
        )

)

[2025-07-14 22:46:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503795
            [orderIds] => []
        )

)

[2025-07-14 22:46:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503806
            [orderIds] => []
        )

)

[2025-07-14 22:46:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503817
            [orderIds] => []
        )

)

[2025-07-14 22:47:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503828
            [orderIds] => []
        )

)

[2025-07-14 22:47:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503839
            [orderIds] => []
        )

)

[2025-07-14 22:47:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503850
            [orderIds] => []
        )

)

[2025-07-14 22:47:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503861
            [orderIds] => []
        )

)

[2025-07-14 22:47:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503872
            [orderIds] => []
        )

)

[2025-07-14 22:48:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503883
            [orderIds] => []
        )

)

[2025-07-14 22:48:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503894
            [orderIds] => []
        )

)

[2025-07-14 22:48:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503905
            [orderIds] => []
        )

)

[2025-07-14 22:48:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503916
            [orderIds] => []
        )

)

[2025-07-14 22:48:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503927
            [orderIds] => []
        )

)

[2025-07-14 22:48:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503938
            [orderIds] => []
        )

)

[2025-07-14 22:49:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503949
            [orderIds] => []
        )

)

[2025-07-14 22:49:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503960
            [orderIds] => []
        )

)

[2025-07-14 22:49:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503971
            [orderIds] => []
        )

)

[2025-07-14 22:49:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503982
            [orderIds] => []
        )

)

[2025-07-14 22:49:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752503993
            [orderIds] => []
        )

)

[2025-07-14 22:50:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504004
            [orderIds] => []
        )

)

[2025-07-14 22:50:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504015
            [orderIds] => []
        )

)

[2025-07-14 22:50:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504026
            [orderIds] => []
        )

)

[2025-07-14 22:50:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504037
            [orderIds] => []
        )

)

[2025-07-14 22:50:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504048
            [orderIds] => []
        )

)

[2025-07-14 22:50:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504059
            [orderIds] => []
        )

)

[2025-07-14 22:51:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504070
            [orderIds] => []
        )

)

[2025-07-14 22:51:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504081
            [orderIds] => []
        )

)

[2025-07-14 22:51:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504092
            [orderIds] => []
        )

)

[2025-07-14 22:51:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504103
            [orderIds] => []
        )

)

[2025-07-14 22:51:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504114
            [orderIds] => []
        )

)

[2025-07-14 22:52:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504125
            [orderIds] => []
        )

)

[2025-07-14 22:52:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504136
            [orderIds] => []
        )

)

[2025-07-14 22:52:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504147
            [orderIds] => []
        )

)

[2025-07-14 22:52:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504158
            [orderIds] => []
        )

)

[2025-07-14 22:52:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504169
            [orderIds] => []
        )

)

[2025-07-14 22:53:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504180
            [orderIds] => []
        )

)

[2025-07-14 22:53:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504191
            [orderIds] => []
        )

)

[2025-07-14 22:53:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504202
            [orderIds] => []
        )

)

[2025-07-14 22:53:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504213
            [orderIds] => []
        )

)

[2025-07-14 22:53:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504224
            [orderIds] => []
        )

)

[2025-07-14 22:53:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504235
            [orderIds] => []
        )

)

[2025-07-14 22:54:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504246
            [orderIds] => []
        )

)

[2025-07-14 22:54:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504257
            [orderIds] => []
        )

)

[2025-07-14 22:54:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504268
            [orderIds] => []
        )

)

[2025-07-14 22:54:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504279
            [orderIds] => []
        )

)

[2025-07-14 22:54:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1752418480
            [orderIds] => []
        )

)

[2025-07-14 22:54:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1751900080
            [orderIds] => []
        )

)

[2025-07-14 22:54:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1751900080
            [orderIds] => []
        )

)

[2025-07-14 22:54:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-07-14 22:54:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-07-14 22:54:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-07-14 22:54:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-07-14 22:54:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-07-14 22:54:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504290
            [orderIds] => []
        )

)

[2025-07-14 22:55:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504301
            [orderIds] => []
        )

)

[2025-07-14 22:55:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504312
            [orderIds] => []
        )

)

[2025-07-14 22:55:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504323
            [orderIds] => []
        )

)

[2025-07-14 22:55:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504334
            [orderIds] => []
        )

)

[2025-07-14 22:55:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 22:55:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 22:55:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 22:55:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 22:55:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504345
            [orderIds] => []
        )

)

[2025-07-14 22:55:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504356
            [orderIds] => []
        )

)

[2025-07-14 22:56:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504367
            [orderIds] => []
        )

)

[2025-07-14 22:56:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504378
            [orderIds] => []
        )

)

[2025-07-14 22:56:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504389
            [orderIds] => []
        )

)

[2025-07-14 22:56:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504400
            [orderIds] => []
        )

)

[2025-07-14 22:56:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504411
            [orderIds] => []
        )

)

[2025-07-14 22:57:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504422
            [orderIds] => []
        )

)

[2025-07-14 22:57:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504433
            [orderIds] => []
        )

)

[2025-07-14 22:57:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504444
            [orderIds] => []
        )

)

[2025-07-14 22:57:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504455
            [orderIds] => []
        )

)

[2025-07-14 22:57:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504466
            [orderIds] => []
        )

)

[2025-07-14 22:57:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504477
            [orderIds] => []
        )

)

[2025-07-14 22:58:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504488
            [orderIds] => []
        )

)

[2025-07-14 22:58:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504499
            [orderIds] => []
        )

)

[2025-07-14 22:58:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504510
            [orderIds] => []
        )

)

[2025-07-14 22:58:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504521
            [orderIds] => []
        )

)

[2025-07-14 22:58:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504532
            [orderIds] => []
        )

)

[2025-07-14 22:59:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504543
            [orderIds] => []
        )

)

[2025-07-14 22:59:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504554
            [orderIds] => []
        )

)

[2025-07-14 22:59:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504565
            [orderIds] => []
        )

)

[2025-07-14 22:59:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504576
            [orderIds] => []
        )

)

[2025-07-14 22:59:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504587
            [orderIds] => []
        )

)

[2025-07-14 22:59:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504598
            [orderIds] => []
        )

)

[2025-07-14 23:00:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504609
            [orderIds] => []
        )

)

[2025-07-14 23:00:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504620
            [orderIds] => []
        )

)

[2025-07-14 23:00:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504631
            [orderIds] => []
        )

)

[2025-07-14 23:00:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504642
            [orderIds] => []
        )

)

[2025-07-14 23:00:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504653
            [orderIds] => []
        )

)

[2025-07-14 23:01:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504664
            [orderIds] => []
        )

)

[2025-07-14 23:01:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504675
            [orderIds] => []
        )

)

[2025-07-14 23:01:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504686
            [orderIds] => []
        )

)

[2025-07-14 23:01:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504697
            [orderIds] => []
        )

)

[2025-07-14 23:01:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504708
            [orderIds] => []
        )

)

[2025-07-14 23:01:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504719
            [orderIds] => []
        )

)

[2025-07-14 23:02:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504730
            [orderIds] => []
        )

)

[2025-07-14 23:02:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504741
            [orderIds] => []
        )

)

[2025-07-14 23:02:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504752
            [orderIds] => []
        )

)

[2025-07-14 23:02:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504763
            [orderIds] => []
        )

)

[2025-07-14 23:02:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504774
            [orderIds] => []
        )

)

[2025-07-14 23:03:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504785
            [orderIds] => []
        )

)

[2025-07-14 23:03:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504796
            [orderIds] => []
        )

)

[2025-07-14 23:03:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504807
            [orderIds] => []
        )

)

[2025-07-14 23:03:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504818
            [orderIds] => []
        )

)

[2025-07-14 23:03:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504829
            [orderIds] => []
        )

)

[2025-07-14 23:04:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504840
            [orderIds] => []
        )

)

[2025-07-14 23:04:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504851
            [orderIds] => []
        )

)

[2025-07-14 23:04:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504862
            [orderIds] => []
        )

)

[2025-07-14 23:04:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504873
            [orderIds] => []
        )

)

[2025-07-14 23:04:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504884
            [orderIds] => []
        )

)

[2025-07-14 23:04:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504895
            [orderIds] => []
        )

)

[2025-07-14 23:05:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504906
            [orderIds] => []
        )

)

[2025-07-14 23:05:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504917
            [orderIds] => []
        )

)

[2025-07-14 23:05:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504928
            [orderIds] => []
        )

)

[2025-07-14 23:05:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 23:05:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 23:05:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 23:05:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 23:05:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504939
            [orderIds] => []
        )

)

[2025-07-14 23:05:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504950
            [orderIds] => []
        )

)

[2025-07-14 23:06:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504961
            [orderIds] => []
        )

)

[2025-07-14 23:06:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504972
            [orderIds] => []
        )

)

[2025-07-14 23:06:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504983
            [orderIds] => []
        )

)

[2025-07-14 23:06:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752504994
            [orderIds] => []
        )

)

[2025-07-14 23:06:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505005
            [orderIds] => []
        )

)

[2025-07-14 23:06:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505016
            [orderIds] => []
        )

)

[2025-07-14 23:07:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505027
            [orderIds] => []
        )

)

[2025-07-14 23:07:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505038
            [orderIds] => []
        )

)

[2025-07-14 23:07:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505049
            [orderIds] => []
        )

)

[2025-07-14 23:07:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505060
            [orderIds] => []
        )

)

[2025-07-14 23:07:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505071
            [orderIds] => []
        )

)

[2025-07-14 23:08:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505082
            [orderIds] => []
        )

)

[2025-07-14 23:08:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505093
            [orderIds] => []
        )

)

[2025-07-14 23:08:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505104
            [orderIds] => []
        )

)

[2025-07-14 23:08:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505115
            [orderIds] => []
        )

)

[2025-07-14 23:08:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505126
            [orderIds] => []
        )

)

[2025-07-14 23:08:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505137
            [orderIds] => []
        )

)

[2025-07-14 23:09:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505148
            [orderIds] => []
        )

)

[2025-07-14 23:09:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505159
            [orderIds] => []
        )

)

[2025-07-14 23:09:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505170
            [orderIds] => []
        )

)

[2025-07-14 23:09:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505181
            [orderIds] => []
        )

)

[2025-07-14 23:09:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505192
            [orderIds] => []
        )

)

[2025-07-14 23:10:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505203
            [orderIds] => []
        )

)

[2025-07-14 23:10:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505214
            [orderIds] => []
        )

)

[2025-07-14 23:10:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505225
            [orderIds] => []
        )

)

[2025-07-14 23:10:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505236
            [orderIds] => []
        )

)

[2025-07-14 23:10:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505247
            [orderIds] => []
        )

)

[2025-07-14 23:10:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505258
            [orderIds] => []
        )

)

[2025-07-14 23:11:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505269
            [orderIds] => []
        )

)

[2025-07-14 23:11:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505280
            [orderIds] => []
        )

)

[2025-07-14 23:11:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505291
            [orderIds] => []
        )

)

[2025-07-14 23:11:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505302
            [orderIds] => []
        )

)

[2025-07-14 23:11:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505313
            [orderIds] => []
        )

)

[2025-07-14 23:12:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505324
            [orderIds] => []
        )

)

[2025-07-14 23:12:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505335
            [orderIds] => []
        )

)

[2025-07-14 23:12:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505346
            [orderIds] => []
        )

)

[2025-07-14 23:12:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505357
            [orderIds] => []
        )

)

[2025-07-14 23:12:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505368
            [orderIds] => []
        )

)

[2025-07-14 23:12:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505379
            [orderIds] => []
        )

)

[2025-07-14 23:13:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505390
            [orderIds] => []
        )

)

[2025-07-14 23:13:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505401
            [orderIds] => []
        )

)

[2025-07-14 23:13:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505412
            [orderIds] => []
        )

)

[2025-07-14 23:13:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505423
            [orderIds] => []
        )

)

[2025-07-14 23:13:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505434
            [orderIds] => []
        )

)

[2025-07-14 23:14:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505445
            [orderIds] => []
        )

)

[2025-07-14 23:14:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505456
            [orderIds] => []
        )

)

[2025-07-14 23:14:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505467
            [orderIds] => []
        )

)

[2025-07-14 23:14:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505478
            [orderIds] => []
        )

)

[2025-07-14 23:14:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505489
            [orderIds] => []
        )

)

[2025-07-14 23:15:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505500
            [orderIds] => []
        )

)

[2025-07-14 23:15:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505511
            [orderIds] => []
        )

)

[2025-07-14 23:15:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505522
            [orderIds] => []
        )

)

[2025-07-14 23:15:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505533
            [orderIds] => []
        )

)

[2025-07-14 23:15:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 23:15:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 23:15:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 23:15:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 23:15:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505544
            [orderIds] => []
        )

)

[2025-07-14 23:15:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505555
            [orderIds] => []
        )

)

[2025-07-14 23:16:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505566
            [orderIds] => []
        )

)

[2025-07-14 23:16:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505577
            [orderIds] => []
        )

)

[2025-07-14 23:16:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505588
            [orderIds] => []
        )

)

[2025-07-14 23:16:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505599
            [orderIds] => []
        )

)

[2025-07-14 23:16:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505610
            [orderIds] => []
        )

)

[2025-07-14 23:17:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505621
            [orderIds] => []
        )

)

[2025-07-14 23:17:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505632
            [orderIds] => []
        )

)

[2025-07-14 23:17:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505643
            [orderIds] => []
        )

)

[2025-07-14 23:17:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505654
            [orderIds] => []
        )

)

[2025-07-14 23:17:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505665
            [orderIds] => []
        )

)

[2025-07-14 23:17:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505676
            [orderIds] => []
        )

)

[2025-07-14 23:18:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505687
            [orderIds] => []
        )

)

[2025-07-14 23:18:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505698
            [orderIds] => []
        )

)

[2025-07-14 23:18:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505709
            [orderIds] => []
        )

)

[2025-07-14 23:18:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505720
            [orderIds] => []
        )

)

[2025-07-14 23:18:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505731
            [orderIds] => []
        )

)

[2025-07-14 23:19:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505742
            [orderIds] => []
        )

)

[2025-07-14 23:19:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505753
            [orderIds] => []
        )

)

[2025-07-14 23:19:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505764
            [orderIds] => []
        )

)

[2025-07-14 23:19:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505775
            [orderIds] => []
        )

)

[2025-07-14 23:19:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505786
            [orderIds] => []
        )

)

[2025-07-14 23:19:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505797
            [orderIds] => []
        )

)

[2025-07-14 23:20:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505808
            [orderIds] => []
        )

)

[2025-07-14 23:20:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505819
            [orderIds] => []
        )

)

[2025-07-14 23:20:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505830
            [orderIds] => []
        )

)

[2025-07-14 23:20:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505841
            [orderIds] => []
        )

)

[2025-07-14 23:20:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505852
            [orderIds] => []
        )

)

[2025-07-14 23:21:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505863
            [orderIds] => []
        )

)

[2025-07-14 23:21:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505874
            [orderIds] => []
        )

)

[2025-07-14 23:21:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505885
            [orderIds] => []
        )

)

[2025-07-14 23:21:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505896
            [orderIds] => []
        )

)

[2025-07-14 23:21:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505907
            [orderIds] => []
        )

)

[2025-07-14 23:21:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505918
            [orderIds] => []
        )

)

[2025-07-14 23:22:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505929
            [orderIds] => []
        )

)

[2025-07-14 23:22:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505940
            [orderIds] => []
        )

)

[2025-07-14 23:22:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505951
            [orderIds] => []
        )

)

[2025-07-14 23:22:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505962
            [orderIds] => []
        )

)

[2025-07-14 23:22:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505973
            [orderIds] => []
        )

)

[2025-07-14 23:23:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505984
            [orderIds] => []
        )

)

[2025-07-14 23:23:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752505995
            [orderIds] => []
        )

)

[2025-07-14 23:23:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506006
            [orderIds] => []
        )

)

[2025-07-14 23:23:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506017
            [orderIds] => []
        )

)

[2025-07-14 23:23:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506028
            [orderIds] => []
        )

)

[2025-07-14 23:23:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506039
            [orderIds] => []
        )

)

[2025-07-14 23:24:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506050
            [orderIds] => []
        )

)

[2025-07-14 23:24:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506061
            [orderIds] => []
        )

)

[2025-07-14 23:24:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506072
            [orderIds] => []
        )

)

[2025-07-14 23:24:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1752420281
            [orderIds] => []
        )

)

[2025-07-14 23:24:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1751901881
            [orderIds] => []
        )

)

[2025-07-14 23:24:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1751901881
            [orderIds] => []
        )

)

[2025-07-14 23:24:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-07-14 23:24:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-07-14 23:24:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506083
            [orderIds] => []
        )

)

[2025-07-14 23:24:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-07-14 23:24:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-07-14 23:24:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-07-14 23:24:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506094
            [orderIds] => []
        )

)

[2025-07-14 23:25:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506105
            [orderIds] => []
        )

)

[2025-07-14 23:25:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506116
            [orderIds] => []
        )

)

[2025-07-14 23:25:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506127
            [orderIds] => []
        )

)

[2025-07-14 23:25:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506138
            [orderIds] => []
        )

)

[2025-07-14 23:25:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 23:25:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 23:25:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 23:25:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 23:25:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506149
            [orderIds] => []
        )

)

[2025-07-14 23:26:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506160
            [orderIds] => []
        )

)

[2025-07-14 23:26:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506171
            [orderIds] => []
        )

)

[2025-07-14 23:26:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506182
            [orderIds] => []
        )

)

[2025-07-14 23:26:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506193
            [orderIds] => []
        )

)

[2025-07-14 23:26:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506204
            [orderIds] => []
        )

)

[2025-07-14 23:26:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506215
            [orderIds] => []
        )

)

[2025-07-14 23:27:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506226
            [orderIds] => []
        )

)

[2025-07-14 23:27:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506237
            [orderIds] => []
        )

)

[2025-07-14 23:27:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506248
            [orderIds] => []
        )

)

[2025-07-14 23:27:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506259
            [orderIds] => []
        )

)

[2025-07-14 23:27:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506270
            [orderIds] => []
        )

)

[2025-07-14 23:28:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506281
            [orderIds] => []
        )

)

[2025-07-14 23:28:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506292
            [orderIds] => []
        )

)

[2025-07-14 23:28:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506303
            [orderIds] => []
        )

)

[2025-07-14 23:28:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506314
            [orderIds] => []
        )

)

[2025-07-14 23:28:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506325
            [orderIds] => []
        )

)

[2025-07-14 23:28:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506336
            [orderIds] => []
        )

)

[2025-07-14 23:29:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506347
            [orderIds] => []
        )

)

[2025-07-14 23:29:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506358
            [orderIds] => []
        )

)

[2025-07-14 23:29:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506369
            [orderIds] => []
        )

)

[2025-07-14 23:29:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506380
            [orderIds] => []
        )

)

[2025-07-14 23:29:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506391
            [orderIds] => []
        )

)

[2025-07-14 23:30:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506402
            [orderIds] => []
        )

)

[2025-07-14 23:30:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506413
            [orderIds] => []
        )

)

[2025-07-14 23:30:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506424
            [orderIds] => []
        )

)

[2025-07-14 23:30:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506435
            [orderIds] => []
        )

)

[2025-07-14 23:30:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506446
            [orderIds] => []
        )

)

[2025-07-14 23:30:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506457
            [orderIds] => []
        )

)

[2025-07-14 23:31:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506468
            [orderIds] => []
        )

)

[2025-07-14 23:31:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506479
            [orderIds] => []
        )

)

[2025-07-14 23:31:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506490
            [orderIds] => []
        )

)

[2025-07-14 23:31:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506501
            [orderIds] => []
        )

)

[2025-07-14 23:31:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506512
            [orderIds] => []
        )

)

[2025-07-14 23:32:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506523
            [orderIds] => []
        )

)

[2025-07-14 23:32:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506534
            [orderIds] => []
        )

)

[2025-07-14 23:32:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506545
            [orderIds] => []
        )

)

[2025-07-14 23:32:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506556
            [orderIds] => []
        )

)

[2025-07-14 23:32:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506567
            [orderIds] => []
        )

)

[2025-07-14 23:32:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506578
            [orderIds] => []
        )

)

[2025-07-14 23:33:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506589
            [orderIds] => []
        )

)

[2025-07-14 23:33:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506600
            [orderIds] => []
        )

)

[2025-07-14 23:33:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506611
            [orderIds] => []
        )

)

[2025-07-14 23:33:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506622
            [orderIds] => []
        )

)

[2025-07-14 23:33:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506633
            [orderIds] => []
        )

)

[2025-07-14 23:34:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506644
            [orderIds] => []
        )

)

[2025-07-14 23:34:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506655
            [orderIds] => []
        )

)

[2025-07-14 23:34:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506666
            [orderIds] => []
        )

)

[2025-07-14 23:34:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506677
            [orderIds] => []
        )

)

[2025-07-14 23:34:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506688
            [orderIds] => []
        )

)

[2025-07-14 23:34:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506699
            [orderIds] => []
        )

)

[2025-07-14 23:35:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506710
            [orderIds] => []
        )

)

[2025-07-14 23:35:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506721
            [orderIds] => []
        )

)

[2025-07-14 23:35:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506732
            [orderIds] => []
        )

)

[2025-07-14 23:35:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 23:35:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 23:35:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 23:35:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 23:35:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506743
            [orderIds] => []
        )

)

[2025-07-14 23:35:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506754
            [orderIds] => []
        )

)

[2025-07-14 23:36:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506765
            [orderIds] => []
        )

)

[2025-07-14 23:36:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506776
            [orderIds] => []
        )

)

[2025-07-14 23:36:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506787
            [orderIds] => []
        )

)

[2025-07-14 23:36:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506798
            [orderIds] => []
        )

)

[2025-07-14 23:36:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506809
            [orderIds] => []
        )

)

[2025-07-14 23:37:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506820
            [orderIds] => []
        )

)

[2025-07-14 23:37:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506831
            [orderIds] => []
        )

)

[2025-07-14 23:37:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506842
            [orderIds] => []
        )

)

[2025-07-14 23:37:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506853
            [orderIds] => []
        )

)

[2025-07-14 23:37:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506864
            [orderIds] => []
        )

)

[2025-07-14 23:37:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506875
            [orderIds] => []
        )

)

[2025-07-14 23:38:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506886
            [orderIds] => []
        )

)

[2025-07-14 23:38:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506897
            [orderIds] => []
        )

)

[2025-07-14 23:38:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506908
            [orderIds] => []
        )

)

[2025-07-14 23:38:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506919
            [orderIds] => []
        )

)

[2025-07-14 23:38:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506930
            [orderIds] => []
        )

)

[2025-07-14 23:39:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506941
            [orderIds] => []
        )

)

[2025-07-14 23:39:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506952
            [orderIds] => []
        )

)

[2025-07-14 23:39:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506963
            [orderIds] => []
        )

)

[2025-07-14 23:39:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506974
            [orderIds] => []
        )

)

[2025-07-14 23:39:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506985
            [orderIds] => []
        )

)

[2025-07-14 23:39:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752506996
            [orderIds] => []
        )

)

[2025-07-14 23:40:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507007
            [orderIds] => []
        )

)

[2025-07-14 23:40:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507018
            [orderIds] => []
        )

)

[2025-07-14 23:40:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507029
            [orderIds] => []
        )

)

[2025-07-14 23:40:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507040
            [orderIds] => []
        )

)

[2025-07-14 23:40:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507051
            [orderIds] => []
        )

)

[2025-07-14 23:41:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507062
            [orderIds] => []
        )

)

[2025-07-14 23:41:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507073
            [orderIds] => []
        )

)

[2025-07-14 23:41:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507084
            [orderIds] => []
        )

)

[2025-07-14 23:41:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507095
            [orderIds] => []
        )

)

[2025-07-14 23:41:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507106
            [orderIds] => []
        )

)

[2025-07-14 23:41:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507117
            [orderIds] => []
        )

)

[2025-07-14 23:42:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507128
            [orderIds] => []
        )

)

[2025-07-14 23:42:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507139
            [orderIds] => []
        )

)

[2025-07-14 23:42:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507150
            [orderIds] => []
        )

)

[2025-07-14 23:42:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507161
            [orderIds] => []
        )

)

[2025-07-14 23:42:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507172
            [orderIds] => []
        )

)

[2025-07-14 23:43:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507183
            [orderIds] => []
        )

)

[2025-07-14 23:43:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507194
            [orderIds] => []
        )

)

[2025-07-14 23:43:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507205
            [orderIds] => []
        )

)

[2025-07-14 23:43:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507216
            [orderIds] => []
        )

)

[2025-07-14 23:43:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507227
            [orderIds] => []
        )

)

[2025-07-14 23:43:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507238
            [orderIds] => []
        )

)

[2025-07-14 23:44:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507249
            [orderIds] => []
        )

)

[2025-07-14 23:44:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507260
            [orderIds] => []
        )

)

[2025-07-14 23:44:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507271
            [orderIds] => []
        )

)

[2025-07-14 23:44:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507282
            [orderIds] => []
        )

)

[2025-07-14 23:44:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507293
            [orderIds] => []
        )

)

[2025-07-14 23:45:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507304
            [orderIds] => []
        )

)

[2025-07-14 23:45:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507315
            [orderIds] => []
        )

)

[2025-07-14 23:45:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507326
            [orderIds] => []
        )

)

[2025-07-14 23:45:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507337
            [orderIds] => []
        )

)

[2025-07-14 23:45:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 23:45:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 23:45:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 23:45:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 23:45:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507348
            [orderIds] => []
        )

)

[2025-07-14 23:45:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507359
            [orderIds] => []
        )

)

[2025-07-14 23:46:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507370
            [orderIds] => []
        )

)

[2025-07-14 23:46:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507381
            [orderIds] => []
        )

)

[2025-07-14 23:46:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507392
            [orderIds] => []
        )

)

[2025-07-14 23:46:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507403
            [orderIds] => []
        )

)

[2025-07-14 23:46:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507414
            [orderIds] => []
        )

)

[2025-07-14 23:47:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507425
            [orderIds] => []
        )

)

[2025-07-14 23:47:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507436
            [orderIds] => []
        )

)

[2025-07-14 23:47:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507447
            [orderIds] => []
        )

)

[2025-07-14 23:47:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507458
            [orderIds] => []
        )

)

[2025-07-14 23:47:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507469
            [orderIds] => []
        )

)

[2025-07-14 23:48:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507480
            [orderIds] => []
        )

)

[2025-07-14 23:48:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507491
            [orderIds] => []
        )

)

[2025-07-14 23:48:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507502
            [orderIds] => []
        )

)

[2025-07-14 23:48:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507513
            [orderIds] => []
        )

)

[2025-07-14 23:48:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507524
            [orderIds] => []
        )

)

[2025-07-14 23:48:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507535
            [orderIds] => []
        )

)

[2025-07-14 23:49:06] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507546
            [orderIds] => []
        )

)

[2025-07-14 23:49:17] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507557
            [orderIds] => []
        )

)

[2025-07-14 23:49:28] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507568
            [orderIds] => []
        )

)

[2025-07-14 23:49:39] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507579
            [orderIds] => []
        )

)

[2025-07-14 23:49:50] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507590
            [orderIds] => []
        )

)

[2025-07-14 23:50:01] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507601
            [orderIds] => []
        )

)

[2025-07-14 23:50:12] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507612
            [orderIds] => []
        )

)

[2025-07-14 23:50:23] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507623
            [orderIds] => []
        )

)

[2025-07-14 23:50:34] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507634
            [orderIds] => []
        )

)

[2025-07-14 23:50:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507645
            [orderIds] => []
        )

)

[2025-07-14 23:50:56] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507656
            [orderIds] => []
        )

)

[2025-07-14 23:51:07] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507667
            [orderIds] => []
        )

)

[2025-07-14 23:51:18] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507678
            [orderIds] => []
        )

)

[2025-07-14 23:51:29] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507689
            [orderIds] => []
        )

)

[2025-07-14 23:51:40] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507700
            [orderIds] => []
        )

)

[2025-07-14 23:51:51] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507711
            [orderIds] => []
        )

)

[2025-07-14 23:52:02] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507722
            [orderIds] => []
        )

)

[2025-07-14 23:52:13] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507733
            [orderIds] => []
        )

)

[2025-07-14 23:52:24] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507744
            [orderIds] => []
        )

)

[2025-07-14 23:52:35] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507755
            [orderIds] => []
        )

)

[2025-07-14 23:52:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507766
            [orderIds] => []
        )

)

[2025-07-14 23:52:57] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507777
            [orderIds] => []
        )

)

[2025-07-14 23:53:08] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507788
            [orderIds] => []
        )

)

[2025-07-14 23:53:19] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507799
            [orderIds] => []
        )

)

[2025-07-14 23:53:30] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507810
            [orderIds] => []
        )

)

[2025-07-14 23:53:41] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507821
            [orderIds] => []
        )

)

[2025-07-14 23:53:52] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507832
            [orderIds] => []
        )

)

[2025-07-14 23:54:03] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507843
            [orderIds] => []
        )

)

[2025-07-14 23:54:14] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507854
            [orderIds] => []
        )

)

[2025-07-14 23:54:25] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507865
            [orderIds] => []
        )

)

[2025-07-14 23:54:36] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507876
            [orderIds] => []
        )

)

[2025-07-14 23:54:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeHours] => 24
            [deadlineTime] => 1752422082
            [orderIds] => []
        )

)

[2025-07-14 23:54:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => receiveEvent
    [param] => Array
        (
            [storeId] => 10001
            [receiveDays] => 7
            [deadlineTime] => 1751903682
            [orderIds] => []
        )

)

[2025-07-14 23:54:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => Order
    [method] => settledEvent
    [param] => Array
        (
            [storeId] => 10001
            [refundDays] => 7
            [deadlineTime] => 1751903682
            [orderIds] => []
        )

)

[2025-07-14 23:54:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserCoupon
    [method] => setExpired
    [param] => Array
        (
            [storeId] => 10001
            [couponIds] => []
        )

)

[2025-07-14 23:54:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => UserGrade
    [method] => setUserGrade
    [param] => Array
        (
            [storeId] => 10001
            [data] => Array
                (
                )

        )

)

[2025-07-14 23:54:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => invalidEvent
    [param] => Array
        (
            [storeId] => 10001
            [invalidOrderIds] => []
        )

)

[2025-07-14 23:54:45] [info] Array
(
    [name] => 定时任务
    [Task-Key] => DealerOrder
    [method] => grantMoneyEvent
    [param] => Array
        (
            [storeId] => 10001
            [ids] => []
            [orderIds] => []
        )

)

[2025-07-14 23:54:46] [info] Array
(
    [name] => 定时任务
    [Task-Key] => BargainTask
    [method] => overdueEvent
    [param] => Array
        (
            [storeId] => 10001
            [endTaskIds] => []
        )

)

[2025-07-14 23:54:47] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507887
            [orderIds] => []
        )

)

[2025-07-14 23:54:58] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507898
            [orderIds] => []
        )

)

[2025-07-14 23:55:09] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507909
            [orderIds] => []
        )

)

[2025-07-14 23:55:20] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507920
            [orderIds] => []
        )

)

[2025-07-14 23:55:31] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507931
            [orderIds] => []
        )

)

[2025-07-14 23:55:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507942
            [orderIds] => []
        )

)

[2025-07-14 23:55:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskMockEvent
    [param] => Array
        (
            [title] => 拼单模拟成团事件
            [robotIds] => []
        )

)

[2025-07-14 23:55:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskFailEvent
    [param] => Array
        (
            [title] => 拼单失败事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 23:55:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => taskCompleteEvent
    [param] => Array
        (
            [title] => 拼单完成事件
            [storeId] => 10001
            [taskIds] => []
        )

)

[2025-07-14 23:55:42] [info] Array
(
    [name] => 定时任务
    [Task-Key] => GrouponTask
    [method] => cancelCompleteOrder
    [param] => Array
        (
            [title] => 将已完成拼单的未付款订单取消
            [storeId] => 10001
            [orderIds] => []
        )

)

[2025-07-14 23:55:53] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507953
            [orderIds] => []
        )

)

[2025-07-14 23:56:04] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507964
            [orderIds] => []
        )

)

[2025-07-14 23:56:15] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507975
            [orderIds] => []
        )

)

[2025-07-14 23:56:26] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507986
            [orderIds] => []
        )

)

[2025-07-14 23:56:37] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752507997
            [orderIds] => []
        )

)

[2025-07-14 23:56:48] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508008
            [orderIds] => []
        )

)

[2025-07-14 23:56:59] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508019
            [orderIds] => []
        )

)

[2025-07-14 23:57:10] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508030
            [orderIds] => []
        )

)

[2025-07-14 23:57:21] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508041
            [orderIds] => []
        )

)

[2025-07-14 23:57:32] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508052
            [orderIds] => []
        )

)

[2025-07-14 23:57:43] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508063
            [orderIds] => []
        )

)

[2025-07-14 23:57:54] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508074
            [orderIds] => []
        )

)

[2025-07-14 23:58:05] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508085
            [orderIds] => []
        )

)

[2025-07-14 23:58:16] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508096
            [orderIds] => []
        )

)

[2025-07-14 23:58:27] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508107
            [orderIds] => []
        )

)

[2025-07-14 23:58:38] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508118
            [orderIds] => []
        )

)

[2025-07-14 23:58:49] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508129
            [orderIds] => []
        )

)

[2025-07-14 23:59:00] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508140
            [orderIds] => []
        )

)

[2025-07-14 23:59:11] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508151
            [orderIds] => []
        )

)

[2025-07-14 23:59:22] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508162
            [orderIds] => []
        )

)

[2025-07-14 23:59:33] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508173
            [orderIds] => []
        )

)

[2025-07-14 23:59:44] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508184
            [orderIds] => []
        )

)

[2025-07-14 23:59:55] [info] Array
(
    [name] => 定时任务
    [Task-Key] => SharpOrder
    [method] => closeEvent
    [param] => Array
        (
            [storeId] => 10001
            [closeMinute] => 10
            [deadlineTime] => 1752508195
            [orderIds] => []
        )

)

