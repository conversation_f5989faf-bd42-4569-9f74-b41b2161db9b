<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\sharp;

use app\common\model\sharp\Active as ActiveModel;

/**
 * 整点秒杀-活动会场模型
 * Class Active
 * @package app\api\model\sharp
 */
class Active extends ActiveModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'is_delete',
        'store_id',
        'create_time',
        'update_time',
    ];

    /**
     * 获取当天的活动
     * @return Active|array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getNowActive()
    {
        $todayTime = strtotime(date('Y-m-d'));
        return $this->getActiveByDate($todayTime, '=');
    }

    /**
     * 获取当天的活动
     * @return Active|array|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getNextActive()
    {
        $todayTime = strtotime(date('Y-m-d'));
        return $this->getActiveByDate($todayTime, '>');
    }

    /**
     * 根据日期获取活动
     * @param $date
     * @param string $op
     * @return array|null|static
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getActiveByDate($date, string $op = '=')
    {
        return $this->where('active_date', $op, $date)
            ->where('status', '=', 1)
            ->where('is_delete', '=', 0)
            ->find();
    }
}