<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\service;

use app\api\model\h5\Setting as H5SettingModel;
use app\api\model\wxofficial\Setting as WxofficialSettingModel;
use app\common\library\helper;
use app\common\service\BaseService;

/**
 * 服务类：客户端公共数据
 * Class Client
 * @package app\api\service
 */
class Client extends BaseService
{
    /**
     * 客户端公共数据
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPublic(): array
    {
        return [
            'h5' => $this->getH5Public(),
            'wxofficial' => $this->getWxofficialPublic(),
            'mpAlipay' => $this->getAliappPublic(),
        ];
    }

    /**
     * 获取H5端公共数据
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getH5Public(): array
    {
        $values = H5SettingModel::getItem('basic');
        return ['setting' => helper::pick($values, ['enabled', 'baseUrl'])];
    }

    /**
     * 获取微信公众号端公共数据
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getWxofficialPublic(): array
    {
        $data = WxofficialSettingModel::getAll();
        return ['setting' => [
            'basic' => helper::pick($data['basic']['values'], ['name', 'qrcodeImageUrl']),
            'share' => helper::pick($data['share']['values'], ['enabled', 'title', 'desc', 'imgUrl']),
        ]];
    }

    /**
     * 获取支付宝小程序端公共数据
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getAliappPublic(): array
    {
        $class = '\app\api\model\mp\alipay\Setting';
        if (!class_exists($class)) {
            return [];
        }
        $data = $class::getAll();
        return ['setting' => [
            'basic' => helper::pick($data['basic']['values'], ['enabled']),
            'customer' => helper::pick($data['customer']['values'], ['enabled', 'provider', 'config']),
        ]];
    }
}