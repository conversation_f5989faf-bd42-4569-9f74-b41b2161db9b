<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\common\library\collector\tools;


/**
 * 根据url获取商品来源
 * Class UrlStore
 * @package app\common\library\collector\tools
 */
class UrlStore
{
    // 商品URL
    private string $url;

    /**
     * 构造方法
     * @param string $url
     */
    public function __construct(string $url)
    {
        $this->url = $url;
    }

    /**
     * 根据url获取商品来源
     * @return string|false
     */
    public function getStore()
    {
        if (stripos($this->url, 'tmall.com')) {
            return 'tmall';
        } else if (stripos($this->url, 'taobao.com')) {
            return 'taobao';
        } else if (stripos($this->url, 'jd.com')) {
            return 'jd';
        } else if (stripos($this->url, '1688.com')) {
            return 'alibaba';
        } else if (stripos($this->url, 'pinduoduo.com') || stripos($this->url, 'yangkeduo.com')) {
            return 'pdd';
        } else if (stripos($this->url, 'suning.com')) {
            return 'suning';
        }
        return false;
    }
}