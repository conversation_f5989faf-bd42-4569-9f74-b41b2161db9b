import{$ as e,o as t,c as a,w as l,a as s,k as i,n as o,f as n,t as r,l as d,i as c,P as p,d as u,F as m,e as y,g,L as f,M as h,b as _,Q as S,v as k,j as b,R as x,z as v,r as C,T as w,U as L,C as I}from"./index-DfvVnKvn.js";import{_ as $}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{_ as T}from"./u-icon.CQ4TvMm_.js";import{r as j}from"./uni-app.es.CHbJxIE1.js";import{C as B}from"./index.CbkdplPd.js";import{_ as A}from"./mp-html.Bz-Agw0_.js";import{r as z}from"./myCoupon.BznRU_d6.js";import{A as O}from"./index.4G-6cgLu.js";import{C as N}from"./index.B97P2zR-.js";import{A as P,G as M}from"./GoodsStatus.DCsdTGXk.js";import{_ as G}from"./u-tag.DZ8X7Oyb.js";import{h as D}from"./color.D-c1b2x3.js";import{A as R}from"./ActiveStatus.D3QA1xyx.js";const U="page/detail";function F(t){return e.get(U,{pageId:t})}const W=$({props:{itemIndex:String,itemStyle:Object,params:Object},methods:{onTargetSearch(){this.$navTo("pages/search/index")}}},[["render",function(e,p,u,m,y,g){const f=d,h=c;return t(),a(h,{class:"diy-search"},{default:l((()=>[s(h,{class:i(["inner",u.itemStyle.searchStyle]),onClick:g.onTargetSearch},{default:l((()=>[s(h,{class:"search-input",style:o({textAlign:u.itemStyle.textAlign})},{default:l((()=>[s(f,{class:"search-icon iconfont icon-search"}),s(f,null,{default:l((()=>[n(r(u.params.placeholder),1)])),_:1})])),_:1},8,["style"])])),_:1},8,["class","onClick"])])),_:1})}],["__scopeId","data-v-0cb1efc8"]]),E={data:()=>({}),methods:{onLink:p}};const H=$({name:"Images",props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},mixins:[E],methods:{}},[["render",function(e,i,n,r,d,p){const f=g,h=c;return t(),a(h,{class:"diy-imageSingle",style:o({paddingBottom:2*n.itemStyle.paddingTop+"rpx",background:n.itemStyle.background})},{default:l((()=>[(t(!0),u(m,null,y(n.dataList,((i,r)=>(t(),a(h,{class:"item-image",key:r,style:o({padding:`${2*n.itemStyle.paddingTop}rpx ${2*n.itemStyle.paddingLeft}rpx 0`})},{default:l((()=>[s(h,{class:"nav-to",onClick:t=>e.onLink(i.link)},{default:l((()=>[s(f,{class:"image",src:i.imgUrl,mode:"widthFix"},null,8,["src"])])),_:2},1032,["onClick"])])),_:2},1032,["style"])))),128))])),_:1},8,["style"])}],["__scopeId","data-v-6836e2d3"]]);const Z=$({name:"Banner",props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},mixins:[E],data:()=>({windowWidth:750,indicatorDots:!1,autoplay:!0,duration:800,imgHeights:[],imgCurrent:0}),created(){const e=this;uni.getSystemInfo({success({windowWidth:t}){e.windowWidth=t>750?750:t}})},methods:{_imagesHeight({detail:e}){const{width:t,height:a}=e,l=t/a,s=this.windowWidth/l;this.imgHeights.push(s)},_bindChange(e){this.imgCurrent=e.detail.current}}},[["render",function(e,n,r,d,p,_){const S=g,k=f,b=h,x=c;return t(),a(x,{class:"diy-banner",style:o({height:`${p.imgHeights[p.imgCurrent]}px`})},{default:l((()=>[s(b,{autoplay:p.autoplay,class:"swiper-box",duration:p.duration,circular:!0,interval:1e3*r.itemStyle.interval,onChange:_._bindChange},{default:l((()=>[(t(!0),u(m,null,y(r.dataList,((i,o)=>(t(),a(k,{key:o},{default:l((()=>[s(S,{mode:"widthFix",class:"slide-image",src:i.imgUrl,onClick:t=>e.onLink(i.link),onLoad:_._imagesHeight},null,8,["src","onClick","onLoad"])])),_:2},1024)))),128))])),_:1},8,["autoplay","duration","interval","onChange"]),s(x,{class:i(["indicator-dots",r.itemStyle.btnShape])},{default:l((()=>[(t(!0),u(m,null,y(r.dataList,((e,l)=>(t(),a(x,{class:i(["dots-item",{active:p.imgCurrent==l}]),style:o({backgroundColor:r.itemStyle.btnColor}),key:l},null,8,["class","style"])))),128))])),_:1},8,["class"])])),_:1},8,["style"])}],["__scopeId","data-v-55fd6fc7"]]);const V=$({name:"Window",props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},mixins:[E],methods:{}},[["render",function(e,n,r,d,p,f){const h=g,S=c;return t(),a(S,{class:"diy-window",style:o({background:r.itemStyle.background,padding:`${r.itemStyle.paddingTop}px ${r.itemStyle.paddingLeft}px`})},{default:l((()=>[r.itemStyle.layout>-1?(t(),a(S,{key:0,class:i(["data-list",[`avg-sm-${r.itemStyle.layout}`]])},{default:l((()=>[(t(!0),u(m,null,y(r.dataList,((i,n)=>(t(),a(S,{key:n,class:"data-item",style:o({padding:`${r.itemStyle.paddingTop}px ${r.itemStyle.paddingLeft}px`})},{default:l((()=>[s(S,{class:"item-image",onClick:t=>e.onLink(i.link)},{default:l((()=>[s(h,{class:"image",mode:"widthFix",src:i.imgUrl},null,8,["src"])])),_:2},1032,["onClick"])])),_:2},1032,["style"])))),128))])),_:1},8,["class"])):(t(),a(S,{key:1,class:"display"},{default:l((()=>[s(S,{class:"display-left",style:o({padding:`${r.itemStyle.paddingTop}px ${r.itemStyle.paddingLeft}px`})},{default:l((()=>[s(h,{class:"image",onClick:n[0]||(n[0]=t=>e.onLink(r.dataList[0].link)),src:r.dataList[0].imgUrl},null,8,["src"])])),_:1},8,["style"]),s(S,{class:"display-right"},{default:l((()=>[r.dataList.length>=2?(t(),a(S,{key:0,class:"display-right1",style:o({padding:`${r.itemStyle.paddingTop}px ${r.itemStyle.paddingLeft}px`})},{default:l((()=>[s(h,{class:"image",onClick:n[1]||(n[1]=t=>e.onLink(r.dataList[1].link)),src:r.dataList[1].imgUrl},null,8,["src"])])),_:1},8,["style"])):_("",!0),s(S,{class:"display-right2"},{default:l((()=>[r.dataList.length>=3?(t(),a(S,{key:0,class:"left",style:o({padding:`${r.itemStyle.paddingTop}px ${r.itemStyle.paddingLeft}px`})},{default:l((()=>[s(h,{class:"image",onClick:n[2]||(n[2]=t=>e.onLink(r.dataList[2].link)),src:r.dataList[2].imgUrl},null,8,["src"])])),_:1},8,["style"])):_("",!0),r.dataList.length>=4?(t(),a(S,{key:1,class:"right",style:o({padding:`${r.itemStyle.paddingTop}px ${r.itemStyle.paddingLeft}px`})},{default:l((()=>[s(h,{class:"image",onClick:n[3]||(n[3]=t=>e.onLink(r.dataList[3].link)),src:r.dataList[3].imgUrl},null,8,["src"])])),_:1},8,["style"])):_("",!0)])),_:1})])),_:1})])),_:1}))])),_:1},8,["style"])}],["__scopeId","data-v-3549cc80"]]);const Q=$({props:{itemIndex:String,itemStyle:Object,params:Object,data:Object},mixins:[E],methods:{}},[["render",function(e,i,n,r,d,p){const f=g,h=c;return t(),a(h,{class:"diy-hotZone",style:o({paddingBottom:2*n.itemStyle.paddingTop+"rpx",background:n.itemStyle.background})},{default:l((()=>[s(h,{class:"bg-image",style:o({padding:`${2*n.itemStyle.paddingTop}rpx ${2*n.itemStyle.paddingLeft}rpx 0`})},{default:l((()=>[s(f,{class:"image",src:n.data.imgUrl,mode:"widthFix"},null,8,["src"])])),_:1},8,["style"]),(t(!0),u(m,null,y(n.data.maps,((l,s)=>(t(),a(h,{class:"zone-item",key:s,style:o({width:`${l.width}rpx`,height:`${l.height}rpx`,left:`${l.left}rpx`,top:`${l.top}rpx`}),onClick:t=>e.onLink(l.link)},null,8,["style","onClick"])))),128))])),_:1},8,["style"])}],["__scopeId","data-v-ab741baa"]]);const X=$({name:"Videos",props:{itemIndex:String,itemStyle:Object,params:Object},methods:{}},[["render",function(e,i,n,r,d,p){const u=S,m=c;return t(),a(m,{class:"diy-video",style:o({padding:`${n.itemStyle.paddingTop}px 0`})},{default:l((()=>[s(u,{class:"video",style:o({height:`${n.itemStyle.height}px`}),src:n.params.videoUrl,poster:n.params.poster,autoplay:1==n.params.autoplay,controls:""},null,8,["style","src","poster","autoplay"])])),_:1},8,["style"])}],["__scopeId","data-v-5fd5e9e7"]]);const Y=$({name:"Article",props:{itemIndex:String,params:Object,dataList:Array},methods:{onTargetDetail(e){uni.navigateTo({url:"/pages/article/detail?articleId="+e})}}},[["render",function(e,o,p,f,h,S){const k=d,b=c,x=g;return t(),a(b,{class:"diy-article"},{default:l((()=>[(t(!0),u(m,null,y(p.dataList,((e,o)=>(t(),a(b,{class:i(["article-item",[`show-type__${e.show_type}`]]),key:o,onClick:t=>S.onTargetDetail(e.article_id)},{default:l((()=>[10==e.show_type?(t(),u(m,{key:0},[s(b,{class:"article-item__left flex-box"},{default:l((()=>[s(b,{class:"article-item__title"},{default:l((()=>[s(k,{class:"twoline-hide"},{default:l((()=>[n(r(e.title),1)])),_:2},1024)])),_:2},1024),s(b,{class:"article-item__footer m-top10"},{default:l((()=>[s(k,{class:"article-views f-24 col-8"},{default:l((()=>[n(r(e.show_views)+"次浏览",1)])),_:2},1024)])),_:2},1024)])),_:2},1024),s(b,{class:"article-item__image"},{default:l((()=>[s(x,{class:"image",mode:"widthFix",src:e.image_url},null,8,["src"])])),_:2},1024)],64)):_("",!0),20==e.show_type?(t(),u(m,{key:1},[s(b,{class:"article-item__title"},{default:l((()=>[s(k,{class:"twoline-hide"},{default:l((()=>[n(r(e.title),1)])),_:2},1024)])),_:2},1024),s(b,{class:"article-item__image m-top20"},{default:l((()=>[s(x,{class:"image",mode:"widthFix",src:e.image_url},null,8,["src"])])),_:2},1024),s(b,{class:"article-item__footer m-top10"},{default:l((()=>[s(k,{class:"article-views f-24 col-8"},{default:l((()=>[n(r(e.show_views)+"次浏览",1)])),_:2},1024)])),_:2},1024)],64)):_("",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1})}],["__scopeId","data-v-ce7b26a5"]]);const q=$({emits:["close","getMore"],props:{list:{type:Array,default:()=>[]},type:{type:String,default:"warning"},volumeIcon:{type:Boolean,default:!0},moreIcon:{type:Boolean,default:!1},closeIcon:{type:Boolean,default:!1},autoplay:{type:Boolean,default:!0},color:{type:String,default:""},bgColor:{type:String,default:""},show:{type:Boolean,default:!0},fontSize:{type:[Number,String],default:26},volumeSize:{type:[Number,String],default:34},speed:{type:[Number,String],default:160},playState:{type:String,default:"play"},padding:{type:[Number,String],default:"18rpx 24rpx"}},data:()=>({textWidth:0,boxWidth:0,animationDuration:"10s",animationPlayState:"paused",showText:""}),watch:{list:{immediate:!0,handler(e){this.showText=e.join("，"),this.$nextTick((()=>{this.initSize()}))}},playState(e){this.animationPlayState="play"==e?"running":"paused"},speed(e){this.initSize()}},computed:{computeColor(){return this.color?this.color:"none"==this.type?"#606266":this.type},textStyle(){let e={};return this.color?e.color=this.color:"none"==this.type&&(e.color="#606266"),e.fontSize=this.fontSize+"rpx",e},computeBgColor(){return this.bgColor?this.bgColor:"none"==this.type?"transparent":void 0}},mounted(){this.$nextTick((()=>{this.initSize()}))},methods:{initSize(){let e=[],t=new Promise(((e,t)=>{uni.createSelectorQuery().in(this).select("#u-notice-content").boundingClientRect().exec((t=>{this.textWidth=t[0].width,e()}))}));e.push(t),Promise.all(e).then((()=>{this.animationDuration=this.textWidth/uni.upx2px(this.speed)+"s",this.animationPlayState="paused",setTimeout((()=>{"play"==this.playState&&this.autoplay&&(this.animationPlayState="running")}),10)}))},click(e){this.$emit("click")},close(){this.$emit("close")},getMore(){this.$emit("getMore")}}},[["render",function(e,p,u,m,y,g){const f=j(k("u-icon"),T),h=c,S=d;return u.show?(t(),a(h,{key:0,class:i(["u-notice-bar",[u.type?`u-type-${u.type}-light-bg`:""]]),style:o({background:g.computeBgColor,padding:u.padding})},{default:l((()=>[s(h,{class:"u-direction-row"},{default:l((()=>[s(h,{class:"u-icon-wrap"},{default:l((()=>[u.volumeIcon?(t(),a(f,{key:0,class:"u-left-icon",name:"volume-fill",size:u.volumeSize,color:g.computeColor},null,8,["size","color"])):_("",!0)])),_:1}),s(h,{class:"u-notice-box",id:"u-notice-box"},{default:l((()=>[s(h,{class:"u-notice-content",id:"u-notice-content",style:o({animationDuration:y.animationDuration,animationPlayState:y.animationPlayState})},{default:l((()=>[s(S,{class:i(["u-notice-text",["u-type-"+u.type]]),onClick:g.click,style:o([g.textStyle])},{default:l((()=>[n(r(y.showText),1)])),_:1},8,["onClick","style","class"])])),_:1},8,["style"])])),_:1}),s(h,{class:"u-icon-wrap"},{default:l((()=>[u.moreIcon?(t(),a(f,{key:0,onClick:g.getMore,class:"u-right-icon",name:"arrow-right",size:26,color:g.computeColor},null,8,["onClick","color"])):_("",!0),u.closeIcon?(t(),a(f,{key:1,onClick:g.close,class:"u-right-icon",name:"close",size:24,color:g.computeColor},null,8,["onClick","color"])):_("",!0)])),_:1})])),_:1})])),_:1},8,["style","class"])):_("",!0)}],["__scopeId","data-v-834500a9"]]);const J=$({emits:["close","getMore","end"],props:{list:{type:Array,default:()=>[]},type:{type:String,default:"warning"},volumeIcon:{type:Boolean,default:!0},moreIcon:{type:Boolean,default:!1},closeIcon:{type:Boolean,default:!1},autoplay:{type:Boolean,default:!0},color:{type:String,default:""},bgColor:{type:String,default:""},direction:{type:String,default:"row"},show:{type:Boolean,default:!0},fontSize:{type:[Number,String],default:26},duration:{type:[Number,String],default:2e3},volumeSize:{type:[Number,String],default:34},speed:{type:Number,default:160},isCircular:{type:Boolean,default:!0},mode:{type:String,default:"horizontal"},playState:{type:String,default:"play"},disableTouch:{type:Boolean,default:!0},padding:{type:[Number,String],default:"18rpx 24rpx"}},computed:{computeColor(){return this.color?this.color:"none"==this.type?"#606266":this.type},textStyle(){let e={};return this.color?e.color=this.color:"none"==this.type&&(e.color="#606266"),e.fontSize=this.fontSize+"rpx",e},vertical(){return"horizontal"!=this.mode},computeBgColor(){return this.bgColor?this.bgColor:"none"==this.type?"transparent":void 0}},data:()=>({}),methods:{click(e){this.$emit("click",e)},close(){this.$emit("close")},getMore(){this.$emit("getMore")},change(e){e.detail.current==this.list.length-1&&this.$emit("end")}}},[["render",function(e,d,p,g,S,b){const x=j(k("u-icon"),T),v=c,C=f,w=h;return t(),a(v,{class:i(["u-notice-bar",[p.type?`u-type-${p.type}-light-bg`:""]]),style:o({background:b.computeBgColor,padding:p.padding})},{default:l((()=>[s(v,{class:"u-icon-wrap"},{default:l((()=>[p.volumeIcon?(t(),a(x,{key:0,class:"u-left-icon",name:"volume-fill",size:p.volumeSize,color:b.computeColor},null,8,["size","color"])):_("",!0)])),_:1}),s(w,{"disable-touch":p.disableTouch,onChange:b.change,autoplay:p.autoplay&&"play"==p.playState,vertical:b.vertical,circular:"",interval:p.duration,class:"u-swiper"},{default:l((()=>[(t(!0),u(m,null,y(p.list,((e,d)=>(t(),a(C,{key:d,class:"u-swiper-item"},{default:l((()=>[s(v,{class:i(["u-news-item u-line-1",["u-type-"+p.type]]),style:o([b.textStyle]),onClick:e=>b.click(d)},{default:l((()=>[n(r(e),1)])),_:2},1032,["style","onClick","class"])])),_:2},1024)))),128))])),_:1},8,["disable-touch","onChange","autoplay","vertical","interval"]),s(v,{class:"u-icon-wrap"},{default:l((()=>[p.moreIcon?(t(),a(x,{key:0,onClick:b.getMore,class:"u-right-icon",name:"arrow-right",size:26,color:b.computeColor},null,8,["onClick","color"])):_("",!0),p.closeIcon?(t(),a(x,{key:1,onClick:b.close,class:"u-right-icon",name:"close",size:24,color:b.computeColor},null,8,["onClick","color"])):_("",!0)])),_:1})])),_:1},8,["style","class"])}],["__scopeId","data-v-d897bbfb"]]);const K=$({name:"u-notice-bar",emits:["click","close","getMore","end"],props:{list:{type:Array,default:()=>[]},type:{type:String,default:"warning"},volumeIcon:{type:Boolean,default:!0},volumeSize:{type:[Number,String],default:34},moreIcon:{type:Boolean,default:!1},closeIcon:{type:Boolean,default:!1},autoplay:{type:Boolean,default:!0},color:{type:String,default:""},bgColor:{type:String,default:""},mode:{type:String,default:"horizontal"},show:{type:Boolean,default:!0},fontSize:{type:[Number,String],default:28},duration:{type:[Number,String],default:2e3},speed:{type:[Number,String],default:160},isCircular:{type:Boolean,default:!0},playState:{type:String,default:"play"},disableTouch:{type:Boolean,default:!0},borderRadius:{type:[Number,String],default:0},padding:{type:[Number,String],default:"18rpx 24rpx"},noListHidden:{type:Boolean,default:!0}},computed:{isShow(){return 0!=this.show&&(1!=this.noListHidden||0!=this.list.length)}},methods:{click(e){this.$emit("click",e)},close(){this.$emit("close")},getMore(){this.$emit("getMore")},end(){this.$emit("end")}}},[["render",function(e,s,i,n,r,d){const p=j(k("u-row-notice"),q),u=j(k("u-column-notice"),J),m=c;return d.isShow?(t(),a(m,{key:0,class:"u-notice-bar-wrap",style:o({borderRadius:i.borderRadius+"rpx"})},{default:l((()=>["horizontal"==i.mode&&i.isCircular?(t(),a(p,{key:0,type:i.type,color:i.color,bgColor:i.bgColor,list:i.list,volumeIcon:i.volumeIcon,moreIcon:i.moreIcon,volumeSize:i.volumeSize,closeIcon:i.closeIcon,mode:i.mode,fontSize:i.fontSize,speed:i.speed,playState:i.playState,padding:i.padding,onGetMore:d.getMore,onClose:d.close,onClick:d.click},null,8,["type","color","bgColor","list","volumeIcon","moreIcon","volumeSize","closeIcon","mode","fontSize","speed","playState","padding","onGetMore","onClose","onClick"])):_("",!0),"vertical"==i.mode||"horizontal"==i.mode&&!i.isCircular?(t(),a(u,{key:1,type:i.type,color:i.color,bgColor:i.bgColor,list:i.list,volumeIcon:i.volumeIcon,moreIcon:i.moreIcon,closeIcon:i.closeIcon,mode:i.mode,volumeSize:i.volumeSize,"disable-touch":i.disableTouch,fontSize:i.fontSize,duration:i.duration,playState:i.playState,padding:i.padding,onGetMore:d.getMore,onClose:d.close,onClick:d.click,onEnd:d.end},null,8,["type","color","bgColor","list","volumeIcon","moreIcon","closeIcon","mode","volumeSize","disable-touch","fontSize","duration","playState","padding","onGetMore","onClose","onClick","onEnd"])):_("",!0)])),_:1},8,["style"])):_("",!0)}],["__scopeId","data-v-7d93799d"]]);const ee=$({name:"Page",components:{Search:W,Images:H,Banner:Z,Window:V,HotZone:Q,Videos:X,Article:Y,Notice:$({props:{itemStyle:Object,params:Object},mixins:[E],methods:{}},[["render",function(e,i,n,r,d,p){const u=j(k("u-notice-bar"),K),m=c;return t(),a(m,{class:"diy-notice",style:o({paddingTop:`${n.itemStyle.paddingTop}px`,paddingBottom:`${n.itemStyle.paddingTop}px`}),onClick:i[0]||(i[0]=t=>e.onLink(n.params.link))},{default:l((()=>[s(u,{padding:"10rpx 24rpx","volume-icon":n.params.showIcon,autoplay:n.params.scrollable,"bg-color":n.itemStyle.background,color:n.itemStyle.textColor,list:[n.params.text]},null,8,["volume-icon","autoplay","bg-color","color","list"])])),_:1},8,["style"])}]]),NavBar:$({name:"NavBar",props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},mixins:[E],methods:{}},[["render",function(e,d,p,f,h,_){const S=g,k=c;return t(),a(k,{class:"diy-navBar",style:o({background:p.itemStyle.background,color:p.itemStyle.textColor})},{default:l((()=>[s(k,{class:i(["data-list",[`avg-sm-${p.itemStyle.rowsNum}`]])},{default:l((()=>[(t(!0),u(m,null,y(p.dataList,((i,o)=>(t(),a(k,{class:"item-nav",key:o},{default:l((()=>[s(k,{class:"nav-to",onClick:t=>e.onLink(i.link)},{default:l((()=>[s(k,{class:"item-image"},{default:l((()=>[s(S,{class:"image",mode:"widthFix",src:i.imgUrl},null,8,["src"])])),_:2},1024),s(k,{class:"item-text oneline-hide"},{default:l((()=>[n(r(i.text),1)])),_:2},1024)])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1},8,["class"])])),_:1},8,["style"])}],["__scopeId","data-v-110b6f51"]]),Goods:$({name:"Goods",props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},methods:{onTargetGoods(e){this.$navTo("pages/goods/detail",{goodsId:e})}}},[["render",function(e,p,f,h,S,k){const x=g,v=c,C=d,w=b;return t(),a(v,{class:"diy-goods",style:o({background:f.itemStyle.background})},{default:l((()=>[s(v,{class:i(["goods-list",[`display__${f.itemStyle.display}`,`column__${f.itemStyle.column}`]])},{default:l((()=>[s(w,{"scroll-x":"slide"===f.itemStyle.display},{default:l((()=>[(t(!0),u(m,null,y(f.dataList,((e,i)=>(t(),a(v,{class:"goods-item",key:i,onClick:t=>k.onTargetGoods(e.goods_id)},{default:l((()=>[1===f.itemStyle.column?(t(),a(v,{key:0,class:"dis-flex"},{default:l((()=>[s(v,{class:"goods-item-left"},{default:l((()=>[s(x,{class:"image",src:e.goods_image},null,8,["src"])])),_:2},1024),s(v,{class:"goods-item-right"},{default:l((()=>[f.itemStyle.show.includes("goodsName")?(t(),a(v,{key:0,class:"goods-name"},{default:l((()=>[s(C,{class:"twoline-hide"},{default:l((()=>[n(r(e.goods_name),1)])),_:2},1024)])),_:2},1024)):_("",!0),s(v,{class:"goods-item-desc"},{default:l((()=>[f.itemStyle.show.includes("sellingPoint")?(t(),a(v,{key:0,class:"desc-selling-point dis-flex"},{default:l((()=>[s(C,{class:"oneline-hide"},{default:l((()=>[n(r(e.selling_point),1)])),_:2},1024)])),_:2},1024)):_("",!0),f.itemStyle.show.includes("goodsSales")?(t(),a(v,{key:1,class:"desc-goods-sales dis-flex"},{default:l((()=>[s(C,null,{default:l((()=>[n("已售"+r(e.goods_sales)+"件",1)])),_:2},1024)])),_:2},1024)):_("",!0),s(v,{class:"desc-footer"},{default:l((()=>[f.itemStyle.show.includes("goodsPrice")?(t(),a(C,{key:0,class:"price-x"},{default:l((()=>[n("¥"+r(e.goods_price_min),1)])),_:2},1024)):_("",!0),f.itemStyle.show.includes("linePrice")&&e.line_price_min>0?(t(),a(C,{key:1,class:"price-y col-9"},{default:l((()=>[n("¥"+r(e.line_price_min),1)])),_:2},1024)):_("",!0)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)):(t(),u(m,{key:1},[s(v,{class:"goods-image"},{default:l((()=>[s(x,{class:"image",mode:"aspectFill",src:e.goods_image},null,8,["src"])])),_:2},1024),s(v,{class:"detail"},{default:l((()=>[f.itemStyle.show.includes("goodsName")?(t(),a(v,{key:0,class:"goods-name twoline-hide"},{default:l((()=>[s(C,{class:"twoline-hide"},{default:l((()=>[n(r(e.goods_name),1)])),_:2},1024)])),_:2},1024)):_("",!0),s(v,{class:"detail-price oneline-hide"},{default:l((()=>[f.itemStyle.show.includes("goodsPrice")?(t(),a(C,{key:0,class:"goods-price f-30 col-m"},{default:l((()=>[n("￥"+r(e.goods_price_min),1)])),_:2},1024)):_("",!0),f.itemStyle.show.includes("linePrice")&&e.line_price_min>0?(t(),a(C,{key:1,class:"line-price col-9 f-24"},{default:l((()=>[n("￥"+r(e.line_price_min),1)])),_:2},1024)):_("",!0)])),_:2},1024)])),_:2},1024)],64))])),_:2},1032,["onClick"])))),128))])),_:1},8,["scroll-x"])])),_:1},8,["class"])])),_:1},8,["style"])}],["__scopeId","data-v-44f64061"]]),Service:$({components:{CustomerBtn:B},props:{itemStyle:Object,params:Object},data:()=>({isShow:!1}),computed:{right(){return x(2*this.itemStyle.right)},bottom(){return x(2*this.itemStyle.bottom)}},async created(){"phone"===this.params.type&&(this.isShow=!0),"chat"===this.params.type&&(this.isShow=await v.isShowCustomerBtn())},methods:{onMakePhoneCall(e){uni.makePhoneCall({phoneNumber:this.params.tel})}}},[["render",function(e,i,n,r,d,p){const u=g,m=c,y=C("customer-btn");return d.isShow?(t(),a(m,{key:0,class:"diy-service",style:o({"--right":`${p.right}px`,"--bottom":`${p.bottom}px`})},{default:l((()=>["phone"===n.params.type?(t(),a(m,{key:0,class:"service-icon",onClick:p.onMakePhoneCall},{default:l((()=>[s(u,{class:"image",src:n.params.image},null,8,["src"])])),_:1},8,["onClick"])):"chat"===n.params.type?(t(),a(y,{key:1},{default:l((()=>[s(m,{class:"service-icon"},{default:l((()=>[s(u,{class:"image",src:n.params.image},null,8,["src"])])),_:1})])),_:1})):_("",!0)])),_:1},8,["style"])):_("",!0)}],["__scopeId","data-v-9c92f6dc"]]),Blank:$({props:{itemStyle:Object},methods:{}},[["render",function(e,l,s,i,n,r){const d=c;return t(),a(d,{class:"diy-blank",style:o({height:`${s.itemStyle.height}px`,background:s.itemStyle.background})},null,8,["style"])}]]),Guide:$({props:{itemStyle:Object},methods:{}},[["render",function(e,i,n,r,d,p){const u=c;return t(),a(u,{class:"diy-guide",style:o({padding:`${n.itemStyle.paddingTop}px 0`,background:n.itemStyle.background})},{default:l((()=>[s(u,{class:"line",style:o({borderTop:`${n.itemStyle.lineHeight}px ${n.itemStyle.lineStyle} ${n.itemStyle.lineColor}`})},null,8,["style"])])),_:1},8,["style"])}],["__scopeId","data-v-8a478461"]]),RichText:$({props:{itemStyle:Object,params:Object},methods:{}},[["render",function(e,i,n,r,d,p){const u=j(k("mp-html"),A),m=c;return t(),a(m,{class:"diy-richText",style:o({padding:`${n.itemStyle.paddingTop}px ${n.itemStyle.paddingLeft}px`,background:n.itemStyle.background})},{default:l((()=>[s(u,{content:n.params.content},null,8,["content"])])),_:1},8,["style"])}],["__scopeId","data-v-e024f8b3"]]),Special:$({props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},mixins:[E],methods:{handleNavDetail(e){this.$navTo("pages/article/detail",{articleId:e})},handleNavMore(){this.$navTo("pages/article/index")}}},[["render",function(e,p,_,S,k,b){const x=g,v=c,C=d,L=f,I=h;return t(),a(v,{class:"diy-special",style:o({padding:`${_.itemStyle.paddingTop}px 0`,background:_.itemStyle.background})},{default:l((()=>[s(v,{class:"special-left",onClick:p[0]||(p[0]=e=>b.handleNavMore())},{default:l((()=>[s(x,{class:"image",mode:"widthFix",src:_.params.image},null,8,["src"])])),_:1}),w("div",{class:i(["special-content",[`display_${_.params.display}`]])},[s(I,{autoplay:!0,interval:1500,duration:800,circular:!0,vertical:!0,"display-multiple-items":_.itemStyle.display},{default:l((()=>[(t(!0),u(m,null,y(_.dataList,((e,i)=>(t(),a(L,{key:i},{default:l((()=>[s(v,{class:"content-item oneline-hide",onClick:t=>b.handleNavDetail(e.article_id)},{default:l((()=>[s(C,{style:o({color:_.itemStyle.textColor})},{default:l((()=>[n(r(e.title),1)])),_:2},1032,["style"])])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1},8,["display-multiple-items"])],2),w("div",{class:"special-more",onClick:p[1]||(p[1]=e=>b.handleNavMore())},[s(C,{class:"iconfont icon-arrow-right"})])])),_:1},8,["style"])}],["__scopeId","data-v-ac41dfe4"]]),DiyOfficialAccount:$({props:{itemIndex:String},mixins:[E],methods:{}},[["render",function(e,t,a,l,s,i){return null}]]),Shop:$({props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},mixins:[E],methods:{handleNavDetail(e){this.$navTo("pages/shop/detail",{shopId:e})}}},[["render",function(e,i,o,n,d,p){const f=g,h=c;return t(),a(h,{class:"diy-shop"},{default:l((()=>[(t(!0),u(m,null,y(o.dataList,((e,i)=>(t(),a(h,{class:"shop-item dis-flex flex-y-center",key:i,onClick:t=>p.handleNavDetail(e.shop_id)},{default:l((()=>[s(h,{class:"shop-item__logo"},{default:l((()=>[s(f,{class:"image",src:e.logo_url},null,8,["src"])])),_:2},1024),s(h,{class:"shop-item__content"},{default:l((()=>[s(h,{class:"shop-item__title"},{default:l((()=>[w("span",null,r(e.shop_name),1)])),_:2},1024),s(h,{class:"shop-item__address oneline-hide"},{default:l((()=>[w("span",null,"门店地址："+r(e.region.province)+r(e.region.city)+r(e.region.region)+r(e.address),1)])),_:2},1024),s(h,{class:"shop-item__phone"},{default:l((()=>[w("span",null,"联系电话："+r(e.phone),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})}],["__scopeId","data-v-69b1de76"]]),Coupon:$({props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},data:()=>({couponList:[],disable:!1}),watch:{dataList:{handler(e){this.couponList=L(e)},immediate:!0,deep:!0}},mixins:[E],methods:{handleReceive(e,t){const a=this;!a.disable&&t.state.value&&(a.disable=!0,z(t.coupon_id,{},{load:!1}).then((l=>{a.$success(l.message),a.setReceived(e,t)})).finally((()=>a.disable=!1)))},setReceived(e,t){this.couponList[e]={...t,state:{value:0,text:"已领取"}}}}},[["render",function(e,p,g,f,h,S){const k=d,x=c,v=b;return h.couponList.length?(t(),a(x,{key:0,class:"diy-coupon",style:o({padding:`${g.itemStyle.paddingTop}px 0`,background:g.itemStyle.background})},{default:l((()=>[s(v,{"scroll-x":!0},{default:l((()=>[s(x,{class:"coupon-wrapper"},{default:l((()=>[(t(!0),u(m,null,y(h.couponList,((e,d)=>(t(),a(x,{class:i(["coupon-item",{disable:!e.state.value}]),key:d,style:o({marginRight:`${g.itemStyle.marginRight}px`})},{default:l((()=>[s(k,{class:"before",style:o({background:g.itemStyle.background})},null,8,["style"]),s(x,{class:"left-content",style:o({background:g.itemStyle.couponBgColor})},{default:l((()=>[s(x,{class:"content-top"},{default:l((()=>[10==e.coupon_type?(t(),u(m,{key:0},[s(k,{class:"unit"},{default:l((()=>[n("￥")])),_:1}),s(k,{class:"price"},{default:l((()=>[n(r(e.reduce_price),1)])),_:2},1024)],64)):_("",!0),20==e.coupon_type?(t(),a(k,{key:1,class:"price"},{default:l((()=>[n(r(e.discount)+"折",1)])),_:2},1024)):_("",!0)])),_:2},1024),s(x,{class:"content-bottom"},{default:l((()=>[s(k,{class:"f-22"},{default:l((()=>[n("满"+r(e.min_price)+"元可用",1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["style"]),s(x,{class:"right-receive",style:o({background:g.itemStyle.receiveBgColor}),onClick:t=>S.handleReceive(d,e)},{default:l((()=>[e.state.value?(t(),u(m,{key:0},[s(k,null,{default:l((()=>[n("立即")])),_:1}),s(k,null,{default:l((()=>[n("领取")])),_:1})],64)):(t(),a(k,{key:1},{default:l((()=>[n(r(e.state.text),1)])),_:2},1024))])),_:2},1032,["style","onClick"])])),_:2},1032,["class","style"])))),128))])),_:1})])),_:1})])),_:1},8,["style"])):_("",!0)}],["__scopeId","data-v-631ff280"]]),Bargain:$({components:{AvatarImage:O},props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},data:()=>({inArray:I}),mixins:[E],methods:{handleNavDetail(e){this.$navTo("pages/bargain/goods/index",{activeId:e.active_id,goodsId:e.goods_id})}}},[["render",function(e,i,p,f,h,S){const k=g,b=c,x=d,v=C("avatar-image");return t(),a(b,{class:"diy-bargain",style:o({background:p.itemStyle.background})},{default:l((()=>[(t(!0),u(m,null,y(p.dataList,((e,i)=>(t(),a(b,{class:"goods-item",key:i,onClick:t=>S.handleNavDetail(e)},{default:l((()=>[s(b,{class:"goods-image"},{default:l((()=>[s(k,{class:"image",src:e.goods_image},null,8,["src"])])),_:2},1024),s(b,{class:"goods-info"},{default:l((()=>[h.inArray("goodsName",p.itemStyle.show)?(t(),a(b,{key:0,class:"goods-name"},{default:l((()=>[s(x,{class:"twoline-hide"},{default:l((()=>[n(r(e.goods_name),1)])),_:2},1024)])),_:2},1024)):_("",!0),h.inArray("peoples",p.itemStyle.show)&&e.helpsCount?(t(),a(b,{key:1,class:"peoples"},{default:l((()=>[s(b,{class:"user-list"},{default:l((()=>[(t(!0),u(m,null,y(e.helpList,((e,i)=>(t(),a(b,{key:i,class:"user-item-avatar"},{default:l((()=>[s(v,{url:e.user.avatar_url,width:32},null,8,["url"])])),_:2},1024)))),128))])),_:2},1024),s(b,{class:"people__text"},{default:l((()=>[s(x,null,{default:l((()=>[n(r(e.helpsCount)+"人正在砍价",1)])),_:2},1024)])),_:2},1024)])),_:2},1024)):_("",!0),h.inArray("originalPrice",p.itemStyle.show)?(t(),a(b,{key:2,class:"goods-price"},{default:l((()=>[s(x,null,{default:l((()=>[n("￥"+r(e.original_price),1)])),_:2},1024)])),_:2},1024)):_("",!0),h.inArray("floorPrice",p.itemStyle.show)?(t(),a(b,{key:3,class:"floor-price"},{default:l((()=>[s(x,{class:"small"},{default:l((()=>[n("最低￥")])),_:1}),s(x,{class:"big"},{default:l((()=>[n(r(e.floor_price),1)])),_:2},1024)])),_:2},1024)):_("",!0),s(b,{class:"opt-touch"},{default:l((()=>[s(b,{class:"touch-btn"},{default:l((()=>[s(x,null,{default:l((()=>[n("立即参加")])),_:1})])),_:1})])),_:1})])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1},8,["style"])}],["__scopeId","data-v-f9341955"]]),Sharp:$({components:{CountDown:N},props:{itemIndex:String,itemStyle:Object,params:Object,data:Object},data:()=>({ActiveStatusEnum:P,GoodsStatusEnum:M,inArray:I}),mixins:[E],methods:{handleNavMore(){this.$navTo("pages/sharp/index")},handleNavDetail(e){const{data:t}=this;this.$navTo("pages/sharp/goods/index",{activeTimeId:t.active.active_time_id,sharpGoodsId:e})}}},[["render",function(e,p,f,h,S,k){const b=d,x=c,v=C("count-down"),w=g;return f.data.goodsList.data&&f.data.goodsList.data.length?(t(),a(x,{key:0,class:"diy-sharp",style:o({background:f.itemStyle.background})},{default:l((()=>[s(x,{class:"sharp-top",onClick:p[0]||(p[0]=e=>k.handleNavMore())},{default:l((()=>[s(x,{class:"sharp-top--left"},{default:l((()=>[s(x,{class:"sharp-modular"},{default:l((()=>[s(b,{class:"iconfont icon-miaosha-b"}),s(b,{class:"modular-name"},{default:l((()=>[n("限时秒杀")])),_:1})])),_:1}),s(x,{class:"sharp-active-status"},{default:l((()=>[s(b,null,{default:l((()=>[n(r(f.data.active.sharp_modular_text),1)])),_:1})])),_:1}),f.data.active.status==S.GoodsStatusEnum.STATE_BEGIN.value?(t(),a(x,{key:0,class:"active-count-down"},{default:l((()=>[s(v,{date:f.data.active.count_down_time,separator:"colon",theme:"custom"},null,8,["date"])])),_:1})):_("",!0)])),_:1}),s(x,{class:"sharp-top--right"},{default:l((()=>[s(x,{class:"sharp-more"},{default:l((()=>[s(b,{class:"sharp-more-text"},{default:l((()=>[n("更多")])),_:1}),s(b,{class:"sharp-more-arrow iconfont icon-arrow-right"})])),_:1})])),_:1})])),_:1}),s(x,{class:i(["goods-list display__list clearfix",[`column__${f.itemStyle.column}`]])},{default:l((()=>[(t(!0),u(m,null,y(f.data.goodsList.data,((e,i)=>(t(),a(x,{class:"goods-item",key:i,onClick:t=>k.handleNavDetail(e.sharp_goods_id)},{default:l((()=>[1==f.itemStyle.column?(t(),u(m,{key:0},[],64)):(t(),u(m,{key:1},[s(x,{class:"goods-image"},{default:l((()=>[s(w,{class:"image",mode:"aspectFill",src:e.goods_image},null,8,["src"])])),_:2},1024),s(x,{class:"detail"},{default:l((()=>[S.inArray("goodsName",f.itemStyle.show)?(t(),a(x,{key:0,class:"goods-name"},{default:l((()=>[s(b,{class:"twoline-hide"},{default:l((()=>[n(r(e.goods_name),1)])),_:2},1024)])),_:2},1024)):_("",!0),s(x,{class:"detail-price"},{default:l((()=>[S.inArray("seckillPrice",f.itemStyle.show)?(t(),a(b,{key:0,class:"goods-price c-red"},{default:l((()=>[s(b,{class:"small-unit"},{default:l((()=>[n("¥")])),_:1}),s(b,null,{default:l((()=>[n(r(e.seckill_price_min),1)])),_:2},1024)])),_:2},1024)):_("",!0),S.inArray("originalPrice",f.itemStyle.show)&&e.original_price>0?(t(),a(b,{key:1,class:"line-price"},{default:l((()=>[n("￥"+r(e.original_price),1)])),_:2},1024)):_("",!0)])),_:2},1024)])),_:2},1024)],64))])),_:2},1032,["onClick"])))),128))])),_:1},8,["class"])])),_:1},8,["style"])):_("",!0)}],["__scopeId","data-v-3badc385"]]),Groupon:$({components:{},mixins:[E],props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},data:()=>({inArray:I,ActiveTypeEnum:R}),computed:{tagBackgroundColor(){return D(this.appTheme.mainBg,.1)},tagBorderColor(){return D(this.appTheme.mainBg,.6)}},methods:{onTargetGoods(e){this.$navTo("pages/groupon/goods/index",{grouponGoodsId:e.groupon_goods_id})}}},[["render",function(e,p,f,h,S,b){const x=d,v=c,C=g,w=j(k("u-tag"),G);return t(),a(v,{class:"diy-groupon",style:o({background:f.itemStyle.background,padding:`${2*f.itemStyle.paddingY}rpx ${2*f.itemStyle.paddingX}rpx`})},{default:l((()=>[(t(!0),u(m,null,y(f.dataList,((d,c)=>(t(),a(v,{class:"goods-item--container",key:c,style:o({marginBottom:2*f.itemStyle.itemMargin+"rpx"})},{default:l((()=>[s(v,{class:i(["goods-item",[`display-${f.itemStyle.display}`,`border-${f.itemStyle.itemBorderRadius}`]]),onClick:e=>b.onTargetGoods(d)},{default:l((()=>[s(v,{class:"goods-item-left"},{default:l((()=>[d.active_type!=S.ActiveTypeEnum.NORMAL.value?(t(),a(v,{key:0,class:"label"},{default:l((()=>[s(x,null,{default:l((()=>[n(r(S.ActiveTypeEnum[d.active_type].name2),1)])),_:2},1024)])),_:2},1024)):_("",!0),s(C,{class:"image",src:d.goods_image},null,8,["src"])])),_:2},1024),s(v,{class:"goods-item-right"},{default:l((()=>[S.inArray("goodsName",f.itemStyle.show)?(t(),a(v,{key:0,class:"goods-name"},{default:l((()=>[s(x,{class:"twoline-hide"},{default:l((()=>[n(r(d.goods_name),1)])),_:2},1024)])),_:2},1024)):_("",!0),s(v,{class:"goods-item-desc"},{default:l((()=>[s(v,{class:"desc_situation"},{default:l((()=>[s(v,{class:"state-tag"},{default:l((()=>[S.inArray("peoples",f.itemStyle.show)?(t(),a(w,{key:0,color:e.appTheme.mainBg,"border-color":e.appTheme.mainBg,text:`${d.show_people}人团`,type:"error",size:"mini",mode:"plain"},null,8,["color","border-color","text"])):_("",!0)])),_:2},1024),s(v,{class:"state-tag"},{default:l((()=>[S.inArray("activeSales",f.itemStyle.show)&&d.active_sales?(t(),a(w,{key:0,color:e.appTheme.mainBg,"border-color":b.tagBorderColor,"bg-color":b.tagBackgroundColor,text:`已团${d.active_sales}件`,type:"error",size:"mini"},null,8,["color","border-color","bg-color","text"])):_("",!0)])),_:2},1024)])),_:2},1024),s(v,{class:"desc-footer"},{default:l((()=>[s(v,{class:"item-prices oneline-hide"},{default:l((()=>[S.inArray("grouponPrice",f.itemStyle.show)?(t(),a(x,{key:0,class:"price-x"},{default:l((()=>[n("¥"+r(d.groupon_price),1)])),_:2},1024)):_("",!0),S.inArray("grouponPrice",f.itemStyle.show)?(t(),a(x,{key:1,class:"price-y cl-9"},{default:l((()=>[n("¥"+r(d.original_price),1)])),_:2},1024)):_("",!0)])),_:2},1024),S.inArray("button",f.itemStyle.show)?(t(),a(v,{key:0,class:"settlement"},{default:l((()=>[n("去拼团")])),_:1})):_("",!0)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick","class"])])),_:2},1032,["style"])))),128))])),_:1},8,["style"])}],["__scopeId","data-v-94dcee0e"]]),ICPLicense:$({props:{itemStyle:Object,params:Object},mixins:[E],methods:{}},[["render",function(e,i,p,u,m,y){const g=d,f=c;return t(),a(f,{class:"diy-ICPLicense",style:o({padding:`${2*p.itemStyle.paddingTop}rpx ${2*p.itemStyle.paddingLeft}rpx`,background:p.itemStyle.background})},{default:l((()=>[s(f,{class:"line",style:o({textAlign:p.itemStyle.textAlign})},{default:l((()=>[s(g,{style:o({fontSize:2*p.itemStyle.fontSize+"rpx",color:p.itemStyle.textColor}),target:"_blank",onClick:i[0]||(i[0]=t=>e.onLink({type:"URL",param:{url:p.params.link}}))},{default:l((()=>[n(r(p.params.text),1)])),_:1},8,["style"])])),_:1},8,["style"])])),_:1},8,["style"])}]])},props:{items:{type:Array,default:()=>[]}}},[["render",function(e,s,i,o,n,r){const d=C("Search"),p=C("Images"),g=C("Banner"),f=C("Window"),h=C("Videos"),S=C("Article"),k=C("Notice"),b=C("NavBar"),x=C("Goods"),v=C("Service"),w=C("Blank"),L=C("Guide"),I=C("RichText"),$=C("Special"),T=C("DiyOfficialAccount"),j=C("Shop"),B=C("Coupon"),A=C("Bargain"),z=C("Sharp"),O=C("Groupon"),N=C("HotZone"),P=C("ICPLicense"),M=c;return t(),a(M,{class:"page-items"},{default:l((()=>[(t(!0),u(m,null,y(i.items,((e,l)=>(t(),u(m,{key:l},["search"===e.type?(t(),a(d,{key:0,itemStyle:e.style,params:e.params},null,8,["itemStyle","params"])):_("",!0),"image"===e.type?(t(),a(p,{key:1,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"banner"===e.type?(t(),a(g,{key:2,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"window"===e.type?(t(),a(f,{key:3,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"video"===e.type?(t(),a(h,{key:4,itemStyle:e.style,params:e.params},null,8,["itemStyle","params"])):_("",!0),"article"===e.type?(t(),a(S,{key:5,params:e.params,dataList:e.data},null,8,["params","dataList"])):_("",!0),"notice"===e.type?(t(),a(k,{key:6,itemStyle:e.style,params:e.params},null,8,["itemStyle","params"])):_("",!0),"navBar"===e.type?(t(),a(b,{key:7,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"goods"===e.type?(t(),a(x,{key:8,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"service"===e.type?(t(),a(v,{key:9,itemStyle:e.style,params:e.params},null,8,["itemStyle","params"])):_("",!0),"blank"===e.type?(t(),a(w,{key:10,itemStyle:e.style},null,8,["itemStyle"])):_("",!0),"guide"===e.type?(t(),a(L,{key:11,itemStyle:e.style},null,8,["itemStyle"])):_("",!0),"richText"===e.type?(t(),a(I,{key:12,itemStyle:e.style,params:e.params},null,8,["itemStyle","params"])):_("",!0),"special"===e.type?(t(),a($,{key:13,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"officialAccount"===e.type?(t(),a(T,{key:14})):_("",!0),"shop"===e.type?(t(),a(j,{key:15,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"coupon"===e.type?(t(),a(B,{key:16,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"bargain"===e.type?(t(),a(A,{key:17,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"sharp"===e.type?(t(),a(z,{key:18,itemStyle:e.style,params:e.params,data:e.data},null,8,["itemStyle","params","data"])):_("",!0),"groupon"===e.type?(t(),a(O,{key:19,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"hotZone"===e.type?(t(),a(N,{key:20,itemStyle:e.style,params:e.params,data:e.data},null,8,["itemStyle","params","data"])):_("",!0),"ICPLicense"===e.type?(t(),a(P,{key:21,itemStyle:e.style,params:e.params},null,8,["itemStyle","params"])):_("",!0)],64)))),128))])),_:1})}],["__scopeId","data-v-5859edb0"]]);export{ee as P,F as d};
