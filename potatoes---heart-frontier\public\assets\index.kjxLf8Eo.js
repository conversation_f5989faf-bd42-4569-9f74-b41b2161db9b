import{y as e,z as s,o as a,c as t,w as o,a as n,A as i,b as r,W as d,i as c}from"./index-BI5vpG2u.js";import{_ as g}from"./_plugin-vue_export-helper.BCo6x5W8.js";const l=g({props:{showCard:{Type:Boolean,default:!1},cardTitle:{Type:String,default:""},cardImage:{Type:String,default:""},cardPath:{Type:String,default:""}},data:()=>({isShow:!1,setting:{}}),async created(){this.isShow=await e.isShowCustomerBtn(),this.setting=await e.item(s.CUSTOMER.value,!0)},methods:{handleContact(){const{setting:e}=this;if("wxqykf"==e.provider){if(!e.config.wxqykf.url||!e.config.wxqykf.corpId)return void this.$toast("客服链接和企业ID不能为空");window.open(e.config.wxqykf.url)}}}},[["render",function(e,s,g,l,p,m){const f=d,h=c;return p.isShow?(a(),t(h,{key:0},{default:o((()=>[n(f,{class:"btn-normal","open-type":"mpwxkf"==p.setting.provider?"contact":"","show-message-card":g.showCard,"send-message-path":g.cardPath,"send-message-title":g.cardTitle,"send-message-img":g.cardImage,onClick:s[0]||(s[0]=e=>m.handleContact())},{default:o((()=>[i(e.$slots,"default")])),_:3},8,["open-type","show-message-card","send-message-path","send-message-title","send-message-img"])])),_:3})):r("",!0)}]]);export{l as C};
