{"name": "guzzlehttp/command", "description": "Provides the foundation for building command-based web service clients", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "require": {"php": "^7.2.5 || ^8.0", "guzzlehttp/guzzle": "^7.9.2", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.19 || ^9.5.8"}, "autoload": {"psr-4": {"GuzzleHttp\\Command\\": "src/"}}, "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "config": {"preferred-install": "dist", "sort-packages": true, "allow-plugins": {"bamarni/composer-bin-plugin": true}}}