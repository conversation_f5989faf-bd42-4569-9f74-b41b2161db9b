(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["dealer"],{"0b18":function(e,t,a){"use strict";a("d2ae")},"1da1":function(e,t,a){"use strict";a.d(t,"a",(function(){return s}));a("d3b7");function r(e,t,a,r,s,n,o){try{var i=e[n](o),l=i.value}catch(d){return void a(d)}i.done?t(l):Promise.resolve(l).then(r,s)}function s(e){return function(){var t=this,a=arguments;return new Promise((function(s,n){var o=e.apply(t,a);function i(e){r(o,s,n,i,l,"next",e)}function l(e){r(o,s,n,i,l,"throw",e)}i(void 0)}))}}},"24c9":function(e,t,a){"use strict";a("2baa")},"2baa":function(e,t,a){},"2d32":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return i}));var r=a("b775"),s={all:"/dealer.setting/all",detail:"/dealer.setting/detail",update:"/dealer.setting/update"};function n(){return Object(r["b"])({url:s.all,method:"get"})}function o(e){return Object(r["b"])({url:s.detail,method:"get",params:{key:e}})}function i(e){return Object(r["b"])({url:s.update,method:"post",data:e})}},"3c19":function(e,t,a){"use strict";a("cba3")},"3c76":function(e,t,a){"use strict";a.d(t,"a",(function(){return s}));var r=a("5c06"),s=new r["a"]([{key:"WECHAT",name:"微信支付",value:"wechat"},{key:"ALIPAY",name:"支付宝",value:"alipay"},{key:"BALANCE",name:"余额支付",value:"balance"}])},"4a95":function(e,t,a){"use strict";a.d(t,"b",(function(){return s})),a.d(t,"c",(function(){return n})),a.d(t,"d",(function(){return o})),a.d(t,"e",(function(){return i})),a.d(t,"g",(function(){return l})),a.d(t,"h",(function(){return d})),a.d(t,"f",(function(){return c})),a.d(t,"a",(function(){return u}));var r=a("5c06"),s=new r["a"]([{key:"NOT_DELIVERED",name:"未发货",value:10},{key:"DELIVERED",name:"已发货",value:20},{key:"PART_DELIVERED",name:"部分发货",value:30}]),n=new r["a"]([{key:"EXPRESS",name:"快递配送",value:10},{key:"EXTRACT",name:"上门自提",value:20},{key:"NOTHING",name:"无需配送",value:30}]),o=new r["a"]([{key:"MASTER",name:"普通订单",value:10},{key:"BARGAIN",name:"砍价订单",value:20},{key:"SHARP",name:"秒杀订单",value:30},{key:"GROUPON",name:"拼团订单",value:40}]),i=new r["a"]([{key:"NORMAL",name:"进行中",value:10},{key:"CANCELLED",name:"已取消",value:20},{key:"APPLY_CANCEL",name:"待取消",value:21},{key:"COMPLETED",name:"已完成",value:30}]),l=new r["a"]([{key:"PENDING",name:"待支付",value:10},{key:"SUCCESS",name:"已支付",value:20}]),d=new r["a"]([{key:"NOT_RECEIVED",name:"未收货",value:10},{key:"RECEIVED",name:"已收货",value:20}]),c=new r["a"]([{key:"PHYSICAL",name:"实物订单",value:10},{key:"VIRTUAL",name:"虚拟订单",value:20}]),u=new r["a"]([{key:"ALL",name:"全部",value:"all"},{key:"DELIVERY",name:"待发货",value:"delivery"},{key:"RECEIPT",name:"待收货",value:"receipt"},{key:"PAY",name:"待付款",value:"pay"},{key:"COMPLETE",name:"已完成",value:"complete"},{key:"APPLY_CANCEL",name:"待取消",value:"apply_cancel"},{key:"CANCEL",name:"已取消",value:"cancel"}])},"5a70":function(e,t,a){"use strict";a("ed2f")},"6747f":function(e,t,a){"use strict";a("9f51")},"696d":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v("分销海报设置")]),t("a-spin",{attrs:{spinning:e.isLoading}},[t("a-alert",{staticClass:"mb-15",attrs:{showIcon:!0,message:"分销商海报设置说明",banner:""}},[t("template",{slot:"description"},[t("p",[e._v("1. 可拖动头像、二维码、昵称调整位置")]),t("p",[e._v("2. 修改后如需生效请前往 设置-清理缓存，清除临时图片")])])],2),e.isLoading?e._e():t("div",{staticClass:"container clearfix",staticStyle:{"margin-top":"25px"}},[t("div",{ref:"preview",staticClass:"poster-preview"},[t("div",{staticClass:"backdrop"},[t("img",{ref:"posterImage",attrs:{src:e.record.backdrop.src,alt:""},on:{load:e.loadImage}})]),t("div",{ref:"qrcode",staticClass:"drag pre-qrcode",class:e.record.qrcode.style,style:{width:e.record.qrcode.width+"px",height:e.record.qrcode.width+"px",top:e.record.qrcode.top+"px",left:e.record.qrcode.left+"px"}},[t("img",{attrs:{src:e.record.qrcode.src,alt:""}})]),t("div",{ref:"avatar",staticClass:"drag pre-avatar",class:e.record.avatar.style,style:{width:e.record.avatar.width+"px",height:e.record.avatar.width+"px",top:e.record.avatar.top+"px",left:e.record.avatar.left+"px"}},[t("img",{attrs:{src:e.record.avatar.src,alt:""}})]),t("div",{ref:"nickName",staticClass:"drag pre-nickName",style:{fontSize:e.record.nickName.fontSize+"px",color:e.record.nickName.color,top:e.record.nickName.top+"px",left:e.record.nickName.left+"px"}},[t("span",[e._v("这里是昵称")])])]),t("a-form-model",{ref:"myForm",staticClass:"my-form",attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-form-model-item",{attrs:{label:"背景图片",required:""}},[t("SelectImage2",{attrs:{tips:"图片尺寸：宽750 高度1200"},model:{value:e.record.backdrop.src,callback:function(t){e.$set(e.record.backdrop,"src",t)},expression:"record.backdrop.src"}}),t("div",{staticClass:"form-item-help mt-5"},[t("p",{staticClass:"extra"},[e._v("图片尺寸：宽750像素 高度1200")]),t("p",{staticClass:"extra"},[e._v("请按照要求尺寸上传，否则生成时会错误")])])],1),t("a-form-model-item",{attrs:{label:"头像宽度"}},[t("a-input-number",{attrs:{min:30,max:150,autocomplete:"off"},model:{value:e.record.avatar.width,callback:function(t){e.$set(e.record.avatar,"width",t)},expression:"record.avatar.width"}})],1),t("a-form-model-item",{attrs:{label:"头像样式"}},[t("a-radio-group",{model:{value:e.record.avatar.style,callback:function(t){e.$set(e.record.avatar,"style",t)},expression:"record.avatar.style"}},[t("a-radio",{attrs:{value:"circle"}},[e._v("圆形")]),t("a-radio",{attrs:{value:"square"}},[e._v("方形")])],1)],1),t("a-form-model-item",{staticClass:"mt-30",attrs:{label:"昵称字体大小"}},[t("a-input-number",{attrs:{min:12,max:38,autocomplete:"off"},model:{value:e.record.nickName.fontSize,callback:function(t){e.$set(e.record.nickName,"fontSize",t)},expression:"record.nickName.fontSize"}})],1),t("a-form-model-item",{attrs:{label:"昵称字体颜色"}},[t("colorPicker",{staticClass:"m-colorPicker",attrs:{defaultColor:"#fff"},model:{value:e.record.nickName.color,callback:function(t){e.$set(e.record.nickName,"color",t)},expression:"record.nickName.color"}})],1),t("a-form-model-item",{staticClass:"mt-30",attrs:{label:"二维码宽度"}},[t("a-input-number",{attrs:{min:60,max:300,autocomplete:"off"},model:{value:e.record.qrcode.width,callback:function(t){e.$set(e.record.qrcode,"width",t)},expression:"record.qrcode.width"}})],1),t("a-form-model-item",{attrs:{wrapperCol:{offset:3}}},[t("a-button",{attrs:{type:"primary",loading:e.confirmLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)],1)],1)},s=[],n=(a("d3b7"),a("2b0e")),o=(a("88bc"),a("a9f5")),i=a.n(o),l=a("2d32"),d=a("2af9");n["a"].use(i.a);var c={components:{SelectImage2:d["i"]},data:function(){return{labelCol:{span:3},wrapperCol:{span:12},isLoading:!1,confirmLoading:!1,record:{backdrop:{src:""},nickName:{fontSize:"14",color:"#000000",left:"199",top:"524"},avatar:{width:"60",style:"circle",left:"127",top:"502",src:""},qrcode:{width:"100",style:"circle",left:"17",top:"480",src:""}}}},created:function(){this.getDetail()},methods:{initDragEvent:function(){var e=this;this.$nextTick((function(){e.dragEvent("qrcode"),e.dragEvent("avatar"),e.dragEvent("nickName")}))},loadImage:function(){this.initDragEvent()},dragEvent:function(e){var t=this,a=t.$refs.preview,r=t.$refs[e],s=0,n=0,o=a.offsetWidth-r.offsetWidth,i=a.offsetHeight-r.offsetHeight;r.onmousedown=function(a){var l=a.clientX-r.offsetLeft,d=a.clientY-r.offsetTop;return document.onmousemove=function(a){var r=a.clientX-l,c=a.clientY-d;r<=s&&(r=s),r>=o&&(r=o),c<=n&&(c=n),c>=i&&(c=i),t.record[e].left=r,t.record[e].top=c},document.onmouseup=function(){document.onmousemove=null,document.onmouseup=null},!1}},getDetail:function(){var e=this;this.isLoading=!0,l["b"]("poster").then((function(t){e.record=t.data.detail})).finally((function(){return e.isLoading=!1}))},handleSubmit:function(e){var t=this;e.preventDefault(),this.confirmLoading=!0,l["c"]({form:{poster:this.record}}).then((function(e){t.$message.success(e.message,1.5)})).finally((function(){return t.confirmLoading=!1}))}}},u=c,m=(a("3c19"),a("2877")),p=Object(m["a"])(u,r,s,!1,null,"0aedf8fa",null);t["default"]=p.exports},"748c":function(e,t,a){"use strict";a.r(t);a("ac1f"),a("841c");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{staticClass:"flex flex-x-end"},[t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px"},attrs:{placeholder:"请输入姓名/手机号/昵称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"user_id",columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"user",fn:function(e){return[t("UserItem",{attrs:{user:e}})]}},{key:"contacts",fn:function(a){return[t("p",[e._v(e._s(a.real_name))]),t("p",[e._v(e._s(a.mobile))])]}},{key:"money",fn:function(a){return[t("p",[e._v(e._s(a.full_money)+"元")]),t("p",[e._v(e._s(a.money)+"元")])]}},{key:"referee",fn:function(a){return[a.referee_id&&a.referee?t("span",[t("UserItem",{attrs:{user:a.referee}})],1):t("span",[e._v("--")])]}},{key:"fans",fn:function(a){return[t("p",[t("a",{on:{click:function(t){return e.handleFans(a,1)}}},[e._v("1级："+e._s(a.first_num))])]),t("p",[t("a",{on:{click:function(t){return e.handleFans(a,2)}}},[e._v("2级："+e._s(a.second_num))])]),t("p",[t("a",{on:{click:function(t){return e.handleFans(a,3)}}},[e._v("3级："+e._s(a.third_num))])])]}},{key:"action",fn:function(a,r){return t("span",{staticClass:"actions"},[t("a",{directives:[{name:"action",rawName:"v-action:edit",arg:"edit"}],on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),t("a-dropdown",[t("a-menu",{attrs:{slot:"overlay"},on:{click:function(t){return e.handleMenuClick(t,r)}},slot:"overlay"},[e.$auth("/apps/dealer/order")?t("a-menu-item",{key:"1"},[e._v("分销订单")]):e._e(),e.$auth("/apps/dealer/withdraw")?t("a-menu-item",{key:"2"},[e._v("提现明细")]):e._e(),t("a-menu-item",{directives:[{name:"action",rawName:"v-action:poster",arg:"poster"}],key:"3"},[e._v("海报二维码")]),t("a-menu-item",{directives:[{name:"action",rawName:"v-action:delete",arg:"delete"}],key:"4"},[e._v("删除")])],1),t("a",[t("span",[e._v("更多")]),t("a-icon",{attrs:{type:"down"}})],1)],1)],1)}}])}),t("a-modal",{attrs:{title:"分销海报二维码"},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[t("div",{staticClass:"poster-modal"},[t("a-spin",{attrs:{spinning:e.isLoading}},[e.posterUrl?t("img",{attrs:{src:e.posterUrl,alt:""}}):e._e()])],1)]),t("FansModal",{ref:"FansModal"}),t("EditForm",{ref:"EditForm",on:{handleSubmit:e.handleRefresh}})],1)},s=[],n=a("5530"),o=(a("d3b7"),a("b775")),i={list:"/dealer.user/list",fans:"/dealer.user/fans",edit:"/dealer.user/edit",delete:"/dealer.user/delete",poster:"/dealer.user/poster"};function l(e){return Object(o["b"])({url:i.list,method:"get",params:e})}function d(e,t){return Object(o["b"])({url:i.fans,method:"get",params:Object(n["a"])({dealerId:e},t)})}function c(e){return Object(o["b"])({url:i.poster,method:"get",params:{dealerId:e}})}function u(e){return Object(o["b"])({url:i.edit,method:"post",data:e})}function m(e){return Object(o["b"])({url:i.delete,method:"post",data:{dealerId:e}})}var p=a("ab09"),f=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:520,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"分销商姓名",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["real_name",{rules:[{required:!0}]}],expression:"['real_name', { rules: [{ required: true }] }]"}]})],1),t("a-form-item",{attrs:{label:"手机号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["mobile",{rules:[{required:!0}]}],expression:"['mobile', { rules: [{ required: true }] }]"}]})],1)],1)],1)],1)},v=[],h=a("88bc"),y=a.n(h),b={data:function(){return{title:"编辑分销商",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{edit:function(e){this.visible=!0,this.record=e,this.setFieldsValue()},setFieldsValue:function(){var e=this.record,t=this.form.setFieldsValue;this.$nextTick((function(){t(y()(e,["real_name","mobile"]))}))},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,u({dealerId:this.record.user_id,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){return t.confirmLoading=!1}))}}},_=b,w=a("2877"),g=Object(w["a"])(_,f,v,!1,null,null,null),x=g.exports,k=function(){var e=this,t=e._self._c;return t("a-modal",{staticClass:"noborder",attrs:{title:e.title,width:820,visible:e.visible,isLoading:e.isLoading,maskClosable:!1},on:{ok:e.handleCancel,cancel:e.handleCancel}},[t("div",{staticClass:"table-operator"},[t("a-row",{staticClass:"row-item-search"},[t("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[t("a-form-item",{attrs:{label:"昵称/手机号"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["search"],expression:"['search']"}],attrs:{placeholder:"请输入昵称/手机号"}})],1),t("a-form-item",{attrs:{label:"推荐关系"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["level",{initialValue:-1}],expression:"['level', { initialValue: -1 }]"}],attrs:{allowClear:""}},[t("a-select-option",{attrs:{value:-1}},[e._v("全部")]),t("a-select-option",{attrs:{value:1}},[e._v("1级")]),t("a-select-option",{attrs:{value:2}},[e._v("2级")]),t("a-select-option",{attrs:{value:3}},[e._v("3级")])],1)],1),t("a-form-item",{staticClass:"search-btn"},[t("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"id",scroll:{y:"420px",scrollToFirstRowOnChange:!0},loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"user",fn:function(e){return[t("UserItem",{attrs:{user:e}})]}},{key:"mobile",fn:function(a){return[t("span",[e._v(e._s(a.user.mobile?a.user.mobile:"--"))])]}},{key:"money",fn:function(a){return[t("span",{staticClass:"c-p"},[e._v(e._s(a.user.expend_money))])]}},{key:"time",fn:function(a){return[t("span",[e._v(e._s(a.user.create_time))])]}}])})],1)},C=[],S=[{title:"会员ID",dataIndex:"user_id"},{title:"会员信息",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"手机号",scopedSlots:{customRender:"mobile"}},{title:"实际消费金额",scopedSlots:{customRender:"money"}},{title:"注册时间",scopedSlots:{customRender:"time"}}],E={components:{STable:p["b"],UserItem:p["c"]},data:function(){var e=this;return{title:"查看下级粉丝",visible:!1,isLoading:!1,isCreated:!1,searchForm:this.$form.createForm(this),queryParam:{},columns:S,loadData:function(t){return d(e.dealerId,Object(n["a"])(Object(n["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},methods:{show:function(e,t){var a=this;this.visible=!0,this.dealerId=e,this.setDefaultValue(t),this.$nextTick((function(){a.isCreated&&a.handleRefresh(!0),a.isCreated=!0}))},setDefaultValue:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1,t=this.queryParam,a=this.searchForm.setFieldsValue;t.level=e,this.$nextTick((function(){a({level:e})}))},handleRefresh:function(){this.$refs.table.refresh(!0)},handleSearch:function(e){var t=this;e.preventDefault(),this.searchForm.validateFields((function(e,a){e||(t.queryParam=Object(n["a"])(Object(n["a"])({},t.queryParam),a),t.handleRefresh(!0))}))},handleCancel:function(){this.visible=!1,this.queryParam={},this.searchForm.resetFields()}}},L=E,q=(a("0b18"),Object(w["a"])(L,k,C,!1,null,"426831da",null)),I=q.exports,$={name:"Index",components:{STable:p["b"],UserItem:p["c"],EditForm:x,FansModal:I},data:function(){var e=this;return{queryParam:{search:""},columns:[{title:"用户ID",dataIndex:"user_id"},{title:"会员信息",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"姓名/手机号",scopedSlots:{customRender:"contacts"}},{title:"累计佣金/可提现佣金",scopedSlots:{customRender:"money"}},{title:"推荐人",scopedSlots:{customRender:"referee"}},{title:"下级用户",scopedSlots:{customRender:"fans"}},{title:"成为时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(t){return l(Object(n["a"])(Object(n["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))},visible:!1,isLoading:!0,posterUrl:""}},created:function(){},methods:{handleFans:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;this.$refs.FansModal.show(e.user_id,t)},handleEdit:function(e){this.$refs.EditForm.edit(e)},handleMenuClick:function(e,t){var a=e.key,r={1:this.handleOrder,2:this.handleWithdraw,3:this.handlePoster,4:this.handleDelete};r[a](t)},handleOrder:function(e){this.$router.push({path:"/apps/dealer/order",query:{searchType:20,searchValue:e.user_id}})},handleWithdraw:function(e){this.$router.push({path:"/apps/dealer/withdraw",query:{dealerId:e.user_id}})},handlePoster:function(e){var t=this;t.isLoading=!0,t.visible=!0,c(e.user_id,"H5").then((function(e){t.posterUrl=e.data.imageUrl})).finally((function(){return t.isLoading=!1}))},handleDelete:function(e){var t=this,a=t.$confirm({title:"您确定要删除该记录吗?",content:"删除后不可恢复",onOk:function(){return m(e.user_id).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},P=$,O=(a("24c9"),Object(w["a"])(P,r,s,!1,null,"e8007c0e",null));t["default"]=O.exports},7757:function(e,t,a){"use strict";a.r(t);a("b0c0"),a("ac1f"),a("841c"),a("a9e3");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{staticClass:"flex flex-x-end"},[t("a-select",{staticStyle:{width:"220px","margin-right":"20px"},attrs:{placeholder:"请选择打款方式"},model:{value:e.queryParam.payType,callback:function(t){e.$set(e.queryParam,"payType",t)},expression:"queryParam.payType"}},[t("a-select-option",{attrs:{value:-1}},[e._v("全部")]),e._l(e.PayTypeEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])}))],2),t("a-select",{staticStyle:{width:"220px","margin-right":"20px"},attrs:{placeholder:"请选择申请状态"},model:{value:e.queryParam.applyStatus,callback:function(t){e.$set(e.queryParam,"applyStatus",t)},expression:"queryParam.applyStatus"}},[t("a-select-option",{attrs:{value:-1}},[e._v("全部")]),e._l(e.ApplyStatusEnum.data,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])}))],2),t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px"},attrs:{placeholder:"请输入姓名/手机号/昵称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"user",fn:function(e){return[t("UserItem",{attrs:{user:e}})]}},{key:"contacts",fn:function(a){return[t("p",[e._v(e._s(a.real_name))]),t("p",[e._v(e._s(a.mobile))])]}},{key:"money",fn:function(a){return[t("p",[e._v(e._s(Number(a))+"元")])]}},{key:"pay_type",fn:function(a){return[t("a-tag",[e._v(e._s(e.PayTypeEnum[a].name))])]}},{key:"pay_info",fn:function(a){return[a.pay_type==e.PayTypeEnum.WECHAT.value?t("div",[t("span",[e._v("--")])]):e._e(),a.pay_type==e.PayTypeEnum.ALIPAY.value?t("div",[t("p",[e._v(e._s(a.alipay_name))]),t("p",[e._v(e._s(a.alipay_account))])]):e._e(),a.pay_type==e.PayTypeEnum.BANK_CARD.value?t("div",[t("p",[e._v(e._s(a.bank_name))]),t("p",[e._v(e._s(a.bank_account))]),t("p",[e._v(e._s(a.bank_card))])]):e._e()]}},{key:"apply_status",fn:function(a){return[t("a-tag",{attrs:{color:e.ApplyStatusColor[a]}},[e._v(e._s(e.ApplyStatusEnum[a].name))])]}},{key:"audit_time",fn:function(a){return[t("span",[e._v(e._s(a||"--"))])]}},{key:"action",fn:function(a,r){return t("span",{staticClass:"actions"},[r.apply_status==e.ApplyStatusEnum.WAIT.value?[t("a",{directives:[{name:"action",rawName:"v-action:audit",arg:"audit"}],on:{click:function(t){return e.handleAudit(r)}}},[e._v("审核")])]:e._e(),r.apply_status==e.ApplyStatusEnum.PASSED.value?[t("a",{directives:[{name:"action",rawName:"v-action:payed",arg:"payed"}],on:{click:function(t){return e.handlePayed(r)}}},[e._v("已打款")]),t("a",{directives:[{name:"action",rawName:"v-action:wechatPay",arg:"wechatPay"}],on:{click:function(t){return e.handleWechatPay(r)}}},[e._v("微信打款")])]:e._e()],2)}}])}),t("AuditForm",{ref:"AuditForm",on:{handleSubmit:e.handleRefresh}})],1)},s=[],n=a("5530"),o=a("ade3"),i=(a("d3b7"),a("b775")),l={list:"/dealer.withdraw/list",audit:"/dealer.withdraw/audit",payed:"/dealer.withdraw/payed",wechatPay:"/dealer.withdraw/wechatPay"};function d(e){return Object(i["b"])({url:l.list,method:"get",params:e})}function c(e){return Object(i["b"])({url:l.audit,method:"post",data:e})}function u(e){return Object(i["b"])({url:l.payed,method:"post",data:{id:e}})}function m(e){return Object(i["b"])({url:l.wechatPay,method:"post",data:{id:e}})}var p,f=a("ab09"),v=a("5c06"),h=new v["a"]([{key:"WECHAT",name:"微信",value:10},{key:"ALIPAY",name:"支付宝",value:20},{key:"BANK_CARD",name:"银行卡",value:30}]),y=new v["a"]([{key:"WAIT",name:"待审核",value:10},{key:"PASSED",name:"审核通过",value:20},{key:"REJECT",name:"驳回",value:30},{key:"PAYMENT",name:"已打款",value:40}]),b=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:520,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"审核状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["apply_status",{initialValue:20,rules:[{required:!0}]}],expression:"['apply_status', { initialValue: 20, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:20}},[e._v("审核通过")]),t("a-radio",{attrs:{value:30}},[e._v("驳回")])],1)],1),t("a-form-item",{attrs:{label:"驳回原因",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["reject_reason"],expression:"['reject_reason']"}],attrs:{placeholder:"仅在驳回时填写"}})],1)],1)],1)],1)},_=[],w=(a("88bc"),{data:function(){return{title:"审核提现申请",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{edit:function(e){this.visible=!0,this.record=e},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,c({id:this.record.id,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){return t.confirmLoading=!1}))}}}),g=w,x=a("2877"),k=Object(x["a"])(g,b,_,!1,null,null,null),C=k.exports,S=(p={},Object(o["a"])(p,y.WAIT.value,""),Object(o["a"])(p,y.PASSED.value,"green"),Object(o["a"])(p,y.REJECT.value,"red"),Object(o["a"])(p,y.PAYMENT.value,"green"),p),E={name:"Index",components:{STable:f["b"],UserItem:f["c"],AuditForm:C},data:function(){var e=this;return{PayTypeEnum:h,ApplyStatusEnum:y,ApplyStatusColor:S,queryParam:{dealerId:void 0,payType:void 0,applyStatus:void 0,search:""},isLoading:!1,columns:[{title:"记录ID",dataIndex:"id"},{title:"会员信息",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"姓名/手机号",scopedSlots:{customRender:"contacts"}},{title:"提现金额",dataIndex:"money",scopedSlots:{customRender:"money"}},{title:"打款方式",dataIndex:"pay_type",scopedSlots:{customRender:"pay_type"}},{title:"提现信息",scopedSlots:{customRender:"pay_info"}},{title:"审核状态",dataIndex:"apply_status",scopedSlots:{customRender:"apply_status"}},{title:"申请时间",dataIndex:"create_time"},{title:"审核时间",dataIndex:"audit_time",scopedSlots:{customRender:"audit_time"}},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(t){return d(Object(n["a"])(Object(n["a"])({},t),e.queryParam)).then((function(e){return e.data.list}))}}},created:function(){var e=this.$route.query;e.dealerId&&(this.queryParam.dealerId=Number(e.dealerId))},methods:{handleAudit:function(e){this.$refs.AuditForm.edit(e)},handlePayed:function(e){var t=this,a=t.$confirm({title:"您确定要标记为已打款状态吗?",content:"该操作仅改变提现申请状态，实际的付款/支付需要您线下操作",onOk:function(){return u(e.id).then((function(e){t.$message.success(e.message,1.5),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleWechatPay:function(e){var t=this,a=t.$confirm({title:"您确定要使用微信支付在线打款吗?",content:"该操作将使用微信支付在线打款（需开通商家转账到零钱功能）",onOk:function(){return m(e.id).then((function(e){t.$success({title:e.message}),t.handleRefresh()})).finally((function(e){return a.destroy()}))}})},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},L=E,q=Object(x["a"])(L,r,s,!1,null,null,null);t["default"]=q.exports},"78a9":function(e,t,a){"use strict";a.r(t);a("b0c0"),a("99af"),a("caad"),a("2532");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("a-spin",{attrs:{spinning:e.isLoading}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",{staticClass:"row-item-search"},[t("a-form",{staticClass:"search-form",attrs:{form:e.searchForm,layout:"inline"},on:{submit:e.handleSearch}},[t("a-form-item",{attrs:{label:"订单查询"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["searchValue"],expression:"['searchValue']"}],staticStyle:{width:"342px"},attrs:{placeholder:"请输入关键词"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["searchType",{initialValue:10}],expression:"['searchType', { initialValue: 10 }]"}],staticStyle:{width:"100px"},attrs:{slot:"addonBefore"},slot:"addonBefore"},e._l(e.SearchTypeEnum,(function(a,r){return t("a-select-option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.name))])})),1)],1)],1),t("a-form-item",{attrs:{label:"佣金结算"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["settled",{initialValue:-1}],expression:"['settled', { initialValue: -1 }]"}]},[t("a-select-option",{attrs:{value:-1}},[e._v("全部")]),t("a-select-option",{attrs:{value:1}},[e._v("已结算")]),t("a-select-option",{attrs:{value:0}},[e._v("未结算")])],1)],1),t("a-form-item",{staticClass:"search-btn"},[t("a-button",{attrs:{type:"primary",icon:"search","html-type":"submit"}},[e._v("搜索")])],1)],1)],1)],1),t("div",{staticClass:"ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered"},[t("div",{staticClass:"ant-table-content"},[t("div",{staticClass:"ant-table-body"},[t("table",[t("thead",{staticClass:"ant-table-thead"},[t("tr",e._l(e.columns,(function(a,r){return t("th",{key:r},[t("span",{staticClass:"ant-table-header-column"},[t("div",[t("span",{staticClass:"ant-table-column-title"},[e._v(e._s(a.title))])])])])})),0)]),t("tbody",{staticClass:"ant-table-tbody"},[e._l(e.orderList.data,(function(a){return[t("tr",{key:"order_".concat(a.order.order_id,"_1"),staticClass:"order-empty"},[t("td",{attrs:{colspan:"8"}})]),t("tr",{key:"order_".concat(a.order.order_id,"_2")},[t("td",{attrs:{colspan:"8"}},[t("span",{staticClass:"mr-20"},[e._v(e._s(a.order.create_time))]),t("span",[e._v("订单号："+e._s(a.order.order_no))])])]),e._l(a.order.goods,(function(r,s){return t("tr",{key:"orderGoods_".concat(a.order.order_id,"_").concat(s)},[t("td",[t("GoodsItem",{attrs:{data:{image:r.goods_image,imageAlt:"商品图片",title:r.goods_name,goodsProps:r.goods_props}}})],1),t("td",[t("p",[e._v("￥"+e._s(r.goods_price))]),t("p",[e._v("×"+e._s(r.total_num))])]),0===s?[t("td",{attrs:{rowspan:a.order.goods.length}},[t("p",[e._v("￥"+e._s(a.order.pay_price))]),t("p",{staticClass:"c-muted-1"},[e._v("(含运费：￥"+e._s(a.order.express_price)+")")])]),t("td",{attrs:{rowspan:a.order.goods.length}},[a.order.user?t("UserItem",{attrs:{user:a.order.user}}):e._e()],1),t("td",{attrs:{rowspan:a.order.goods.length}},[a.pay_method?t("a-tag",[e._v(e._s(e.PaymentMethodEnum[a.pay_method].name))]):t("span",[e._v("--")])],1),t("td",{attrs:{rowspan:a.order.goods.length}},[t("p",{staticClass:"mtb-2"},[t("span",{staticClass:"f-13"},[e._v("付款状态：")]),t("a-tag",{attrs:{color:a.order.pay_status==e.PayStatusEnum.SUCCESS.value?"green":""}},[e._v(e._s(e.PayStatusEnum[a.order.pay_status].name))])],1),t("p",{staticClass:"mtb-2"},[t("span",{staticClass:"f-13"},[e._v("发货状态：")]),t("a-tag",{attrs:{color:a.order.delivery_status==e.DeliveryStatusEnum.DELIVERED.value?"green":""}},[e._v(e._s(e.DeliveryStatusEnum[a.order.delivery_status].name))])],1),t("p",{staticClass:"mtb-2"},[t("span",{staticClass:"f-13"},[e._v("收货状态：")]),t("a-tag",{attrs:{color:a.order.receipt_status==e.ReceiptStatusEnum.RECEIVED.value?"green":""}},[e._v(e._s(e.ReceiptStatusEnum[a.order.receipt_status].name))])],1),[e.OrderStatusEnum.CANCELLED.value,e.OrderStatusEnum.APPLY_CANCEL.value].includes(a.order.order_status)?t("p",{staticClass:"mtb-2"},[t("span",{staticClass:"f-13"},[e._v("订单状态：")]),t("a-tag",{attrs:{color:e.renderOrderStatusColor(a.order.order_status)}},[e._v(e._s(e.OrderStatusEnum[a.order.order_status].name))])],1):e._e()]),t("td",{attrs:{rowspan:a.order.goods.length}},[t("a-tag",{attrs:{color:a.is_settled?"green":""}},[e._v(e._s(a.is_settled?"已结算":"未结算"))])],1),t("td",{attrs:{rowspan:a.order.goods.length}},[t("div",{staticClass:"actions"},[e.$auth("/order/detail")?t("router-link",{attrs:{to:{path:"/order/detail",query:{orderId:a.order.order_id}},target:"_blank"}},[e._v("详情")]):e._e()],1)])]:e._e()],2)})),t("tr",{key:"order_".concat(a.order.order_id,"_3")},[t("td",{staticClass:"am-text-middle am-text-left",attrs:{colspan:"8"}},[t("div",{staticClass:"order-dealer clearfix"},[a.dealer_first?t("div",{staticClass:"dealer-item"},[t("p",[t("span",{staticClass:"am-text-right"},[e._v("一级分销商：")]),t("span",[e._v(e._s(a.dealer_first.user.nick_name)+" (ID: "+e._s(a.first_user_id)+")")])]),t("p",[t("span",{staticClass:"am-text-right"},[e._v("分销佣金：")]),t("span",{staticClass:"c-red"},[e._v("￥"+e._s(a.first_money))])])]):e._e(),a.dealer_second?t("div",{staticClass:"dealer-item"},[t("p",[t("span",{staticClass:"am-text-right"},[e._v("二级分销商：")]),t("span",[e._v(e._s(a.dealer_second.user.nick_name)+" (ID: "+e._s(a.second_user_id)+")")])]),t("p",[t("span",{staticClass:"am-text-right"},[e._v("分销佣金：")]),t("span",{staticClass:"c-red"},[e._v("￥"+e._s(a.second_money))])])]):e._e(),a.dealer_third?t("div",{staticClass:"dealer-item"},[t("p",[t("span",{staticClass:"am-text-right"},[e._v("三级分销商：")]),t("span",[e._v(e._s(a.dealer_third.user.nick_name)+" (ID: "+e._s(a.third_user_id)+")")])]),t("p",[t("span",{staticClass:"am-text-right"},[e._v("分销佣金：")]),t("span",{staticClass:"c-red"},[e._v("￥"+e._s(a.third_money))])])]):e._e()])])])]}))],2)])]),e.orderList.data.length?e._e():t("a-empty",{attrs:{image:e.simpleImage}})],1)]),e.orderList.data.length?t("div",{staticClass:"pagination"},[t("a-pagination",{attrs:{current:e.page,pageSize:e.orderList.per_page,total:e.orderList.total},on:{change:e.onChangePage}})],1):e._e()])],1)},s=[],n=a("ade3"),o=a("5530"),i=(a("06f4"),a("fc25")),l=(a("a9e3"),a("d3b7"),a("ca00")),d=a("ab09"),c=a("4a95"),u=a("3c76"),m=a("b775"),p={list:"/dealer.order/list"};function f(e){return Object(m["b"])({url:p.list,method:"get",params:e})}var v=[{title:"商品信息",align:"center",dataIndex:"goods",scopedSlots:{customRender:"goods"}},{title:"单价/数量",align:"center",scopedSlots:{customRender:"unit_price"}},{title:"实付款",align:"center",dataIndex:"pay_price",scopedSlots:{customRender:"pay_price"}},{title:"买家",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"支付方式",dataIndex:"pay_method",scopedSlots:{customRender:"pay_method"}},{title:"交易状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"分销佣金结算",dataIndex:"delivery_type",scopedSlots:{customRender:"delivery_type"}},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],h=[{name:"订单号",value:10},{name:"分销商ID",value:20},{name:"会员ID",value:30}],y={name:"Index",components:{GoodsItem:d["a"],UserItem:d["c"]},data:function(){return{searchForm:this.$form.createForm(this),queryParam:{},isLoading:!1,columns:v,page:1,orderList:{data:[],total:0,per_page:10}}},beforeCreate:function(){Object(l["a"])(this,{SearchTypeEnum:h,DeliveryStatusEnum:c["b"],DeliveryTypeEnum:c["c"],OrderStatusEnum:c["e"],PayStatusEnum:c["g"],ReceiptStatusEnum:c["h"],PaymentMethodEnum:u["a"],simpleImage:i["a"].PRESENTED_IMAGE_SIMPLE})},created:function(){this.init()},methods:{init:function(){this.setFieldsValue(),this.handleRefresh(!0)},setFieldsValue:function(){var e=this,t=this.$route.query;this.queryParam={searchType:t.searchType?Number(t.searchType):10,searchValue:t.searchValue?t.searchValue:""},this.$nextTick((function(){e.searchForm.setFieldsValue(e.queryParam)}))},getList:function(){var e=this,t=this.queryParam,a=this.page;return this.isLoading=!0,f(Object(o["a"])(Object(o["a"])({},t),{},{page:a})).then((function(t){e.orderList=t.data.list})).finally((function(){return e.isLoading=!1}))},renderOrderStatusColor:function(e){var t,a=this.OrderStatusEnum,r=(t={},Object(n["a"])(t,a.NORMAL.value,""),Object(n["a"])(t,a.CANCELLED.value,"red"),Object(n["a"])(t,a.APPLY_CANCEL.value,"red"),Object(n["a"])(t,a.COMPLETED.value,"green"),t);return r[e]},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e&&(this.page=1),this.getList()},handleSearch:function(e){var t=this;e.preventDefault(),this.searchForm.validateFields((function(e,a){e||(t.queryParam=Object(o["a"])(Object(o["a"])({},t.queryParam),a),t.handleRefresh(!0))}))},handleReset:function(){this.searchForm.resetFields()},onChangePage:function(e){this.page=e,this.handleRefresh()}}},b=y,_=(a("6747f"),a("2877")),w=Object(_["a"])(b,r,s,!1,null,"987136dc",null);t["default"]=w.exports},"9f51":function(e,t,a){},c7eb:function(e,t,a){"use strict";a.d(t,"a",(function(){return s}));a("a4d3"),a("e01a"),a("d3b7"),a("d28b"),a("3ca3"),a("ddb0"),a("b636"),a("944a"),a("0c47"),a("23dc"),a("3410"),a("159b"),a("b0c0"),a("131a"),a("fb6a");var r=a("53ca");function s(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
s=function(){return e};var e={},t=Object.prototype,a=t.hasOwnProperty,n=Object.defineProperty||function(e,t,a){e[t]=a.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",d=o.toStringTag||"@@toStringTag";function c(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch($){c=function(e,t,a){return e[t]=a}}function u(e,t,a,r){var s=t&&t.prototype instanceof f?t:f,o=Object.create(s.prototype),i=new L(r||[]);return n(o,"_invoke",{value:k(e,a,i)}),o}function m(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch($){return{type:"throw",arg:$}}}e.wrap=u;var p={};function f(){}function v(){}function h(){}var y={};c(y,i,(function(){return this}));var b=Object.getPrototypeOf,_=b&&b(b(q([])));_&&_!==t&&a.call(_,i)&&(y=_);var w=h.prototype=f.prototype=Object.create(y);function g(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function s(n,o,i,l){var d=m(e[n],e,o);if("throw"!==d.type){var c=d.arg,u=c.value;return u&&"object"==Object(r["a"])(u)&&a.call(u,"__await")?t.resolve(u.__await).then((function(e){s("next",e,i,l)}),(function(e){s("throw",e,i,l)})):t.resolve(u).then((function(e){c.value=e,i(c)}),(function(e){return s("throw",e,i,l)}))}l(d.arg)}var o;n(this,"_invoke",{value:function(e,a){function r(){return new t((function(t,r){s(e,a,t,r)}))}return o=o?o.then(r,r):r()}})}function k(e,t,a){var r="suspendedStart";return function(s,n){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===s)throw n;return I()}for(a.method=s,a.arg=n;;){var o=a.delegate;if(o){var i=C(o,a);if(i){if(i===p)continue;return i}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===r)throw r="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r="executing";var l=m(e,t,a);if("normal"===l.type){if(r=a.done?"completed":"suspendedYield",l.arg===p)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(r="completed",a.method="throw",a.arg=l.arg)}}}function C(e,t){var a=t.method,r=e.iterator[a];if(void 0===r)return t.delegate=null,"throw"===a&&e.iterator["return"]&&(t.method="return",t.arg=void 0,C(e,t),"throw"===t.method)||"return"!==a&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+a+"' method")),p;var s=m(r,e.iterator,t.arg);if("throw"===s.type)return t.method="throw",t.arg=s.arg,t.delegate=null,p;var n=s.arg;return n?n.done?(t[e.resultName]=n.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,p):n:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function q(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,s=function t(){for(;++r<e.length;)if(a.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return s.next=s}}return{next:I}}function I(){return{value:void 0,done:!0}}return v.prototype=h,n(w,"constructor",{value:h,configurable:!0}),n(h,"constructor",{value:v,configurable:!0}),v.displayName=c(h,d,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,c(e,d,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},g(x.prototype),c(x.prototype,l,(function(){return this})),e.AsyncIterator=x,e.async=function(t,a,r,s,n){void 0===n&&(n=Promise);var o=new x(u(t,a,r,s),n);return e.isGeneratorFunction(a)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},g(w),c(w,d,"Generator"),c(w,i,(function(){return this})),c(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),a=[];for(var r in t)a.push(r);return a.reverse(),function e(){for(;a.length;){var r=a.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=q,L.prototype={constructor:L,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&a.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(a,r){return o.type="throw",o.arg=e,t.next=a,r&&(t.method="next",t.arg=void 0),!!r}for(var s=this.tryEntries.length-1;s>=0;--s){var n=this.tryEntries[s],o=n.completion;if("root"===n.tryLoc)return r("end");if(n.tryLoc<=this.prev){var i=a.call(n,"catchLoc"),l=a.call(n,"finallyLoc");if(i&&l){if(this.prev<n.catchLoc)return r(n.catchLoc,!0);if(this.prev<n.finallyLoc)return r(n.finallyLoc)}else if(i){if(this.prev<n.catchLoc)return r(n.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return r(n.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var s=this.tryEntries[r];if(s.tryLoc<=this.prev&&a.call(s,"finallyLoc")&&this.prev<s.finallyLoc){var n=s;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=e,o.arg=t,n?(this.method="next",this.next=n.finallyLoc,p):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),E(a),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var r=a.completion;if("throw"===r.type){var s=r.arg;E(a)}return s}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,a){return this.delegate={iterator:q(e),resultName:t,nextLoc:a},"next"===this.method&&(this.arg=void 0),p}},e}},cba3:function(e,t,a){},d149:function(e,t,a){"use strict";a.r(t);a("ac1f"),a("841c"),a("b0c0");var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("div",{staticClass:"table-operator"},[t("a-row",[t("a-col",{staticClass:"flex flex-x-end"},[t("a-input-search",{staticStyle:{"max-width":"300px","min-width":"150px"},attrs:{placeholder:"请输入姓名/手机号/昵称"},on:{search:e.onSearch},model:{value:e.queryParam.search,callback:function(t){e.$set(e.queryParam,"search",t)},expression:"queryParam.search"}})],1)],1)],1),t("s-table",{ref:"table",attrs:{rowKey:"apply_id",loading:e.isLoading,columns:e.columns,data:e.loadData,pageSize:15},scopedSlots:e._u([{key:"user",fn:function(e){return[t("UserItem",{attrs:{user:e}})]}},{key:"contacts",fn:function(a){return[t("p",[e._v(e._s(a.real_name))]),t("p",[e._v(e._s(a.mobile))])]}},{key:"referee",fn:function(a){return[a.referee_id&&a.referee?t("span",[t("UserItem",{attrs:{user:a.referee}})],1):t("span",[e._v("--")])]}},{key:"apply_status",fn:function(a){return[t("a-tag",{attrs:{color:e.ApplyStatusTagColor[a]}},[e._v(e._s(e.ApplyStatusEnum[a].name))])]}},{key:"apply_type",fn:function(a){return[t("a-tag",[e._v(e._s(e.ApplyTypeEnum[a].name))])]}},{key:"action",fn:function(a,r){return t("span",{staticClass:"actions"},[r.apply_status==e.ApplyStatusEnum.WAIT.value?t("span",[t("a",{directives:[{name:"action",rawName:"v-action:audit",arg:"audit"}],on:{click:function(t){return e.handleAudit(r)}}},[e._v("审核")])]):e._e()])}}])}),t("AuditForm",{ref:"AuditForm",on:{handleSubmit:e.handleRefresh}})],1)},s=[],n=a("5530"),o=a("ade3"),i=a("b775"),l={list:"/dealer.apply/list",audit:"/dealer.apply/audit"};function d(e){return Object(i["b"])({url:l.list,method:"get",params:e})}function c(e){return Object(i["b"])({url:l.audit,method:"post",data:e})}var u=a("ab09"),m=a("5c06"),p=new m["a"]([{key:"WAIT",name:"待审核",value:10},{key:"PASSED",name:"审核通过",value:20},{key:"REJECT",name:"驳回",value:30}]),f=new m["a"]([{key:"AUDIT",name:"需后台审核",value:10},{key:"PASS",name:"无需审核",value:20}]),v=function(){var e=this,t=e._self._c;return t("a-modal",{attrs:{title:e.title,width:520,visible:e.visible,confirmLoading:e.confirmLoading,maskClosable:!1},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.confirmLoading}},[t("a-form",{attrs:{form:e.form}},[t("a-form-item",{attrs:{label:"审核状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["apply_status",{initialValue:20,rules:[{required:!0}]}],expression:"['apply_status', { initialValue: 20, rules: [{ required: true }] }]"}]},[t("a-radio",{attrs:{value:20}},[e._v("审核通过")]),t("a-radio",{attrs:{value:30}},[e._v("驳回")])],1)],1),t("a-form-item",{attrs:{label:"驳回原因",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["reject_reason"],expression:"['reject_reason']"}],attrs:{placeholder:"仅在驳回时填写"}})],1)],1)],1)],1)},h=[],y=(a("d3b7"),a("88bc"),{data:function(){return{title:"入驻申请审核",labelCol:{span:7},wrapperCol:{span:13},visible:!1,confirmLoading:!1,form:this.$form.createForm(this),record:{}}},methods:{edit:function(e){this.visible=!0,this.record=e},handleSubmit:function(e){var t=this;e.preventDefault();var a=this.form.validateFields;a((function(e,a){!e&&t.onFormSubmit(a)}))},handleCancel:function(){this.visible=!1,this.form.resetFields()},onFormSubmit:function(e){var t=this;this.confirmLoading=!0,c({applyId:this.record.apply_id,form:e}).then((function(a){t.$message.success(a.message,1.5),t.handleCancel(),t.$emit("handleSubmit",e)})).finally((function(){return t.confirmLoading=!1}))}}}),b=y,_=a("2877"),w=Object(_["a"])(b,v,h,!1,null,null,null),g=w.exports,x={name:"Index",components:{STable:u["b"],UserItem:u["c"],AuditForm:g},data:function(){var e,t=this;return{ApplyStatusEnum:p,ApplyTypeEnum:f,ApplyStatusTagColor:(e={},Object(o["a"])(e,p.WAIT.value,""),Object(o["a"])(e,p.PASSED.value,"green"),Object(o["a"])(e,p.REJECT.value,"red"),e),queryParam:{search:""},isLoading:!1,columns:[{title:"申请ID",dataIndex:"apply_id"},{title:"会员信息",dataIndex:"user",scopedSlots:{customRender:"user"}},{title:"姓名/手机号",scopedSlots:{customRender:"contacts"}},{title:"推荐人",scopedSlots:{customRender:"referee"}},{title:"审核状态",dataIndex:"apply_status",scopedSlots:{customRender:"apply_status"}},{title:"申请方式",dataIndex:"apply_type",scopedSlots:{customRender:"apply_type"}},{title:"申请时间",dataIndex:"create_time"},{title:"操作",dataIndex:"action",width:"180px",scopedSlots:{customRender:"action"}}],loadData:function(e){return d(Object(n["a"])(Object(n["a"])({},e),t.queryParam)).then((function(e){return e.data.list}))}}},created:function(){},methods:{handleAudit:function(e){this.$refs.AuditForm.edit(e)},handleRefresh:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs.table.refresh(e)},onSearch:function(){this.handleRefresh(!0)}}},k=x,C=Object(_["a"])(k,r,s,!1,null,null,null);t["default"]=C.exports},d2ae:function(e,t,a){},eb58:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("a-card",{attrs:{bordered:!1}},[t("div",{staticClass:"card-title"},[e._v(e._s(e.$route.meta.title))]),t("a-spin",{attrs:{spinning:e.isLoading}},[e.isLoading?e._e():t("a-form-model",{ref:"myForm",staticClass:"my-form",attrs:{"label-col":e.labelCol,wrapperCol:e.wrapperCol}},[t("a-tabs",{attrs:{activeKey:e.tabKey,tabBarStyle:{marginBottom:"30px"}},on:{change:e.handleTabs}},[t("a-tab-pane",{key:0,attrs:{tab:"基础设置"}}),t("a-tab-pane",{key:1,attrs:{tab:"分销商条件"}}),t("a-tab-pane",{key:2,attrs:{tab:"佣金设置"}}),t("a-tab-pane",{key:3,attrs:{tab:"结算"}}),t("a-tab-pane",{key:4,attrs:{tab:"自定义文字"}}),t("a-tab-pane",{key:5,attrs:{tab:"申请协议"}}),t("a-tab-pane",{key:6,attrs:{tab:"页面背景图"}})],1),t("div",{staticClass:"tabs-content"},[t("div",{directives:[{name:"show",rawName:"v-show",value:0==e.tabKey,expression:"tabKey == 0"}],staticClass:"tab-pane"},[t("a-form-model-item",{attrs:{label:"是否开启分销功能",required:""}},[t("a-radio-group",{model:{value:e.setting.basic.is_open,callback:function(t){e.$set(e.setting.basic,"is_open",t)},expression:"setting.basic.is_open"}},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1)],1),t("a-form-model-item",{attrs:{label:"分销层级",required:""}},[t("a-radio-group",{model:{value:e.setting.basic.level,callback:function(t){e.$set(e.setting.basic,"level",t)},expression:"setting.basic.level"}},[t("a-radio",{attrs:{value:1}},[e._v("一级")]),t("a-radio",{attrs:{value:2}},[e._v("二级")]),t("a-radio",{attrs:{value:3}},[e._v("三级")])],1)],1),t("a-form-model-item",{attrs:{label:"分销商内购",required:""}},[t("a-radio-group",{model:{value:e.setting.basic.self_buy,callback:function(t){e.$set(e.setting.basic,"self_buy",t)},expression:"setting.basic.self_buy"}},[t("a-radio",{attrs:{value:1}},[e._v("开启")]),t("a-radio",{attrs:{value:0}},[e._v("关闭")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("注：分销商自己购买商品，获得一级佣金，上级获得二级佣金")])])],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:1==e.tabKey,expression:"tabKey == 1"}],staticClass:"tab-pane"},[t("a-form-model-item",{attrs:{label:"成为分销商条件",required:""}},[t("a-radio-group",{model:{value:e.setting.condition.become,callback:function(t){e.$set(e.setting.condition,"become",t)},expression:"setting.condition.become"}},[t("a-radio",{attrs:{value:10}},[e._v("需后台审核")]),t("a-radio",{attrs:{value:20}},[e._v("无需审核")])],1)],1),t("a-form-model-item",{attrs:{label:"购买指定商品成为分销商",required:""}},[t("a-radio-group",{model:{value:e.setting.condition.becomeBuyGoods,callback:function(t){e.$set(e.setting.condition,"becomeBuyGoods",t)},expression:"setting.condition.becomeBuyGoods"}},[t("a-radio",{attrs:{value:0}},[e._v("关闭")]),t("a-radio",{attrs:{value:1}},[e._v("开启")])],1),t("div",{staticClass:"form-item-help"},[t("small",[e._v("注：购买指定商品付款后自动成为分销商，无需后台审核")])]),e.setting.condition.becomeBuyGoods?t("div",{staticClass:"mt-20"},[t("SelectGoods",{attrs:{defaultList:e.becomeBuyGoodsList},model:{value:e.setting.condition.becomeBuyGoodsIds,callback:function(t){e.$set(e.setting.condition,"becomeBuyGoodsIds",t)},expression:"setting.condition.becomeBuyGoodsIds"}})],1):e._e()],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:2==e.tabKey,expression:"tabKey == 2"}],staticClass:"tab-pane"},[t("a-form-model-item",{attrs:{label:"一级佣金比例 ",required:""}},[t("a-input-number",{attrs:{min:0,max:100,precision:0},model:{value:e.setting.commission.first_money,callback:function(t){e.$set(e.setting.commission,"first_money",t)},expression:"setting.commission.first_money"}}),t("span",{staticClass:"ml-10"},[e._v("%")]),t("div",{staticClass:"form-item-help"},[t("small",[e._v("注：佣金比例范围 0% - 100%")])])],1),t("a-form-model-item",{attrs:{label:"二级佣金比例 ",required:""}},[t("a-input-number",{attrs:{min:0,max:100,precision:0},model:{value:e.setting.commission.second_money,callback:function(t){e.$set(e.setting.commission,"second_money",t)},expression:"setting.commission.second_money"}}),t("span",{staticClass:"ml-10"},[e._v("%")]),t("div",{staticClass:"form-item-help"},[t("small",[e._v("注：佣金比例范围 0% - 100%")])])],1),t("a-form-model-item",{attrs:{label:"三级佣金比例 ",required:""}},[t("a-input-number",{attrs:{min:0,max:100,precision:0},model:{value:e.setting.commission.third_money,callback:function(t){e.$set(e.setting.commission,"third_money",t)},expression:"setting.commission.third_money"}}),t("span",{staticClass:"ml-10"},[e._v("%")]),t("div",{staticClass:"form-item-help"},[t("small",[e._v("注：佣金比例范围 0% - 100%")])])],1),t("a-form-model-item",{attrs:{label:"一级积分分成比例 ",required:""}},[t("a-input-number",{attrs:{min:1,max:30,precision:0},model:{value:e.setting.commission.points_money,callback:function(t){e.$set(e.setting.commission,"points_money",t)},expression:"setting.commission.points_money"}}),t("span",{staticClass:"ml-10"},[e._v("%")]),t("div",{staticClass:"form-item-help"},[t("small",[e._v("注：佣金比例范围 1% - 30%")])])],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:3==e.tabKey,expression:"tabKey == 3"}],staticClass:"tab-pane"},[t("a-form-model-item",{attrs:{label:"提现方式",required:""}},[t("a-checkbox-group",{model:{value:e.setting.settlement.pay_type,callback:function(t){e.$set(e.setting.settlement,"pay_type",t)},expression:"setting.settlement.pay_type"}},[t("a-checkbox",{attrs:{value:30}},[e._v("银行卡支付")]),t("a-checkbox",{attrs:{value:20}},[e._v("支付宝支付")]),t("a-checkbox",{attrs:{value:10}},[e._v("微信支付 [商家转账到零钱]")])],1),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("如使用支付宝支付或银行卡支付需要线下手动打款")]),t("p",{staticClass:"extra"},[e._v("如使用微信支付，则需申请微信支付商家转账到零钱功能")]),t("p",{staticClass:"extra"},[e._v("如使用微信支付，分销商必须在微信小程序端申请提现")])])],1),t("a-form-model-item",{attrs:{label:"最低提现额度 ",required:""}},[t("a-input-number",{attrs:{min:0,precision:2},model:{value:e.setting.settlement.min_money,callback:function(t){e.$set(e.setting.settlement,"min_money",t)},expression:"setting.settlement.min_money"}}),t("span",{staticClass:"ml-10"},[e._v("元")]),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("如使用微信支付 [商家转账到零钱]，则打款额度必须大于0.30元")])])],1),t("a-form-model-item",{attrs:{label:"佣金结算周期 ",required:""}},[t("a-input-number",{attrs:{min:0,precision:0},model:{value:e.setting.settlement.settle_days,callback:function(t){e.$set(e.setting.settlement,"settle_days",t)},expression:"setting.settlement.settle_days"}}),t("span",{staticClass:"ml-10"},[e._v("天")]),t("div",{staticClass:"form-item-help"},[t("small",[e._v("佣金结算规则：用户下单付款 -> 订单完成确认收货 -> 超出售后期限 -> 结算佣金")])])],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:4==e.tabKey,expression:"tabKey == 4"}],staticClass:"tab-pane"},[t("a-divider",{attrs:{orientation:"left"}},[e._v("分销中心页面")]),t("a-form-model-item",{attrs:{label:"页面标题",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.index.title.value,callback:function(t){e.$set(e.setting.words.index.title,"value",t)},expression:"setting.words.index.title.value"}})],1),t("a-form-model-item",{attrs:{label:"非分销商提示",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.index.words.not_dealer.value,callback:function(t){e.$set(e.setting.words.index.words.not_dealer,"value",t)},expression:"setting.words.index.words.not_dealer.value"}})],1),t("a-form-model-item",{attrs:{label:"申请成为分销商",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.index.words.apply_now.value,callback:function(t){e.$set(e.setting.words.index.words.apply_now,"value",t)},expression:"setting.words.index.words.apply_now.value"}})],1),t("a-form-model-item",{attrs:{label:"推荐人",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.index.words.referee.value,callback:function(t){e.$set(e.setting.words.index.words.referee,"value",t)},expression:"setting.words.index.words.referee.value"}})],1),t("a-form-model-item",{attrs:{label:"可提现佣金",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.index.words.money.value,callback:function(t){e.$set(e.setting.words.index.words.money,"value",t)},expression:"setting.words.index.words.money.value"}})],1),t("a-form-model-item",{attrs:{label:"待提现佣金",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.index.words.freeze_money.value,callback:function(t){e.$set(e.setting.words.index.words.freeze_money,"value",t)},expression:"setting.words.index.words.freeze_money.value"}})],1),t("a-form-model-item",{attrs:{label:"已提现金额",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.index.words.total_money.value,callback:function(t){e.$set(e.setting.words.index.words.total_money,"value",t)},expression:"setting.words.index.words.total_money.value"}})],1),t("a-form-model-item",{attrs:{label:"去提现",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.index.words.withdraw.value,callback:function(t){e.$set(e.setting.words.index.words.withdraw,"value",t)},expression:"setting.words.index.words.withdraw.value"}})],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("申请成为分销商页面")]),t("a-form-model-item",{attrs:{label:"页面标题",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.apply.title.value,callback:function(t){e.$set(e.setting.words.apply.title,"value",t)},expression:"setting.words.apply.title.value"}})],1),t("a-form-model-item",{attrs:{label:"请填写申请信息",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.apply.words.title.value,callback:function(t){e.$set(e.setting.words.apply.words.title,"value",t)},expression:"setting.words.apply.words.title.value"}})],1),t("a-form-model-item",{attrs:{label:"分销商申请协议",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.apply.words.license.value,callback:function(t){e.$set(e.setting.words.apply.words.license,"value",t)},expression:"setting.words.apply.words.license.value"}})],1),t("a-form-model-item",{attrs:{label:"申请成为经销商",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.apply.words.submit.value,callback:function(t){e.$set(e.setting.words.apply.words.submit,"value",t)},expression:"setting.words.apply.words.submit.value"}})],1),t("a-form-model-item",{attrs:{label:"审核中提示信息",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.apply.words.wait_audit.value,callback:function(t){e.$set(e.setting.words.apply.words.wait_audit,"value",t)},expression:"setting.words.apply.words.wait_audit.value"}})],1),t("a-form-model-item",{attrs:{label:"去商城逛逛",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.apply.words.goto_mall.value,callback:function(t){e.$set(e.setting.words.apply.words.goto_mall,"value",t)},expression:"setting.words.apply.words.goto_mall.value"}})],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("分销订单页面")]),t("a-form-model-item",{attrs:{label:"页面标题",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.order.title.value,callback:function(t){e.$set(e.setting.words.order.title,"value",t)},expression:"setting.words.order.title.value"}})],1),t("a-form-model-item",{attrs:{label:"全部",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.order.words.all.value,callback:function(t){e.$set(e.setting.words.order.words.all,"value",t)},expression:"setting.words.order.words.all.value"}})],1),t("a-form-model-item",{attrs:{label:"未结算",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.order.words.unsettled.value,callback:function(t){e.$set(e.setting.words.order.words.unsettled,"value",t)},expression:"setting.words.order.words.unsettled.value"}})],1),t("a-form-model-item",{attrs:{label:"已结算",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.order.words.settled.value,callback:function(t){e.$set(e.setting.words.order.words.settled,"value",t)},expression:"setting.words.order.words.settled.value"}})],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("我的团队页面")]),t("a-form-model-item",{attrs:{label:"页面标题",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.team.title.value,callback:function(t){e.$set(e.setting.words.team.title,"value",t)},expression:"setting.words.team.title.value"}})],1),t("a-form-model-item",{attrs:{label:"团队总人数",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.team.words.total_team.value,callback:function(t){e.$set(e.setting.words.team.words.total_team,"value",t)},expression:"setting.words.team.words.total_team.value"}})],1),t("a-form-model-item",{attrs:{label:"一级团队",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.team.words.first.value,callback:function(t){e.$set(e.setting.words.team.words.first,"value",t)},expression:"setting.words.team.words.first.value"}})],1),t("a-form-model-item",{attrs:{label:"二级团队",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.team.words.second.value,callback:function(t){e.$set(e.setting.words.team.words.second,"value",t)},expression:"setting.words.team.words.second.value"}})],1),t("a-form-model-item",{attrs:{label:"三级团队",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.team.words.third.value,callback:function(t){e.$set(e.setting.words.team.words.third,"value",t)},expression:"setting.words.team.words.third.value"}})],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("提现明细页面")]),t("a-form-model-item",{attrs:{label:"页面标题",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.withdraw_list.title.value,callback:function(t){e.$set(e.setting.words.withdraw_list.title,"value",t)},expression:"setting.words.withdraw_list.title.value"}})],1),t("a-form-model-item",{attrs:{label:"全部",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.withdraw_list.words.all.value,callback:function(t){e.$set(e.setting.words.withdraw_list.words.all,"value",t)},expression:"setting.words.withdraw_list.words.all.value"}})],1),t("a-form-model-item",{attrs:{label:"审核中",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.withdraw_list.words.apply_10.value,callback:function(t){e.$set(e.setting.words.withdraw_list.words.apply_10,"value",t)},expression:"setting.words.withdraw_list.words.apply_10.value"}})],1),t("a-form-model-item",{attrs:{label:"审核通过",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.withdraw_list.words.apply_20.value,callback:function(t){e.$set(e.setting.words.withdraw_list.words.apply_20,"value",t)},expression:"setting.words.withdraw_list.words.apply_20.value"}})],1),t("a-form-model-item",{attrs:{label:"驳回",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.withdraw_list.words.apply_30.value,callback:function(t){e.$set(e.setting.words.withdraw_list.words.apply_30,"value",t)},expression:"setting.words.withdraw_list.words.apply_30.value"}})],1),t("a-form-model-item",{attrs:{label:"已打款",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.withdraw_list.words.apply_40.value,callback:function(t){e.$set(e.setting.words.withdraw_list.words.apply_40,"value",t)},expression:"setting.words.withdraw_list.words.apply_40.value"}})],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("申请提现页面")]),t("a-form-model-item",{attrs:{label:"页面标题",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.withdraw_apply.title.value,callback:function(t){e.$set(e.setting.words.withdraw_apply.title,"value",t)},expression:"setting.words.withdraw_apply.title.value"}})],1),t("a-form-model-item",{attrs:{label:"可提现佣金",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.withdraw_apply.words.capital.value,callback:function(t){e.$set(e.setting.words.withdraw_apply.words.capital,"value",t)},expression:"setting.words.withdraw_apply.words.capital.value"}})],1),t("a-form-model-item",{attrs:{label:"提现金额",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.withdraw_apply.words.money.value,callback:function(t){e.$set(e.setting.words.withdraw_apply.words.money,"value",t)},expression:"setting.words.withdraw_apply.words.money.value"}})],1),t("a-form-model-item",{attrs:{label:"请输入要提取的金额",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.withdraw_apply.words.money_placeholder.value,callback:function(t){e.$set(e.setting.words.withdraw_apply.words.money_placeholder,"value",t)},expression:"setting.words.withdraw_apply.words.money_placeholder.value"}})],1),t("a-form-model-item",{attrs:{label:"最低提现佣金",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.withdraw_apply.words.min_money.value,callback:function(t){e.$set(e.setting.words.withdraw_apply.words.min_money,"value",t)},expression:"setting.words.withdraw_apply.words.min_money.value"}})],1),t("a-form-model-item",{attrs:{label:"提交申请",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.withdraw_apply.words.submit.value,callback:function(t){e.$set(e.setting.words.withdraw_apply.words.submit,"value",t)},expression:"setting.words.withdraw_apply.words.submit.value"}})],1),t("a-divider",{attrs:{orientation:"left"}},[e._v("推广二维码")]),t("a-form-model-item",{attrs:{label:"页面标题",required:""}},[t("a-input",{attrs:{autocomplete:"off"},model:{value:e.setting.words.poster.title.value,callback:function(t){e.$set(e.setting.words.poster.title,"value",t)},expression:"setting.words.poster.title.value"}})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:5==e.tabKey,expression:"tabKey == 5"}],staticClass:"tab-pane"},[t("a-form-model-item",{attrs:{label:"分销商申请协议",required:""}},[t("a-textarea",{attrs:{"auto-size":{minRows:6,maxRows:12}},model:{value:e.setting.license.license,callback:function(t){e.$set(e.setting.license,"license",t)},expression:"setting.license.license"}})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:6==e.tabKey,expression:"tabKey == 6"}],staticClass:"tab-pane"},[t("a-form-model-item",{attrs:{label:"分销中心首页",required:""}},[t("SelectImage2",{staticClass:"mb-10",attrs:{width:375,height:100,tips:"建议尺寸：宽750 高度200"},model:{value:e.setting.background.index,callback:function(t){e.$set(e.setting.background,"index",t)},expression:"setting.background.index"}}),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("尺寸：宽750像素 高度不限")])])],1),t("a-form-model-item",{attrs:{label:"申请成为分销商页",required:""}},[t("SelectImage2",{staticClass:"mb-10",attrs:{width:375,height:100,tips:"建议尺寸：宽750 高度200"},model:{value:e.setting.background.apply,callback:function(t){e.$set(e.setting.background,"apply",t)},expression:"setting.background.apply"}}),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("尺寸：宽750像素 高度不限")])])],1),t("a-form-model-item",{attrs:{label:"申请提现页",required:""}},[t("SelectImage2",{staticClass:"mb-10",attrs:{width:375,height:100,tips:"建议尺寸：宽750 高度200"},model:{value:e.setting.background.withdraw_apply,callback:function(t){e.$set(e.setting.background,"withdraw_apply",t)},expression:"setting.background.withdraw_apply"}}),t("div",{staticClass:"form-item-help"},[t("p",{staticClass:"extra"},[e._v("尺寸：宽750像素 高度不限")])])],1)],1),t("a-form-model-item",{staticClass:"mt-30",attrs:{wrapperCol:{offset:5}}},[t("a-button",{attrs:{type:"primary",loading:e.confirmLoading},on:{click:e.handleSubmit}},[e._v("保存")])],1)],1)],1)],1)],1)},s=[],n=a("c7eb"),o=a("1da1"),i=(a("d3b7"),a("88bc"),a("2d32")),l=a("d084"),d=a("2af9"),c={components:{SelectGoods:d["g"],SelectImage2:d["i"]},data:function(){return{tabKey:0,labelCol:{span:5},wrapperCol:{span:10},isLoading:!1,confirmLoading:!1,setting:{},becomeBuyGoodsList:[]}},created:function(){var e=this;return Object(o["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getDetail();case 2:return t.next=4,e.getBecomeBuyGoodsList();case 4:case"end":return t.stop()}}),t)})))()},methods:{handleTabs:function(e){this.tabKey=e},getDetail:function(){var e=this;return Object(o["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,i["a"]().then((function(t){e.setting=t.data.setting})).finally((function(){return e.isLoading=!1}));case 3:case"end":return t.stop()}}),t)})))()},getBecomeBuyGoodsList:function(){var e=this;return Object(o["a"])(Object(n["a"])().mark((function t(){var a;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=e.setting.condition.becomeBuyGoodsIds,!(a.length>0)){t.next=5;break}return e.isLoading=!0,t.next=5,l["g"](a).then((function(t){e.becomeBuyGoodsList=t.data.list})).finally((function(t){e.isLoading=!1}));case 5:case"end":return t.stop()}}),t)})))()},handleSubmit:function(e){var t=this;e.preventDefault(),this.confirmLoading=!0,i["c"]({form:this.setting}).then((function(e){t.$message.success(e.message,1.5)})).finally((function(){return t.confirmLoading=!1}))},onFormSubmit:function(e){var t=this;this.isLoading=!0,i["c"]({form:e}).then((function(e){return t.$message.success(e.message,1.5)})).finally((function(){return t.isLoading=!1}))}}},u=c,m=(a("5a70"),a("2877")),p=Object(m["a"])(u,r,s,!1,null,"6f3dcd88",null);t["default"]=p.exports},ed2f:function(e,t,a){}}]);