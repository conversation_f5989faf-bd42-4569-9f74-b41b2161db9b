!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const e={},t=function(t,n,o){if(!n||0===n.length)return t();const r=document.getElementsByTagName("link");return Promise.all(n.map((t=>{if(t=function(e,t){return new URL(e,t).href}(t,o),t in e)return;e[t]=!0;const n=t.endsWith(".css"),i=n?'[rel="stylesheet"]':"";if(!!o)for(let e=r.length-1;e>=0;e--){const o=r[e];if(o.href===t&&(!n||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${t}"]${i}`))return;const a=document.createElement("link");return a.rel=n?"stylesheet":"modulepreload",n||(a.as="script",a.crossOrigin=""),a.href=t,document.head.appendChild(a),n?new Promise(((e,n)=>{a.addEventListener("load",e),a.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${t}`))))})):void 0}))).then((()=>t()))};function n(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}const o={},r=[],i=()=>{},a=()=>!1,s=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),l=e=>e.startsWith("onUpdate:"),c=Object.assign,u=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},d=Object.prototype.hasOwnProperty,p=(e,t)=>d.call(e,t),f=Array.isArray,h=e=>"[object Map]"===T(e),g=e=>"[object Set]"===T(e),m=e=>"[object Date]"===T(e),v=e=>"function"==typeof e,y=e=>"string"==typeof e,b=e=>"symbol"==typeof e,_=e=>null!==e&&"object"==typeof e,w=e=>(_(e)||v(e))&&v(e.then)&&v(e.catch),x=Object.prototype.toString,T=e=>x.call(e),S=e=>"[object Object]"===T(e),k=e=>y(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,C=n(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),E=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},A=/-(\w)/g,M=E((e=>e.replace(A,((e,t)=>t?t.toUpperCase():"")))),P=/\B([A-Z])/g,I=E((e=>e.replace(P,"-$1").toLowerCase())),O=E((e=>e.charAt(0).toUpperCase()+e.slice(1))),L=E((e=>e?`on${O(e)}`:"")),$=(e,t)=>!Object.is(e,t),D=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},R=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},B=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let N;function q(e){if(f(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=y(o)?F(o):q(o);if(r)for(const e in r)t[e]=r[e]}return t}if(y(e)||_(e))return e}const j=/;(?![^(]*\))/g,z=/:([^]+)/,V=/\/\*[^]*?\*\//g;function F(e){const t={};return e.replace(V,"").split(j).forEach((e=>{if(e){const n=e.split(z);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function U(e){let t="";if(y(e))t=e;else if(f(e))for(let n=0;n<e.length;n++){const o=U(e[n]);o&&(t+=o+" ")}else if(_(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const W=n("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function H(e){return!!e||""===e}function Y(e,t){if(e===t)return!0;let n=m(e),o=m(t);if(n||o)return!(!n||!o)&&e.getTime()===t.getTime();if(n=b(e),o=b(t),n||o)return e===t;if(n=f(e),o=f(t),n||o)return!(!n||!o)&&function(e,t){if(e.length!==t.length)return!1;let n=!0;for(let o=0;n&&o<e.length;o++)n=Y(e[o],t[o]);return n}(e,t);if(n=_(e),o=_(t),n||o){if(!n||!o)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e){const o=e.hasOwnProperty(n),r=t.hasOwnProperty(n);if(o&&!r||!o&&r||!Y(e[n],t[n]))return!1}}return String(e)===String(t)}function X(e,t){return e.findIndex((e=>Y(e,t)))}const G=e=>y(e)?e:null==e?"":f(e)||_(e)&&(e.toString===x||!v(e.toString))?JSON.stringify(e,J,2):String(e),J=(e,t)=>t&&t.__v_isRef?J(e,t.value):h(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[Q(t,o)+" =>"]=n,e)),{})}:g(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>Q(e)))}:b(t)?Q(t):!_(t)||f(t)||S(t)?t:String(t),Q=(e,t="")=>{var n;return b(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},K=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view"].map((e=>"uni-"+e)),Z=["list-view","list-item","sticky-section","sticky-header","cloud-db-element"].map((e=>"uni-"+e)),ee=["list-item"].map((e=>"uni-"+e));function te(e){if(-1!==ee.indexOf(e))return!1;const t="uni-"+e.replace("v-uni-","");return-1!==K.indexOf(t)||-1!==Z.indexOf(t)}const ne=["%","%"],oe=/^([a-z-]+:)?\/\//i,re=/^data:.*,.*/;function ie(e){return e&&(e.appContext?e.proxy:e)}function ae(e){if(!e)return;let t=e.type.name;for(;t&&te(I(t));)t=(e=e.parent).type.name;return e.proxy}function se(e){return 1===e.nodeType}function le(e){if(e instanceof Map){const t={};return e.forEach(((e,n)=>{t[n]=e})),q(t)}if(f(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=y(o)?F(o):le(o);if(r)for(const e in r)t[e]=r[e]}return t}return q(e)}function ce(e){let t="";if(e instanceof Map)e.forEach(((e,n)=>{e&&(t+=n+" ")}));else if(f(e))for(let n=0;n<e.length;n++){const o=ce(e[n]);o&&(t+=o+" ")}else t=U(e);return t.trim()}function ue(e){return 0===e.indexOf("/")}function de(e){return ue(e)?e:"/"+e}function pe(e){return ue(e)?e.slice(1):e}function fe(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}const he=e=>e>9?e:"0"+e;function ge({date:e=new Date,mode:t="date"}){return"time"===t?he(e.getHours())+":"+he(e.getMinutes()):e.getFullYear()+"-"+he(e.getMonth()+1)+"-"+he(e.getDate())}function me(e,t){e=e||{},y(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?v(e.success)&&e.success(t):v(e.fail)&&e.fail(t),v(e.complete)&&e.complete(t)}function ve(e){return M(e.substring(5))}const ye=fe((()=>{const e=HTMLElement.prototype,t=e.setAttribute;e.setAttribute=function(e,n){if(e.startsWith("data-")&&this.tagName.startsWith("UNI-")){(this.__uniDataset||(this.__uniDataset={}))[ve(e)]=n}t.call(this,e,n)};const n=e.removeAttribute;e.removeAttribute=function(e){this.__uniDataset&&e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&delete this.__uniDataset[ve(e)],n.call(this,e)}}));function be(e){return c({},e.dataset,e.__uniDataset)}const _e=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function we(e){return{passive:e}}function xe(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:be(e),offsetTop:n,offsetLeft:o}}function Te(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function Se(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=Te(e[n])}catch(EC){t[n]=e[n]}})),t}const ke=/\+/g;function Ce(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(ke," ");let r=e.indexOf("="),i=Te(r<0?e:e.slice(0,r)),a=r<0?null:Te(e.slice(r+1));if(i in t){let e=t[i];f(e)||(e=t[i]=[e]),e.push(a)}else t[i]=a}return t}function Ee(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);const i=()=>e.apply(this,arguments);r=o(i,t)};return i.cancel=function(){n(r)},i}class Ae{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const Me=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"],Pe=["onLoad","onShow"];const Ie=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];let Oe;const Le=[];const $e=fe(((e,t)=>{if(v(e._component.onError))return t(e)})),De=function(){};De.prototype={on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var i=o.length-1;i>=0;i--)if(o[i].fn===t||o[i].fn._===t){o.splice(i,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var Re=De;const Be={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function Ne(e,t={},n="light"){const o=t[n],r={};return o?(Object.keys(e).forEach((i=>{let a=e[i];r[i]=(()=>{if(S(a))return Ne(a,t,n);if(f(a))return a.map((e=>S(e)?Ne(e,t,n):e));if(y(a)&&a.startsWith("@")){const t=a.replace("@","");let n=o[t]||a;switch(i){case"titleColor":n="black"===n?"#000000":"#ffffff";break;case"borderStyle":n=(e=n)&&e in Be?Be[e]:e}return n}var e;return a})()})),r):e}let qe;class je{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=qe,!e&&qe&&(this.index=(qe.scopes||(qe.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=qe;try{return qe=this,e()}finally{qe=t}}}on(){qe=this}off(){qe=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function ze(e){return new je(e)}const Ve=e=>{const t=new Set(e);return t.w=0,t.n=0,t},Fe=e=>(e.w&Ye)>0,Ue=e=>(e.n&Ye)>0,We=new WeakMap;let He=0,Ye=1;let Xe;const Ge=Symbol(""),Je=Symbol("");class Qe{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,function(e,t=qe){t&&t.active&&t.effects.push(e)}(this,n)}run(){if(!this.active)return this.fn();let e=Xe,t=Ze;for(;e;){if(e===this)return;e=e.parent}try{return this.parent=Xe,Xe=this,Ze=!0,Ye=1<<++He,He<=30?(({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=Ye})(this):Ke(this),this.fn()}finally{He<=30&&(e=>{const{deps:t}=e;if(t.length){let n=0;for(let o=0;o<t.length;o++){const r=t[o];Fe(r)&&!Ue(r)?r.delete(e):t[n++]=r,r.w&=~Ye,r.n&=~Ye}t.length=n}})(this),Ye=1<<--He,Xe=this.parent,Ze=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){Xe===this?this.deferStop=!0:this.active&&(Ke(this),this.onStop&&this.onStop(),this.active=!1)}}function Ke(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let Ze=!0;const et=[];function tt(){et.push(Ze),Ze=!1}function nt(){const e=et.pop();Ze=void 0===e||e}function ot(e,t,n){if(Ze&&Xe){let t=We.get(e);t||We.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=Ve()),rt(o)}}function rt(e,t){let n=!1;He<=30?Ue(e)||(e.n|=Ye,n=!Fe(e)):n=!e.has(Xe),n&&(e.add(Xe),Xe.deps.push(e))}function it(e,t,n,o,r,i){const a=We.get(e);if(!a)return;let s=[];if("clear"===t)s=[...a.values()];else if("length"===n&&f(e)){const e=Number(o);a.forEach(((t,n)=>{("length"===n||n>=e)&&s.push(t)}))}else switch(void 0!==n&&s.push(a.get(n)),t){case"add":f(e)?k(n)&&s.push(a.get("length")):(s.push(a.get(Ge)),h(e)&&s.push(a.get(Je)));break;case"delete":f(e)||(s.push(a.get(Ge)),h(e)&&s.push(a.get(Je)));break;case"set":h(e)&&s.push(a.get(Ge))}if(1===s.length)s[0]&&at(s[0]);else{const e=[];for(const t of s)t&&e.push(...t);at(Ve(e))}}function at(e,t){const n=f(e)?e:[...e];for(const o of n)o.computed&&st(o);for(const o of n)o.computed||st(o)}function st(e,t){(e!==Xe||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const lt=n("__proto__,__v_isRef,__isVue"),ct=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(b)),ut=mt(),dt=mt(!1,!0),pt=mt(!0),ft=ht();function ht(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=en(this);for(let t=0,r=this.length;t<r;t++)ot(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(en)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){tt();const n=en(this)[t].apply(this,e);return nt(),n}})),e}function gt(e){const t=en(this);return ot(t,0,e),t.hasOwnProperty(e)}function mt(e=!1,t=!1){return function(n,o,r){if("__v_isReactive"===o)return!e;if("__v_isReadonly"===o)return e;if("__v_isShallow"===o)return t;if("__v_raw"===o&&r===(e?t?Wt:Ut:t?Ft:Vt).get(n))return n;const i=f(n);if(!e){if(i&&p(ft,o))return Reflect.get(ft,o,r);if("hasOwnProperty"===o)return gt}const a=Reflect.get(n,o,r);return(b(o)?ct.has(o):lt(o))?a:(e||ot(n,0,o),t?a:sn(a)?i&&k(o)?a:a.value:_(a)?e?Xt(a):Yt(a):a)}}function vt(e=!1){return function(t,n,o,r){let i=t[n];if(Qt(i)&&sn(i)&&!sn(o))return!1;if(!e&&(Kt(o)||Qt(o)||(i=en(i),o=en(o)),!f(t)&&sn(i)&&!sn(o)))return i.value=o,!0;const a=f(t)&&k(n)?Number(n)<t.length:p(t,n),s=Reflect.set(t,n,o,r);return t===en(r)&&(a?$(o,i)&&it(t,"set",n,o):it(t,"add",n,o)),s}}const yt={get:ut,set:vt(),deleteProperty:function(e,t){const n=p(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&it(e,"delete",t,void 0),o},has:function(e,t){const n=Reflect.has(e,t);return b(t)&&ct.has(t)||ot(e,0,t),n},ownKeys:function(e){return ot(e,0,f(e)?"length":Ge),Reflect.ownKeys(e)}},bt={get:pt,set:(e,t)=>!0,deleteProperty:(e,t)=>!0},_t=c({},yt,{get:dt,set:vt(!0)}),wt=e=>e,xt=e=>Reflect.getPrototypeOf(e);function Tt(e,t,n=!1,o=!1){const r=en(e=e.__v_raw),i=en(t);n||(t!==i&&ot(r,0,t),ot(r,0,i));const{has:a}=xt(r),s=o?wt:n?on:nn;return a.call(r,t)?s(e.get(t)):a.call(r,i)?s(e.get(i)):void(e!==r&&e.get(t))}function St(e,t=!1){const n=this.__v_raw,o=en(n),r=en(e);return t||(e!==r&&ot(o,0,e),ot(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function kt(e,t=!1){return e=e.__v_raw,!t&&ot(en(e),0,Ge),Reflect.get(e,"size",e)}function Ct(e){e=en(e);const t=en(this);return xt(t).has.call(t,e)||(t.add(e),it(t,"add",e,e)),this}function Et(e,t){t=en(t);const n=en(this),{has:o,get:r}=xt(n);let i=o.call(n,e);i||(e=en(e),i=o.call(n,e));const a=r.call(n,e);return n.set(e,t),i?$(t,a)&&it(n,"set",e,t):it(n,"add",e,t),this}function At(e){const t=en(this),{has:n,get:o}=xt(t);let r=n.call(t,e);r||(e=en(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&it(t,"delete",e,void 0),i}function Mt(){const e=en(this),t=0!==e.size,n=e.clear();return t&&it(e,"clear",void 0,void 0),n}function Pt(e,t){return function(n,o){const r=this,i=r.__v_raw,a=en(i),s=t?wt:e?on:nn;return!e&&ot(a,0,Ge),i.forEach(((e,t)=>n.call(o,s(e),s(t),r)))}}function It(e,t,n){return function(...o){const r=this.__v_raw,i=en(r),a=h(i),s="entries"===e||e===Symbol.iterator&&a,l="keys"===e&&a,c=r[e](...o),u=n?wt:t?on:nn;return!t&&ot(i,0,l?Je:Ge),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:s?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Ot(e){return function(...t){return"delete"!==e&&this}}function Lt(){const e={get(e){return Tt(this,e)},get size(){return kt(this)},has:St,add:Ct,set:Et,delete:At,clear:Mt,forEach:Pt(!1,!1)},t={get(e){return Tt(this,e,!1,!0)},get size(){return kt(this)},has:St,add:Ct,set:Et,delete:At,clear:Mt,forEach:Pt(!1,!0)},n={get(e){return Tt(this,e,!0)},get size(){return kt(this,!0)},has(e){return St.call(this,e,!0)},add:Ot("add"),set:Ot("set"),delete:Ot("delete"),clear:Ot("clear"),forEach:Pt(!0,!1)},o={get(e){return Tt(this,e,!0,!0)},get size(){return kt(this,!0)},has(e){return St.call(this,e,!0)},add:Ot("add"),set:Ot("set"),delete:Ot("delete"),clear:Ot("clear"),forEach:Pt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=It(r,!1,!1),n[r]=It(r,!0,!1),t[r]=It(r,!1,!0),o[r]=It(r,!0,!0)})),[e,n,t,o]}const[$t,Dt,Rt,Bt]=Lt();function Nt(e,t){const n=t?e?Bt:Rt:e?Dt:$t;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(p(n,o)&&o in t?n:t,o,r)}const qt={get:Nt(!1,!1)},jt={get:Nt(!1,!0)},zt={get:Nt(!0,!1)},Vt=new WeakMap,Ft=new WeakMap,Ut=new WeakMap,Wt=new WeakMap;function Ht(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>T(e).slice(8,-1))(e))}function Yt(e){return Qt(e)?e:Gt(e,!1,yt,qt,Vt)}function Xt(e){return Gt(e,!0,bt,zt,Ut)}function Gt(e,t,n,o,r){if(!_(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const a=Ht(e);if(0===a)return e;const s=new Proxy(e,2===a?o:n);return r.set(e,s),s}function Jt(e){return Qt(e)?Jt(e.__v_raw):!(!e||!e.__v_isReactive)}function Qt(e){return!(!e||!e.__v_isReadonly)}function Kt(e){return!(!e||!e.__v_isShallow)}function Zt(e){return Jt(e)||Qt(e)}function en(e){const t=e&&e.__v_raw;return t?en(t):e}function tn(e){return R(e,"__v_skip",!0),e}const nn=e=>_(e)?Yt(e):e,on=e=>_(e)?Xt(e):e;function rn(e){Ze&&Xe&&rt((e=en(e)).dep||(e.dep=Ve()))}function an(e,t){const n=(e=en(e)).dep;n&&at(n)}function sn(e){return!(!e||!0!==e.__v_isRef)}function ln(e){return un(e,!1)}function cn(e){return un(e,!0)}function un(e,t){return sn(e)?e:new dn(e,t)}class dn{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:en(e),this._value=t?e:nn(e)}get value(){return rn(this),this._value}set value(e){const t=this.__v_isShallow||Kt(e)||Qt(e);e=t?e:en(e),$(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:nn(e),an(this))}}function pn(e){return sn(e)?e.value:e}const fn={get:(e,t,n)=>pn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return sn(r)&&!sn(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function hn(e){return Jt(e)?e:new Proxy(e,fn)}var gn;class mn{constructor(e,t,n,o){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this[gn]=!1,this._dirty=!0,this.effect=new Qe(e,(()=>{this._dirty||(this._dirty=!0,an(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=en(this);return rn(e),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function vn(e,t,n,o){let r;try{r=o?e(...o):e()}catch(i){bn(i,t,n)}return r}function yn(e,t,n,o){if(v(e)){const r=vn(e,t,n,o);return r&&w(r)&&r.catch((e=>{bn(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(yn(e[i],t,n,o));return r}function bn(e,t,n,o=!0){t&&t.vnode;if(t){let o=t.parent;const r=t.proxy,i=n;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const a=t.appContext.config.errorHandler;if(a)return void vn(a,null,10,[e,r,i])}!function(e,t,n,o=!0){console.error(e)}(e,0,0,o)}gn="__v_isReadonly";let _n=!1,wn=!1;const xn=[];let Tn=0;const Sn=[];let kn=null,Cn=0;const En=Promise.resolve();let An=null;function Mn(e){const t=An||En;return e?t.then(this?e.bind(this):e):t}function Pn(e){xn.length&&xn.includes(e,_n&&e.allowRecurse?Tn+1:Tn)||(null==e.id?xn.push(e):xn.splice(function(e){let t=Tn+1,n=xn.length;for(;t<n;){const o=t+n>>>1;$n(xn[o])<e?t=o+1:n=o}return t}(e.id),0,e),In())}function In(){_n||wn||(wn=!0,An=En.then(Rn))}function On(e,t=(_n?Tn+1:0)){for(;t<xn.length;t++){const e=xn[t];e&&e.pre&&(xn.splice(t,1),t--,e())}}function Ln(e){if(Sn.length){const e=[...new Set(Sn)];if(Sn.length=0,kn)return void kn.push(...e);for(kn=e,kn.sort(((e,t)=>$n(e)-$n(t))),Cn=0;Cn<kn.length;Cn++)kn[Cn]();kn=null,Cn=0}}const $n=e=>null==e.id?1/0:e.id,Dn=(e,t)=>{const n=$n(e)-$n(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Rn(e){wn=!1,_n=!0,xn.sort(Dn);try{for(Tn=0;Tn<xn.length;Tn++){const e=xn[Tn];e&&!1!==e.active&&vn(e,null,14)}}finally{Tn=0,xn.length=0,Ln(),_n=!1,An=null,(xn.length||Sn.length)&&Rn()}}function Bn(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||o;let i=n;const a=t.startsWith("update:"),s=a&&t.slice(7);if(s&&s in r){const e=`${"modelValue"===s?"model":s}Modifiers`,{number:t,trim:a}=r[e]||o;a&&(i=n.map((e=>y(e)?e.trim():e))),t&&(i=n.map(B))}let l,c=r[l=L(t)]||r[l=L(M(t))];!c&&a&&(c=r[l=L(I(t))]),c&&yn(c,e,6,Nn(e,c,i));const u=r[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,yn(u,e,6,Nn(e,u,i))}}function Nn(e,t,n){if(1!==n.length)return n;if(v(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&p(o,"type")&&p(o,"timeStamp")&&p(o,"target")&&p(o,"currentTarget")&&p(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function qn(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let a={},s=!1;if(!v(e)){const o=e=>{const n=qn(e,t,!0);n&&(s=!0,c(a,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||s?(f(i)?i.forEach((e=>a[e]=null)):c(a,i),_(e)&&o.set(e,a),a):(_(e)&&o.set(e,null),null)}function jn(e,t){return!(!e||!s(t))&&(t=t.slice(2).replace(/Once$/,""),p(e,t[0].toLowerCase()+t.slice(1))||p(e,I(t))||p(e,t))}let zn=null,Vn=null;function Fn(e){const t=zn;return zn=e,Vn=e&&e.type.__scopeId||null,t}function Un(e,t=zn,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&Wr(-1);const r=Fn(t);let i;try{i=e(...n)}finally{Fn(r),o._d&&Wr(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function Wn(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[a],slots:s,attrs:c,emit:u,render:d,renderCache:p,data:f,setupState:h,ctx:g,inheritAttrs:m}=e;let v,y;const b=Fn(e);try{if(4&n.shapeFlag){const e=r||o;v=ii(d.call(e,e,p,i,h,f,g)),y=c}else{const e=t;0,v=ii(e.length>1?e(i,{attrs:c,slots:s,emit:u}):e(i,null)),y=t.props?c:Hn(c)}}catch(w){zr.length=0,bn(w,e,1),v=ti(qr)}let _=v;if(y&&!1!==m){const e=Object.keys(y),{shapeFlag:t}=_;e.length&&7&t&&(a&&e.some(l)&&(y=Yn(y,a)),_=ni(_,y))}return n.dirs&&(_=ni(_),_.dirs=_.dirs?_.dirs.concat(n.dirs):n.dirs),n.transition&&(_.transition=n.transition),v=_,Fn(b),v}const Hn=e=>{let t;for(const n in e)("class"===n||"style"===n||s(n))&&((t||(t={}))[n]=e[n]);return t},Yn=(e,t)=>{const n={};for(const o in e)l(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function Xn(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!jn(n,i))return!0}return!1}const Gn=e=>e.__isSuspense;function Jn(e,t){if(pi){let n=pi.provides;const o=pi.parent&&pi.parent.provides;o===n&&(n=pi.provides=Object.create(o)),n[e]=t,"app"===pi.type.mpType&&pi.appContext.app.provide(e,t)}else;}function Qn(e,t,n=!1){const o=pi||zn;if(o){const r=null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&v(t)?t.call(o.proxy):t}}function Kn(e,t){return to(e,null,t)}const Zn={};function eo(e,t,n){return to(e,t,n)}function to(e,t,{immediate:n,deep:r,flush:a,onTrack:s,onTrigger:l}=o){const c=qe===(null==pi?void 0:pi.scope)?pi:null;let d,p,h=!1,g=!1;if(sn(e)?(d=()=>e.value,h=Kt(e)):Jt(e)?(d=()=>e,r=!0):f(e)?(g=!0,h=e.some((e=>Jt(e)||Kt(e))),d=()=>e.map((e=>sn(e)?e.value:Jt(e)?ro(e):v(e)?vn(e,c,2):void 0))):d=v(e)?t?()=>vn(e,c,2):()=>{if(!c||!c.isUnmounted)return p&&p(),yn(e,c,3,[y])}:i,t&&r){const e=d;d=()=>ro(e())}let m,y=e=>{p=x.onStop=()=>{vn(e,c,4)}};if(vi){if(y=i,t?n&&yn(t,c,3,[d(),g?[]:void 0,y]):d(),"sync"!==a)return i;{const e=ki();m=e.__watcherHandles||(e.__watcherHandles=[])}}let b=g?new Array(e.length).fill(Zn):Zn;const _=()=>{if(x.active)if(t){const e=x.run();(r||h||(g?e.some(((e,t)=>$(e,b[t]))):$(e,b)))&&(p&&p(),yn(t,c,3,[e,b===Zn?void 0:g&&b[0]===Zn?[]:b,y]),b=e)}else x.run()};let w;_.allowRecurse=!!t,"sync"===a?w=_:"post"===a?w=()=>Lr(_,c&&c.suspense):(_.pre=!0,c&&(_.id=c.uid),w=()=>Pn(_));const x=new Qe(d,w);t?n?_():b=x.run():"post"===a?Lr(x.run.bind(x),c&&c.suspense):x.run();const T=()=>{x.stop(),c&&c.scope&&u(c.scope.effects,x)};return m&&m.push(T),T}function no(e,t,n){const o=this.proxy,r=y(e)?e.includes(".")?oo(o,e):()=>o[e]:e.bind(o,o);let i;v(t)?i=t:(i=t.handler,n=t);const a=pi;hi(this);const s=to(r,i.bind(o),n);return a?hi(a):gi(),s}function oo(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function ro(e,t){if(!_(e)||e.__v_skip)return e;if((t=t||new Set).has(e))return e;if(t.add(e),sn(e))ro(e.value,t);else if(f(e))for(let n=0;n<e.length;n++)ro(e[n],t);else if(g(e)||h(e))e.forEach((e=>{ro(e,t)}));else if(S(e))for(const n in e)ro(e[n],t);return e}const io=[Function,Array],ao={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:io,onEnter:io,onAfterEnter:io,onEnterCancelled:io,onBeforeLeave:io,onLeave:io,onAfterLeave:io,onLeaveCancelled:io,onBeforeAppear:io,onAppear:io,onAfterAppear:io,onAppearCancelled:io},so={name:"BaseTransition",props:ao,setup(e,{slots:t}){const n=fi(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Ro((()=>{e.isMounted=!0})),qo((()=>{e.isUnmounting=!0})),e}();let r;return()=>{const i=t.default&&ho(t.default(),!0);if(!i||!i.length)return;let a=i[0];if(i.length>1)for(const e of i)if(e.type!==qr){a=e;break}const s=en(e),{mode:l}=s;if(o.isLeaving)return uo(a);const c=po(a);if(!c)return uo(a);const u=co(c,s,o,n);fo(c,u);const d=n.subTree,p=d&&po(d);let f=!1;const{getTransitionKey:h}=c.type;if(h){const e=h();void 0===r?r=e:e!==r&&(r=e,f=!0)}if(p&&p.type!==qr&&(!Jr(c,p)||f)){const e=co(p,s,o,n);if(fo(p,e),"out-in"===l)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&n.update()},uo(a);"in-out"===l&&c.type!==qr&&(e.delayLeave=(e,t,n)=>{lo(o,p)[String(p.key)]=p,e._leaveCb=()=>{t(),e._leaveCb=void 0,delete u.delayedLeave},u.delayedLeave=n})}return a}}};function lo(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function co(e,t,n,o){const{appear:r,mode:i,persisted:a=!1,onBeforeEnter:s,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:p,onAfterLeave:h,onLeaveCancelled:g,onBeforeAppear:m,onAppear:v,onAfterAppear:y,onAppearCancelled:b}=t,_=String(e.key),w=lo(n,e),x=(e,t)=>{e&&yn(e,o,9,t)},T=(e,t)=>{const n=t[1];x(e,t),f(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},S={mode:i,persisted:a,beforeEnter(t){let o=s;if(!n.isMounted){if(!r)return;o=m||s}t._leaveCb&&t._leaveCb(!0);const i=w[_];i&&Jr(e,i)&&i.el._leaveCb&&i.el._leaveCb(),x(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=v||l,o=y||c,i=b||u}let a=!1;const s=e._enterCb=t=>{a||(a=!0,x(t?i:o,[e]),S.delayedLeave&&S.delayedLeave(),e._enterCb=void 0)};t?T(t,[e,s]):s()},leave(t,o){const r=String(e.key);if(t._enterCb&&t._enterCb(!0),n.isUnmounting)return o();x(d,[t]);let i=!1;const a=t._leaveCb=n=>{i||(i=!0,o(),x(n?g:h,[t]),t._leaveCb=void 0,w[r]===e&&delete w[r])};w[r]=e,p?T(p,[t,a]):a()},clone:e=>co(e,t,n,o)};return S}function uo(e){if(bo(e))return(e=ni(e)).children=null,e}function po(e){return bo(e)?e.children?e.children[0]:void 0:e}function fo(e,t){6&e.shapeFlag&&e.component?fo(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function ho(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let a=e[i];const s=null==n?a.key:String(n)+String(null!=a.key?a.key:i);a.type===Br?(128&a.patchFlag&&r++,o=o.concat(ho(a.children,t,s))):(t||a.type!==qr)&&o.push(null!=s?ni(a,{key:s}):a)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}function go(e){return v(e)?{setup:e,name:e.name}:e}const mo=e=>!!e.type.__asyncLoader;function vo(e){v(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:a=!0,onError:s}=e;let l,c=null,u=0;const d=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),s)return new Promise(((t,n)=>{s(e,(()=>t((u++,c=null,d()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return go({name:"AsyncComponentWrapper",__asyncLoader:d,get __asyncResolved(){return l},setup(){const e=pi;if(l)return()=>yo(l,e);const t=t=>{c=null,bn(t,e,13,!o)};if(a&&e.suspense||vi)return d().then((t=>()=>yo(t,e))).catch((e=>(t(e),()=>o?ti(o,{error:e}):null)));const s=ln(!1),u=ln(),p=ln(!!r);return r&&setTimeout((()=>{p.value=!1}),r),null!=i&&setTimeout((()=>{if(!s.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),d().then((()=>{s.value=!0,e.parent&&bo(e.parent.vnode)&&Pn(e.parent.update)})).catch((e=>{t(e),u.value=e})),()=>s.value&&l?yo(l,e):u.value&&o?ti(o,{error:u.value}):n&&!p.value?ti(n):void 0}})}function yo(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,a=ti(e,o,r);return a.ref=n,a.ce=i,delete t.vnode.ce,a}const bo=e=>e.type.__isKeepAlive;class _o{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,r=t.get(e);if(r)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return r}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const wo={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=fi(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=e.cache||new _o(e.max);r.pruneCacheEntry=a;let i=null;function a(t){var o;!i||!Jr(t,i)||"key"===e.matchBy&&t.key!==i.key?(Ao(o=t),u(o,n,s,!0)):i&&Ao(i)}const s=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:d}}}=o,p=d("div");function f(t){r.forEach(((n,o)=>{const i=Po(n,e.matchBy);!i||t&&t(i)||(r.delete(o),a(n))}))}o.activate=(e,t,n,o,r)=>{const i=e.component;if(i.ba){const e=i.isDeactivated;i.isDeactivated=!1,D(i.ba),i.isDeactivated=e}c(e,t,n,0,s),l(i.vnode,e,t,n,i,s,o,e.slotScopeIds,r),Lr((()=>{i.isDeactivated=!1,i.a&&D(i.a);const t=e.props&&e.props.onVnodeMounted;t&&ci(t,i.parent,e)}),s)},o.deactivate=e=>{const t=e.component;t.bda&&Io(t.bda),c(e,p,null,1,s),Lr((()=>{t.bda&&Oo(t.bda),t.da&&D(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&ci(n,t.parent,e),t.isDeactivated=!0}),s)},eo((()=>[e.include,e.exclude,e.matchBy]),(([e,t])=>{e&&f((t=>To(e,t))),t&&f((e=>!To(t,e)))}),{flush:"post",deep:!0});let h=null;const g=()=>{null!=h&&r.set(h,Mo(n.subTree))};return Ro(g),No(g),qo((()=>{r.forEach(((t,o)=>{r.delete(o),a(t);const{subTree:i,suspense:s}=n,l=Mo(i);if(t.type!==l.type||"key"===e.matchBy&&t.key!==l.key);else{l.component.bda&&D(l.component.bda),Ao(l);const e=l.component.da;e&&Lr(e,s)}}))})),()=>{if(h=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return i=null,n;if(!Gr(o)||!(4&o.shapeFlag)&&!Gn(o.type))return i=null,o;let a=Mo(o);const s=a.type,l=Po(a,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!To(c,l))||u&&l&&To(u,l))return i=a,o;const d=null==a.key?s:a.key,p=r.get(d);return a.el&&(a=ni(a),Gn(o.type)&&(o.ssContent=a)),h=d,p&&(a.el=p.el,a.component=p.component,a.transition&&fo(a,a.transition),a.shapeFlag|=512),a.shapeFlag|=256,i=a,Gn(o.type)?o:a}}},xo=wo;function To(e,t){return f(e)?e.some((e=>To(e,t))):y(e)?e.split(",").includes(t):!!e.test&&e.test(t)}function So(e,t){Co(e,"a",t)}function ko(e,t){Co(e,"da",t)}function Co(e,t,n=pi){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,Lo(t,o,n),n){let e=n.parent;for(;e&&e.parent;)bo(e.parent.vnode)&&Eo(o,t,n,e),e=e.parent}}function Eo(e,t,n,o){const r=Lo(t,e,o,!0);jo((()=>{u(o[t],r)}),n)}function Ao(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Mo(e){return Gn(e.type)?e.ssContent:e}function Po(e,t){if("name"===t){const t=e.type;return wi(mo(e)?t.__asyncResolved||{}:t)}return String(e.key)}function Io(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function Oo(e){e.forEach((e=>e.__called=!1))}function Lo(e,t,n=pi,o=!1){if(n){if(r=e,Me.indexOf(r)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return Pe.indexOf(e)>-1}(e))){const o=n.proxy;yn(t.bind(o),n,e,"onLoad"===e?[o.$page.options]:[])}}const i=n[e]||(n[e]=[]),a=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;tt(),hi(n);const r=yn(t,n,e,o);return gi(),nt(),r});return o?i.unshift(a):i.push(a),a}var r}const $o=e=>(t,n=pi)=>(!vi||"sp"===e)&&Lo(e,((...e)=>t(...e)),n),Do=$o("bm"),Ro=$o("m"),Bo=$o("bu"),No=$o("u"),qo=$o("bum"),jo=$o("um"),zo=$o("sp"),Vo=$o("rtg"),Fo=$o("rtc");function Uo(e,t=pi){Lo("ec",e,t)}function Wo(e,t){const n=zn;if(null===n)return e;const r=_i(n)||n.proxy,i=e.dirs||(e.dirs=[]);for(let a=0;a<t.length;a++){let[e,n,s,l=o]=t[a];e&&(v(e)&&(e={mounted:e,updated:e}),e.deep&&ro(n),i.push({dir:e,instance:r,value:n,oldValue:void 0,arg:s,modifiers:l}))}return e}function Ho(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let a=0;a<r.length;a++){const s=r[a];i&&(s.oldValue=i[a].value);let l=s.dir[o];l&&(tt(),yn(l,n,8,[e.el,s,e,t]),nt())}}function Yo(e,t){return Jo("components",e,!0,t)||e}const Xo=Symbol();function Go(e){return y(e)?Jo("components",e,!1)||e:e||Xo}function Jo(e,t,n=!0,o=!1){const r=zn||pi;if(r){const n=r.type;if("components"===e){const e=wi(n,!1);if(e&&(e===t||e===M(t)||e===O(M(t))))return n}const i=Qo(r[e]||n[e],t)||Qo(r.appContext[e],t);return!i&&o?n:i}}function Qo(e,t){return e&&(e[t]||e[M(t)]||e[O(M(t))])}function Ko(e,t,n,o){let r;const i=n&&n[o];if(f(e)||y(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i&&i[n])}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i&&i[n])}else if(_(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,a=n.length;o<a;o++){const a=n[o];r[o]=t(e[a],a,o,i&&i[o])}}else r=[];return n&&(n[o]=r),r}function Zo(e,t,n={},o,r){if(zn.isCE||zn.parent&&mo(zn.parent)&&zn.parent.isCE)return"default"!==t&&(n.name=t),ti("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),Fr();const a=i&&er(i(n)),s=Xr(Br,{key:n.key||a&&a.key||`_${t}`},a||(o?o():[]),a&&1===e._?64:-2);return!r&&s.scopeId&&(s.slotScopeIds=[s.scopeId+"-s"]),i&&i._c&&(i._d=!0),s}function er(e){return e.some((e=>!Gr(e)||e.type!==qr&&!(e.type===Br&&!er(e.children))))?e:null}const tr=e=>{if(!e)return null;if(mi(e)){return _i(e)||e.proxy}return tr(e.parent)},nr=c(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>tr(e.parent),$root:e=>tr(e.root),$emit:e=>e.emit,$options:e=>cr(e),$forceUpdate:e=>e.f||(e.f=()=>{Pn(e.update)}),$nextTick:e=>e.n||(e.n=Mn.bind(e.proxy)),$watch:e=>no.bind(e)}),or=(e,t)=>e!==o&&!e.__isScriptSetup&&p(e,t),rr={get({_:e},t){const{ctx:n,setupState:r,data:i,props:a,accessCache:s,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=s[t];if(void 0!==l)switch(l){case 1:return r[t];case 2:return i[t];case 4:return n[t];case 3:return a[t]}else{if(or(r,t))return s[t]=1,r[t];if(i!==o&&p(i,t))return s[t]=2,i[t];if((u=e.propsOptions[0])&&p(u,t))return s[t]=3,a[t];if(n!==o&&p(n,t))return s[t]=4,n[t];ir&&(s[t]=0)}}const d=nr[t];let f,h;return d?("$attrs"===t&&ot(e,0,t),d(e)):(f=l.__cssModules)&&(f=f[t])?f:n!==o&&p(n,t)?(s[t]=4,n[t]):(h=c.config.globalProperties,p(h,t)?h[t]:void 0)},set({_:e},t,n){const{data:r,setupState:i,ctx:a}=e;return or(i,t)?(i[t]=n,!0):r!==o&&p(r,t)?(r[t]=n,!0):!p(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(a[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:i,propsOptions:a}},s){let l;return!!n[s]||e!==o&&p(e,s)||or(t,s)||(l=a[0])&&p(l,s)||p(r,s)||p(nr,s)||p(i.config.globalProperties,s)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:p(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};let ir=!0;function ar(e){const t=cr(e),n=e.proxy,o=e.ctx;ir=!1,t.beforeCreate&&sr(t.beforeCreate,e,"bc");const{data:r,computed:a,methods:s,watch:l,provide:c,inject:u,created:d,beforeMount:p,mounted:h,beforeUpdate:g,updated:m,activated:y,deactivated:b,beforeDestroy:w,beforeUnmount:x,destroyed:T,unmounted:S,render:k,renderTracked:C,renderTriggered:E,errorCaptured:A,serverPrefetch:M,expose:P,inheritAttrs:I,components:O,directives:L,filters:$}=t;if(u&&function(e,t,n=i,o=!1){f(e)&&(e=fr(e));for(const r in e){const n=e[r];let i;i=_(n)?"default"in n?Qn(n.from||r,n.default,!0):Qn(n.from||r):Qn(n),sn(i)&&o?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e}):t[r]=i}}(u,o,null,e.appContext.config.unwrapInjectedRef),s)for(const i in s){const e=s[i];v(e)&&(o[i]=e.bind(n))}if(r){const t=r.call(n,n);_(t)&&(e.data=Yt(t))}if(ir=!0,a)for(const f in a){const e=a[f],t=v(e)?e.bind(n,n):v(e.get)?e.get.bind(n,n):i,r=!v(e)&&v(e.set)?e.set.bind(n):i,s=xi({get:t,set:r});Object.defineProperty(o,f,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(l)for(const i in l)lr(l[i],o,n,i);if(c){const e=v(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{Jn(t,e[t])}))}function D(e,t){f(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&sr(d,e,"c"),D(Do,p),D(Ro,h),D(Bo,g),D(No,m),D(So,y),D(ko,b),D(Uo,A),D(Fo,C),D(Vo,E),D(qo,x),D(jo,S),D(zo,M),f(P))if(P.length){const t=e.exposed||(e.exposed={});P.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});k&&e.render===i&&(e.render=k),null!=I&&(e.inheritAttrs=I),O&&(e.components=O),L&&(e.directives=L);const R=e.appContext.config.globalProperties.$applyOptions;R&&R(t,e,n)}function sr(e,t,n){yn(f(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function lr(e,t,n,o){const r=o.includes(".")?oo(n,o):()=>n[o];if(y(e)){const n=t[e];v(n)&&eo(r,n)}else if(v(e))eo(r,e.bind(n));else if(_(e))if(f(e))e.forEach((e=>lr(e,t,n,o)));else{const o=v(e.handler)?e.handler.bind(n):t[e.handler];v(o)&&eo(r,o,e)}}function cr(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,s=i.get(t);let l;return s?l=s:r.length||n||o?(l={},r.length&&r.forEach((e=>ur(l,e,a,!0))),ur(l,t,a)):l=t,_(t)&&i.set(t,l),l}function ur(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&ur(e,i,n,!0),r&&r.forEach((t=>ur(e,t,n,!0)));for(const a in t)if(o&&"expose"===a);else{const o=dr[a]||n&&n[a];e[a]=o?o(e[a],t[a]):t[a]}return e}const dr={data:pr,props:gr,emits:gr,methods:gr,computed:gr,beforeCreate:hr,created:hr,beforeMount:hr,mounted:hr,beforeUpdate:hr,updated:hr,beforeDestroy:hr,beforeUnmount:hr,destroyed:hr,unmounted:hr,activated:hr,deactivated:hr,errorCaptured:hr,serverPrefetch:hr,components:gr,directives:gr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=c(Object.create(null),e);for(const o in t)n[o]=hr(e[o],t[o]);return n},provide:pr,inject:function(e,t){return gr(fr(e),fr(t))}};function pr(e,t){return t?e?function(){return c(v(e)?e.call(this,this):e,v(t)?t.call(this,this):t)}:t:e}function fr(e){if(f(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function hr(e,t){return e?[...new Set([].concat(e,t))]:t}function gr(e,t){return e?c(c(Object.create(null),e),t):t}function mr(e,t,n,o=!1){const r={},i={};R(i,Qr,1),e.propsDefaults=Object.create(null),vr(e,t,r,i);for(const a in e.propsOptions[0])a in r||(r[a]=void 0);n?e.props=o?r:Gt(r,!1,_t,jt,Ft):e.type.props?e.props=r:e.props=i,e.attrs=i}function vr(e,t,n,r){const[i,a]=e.propsOptions;let s,l=!1;if(t)for(let o in t){if(C(o))continue;const c=t[o];let u;i&&p(i,u=M(o))?a&&a.includes(u)?(s||(s={}))[u]=c:n[u]=c:jn(e.emitsOptions,o)||o in r&&c===r[o]||(r[o]=c,l=!0)}if(a){const t=en(n),r=s||o;for(let o=0;o<a.length;o++){const s=a[o];n[s]=yr(i,t,s,r[s],e,!p(r,s))}}return l}function yr(e,t,n,o,r,i){const a=e[n];if(null!=a){const e=p(a,"default");if(e&&void 0===o){const e=a.default;if(a.type!==Function&&v(e)){const{propsDefaults:i}=r;n in i?o=i[n]:(hi(r),o=i[n]=e.call(null,t),gi())}else o=e}a[0]&&(i&&!e?o=!1:!a[1]||""!==o&&o!==I(n)||(o=!0))}return o}function br(e,t,n=!1){const i=t.propsCache,a=i.get(e);if(a)return a;const s=e.props,l={},u=[];let d=!1;if(!v(e)){const o=e=>{d=!0;const[n,o]=br(e,t,!0);c(l,n),o&&u.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!s&&!d)return _(e)&&i.set(e,r),r;if(f(s))for(let r=0;r<s.length;r++){const e=M(s[r]);_r(e)&&(l[e]=o)}else if(s)for(const o in s){const e=M(o);if(_r(e)){const t=s[o],n=l[e]=f(t)||v(t)?{type:t}:Object.assign({},t);if(n){const t=Tr(Boolean,n.type),o=Tr(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||p(n,"default"))&&u.push(e)}}}const h=[l,u];return _(e)&&i.set(e,h),h}function _r(e){return"$"!==e[0]}function wr(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function xr(e,t){return wr(e)===wr(t)}function Tr(e,t){return f(t)?t.findIndex((t=>xr(t,e))):v(t)&&xr(t,e)?0:-1}const Sr=e=>"_"===e[0]||"$stable"===e,kr=e=>f(e)?e.map(ii):[ii(e)],Cr=(e,t,n)=>{if(t._n)return t;const o=Un(((...e)=>kr(t(...e))),n);return o._c=!1,o},Er=(e,t,n)=>{const o=e._ctx;for(const r in e){if(Sr(r))continue;const n=e[r];if(v(n))t[r]=Cr(0,n,o);else if(null!=n){const e=kr(n);t[r]=()=>e}}},Ar=(e,t)=>{const n=kr(t);e.slots.default=()=>n};function Mr(){return{app:null,config:{isNativeTag:a,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Pr=0;function Ir(e,t){return function(n,o=null){v(n)||(n=Object.assign({},n)),null==o||_(o)||(o=null);const r=Mr(),i=new Set;let a=!1;const s=r.app={_uid:Pr++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:Ci,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&v(e.install)?(i.add(e),e.install(s,...t)):v(e)&&(i.add(e),e(s,...t))),s),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),s),component:(e,t)=>t?(r.components[e]=t,s):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,s):r.directives[e],mount(i,l,c){if(!a){const u=ti(n,o);return u.appContext=r,l&&t?t(u,i):e(u,i,c),a=!0,s._container=i,i.__vue_app__=s,s._instance=u.component,_i(u.component)||u.component.proxy}},unmount(){a&&(e(null,s._container),delete s._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,s)};return s}}function Or(e,t,n,r,i=!1){if(f(e))return void e.forEach(((e,o)=>Or(e,t&&(f(t)?t[o]:t),n,r,i)));if(mo(r)&&!i)return;const a=4&r.shapeFlag?_i(r.component)||r.component.proxy:r.el,s=i?null:a,{i:l,r:c}=e,d=t&&t.r,h=l.refs===o?l.refs={}:l.refs,g=l.setupState;if(null!=d&&d!==c&&(y(d)?(h[d]=null,p(g,d)&&(g[d]=null)):sn(d)&&(d.value=null)),v(c))vn(c,l,12,[s,h]);else{const t=y(c),o=sn(c);if(t||o){const r=()=>{if(e.f){const n=t?p(g,c)?g[c]:h[c]:c.value;i?f(n)&&u(n,a):f(n)?n.includes(a)||n.push(a):t?(h[c]=[a],p(g,c)&&(g[c]=h[c])):(c.value=[a],e.k&&(h[e.k]=c.value))}else t?(h[c]=s,p(g,c)&&(g[c]=s)):o&&(c.value=s,e.k&&(h[e.k]=s))};s?(r.id=-1,Lr(r,n)):r()}}}const Lr=function(e,t){var n;t&&t.pendingBranch?f(e)?t.effects.push(...e):t.effects.push(e):(f(n=e)?Sn.push(...n):kn&&kn.includes(n,n.allowRecurse?Cn+1:Cn)||Sn.push(n),In())};function $r(e){return function(e,t){(N||(N="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})).__VUE__=!0;const{insert:n,remove:a,patchProp:s,forcePatchProp:l,createElement:u,createText:d,createComment:f,setText:h,setElementText:g,parentNode:m,nextSibling:v,setScopeId:y=i,insertStaticContent:b}=e,_=(e,t,n,o=null,r=null,i=null,a=!1,s=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!Jr(e,t)&&(o=te(e),J(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case Nr:x(e,t,n,o);break;case qr:T(e,t,n,o);break;case jr:null==e&&S(t,n,o,a);break;case Br:j(e,t,n,o,r,i,a,s,l);break;default:1&d?A(e,t,n,o,r,i,a,s,l):6&d?z(e,t,n,o,r,i,a,s,l):(64&d||128&d)&&c.process(e,t,n,o,r,i,a,s,l,oe)}null!=u&&r&&Or(u,e&&e.ref,i,t||e,!t)},x=(e,t,o,r)=>{if(null==e)n(t.el=d(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&h(n,t.children)}},T=(e,t,o,r)=>{null==e?n(t.el=f(t.children||""),o,r):t.el=e.el},S=(e,t,n,o)=>{[e.el,e.anchor]=b(e.children,t,n,o,e.el,e.anchor)},k=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=v(e),n(e,o,r),e=i;n(t,o,r)},E=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=v(e),a(e),e=n;a(t)},A=(e,t,n,o,r,i,a,s,l)=>{a=a||"svg"===t.type,null==e?P(t,n,o,r,i,a,s,l):$(e,t,r,i,a,s,l)},P=(e,t,o,r,i,a,l,c)=>{let d,p;const{type:f,props:h,shapeFlag:m,transition:v,dirs:y}=e;if(d=e.el=u(e.type,a,h&&h.is,h),8&m?g(d,e.children):16&m&&L(e.children,d,null,r,i,a&&"foreignObject"!==f,l,c),y&&Ho(e,null,r,"created"),O(d,e,e.scopeId,l,r),h){for(const t in h)"value"===t||C(t)||s(d,t,null,h[t],a,e.children,r,i,ee);"value"in h&&s(d,"value",null,h.value),(p=h.onVnodeBeforeMount)&&ci(p,r,e)}Object.defineProperty(d,"__vueParentComponent",{value:r,enumerable:!1}),y&&Ho(e,null,r,"beforeMount");const b=(!i||i&&!i.pendingBranch)&&v&&!v.persisted;b&&v.beforeEnter(d),n(d,t,o),((p=h&&h.onVnodeMounted)||b||y)&&Lr((()=>{p&&ci(p,r,e),b&&v.enter(d),y&&Ho(e,null,r,"mounted")}),i)},O=(e,t,n,o,r)=>{if(n&&y(e,n),o)for(let i=0;i<o.length;i++)y(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;O(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},L=(e,t,n,o,r,i,a,s,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=s?ai(e[c]):ii(e[c]);_(null,l,t,n,o,r,i,a,s)}},$=(e,t,n,r,i,a,c)=>{const u=t.el=e.el;let{patchFlag:d,dynamicChildren:p,dirs:f}=t;d|=16&e.patchFlag;const h=e.props||o,m=t.props||o;let v;n&&Dr(n,!1),(v=m.onVnodeBeforeUpdate)&&ci(v,n,t,e),f&&Ho(t,e,n,"beforeUpdate"),n&&Dr(n,!0);const y=i&&"foreignObject"!==t.type;if(p?B(e.dynamicChildren,p,u,n,r,y,a):c||H(e,t,u,null,n,r,y,a,!1),d>0){if(16&d)q(u,t,h,m,n,r,i);else if(2&d&&h.class!==m.class&&s(u,"class",null,m.class,i),4&d&&s(u,"style",h.style,m.style,i),8&d){const o=t.dynamicProps;for(let t=0;t<o.length;t++){const a=o[t],c=h[a],d=m[a];(d!==c||"value"===a||l&&l(u,a))&&s(u,a,c,d,i,e.children,n,r,ee)}}1&d&&e.children!==t.children&&g(u,t.children)}else c||null!=p||q(u,t,h,m,n,r,i);((v=m.onVnodeUpdated)||f)&&Lr((()=>{v&&ci(v,n,t,e),f&&Ho(t,e,n,"updated")}),r)},B=(e,t,n,o,r,i,a)=>{for(let s=0;s<t.length;s++){const l=e[s],c=t[s],u=l.el&&(l.type===Br||!Jr(l,c)||70&l.shapeFlag)?m(l.el):n;_(l,c,u,null,o,r,i,a,!0)}},q=(e,t,n,r,i,a,c)=>{if(n!==r){if(n!==o)for(const o in n)C(o)||o in r||s(e,o,n[o],null,c,t.children,i,a,ee);for(const o in r){if(C(o))continue;const u=r[o],d=n[o];(u!==d&&"value"!==o||l&&l(e,o))&&s(e,o,d,u,c,t.children,i,a,ee)}"value"in r&&s(e,"value",n.value,r.value)}},j=(e,t,o,r,i,a,s,l,c)=>{const u=t.el=e?e.el:d(""),p=t.anchor=e?e.anchor:d("");let{patchFlag:f,dynamicChildren:h,slotScopeIds:g}=t;g&&(l=l?l.concat(g):g),null==e?(n(u,o,r),n(p,o,r),L(t.children||[],o,p,i,a,s,l,c)):f>0&&64&f&&h&&e.dynamicChildren?(B(e.dynamicChildren,h,o,i,a,s,l),(null!=t.key||i&&t===i.subTree)&&Rr(e,t,!0)):H(e,t,o,p,i,a,s,l,c)},z=(e,t,n,o,r,i,a,s,l)=>{t.slotScopeIds=s,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,a,l):V(t,n,o,r,i,a,l):F(e,t,l)},V=(e,t,n,r,i,a,s)=>{const l=e.component=function(e,t,n){const r=e.type,i=(t?t.appContext:e.appContext)||ui,a={uid:di++,vnode:e,type:r,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,scope:new je(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:br(r,i),emitsOptions:qn(r,i),emit:null,emitted:null,propsDefaults:o,inheritAttrs:r.inheritAttrs,ctx:o,data:o,props:o,attrs:o,slots:o,refs:o,setupState:o,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};a.ctx={_:a},a.root=t?t.root:a,a.emit=Bn.bind(null,a),a.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(a);return a}(e,r,i);if(bo(e)&&(l.ctx.renderer=oe),function(e,t=!1){vi=t;const{props:n,children:o}=e.vnode,r=mi(e);mr(e,n,r,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=en(t),R(t,"_",n)):Er(t,e.slots={})}else e.slots={},t&&Ar(e,t);R(e.slots,Qr,1)})(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=tn(new Proxy(e.ctx,rr));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};let n;return{get attrs(){return n||(n=function(e){return new Proxy(e.attrs,{get:(t,n)=>(ot(e,0,"$attrs"),t[n])})}(e))},slots:e.slots,emit:e.emit,expose:t}}(e):null;hi(e),tt();const r=vn(o,e,0,[e.props,n]);if(nt(),gi(),w(r)){if(r.then(gi,gi),t)return r.then((n=>{yi(e,n,t)})).catch((t=>{bn(t,e,0)}));e.asyncDep=r}else yi(e,r,t)}else bi(e,t)}(e,t):void 0;vi=!1}(l),l.asyncDep){if(i&&i.registerDep(l,U),!e.el){const e=l.subTree=ti(qr);T(null,e,t,n)}}else U(l,e,t,n,i,a,s)},F=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:a,children:s,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!s||s&&s.$stable)||o!==a&&(o?!a||Xn(o,a,c):!!a);if(1024&l)return!0;if(16&l)return o?Xn(o,a,c):!!a;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(a[n]!==o[n]&&!jn(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void W(o,t,n);o.next=t,function(e){const t=xn.indexOf(e);t>Tn&&xn.splice(t,1)}(o.update),o.update()}else t.el=e.el,o.vnode=t},U=(e,t,n,o,r,i,a)=>{const s=()=>{if(e.isMounted){let t,{next:n,bu:o,u:s,parent:l,vnode:c}=e,u=n;Dr(e,!1),n?(n.el=c.el,W(e,n,a)):n=c,o&&D(o),(t=n.props&&n.props.onVnodeBeforeUpdate)&&ci(t,l,n,c),Dr(e,!0);const d=Wn(e),p=e.subTree;e.subTree=d,_(p,d,m(p.el),te(p),e,r,i),n.el=d.el,null===u&&function({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}(e,d.el),s&&Lr(s,r),(t=n.props&&n.props.onVnodeUpdated)&&Lr((()=>ci(t,l,n,c)),r)}else{let a;const{el:s,props:l}=t,{bm:c,m:u,parent:d}=e,p=mo(t);if(Dr(e,!1),c&&D(c),!p&&(a=l&&l.onVnodeBeforeMount)&&ci(a,d,t),Dr(e,!0),s&&ie){const n=()=>{e.subTree=Wn(e),ie(s,e.subTree,e,r,null)};p?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const a=e.subTree=Wn(e);_(null,a,n,o,e,r,i),t.el=a.el}if(u&&Lr(u,r),!p&&(a=l&&l.onVnodeMounted)){const e=t;Lr((()=>ci(a,d,e)),r)}const{ba:f,a:h}=e;(256&t.shapeFlag||d&&mo(d.vnode)&&256&d.vnode.shapeFlag)&&(f&&Io(f),h&&Lr(h,r),f&&Lr((()=>Oo(f)),r)),e.isMounted=!0,t=n=o=null}},l=e.effect=new Qe(s,(()=>Pn(c)),e.scope),c=e.update=()=>l.run();c.id=e.uid,Dr(e,!0),c()},W=(e,t,n)=>{t.component=e;const r=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:a}}=e,s=en(r),[l]=e.propsOptions;let c=!1;if(!(o||a>0)||16&a){let o;vr(e,t,r,i)&&(c=!0);for(const i in s)t&&(p(t,i)||(o=I(i))!==i&&p(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=yr(l,s,i,void 0,e,!0)):delete r[i]);if(i!==s)for(const e in i)t&&p(t,e)||(delete i[e],c=!0)}else if(8&a){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let a=n[o];if(jn(e.emitsOptions,a))continue;const u=t[a];if(l)if(p(i,a))u!==i[a]&&(i[a]=u,c=!0);else{const t=M(a);r[t]=yr(l,s,t,u,e,!1)}else u!==i[a]&&(i[a]=u,c=!0)}}c&&it(e,"set","$attrs")}(e,t.props,r,n),((e,t,n)=>{const{vnode:r,slots:i}=e;let a=!0,s=o;if(32&r.shapeFlag){const e=t._;e?n&&1===e?a=!1:(c(i,t),n||1!==e||delete i._):(a=!t.$stable,Er(t,i)),s=t}else t&&(Ar(e,t),s={default:1});if(a)for(const o in i)Sr(o)||o in s||delete i[o]})(e,t.children,n),tt(),On(),nt()},H=(e,t,n,o,r,i,a,s,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:p,shapeFlag:f}=t;if(p>0){if(128&p)return void X(c,d,n,o,r,i,a,s,l);if(256&p)return void Y(c,d,n,o,r,i,a,s,l)}8&f?(16&u&&ee(c,r,i),d!==c&&g(n,d)):16&u?16&f?X(c,d,n,o,r,i,a,s,l):ee(c,r,i,!0):(8&u&&g(n,""),16&f&&L(d,n,o,r,i,a,s,l))},Y=(e,t,n,o,i,a,s,l,c)=>{t=t||r;const u=(e=e||r).length,d=t.length,p=Math.min(u,d);let f;for(f=0;f<p;f++){const o=t[f]=c?ai(t[f]):ii(t[f]);_(e[f],o,n,null,i,a,s,l,c)}u>d?ee(e,i,a,!0,!1,p):L(t,n,o,i,a,s,l,c,p)},X=(e,t,n,o,i,a,s,l,c)=>{let u=0;const d=t.length;let p=e.length-1,f=d-1;for(;u<=p&&u<=f;){const o=e[u],r=t[u]=c?ai(t[u]):ii(t[u]);if(!Jr(o,r))break;_(o,r,n,null,i,a,s,l,c),u++}for(;u<=p&&u<=f;){const o=e[p],r=t[f]=c?ai(t[f]):ii(t[f]);if(!Jr(o,r))break;_(o,r,n,null,i,a,s,l,c),p--,f--}if(u>p){if(u<=f){const e=f+1,r=e<d?t[e].el:o;for(;u<=f;)_(null,t[u]=c?ai(t[u]):ii(t[u]),n,r,i,a,s,l,c),u++}}else if(u>f)for(;u<=p;)J(e[u],i,a,!0),u++;else{const h=u,g=u,m=new Map;for(u=g;u<=f;u++){const e=t[u]=c?ai(t[u]):ii(t[u]);null!=e.key&&m.set(e.key,u)}let v,y=0;const b=f-g+1;let w=!1,x=0;const T=new Array(b);for(u=0;u<b;u++)T[u]=0;for(u=h;u<=p;u++){const o=e[u];if(y>=b){J(o,i,a,!0);continue}let r;if(null!=o.key)r=m.get(o.key);else for(v=g;v<=f;v++)if(0===T[v-g]&&Jr(o,t[v])){r=v;break}void 0===r?J(o,i,a,!0):(T[r-g]=u+1,r>=x?x=r:w=!0,_(o,t[r],n,null,i,a,s,l,c),y++)}const S=w?function(e){const t=e.slice(),n=[0];let o,r,i,a,s;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,a=n.length-1;i<a;)s=i+a>>1,e[n[s]]<l?i=s+1:a=s;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,a=n[i-1];for(;i-- >0;)n[i]=a,a=t[a];return n}(T):r;for(v=S.length-1,u=b-1;u>=0;u--){const e=g+u,r=t[e],p=e+1<d?t[e+1].el:o;0===T[u]?_(null,r,n,p,i,a,s,l,c):w&&(v<0||u!==S[v]?G(r,n,p,2):v--)}}},G=(e,t,o,r,i=null)=>{const{el:a,type:s,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void G(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void s.move(e,t,o,oe);if(s===Br){n(a,t,o);for(let e=0;e<c.length;e++)G(c[e],t,o,r);return void n(e.anchor,t,o)}if(s===jr)return void k(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(a),n(a,t,o),Lr((()=>l.enter(a)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,s=()=>n(a,t,o),c=()=>{e(a,(()=>{s(),i&&i()}))};r?r(a,s,c):c()}else n(a,t,o)},J=(e,t,n,o=!1,r=!1)=>{const{type:i,props:a,ref:s,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:p}=e;if(null!=s&&Or(s,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const f=1&u&&p,h=!mo(e);let g;if(h&&(g=a&&a.onVnodeBeforeUnmount)&&ci(g,t,e),6&u)Z(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);f&&Ho(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,oe,o):c&&(i!==Br||d>0&&64&d)?ee(c,t,n,!1,!0):(i===Br&&384&d||!r&&16&u)&&ee(l,t,n),o&&Q(e)}(h&&(g=a&&a.onVnodeUnmounted)||f)&&Lr((()=>{g&&ci(g,t,e),f&&Ho(e,null,t,"unmounted")}),n)},Q=e=>{const{type:t,el:n,anchor:o,transition:r}=e;if(t===Br)return void K(n,o);if(t===jr)return void E(e);const i=()=>{a(n),r&&!r.persisted&&r.afterLeave&&r.afterLeave()};if(1&e.shapeFlag&&r&&!r.persisted){const{leave:t,delayLeave:o}=r,a=()=>t(n,i);o?o(e.el,i,a):a()}else i()},K=(e,t)=>{let n;for(;e!==t;)n=v(e),a(e),e=n;a(t)},Z=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:a,um:s}=e;o&&D(o),r.stop(),i&&(i.active=!1,J(a,e,t,n)),s&&Lr(s,t),Lr((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,r=!1,i=0)=>{for(let a=i;a<e.length;a++)J(e[a],t,n,o,r)},te=e=>6&e.shapeFlag?te(e.component.subTree):128&e.shapeFlag?e.suspense.next():v(e.anchor||e.el),ne=(e,t,n)=>{null==e?t._vnode&&J(t._vnode,null,null,!0):_(t._vnode||null,e,t,null,null,null,n),On(),Ln(),t._vnode=e},oe={p:_,um:J,m:G,r:Q,mt:V,mc:L,pc:H,pbc:B,n:te,o:e};let re,ie;t&&([re,ie]=t(oe));return{render:ne,hydrate:re,createApp:Ir(ne,re)}}(e)}function Dr({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Rr(e,t,n=!1){const o=e.children,r=t.children;if(f(o)&&f(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=ai(r[i]),t.el=e.el),n||Rr(e,t)),t.type===Nr&&(t.el=e.el)}}const Br=Symbol(void 0),Nr=Symbol(void 0),qr=Symbol(void 0),jr=Symbol(void 0),zr=[];let Vr=null;function Fr(e=!1){zr.push(Vr=e?null:[])}let Ur=1;function Wr(e){Ur+=e}function Hr(e){return e.dynamicChildren=Ur>0?Vr||r:null,zr.pop(),Vr=zr[zr.length-1]||null,Ur>0&&Vr&&Vr.push(e),e}function Yr(e,t,n,o,r,i){return Hr(ei(e,t,n,o,r,i,!0))}function Xr(e,t,n,o,r){return Hr(ti(e,t,n,o,r,!0))}function Gr(e){return!!e&&!0===e.__v_isVNode}function Jr(e,t){return e.type===t.type&&e.key===t.key}const Qr="__vInternal",Kr=({key:e})=>null!=e?e:null,Zr=({ref:e,ref_key:t,ref_for:n})=>null!=e?y(e)||sn(e)||v(e)?{i:zn,r:e,k:t,f:!!n}:e:null;function ei(e,t=null,n=null,o=0,r=null,i=(e===Br?0:1),a=!1,s=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Kr(t),ref:t&&Zr(t),scopeId:Vn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:zn};return s?(si(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=y(n)?8:16),Ur>0&&!a&&Vr&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&Vr.push(l),l}const ti=function(e,t=null,n=null,o=0,r=null,i=!1){e&&e!==Xo||(e=qr);if(Gr(e)){const o=ni(e,t,!0);return n&&si(o,n),Ur>0&&!i&&Vr&&(6&o.shapeFlag?Vr[Vr.indexOf(e)]=o:Vr.push(o)),o.patchFlag|=-2,o}a=e,v(a)&&"__vccOpts"in a&&(e=e.__vccOpts);var a;if(t){t=function(e){return e?Zt(e)||Qr in e?c({},e):e:null}(t);let{class:e,style:n}=t;e&&!y(e)&&(t.class=ce(e)),_(n)&&(Zt(n)&&!f(n)&&(n=c({},n)),t.style=le(n))}const s=y(e)?1:Gn(e)?128:(e=>e.__isTeleport)(e)?64:_(e)?4:v(e)?2:0;return ei(e,t,n,o,r,s,i,!0)};function ni(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:a}=e,s=t?li(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:s,key:s&&Kr(s),ref:t&&t.ref?n&&r?f(r)?r.concat(Zr(t)):[r,Zr(t)]:Zr(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Br?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ni(e.ssContent),ssFallback:e.ssFallback&&ni(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function oi(e=" ",t=0){return ti(Nr,null,e,t)}function ri(e="",t=!1){return t?(Fr(),Xr(qr,null,e)):ti(qr,null,e)}function ii(e){return null==e||"boolean"==typeof e?ti(qr):f(e)?ti(Br,null,e.slice()):"object"==typeof e?ai(e):ti(Nr,null,String(e))}function ai(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:ni(e)}function si(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(f(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),si(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||Qr in t?3===o&&zn&&(1===zn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=zn}}else v(t)?(t={default:t,_ctx:zn},n=32):(t=String(t),64&o?(n=16,t=[oi(t)]):n=8);e.children=t,e.shapeFlag|=n}function li(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=ce([t.class,o.class]));else if("style"===e)t.style=le([t.style,o.style]);else if(s(e)){const n=t[e],r=o[e];!r||n===r||f(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function ci(e,t,n,o=null){yn(e,t,7,[n,o])}const ui=Mr();let di=0;let pi=null;const fi=()=>pi||zn,hi=e=>{pi=e,e.scope.on()},gi=()=>{pi&&pi.scope.off(),pi=null};function mi(e){return 4&e.vnode.shapeFlag}let vi=!1;function yi(e,t,n){v(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:_(t)&&(e.setupState=hn(t)),bi(e,n)}function bi(e,t,n){const o=e.type;e.render||(e.render=o.render||i),hi(e),tt(),ar(e),nt(),gi()}function _i(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(hn(tn(e.exposed)),{get:(t,n)=>n in t?t[n]:n in nr?nr[n](e):void 0,has:(e,t)=>t in e||t in nr}))}function wi(e,t=!0){return v(e)?e.displayName||e.name:e.name||t&&e.__name}const xi=(e,t)=>function(e,t,n=!1){let o,r;const a=v(e);return a?(o=e,r=i):(o=e.get,r=e.set),new mn(o,r,a||!r,n)}(e,0,vi);function Ti(e,t,n){const o=arguments.length;return 2===o?_(t)&&!f(t)?Gr(t)?ti(e,null,[t]):ti(e,t):ti(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Gr(n)&&(n=[n]),ti(e,t,n))}const Si=Symbol(""),ki=()=>Qn(Si),Ci="3.2.47",Ei="undefined"!=typeof document?document:null,Ai=Ei&&Ei.createElement("template"),Mi={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r=t?Ei.createElementNS("http://www.w3.org/2000/svg",e):n?Ei.createElement(e,{is:n}):Ei.createElement(e);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>Ei.createTextNode(e),createComment:e=>Ei.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ei.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const a=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{Ai.innerHTML=o?`<svg>${e}</svg>`:e;const r=Ai.content;if(o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[a?a.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};const Pi=/\s*!important$/;function Ii(e,t,n){if(f(n))n.forEach((n=>Ii(e,t,n)));else if(null==n&&(n=""),n=zi(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=Li[t];if(n)return n;let o=M(t);if("filter"!==o&&o in e)return Li[t]=o;o=O(o);for(let r=0;r<Oi.length;r++){const n=Oi[r]+o;if(n in e)return Li[t]=n}return t}(e,t);Pi.test(n)?e.setProperty(I(o),n.replace(Pi,""),"important"):e[o]=n}}const Oi=["Webkit","Moz","ms"],Li={};const{unit:$i,unitRatio:Di,unitPrecision:Ri}={unit:"rem",unitRatio:10/320,unitPrecision:5},Bi=(Ni=$i,qi=Di,ji=Ri,e=>e.replace(_e,((e,t)=>{if(!t)return e;if(1===qi)return`${t}${Ni}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*qi,ji);return 0===n?"0":`${n}${Ni}`})));var Ni,qi,ji;const zi=e=>y(e)?Bi(e):e,Vi="http://www.w3.org/1999/xlink";function Fi(e,t,n,o){e.addEventListener(t,n,o)}function Ui(e,t,n,o,r=null){const i=e._vei||(e._vei={}),a=i[t];if(o&&a)a.value=o;else{const[n,s]=function(e){let t;if(Wi.test(e)){let n;for(t={};n=e.match(Wi);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):I(e.slice(2)),t]}(t);if(o){const a=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&f(i)){const n=Xi(e,i);for(let o=0;o<n.length;o++){const i=n[o];yn(i,t,5,i.__wwe?[e]:r(e))}}else yn(Xi(e,i),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=(()=>Hi||(Yi.then((()=>Hi=0)),Hi=Date.now()))(),n}(o,r);Fi(e,n,a,s)}else a&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,a,s),i[t]=void 0)}}const Wi=/(?:Once|Passive|Capture)$/;let Hi=0;const Yi=Promise.resolve();function Xi(e,t){if(f(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const Gi=/^on[a-z]/;const Ji="transition",Qi=(e,{slots:t})=>Ti(so,function(e){const t={};for(const c in e)c in Ki||(t[c]=e[c]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:a=`${n}-enter-active`,enterToClass:s=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:u=a,appearToClass:d=s,leaveFromClass:p=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,g=function(e){if(null==e)return null;if(_(e))return[ta(e.enter),ta(e.leave)];{const t=ta(e);return[t,t]}}(r),m=g&&g[0],v=g&&g[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:w,onLeave:x,onLeaveCancelled:T,onBeforeAppear:S=y,onAppear:k=b,onAppearCancelled:C=w}=t,E=(e,t,n)=>{oa(e,t?d:s),oa(e,t?u:a),n&&n()},A=(e,t)=>{e._isLeaving=!1,oa(e,p),oa(e,h),oa(e,f),t&&t()},M=e=>(t,n)=>{const r=e?k:b,a=()=>E(t,e,n);Zi(r,[t,a]),ra((()=>{oa(t,e?l:i),na(t,e?d:s),ea(r)||aa(t,o,m,a)}))};return c(t,{onBeforeEnter(e){Zi(y,[e]),na(e,i),na(e,a)},onBeforeAppear(e){Zi(S,[e]),na(e,l),na(e,u)},onEnter:M(!1),onAppear:M(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>A(e,t);na(e,p),document.body.offsetHeight,na(e,f),ra((()=>{e._isLeaving&&(oa(e,p),na(e,h),ea(x)||aa(e,o,v,n))})),Zi(x,[e,n])},onEnterCancelled(e){E(e,!1),Zi(w,[e])},onAppearCancelled(e){E(e,!0),Zi(C,[e])},onLeaveCancelled(e){A(e),Zi(T,[e])}})}(e),t);Qi.displayName="Transition";const Ki={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};Qi.props=c({},ao,Ki);const Zi=(e,t=[])=>{f(e)?e.forEach((e=>e(...t))):e&&e(...t)},ea=e=>!!e&&(f(e)?e.some((e=>e.length>1)):e.length>1);function ta(e){const t=(e=>{const t=y(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function na(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e._vtc||(e._vtc=new Set)).add(t)}function oa(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function ra(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let ia=0;function aa(e,t,n,o){const r=e._endId=++ia,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:a,timeout:s,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o("transitionDelay"),i=o("transitionDuration"),a=sa(r,i),s=o("animationDelay"),l=o("animationDuration"),c=sa(s,l);let u=null,d=0,p=0;t===Ji?a>0&&(u=Ji,d=a,p=i.length):"animation"===t?c>0&&(u="animation",d=c,p=l.length):(d=Math.max(a,c),u=d>0?a>c?Ji:"animation":null,p=u?u===Ji?i.length:l.length:0);const f=u===Ji&&/\b(transform|all)(,|$)/.test(o("transitionProperty").toString());return{type:u,timeout:d,propCount:p,hasTransform:f}}(e,t);if(!a)return o();const c=a+"end";let u=0;const d=()=>{e.removeEventListener(c,p),i()},p=t=>{t.target===e&&++u>=l&&d()};setTimeout((()=>{u<l&&d()}),s+1),e.addEventListener(c,p)}function sa(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>la(t)+la(e[n]))))}function la(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}const ca=e=>{const t=e.props["onUpdate:modelValue"]||!1;return f(t)?e=>D(t,e):t};function ua(e){e.target.composing=!0}function da(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const pa={created(e,{modifiers:{lazy:t,trim:n,number:o}},r){e._assign=ca(r);const i=o||r.props&&"number"===r.props.type;Fi(e,t?"change":"input",(t=>{if(t.target.composing)return;let o=e.value;n&&(o=o.trim()),i&&(o=B(o)),e._assign(o)})),n&&Fi(e,"change",(()=>{e.value=e.value.trim()})),t||(Fi(e,"compositionstart",ua),Fi(e,"compositionend",da),Fi(e,"change",da))},mounted(e,{value:t}){e.value=null==t?"":t},beforeUpdate(e,{value:t,modifiers:{lazy:n,trim:o,number:r}},i){if(e._assign=ca(i),e.composing)return;if(document.activeElement===e&&"range"!==e.type){if(n)return;if(o&&e.value.trim()===t)return;if((r||"number"===e.type)&&B(e.value)===t)return}const a=null==t?"":t;e.value!==a&&(e.value=a)}},fa={deep:!0,created(e,t,n){e._assign=ca(n),Fi(e,"change",(()=>{const t=e._modelValue,n=ya(e),o=e.checked,r=e._assign;if(f(t)){const e=X(t,n),i=-1!==e;if(o&&!i)r(t.concat(n));else if(!o&&i){const n=[...t];n.splice(e,1),r(n)}}else if(g(t)){const e=new Set(t);o?e.add(n):e.delete(n),r(e)}else r(ba(e,o))}))},mounted:ha,beforeUpdate(e,t,n){e._assign=ca(n),ha(e,t,n)}};function ha(e,{value:t,oldValue:n},o){e._modelValue=t,f(t)?e.checked=X(t,o.props.value)>-1:g(t)?e.checked=t.has(o.props.value):t!==n&&(e.checked=Y(t,ba(e,!0)))}const ga={created(e,{value:t},n){e.checked=Y(t,n.props.value),e._assign=ca(n),Fi(e,"change",(()=>{e._assign(ya(e))}))},beforeUpdate(e,{value:t,oldValue:n},o){e._assign=ca(o),t!==n&&(e.checked=Y(t,o.props.value))}},ma={deep:!0,created(e,{value:t,modifiers:{number:n}},o){const r=g(t);Fi(e,"change",(()=>{const t=Array.prototype.filter.call(e.options,(e=>e.selected)).map((e=>n?B(ya(e)):ya(e)));e._assign(e.multiple?r?new Set(t):t:t[0])})),e._assign=ca(o)},mounted(e,{value:t}){va(e,t)},beforeUpdate(e,t,n){e._assign=ca(n)},updated(e,{value:t}){va(e,t)}};function va(e,t){const n=e.multiple;if(!n||f(t)||g(t)){for(let o=0,r=e.options.length;o<r;o++){const r=e.options[o],i=ya(r);if(n)f(t)?r.selected=X(t,i)>-1:r.selected=t.has(i);else if(Y(ya(r),t))return void(e.selectedIndex!==o&&(e.selectedIndex=o))}n||-1===e.selectedIndex||(e.selectedIndex=-1)}}function ya(e){return"_value"in e?e._value:e.value}function ba(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const _a={created(e,t,n){wa(e,t,n,null,"created")},mounted(e,t,n){wa(e,t,n,null,"mounted")},beforeUpdate(e,t,n,o){wa(e,t,n,o,"beforeUpdate")},updated(e,t,n,o){wa(e,t,n,o,"updated")}};function wa(e,t,n,o,r){const i=function(e,t){switch(e){case"SELECT":return ma;case"TEXTAREA":return pa;default:switch(t){case"checkbox":return fa;case"radio":return ga;default:return pa}}}(e.tagName,n.props&&n.props.type)[r];i&&i(e,t,n,o)}const xa=["ctrl","shift","alt","meta"],Ta={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>xa.some((n=>e[`${n}Key`]&&!t.includes(n)))},Sa=(e,t)=>(n,...o)=>{for(let e=0;e<t.length;e++){const o=Ta[t[e]];if(o&&o(n,t))return}return e(n,...o)},ka={beforeMount(e,{value:t},{transition:n}){e._vod="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Ca(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Ca(e,!0),o.enter(e)):o.leave(e,(()=>{Ca(e,!1)})):Ca(e,t))},beforeUnmount(e,{value:t}){Ca(e,t)}};function Ca(e,t){e.style.display=t?e._vod:"none"}const Ea=c({patchProp:(e,t,n,o,r=!1,i,a,c,u)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,a=i[r],s=(e.__wxsProps||(e.__wxsProps={}))[r];if(s===a)return;e.__wxsProps[r]=a;const l=o.proxy;Mn((()=>{n(a,s,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,a);"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e._vtc;i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,r):"style"===t?function(e,t,n){const o=e.style,r=y(n);if(n&&!r){if(t&&!y(t))for(const e in t)null==n[e]&&Ii(o,e,"");for(const e in n)Ii(o,e,n[e])}else{const i=o.display;r?t!==n&&(o.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(o.display=i)}const{__wxsStyle:i}=e;if(i)for(const a in i)Ii(o,a,i[a])}(e,n,o):s(t)?l(t)||Ui(e,t,0,o,a):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&Gi.test(t)&&v(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if(Gi.test(t)&&y(n))return!1;return t in e}(e,t,o,r))?function(e,t,n,o,r,i,a){if("innerHTML"===t||"textContent"===t)return o&&a(o,r,i),void(e[t]=null==n?"":n);if("value"===t&&"PROGRESS"!==e.tagName&&!e.tagName.includes("-")){e._value=n;const o=null==n?"":n;return e.value===o&&"OPTION"!==e.tagName||(e.value=o),void(null==n&&e.removeAttribute(t))}let s=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=H(n):null==n&&"string"===o?(n="",s=!0):"number"===o&&(n=0,s=!0)}try{e[t]=n}catch(EC){}s&&e.removeAttribute(t)}(e,t,o,i,a,c,u):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o,r){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(Vi,t.slice(6,t.length)):e.setAttributeNS(Vi,t,n);else{const o=W(t);null==n||o&&!H(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,r))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},Mi);let Aa;const Ma=(...e)=>{const t=(Aa||(Aa=$r(Ea))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(y(e)){return document.querySelector(e)}return e}(e);if(!o)return;const r=t._component;v(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,o instanceof SVGElement);return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const Pa=["{","}"];const Ia=/^(?:\d)+/,Oa=/^(?:\w)+/;const La=Object.prototype.hasOwnProperty,$a=(e,t)=>La.call(e,t),Da=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=Pa){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let a=e[r++];if(a===t){i&&o.push({type:"text",value:i}),i="";let t="";for(a=e[r++];void 0!==a&&a!==n;)t+=a,a=e[r++];const s=a===n,l=Ia.test(t)?"list":s&&Oa.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=a}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function Ra(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class Ba{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||Da,this.messages=n||{},this.setLocale(e||"en"),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=Ra(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{$a(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=Ra(t,this.messages))&&(o=this.messages[t]):n=t,$a(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function Na(e,t={},n,o){"string"!=typeof e&&([e,t]=[t,e]),"string"!=typeof e&&(e="undefined"!=typeof uni&&uni.getLocale?uni.getLocale():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const r=new Ba({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=bv().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}function qa(e,t){return e.indexOf(t[0])>-1}
/*!
  * vue-router v4.1.6
  * (c) 2022 Eduardo San Martin Morote
  * @license MIT
  */const ja="undefined"!=typeof window;const za=Object.assign;function Va(e,t){const n={};for(const o in t){const r=t[o];n[o]=Ua(r)?r.map(e):e(r)}return n}const Fa=()=>{},Ua=Array.isArray,Wa=/\/$/;function Ha(e,t,n="/"){let o,r={},i="",a="";const s=t.indexOf("#");let l=t.indexOf("?");return s<l&&s>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,s>-1?s:t.length),r=e(i)),s>-1&&(o=o||t.slice(0,s),a=t.slice(s,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/");let r,i,a=n.length-1;for(r=0;r<o.length;r++)if(i=o[r],"."!==i){if(".."!==i)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+o.slice(r-(r===o.length?1:0)).join("/")}(null!=o?o:t,n),{fullPath:o+(i&&"?")+i+a,path:o,query:r,hash:a}}function Ya(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Xa(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ga(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Ja(e[n],t[n]))return!1;return!0}function Ja(e,t){return Ua(e)?Qa(e,t):Ua(t)?Qa(t,e):e===t}function Qa(e,t){return Ua(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}var Ka,Za,es,ts;function ns(e){if(!e)if(ja){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Wa,"")}(Za=Ka||(Ka={})).pop="pop",Za.push="push",(ts=es||(es={})).back="back",ts.forward="forward",ts.unknown="";const os=/^[^#]+#/;function rs(e,t){return e.replace(os,"#")+t}const is=()=>({left:window.pageXOffset,top:window.pageYOffset});function as(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.pageXOffset,null!=t.top?t.top:window.pageYOffset)}function ss(e,t){return(history.state?history.state.position-t:-1)+e}const ls=new Map;function cs(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let t=r.includes(e.slice(i))?e.slice(i).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),Ya(n,"")}return Ya(n,e)+o+r}function us(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?is():null}}function ds(e){const{history:t,location:n}=window,o={value:cs(e,n)},r={value:t.state};function i(o,i,a){const s=e.indexOf("#"),l=s>-1?(n.host&&document.querySelector("base")?e:e.slice(s))+o:location.protocol+"//"+location.host+e+o;try{t[a?"replaceState":"pushState"](i,"",l),r.value=i}catch(c){console.error(c),n[a?"replace":"assign"](l)}}return r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const a=za({},r.value,t.state,{forward:e,scroll:is()});i(a.current,a,!0),i(e,za({},us(o.value,e,null),{position:a.position+1},n),!1),o.value=e},replace:function(e,n){i(e,za({},t.state,us(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}function ps(e){const t=ds(e=ns(e)),n=function(e,t,n,o){let r=[],i=[],a=null;const s=({state:i})=>{const s=cs(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=s,t.value=i,a&&a===l)return void(a=null);u=c?i.position-c.position:0}else o(s);r.forEach((e=>{e(n.value,l,{delta:u,type:Ka.pop,direction:u?u>0?es.forward:es.back:es.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(za({},e.state,{scroll:is()}),"")}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",l),{pauseListeners:function(){a=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=za({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:rs.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function fs(e){return"string"==typeof e||"symbol"==typeof e}const hs={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},gs=Symbol("");var ms,vs;function ys(e,t){return za(new Error,{type:e,[gs]:!0},t)}function bs(e,t){return e instanceof Error&&gs in e&&(null==t||!!(e.type&t))}(vs=ms||(ms={}))[vs.aborted=4]="aborted",vs[vs.cancelled=8]="cancelled",vs[vs.duplicated=16]="duplicated";const _s={sensitive:!1,strict:!1,start:!0,end:!0},ws=/[.+*?^${}()[\]/\\]/g;function xs(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function Ts(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=xs(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(Ss(o))return 1;if(Ss(r))return-1}return r.length-o.length}function Ss(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ks={type:0,value:""},Cs=/[a-zA-Z0-9_]/;function Es(e,t,n){const o=function(e,t){const n=za({},_s,t),o=[];let r=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let a=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(ws,"\\$&"),a+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;i.push({name:e,repeatable:n,optional:c});const d=u||"[^/]+?";if("[^/]+?"!==d){a+=10;try{new RegExp(`(${d})`)}catch(s){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+s.message)}}let p=n?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(p=c&&l.length<2?`(?:/${p})`:"/"+p),c&&(p+="?"),r+=p,a+=20,c&&(a+=-8),n&&(a+=-20),".*"===d&&(a+=-50)}e.push(a)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const a=new RegExp(r,n.sensitive?"":"i");return{re:a,score:o,keys:i,parse:function(e){const t=e.match(a),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=i[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:a,optional:s}=e,l=i in t?t[i]:"";if(Ua(l)&&!a)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=Ua(l)?l.join("/"):l;if(!c){if(!s)throw new Error(`Missing required param "${i}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[ks]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let i;function a(){i&&r.push(i),i=[]}let s,l=0,c="",u="";function d(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===s||"+"===s)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===s||"+"===s,optional:"*"===s||"?"===s})):t("Invalid state to consume buffer"),c="")}function p(){c+=s}for(;l<e.length;)if(s=e[l++],"\\"!==s||2===n)switch(n){case 0:"/"===s?(c&&d(),a()):":"===s?(d(),n=1):p();break;case 4:p(),n=o;break;case 1:"("===s?n=2:Cs.test(s)?p():(d(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--);break;case 2:")"===s?"\\"==u[u.length-1]?u=u.slice(0,-1)+s:n=3:u+=s;break;case 3:d(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),d(),a(),r}(e.path),n),r=za(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function As(e,t){const n=[],o=new Map;function r(e,n,o){const s=!o,l=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:Ps(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}(e);l.aliasOf=o&&o.record;const c=Ls(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(za({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l}))}let d,p;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(d=Es(t,n,c),o?o.alias.push(d):(p=p||d,p!==d&&p.alias.push(d),s&&e.name&&!Is(d)&&i(e.name)),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],d,o&&o.children[t])}o=o||d,(d.record.components&&Object.keys(d.record.components).length||d.record.name||d.record.redirect)&&a(d)}return p?()=>{i(p)}:Fa}function i(e){if(fs(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function a(e){let t=0;for(;t<n.length&&Ts(e,n[t])>=0&&(e.record.path!==n[t].record.path||!$s(e,n[t]));)t++;n.splice(t,0,e),e.record.name&&!Is(e)&&o.set(e.record.name,e)}return t=Ls({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,i,a,s={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw ys(1,{location:e});a=r.record.name,s=za(Ms(t.params,r.keys.filter((e=>!e.optional)).map((e=>e.name))),e.params&&Ms(e.params,r.keys.map((e=>e.name)))),i=r.stringify(s)}else if("path"in e)i=e.path,r=n.find((e=>e.re.test(i))),r&&(s=r.parse(i),a=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw ys(1,{location:e,currentLocation:t});a=r.record.name,s=za({},t.params,e.params),i=r.stringify(s)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:a,path:i,params:s,matched:l,meta:Os(l)}},removeRoute:i,getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function Ms(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function Ps(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="boolean"==typeof n?n:n[o];return t}function Is(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Os(e){return e.reduce(((e,t)=>za(e,t.meta)),{})}function Ls(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function $s(e,t){return t.children.some((t=>t===e||$s(e,t)))}const Ds=/#/g,Rs=/&/g,Bs=/\//g,Ns=/=/g,qs=/\?/g,js=/\+/g,zs=/%5B/g,Vs=/%5D/g,Fs=/%5E/g,Us=/%60/g,Ws=/%7B/g,Hs=/%7C/g,Ys=/%7D/g,Xs=/%20/g;function Gs(e){return encodeURI(""+e).replace(Hs,"|").replace(zs,"[").replace(Vs,"]")}function Js(e){return Gs(e).replace(js,"%2B").replace(Xs,"+").replace(Ds,"%23").replace(Rs,"%26").replace(Us,"`").replace(Ws,"{").replace(Ys,"}").replace(Fs,"^")}function Qs(e){return null==e?"":function(e){return Gs(e).replace(Ds,"%23").replace(qs,"%3F")}(e).replace(Bs,"%2F")}function Ks(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function Zs(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(js," "),r=e.indexOf("="),i=Ks(r<0?e:e.slice(0,r)),a=r<0?null:Ks(e.slice(r+1));if(i in t){let e=t[i];Ua(e)||(e=t[i]=[e]),e.push(a)}else t[i]=a}return t}function el(e){let t="";for(let n in e){const o=e[n];if(n=Js(n).replace(Ns,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(Ua(o)?o.map((e=>e&&Js(e))):[o&&Js(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function tl(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=Ua(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const nl=Symbol(""),ol=Symbol(""),rl=Symbol(""),il=Symbol(""),al=Symbol("");function sl(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e,reset:function(){e=[]}}}function ll(e,t,n,o,r){const i=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((a,s)=>{const l=e=>{var l;!1===e?s(ys(4,{from:n,to:t})):e instanceof Error?s(e):"string"==typeof(l=e)||l&&"object"==typeof l?s(ys(2,{from:t,to:e})):(i&&o.enterCallbacks[r]===i&&"function"==typeof e&&i.push(e),a())},c=e.call(o&&o.instances[r],t,n,l);let u=Promise.resolve(c);e.length<3&&(u=u.then(l)),u.catch((e=>s(e)))}))}function cl(e,t,n,o){const r=[];for(const a of e)for(const e in a.components){let s=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if("object"==typeof(i=s)||"displayName"in i||"props"in i||"__vccOpts"in i){const i=(s.__vccOpts||s)[t];i&&r.push(ll(i,n,o,a,e))}else{let i=s();r.push((()=>i.then((r=>{if(!r)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${a.path}"`));const i=(s=r).__esModule||"Module"===s[Symbol.toStringTag]?r.default:r;var s;a.components[e]=i;const l=(i.__vccOpts||i)[t];return l&&ll(l,n,o,a,e)()}))))}}var i;return r}function ul(e){const t=Qn(rl),n=Qn(il),o=xi((()=>t.resolve(pn(e.to)))),r=xi((()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;const a=i.findIndex(Xa.bind(null,r));if(a>-1)return a;const s=fl(e[t-2]);return t>1&&fl(r)===s&&i[i.length-1].path!==s?i.findIndex(Xa.bind(null,e[t-2])):a})),i=xi((()=>r.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!Ua(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}(n.params,o.value.params))),a=xi((()=>r.value>-1&&r.value===n.matched.length-1&&Ga(n.params,o.value.params)));return{route:o,href:xi((()=>o.value.href)),isActive:i,isExactActive:a,navigate:function(n={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)?t[pn(e.replace)?"replace":"push"](pn(e.to)).catch(Fa):Promise.resolve()}}}const dl=go({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:ul,setup(e,{slots:t}){const n=Yt(ul(e)),{options:o}=Qn(rl),r=xi((()=>({[hl(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[hl(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:Ti("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),pl=dl;function fl(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const hl=(e,t,n)=>null!=e?e:null!=t?t:n;function gl(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const ml=go({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=Qn(al),r=xi((()=>e.route||o.value)),i=Qn(ol,0),a=xi((()=>{let e=pn(i);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),s=xi((()=>r.value.matched[a.value]));Jn(ol,xi((()=>a.value+1))),Jn(nl,s),Jn(al,r);const l=ln();return eo((()=>[l.value,s.value,e.name]),(([e,t,n],[o,r,i])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&Xa(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,i=e.name,a=s.value,c=a&&a.components[i];if(!c)return gl(n.default,{Component:c,route:o});const u=a.props[i],d=u?!0===u?o.params:"function"==typeof u?u(o):u:null,p=Ti(c,za({},d,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(a.instances[i]=null)},ref:l}));return gl(n.default,{Component:p,route:o})||p}}});function vl(e){const t=As(e.routes,e),n=e.parseQuery||Zs,o=e.stringifyQuery||el,r=e.history,i=sl(),a=sl(),s=sl(),l=cn(hs);let c=hs;ja&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Va.bind(null,(e=>""+e)),d=Va.bind(null,Qs),p=Va.bind(null,Ks);function f(e,i){if(i=za({},i||l.value),"string"==typeof e){const o=Ha(n,e,i.path),a=t.resolve({path:o.path},i),s=r.createHref(o.fullPath);return za(o,a,{params:p(a.params),hash:Ks(o.hash),redirectedFrom:void 0,href:s})}let a;if("path"in e)a=za({},e,{path:Ha(n,e.path,i.path).path});else{const t=za({},e.params);for(const e in t)null==t[e]&&delete t[e];a=za({},e,{params:d(e.params)}),i.params=d(i.params)}const s=t.resolve(a,i),c=e.hash||"";s.params=u(p(s.params));const f=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,za({},e,{hash:(h=c,Gs(h).replace(Ws,"{").replace(Ys,"}").replace(Fs,"^")),path:s.path}));var h;const g=r.createHref(f);return za({fullPath:f,hash:c,query:o===el?tl(e.query):e.query||{}},s,{redirectedFrom:void 0,href:g})}function h(e){return"string"==typeof e?Ha(n,e,l.value.path):za({},e)}function g(e,t){if(c!==e)return ys(8,{from:t,to:e})}function m(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=h(o):{path:o},o.params={}),za({query:e.query,hash:e.hash,params:"path"in o?{}:e.params},o)}}function y(e,t){const n=c=f(e),r=l.value,i=e.state,a=e.force,s=!0===e.replace,u=v(n);if(u)return y(za(h(u),{state:"object"==typeof u?za({},i,u.state):i,force:a,replace:s}),t||n);const d=n;let p;return d.redirectedFrom=t,!a&&function(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&Xa(t.matched[o],n.matched[r])&&Ga(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(p=ys(16,{to:d,from:r}),P(r,r,!0,!1)),(p?Promise.resolve(p):_(d,r)).catch((e=>bs(e)?bs(e,2)?e:M(e):A(e,d,r))).then((e=>{if(e){if(bs(e,2))return y(za({replace:s},h(e.to),{state:"object"==typeof e.to?za({},i,e.to.state):i,force:a}),t||d)}else e=x(d,r,!0,s,i);return w(d,r,e),e}))}function b(e,t){const n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function _(e,t){let n;const[o,r,s]=function(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let a=0;a<i;a++){const i=t.matched[a];i&&(e.matched.find((e=>Xa(e,i)))?o.push(i):n.push(i));const s=e.matched[a];s&&(t.matched.find((e=>Xa(e,s)))||r.push(s))}return[n,o,r]}(e,t);n=cl(o.reverse(),"beforeRouteLeave",e,t);for(const i of o)i.leaveGuards.forEach((o=>{n.push(ll(o,e,t))}));const l=b.bind(null,e,t);return n.push(l),yl(n).then((()=>{n=[];for(const o of i.list())n.push(ll(o,e,t));return n.push(l),yl(n)})).then((()=>{n=cl(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(ll(o,e,t))}));return n.push(l),yl(n)})).then((()=>{n=[];for(const o of e.matched)if(o.beforeEnter&&!t.matched.includes(o))if(Ua(o.beforeEnter))for(const r of o.beforeEnter)n.push(ll(r,e,t));else n.push(ll(o.beforeEnter,e,t));return n.push(l),yl(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=cl(s,"beforeRouteEnter",e,t),n.push(l),yl(n)))).then((()=>{n=[];for(const o of a.list())n.push(ll(o,e,t));return n.push(l),yl(n)})).catch((e=>bs(e,8)?e:Promise.reject(e)))}function w(e,t,n){for(const o of s.list())o(e,t,n)}function x(e,t,n,o,i){const a=g(e,t);if(a)return a;const s=t===hs,c=ja?history.state:{};n&&(o||s?r.replace(e.fullPath,za({scroll:s&&c&&c.scroll},i)):r.push(e.fullPath,i)),l.value=e,P(e,t,n,s),M()}let T;function S(){T||(T=r.listen(((e,t,n)=>{if(!$.listening)return;const o=f(e),i=v(o);if(i)return void y(za(i,{replace:!0}),o).catch(Fa);c=o;const a=l.value;var s,u;ja&&(s=ss(a.fullPath,n.delta),u=is(),ls.set(s,u)),_(o,a).catch((e=>bs(e,12)?e:bs(e,2)?(y(e.to,o).then((e=>{bs(e,20)&&!n.delta&&n.type===Ka.pop&&r.go(-1,!1)})).catch(Fa),Promise.reject()):(n.delta&&r.go(-n.delta,!1),A(e,o,a)))).then((e=>{(e=e||x(o,a,!1))&&(n.delta&&!bs(e,8)?r.go(-n.delta,!1):n.type===Ka.pop&&bs(e,20)&&r.go(-1,!1)),w(o,a,e)})).catch(Fa)})))}let k,C=sl(),E=sl();function A(e,t,n){M(e);const o=E.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function M(e){return k||(k=!e,S(),C.list().forEach((([t,n])=>e?n(e):t())),C.reset()),e}function P(t,n,o,r){const{scrollBehavior:i}=e;if(!ja||!i)return Promise.resolve();const a=!o&&function(e){const t=ls.get(e);return ls.delete(e),t}(ss(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return Mn().then((()=>i(t,n,a))).then((e=>e&&as(e))).catch((e=>A(e,t,n)))}const I=e=>r.go(e);let O;const L=new Set,$={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return fs(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:f,options:e,push:m,replace:function(e){return m(za(h(e),{replace:!0}))},go:I,back:()=>I(-1),forward:()=>I(1),beforeEach:i.add,beforeResolve:a.add,afterEach:s.add,onError:E.add,isReady:function(){return k&&l.value!==hs?Promise.resolve():new Promise(((e,t)=>{C.add([e,t])}))},install(e){e.component("RouterLink",pl),e.component("RouterView",ml),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>pn(l)}),ja&&!O&&l.value===hs&&(O=!0,m(r.location).catch((e=>{})));const t={};for(const o in hs)t[o]=xi((()=>l.value[o]));e.provide(rl,this),e.provide(il,Yt(t)),e.provide(al,l);const n=e.unmount;L.add(e),e.unmount=function(){L.delete(e),L.size<1&&(c=hs,T&&T(),T=null,l.value=hs,O=!1,k=!1),n()}}};return $}function yl(e){return e.reduce(((e,t)=>e.then((()=>t()))),Promise.resolve())}function bl(){return Qn(il)}const _l=fe((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let wl;function xl(e){return qa(e,ne)?kl().f(e,function(){const e=uni.getLocale(),t=__uniConfig.locales;return t[e]||t[__uniConfig.fallbackLocale]||t.en||{}}(),ne):e}function Tl(e,t){if(1===t.length){if(e){const n=e=>y(e)&&qa(e,ne),o=t[0];let r=[];if(f(e)&&(r=e.filter((e=>n(e[o])))).length)return r;const i=e[t[0]];if(n(i))return e}return}const n=t.shift();return Tl(e&&e[n],t)}function Sl(e,t){const n=Tl(e,t);if(!n)return!1;const o=t[t.length-1];if(f(n))n.forEach((e=>Sl(e,[o])));else{let e=n[o];Object.defineProperty(n,o,{get:()=>xl(e),set(t){e=t}})}return!0}function kl(){if(!wl){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,wl=Na(e),_l()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>wl.add(e,__uniConfig.locales[e]))),wl.setLocale(e)}}return wl}function Cl(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const El=fe((()=>{const e="uni.async.",t=["error"];kl().add("en",Cl(e,t,["The connection timed out, click the screen to try again."]),!1),kl().add("es",Cl(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),kl().add("fr",Cl(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),kl().add("zh-Hans",Cl(e,t,["连接服务器超时，点击屏幕重试"]),!1),kl().add("zh-Hant",Cl(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),Al=fe((()=>{const e="uni.showActionSheet.",t=["cancel"];kl().add("en",Cl(e,t,["Cancel"]),!1),kl().add("es",Cl(e,t,["Cancelar"]),!1),kl().add("fr",Cl(e,t,["Annuler"]),!1),kl().add("zh-Hans",Cl(e,t,["取消"]),!1),kl().add("zh-Hant",Cl(e,t,["取消"]),!1)})),Ml=fe((()=>{const e="uni.showToast.",t=["unpaired"];kl().add("en",Cl(e,t,["Please note showToast must be paired with hideToast"]),!1),kl().add("es",Cl(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),kl().add("fr",Cl(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),kl().add("zh-Hans",Cl(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),kl().add("zh-Hant",Cl(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),Pl=fe((()=>{const e="uni.showLoading.",t=["unpaired"];kl().add("en",Cl(e,t,["Please note showLoading must be paired with hideLoading"]),!1),kl().add("es",Cl(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),kl().add("fr",Cl(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),kl().add("zh-Hans",Cl(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),kl().add("zh-Hant",Cl(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),Il=fe((()=>{const e="uni.showModal.",t=["cancel","confirm"];kl().add("en",Cl(e,t,["Cancel","OK"]),!1),kl().add("es",Cl(e,t,["Cancelar","OK"]),!1),kl().add("fr",Cl(e,t,["Annuler","OK"]),!1),kl().add("zh-Hans",Cl(e,t,["取消","确定"]),!1),kl().add("zh-Hant",Cl(e,t,["取消","確定"]),!1)})),Ol=fe((()=>{const e="uni.chooseFile.",t=["notUserActivation"];kl().add("en",Cl(e,t,["File chooser dialog can only be shown with a user activation"]),!1),kl().add("es",Cl(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),kl().add("fr",Cl(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),kl().add("zh-Hans",Cl(e,t,["文件选择器对话框只能在由用户激活时显示"]),!1),kl().add("zh-Hant",Cl(e,t,["文件選擇器對話框只能在由用戶激活時顯示"]),!1)})),Ll=fe((()=>{const e="uni.setClipboardData.",t=["success","fail"];kl().add("en",Cl(e,t,["Content copied","Copy failed, please copy manually"]),!1),kl().add("es",Cl(e,t,["Contenido copiado","Error al copiar, copie manualmente"]),!1),kl().add("fr",Cl(e,t,["Contenu copié","Échec de la copie, copiez manuellement"]),!1),kl().add("zh-Hans",Cl(e,t,["内容已复制","复制失败，请手动复制"]),!1),kl().add("zh-Hant",Cl(e,t,["內容已復制","復制失敗，請手動復製"]),!1)})),$l=fe((()=>{const e="uni.getClipboardData.",t=["fail"];kl().add("en",Cl(e,t,["Reading failed, please paste manually"]),!1),kl().add("es",Cl(e,t,["Error de lectura, pegue manualmente"]),!1),kl().add("fr",Cl(e,t,["Échec de la lecture, veuillez coller manuellement"]),!1),kl().add("zh-Hans",Cl(e,t,["读取失败，请手动粘贴"]),!1),kl().add("zh-Hant",Cl(e,t,["讀取失敗，請手動粘貼"]),!1)})),Dl=fe((()=>{const e="uni.picker.",t=["done","cancel"];kl().add("en",Cl(e,t,["Done","Cancel"]),!1),kl().add("es",Cl(e,t,["OK","Cancelar"]),!1),kl().add("fr",Cl(e,t,["OK","Annuler"]),!1),kl().add("zh-Hans",Cl(e,t,["完成","取消"]),!1),kl().add("zh-Hant",Cl(e,t,["完成","取消"]),!1)})),Rl=fe((()=>{const e="uni.video.",t=["danmu","volume"];kl().add("en",Cl(e,t,["Danmu","Volume"]),!1),kl().add("es",Cl(e,t,["Danmu","Volumen"]),!1),kl().add("fr",Cl(e,t,["Danmu","Le Volume"]),!1),kl().add("zh-Hans",Cl(e,t,["弹幕","音量"]),!1),kl().add("zh-Hant",Cl(e,t,["彈幕","音量"]),!1)})),Bl=fe((()=>{const e="uni.chooseLocation.",t=["search","cancel"];kl().add("en",Cl(e,t,["Find Place","Cancel"]),!1),kl().add("es",Cl(e,t,["Encontrar","Cancelar"]),!1),kl().add("fr",Cl(e,t,["Trouve","Annuler"]),!1),kl().add("zh-Hans",Cl(e,t,["搜索地点","取消"]),!1),kl().add("zh-Hant",Cl(e,t,["搜索地點","取消"]),!1)}));function Nl(e){const t=new Re;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}let ql=1;const jl=Object.create(null);function zl(e,t){return e+"."+t}function Vl(e,t,n){t=zl(e,t),jl[t]||(jl[t]=n)}function Fl({id:e,name:t,args:n},o){t=zl(o,t);const r=t=>{e&&Lx.publishHandler("invokeViewApi."+e,t)},i=jl[t];i?i(n,r):r({})}const Ul=c(Nl("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=Lx,i=n?ql++:0;n&&o("invokeServiceApi."+i,n,!0),r("invokeServiceApi",{id:i,name:e,args:t})}}),Wl=we(!0);let Hl;function Yl(){Hl&&(clearTimeout(Hl),Hl=null)}let Xl=0,Gl=0;function Jl(e){if(Yl(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];Xl=t,Gl=n,Hl=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}function Ql(e){if(!Hl)return;if(1!==e.touches.length)return Yl();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Xl)>10||Math.abs(n-Gl)>10?Yl():void 0}function Kl(e,t){const n=Number(e);return isNaN(n)?t:n}function Zl(){const e=__uniConfig.globalStyle||{},t=Kl(e.rpxCalcMaxDeviceWidth,960),n=Kl(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function ec(){Zl(),ye(),window.addEventListener("touchstart",Jl,Wl),window.addEventListener("touchmove",Ql,Wl),window.addEventListener("touchend",Yl,Wl),window.addEventListener("touchcancel",Yl,Wl)}function tc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var nc,oc,rc=["top","left","right","bottom"],ic={};function ac(){return oc="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function sc(){if(oc="string"==typeof oc?oc:ac()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(EC){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),rc.forEach((function(e){a(o,e)})),document.body.appendChild(o),i(),nc=!0}else rc.forEach((function(e){ic[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function a(e,n){var o=document.createElement("div"),a=document.createElement("div"),s=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:oc+"(safe-area-inset-"+n+")"};r(o,c),r(a,c),r(s,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(s),a.appendChild(l),e.appendChild(o),e.appendChild(a),i((function(){o.scrollTop=a.scrollTop=1e4;var e=o.scrollTop,r=a.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=a.scrollTop=1e4,e=o.scrollTop,r=a.scrollTop,function(e){cc.length||setTimeout((function(){var e={};cc.forEach((function(t){e[t]=ic[t]})),cc.length=0,uc.forEach((function(t){t(e)}))}),0);cc.push(e)}(n))}o.addEventListener("scroll",i,t),a.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(ic,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function lc(e){return nc||sc(),ic[e]}var cc=[];var uc=[];const dc=tc({get support(){return 0!=("string"==typeof oc?oc:ac()).length},get top(){return lc("top")},get left(){return lc("left")},get right(){return lc("right")},get bottom(){return lc("bottom")},onChange:function(e){ac()&&(nc||sc(),"function"==typeof e&&uc.push(e))},offChange:function(e){var t=uc.indexOf(e);t>=0&&uc.splice(t,1)}}),pc=Sa((()=>{}),["prevent"]),fc=Sa((e=>{}),["stop"]);function hc(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function gc(){const e=hc(document.documentElement.style,"--window-top");return e?e+dc.top:0}function mc(){const e=document.documentElement.style,t=gc(),n=hc(e,"--window-bottom"),o=hc(e,"--window-left"),r=hc(e,"--window-right"),i=hc(e,"--top-window-height");return{top:t,bottom:n?n+dc.bottom:0,left:o?o+dc.left:0,right:r?r+dc.right:0,topWindowHeight:i||0}}function vc(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function yc(e){return vc(e)}function bc(e){return Symbol(e)}function _c(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function wc(e,t=!1){if(t)return function(e){if(!_c(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>uni.upx2px(parseFloat(t))+"px"))}(e);if(y(e)){const t=parseInt(e)||0;return _c(e)?uni.upx2px(t):t}return e}const xc="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",Tc="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z",Sc="M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z",kc="M17.25 16.156l7.375-7.313q0.281-0.281 0.281-0.641t-0.281-0.641q-0.25-0.25-0.625-0.25t-0.625 0.25l-7.375 7.344-7.313-7.344q-0.25-0.25-0.625-0.25t-0.625 0.25q-0.281 0.25-0.281 0.625t0.281 0.625l7.313 7.344-7.375 7.344q-0.281 0.25-0.281 0.625t0.281 0.625q0.125 0.125 0.281 0.188t0.344 0.063q0.156 0 0.328-0.063t0.297-0.188l7.375-7.344 7.375 7.406q0.125 0.156 0.297 0.219t0.328 0.063q0.188 0 0.344-0.078t0.281-0.203q0.281-0.25 0.281-0.609t-0.281-0.641l-7.375-7.406z",Cc="M31.562 4.9966666659375q0.435 0.399 0.435 0.87 0.036 0.58-0.399 0.98l-18.61 19.917q-0.145 0.145-0.327 0.217-0.073 0.037-0.145 0.11-0.254 0.035-0.472 0.035-0.29 0-0.544-0.036l-0.145-0.072q-0.109-0.073-0.217-0.182l-0.11-0.072L0.363 16.2786666659375q-0.327-0.399-0.363-0.907 0-0.544 0.363-1.016 0.435-0.326 0.961-0.362 0.527-0.036 0.962 0.362l9.722 9.542L29.712 5.0326666659375q0.399-0.363 0.943-0.363 0.544-0.036 0.907 0.327z";function Ec(e,t="#000",n=27){return ti("svg",{width:n,height:n,viewBox:"0 0 32 32"},[ti("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function Ac(){{const{$pageInstance:e}=fi();return e&&e.proxy.$page.id}}function Mc(e){const t=ie(e);if(t.$page)return t.$page.id;if(t.$){const{$pageInstance:e}=t.$;return e&&e.proxy.$page.id}}function Pc(){const e=Gm(),t=e.length;if(t)return e[t-1]}function Ic(){const e=Pc();if(e)return e.$page.meta}function Oc(){const e=Ic();return e?e.id:-1}function Lc(){const e=Pc();if(e)return e.$vm}const $c=["navigationBar","pullToRefresh"];function Dc(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=c({id:t},n,e);$c.forEach((t=>{o[t]=c({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function Rc(e,t,n){if(y(e))n=t,t=e,e=Lc();else if("number"==typeof e){const t=Gm().find((t=>t.$page.id===e));e=t?t.$vm:Lc()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function Bc(e){e.preventDefault()}let Nc,qc=0;function jc({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const a=()=>{function a(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,a=Math.abs(e-qc)>n;return!i||r&&!a?(!i&&r&&(r=!1),!1):(qc=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(a()||(Nc=setTimeout(a,300))),o=!1};return function(){clearTimeout(Nc),o||requestAnimationFrame(a),o=!0}}function zc(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return zc(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),de(i.concat(n).join("/"))}function Vc(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}class Fc{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(se(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&se(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=Yc(this.$el.querySelector(e));return t?Uc(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=Yc(n[o]);e&&t.push(Uc(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||y(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:I(n);(y(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(y(e)&&(e=F(e)),S(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];v(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&Lx.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function Uc(e,t=!0){if(t&&e&&(e=ae(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Fc(e)),e.$el.__wxsComponentDescriptor}function Wc(e,t){return Uc(e,t)}function Hc(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>Wc(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=ae(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,Wc(r,!1)]}}function Yc(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function Xc(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e;let a,s;a=xe(t?r:function(e){for(;e&&0!==e.tagName.indexOf("UNI-");)e=e.parentElement;return e}(r)),s=xe(i);const l={type:n,timeStamp:o,target:a,detail:{},currentTarget:s};return e._stopped&&(l._stopped=!0),e.type.startsWith("touch")&&(l.touches=e.touches,l.changedTouches=e.changedTouches),function(e,t){c(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(l,e),l}function Gc(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function Jc(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:a,clientX:s,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:a-t,clientX:s,clientY:l-t,force:c||0})}return n}const Qc=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=0!==o.tagName.indexOf("UNI-");if(r)return Hc(e,t,n,!1)||[e];const i=Xc(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=gc();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[Gc(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=gc();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Gc(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=gc();i.touches=Jc(e.touches,t),i.changedTouches=Jc(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(i,t,{get:()=>e[t]})}))}return Hc(i,t,n)||[i]},createNativeEvent:Xc},Symbol.toStringTag,{value:"Module"});function Kc(e){!function(e){const t=e.globalProperties;c(t,Qc),t.$gcd=Wc}(e._context.config)}let Zc=1;function eu(e){return(e||Oc())+".invokeViewApi"}const tu=c(Nl("view"),{invokeOnCallback:(e,t)=>Dx.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=Dx,a=o?Zc++:0;o&&r("invokeViewApi."+a,o,!0),i(eu(n),{id:a,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:a}=Dx,s=Zc++,l="invokeViewApi."+s;return r(l,n),a(eu(o),{id:s,name:e,args:t},o),()=>{i(l)}}});function nu(e){Rc(Pc(),"onResize",e),Dx.invokeOnCallback("onWindowResize",e)}function ou(e){const t=Pc();Rc(bv(),"onShow",e),Rc(t,"onShow")}function ru(){Rc(bv(),"onHide"),Rc(Pc(),"onHide")}const iu=["onPageScroll","onReachBottom"];function au(){iu.forEach((e=>Dx.subscribe(e,function(e){return(t,n)=>{Rc(parseInt(n),e,t)}}(e))))}function su(){!function(){const{on:e}=Dx;e("onResize",nu),e("onAppEnterForeground",ou),e("onAppEnterBackground",ru)}(),au()}function lu(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new Ae(this.$page.id)),e.eventChannel}}function cu(e){e._context.config.globalProperties.getOpenerEventChannel=lu}function uu(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function du(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${uni.upx2px(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function pu(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],a=t.option.transition,s=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,a=e.option,s=a.transition,l={},c=[];return i.forEach((e=>{let i=e.type,a=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?a=a.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(a=a.map(du)),n.indexOf(i)>=0&&(a.length=1),c.push(`${i}(${a.join(",")})`);else if(o.concat(r).includes(a[0])){i=a[0];const e=a[1];l[i]=r.includes(i)?du(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${s.duration}ms ${s.timingFunction} ${s.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=a.transformOrigin,l}(t);Object.keys(s).forEach((t=>{e.$el.style[t]=s[t]})),n+=1,n<r&&setTimeout(i,a.duration+a.delay)}setTimeout((()=>{i()}),0)}const fu={props:["animation"],watch:{animation:{deep:!0,handler(){pu(this)}}},mounted(){pu(this)}},hu=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(fu),gu(e)},gu=e=>(e.__reserved=!0,e.compatConfig={MODE:3},go(e));function mu(e){return e.__wwe=!0,e}function vu(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){let r;return r=xe(n),{type:o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const yu={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function bu(e){const t=ln(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function a(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function s(){r=!1,t.value&&i()}function l(){s(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:mu((function(e){e.touches.length>1||a(e)})),onMousedown:mu((function(e){r||(a(e),window.addEventListener("mouseup",l))})),onTouchend:mu((function(){s()})),onMouseup:mu((function(){r&&l()})),onTouchcancel:mu((function(){r=!1,t.value=!1,clearTimeout(n)}))}}}function _u(e,t){return y(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}const wu=bc("uf"),xu=hu({name:"Form",emits:["submit","reset"],setup(e,{slots:t,emit:n}){const o=ln(null);return function(e){const t=[];Jn(wu,{addField(e){t.push(e)},removeField(e){t.splice(t.indexOf(e),1)},submit(n){e("submit",n,{value:t.reduce(((e,t)=>{if(t.submit){const[n,o]=t.submit();n&&(e[n]=o)}return e}),Object.create(null))})},reset(n){t.forEach((e=>e.reset&&e.reset())),e("reset",n)}})}(vu(o,n)),()=>ti("uni-form",{ref:o},[ti("span",null,[t.default&&t.default()])],512)}});const Tu=bc("ul");function Su(e,t,n){const o=Ac();n&&!e||S(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&Lx.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?Lx.on(r,t[r]):e&&Lx.on(`uni-${r}-${o}-${e}`,t[r])}))}function ku(e,t,n){const o=Ac();n&&!e||S(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&Lx.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?Lx.off(r,t[r]):e&&Lx.off(`uni-${r}-${o}-${e}`,t[r])}))}const Cu=hu({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=ln(null),o=Qn(wu,!1),{hovering:r,binding:i}=bu(e);kl();const a=mu(((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;})),s=Qn(Tu,!1);return s&&(s.addHandler(a),qo((()=>{s.removeHandler(a)}))),function(e,t){Su(e.id,t),eo((()=>e.id),((e,n)=>{ku(n,t,!0),Su(e,t,!0)})),jo((()=>{ku(e.id,t)}))}(e,{"label-click":a}),()=>{const o=e.hoverClass,s=_u(e,"disabled"),l=_u(e,"loading"),c=_u(e,"plain"),u=o&&"none"!==o;return ti("uni-button",li({ref:n,onClick:a,id:e.id,class:u&&r.value?o:""},u&&i,s,l,c),[t.default&&t.default()],16,["onClick","id"])}}});function Eu(e){return e.$el}function Au(e){const{base:t}=__uniConfig.router;return 0===de(e).indexOf(t)?de(e):t+e}function Mu(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0===e.indexOf("./static/")||n&&0===e.indexOf("./"+n+"/"))&&(e=e.slice(1)),0===e.indexOf("/")){if(0!==e.indexOf("//"))return Au(e.slice(1));e="https:"+e}if(oe.test(e)||re.test(e)||0===e.indexOf("blob:"))return e;const o=Gm();return o.length?Au(zc(o[o.length-1].$page.route,e).slice(1)):e}const Pu=navigator.userAgent,Iu=/android/i.test(Pu),Ou=/iphone|ipad|ipod/i.test(Pu),Lu=Pu.match(/Windows NT ([\d|\d.\d]*)/i),$u=/Macintosh|Mac/i.test(Pu),Du=/Linux|X11/i.test(Pu),Ru=$u&&navigator.maxTouchPoints>0;function Bu(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function Nu(e){return e&&90===Math.abs(window.orientation)}function qu(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function ju(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function zu(e,t,n,o){Dx.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function Vu(e,t){const n={},{top:o,topWindowHeight:r}=mc();if(t.node){const t=e.tagName.split("-")[1];t&&(n.node=e.querySelector(t))}if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=be(e)),t.rect||t.size){const i=e.getBoundingClientRect();t.rect&&(n.left=i.left,n.right=i.right,n.top=i.top-o-r,n.bottom=i.bottom-o-r),t.size&&(n.width=i.width,n.height=i.height)}if(f(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){const t=e.children[0].children[0];n.scrollLeft=t.scrollLeft,n.scrollTop=t.scrollTop,n.scrollHeight=t.scrollHeight,n.scrollWidth=t.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(f(t.computedStyle)){const o=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=o[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function Fu(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){const t=this.parentElement.querySelectorAll(e);let n=t.length;for(;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}function Uu(e,t,n){const o=[];t.forEach((({component:t,selector:n,single:r,fields:i})=>{null===t?o.push(function(e){const t={};if(e.id&&(t.id=""),e.dataset&&(t.dataset={}),e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0),e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight),e.scrollOffset){const e=document.documentElement,n=document.body;t.scrollLeft=e.scrollLeft||n.scrollLeft||0,t.scrollTop=e.scrollTop||n.scrollTop||0,t.scrollHeight=e.scrollHeight||n.scrollHeight||0,t.scrollWidth=e.scrollWidth||n.scrollWidth||0}return t}(i)):o.push(function(e,t,n,o,r){const i=function(e,t){return e?e.$el:t.$el}(t,e),a=i.parentElement;if(!a)return o?null:[];const{nodeType:s}=i,l=3===s||8===s;if(o){const e=l?a.querySelector(n):Fu(i,n)?i:i.querySelector(n);return e?Vu(e,r):null}{let e=[];const t=(l?a:i).querySelectorAll(n);return t&&t.length&&[].forEach.call(t,(t=>{e.push(Vu(t,r))})),!l&&Fu(i,n)&&e.unshift(Vu(i,r)),e}}(e,t,n,r,i))})),n(o)}var Wu="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Hu=function(){const e=new Uint8Array(256);for(var t=0;t<Wu.length;t++)e[Wu.charCodeAt(t)]=t;return e}();const Yu=["original","compressed"],Xu=["album","camera"],Gu=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function Ju(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function Qu(e,t){return!f(e)||0===e.length||e.find((e=>-1===t.indexOf(e)))?t:e}function Ku(e){return function(){try{return e.apply(e,arguments)}catch(EC){console.error(EC)}}}let Zu=1;const ed={};function td(e,t,n,o=!1){return ed[e]={name:t,keepAlive:o,callback:n},e}function nd(e,t,n){if("number"==typeof e){const o=ed[e];if(o)return o.keepAlive||delete ed[e],o.callback(t,n)}return t}function od(e){for(const t in ed)if(ed[t].name===e)return!0;return!1}const rd="success",id="fail",ad="complete";function sd(e,t={},{beforeAll:n,beforeSuccess:o}={}){S(t)||(t={});const{success:r,fail:i,complete:a}=function(e){const t={};for(const n in e){const o=e[n];v(o)&&(t[n]=Ku(o),delete e[n])}return t}(t),s=v(r),l=v(i),c=v(a),u=Zu++;return td(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),v(n)&&n(u),u.errMsg===e+":ok"?(v(o)&&o(u,t),s&&r(u)):l&&i(u),c&&a(u)})),u}const ld="success",cd="fail",ud="complete",dd={},pd={};function fd(e,t){return function(n){return e(n,t)||n}}function hd(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(fd(i,n));else{const e=i(t,n);if(w(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function gd(e,t={}){return[ld,cd,ud].forEach((n=>{const o=e[n];if(!f(o))return;const r=t[n];t[n]=function(e){hd(o,e,t).then((e=>v(r)&&r(e)||e))}})),t}function md(e,t){const n=[];f(dd.returnValue)&&n.push(...dd.returnValue);const o=pd[e];return o&&f(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function vd(e){const t=Object.create(null);Object.keys(dd).forEach((e=>{"returnValue"!==e&&(t[e]=dd[e].slice())}));const n=pd[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function yd(e,t,n,o){const r=vd(e);if(r&&Object.keys(r).length){if(f(r.invoke)){return hd(r.invoke,n).then((n=>t(gd(vd(e),n),...o)))}return t(gd(r,n),...o)}return t(n,...o)}function bd(e,t){return(n={},...o)=>function(e){return!(!S(e)||![rd,id,ad].find((t=>v(e[t]))))}(n)?md(e,yd(e,t,n,o)):md(e,new Promise(((r,i)=>{yd(e,t,c(n,{success:r,fail:i}),o)})))}function _d(e,t,n,o={}){const r=t+":fail"+(n?" "+n:"");return delete o.errCode,nd(e,"undefined"!=typeof UniError?void 0!==o.errCode?new UniError(t,o.errCode,r):new UniError(r,o):c({errMsg:r},o))}function wd(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(y(e))return e}const r=function(e,t){const n=e[0];if(!t||!S(t.formatArgs)&&S(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],a=o[t];if(v(a)){const o=a(e[0][t],n);if(y(o))return o}else p(n,t)||(n[t]=a)}}(t,o);if(r)return r}function xd(e){if(!v(e))throw new Error('Invalid args: type check failed for args "callback". Expected Function')}function Td(e,t,n){return o=>{xd(o);const r=wd(0,[o],0,n);if(r)throw new Error(r);const i=!od(e);!function(e,t){td(Zu++,e,t,!0)}(e,o),i&&(!function(e){Dx.on("api."+e,(t=>{for(const n in ed){const o=ed[n];o.name===e&&o.callback(t)}}))}(e),t())}}function Sd(e,t,n){return o=>{xd(o);const r=wd(0,[o],0,n);if(r)throw new Error(r);!function(e,t){for(const n in ed){const o=ed[n];o.callback===t&&o.name===e&&delete ed[n]}}(e=e.replace("off","on"),o);od(e)||(!function(e){Dx.off("api."+e)}(e),t())}}function kd(e,t,n,o){return n=>{const r=sd(e,n,o),i=wd(0,[n],0,o);return i?_d(r,e,i):t(n,{resolve:t=>function(e,t,n){return nd(e,c(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>_d(r,e,function(e){return!e||y(e)?e:e.stack?(console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function Cd(e,t,n){return Td(e,t,n)}function Ed(e,t,n){return Sd(e,t,n)}function Ad(e,t,n,o){return bd(e,kd(e,t,0,o))}function Md(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=wd(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function Pd(e,t,n,o){return bd(e,function(e,t,n,o){return kd(e,t,0,o)}(e,t,0,o))}function Id(e){return`method 'uni.${e}' not supported`}function Od(e){return()=>{console.error(Id(e))}}const Ld=Od;function $d(e){return(t,{reject:n})=>n(Id(e))}const Dd=Md(0,(e=>function(e){var t,n,o,r,i,a=.75*e.length,s=e.length,l=0;"="===e[e.length-1]&&(a--,"="===e[e.length-2]&&a--);var c=new ArrayBuffer(a),u=new Uint8Array(c);for(t=0;t<s;t+=4)n=Hu[e.charCodeAt(t)],o=Hu[e.charCodeAt(t+1)],r=Hu[e.charCodeAt(t+2)],i=Hu[e.charCodeAt(t+3)],u[l++]=n<<2|o>>4,u[l++]=(15&o)<<4|r>>2,u[l++]=(3&r)<<6|63&i;return c}(e))),Rd=Md(0,(e=>function(e){var t,n=new Uint8Array(e),o=n.length,r="";for(t=0;t<o;t+=3)r+=Wu[n[t]>>2],r+=Wu[(3&n[t])<<4|n[t+1]>>4],r+=Wu[(15&n[t+1])<<2|n[t+2]>>6],r+=Wu[63&n[t+2]];return o%3==2?r=r.substring(0,r.length-1)+"=":o%3==1&&(r=r.substring(0,r.length-2)+"=="),r}(e)));let Bd=!1,Nd=0,qd=0,jd=960,zd=375,Vd=750;function Fd(){const{platform:e,pixelRatio:t,windowWidth:n}=function(){const e=Bu(),t=ju(qu(e,Nu(e)));return{platform:Ou?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();Nd=n,qd=t,Bd="ios"===e}function Ud(e,t){const n=Number(e);return isNaN(n)?t:n}const Wd=Md(0,((e,t)=>{if(0===Nd&&(Fd(),function(){const e=__uniConfig.globalStyle||{};jd=Ud(e.rpxCalcMaxDeviceWidth,960),zd=Ud(e.rpxCalcBaseDeviceWidth,375),Vd=Ud(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||Nd;n=e===Vd||n<=jd?n:zd;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==qd&&Bd?.5:1),e<0?-o:o}));function Hd(e,t){Object.keys(t).forEach((n=>{v(t[n])&&(e[n]=function(e,t){const n=t?e?e.concat(t):f(t)?t:[t]:e;return n?function(e){const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))}))}function Yd(e,t){e&&t&&Object.keys(t).forEach((n=>{const o=e[n],r=t[n];f(o)&&v(r)&&u(o,r)}))}const Xd=Md(0,((e,t)=>{y(e)&&S(t)?Hd(pd[e]||(pd[e]={}),t):S(e)&&Hd(dd,e)})),Gd=Md(0,((e,t)=>{y(e)?S(t)?Yd(pd[e],t):delete pd[e]:S(e)&&Yd(dd,e)})),Jd=new Re,Qd=Md(0,((e,t)=>(Jd.on(e,t),()=>Jd.off(e,t)))),Kd=Md(0,((e,t)=>(Jd.once(e,t),()=>Jd.off(e,t)))),Zd=Md(0,((e,t)=>{e?(f(e)||(e=[e]),e.forEach((e=>Jd.off(e,t)))):Jd.e={}})),ep=Md(0,((e,...t)=>{Jd.emit(e,...t)})),tp=[.5,.8,1,1.25,1.5,2];class np{constructor(e,t){this.id=e,this.pageId=t}play(){zu(this.id,this.pageId,"play")}pause(){zu(this.id,this.pageId,"pause")}stop(){zu(this.id,this.pageId,"stop")}seek(e){zu(this.id,this.pageId,"seek",{position:e})}sendDanmu(e){zu(this.id,this.pageId,"sendDanmu",e)}playbackRate(e){~tp.indexOf(e)||(e=1),zu(this.id,this.pageId,"playbackRate",{rate:e})}requestFullScreen(e={}){zu(this.id,this.pageId,"requestFullScreen",e)}exitFullScreen(){zu(this.id,this.pageId,"exitFullScreen")}showStatusBar(){zu(this.id,this.pageId,"showStatusBar")}hideStatusBar(){zu(this.id,this.pageId,"hideStatusBar")}}const op=Md(0,((e,t)=>new np(e,Mc(t||Lc())))),rp=(e,t,n,o)=>{!function(e,t,n,o,r){Dx.invokeViewMethod("map."+e,{type:n,data:o},t,r)}(e,t,n,o,(e=>{o&&((e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)})(o,e)}))};class ip{constructor(e,t){this.id=e,this.pageId=t}getCenterLocation(e){rp(this.id,this.pageId,"getCenterLocation",e)}moveToLocation(e){rp(this.id,this.pageId,"moveToLocation",e)}getScale(e){rp(this.id,this.pageId,"getScale",e)}getRegion(e){rp(this.id,this.pageId,"getRegion",e)}includePoints(e){rp(this.id,this.pageId,"includePoints",e)}translateMarker(e){rp(this.id,this.pageId,"translateMarker",e)}$getAppMap(){}addCustomLayer(e){rp(this.id,this.pageId,"addCustomLayer",e)}removeCustomLayer(e){rp(this.id,this.pageId,"removeCustomLayer",e)}addGroundOverlay(e){rp(this.id,this.pageId,"addGroundOverlay",e)}removeGroundOverlay(e){rp(this.id,this.pageId,"removeGroundOverlay",e)}updateGroundOverlay(e){rp(this.id,this.pageId,"updateGroundOverlay",e)}initMarkerCluster(e){rp(this.id,this.pageId,"initMarkerCluster",e)}addMarkers(e){rp(this.id,this.pageId,"addMarkers",e)}removeMarkers(e){rp(this.id,this.pageId,"removeMarkers",e)}moveAlong(e){rp(this.id,this.pageId,"moveAlong",e)}setLocMarkerIcon(e){rp(this.id,this.pageId,"setLocMarkerIcon",e)}openMapApp(e){rp(this.id,this.pageId,"openMapApp",e)}on(e,t){rp(this.id,this.pageId,"on",{name:e,callback:t})}}const ap=Md(0,((e,t)=>new ip(e,Mc(t||Lc()))));function sp(e,t){return function(n,o){n?o[e]=Math.round(n):void 0!==t&&(o[e]=t)}}const lp=sp("width"),cp=sp("height"),up={formatArgs:{x:sp("x"),y:sp("y"),width:lp,height:cp}},dp={canvasId:{type:String,required:!0},x:{type:Number,required:!0},y:{type:Number,required:!0},width:{type:Number,required:!0},height:{type:Number,required:!0}},pp=up,fp=(Uint8ClampedArray,{PNG:"png",JPG:"jpg",JPEG:"jpg"}),hp={formatArgs:{x:sp("x",0),y:sp("y",0),width:lp,height:cp,destWidth:sp("destWidth"),destHeight:sp("destHeight"),fileType(e,t){e=(e||"").toUpperCase();let n=fp[e];n||(n=fp.PNG),t.fileType=n},quality(e,t){t.quality=e&&e>0&&e<1?e:1}}};function gp(e,t,n,o,r){Dx.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,(e=>{r&&r(e)}))}var mp=["scale","rotate","translate","setTransform","transform"],vp=["drawImage","fillText","fill","stroke","fillRect","strokeRect","clearRect","strokeText"],yp=["setFillStyle","setTextAlign","setStrokeStyle","setGlobalAlpha","setShadow","setFontSize","setLineCap","setLineJoin","setLineWidth","setMiterLimit","setTextBaseline","setLineDash"];const bp={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function _p(e){var t=null;if(null!=(t=/^#([0-9|A-F|a-f]{6})$/.exec(e=e||"#000000"))){return[parseInt(t[1].slice(0,2),16),parseInt(t[1].slice(2,4),16),parseInt(t[1].slice(4),16),255]}if(null!=(t=/^#([0-9|A-F|a-f]{3})$/.exec(e))){let e=t[1].slice(0,1),n=t[1].slice(1,2),o=t[1].slice(2,3);return e=parseInt(e+e,16),n=parseInt(n+n,16),o=parseInt(o+o,16),[e,n,o,255]}if(null!=(t=/^rgb\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e){return Math.min(255,parseInt(e.trim()))})).concat(255);if(null!=(t=/^rgba\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e,t){return 3===t?Math.floor(255*parseFloat(e.trim())):Math.min(255,parseInt(e.trim()))}));var n=e.toLowerCase();if(p(bp,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(bp[n]);const e=parseInt(t[1].slice(0,2),16),o=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4,6),16);let i=parseInt(t[1].slice(6,8),16);return i=i>=0?i:255,[e,o,r,i]}return console.error("unsupported color:"+e),[0,0,0,255]}class wp{constructor(e,t){this.type=e,this.data=t,this.colorStop=[]}addColorStop(e,t){this.colorStop.push([e,_p(t)])}}class xp{constructor(e,t){this.type="pattern",this.data=e,this.colorStop=t}}class Tp{constructor(e){this.width=e}}class Sp{constructor(e,t){this.id=e,this.pageId=t,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}draw(e=!1,t){var n=[...this.actions];this.actions=[],this.path=[],gp(this.id,this.pageId,"actionsChanged",{actions:n,reserve:e},t)}createLinearGradient(e,t,n,o){return new wp("linear",[e,t,n,o])}createCircularGradient(e,t,n){return new wp("radial",[e,t,n])}createPattern(e,t){if(void 0===t)console.error("Failed to execute 'createPattern' on 'CanvasContext': 2 arguments required, but only 1 present.");else{if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(t)<0))return new xp(e,t);console.error("Failed to execute 'createPattern' on 'CanvasContext': The provided type ('"+t+"') is not one of 'repeat', 'no-repeat', 'repeat-x', or 'repeat-y'.")}}measureText(e){let t=0;return t=function(e,t){const n=document.createElement("canvas").getContext("2d");return n.font=t,n.measureText(e).width||0}(e,this.state.font),new Tp(t)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(e,t){this.path.push({method:"moveTo",data:[e,t]}),this.subpath=[[e,t]]}lineTo(e,t){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[e,t]}):this.path.push({method:"lineTo",data:[e,t]}),this.subpath.push([e,t])}quadraticCurveTo(e,t,n,o){this.path.push({method:"quadraticCurveTo",data:[e,t,n,o]}),this.subpath.push([n,o])}bezierCurveTo(e,t,n,o,r,i){this.path.push({method:"bezierCurveTo",data:[e,t,n,o,r,i]}),this.subpath.push([r,i])}arc(e,t,n,o,r,i=!1){this.path.push({method:"arc",data:[e,t,n,o,r,i]}),this.subpath.push([e,t])}rect(e,t,n,o){this.path.push({method:"rect",data:[e,t,n,o]}),this.subpath=[[e,t]]}arcTo(e,t,n,o,r){this.path.push({method:"arcTo",data:[e,t,n,o,r]}),this.subpath.push([n,o])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var e=[...this.actions];return this.clearActions(),e}set lineDashOffset(e){this.actions.push({method:"setLineDashOffset",data:[e]})}set globalCompositeOperation(e){this.actions.push({method:"setGlobalCompositeOperation",data:[e]})}set shadowBlur(e){this.actions.push({method:"setShadowBlur",data:[e]})}set shadowColor(e){this.actions.push({method:"setShadowColor",data:[e]})}set shadowOffsetX(e){this.actions.push({method:"setShadowOffsetX",data:[e]})}set shadowOffsetY(e){this.actions.push({method:"setShadowOffsetY",data:[e]})}set font(e){var t=this;this.state.font=e;var n=e.match(/^(([\w\-]+\s)*)(\d+r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var o=n[1].trim().split(/\s/),r=parseFloat(n[3]),i=n[7],a=[];o.forEach((function(e,n){["italic","oblique","normal"].indexOf(e)>-1?(a.push({method:"setFontStyle",data:[e]}),t.state.fontStyle=e):["bold","normal"].indexOf(e)>-1?(a.push({method:"setFontWeight",data:[e]}),t.state.fontWeight=e):0===n?(a.push({method:"setFontStyle",data:["normal"]}),t.state.fontStyle="normal"):1===n&&s()})),1===o.length&&s(),o=a.map((function(e){return e.data[0]})).join(" "),this.state.fontSize=r,this.state.fontFamily=i,this.actions.push({method:"setFont",data:[`${o} ${r}px ${i}`]})}else console.warn("Failed to set 'font' on 'CanvasContext': invalid format.");function s(){a.push({method:"setFontWeight",data:["normal"]}),t.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(e){this.setFillStyle(e)}set strokeStyle(e){this.setStrokeStyle(e)}set globalAlpha(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:"setGlobalAlpha",data:[e]})}set textAlign(e){this.actions.push({method:"setTextAlign",data:[e]})}set lineCap(e){this.actions.push({method:"setLineCap",data:[e]})}set lineJoin(e){this.actions.push({method:"setLineJoin",data:[e]})}set lineWidth(e){this.actions.push({method:"setLineWidth",data:[e]})}set miterLimit(e){this.actions.push({method:"setMiterLimit",data:[e]})}set textBaseline(e){this.actions.push({method:"setTextBaseline",data:[e]})}}const kp=fe((()=>{[...mp,...vp].forEach((function(e){Sp.prototype[e]=function(e){switch(e){case"fill":case"stroke":return function(){this.actions.push({method:e+"Path",data:[...this.path]})};case"fillRect":return function(e,t,n,o){this.actions.push({method:"fillPath",data:[{method:"rect",data:[e,t,n,o]}]})};case"strokeRect":return function(e,t,n,o){this.actions.push({method:"strokePath",data:[{method:"rect",data:[e,t,n,o]}]})};case"fillText":case"strokeText":return function(t,n,o,r){var i=[t.toString(),n,o];"number"==typeof r&&i.push(r),this.actions.push({method:e,data:i})};case"drawImage":return function(t,n,o,r,i,a,s,l,c){var u;function d(e){return"number"==typeof e}void 0===c&&(a=n,s=o,l=r,c=i,n=void 0,o=void 0,r=void 0,i=void 0),u=d(n)&&d(o)&&d(r)&&d(i)?[t,a,s,l,c,n,o,r,i]:d(l)&&d(c)?[t,a,s,l,c]:[t,a,s],this.actions.push({method:e,data:u})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)})),yp.forEach((function(e){Sp.prototype[e]=function(e){switch(e){case"setFillStyle":case"setStrokeStyle":return function(t){"object"!=typeof t?this.actions.push({method:e,data:["normal",_p(t)]}):this.actions.push({method:e,data:[t.type,t.data,t.colorStop]})};case"setGlobalAlpha":return function(t){t=Math.floor(255*parseFloat(t)),this.actions.push({method:e,data:[t]})};case"setShadow":return function(t,n,o,r){r=_p(r),this.actions.push({method:e,data:[t,n,o,r]}),this.state.shadowBlur=o,this.state.shadowColor=r,this.state.shadowOffsetX=t,this.state.shadowOffsetY=n};case"setLineDash":return function(t,n){t=t||[0,0],n=n||0,this.actions.push({method:e,data:[t,n]}),this.state.lineDash=t};case"setFontSize":return function(t){this.state.font=this.state.font.replace(/\d+\.?\d*px/,t+"px"),this.state.fontSize=t,this.actions.push({method:e,data:[t]})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)}))})),Cp=Md(0,((e,t)=>{if(kp(),t)return new Sp(e,Mc(t));const n=Mc(Lc());if(n)return new Sp(e,n);Dx.emit("onError","createCanvasContext:fail")})),Ep=Pd("canvasGetImageData",(({canvasId:e,x:t,y:n,width:o,height:r},{resolve:i,reject:a})=>{const s=Mc(Lc());s?gp(e,s,"getImageData",{x:t,y:n,width:o,height:r},(function(e){if(e.errMsg&&-1!==e.errMsg.indexOf("fail"))return void a("",e);let t=e.data;t&&t.length&&(e.data=new Uint8ClampedArray(t)),delete e.compressed,i(e)})):a()}),0,up),Ap=Pd("canvasPutImageData",(({canvasId:e,data:t,x:n,y:o,width:r,height:i},{resolve:a,reject:s})=>{var l=Mc(Lc());if(!l)return void s();t=Array.prototype.slice.call(t),gp(e,l,"putImageData",{data:t,x:n,y:o,width:r,height:i,compressed:void 0},(e=>{e.errMsg&&-1!==e.errMsg.indexOf("fail")?s():a(e)}))}),0,pp),Mp=Pd("canvasToTempFilePath",(({x:e=0,y:t=0,width:n,height:o,destWidth:r,destHeight:i,canvasId:a,fileType:s,quality:l},{resolve:c,reject:u})=>{var d=Mc(Lc());if(!d)return void u();gp(a,d,"toTempFilePath",{x:e,y:t,width:n,height:o,destWidth:r,destHeight:i,fileType:s,quality:l,dirname:`${wh}/canvas`},(e=>{e.errMsg&&-1!==e.errMsg.indexOf("fail")?u("",e):c(e)}))}),0,hp),Pp=["onCanplay","onPlay","onPause","onStop","onEnded","onTimeUpdate","onError","onWaiting","onSeeking","onSeeked"],Ip=["offCanplay","offPlay","offPause","offStop","offEnded","offTimeUpdate","offError","offWaiting","offSeeking","offSeeked"],Op={thresholds:[0],initialRatio:0,observeAll:!1},Lp=["top","right","bottom","left"];let $p=1;function Dp(e={}){return Lp.map((t=>`${Number(e[t])||0}px`)).join(" ")}class Rp{constructor(e,t){this._pageId=Mc(e),this._component=e,this._options=c({},Op,t)}relativeTo(e,t){return this._options.relativeToSelector=e,this._options.rootMargin=Dp(t),this}relativeToViewport(e){return this._options.relativeToSelector=void 0,this._options.rootMargin=Dp(e),this}observe(e,t){v(t)&&(this._options.selector=e,this._reqId=$p++,function({reqId:e,component:t,options:n,callback:o},r){const i=Eu(t);(i.__io||(i.__io={}))[e]=function(e,t,n){!function(){if("object"!=typeof window)return;if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)return void("isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}}));function e(e){try{return e.defaultView&&e.defaultView.frameElement||null}catch(EC){return null}}var t=function(t){for(var n=window.document,o=e(n);o;)o=e(n=o.ownerDocument);return n}(),n=[],o=null,r=null;function i(e){this.time=e.time,this.target=e.target,this.rootBounds=h(e.rootBounds),this.boundingClientRect=h(e.boundingClientRect),this.intersectionRect=h(e.intersectionRect||f()),this.isIntersecting=!!e.intersectionRect;var t=this.boundingClientRect,n=t.width*t.height,o=this.intersectionRect,r=o.width*o.height;this.intersectionRatio=n?Number((r/n).toFixed(4)):this.isIntersecting?1:0}function a(e,t){var n=t||{};if("function"!=typeof e)throw new Error("callback must be a function");if(n.root&&1!=n.root.nodeType&&9!=n.root.nodeType)throw new Error("root must be a Document or Element");this._checkForIntersections=l(this._checkForIntersections.bind(this),this.THROTTLE_TIMEOUT),this._callback=e,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(n.rootMargin),this.thresholds=this._initThresholds(n.threshold),this.root=n.root||null,this.rootMargin=this._rootMarginValues.map((function(e){return e.value+e.unit})).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}function s(){return window.performance&&performance.now&&performance.now()}function l(e,t){var n=null;return function(){n||(n=setTimeout((function(){e(),n=null}),t))}}function c(e,t,n,o){"function"==typeof e.addEventListener?e.addEventListener(t,n,o||!1):"function"==typeof e.attachEvent&&e.attachEvent("on"+t,n)}function u(e,t,n,o){"function"==typeof e.removeEventListener?e.removeEventListener(t,n,o||!1):"function"==typeof e.detatchEvent&&e.detatchEvent("on"+t,n)}function d(e,t){var n=Math.max(e.top,t.top),o=Math.min(e.bottom,t.bottom),r=Math.max(e.left,t.left),i=Math.min(e.right,t.right),a=i-r,s=o-n;return a>=0&&s>=0&&{top:n,bottom:o,left:r,right:i,width:a,height:s}||null}function p(e){var t;try{t=e.getBoundingClientRect()}catch(n){}return t?(t.width&&t.height||(t={top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:t.right-t.left,height:t.bottom-t.top}),t):f()}function f(){return{top:0,bottom:0,left:0,right:0,width:0,height:0}}function h(e){return!e||"x"in e?e:{top:e.top,y:e.top,bottom:e.bottom,left:e.left,x:e.left,right:e.right,width:e.width,height:e.height}}function g(e,t){var n=t.top-e.top,o=t.left-e.left;return{top:n,left:o,height:t.height,width:t.width,bottom:n+t.height,right:o+t.width}}function m(e,t){for(var n=t;n;){if(n==e)return!0;n=v(n)}return!1}function v(n){var o=n.parentNode;return 9==n.nodeType&&n!=t?e(n):(o&&o.assignedSlot&&(o=o.assignedSlot.parentNode),o&&11==o.nodeType&&o.host?o.host:o)}function y(e){return e&&9===e.nodeType}a.prototype.THROTTLE_TIMEOUT=100,a.prototype.POLL_INTERVAL=null,a.prototype.USE_MUTATION_OBSERVER=!0,a._setupCrossOriginUpdater=function(){return o||(o=function(e,t){r=e&&t?g(e,t):f(),n.forEach((function(e){e._checkForIntersections()}))}),o},a._resetCrossOriginUpdater=function(){o=null,r=null},a.prototype.observe=function(e){if(!this._observationTargets.some((function(t){return t.element==e}))){if(!e||1!=e.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:e,entry:null}),this._monitorIntersections(e.ownerDocument),this._checkForIntersections()}},a.prototype.unobserve=function(e){this._observationTargets=this._observationTargets.filter((function(t){return t.element!=e})),this._unmonitorIntersections(e.ownerDocument),0==this._observationTargets.length&&this._unregisterInstance()},a.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},a.prototype.takeRecords=function(){var e=this._queuedEntries.slice();return this._queuedEntries=[],e},a.prototype._initThresholds=function(e){var t=e||[0];return Array.isArray(t)||(t=[t]),t.sort().filter((function(e,t,n){if("number"!=typeof e||isNaN(e)||e<0||e>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return e!==n[t-1]}))},a.prototype._parseRootMargin=function(e){var t=(e||"0px").split(/\s+/).map((function(e){var t=/^(-?\d*\.?\d+)(px|%)$/.exec(e);if(!t)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(t[1]),unit:t[2]}}));return t[1]=t[1]||t[0],t[2]=t[2]||t[0],t[3]=t[3]||t[1],t},a.prototype._monitorIntersections=function(n){var o=n.defaultView;if(o&&-1==this._monitoringDocuments.indexOf(n)){var r=this._checkForIntersections,i=null,a=null;this.POLL_INTERVAL?i=o.setInterval(r,this.POLL_INTERVAL):(c(o,"resize",r,!0),c(n,"scroll",r,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in o&&(a=new o.MutationObserver(r)).observe(n,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this._monitoringDocuments.push(n),this._monitoringUnsubscribes.push((function(){var e=n.defaultView;e&&(i&&e.clearInterval(i),u(e,"resize",r,!0)),u(n,"scroll",r,!0),a&&a.disconnect()}));var s=this.root&&(this.root.ownerDocument||this.root)||t;if(n!=s){var l=e(n);l&&this._monitorIntersections(l.ownerDocument)}}},a.prototype._unmonitorIntersections=function(n){var o=this._monitoringDocuments.indexOf(n);if(-1!=o){var r=this.root&&(this.root.ownerDocument||this.root)||t;if(!this._observationTargets.some((function(t){var o=t.element.ownerDocument;if(o==n)return!0;for(;o&&o!=r;){var i=e(o);if((o=i&&i.ownerDocument)==n)return!0}return!1}))){var i=this._monitoringUnsubscribes[o];if(this._monitoringDocuments.splice(o,1),this._monitoringUnsubscribes.splice(o,1),i(),n!=r){var a=e(n);a&&this._unmonitorIntersections(a.ownerDocument)}}}},a.prototype._unmonitorAllIntersections=function(){var e=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0,this._monitoringUnsubscribes.length=0;for(var t=0;t<e.length;t++)e[t]()},a.prototype._checkForIntersections=function(){if(this.root||!o||r){var e=this._rootIsInDom(),t=e?this._getRootRect():f();this._observationTargets.forEach((function(n){var r=n.element,a=p(r),l=this._rootContainsTarget(r),c=n.entry,u=e&&l&&this._computeTargetAndRootIntersection(r,a,t),d=null;this._rootContainsTarget(r)?o&&!this.root||(d=t):d=f();var h=n.entry=new i({time:s(),target:r,boundingClientRect:a,rootBounds:d,intersectionRect:u});c?e&&l?this._hasCrossedThreshold(c,h)&&this._queuedEntries.push(h):c&&c.isIntersecting&&this._queuedEntries.push(h):this._queuedEntries.push(h)}),this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)}},a.prototype._computeTargetAndRootIntersection=function(e,n,i){if("none"!=window.getComputedStyle(e).display){for(var a=n,s=v(e),l=!1;!l&&s;){var c=null,u=1==s.nodeType?window.getComputedStyle(s):{};if("none"==u.display)return null;if(s==this.root||9==s.nodeType)if(l=!0,s==this.root||s==t)o&&!this.root?!r||0==r.width&&0==r.height?(s=null,c=null,a=null):c=r:c=i;else{var f=v(s),h=f&&p(f),m=f&&this._computeTargetAndRootIntersection(f,h,i);h&&m?(s=f,c=g(h,m)):(s=null,a=null)}else{var y=s.ownerDocument;s!=y.body&&s!=y.documentElement&&"visible"!=u.overflow&&(c=p(s))}if(c&&(a=d(c,a)),!a)break;s=s&&v(s)}return a}},a.prototype._getRootRect=function(){var e;if(this.root&&!y(this.root))e=p(this.root);else{var n=y(this.root)?this.root:t,o=n.documentElement,r=n.body;e={top:0,left:0,right:o.clientWidth||r.clientWidth,width:o.clientWidth||r.clientWidth,bottom:o.clientHeight||r.clientHeight,height:o.clientHeight||r.clientHeight}}return this._expandRectByRootMargin(e)},a.prototype._expandRectByRootMargin=function(e){var t=this._rootMarginValues.map((function(t,n){return"px"==t.unit?t.value:t.value*(n%2?e.width:e.height)/100})),n={top:e.top-t[0],right:e.right+t[1],bottom:e.bottom+t[2],left:e.left-t[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},a.prototype._hasCrossedThreshold=function(e,t){var n=e&&e.isIntersecting?e.intersectionRatio||0:-1,o=t.isIntersecting?t.intersectionRatio||0:-1;if(n!==o)for(var r=0;r<this.thresholds.length;r++){var i=this.thresholds[r];if(i==n||i==o||i<n!=i<o)return!0}},a.prototype._rootIsInDom=function(){return!this.root||m(t,this.root)},a.prototype._rootContainsTarget=function(e){var n=this.root&&(this.root.ownerDocument||this.root)||t;return m(n,e)&&(!this.root||n==e.ownerDocument)},a.prototype._registerInstance=function(){n.indexOf(this)<0&&n.push(this)},a.prototype._unregisterInstance=function(){var e=n.indexOf(this);-1!=e&&n.splice(e,1)},window.IntersectionObserver=a,window.IntersectionObserverEntry=i}();const o=t.relativeToSelector?e.querySelector(t.relativeToSelector):null,r=new IntersectionObserver((e=>{e.forEach((e=>{n({intersectionRatio:vh(e),intersectionRect:mh(e.intersectionRect),boundingClientRect:mh(e.boundingClientRect),relativeRect:mh(e.rootBounds),time:Date.now(),dataset:be(e.target),id:e.target.id})}))}),{root:o,rootMargin:t.rootMargin,threshold:t.thresholds});if(t.observeAll){r.USE_MUTATION_OBSERVER=!0;const n=e.querySelectorAll(t.selector);for(let e=0;e<n.length;e++)r.observe(n[e])}else{r.USE_MUTATION_OBSERVER=!1;const n=e.querySelector(t.selector);n?r.observe(n):console.warn(`Node ${t.selector} is not found. Intersection observer will not trigger.`)}return r}(i,n,o)}({reqId:this._reqId,component:this._component,options:this._options,callback:t},this._pageId))}disconnect(){this._reqId&&function({reqId:e,component:t},n){const o=Eu(t),r=o.__io&&o.__io[e];r&&(r.disconnect(),delete o.__io[e])}({reqId:this._reqId,component:this._component},this._pageId)}}const Bp=Md(0,((e,t)=>((e=ie(e))&&!Mc(e)&&(t=e,e=null),new Rp(e||Lc(),t))));let Np=1;class qp{constructor(e){this._pageId=e.$page&&e.$page.id,this._component=e}observe(e,t){v(t)&&(this._reqId=Np++,function({reqId:e,component:t,options:n,callback:o},r){const i=yh[e]=window.matchMedia(function(e){const t=[],n=["width","minWidth","maxWidth","height","minHeight","maxHeight","orientation"];for(const o of n)"orientation"!==o&&e[o]&&Number(e[o]>=0)&&t.push(`(${_h(o)}: ${Number(e[o])}px)`),"orientation"===o&&e[o]&&t.push(`(${_h(o)}: ${e[o]})`);return t.join(" and ")}(n)),a=bh[e]=e=>o(e.matches);a(i),i.addListener(a)}({reqId:this._reqId,component:this._component,options:e,callback:t},this._pageId))}disconnect(){this._reqId&&function({reqId:e,component:t},n){const o=bh[e],r=yh[e];r&&(r.removeListener(o),delete bh[e],delete yh[e])}({reqId:this._reqId,component:this._component},this._pageId)}}const jp=Md(0,(e=>((e=ie(e))&&!Mc(e)&&(e=null),new qp(e||Lc()))));let zp=0,Vp={};const Fp={canvas:Sp,map:ip,video:np,editor:class{constructor(e,t){this.id=e,this.pageId=t}format(e,t){this._exec("format",{name:e,value:t})}insertDivider(){this._exec("insertDivider")}insertImage(e){this._exec("insertImage",e)}insertText(e){this._exec("insertText",e)}setContents(e){this._exec("setContents",e)}getContents(e){this._exec("getContents",e)}clear(e){this._exec("clear",e)}removeFormat(e){this._exec("removeFormat",e)}undo(e){this._exec("undo",e)}redo(e){this._exec("redo",e)}blur(e){this._exec("blur",e)}getSelectionText(e){this._exec("getSelectionText",e)}scrollIntoView(e){this._exec("scrollIntoView",e)}_exec(e,t){!function(e,t,n,o){const r={options:o},i=o&&("success"in o||"fail"in o||"complete"in o);if(i){const e=String(zp++);r.callbackId=e,Vp[e]=o}Dx.invokeViewMethod(`editor.${e}`,{type:n,data:r},t,(({callbackId:e,data:t})=>{i&&(me(Vp[e],t),delete Vp[e])}))}(this.id,this.pageId,e,t)}}};function Up(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,r=Fp[n];e.context=new r(t,o),delete e.contextInfo}}class Wp{constructor(e,t,n,o){this._selectorQuery=e,this._component=t,this._selector=n,this._single=o}boundingClientRect(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},e),this._selectorQuery}fields(e,t){return this._selectorQuery._push(this._selector,this._component,this._single,e,t),this._selectorQuery}scrollOffset(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},e),this._selectorQuery}context(e){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},e),this._selectorQuery}node(e){return this._selectorQuery._push(this._selector,this._component,this._single,{node:!0},e),this._selectorQuery}}class Hp{constructor(e){this._component=void 0,this._page=e,this._queue=[],this._queueCb=[]}exec(e){return Uu(this._page,this._queue,(t=>{const n=this._queueCb;t.forEach(((e,t)=>{f(e)?e.forEach(Up):Up(e);const o=n[t];v(o)&&o.call(this,e)})),v(e)&&e.call(this,t)})),this._nodesRef}in(e){return this._component=ie(e),this}select(e){return this._nodesRef=new Wp(this,this._component,e,!0)}selectAll(e){return this._nodesRef=new Wp(this,this._component,e,!1)}selectViewport(){return this._nodesRef=new Wp(this,null,"",!0)}_push(e,t,n,o,r){this._queue.push({component:t,selector:e,single:n,fields:o}),this._queueCb.push(r)}}const Yp=Md(0,(e=>((e=ie(e))&&!Mc(e)&&(e=null),new Hp(e||Lc())))),Xp={formatArgs:{}},Gp={duration:400,timingFunction:"linear",delay:0,transformOrigin:"50% 50% 0"};class Jp{constructor(e){this.actions=[],this.currentTransform={},this.currentStepAnimates=[],this.option=c({},Gp,e)}_getOption(e){const t={transition:c({},this.option,e),transformOrigin:""};return t.transformOrigin=t.transition.transformOrigin,delete t.transition.transformOrigin,t}_pushAnimates(e,t){this.currentStepAnimates.push({type:e,args:t})}_converType(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}_getValue(e){return"number"==typeof e?`${e}px`:e}export(){const e=this.actions;return this.actions=[],{actions:e}}step(e){return this.currentStepAnimates.forEach((e=>{"style"!==e.type?this.currentTransform[e.type]=e:this.currentTransform[`${e.type}.${e.args[0]}`]=e})),this.actions.push({animates:Object.values(this.currentTransform),option:this._getOption(e)}),this.currentStepAnimates=[],this}}const Qp=fe((()=>{const e=["opacity","backgroundColor"],t=["width","height","left","right","top","bottom"];["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"].concat(e,t).forEach((n=>{Jp.prototype[n]=function(...o){return e.concat(t).includes(n)?this._pushAnimates("style",[this._converType(n),t.includes(n)?this._getValue(o[0]):o[0]]):this._pushAnimates(n,o),this}}))})),Kp=Md(0,(e=>(Qp(),new Jp(e))),0,Xp),Zp=Cd("onTabBarMidButtonTap",(()=>{})),ef=Cd("onWindowResize",(()=>{})),tf=Ed("offWindowResize",(()=>{})),nf=Md(0,(()=>{const e=bv();return e&&e.$vm?e.$vm.$locale:kl().getLocale()})),of=Cd("onLocaleChange",(()=>{})),rf=Md(0,(e=>{const t=bv();if(!t)return!1;return t.$vm.$locale!==e&&(t.$vm.$locale=e,navigator.cookieEnabled&&window.localStorage&&(localStorage.UNI_LOCALE=e),Dx.invokeOnCallback("onLocaleChange",{locale:e}),!0)})),af=Pd("setPageMeta",((e,{resolve:t})=>{t(function(e,{pageStyle:t,rootFontSize:n}){t&&(document.querySelector("uni-page-body")||document.body).setAttribute("style",t);n&&document.documentElement.style.fontSize!==n&&(document.documentElement.style.fontSize=n)}(Lc(),e))})),sf=Pd("getSelectedTextRange",((e,{resolve:t,reject:n})=>{Dx.invokeViewMethod("getSelectedTextRange",{},Oc(),(e=>{void 0===e.end&&void 0===e.start?n("no focused"):t(e)}))})),lf={onUnhandledRejection:[],onPageNotFound:[],onError:[],onShow:[],onHide:[]};function cf(e,t){const n=bv();if(n&&n.$vm)return Lo(e,t,n.$vm.$);lf[e].push(t)}function uf(e,t){const n=bv();if(n&&n.$vm)return function(e,t,n){const o=e.$[t];f(o)&&n.__weh&&u(o,n.__weh)}(n.$vm,e,t);u(lf[e],t)}const df=Md(0,(()=>Mh())),pf=Md(0,(()=>c({},Eh)));let ff,hf,gf;function mf(e){try{return JSON.parse(e)}catch(EC){}return e}const vf=[];function yf(e,t){vf.forEach((n=>{n(e,t)})),vf.length=0}const bf=Pd("getPushClientId",((e,{resolve:t,reject:n})=>{Promise.resolve().then((()=>{void 0===gf&&(gf=!1,ff="",hf="uniPush is not enabled"),vf.push(((e,o)=>{e?t({cid:e}):n(o)})),void 0!==ff&&yf(ff,hf)}))})),_f=[],wf={formatArgs:{showToast:!0},beforeInvoke(){Ll()},beforeSuccess(e,t){if(!t.showToast)return;const{t:n}=kl(),o=n("uni.setClipboardData.success");o&&uni.showToast({title:o,icon:"success",mask:!1})}},xf=(Boolean,{formatArgs:{filePath(e,t){t.filePath=Mu(e)}}}),Tf={formatArgs:{filePath(e,t){t.filePath=Mu(e)}}},Sf=["wgs84","gcj02"],kf={formatArgs:{type(e,t){e=(e||"").toLowerCase(),-1===Sf.indexOf(e)?t.type=Sf[0]:t.type=e},altitude(e,t){t.altitude=e||!1}}},Cf=(Boolean,(e,t)=>{if(void 0===t)return`${e} should not be empty.`;if("number"!=typeof t){let e=typeof t;return e=e[0].toUpperCase()+e.substring(1),`Expected Number, got ${e} with value ${JSON.stringify(t)}.`}}),Ef={formatArgs:{latitude(e,t){const n=Cf("latitude",e);if(n)return n;t.latitude=e},longitude(e,t){const n=Cf("longitude",e);if(n)return n;t.longitude=e},scale(e,t){e=Math.floor(e),t.scale=e>=5&&e<=18?e:18}}},Af={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=Qu(e,Yu)},sourceType(e,t){t.sourceType=Qu(e,Xu)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Mf={formatArgs:{sourceType(e,t){t.sourceType=Qu(e,Xu)},compressed:!0,maxDuration:60,camera:"back",extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Pf=(Boolean,["all","image","video"]),If={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=100)},sourceType(e,t){t.sourceType=Qu(e,Xu)},type(e,t){t.type=Ju(e,Pf)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=[""])}}},Of={formatArgs:{src(e,t){t.src=Mu(e)}}},Lf={formatArgs:{urls(e,t){t.urls=e.map((e=>y(e)&&e?Mu(e):""))},current(e,t){"number"==typeof e?t.current=e>0&&e<t.urls.length?e:0:y(e)&&e&&(t.current=Mu(e))}}},$f={formatArgs:{src(e,t){t.src=Mu(e)}}},Df="json",Rf=["text","arraybuffer"],Bf=encodeURIComponent;ArrayBuffer,Boolean;const Nf={formatArgs:{method(e,t){t.method=Ju((e||"").toUpperCase(),Gu)},data(e,t){t.data=e||""},url(e,t){t.method===Gu[0]&&S(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),a={};i.forEach((e=>{const t=e.split("=");a[t[0]]=t[1]}));for(const s in t)if(p(t,s)){let e=t[s];null==e?e="":S(e)&&(e=JSON.stringify(e)),a[Bf(s)]=Bf(e)}return r=Object.keys(a).map((e=>`${e}=${a[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==Gu[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||Df).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===Rf.indexOf(t.responseType)&&(t.responseType="text")}}},qf={formatArgs:{header(e,t){t.header=e||{}}}},jf={formatArgs:{filePath(e,t){e&&(t.filePath=Mu(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}},zf={formatArgs:{header(e,t){t.header=e||{}},method(e,t){t.method=Ju((e||"").toUpperCase(),Gu)},protocols(e,t){y(e)&&(t.protocols=[e])}}},Vf=["wgs84","gcj02"],Ff={formatArgs:{type(e,t){e=(e||"").toLowerCase(),-1===Vf.indexOf(e)?t.type=Vf[1]:t.type=e}}};const Uf={url:{type:String,required:!0}},Wf=(Jf(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),Jf(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),Zf("navigateTo")),Hf=Zf("redirectTo"),Yf=Zf("reLaunch"),Xf=Zf("switchTab"),Gf={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(Gm().length-1,e)}}};function Jf(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let Qf;function Kf(){Qf=""}function Zf(e){return{formatArgs:{url:eh(e)},beforeAll:Kf}}function eh(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/"))return e;let t="";const n=Gm();return n.length&&(t=n[n.length-1].$page.route),zc(t,e)}(t)).split("?")[0],r=Vc(o,!0);if(!r)return"page `"+t+"` is not found";if("navigateTo"===e||"redirectTo"===e){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if("switchTab"===e&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if("switchTab"!==e&&"preloadPage"!==e||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!y(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))})),o.length?e+"?"+o.join("&"):e}(t),"unPreloadPage"!==e)if("preloadPage"!==e){if(Qf===t&&"appLaunch"!==n.openType)return`${Qf} locked`;__uniConfig.ready&&(Qf=t)}else if(r.meta.isTabBar){const e=Gm(),t=r.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}const th={formatArgs:{animation(e,t){e||(e={duration:0,timingFunc:"linear"}),t.animation={duration:e.duration||0,timingFunc:e.timingFunc||"linear"}}}},nh={formatArgs:{duration:300}},oh={formatArgs:{itemColor:"#000"}},rh=(Boolean,{formatArgs:{title:"",mask:!1}}),ih=(Boolean,{beforeInvoke(){Il()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!p(t,"cancelText")){const{t:e}=kl();t.cancelText=e("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!p(t,"confirmText")){const{t:e}=kl();t.confirmText=e("uni.showModal.confirm")}},confirmColor:"#007aff"}}),ah=["success","loading","none","error"],sh=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=Ju(e,ah)},image(e,t){t.image=e?Mu(e):""},duration:1500,mask:!1}}),lh={beforeInvoke(){const e=Ic();if(e&&!e.isTabBar)return"not TabBar page"},formatArgs:{index(e){if(!__uniConfig.tabBar.list[e])return"tabbar item not found"}}},ch={beforeInvoke:lh.beforeInvoke,formatArgs:c({pagePath(e,t){e&&(t.pagePath=pe(e))}},lh.formatArgs)},uh=/^(linear|radial)-gradient\(.+?\);?$/,dh={beforeInvoke:lh.beforeInvoke,formatArgs:{backgroundImage(e,t){e&&!uh.test(e)&&(t.backgroundImage=Mu(e))},borderStyle(e,t){e&&(t.borderStyle="white"===e?"white":"black")}}},ph=lh,fh=lh,hh=lh,gh={beforeInvoke:lh.beforeInvoke,formatArgs:c({text(e,t){(function(e=""){return(""+e).replace(/[^\x00-\xff]/g,"**").length})(e)>=4&&(t.text="...")}},lh.formatArgs)};function mh(e){const{bottom:t,height:n,left:o,right:r,top:i,width:a}=e||{};return{bottom:t,height:n,left:o,right:r,top:i,width:a}}function vh(e){const{intersectionRatio:t,boundingClientRect:{height:n,width:o},intersectionRect:{height:r,width:i}}=e;return 0!==t?t:r===n?i/o:r/n}let yh={},bh={};function _h(e){return e.replace(/([A-Z])/g,"-$1").toLowerCase()}const wh="",xh={};function Th(e,t){const n=xh[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(function(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",r=atob(t[1]);let i=r.length;const a=new Uint8Array(i);for(;i--;)a[i]=r.charCodeAt(i);return Sh(a,o)}(e)):t?Promise.reject(new Error("not find")):new Promise(((t,n)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){t(this.response)},o.onerror=n,o.send()}))}function Sh(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const r=`${Date.now()}${function(e){const t=e.split("/")[1];return t?`.${t}`:""}(t)}`;try{n=new File([e],r,{type:t})}catch(o){n=e=e instanceof Blob?e:new Blob([e],{type:t}),n.name=n.name||r}}return n}function kh(e){for(const n in xh)if(p(xh,n)){if(xh[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return xh[t]=e,t}function Ch(e){(window.URL||window.webkitURL).revokeObjectURL(e),delete xh[e]}const Eh=uu(),Ah=uu();function Mh(){return c({},Ah)}const Ph=hu({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=ln(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=Yt({width:-1,height:-1});return eo((()=>c({},o)),(e=>t("resize",e))),()=>{const t=e.value;o.width=t.offsetWidth,o.height=t.offsetHeight,n()}}(n,t,o);return function(e,t,n,o){So(o),Ro((()=>{t.initial&&Mn(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,r,o),()=>ti("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[ti("div",{onScroll:r},[ti("div",null,null)],40,["onScroll"]),ti("div",{onScroll:r},[ti("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});function Ih(){}const Oh={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function Lh(e,t,n){function o(e){const t=xi((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(undefined),document.addEventListener("click",Ih,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",Ih,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}eo((()=>t.value),(e=>e&&o(e)))}var $h=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,Dh=/^<\/([-A-Za-z0-9_]+)[^>]*>/,Rh=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,Bh=Fh("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),Nh=Fh("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),qh=Fh("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),jh=Fh("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),zh=Fh("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),Vh=Fh("script,style");function Fh(e){for(var t={},n=e.split(","),o=0;o<n.length;o++)t[n[o]]=!0;return t}const Uh={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},Wh={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},Hh={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},Yh=hu({name:"Image",props:Uh,setup(e,{emit:t}){const n=ln(null),o=function(e,t){const n=ln(""),o=xi((()=>{let e="auto",o="";const r=Hh[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),r=Yt({rootEl:e,src:xi((()=>t.src?Mu(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return Ro((()=>{const t=e.value.style;r.origWidth=Number(t.width)||0,r.origHeight=Number(t.height)||0})),r}(n,e),r=vu(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=Wh[o];if(!r)return;const{origWidth:i,origHeight:a}=n,s=i&&a?i/a:0;if(!s)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){Xh&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,s))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return eo((()=>t.mode),((e,t)=>{Wh[t]&&r(),Wh[e]&&o()})),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,a;const s=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void s();i=i||new Image,i.onload=e=>{const{width:u,height:d}=i;s(u,d,l),o(),i.draggable=t.draggable,a&&a.remove(),a=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:d})},i.onerror=t=>{s(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};eo((()=>e.src),(e=>l(e))),eo((()=>e.imgSrc),(e=>{!e&&a&&(a.remove(),a=null)})),Ro((()=>l(e.src))),qo((()=>c()))}(o,e,n,i,r),()=>ti("uni-image",{ref:n},[ti("div",{style:o.modeStyle},null,4),Wh[e.mode]?ti(Ph,{onResize:i},null,8,["onResize"]):ti("span",null,null)],512)}});const Xh="Google Inc."===navigator.vendor;const Gh=we(!0),Jh=[];let Qh,Kh=0;const Zh=e=>Jh.forEach((t=>t.userAction=e));function eg(e={userAction:!1}){if(!Qh){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!Kh&&Zh(!0),Kh++,setTimeout((()=>{!--Kh&&Zh(!1)}),0)}),Gh)})),Qh=!0}Jh.push(e)}const tg=()=>!!Kh;function ng(){const e=Yt({userAction:!1});return Ro((()=>{eg(e)})),qo((()=>{!function(e){const t=Jh.indexOf(e);t>=0&&Jh.splice(t,1)}(e)})),{state:e}}function og(){const e=Yt({attrs:{}});return Ro((()=>{let t=fi();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}function rg(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}function ig(e,t){return"number"===t&&isNaN(Number(e))&&(e=""),null===e?"":String(e)}const ag=["none","text","decimal","numeric","tel","search","email","url"],sg=c({},{name:{type:String,default:""},modelValue:{type:[String,Number],default:""},value:{type:[String,Number],default:""},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~ag.indexOf(e)},cursorColor:{type:String,default:""}},Oh),lg=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function cg(e,t,n,o){const r=Ee((n=>{t.value=ig(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout});eo((()=>e.modelValue),r),eo((()=>e.value),r);const i=function(e,t){let n,o,r=0;const i=function(...i){const a=Date.now();clearTimeout(n),o=()=>{o=null,r=a,e.apply(this,i)},a-r<t?n=setTimeout(o,t-(a-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}(((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return Do((()=>{r.cancel(),i.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function ug(e,t){ng();const n=xi((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}eo((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),Ro((()=>{n.value&&Mn(o)}))}function dg(e,t,n,o){Vl(Oc(),"getSelectedTextRange",rg);const{fieldRef:r,state:i,trigger:a}=function(e,t,n){const o=ln(null),r=vu(t,n),i=xi((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),a=xi((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),s=xi((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=xi((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t})),c=ig(e.modelValue,e.type)||ig(e.value,e.type),u=Yt({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:a,cursor:s});return eo((()=>u.focus),(e=>n("update:focus",e))),eo((()=>u.maxlength),(e=>u.value=u.value.slice(0,e))),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:s}=cg(e,i,n,a);ug(e,r),Lh(0,r);const{state:l}=og();!function(e,t){const n=Qn(wu,!1);if(!n)return;const o=fi(),r={submit(){const n=o.proxy;return[n[e],y(t)?n[t]:t.value]},reset(){y(t)?o.proxy[t]="":t.value=""}};n.addField(r),qo((()=>{n.removeField(r)}))}("name",i),function(e,t,n,o,r,i){function a(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function s(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}eo([()=>t.selectionStart,()=>t.selectionEnd],a),eo((()=>t.cursor),s),eo((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),v(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function d(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),a(),s()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,d(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),d(e)})),c.addEventListener("compositionupdate",d)}))}(r,i,e,a,s,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:a}}const pg=hu({name:"Input",props:c({},sg,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),emits:["confirm",...lg],setup(e,{emit:t,expose:n}){const o=["text","number","idcard","digit","password","tel"],r=["off","one-time-code"],i=xi((()=>{let t="";switch(e.type){case"text":"search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=~o.includes(e.type)?e.type:"text"}return e.password?"password":t})),a=xi((()=>{const t=r.indexOf(e.textContentType),n=r.indexOf(I(e.textContentType));return r[-1!==t?t:-1!==n?n:0]}));let s,l=ln("");const c=ln(null),{fieldRef:u,state:d,scopedAttrsState:p,fixDisabledColor:f,trigger:h}=dg(e,c,t,((e,t)=>{const n=e.target;if("number"===i.value){if(s&&(n.removeEventListener("blur",s),s=null),n.validity&&!n.validity.valid){if((!l.value||!n.value)&&"-"===e.data||"-"===l.value[0]&&"deleteContentBackward"===e.inputType)return l.value="-",t.value="",s=()=>{l.value=n.value=""},n.addEventListener("blur",s),!1;if(l.value)if(-1!==l.value.indexOf(".")){if("."!==e.data&&"deleteContentBackward"===e.inputType){const e=l.value.indexOf(".");return l.value=n.value=t.value=l.value.slice(0,e),!0}}else if("."===e.data)return l.value+=".",s=()=>{l.value=n.value=l.value.slice(0,-1)},n.addEventListener("blur",s),!1;return l.value=t.value=n.value="-"===l.value?"":l.value,!1}l.value=n.value;const o=t.maxlength;if(o>0&&n.value.length>o)return n.value=n.value.slice(0,o),t.value=n.value,!1}}));eo((()=>d.value),(t=>{"number"!==e.type||"-"===l.value&&""===t||(l.value=t)}));const g=["number","digit"],m=xi((()=>g.includes(e.type)?e.step:""));function v(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),h("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),d.value=e.value}}),()=>{let t=e.disabled&&f?ti("input",{key:"disabled-input",ref:u,value:d.value,tabindex:"-1",readonly:!!e.disabled,type:i.value,maxlength:d.maxlength,step:m.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:e=>e.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):Wo(ti("input",{key:"input",ref:u,"onUpdate:modelValue":e=>d.value=e,disabled:!!e.disabled,type:i.value,maxlength:d.maxlength,step:m.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:a.value,onKeyup:v,inputmode:e.inputmode},null,44,["onUpdate:modelValue","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]),[[_a,d.value]]);return ti("uni-input",{ref:c},[ti("div",{class:"uni-input-wrapper"},[Wo(ti("div",li(p.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[ka,!(d.value.length||"-"===l.value)]]),"search"===e.confirmType?ti("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});const fg=["class","style"],hg=/^on[A-Z]+/,gg=(e={})=>{const{excludeListeners:t=!1,excludeKeys:n=[]}=e,o=fi(),r=cn({}),i=cn({}),a=cn({}),s=n.concat(fg);return o.attrs=Yt(o.attrs),Kn((()=>{const e=(n=o.attrs,Object.keys(n).map((e=>[e,n[e]]))).reduce(((e,[n,o])=>(s.includes(n)?e.exclude[n]=o:hg.test(n)?(t||(e.attrs[n]=o),e.listeners[n]=o):e.attrs[n]=o,e)),{exclude:{},attrs:{},listeners:{}});var n;r.value=e.attrs,i.value=e.listeners,a.value=e.exclude})),{$attrs:r,$listeners:i,$excludeAttrs:a}};function mg(e){const t=[];return f(e)&&e.forEach((e=>{Gr(e)?e.type===Br?t.push(...mg(e.children)):t.push(e):f(e)&&t.push(...mg(e))})),t}const vg=hu({inheritAttrs:!1,name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},setup(e,{slots:t}){const n=ln(null),o=ln(!1);let{setContexts:r,events:i}=function(e,t){const n=ln(0),o=ln(0),r=Yt({x:null,y:null}),i=ln(null);let a=null,s=[];function l(t){t&&1!==t&&(e.scaleArea?s.forEach((function(e){e._setScale(t)})):a&&a._setScale(t))}function c(e,n=s){let o=t.value;function r(e){for(let t=0;t<n.length;t++){const o=n[t];if(e===o.rootRef.value)return o}return e===o||e===document.body||e===document?null:r(e.parentNode)}return r(e)}const u=mu((t=>{let n=t.touches;if(n&&n.length>1){let t={x:n[1].pageX-n[0].pageX,y:n[1].pageY-n[0].pageY};if(i.value=yg(t),r.x=t.x,r.y=t.y,!e.scaleArea){let e=c(n[0].target),t=c(n[1].target);a=e&&e===t?e:null}}})),d=mu((e=>{let t=e.touches;if(t&&t.length>1){e.preventDefault();let n={x:t[1].pageX-t[0].pageX,y:t[1].pageY-t[0].pageY};if(null!==r.x&&i.value&&i.value>0){l(yg(n)/i.value)}r.x=n.x,r.y=n.y}})),p=mu((t=>{let n=t.touches;n&&n.length||t.changedTouches&&(r.x=0,r.y=0,i.value=null,e.scaleArea?s.forEach((function(e){e._endScale()})):a&&a._endScale())}));function f(){h(),s.forEach((function(e,t){e.setParent()}))}function h(){let e=window.getComputedStyle(t.value),r=t.value.getBoundingClientRect();n.value=r.width-["Left","Right"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0),o.value=r.height-["Top","Bottom"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0)}return Jn("movableAreaWidth",n),Jn("movableAreaHeight",o),{setContexts(e){s=e},events:{_onTouchstart:u,_onTouchmove:d,_onTouchend:p,_resize:f}}}(e,n);const{$listeners:a,$attrs:s,$excludeAttrs:l}=gg(),c=a.value;["onTouchstart","onTouchmove","onTouchend"].forEach((e=>{let t=c[e],n=i[`_${e}`];c[e]=t?[].concat(t,n):n})),Ro((()=>{i._resize(),o.value=!0}));let u=[];const d=[];function p(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n=n.el;const o=d.find((e=>n===e.rootRef.value));o&&e.push(tn(o))}r(e)}return Jn("_isMounted",o),Jn("movableAreaRootRef",n),Jn("addMovableViewContext",(e=>{d.push(e),p()})),Jn("removeMovableViewContext",(e=>{const t=d.indexOf(e);t>=0&&(d.splice(t,1),p())})),()=>{const e=t.default&&t.default();return u=mg(e),ti("uni-movable-area",li({ref:n},s.value,l.value,c),[ti(Ph,{onResize:i._resize},null,8,["onResize"]),u],16)}}});function yg(e){return Math.sqrt(e.x*e.x+e.y*e.y)}const bg=function(e,t,n,o){e.addEventListener(t,(e=>{v(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};let _g,wg;function xg(e,t,n){qo((()=>{document.removeEventListener("mousemove",_g),document.removeEventListener("mouseup",wg)}));let o=0,r=0,i=0,a=0;const s=function(e,n,s,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:s,y:l,dx:s-o,dy:l-r,ddx:s-i,ddy:l-a,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;bg(e,"touchstart",(function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=i=e.touches[0].pageX,r=a=e.touches[0].pageY,s(e,"start",o,r)})),bg(e,"mousedown",(function(e){if(c=!0,!l&&!u)return u=e,o=i=e.pageX,r=a=e.pageY,s(e,"start",o,r)})),bg(e,"touchmove",(function(e){if(1===e.touches.length&&u){const t=s(e,"move",e.touches[0].pageX,e.touches[0].pageY);return i=e.touches[0].pageX,a=e.touches[0].pageY,t}}));const d=_g=function(e){if(!l&&c&&u){const t=s(e,"move",e.pageX,e.pageY);return i=e.pageX,a=e.pageY,t}};document.addEventListener("mousemove",d),bg(e,"touchend",(function(e){if(0===e.touches.length&&u)return l=!1,u=null,s(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));const p=wg=function(e){if(c=!1,!l&&u)return u=null,s(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",p),bg(e,"touchcancel",(function(e){if(u){l=!1;const t=u;return u=null,s(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}function Tg(e,t,n){return e>t-n&&e<t+n}function Sg(e,t){return Tg(e,0,t)}function kg(){}function Cg(e,t){this._m=e,this._f=1e3*t,this._startTime=0,this._v=0}function Eg(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}function Ag(e,t,n){this._springX=new Eg(e,t,n),this._springY=new Eg(e,t,n),this._springScale=new Eg(e,t,n),this._startTime=0}kg.prototype.x=function(e){return Math.sqrt(e)},Cg.prototype.setV=function(e,t){const n=Math.pow(Math.pow(e,2)+Math.pow(t,2),.5);this._x_v=e,this._y_v=t,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(e/this._x_a)||Math.abs(t/this._y_a),this._lastDt=null,this._startTime=(new Date).getTime()},Cg.prototype.setS=function(e,t){this._x_s=e,this._y_s=t},Cg.prototype.s=function(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t,this._lastDt=e);let t=this._x_v*e+.5*this._x_a*Math.pow(e,2)+this._x_s,n=this._y_v*e+.5*this._y_a*Math.pow(e,2)+this._y_s;return(this._x_a>0&&t<this._endPositionX||this._x_a<0&&t>this._endPositionX)&&(t=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:t,y:n}},Cg.prototype.ds=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t),{dx:this._x_v+this._x_a*e,dy:this._y_v+this._y_a*e}},Cg.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},Cg.prototype.dt=function(){return-this._x_v/this._x_a},Cg.prototype.done=function(){const e=Tg(this.s().x,this._endPositionX)||Tg(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,e},Cg.prototype.setEnd=function(e,t){this._endPositionX=e,this._endPositionY=t},Cg.prototype.reconfigure=function(e,t){this._m=e,this._f=1e3*t},Eg.prototype._solve=function(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,a=t/(r*e);return{x:function(e){return(i+a*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+a*e)*t+a*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),a=(-n+Math.sqrt(i))/(2*o),s=(t-r*e)/(a-r),l=e-s;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*t+s*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*r*t+s*a*n}}}const a=Math.sqrt(4*o*r-n*n)/(2*o),s=-n/2*o,l=e,c=(t-s*e)/a;return{x:function(e){return Math.pow(Math.E,s*e)*(l*Math.cos(a*e)+c*Math.sin(a*e))},dx:function(e){const t=Math.pow(Math.E,s*e),n=Math.cos(a*e),o=Math.sin(a*e);return t*(c*a*n-l*a*o)+s*t*(c*o+l*n)}}},Eg.prototype.x=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0},Eg.prototype.dx=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0},Eg.prototype.setEnd=function(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!Sg(t,.1)){t=t||0;let o=this._endPosition;this._solution&&(Sg(t,.1)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),Sg(t,.1)&&(t=0),Sg(o,.1)&&(o=0),o+=this._endPosition),this._solution&&Sg(o-e,.1)&&Sg(t,.1)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}},Eg.prototype.snap=function(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}},Eg.prototype.done=function(e){return e||(e=(new Date).getTime()),Tg(this.x(),this._endPosition,.1)&&Sg(this.dx(),.1)},Eg.prototype.reconfigure=function(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},Eg.prototype.springConstant=function(){return this._k},Eg.prototype.damping=function(){return this._c},Eg.prototype.configuration=function(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]},Ag.prototype.setEnd=function(e,t,n,o){const r=(new Date).getTime();this._springX.setEnd(e,o,r),this._springY.setEnd(t,o,r),this._springScale.setEnd(n,o,r),this._startTime=r},Ag.prototype.x=function(){const e=((new Date).getTime()-this._startTime)/1e3;return{x:this._springX.x(e),y:this._springY.x(e),scale:this._springScale.x(e)}},Ag.prototype.done=function(){const e=(new Date).getTime();return this._springX.done(e)&&this._springY.done(e)&&this._springScale.done(e)},Ag.prototype.reconfigure=function(e,t,n){this._springX.reconfigure(e,t,n),this._springY.reconfigure(e,t,n),this._springScale.reconfigure(e,t,n)};function Mg(e,t){return+((1e3*e-1e3*t)/1e3).toFixed(1)}const Pg=hu({name:"MovableView",props:{direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.5},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}},emits:["change","scale"],setup(e,{slots:t,emit:n}){const o=ln(null),r=vu(o,n),{setParent:i}=function(e,t,n){const o=Qn("_isMounted",ln(!1)),r=Qn("addMovableViewContext",(()=>{})),i=Qn("removeMovableViewContext",(()=>{}));let a,s,l=ln(1),c=ln(1),u=ln(!1),d=ln(0),p=ln(0),f=null,h=null,g=!1,m=null,v=null;const y=new kg,b=new kg,_={historyX:[0,0],historyY:[0,0],historyT:[0,0]},w=xi((()=>{let t=Number(e.friction);return isNaN(t)||t<=0?2:t})),x=new Cg(1,w.value);eo((()=>e.disabled),(()=>{W()}));const{_updateOldScale:T,_endScale:S,_setScale:k,scaleValueSync:C,_updateBoundary:E,_updateOffset:A,_updateWH:M,_scaleOffset:P,minX:I,minY:O,maxX:L,maxY:$,FAandSFACancel:D,_getLimitXY:R,_setTransform:B,_revise:N,dampingNumber:q,xMove:j,yMove:z,xSync:V,ySync:F,_STD:U}=function(e,t,n,o,r,i,a,s,l,c){const u=xi((()=>{let t=Number(e.scaleMin);return isNaN(t)?.5:t})),d=xi((()=>{let t=Number(e.scaleMax);return isNaN(t)?10:t})),p=ln(Number(e.scaleValue)||1);eo(p,(e=>{B(e)})),eo(u,(()=>{R()})),eo(d,(()=>{R()})),eo((()=>e.scaleValue),(e=>{p.value=Number(e)||0}));const{_updateBoundary:f,_updateOffset:h,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:b,maxY:_}=function(e,t,n){const o=Qn("movableAreaWidth",ln(0)),r=Qn("movableAreaHeight",ln(0)),i=Qn("movableAreaRootRef"),a={x:0,y:0},s={x:0,y:0},l=ln(0),c=ln(0),u=ln(0),d=ln(0),p=ln(0),f=ln(0);function h(){let e=0-a.x+s.x,t=o.value-l.value-a.x-s.x;u.value=Math.min(e,t),p.value=Math.max(e,t);let n=0-a.y+s.y,i=r.value-c.value-a.y-s.y;d.value=Math.min(n,i),f.value=Math.max(n,i)}function g(){a.x=Lg(e.value,i.value),a.y=$g(e.value,i.value)}function m(o){o=o||t.value,o=n(o);let r=e.value.getBoundingClientRect();c.value=r.height/t.value,l.value=r.width/t.value;let i=c.value*o,a=l.value*o;s.x=(a-l.value)/2,s.y=(i-c.value)/2}return{_updateBoundary:h,_updateOffset:g,_updateWH:m,_scaleOffset:s,minX:u,minY:d,maxX:p,maxY:f}}(t,o,D),{FAandSFACancel:w,_getLimitXY:x,_animationTo:T,_setTransform:S,_revise:k,dampingNumber:C,xMove:E,yMove:A,xSync:M,ySync:P,_STD:I}=function(e,t,n,o,r,i,a,s,l,c,u,d,p,f){const h=xi((()=>{let e=Number(t.damping);return isNaN(e)?20:e})),g=xi((()=>"all"===t.direction||"horizontal"===t.direction)),m=xi((()=>"all"===t.direction||"vertical"===t.direction)),v=ln(Rg(t.x)),y=ln(Rg(t.y));eo((()=>t.x),(e=>{v.value=Rg(e)})),eo((()=>t.y),(e=>{y.value=Rg(e)})),eo(v,(e=>{k(e)})),eo(y,(e=>{C(e)}));const b=new Ag(1,9*Math.pow(h.value,2)/40,h.value);function _(e,t){let n=!1;return e>r.value?(e=r.value,n=!0):e<a.value&&(e=a.value,n=!0),t>i.value?(t=i.value,n=!0):t<s.value&&(t=s.value,n=!0),{x:e,y:t,outOfBounds:n}}function w(){d&&d.cancel(),u&&u.cancel()}function x(e,n,r,i,a,s){w(),g.value||(e=l.value),m.value||(n=c.value),t.scale||(r=o.value);let d=_(e,n);e=d.x,n=d.y,t.animation?(b._springX._solution=null,b._springY._solution=null,b._springScale._solution=null,b._springX._endPosition=l.value,b._springY._endPosition=c.value,b._springScale._endPosition=o.value,b.setEnd(e,n,r,1),u=Dg(b,(function(){let e=b.x();T(e.x,e.y,e.scale,i,a,s)}),(function(){u.cancel()}))):T(e,n,r,i,a,s)}function T(r,i,a,s="",u,d){null!==r&&"NaN"!==r.toString()&&"number"==typeof r||(r=l.value||0),null!==i&&"NaN"!==i.toString()&&"number"==typeof i||(i=c.value||0),r=Number(r.toFixed(1)),i=Number(i.toFixed(1)),a=Number(a.toFixed(1)),l.value===r&&c.value===i||u||f("change",{},{x:Mg(r,n.x),y:Mg(i,n.y),source:s}),t.scale||(a=o.value),a=+(a=p(a)).toFixed(3),d&&a!==o.value&&f("scale",{},{x:r,y:i,scale:a});let h="translateX("+r+"px) translateY("+i+"px) translateZ(0px) scale("+a+")";e.value&&(e.value.style.transform=h,e.value.style.webkitTransform=h,l.value=r,c.value=i,o.value=a)}function S(e){let t=_(l.value,c.value),n=t.x,r=t.y,i=t.outOfBounds;return i&&x(n,r,o.value,e),i}function k(e){if(g.value){if(e+n.x===l.value)return l;u&&u.cancel(),x(e+n.x,y.value+n.y,o.value)}return e}function C(e){if(m.value){if(e+n.y===c.value)return c;u&&u.cancel(),x(v.value+n.x,e+n.y,o.value)}return e}return{FAandSFACancel:w,_getLimitXY:_,_animationTo:x,_setTransform:T,_revise:S,dampingNumber:h,xMove:g,yMove:m,xSync:v,ySync:y,_STD:b}}(t,e,m,o,b,_,v,y,a,s,l,c,D,n);function O(t,n){if(e.scale){t=D(t),g(t),f();const e=x(a.value,s.value),o=e.x,r=e.y;n?T(o,r,t,"",!0,!0):Og((function(){S(o,r,t,"",!0,!0)}))}}function L(){i.value=!0}function $(e){r.value=e}function D(e){return e=Math.max(.5,u.value,e),e=Math.min(10,d.value,e)}function R(){if(!e.scale)return!1;O(o.value,!0),$(o.value)}function B(t){return!!e.scale&&(O(t=D(t),!0),$(t),t)}function N(){i.value=!1,$(o.value)}function q(e){e&&(e=r.value*e,L(),O(e))}return{_updateOldScale:$,_endScale:N,_setScale:q,scaleValueSync:p,_updateBoundary:f,_updateOffset:h,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:b,maxY:_,FAandSFACancel:w,_getLimitXY:x,_animationTo:T,_setTransform:S,_revise:k,dampingNumber:C,xMove:E,yMove:A,xSync:M,ySync:P,_STD:I}}(e,n,t,l,c,u,d,p,f,h);function W(){u.value||e.disabled||(D(),_.historyX=[0,0],_.historyY=[0,0],_.historyT=[0,0],j.value&&(a=d.value),z.value&&(s=p.value),n.value.style.willChange="transform",m=null,v=null,g=!0)}function H(t){if(!u.value&&!e.disabled&&g){let n=d.value,o=p.value;if(null===v&&(v=Math.abs(t.detail.dx/t.detail.dy)>1?"htouchmove":"vtouchmove"),j.value&&(n=t.detail.dx+a,_.historyX.shift(),_.historyX.push(n),z.value||null!==m||(m=Math.abs(t.detail.dx/t.detail.dy)<1)),z.value&&(o=t.detail.dy+s,_.historyY.shift(),_.historyY.push(o),j.value||null!==m||(m=Math.abs(t.detail.dy/t.detail.dx)<1)),_.historyT.shift(),_.historyT.push(t.detail.timeStamp),!m){t.preventDefault();let r="touch";n<I.value?e.outOfBounds?(r="touch-out-of-bounds",n=I.value-y.x(I.value-n)):n=I.value:n>L.value&&(e.outOfBounds?(r="touch-out-of-bounds",n=L.value+y.x(n-L.value)):n=L.value),o<O.value?e.outOfBounds?(r="touch-out-of-bounds",o=O.value-b.x(O.value-o)):o=O.value:o>$.value&&(e.outOfBounds?(r="touch-out-of-bounds",o=$.value+b.x(o-$.value)):o=$.value),Og((function(){B(n,o,l.value,r)}))}}}function Y(){if(!u.value&&!e.disabled&&g&&(n.value.style.willChange="auto",g=!1,!m&&!N("out-of-bounds")&&e.inertia)){const e=1e3*(_.historyX[1]-_.historyX[0])/(_.historyT[1]-_.historyT[0]),t=1e3*(_.historyY[1]-_.historyY[0])/(_.historyT[1]-_.historyT[0]),n=d.value,o=p.value;x.setV(e,t),x.setS(n,o);const r=x.delta().x,i=x.delta().y;let a=r+n,s=i+o;a<I.value?(a=I.value,s=o+(I.value-n)*i/r):a>L.value&&(a=L.value,s=o+(L.value-n)*i/r),s<O.value?(s=O.value,a=n+(O.value-o)*r/i):s>$.value&&(s=$.value,a=n+($.value-o)*r/i),x.setEnd(a,s),h=Dg(x,(function(){let e=x.s(),t=e.x,n=e.y;B(t,n,l.value,"friction")}),(function(){h.cancel()}))}e.outOfBounds||e.inertia||D()}function X(){if(!o.value)return;D();let t=e.scale?C.value:1;A(),M(t),E();let n=R(V.value+P.x,F.value+P.y),r=n.x,i=n.y;B(r,i,t,"",!0),T(t)}return Ro((()=>{xg(n.value,(e=>{switch(e.detail.state){case"start":W();break;case"move":H(e);break;case"end":Y()}})),X(),x.reconfigure(1,w.value),U.reconfigure(1,9*Math.pow(q.value,2)/40,q.value),n.value.style.transformOrigin="center";const e={rootRef:n,setParent:X,_endScale:S,_setScale:k};r(e),jo((()=>{i(e)}))})),jo((()=>{D()})),{setParent:X}}(e,r,o);return()=>ti("uni-movable-view",{ref:o},[ti(Ph,{onResize:i},null,8,["onResize"]),t.default&&t.default()],512)}});let Ig=!1;function Og(e){Ig||(Ig=!0,requestAnimationFrame((function(){e(),Ig=!1})))}function Lg(e,t){if(e===t)return 0;let n=e.offsetLeft;return e.offsetParent?n+=Lg(e.offsetParent,t):0}function $g(e,t){if(e===t)return 0;let n=e.offsetTop;return e.offsetParent?n+=$g(e.offsetParent,t):0}function Dg(e,t,n){let o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);let i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}function Rg(e){return/\d+[ur]px$/i.test(e)?uni.upx2px(parseFloat(e)):Number(e)||0}const Bg=["navigate","redirect","switchTab","reLaunch","navigateBack"],Ng=["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"],qg=["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"],jg={hoverClass:{type:String,default:"navigator-hover"},url:{type:String,default:""},openType:{type:String,default:"navigate",validator:e=>Boolean(~Bg.indexOf(e))},delta:{type:Number,default:1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:600},exists:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},animationType:{type:String,default:"",validator:e=>!e||Ng.concat(qg).includes(e)},animationDuration:{type:[String,Number],default:300}};c({},jg,{renderLink:{type:Boolean,default:!0}});const zg=hu({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return f(e)&&e.filter((e=>"number"==typeof e)).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,{slots:t,emit:n}){const o=ln(null),r=ln(null),i=vu(o,n),a=function(e){const t=Yt([...e.value]),n=Yt({value:t,height:34});return eo((()=>e.value),((e,t)=>{n.value.length=e.length,e.forEach(((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)}))})),n}(e),s=ln(null);Ro((()=>{const e=s.value;e&&(a.height=e.$el.offsetHeight)}));let l=ln([]),c=ln([]);function u(e){let t=c.value;t=t.filter((e=>e.type!==qr));let n=t.indexOf(e);return-1!==n?n:l.value.indexOf(e)}return Jn("getPickerViewColumn",(function(e){return xi({get(){const t=u(e.vnode);return a.value[t]||0},set(t){const o=u(e.vnode);if(o<0)return;if(a.value[o]!==t){a.value[o]=t;const e=a.value.map((e=>e));n("update:value",e),i("change",{},{value:e})}}})})),Jn("pickerViewProps",e),Jn("pickerViewState",a),()=>{const e=t.default&&t.default();{const t=mg(e);l.value=t,Mn((()=>{c.value=t}))}return ti("uni-picker-view",{ref:o},[ti(Ph,{ref:s,onResize:({height:e})=>a.height=e},null,8,["onResize"]),ti("div",{ref:r,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class Vg{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){const t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){const e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function Fg(e,t,n){return e>t-n&&e<t+n}function Ug(e,t){return Fg(e,0,t)}class Wg{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,a=t/(r*e);return{x:function(e){return(i+a*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+a*e)*t+a*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),a=(-n+Math.sqrt(i))/(2*o),s=(t-r*e)/(a-r),l=e-s;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*t+s*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*r*t+s*a*n}}}const a=Math.sqrt(4*o*r-n*n)/(2*o),s=-n/2*o,l=e,c=(t-s*e)/a;return{x:function(e){return Math.pow(Math.E,s*e)*(l*Math.cos(a*e)+c*Math.sin(a*e))},dx:function(e){const t=Math.pow(Math.E,s*e),n=Math.cos(a*e),o=Math.sin(a*e);return t*(c*a*n-l*a*o)+s*t*(c*o+l*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!Ug(t,.4)){t=t||0;let o=this._endPosition;this._solution&&(Ug(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),Ug(t,.4)&&(t=0),Ug(o,.4)&&(o=0),o+=this._endPosition),this._solution&&Ug(o-e,.4)&&Ug(t,.4)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),Fg(this.x(),this._endPosition,.4)&&Ug(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class Hg{constructor(e,t,n){this._extent=e,this._friction=t||new Vg(.01),this._spring=n||new Wg(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){let t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){const e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class Yg{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new Hg(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){let n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}let o;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){const e=this._scroll._friction.x(100),t=e%this._itemSize;o=Math.abs(t)>this._itemSize/2?e-(this._itemSize-Math.abs(t)):e-t,o<=0&&o>=-this._extent&&this._scroll.setVelocityByEnd(o)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=function(e,t,n){const o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);const i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}(this._scroll,(()=>{const e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();const o=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/o),this._lastTime=e)}),(()=>{this._enableSnap&&(o<=0&&o>=-this._extent&&(this._position=o,this.updatePosition()),v(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1}))}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),v(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(v(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){let o=0;const r=this._position;this._enableX?(o=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(o=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-o?this._position=-o:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),r!==this._position&&(this.dispatchScroll(),v(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=o,this._scroll._extent=o}updatePosition(){let e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}function Xg(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},o=new Yg(e,t);function r(e){const t=e,o=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:o.screenX-n.x,y:o.screenY-n.y}}return{scroller:o,handleTouchStart:function(e){const t=e,r=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=r.screenX,n.y=r.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||r.timeStamp],n.listener=o,o.onTouchStart&&o.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){const t=e,o=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();const i=r(e);if(i){for(n.maxDy=Math.max(n.maxDy,Math.abs(i.y)),n.maxDx=Math.max(n.maxDx,Math.abs(i.x)),n.historyX.push(i.x),n.historyY.push(i.y),n.historyTime.push(t.detail.timeStamp||o.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(i.x,i.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();const t=r(e);if(t){const e=n.listener;n.trackingID=-1,n.listener=null;const o={x:0,y:0};if(n.historyTime.length>2)for(let t=n.historyTime.length-1,r=n.historyTime[t],i=n.historyX[t],a=n.historyY[t];t>0;){t--;const e=r-n.historyTime[t];if(e>30&&e<50){o.x=(i-n.historyX[t])/(e/1e3),o.y=(a-n.historyY[t])/(e/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],e&&e.onTouchEnd&&e.onTouchEnd(t.x,t.y,o)}}}}}let Gg=0;const Jg=hu({name:"PickerViewColumn",setup(e,{slots:t,emit:n}){const o=ln(null),r=ln(null),i=Qn("getPickerViewColumn"),a=fi(),s=i?i(a):ln(0),l=Qn("pickerViewProps"),c=Qn("pickerViewState"),u=ln(34),d=ln(null);Ro((()=>{const e=d.value;u.value=e.$el.offsetHeight}));const p=xi((()=>(c.height-u.value)/2)),{state:f}=og(),h=function(e){const t="uni-picker-view-content-"+Gg++;return eo((()=>e.value),(function(){const n=document.createElement("style");n.innerText=`.uni-picker-view-content.${t}>*{height: ${e.value}px;overflow: hidden;}`,document.head.appendChild(n)})),t}(u);let g;const m=Yt({current:s.value,length:0});let v;function y(){g&&!v&&(v=!0,Mn((()=>{v=!1;let e=Math.min(m.current,m.length-1);e=Math.max(e,0),g.update(e*u.value,void 0,u.value)})))}eo((()=>s.value),(e=>{e!==m.current&&(m.current=e,y())})),eo((()=>m.current),(e=>s.value=e)),eo([()=>u.value,()=>m.length,()=>c.height],y);let b=0;function _(e){const t=b+e.deltaY;if(Math.abs(t)>10){b=0;let e=Math.min(m.current+(t<0?-1:1),m.length-1);m.current=e=Math.max(e,0),g.scrollTo(e*u.value)}else b=t;e.preventDefault()}function w({clientY:e}){const t=o.value;if(!g.isScrolling()){const n=e-t.getBoundingClientRect().top-c.height/2,o=u.value/2;if(!(Math.abs(n)<=o)){const e=Math.ceil((Math.abs(n)-o)/u.value),t=n<0?-e:e;let r=Math.min(m.current+t,m.length-1);m.current=r=Math.max(r,0),g.scrollTo(r*u.value)}}}return Ro((()=>{const e=o.value,t=r.value,{scroller:n,handleTouchStart:i,handleTouchMove:a,handleTouchEnd:s}=Xg(t,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:u.value,friction:new Vg(1e-4),spring:new Wg(2,90,20),onSnap:e=>{isNaN(e)||e===m.current||(m.current=e)}});g=n,xg(e,(e=>{switch(e.detail.state){case"start":i(e);break;case"move":a(e),e.stopPropagation();break;case"end":case"cancel":s(e)}}),!0),function(e){let t=0,n=0;e.addEventListener("touchstart",(e=>{const o=e.changedTouches[0];t=o.clientX,n=o.clientY})),e.addEventListener("touchend",(e=>{const o=e.changedTouches[0];if(Math.abs(o.clientX-t)<20&&Math.abs(o.clientY-n)<20){const t={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},n=new CustomEvent("click",t);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{n[e]=o[e]})),e.target.dispatchEvent(n)}}))}(e),y()})),()=>{const e=t.default&&t.default();m.length=mg(e).length;const n=`${p.value}px 0`;return ti("uni-picker-view-column",{ref:o},[ti("div",{onWheel:_,onClick:w,class:"uni-picker-view-group"},[ti("div",li(f.attrs,{class:["uni-picker-view-mask",l.maskClass],style:`background-size: 100% ${p.value}px;${l.maskStyle}`}),null,16),ti("div",li(f.attrs,{class:["uni-picker-view-indicator",l.indicatorClass],style:l.indicatorStyle}),[ti(Ph,{ref:d,onResize:({height:e})=>u.value=e},null,8,["onResize"])],16),ti("div",{ref:r,class:["uni-picker-view-content",h],style:{padding:n}},[e],6)],40,["onWheel","onClick"])],512)}}}),Qg={a:"",abbr:"",address:"",article:"",aside:"",b:"",bdi:"",bdo:["dir"],big:"",blockquote:"",br:"",caption:"",center:"",cite:"",code:"",col:["span","width"],colgroup:["span","width"],dd:"",del:"",div:"",dl:"",dt:"",em:"",fieldset:"",font:"",footer:"",h1:"",h2:"",h3:"",h4:"",h5:"",h6:"",header:"",hr:"",i:"",img:["alt","src","height","width"],ins:"",label:"",legend:"",li:"",mark:"",nav:"",ol:["start","type"],p:"",pre:"",q:"",rt:"",ruby:"",s:"",section:"",small:"",span:"",strong:"",sub:"",sup:"",table:["width"],tbody:"",td:["colspan","height","rowspan","width"],tfoot:"",th:["colspan","height","rowspan","width"],thead:"",tr:["colspan","height","rowspan","width"],tt:"",u:"",ul:""},Kg={amp:"&",gt:">",lt:"<",nbsp:" ",quot:'"',apos:"'",ldquo:"“",rdquo:"”",yen:"￥",radic:"√",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",hellip:"…"};const Zg=(e,t,n)=>!n||f(n)&&!n.length?[]:n.map((n=>{if(S(n)){if(!p(n,"type")||"node"===n.type){let o={[e]:""};const r=n.name.toLowerCase();if(!p(Qg,r))return;return function(e,t){if(S(t))for(const n in t)if(p(t,n)){const o=t[n];"img"===e&&"src"===n&&(t[n]=Mu(o))}}(r,n.attrs),o=c(o,function(e,t){if(["a","img"].includes(e.name)&&t)return{onClick:n=>{t(n,{node:e}),n.stopPropagation(),n.preventDefault(),n.returnValue=!1}}}(n,t),n.attrs),Ti(n.name,o,Zg(e,t,n.children))}return"text"===n.type&&y(n.text)&&""!==n.text?oi((n.text||"").replace(/&(([a-zA-Z]+)|(#x{0,1}[\da-zA-Z]+));/gi,(function(e,t){return p(Kg,t)&&Kg[t]?Kg[t]:/^#[0-9]{1,4}$/.test(t)?String.fromCharCode(t.slice(1)):/^#x[0-9a-f]{1,4}$/i.test(t)?String.fromCharCode(0+t.slice(1)):e}))):void 0}}));function em(e){e=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(e);const t=[],n={node:"root",children:[]};return function(e,t){var n,o,r,i=[],a=e;for(i.last=function(){return this[this.length-1]};e;){if(o=!0,i.last()&&Vh[i.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+i.last()+"[^>]*>"),(function(e,n){return n=n.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(n),""})),c("",i.last());else if(0==e.indexOf("\x3c!--")?(n=e.indexOf("--\x3e"))>=0&&(t.comment&&t.comment(e.substring(4,n)),e=e.substring(n+3),o=!1):0==e.indexOf("</")?(r=e.match(Dh))&&(e=e.substring(r[0].length),r[0].replace(Dh,c),o=!1):0==e.indexOf("<")&&(r=e.match($h))&&(e=e.substring(r[0].length),r[0].replace($h,l),o=!1),o){var s=(n=e.indexOf("<"))<0?e:e.substring(0,n);e=n<0?"":e.substring(n),t.chars&&t.chars(s)}if(e==a)throw"Parse Error: "+e;a=e}function l(e,n,o,r){if(n=n.toLowerCase(),Nh[n])for(;i.last()&&qh[i.last()];)c("",i.last());if(jh[n]&&i.last()==n&&c("",n),(r=Bh[n]||!!r)||i.push(n),t.start){var a=[];o.replace(Rh,(function(e,t){var n=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:zh[t]?t:"";a.push({name:t,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})})),t.start&&t.start(n,a,r)}}function c(e,n){if(n)for(o=i.length-1;o>=0&&i[o]!=n;o--);else var o=0;if(o>=0){for(var r=i.length-1;r>=o;r--)t.end&&t.end(i[r]);i.length=o}}c()}(e,{start:function(e,o,r){const i={name:e};if(0!==o.length&&(i.attrs=function(e){return e.reduce((function(e,t){let n=t.value;const o=t.name;return n.match(/ /)&&-1===["style","src"].indexOf(o)&&(n=n.split(" ")),e[o]?Array.isArray(e[o])?e[o].push(n):e[o]=[e[o],n]:e[o]=n,e}),{})}(o)),r){const e=t[0]||n;e.children||(e.children=[]),e.children.push(i)}else t.unshift(i)},end:function(e){const o=t.shift();if(o.name!==e&&console.error("invalid state: mismatch end tag"),0===t.length)n.children.push(o);else{const e=t[0];e.children||(e.children=[]),e.children.push(o)}},chars:function(e){const o={type:"text",text:e};if(0===t.length)n.children.push(o);else{const e=t[0];e.children||(e.children=[]),e.children.push(o)}},comment:function(e){const n={node:"comment",text:e},o=t[0];o.children||(o.children=[]),o.children.push(n)}}),n.children}const tm=hu({name:"RichText",compatConfig:{MODE:3},props:{nodes:{type:[Array,String],default:function(){return[]}}},emits:["click","touchstart","touchmove","touchcancel","touchend","longpress","itemclick"],setup(e,{emit:t}){const n=fi(),o=n&&n.vnode.scopeId||"",r=ln(null),i=ln([]),a=vu(r,t);function s(e,t={}){a("itemclick",e,t)}return eo((()=>e.nodes),(function(){let t=e.nodes;y(t)&&(t=em(e.nodes)),i.value=Zg(o,s,t)}),{immediate:!0}),()=>Ti("uni-rich-text",{ref:r},Ti("div",{},i.value))}}),nm=we(!0),om=hu({name:"ScrollView",compatConfig:{MODE:3},props:{direction:{type:[String],default:"vertical"},scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},showScrollbar:{type:[Boolean,String],default:!0},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"back"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n}){const o=ln(null),r=ln(null),i=ln(null),a=ln(null),s=ln(null),l=vu(o,t),{state:c,scrollTopNumber:u,scrollLeftNumber:d}=function(e){const t=xi((()=>Number(e.scrollTop)||0)),n=xi((()=>Number(e.scrollLeft)||0));return{state:Yt({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshRotate:0,refreshState:""}),scrollTopNumber:t,scrollLeftNumber:n}}(e),{realScrollX:p,realScrollY:f}=function(e,t,n,o,r,i,a,s,l){let c=!1,u=0,d=!1,p=()=>{};const f=xi((()=>e.scrollX)),h=xi((()=>e.scrollY)),g=xi((()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t})),m=xi((()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function v(e,t){const n=a.value;let o=0,r="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?o=n.scrollLeft-e:"y"===t&&(o=n.scrollTop-e),0===o)return;let i=s.value;i.style.transition="transform .3s ease-out",i.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?r="translateX("+o+"px) translateZ(0)":"y"===t&&(r="translateY("+o+"px) translateZ(0)"),i.removeEventListener("transitionend",p),i.removeEventListener("webkitTransitionEnd",p),p=()=>x(e,t),i.addEventListener("transitionend",p),i.addEventListener("webkitTransitionEnd",p),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),i.style.transform=r,i.style.webkitTransform=r}function y(e){const n=e.target;r("scroll",e,{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop,scrollHeight:n.scrollHeight,scrollWidth:n.scrollWidth,deltaX:t.lastScrollLeft-n.scrollLeft,deltaY:t.lastScrollTop-n.scrollTop}),h.value&&(n.scrollTop<=g.value&&t.lastScrollTop-n.scrollTop>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"top"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollTop+n.offsetHeight+m.value>=n.scrollHeight&&t.lastScrollTop-n.scrollTop<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"bottom"}),t.lastScrollToLowerTime=e.timeStamp)),f.value&&(n.scrollLeft<=g.value&&t.lastScrollLeft-n.scrollLeft>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"left"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollLeft+n.offsetWidth+m.value>=n.scrollWidth&&t.lastScrollLeft-n.scrollLeft<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"right"}),t.lastScrollToLowerTime=e.timeStamp)),t.lastScrollTop=n.scrollTop,t.lastScrollLeft=n.scrollLeft}function b(t){h.value&&(e.scrollWithAnimation?v(t,"y"):a.value.scrollTop=t)}function _(t){f.value&&(e.scrollWithAnimation?v(t,"x"):a.value.scrollLeft=t)}function w(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error(`id error: scroll-into-view=${t}`);let n=i.value.querySelector("#"+t);if(n){let t=a.value.getBoundingClientRect(),o=n.getBoundingClientRect();if(f.value){let n=o.left-t.left,r=a.value.scrollLeft+n;e.scrollWithAnimation?v(r,"x"):a.value.scrollLeft=r}if(h.value){let n=o.top-t.top,r=a.value.scrollTop+n;e.scrollWithAnimation?v(r,"y"):a.value.scrollTop=r}}}}function x(e,t){s.value.style.transition="",s.value.style.webkitTransition="",s.value.style.transform="",s.value.style.webkitTransform="";let n=a.value;"x"===t?(n.style.overflowX=f.value?"auto":"hidden",n.scrollLeft=e):"y"===t&&(n.style.overflowY=h.value?"auto":"hidden",n.scrollTop=e),s.value.removeEventListener("transitionend",p),s.value.removeEventListener("webkitTransitionEnd",p)}function T(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,r("refresherrefresh",{},{}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(d=!1,r("refresherrestore",{},{})),"refresherabort"===n&&d&&(d=!1,r("refresherabort",{},{}))}t.refreshState=n}}return Ro((()=>{Mn((()=>{b(n.value),_(o.value)})),w(e.scrollIntoView);let i=function(e){e.preventDefault(),e.stopPropagation(),y(e)},s={x:0,y:0},l=null,p=function(n){if(null===s)return;let o=n.touches[0].pageX,i=n.touches[0].pageY,p=a.value;if(Math.abs(o-s.x)>Math.abs(i-s.y))if(f.value){if(0===p.scrollLeft&&o>s.x)return void(l=!1);if(p.scrollWidth===p.offsetWidth+p.scrollLeft&&o<s.x)return void(l=!1);l=!0}else l=!1;else if(h.value)if(0===p.scrollTop&&i>s.y)l=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(p.scrollHeight===p.offsetHeight+p.scrollTop&&i<s.y)return void(l=!1);l=!0}else l=!1;if(l&&n.stopPropagation(),0===p.scrollTop&&1===n.touches.length&&T("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const o=i-s.y;0===u&&(u=i),c?(t.refresherHeight=o+e.refresherThreshold,d=!1):(t.refresherHeight=i-u,t.refresherHeight>0&&(d=!0,r("refresherpulling",n,{deltaY:o})));const a=t.refresherHeight/e.refresherThreshold;t.refreshRotate=360*(a>1?1:a)}},g=function(e){1===e.touches.length&&(s={x:e.touches[0].pageX,y:e.touches[0].pageY})},m=function(n){s=null,t.refresherHeight>=e.refresherThreshold?T("refreshing"):T("refresherabort")};a.value.addEventListener("touchstart",g,nm),a.value.addEventListener("touchmove",p,we(!1)),a.value.addEventListener("scroll",i,we(!1)),a.value.addEventListener("touchend",m,nm),qo((()=>{a.value.removeEventListener("touchstart",g),a.value.removeEventListener("touchmove",p),a.value.removeEventListener("scroll",i),a.value.removeEventListener("touchend",m)}))})),So((()=>{h.value&&(a.value.scrollTop=t.lastScrollTop),f.value&&(a.value.scrollLeft=t.lastScrollLeft)})),eo(n,(e=>{b(e)})),eo(o,(e=>{_(e)})),eo((()=>e.scrollIntoView),(e=>{w(e)})),eo((()=>e.refresherTriggered),(e=>{!0===e?T("refreshing"):!1===e&&T("restore")})),{realScrollX:f,realScrollY:h}}(e,c,u,d,l,o,r,a,t),h=xi((()=>{let e="";return p.value?e+="overflow-x:auto;":e+="overflow-x:hidden;",f.value?e+="overflow-y:auto;":e+="overflow-y:hidden;",e})),g=xi((()=>{let t="uni-scroll-view";return!1===e.showScrollbar&&(t+=" uni-scroll-view-scrollbar-hidden"),t}));return()=>{const{refresherEnabled:t,refresherBackground:l,refresherDefaultStyle:u}=e,{refresherHeight:d,refreshState:p,refreshRotate:f}=c;return ti("uni-scroll-view",{ref:o},[ti("div",{ref:i,class:"uni-scroll-view"},[ti("div",{ref:r,style:h.value,class:g.value},[ti("div",{ref:a,class:"uni-scroll-view-content"},[t?ti("div",{ref:s,style:{backgroundColor:l,height:d+"px"},class:"uni-scroll-view-refresher"},["none"!==u?ti("div",{class:"uni-scroll-view-refresh"},[ti("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==p?ti("svg",{key:"refresh__icon",style:{transform:"rotate("+f+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[ti("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),ti("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==p?ti("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[ti("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"==u?n.refresher&&n.refresher():null],4):null,n.default&&n.default()],512)],6)],512)],512)}}});function rm(e,t,n,o,r,i){function a(){c&&(clearTimeout(c),c=null)}let s,l,c=null,u=!0,d=0,p=1,f=null,h=!1,g=0,m="";const v=xi((()=>n.value.length>t.displayMultipleItems)),y=xi((()=>e.circular&&v.value));function b(r){Math.floor(2*d)===Math.floor(2*r)&&Math.ceil(2*d)===Math.ceil(2*r)||y.value&&function(o){if(!u)for(let r=n.value,i=r.length,a=o+t.displayMultipleItems,s=0;s<i;s++){const t=r[s],n=Math.floor(o/i)*i+s,l=n+i,c=n-i,u=Math.max(o-(n+1),n-a,0),d=Math.max(o-(l+1),l-a,0),p=Math.max(o-(c+1),c-a,0),f=Math.min(u,d,p),h=[n,l,c][[u,d,p].indexOf(f)];t.updatePosition(h,e.vertical)}}(r);const a="translate("+(e.vertical?"0":100*-r*p+"%")+", "+(e.vertical?100*-r*p+"%":"0")+") translateZ(0)",l=o.value;if(l&&(l.style.webkitTransform=a,l.style.transform=a),d=r,!s){if(r%1==0)return;s=r}r-=Math.floor(s);const c=n.value;r<=-(c.length-1)?r+=c.length:r>=c.length&&(r-=c.length),r=s%1>.5||s<0?r-1:r,i("transition",{},{dx:e.vertical?0:r*l.offsetWidth,dy:e.vertical?r*l.offsetHeight:0})}function _(e){const o=n.value.length;if(!o)return-1;const r=(Math.round(e)%o+o)%o;if(y.value){if(o<=t.displayMultipleItems)return 0}else if(r>o-t.displayMultipleItems)return o-t.displayMultipleItems;return r}function w(){f=null}function x(){if(!f)return void(h=!1);const e=f,o=e.toPos,r=e.acc,a=e.endTime,c=e.source,u=a-Date.now();if(u<=0){b(o),f=null,h=!1,s=null;const e=n.value[t.current];if(e){const n=e.getItemId();i("animationfinish",{},{current:t.current,currentItemId:n,source:c})}return}b(o+r*u*u/2),l=requestAnimationFrame(x)}function T(e,o,r){w();const i=t.duration,a=n.value.length;let s=d;if(y.value)if(r<0){for(;s<e;)s+=a;for(;s-a>e;)s-=a}else if(r>0){for(;s>e;)s-=a;for(;s+a<e;)s+=a;s+a-e<e-s&&(s+=a)}else{for(;s+a<e;)s+=a;for(;s-a>e;)s-=a;s+a-e<e-s&&(s+=a)}else"click"===o&&(e=e+t.displayMultipleItems-1<a?e:0);f={toPos:e,acc:2*(s-e)/(i*i),endTime:Date.now()+i,source:o},h||(h=!0,l=requestAnimationFrame(x))}function S(){a();const e=n.value,o=function(){c=null,m="autoplay",y.value?t.current=_(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,T(t.current,"autoplay",y.value?1:0),c=setTimeout(o,t.interval)};u||e.length<=t.displayMultipleItems||(c=setTimeout(o,t.interval))}function k(e){e?S():a()}return eo([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{let o=-1;if(e.currentItemId)for(let t=0,r=n.value;t<r.length;t++){if(r[t].getItemId()===e.currentItemId){o=t;break}}o<0&&(o=Math.round(e.current)||0),o=o<0?0:o,t.current!==o&&(m="",t.current=o)})),eo([()=>e.vertical,()=>y.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){a(),f&&(b(f.toPos),f=null);const r=n.value;for(let t=0;t<r.length;t++)r[t].updatePosition(t,e.vertical);p=1;const i=o.value;if(1===t.displayMultipleItems&&r.length){const e=r[0].getBoundingClientRect(),t=i.getBoundingClientRect();p=e.width/t.width,p>0&&p<1||(p=1)}const s=d;d=-2;const l=t.current;l>=0?(u=!1,t.userTracking?(b(s+l-g),g=l):(b(l),e.autoplay&&S())):(u=!0,b(-t.displayMultipleItems-1))})),eo((()=>t.interval),(()=>{c&&(a(),S())})),eo((()=>t.current),((e,o)=>{!function(e,o){const r=m;m="";const a=n.value;if(!r){const t=a.length;T(e,"",y.value&&o+(t-e)%t>t/2?1:0)}const s=a[e];if(s){const e=t.currentItemId=s.getItemId();i("change",{},{current:t.current,currentItemId:e,source:r})}}(e,o),r("update:current",e)})),eo((()=>t.currentItemId),(e=>{r("update:currentItemId",e)})),eo((()=>e.autoplay&&!t.userTracking),k),k(e.autoplay&&!t.userTracking),Ro((()=>{let r=!1,i=0,s=0;function l(e){t.userTracking=!1;const n=i/Math.abs(i);let o=0;!e&&Math.abs(i)>.2&&(o=.5*n);const r=_(d+o);e?b(g):(m="touch",t.current=r,T(r,"touch",0!==o?o:0===r&&y.value&&d>=1?1:0))}xg(o.value,(c=>{if(!e.disableTouch&&!u){if("start"===c.detail.state)return t.userTracking=!0,r=!1,a(),g=d,i=0,s=Date.now(),void w();if("end"===c.detail.state)return l(!1);if("cancel"===c.detail.state)return l(!0);if(t.userTracking){if(!r){r=!0;const n=Math.abs(c.detail.dx),o=Math.abs(c.detail.dy);if((n>=o&&e.vertical||n<=o&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&S())}return function(r){const a=s;s=Date.now();const l=n.value.length-t.displayMultipleItems;function c(e){return.5-.25/(e+.5)}function u(e,t){let n=g+e;i=.6*i+.4*t,y.value||(n<0||n>l)&&(n<0?n=-c(-n):n>l&&(n=l+c(n-l)),i=0),b(n)}const d=s-a||1,p=o.value;e.vertical?u(-r.dy/p.offsetHeight,-r.ddy/d):u(-r.dx/p.offsetWidth,-r.ddx/d)}(c.detail),!1}}}))})),jo((()=>{a(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){T(t.current=e,m="click",y.value?1:0)},circularEnabled:y,swiperEnabled:v}}const im=hu({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const o=ln(null),r=vu(o,n),i=ln(null),a=ln(null),s=function(e){return Yt({interval:xi((()=>{const t=Number(e.interval);return isNaN(t)?5e3:t})),duration:xi((()=>{const t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:xi((()=>{const t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),l=xi((()=>{let t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:wc(e.previousMargin,!0),bottom:wc(e.nextMargin,!0)}:{top:0,bottom:0,left:wc(e.previousMargin,!0),right:wc(e.nextMargin,!0)}),t})),c=xi((()=>{const t=Math.abs(100/s.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}}));let u=[];const d=[],p=ln([]);function f(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n instanceof Element||(n=n.el);const o=d.find((e=>n===e.rootRef.value));o&&e.push(tn(o))}p.value=e}Jn("addSwiperContext",(function(e){d.push(e),f()}));Jn("removeSwiperContext",(function(e){const t=d.indexOf(e);t>=0&&(d.splice(t,1),f())}));const{onSwiperDotClick:h,circularEnabled:g,swiperEnabled:m}=rm(e,s,p,a,n,r);let v=()=>null;return v=am(o,e,s,h,p,g,m),()=>{const n=t.default&&t.default();return u=mg(n),ti("uni-swiper",{ref:o},[ti("div",{ref:i,class:"uni-swiper-wrapper"},[ti("div",{class:"uni-swiper-slides",style:l.value},[ti("div",{ref:a,class:"uni-swiper-slide-frame",style:c.value},[n],4)],4),e.indicatorDots&&ti("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[p.value.map(((t,n,o)=>ti("div",{onClick:()=>h(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<s.current+s.displayMultipleItems&&n>=s.current||n<s.current+s.displayMultipleItems-o.length},style:{background:n===s.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),v()],512)],512)}}}),am=(e,t,n,o,r,i,a)=>{let s=!1,l=!1,u=!1,d=ln(!1);function p(e,n){const o=e.currentTarget;o&&(o.style.backgroundColor="over"===n?t.navigationActiveColor:"")}Kn((()=>{s="auto"===t.navigation,d.value=!0!==t.navigation||s,b()})),Kn((()=>{const e=r.value.length,t=!i.value;l=0===n.current&&t,u=n.current===e-1&&t||t&&n.current+n.displayMultipleItems>=e,a.value||(l=!0,u=!0,s&&(d.value=!0))}));const f={onMouseover:e=>p(e,"over"),onMouseout:e=>p(e,"out")};function h(e,t,a){if(e.stopPropagation(),a)return;const s=r.value.length;let l=n.current;switch(t){case"prev":l--,l<0&&i.value&&(l=s-1);break;case"next":l++,l>=s&&i.value&&(l=0)}o(l)}const g=()=>Ec(Sc,t.navigationColor,26);let m;const v=n=>{clearTimeout(m);const{clientX:o,clientY:r}=n,{left:i,right:a,top:s,bottom:l,width:c,height:u}=e.value.getBoundingClientRect();let p=!1;if(p=t.vertical?!(r-s<u/3||l-r<u/3):!(o-i<c/3||a-o<c/3),p)return m=setTimeout((()=>{d.value=p}),300);d.value=p},y=()=>{d.value=!0};function b(){e.value&&(e.value.removeEventListener("mousemove",v),e.value.removeEventListener("mouseleave",y),s&&(e.value.addEventListener("mousemove",v),e.value.addEventListener("mouseleave",y)))}return Ro(b),function(){const e={"uni-swiper-navigation-hide":d.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?ti(Br,null,[ti("div",li({class:["uni-swiper-navigation uni-swiper-navigation-prev",c({"uni-swiper-navigation-disabled":l},e)],onClick:e=>h(e,"prev",l)},f),[g()],16,["onClick"]),ti("div",li({class:["uni-swiper-navigation uni-swiper-navigation-next",c({"uni-swiper-navigation-disabled":u},e)],onClick:e=>h(e,"next",u)},f),[g()],16,["onClick"])]):null}},sm=hu({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=ln(null),o={rootRef:n,getItemId:()=>e.itemId,getBoundingClientRect:()=>n.value.getBoundingClientRect(),updatePosition(e,t){const o=t?"0":100*e+"%",r=t?100*e+"%":"0",i=n.value,a=`translate(${o},${r}) translateZ(0)`;i&&(i.style.webkitTransform=a,i.style.transform=a)}};return Ro((()=>{const e=Qn("addSwiperContext");e&&e(o)})),jo((()=>{const e=Qn("removeSwiperContext");e&&e(o)})),()=>ti("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),lm={ensp:" ",emsp:" ",nbsp:" "};function cm(e,t){return function(e,{space:t,decode:n}){let o="",r=!1;for(let i of e)t&&lm[t]&&" "===i&&(i=lm[t]),r?(o+="n"===i?"\n":"\\"===i?"\\":"\\"+i,r=!1):"\\"===i?r=!0:o+=i;return n?o.replace(/&nbsp;/g,lm.nbsp).replace(/&ensp;/g,lm.ensp).replace(/&emsp;/g,lm.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):o}(e,t).split("\n")}const um=hu({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=ln(null);return()=>{const o=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==qr){const n=cm(t.children,{space:e.space,decode:e.decode}),r=n.length-1;n.forEach(((e,t)=>{(0!==t||e)&&o.push(oi(e)),t!==r&&o.push(ti("br"))}))}else o.push(t)})),ti("uni-text",{ref:n,selectable:!!e.selectable||null},[ti("span",null,o)],8,["selectable"])}}}),dm=c({},sg,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator:e=>fm.concat("return").includes(e)}});let pm=!1;const fm=["done","go","next","search","send"];const hm=hu({name:"Textarea",props:dm,emits:["confirm","linechange",...lg],setup(e,{emit:t,expose:n}){const o=ln(null),r=ln(null),{fieldRef:i,state:a,scopedAttrsState:s,fixDisabledColor:l,trigger:c}=dg(e,o,t),u=xi((()=>a.value.split("\n"))),d=xi((()=>fm.includes(e.confirmType))),p=ln(0),f=ln(null);function h({height:e}){p.value=e}function g(e){"Enter"===e.key&&d.value&&e.preventDefault()}function m(t){if("Enter"===t.key&&d.value){!function(e){c("confirm",e,{value:a.value})}(t);const n=t.target;!e.confirmHold&&n.blur()}}return eo((()=>p.value),(t=>{const n=o.value,i=f.value,a=r.value;let s=parseFloat(getComputedStyle(n).lineHeight);isNaN(s)&&(s=i.offsetHeight);var l=Math.round(t/s);c("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:l}),e.autoHeight&&(n.style.height="auto",a.style.height=t+"px")})),function(){const e="(prefers-color-scheme: dark)";pm=0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(e).media!==e}(),n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),a.value=e.value}}),()=>{let t=e.disabled&&l?ti("textarea",{key:"disabled-textarea",ref:i,value:a.value,tabindex:"-1",readonly:!!e.disabled,maxlength:a.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":pm},style:{overflowY:e.autoHeight?"hidden":"auto",...e.cursorColor&&{caretColor:e.cursorColor}},onFocus:e=>e.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):ti("textarea",{key:"textarea",ref:i,value:a.value,disabled:!!e.disabled,maxlength:a.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":pm},style:{overflowY:e.autoHeight?"hidden":"auto",...e.cursorColor&&{caretColor:e.cursorColor}},onKeydown:g,onKeyup:m},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return ti("uni-textarea",{ref:o},[ti("div",{ref:r,class:"uni-textarea-wrapper"},[Wo(ti("div",li(s.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[ka,!a.value.length]]),ti("div",{ref:f,class:"uni-textarea-line"},[" "],512),ti("div",{class:"uni-textarea-compute"},[u.value.map((e=>ti("div",null,[e.trim()?e:"."]))),ti(Ph,{initial:!0,onResize:h},null,8,["initial","onResize"])]),"search"===e.confirmType?ti("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[t],40,["onSubmit"]):t],512)],512)}}}),gm=hu({name:"View",props:c({},yu),setup(e,{slots:t}){const n=ln(null),{hovering:o,binding:r}=bu(e);return()=>{const i=e.hoverClass;return i&&"none"!==i?ti("uni-view",li({class:o.value?i:"",ref:n},r),[t.default&&t.default()],16):ti("uni-view",{ref:n},[t.default&&t.default()],512)}}});function mm(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function vm(e,t,n){e&&Vl(n||Oc(),e,(({type:e,data:n},o)=>{t(e,n,o)}))}function ym(e,t){e&&function(e,t){t=zl(e,t),delete jl[t]}(t||Oc(),e)}function bm(e,t,n,o){const r=fi().proxy;Ro((()=>{vm(t||mm(r),e,o),!n&&t||eo((()=>r.id),((t,n)=>{vm(mm(r,t),e,o),ym(n&&mm(r,n))}))})),qo((()=>{ym(t||mm(r),o)}))}let _m=0;function wm(e){const t=Ac(),n=fi().proxy,o=n.$options.name.toLowerCase(),r=e||n.id||"context"+_m++;return Ro((()=>{n.$el.__uniContextInfo={id:r,type:o,page:t}})),`${o}.${r}`}function xm(e,t,n,o){v(t)&&Lo(e,t.bind(n),o)}function Tm(e,t,n){var o;const r=e.mpType||n.$mpType;if(r&&"component"!==r&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!v(t))&&(Ie.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];f(r)?r.forEach((e=>xm(o,e,n,t))):xm(o,r,n,t)}})),"page"===r)){t.__isVisible=!0;try{Rc(n,"onLoad",t.attrs.__pageQuery),delete t.attrs.__pageQuery,"preloadPage"!==(null==(o=n.$page)?void 0:o.openType)&&Rc(n,"onShow")}catch(EC){console.error(EC.message+"\n"+EC.stack)}}}function Sm(e,t,n){Tm(e,t,n)}function km(e,t,n){return e[t]=n}function Cm(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function Em(e){return function(t,n,o){if(!n)throw t;const r=e._instance;if(!r||!r.proxy)throw t;Rc(r.proxy,"onError",t)}}function Am(e,t){return e?[...new Set([].concat(e,t))]:t}function Mm(e){const t=e._context.config;var n;t.errorHandler=$e(e,Em),n=t.optionMergeStrategies,Ie.forEach((e=>{n[e]=Am}));const o=t.globalProperties;o.$set=km,o.$applyOptions=Sm,o.$callMethod=Cm,function(e){Oe=e,Le.forEach((t=>t(e)))}(e)}const Pm=bc("upm");function Im(){return Qn(Pm)}function Om(e){const t=function(e){return Yt(function(e){{const{enablePullDownRefresh:t,navigationBar:n}=e;if(t){const t=function(e){return e.offset&&(e.offset=wc(e.offset)),e.height&&(e.height=wc(e.height)),e.range&&(e.range=wc(e.range)),e}(c({support:!0,color:"#2BD009",style:"circle",height:70,range:150,offset:0},e.pullToRefresh)),{type:o,style:r}=n;"custom"!==r&&"transparent"!==o&&(t.offset+=44+dc.top),e.pullToRefresh=t}}{const{navigationBar:t}=e,{titleSize:n,titleColor:o,backgroundColor:r}=t;t.titleText=t.titleText||"",t.type=t.type||"default",t.titleSize=n||"16px",t.titleColor=o||"#000000",t.backgroundColor=r||"#F8F8F8"}if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==Gm().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(Dc(bl().meta,e)))))}(e);return Jn(Pm,t),t}function Lm(){return bl()}function $m(){return history.state&&history.state.__id__||1}let Dm;function Rm(){var e;return Dm||(Dm=__uniConfig.tabBar&&Yt((e=__uniConfig.tabBar,_l()&&e.list&&e.list.forEach((e=>{Sl(e,["text"])})),e))),Dm}const Bm=window.CSS&&window.CSS.supports;function Nm(e){return Bm&&(Bm(e)||Bm.apply(window.CSS,e.split(":")))}const qm=Nm("--a:0"),jm=Nm("top:env(a)"),zm=Nm("top:constant(a)"),Vm=Nm("backdrop-filter:blur(10px)"),Fm={"css.var":qm,"css.env":jm,"css.constant":zm,"css.backdrop-filter":Vm},Um=Md(0,(e=>!p(Fm,e)||Fm[e])),Wm=(()=>jm?"env":zm?"constant":"")();function Hm(e){return Wm?`calc(${e}px + ${Wm}(safe-area-inset-bottom))`:`${e}px`}const Ym=new Map;function Xm(){return Ym}function Gm(){const e=[],t=Ym.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function Jm(e,t=!0){const n=Ym.get(e);n.$.__isUnload=!0,Rc(n,"onUnload"),Ym.delete(e),t&&function(e){const t=tv.get(e);t&&(tv.delete(e),nv.pruneCacheEntry(t))}(e)}let Qm=$m();function Km(e){const t=Im();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),function(e,t,n,o,r,i){const{id:a,route:s}=o,l=Ne(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:a,path:de(s),route:s,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#ffffff"===l?"light":"dark"}}("navigateTo",n,{},t)}function Zm(e){const t=Km(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),Ym.set(ev(t.path,t.id),e)}function ev(e,t){return e+"$$"+t}const tv=new Map,nv={get:e=>tv.get(e),set(e,t){!function(e){const t=parseInt(e.split("$$")[1]);if(!t)return;nv.forEach(((e,n)=>{const o=parseInt(n.split("$$")[1]);if(o&&o>t){if(function(e){return"tabBar"===e.props.type}(e))return;nv.delete(n),nv.pruneCacheEntry(e),Mn((()=>{Ym.forEach(((e,t)=>{e.$.isUnmounted&&Ym.delete(t)}))}))}}))}(e),tv.set(e,t)},delete(e){tv.get(e)&&tv.delete(e)},forEach(e){tv.forEach(e)}};function ov(e,t){!function(e){const t=iv(e),{body:n}=document;av&&n.removeAttribute(av),t&&n.setAttribute(t,""),av=t}(e),function(e){let t=0,n=0;if("custom"!==e.navigationBar.style&&["default","float"].indexOf(e.navigationBar.type)>-1&&(t=44),e.isTabBar){const e=Rm();e.shown&&(n=parseInt(e.height))}var o;yc({"--window-top":(o=t,Wm?`calc(${o}px + ${Wm}(safe-area-inset-top))`:`${o}px`),"--window-bottom":Hm(n)})}(t),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),function(e,t){document.removeEventListener("touchmove",Bc),sv&&document.removeEventListener("scroll",sv);if(t.disableScroll)return document.addEventListener("touchmove",Bc);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!n&&!o&&!r)return;const i={},a=e.proxy.$page.id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&Lx.publishHandler("onPageScroll",{scrollTop:o},e),n&&Lx.emit(e+".onPageScroll",{scrollTop:o})}}(a,n,r));o&&(i.onReachBottomDistance=t.onReachBottomDistance||50,i.onReachBottom=()=>Lx.publishHandler("onReachBottom",{},a));sv=jc(i),requestAnimationFrame((()=>document.addEventListener("scroll",sv)))}(e,t)}function rv(e){const t=iv(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function iv(e){return e.type.__scopeId}let av,sv;function lv(e){const t=vl({history:uv(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:cv});e.router=t,e.use(t)}const cv=(e,t,n)=>{if(n)return n};function uv(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=(n=e,(n=location.host?n||location.pathname+location.search:"").includes("#")||(n+="#"),ps(n));var n;return t.listen(((e,t,n)=>{"back"===n.direction&&function(e=1){const t=Gm(),n=t.length-1,o=n-e;for(let r=n;r>o;r--){const e=t[r].$page;Jm(ev(e.path,e.id),!1)}}(Math.abs(n.delta))})),t}const dv={install(e){Mm(e),Kc(e),cu(e),e.config.warnHandler||(e.config.warnHandler=pv),lv(e)}};function pv(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n),console.warn(...o)}const fv={class:"uni-async-loading"},hv=ti("i",{class:"uni-loading"},null,-1),gv=gu({name:"AsyncLoading",render:()=>(Fr(),Xr("div",fv,[hv]))});function mv(){window.location.reload()}const vv=gu({name:"AsyncError",setup(){El();const{t:e}=kl();return()=>ti("div",{class:"uni-async-error",onClick:mv},[e("uni.async.error")],8,["onClick"])}});let yv;function bv(){return yv}function _v(e){yv=e,Object.defineProperty(yv.$.ctx,"$children",{get:()=>Gm().map((e=>e.$vm))});const t=yv.$.appContext.app;t.component(gv.name)||t.component(gv.name,gv),t.component(vv.name)||t.component(vv.name,vv),function(e){e.$vm=e,e.$mpType="app";const t=ln(kl().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(yv),function(e,t){const n=e.$options||{};n.globalData=c(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(yv),su(),ec()}function wv(e,{clone:t,init:n,setup:o,before:r}){t&&(e=c({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=fi();n(r.proxy);const a=o(r);if(i)return i(a||e,t)},e}function xv(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?wv(e.default,t):wv(e,t)}function Tv(e){return xv(e,{clone:!0,init:Zm,setup(e){e.$pageInstance=e;const t=Lm(),n=Se(t.query);e.attrs.__pageQuery=n,e.proxy.$page.options=n,e.proxy.options=n;const o=Im();var r,i,a;return Do((()=>{ov(e,o)})),Ro((()=>{rv(e);const{onReady:n}=e;n&&D(n),Ev(t)})),Co((()=>{if(!e.__isVisible){ov(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&D(n),Mn((()=>{Ev(t)}))}}),"ba",r),function(e,t){Co(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;const{onHide:t}=e;t&&D(t)}})),i=o.id,Lx.subscribe(zl(i,"invokeViewApi"),a?a(Fl):Fl),qo((()=>{!function(e){Lx.unsubscribe(zl(e,"invokeViewApi")),Object.keys(jl).forEach((t=>{0===t.indexOf(e+".")&&delete jl[t]}))}(o.id)})),n}})}function Sv(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=uni.getSystemInfoSync(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";Dx.emit("onResize",{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function kv(e){S(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&Dx.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function Cv(){const{emit:e}=Dx;"visible"===document.visibilityState?e("onAppEnterForeground",Mh()):e("onAppEnterBackground")}function Ev(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&Rc("onTabItemTap",{index:n,text:t,pagePath:o})}function Av(e){e=e>0&&e<1/0?e:0;const t=Math.floor(e/3600),n=Math.floor(e%3600/60),o=Math.floor(e%3600%60),r=(t<10?"0":"")+t;let i=(n<10?"0":"")+n+":"+((o<10?"0":"")+o);return"00"!==r&&(i=r+":"+i),i}function Mv(e,t,n){const o=Yt({gestureType:"none",volumeOld:0,volumeNew:0,currentTimeOld:0,currentTimeNew:0}),r={x:0,y:0};return{state:o,onTouchstart:function(e){const t=e.targetTouches[0];r.x=t.pageX,r.y=t.pageY,o.gestureType="none",o.volumeOld=0,o.currentTimeOld=o.currentTimeNew=0},onTouchmove:function(i){function a(){i.stopPropagation(),i.preventDefault()}n.fullscreen&&a();const s=o.gestureType;if("stop"===s)return;const l=i.targetTouches[0],c=l.pageX,u=l.pageY,d=r,p=t.value;if("progress"===s?function(e){const n=t.value.duration;let r=e/600*n+o.currentTimeOld;r<0?r=0:r>n&&(r=n);o.currentTimeNew=r}(c-d.x):"volume"===s&&function(e){const n=t.value,r=o.volumeOld;let i;"number"==typeof r&&(i=r-e/200,i<0?i=0:i>1&&(i=1),n.volume=i,o.volumeNew=i)}(u-d.y),"none"===s)if(Math.abs(c-d.x)>Math.abs(u-d.y)){if(!e.enableProgressGesture)return void(o.gestureType="stop");o.gestureType="progress",o.currentTimeOld=o.currentTimeNew=p.currentTime,n.fullscreen||a()}else{if(!e.pageGesture)return void(o.gestureType="stop");o.gestureType="volume",o.volumeOld=p.volume,n.fullscreen||a()}},onTouchend:function(e){const n=t.value;"none"!==o.gestureType&&"stop"!==o.gestureType&&(e.stopPropagation(),e.preventDefault()),"progress"===o.gestureType&&o.currentTimeOld!==o.currentTimeNew&&(n.currentTime=o.currentTimeNew),o.gestureType="none"}}}const Pv=hu({name:"Video",props:{id:{type:String,default:""},src:{type:String,default:""},duration:{type:[Number,String],default:""},controls:{type:[Boolean,String],default:!0},danmuList:{type:Array,default:()=>[]},danmuBtn:{type:[Boolean,String],default:!1},enableDanmu:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},loop:{type:[Boolean,String],default:!1},muted:{type:[Boolean,String],default:!1},objectFit:{type:String,default:"contain"},poster:{type:String,default:""},direction:{type:[String,Number],default:""},showProgress:{type:Boolean,default:!0},initialTime:{type:[String,Number],default:0},showFullscreenBtn:{type:[Boolean,String],default:!0},pageGesture:{type:[Boolean,String],default:!1},enableProgressGesture:{type:[Boolean,String],default:!0},showPlayBtn:{type:[Boolean,String],default:!0},showCenterPlayBtn:{type:[Boolean,String],default:!0}},emits:["fullscreenchange","progress","loadedmetadata","waiting","error","play","pause","ended","timeupdate"],setup(e,{emit:t,attrs:n,slots:o}){const r=ln(null),i=ln(null),a=vu(r,t),{state:s}=ng(),{$attrs:l}=gg({excludeListeners:!0}),{t:c}=kl();Rl();const{videoRef:u,state:d,play:p,pause:h,stop:g,seek:m,playbackRate:v,toggle:y,onDurationChange:b,onLoadedMetadata:_,onProgress:w,onWaiting:x,onVideoError:T,onPlay:S,onPause:k,onEnded:C,onTimeUpdate:E}=function(e,t,n){const o=ln(null),r=xi((()=>Mu(e.src))),i=xi((()=>"true"===e.muted||!0===e.muted)),a=Yt({start:!1,src:r,playing:!1,currentTime:0,duration:0,progress:0,buffered:0,muted:i});function s(e){const t=e.target,n=t.buffered;n.length&&(a.buffered=n.end(n.length-1)/t.duration*100)}function l(){o.value.pause()}function c(e){const t=o.value;"number"!=typeof(e=Number(e))||isNaN(e)||(t.currentTime=e)}return eo((()=>r.value),(()=>{a.playing=!1,a.currentTime=0})),eo((()=>a.buffered),(e=>{n("progress",{},{buffered:e})})),eo((()=>i.value),(e=>{o.value.muted=e})),{videoRef:o,state:a,play:function(){const e=o.value;a.start=!0,e.play()},pause:l,stop:function(){c(0),l()},seek:c,playbackRate:function(e){o.value.playbackRate=e},toggle:function(){const e=o.value;a.playing?e.pause():e.play()},onDurationChange:function({target:e}){a.duration=e.duration},onLoadedMetadata:function(t){const o=Number(e.initialTime)||0,r=t.target;o>0&&(r.currentTime=o),n("loadedmetadata",t,{width:r.videoWidth,height:r.videoHeight,duration:r.duration}),s(t)},onProgress:s,onWaiting:function(e){n("waiting",e,{})},onVideoError:function(e){a.playing=!1,n("error",e,{})},onPlay:function(e){a.start=!0,a.playing=!0,n("play",e,{})},onPause:function(e){a.playing=!1,n("pause",e,{})},onEnded:function(e){a.playing=!1,n("ended",e,{})},onTimeUpdate:function(e){const t=e.target,o=a.currentTime=t.currentTime;n("timeupdate",e,{currentTime:o,duration:t.duration})}}}(e,0,a),{state:A,danmuRef:M,updateDanmu:P,toggleDanmu:I,sendDanmu:O}=function(e,t){const n=ln(null),o=Yt({enable:Boolean(e.enableDanmu)});let r={time:0,index:-1};const i=f(e.danmuList)?JSON.parse(JSON.stringify(e.danmuList)):[];function a(e){const t=document.createElement("p");t.className="uni-video-danmu-item",t.innerText=e.text;let o=`bottom: ${100*Math.random()}%;color: ${e.color};`;t.setAttribute("style",o),n.value.appendChild(t),setTimeout((function(){o+="left: 0;-webkit-transform: translateX(-100%);transform: translateX(-100%);",t.setAttribute("style",o),setTimeout((function(){t.remove()}),4e3)}),17)}return i.sort((function(e,t){return(e.time||0)-(t.time||0)})),{state:o,danmuRef:n,updateDanmu:function(e){const n=e.target.currentTime,s=r,l={time:n,index:s.index};if(n>s.time)for(let r=s.index+1;r<i.length;r++){const e=i[r];if(!(n>=(e.time||0)))break;l.index=r,t.playing&&o.enable&&a(e)}else if(n<s.time)for(let t=s.index-1;t>-1&&n<=(i[t].time||0);t--)l.index=t-1;r=l},toggleDanmu:function(){o.enable=!o.enable},sendDanmu:function(e){i.splice(r.index+1,0,{text:String(e.text),color:e.color,time:t.currentTime||0})}}}(e,d),{state:L,onFullscreenChange:$,emitFullscreenChange:D,toggleFullscreen:R,requestFullScreen:B,exitFullScreen:N}=function(e,t,n,o,r){const i=Yt({fullscreen:!1}),a=/^Apple/.test(navigator.vendor);function s(t){i.fullscreen=t,e("fullscreenchange",{},{fullScreen:t,direction:"vertical"})}function l(e){const i=r.value,l=t.value,c=n.value;let u;e?!document.fullscreenEnabled&&!document.webkitFullscreenEnabled||a&&!o.userAction?c.webkitEnterFullScreen?c.webkitEnterFullScreen():(u=!0,l.remove(),l.classList.add("uni-video-type-fullscreen"),document.body.appendChild(l)):l[document.fullscreenEnabled?"requestFullscreen":"webkitRequestFullscreen"]():document.fullscreenEnabled||document.webkitFullscreenEnabled?document.fullscreenElement?document.exitFullscreen():document.webkitFullscreenElement&&document.webkitExitFullscreen():c.webkitExitFullScreen?c.webkitExitFullScreen():(u=!0,l.remove(),l.classList.remove("uni-video-type-fullscreen"),i.appendChild(l)),u&&s(e)}function c(){l(!1)}return qo(c),{state:i,onFullscreenChange:function(e,t){t&&document.fullscreenEnabled||s(!(!document.fullscreenElement&&!document.webkitFullscreenElement))},emitFullscreenChange:s,toggleFullscreen:l,requestFullScreen:function(){l(!0)},exitFullScreen:c}}(a,i,u,s,r),{state:q,onTouchstart:j,onTouchend:z,onTouchmove:V}=Mv(e,u,L),{state:F,progressRef:U,ballRef:W,clickProgress:H,toggleControls:Y}=function(e,t,n){const o=ln(null),r=ln(null),i=xi((()=>e.showCenterPlayBtn&&!t.start)),a=ln(!0),s=xi((()=>!i.value&&e.controls&&a.value)),l=Yt({touching:!1,controlsTouching:!1,centerPlayBtnShow:i,controlsShow:s,controlsVisible:a});let c;function u(){c=setTimeout((()=>{l.controlsVisible=!1}),3e3)}function d(){c&&(clearTimeout(c),c=null)}return qo((()=>{c&&clearTimeout(c)})),eo((()=>l.controlsShow&&t.playing&&!l.controlsTouching),(e=>{e?u():d()})),eo([()=>t.currentTime,()=>{e.duration}],(function(){l.touching||(t.progress=t.currentTime/t.duration*100)})),Ro((()=>{const e=we(!1);let i,a,s,c=!0;const u=r.value;function d(e){const n=e.targetTouches[0],r=n.pageX,l=n.pageY;if(c&&Math.abs(r-i)<Math.abs(l-a))return void p(e);c=!1;const u=o.value.offsetWidth;let d=s+(r-i)/u*100;d<0?d=0:d>100&&(d=100),t.progress=d,e.preventDefault(),e.stopPropagation()}function p(o){l.controlsTouching=!1,l.touching&&(u.removeEventListener("touchmove",d,e),c||(o.preventDefault(),o.stopPropagation(),n(t.duration*t.progress/100)),l.touching=!1)}u.addEventListener("touchstart",(n=>{l.controlsTouching=!0;const o=n.targetTouches[0];i=o.pageX,a=o.pageY,s=t.progress,c=!0,l.touching=!0,u.addEventListener("touchmove",d,e)})),u.addEventListener("touchend",p),u.addEventListener("touchcancel",p)})),{state:l,progressRef:o,ballRef:r,clickProgress:function(e){const r=o.value;let i=e.target,a=e.offsetX;for(;i&&i!==r;)a+=i.offsetLeft,i=i.parentNode;const s=r.offsetWidth;let l=0;a>=0&&a<=s&&(l=a/s,n(t.duration*l))},toggleControls:function(){l.controlsVisible=!l.controlsVisible},autoHideStart:u,autoHideEnd:d}}(e,d,m);return function(e,t,n,o,r,i,a,s){const l={play:e,stop:n,pause:t,seek:o,sendDanmu:r,playbackRate:i,requestFullScreen:a,exitFullScreen:s};bm(((e,t)=>{let n;switch(e){case"seek":n=t.position;break;case"sendDanmu":n=t;break;case"playbackRate":n=t.rate}e in l&&l[e](n)}),wm(),!0)}(p,h,g,m,O,v,B,N),()=>ti("uni-video",{ref:r,id:e.id,onClick:Y},[ti("div",{ref:i,class:"uni-video-container",onTouchstart:j,onTouchend:z,onTouchmove:V,onFullscreenchange:Sa($,["stop"]),onWebkitfullscreenchange:Sa((e=>$(e,!0)),["stop"])},[ti("video",li({ref:u,style:{"object-fit":e.objectFit},muted:!!e.muted,loop:!!e.loop,src:d.src,poster:e.poster,autoplay:!!e.autoplay},l.value,{class:"uni-video-video","webkit-playsinline":!0,playsinline:!0,onDurationchange:b,onLoadedmetadata:_,onProgress:w,onWaiting:x,onError:T,onPlay:S,onPause:k,onEnded:C,onTimeupdate:e=>{E(e),P(e)},onWebkitbeginfullscreen:()=>D(!0),onX5videoenterfullscreen:()=>D(!0),onWebkitendfullscreen:()=>D(!1),onX5videoexitfullscreen:()=>D(!1)}),null,16,["muted","loop","src","poster","autoplay","webkit-playsinline","playsinline","onDurationchange","onLoadedmetadata","onProgress","onWaiting","onError","onPlay","onPause","onEnded","onTimeupdate","onWebkitbeginfullscreen","onX5videoenterfullscreen","onWebkitendfullscreen","onX5videoexitfullscreen"]),Wo(ti("div",{class:"uni-video-bar uni-video-bar-full",onClick:Sa((()=>{}),["stop"])},[ti("div",{class:"uni-video-controls"},[Wo(ti("div",{class:{"uni-video-control-button":!0,"uni-video-control-button-play":!d.playing,"uni-video-control-button-pause":d.playing},onClick:Sa(y,["stop"])},null,10,["onClick"]),[[ka,e.showPlayBtn]]),Wo(ti("div",{class:"uni-video-current-time"},[Av(d.currentTime)],512),[[ka,e.showProgress]]),Wo(ti("div",{ref:U,class:"uni-video-progress-container",onClick:Sa(H,["stop"])},[ti("div",{class:"uni-video-progress"},[ti("div",{style:{width:d.buffered+"%"},class:"uni-video-progress-buffered"},null,4),ti("div",{ref:W,style:{left:d.progress+"%"},class:"uni-video-ball"},[ti("div",{class:"uni-video-inner"},null)],4)])],8,["onClick"]),[[ka,e.showProgress]]),Wo(ti("div",{class:"uni-video-duration"},[Av(Number(e.duration)||d.duration)],512),[[ka,e.showProgress]])]),Wo(ti("div",{class:{"uni-video-danmu-button":!0,"uni-video-danmu-button-active":A.enable},onClick:Sa(I,["stop"])},[c("uni.video.danmu")],10,["onClick"]),[[ka,e.danmuBtn]]),Wo(ti("div",{class:{"uni-video-fullscreen":!0,"uni-video-type-fullscreen":L.fullscreen},onClick:Sa((()=>R(!L.fullscreen)),["stop"])},null,10,["onClick"]),[[ka,e.showFullscreenBtn]])],8,["onClick"]),[[ka,F.controlsShow]]),Wo(ti("div",{ref:M,style:"z-index: 0;",class:"uni-video-danmu"},null,512),[[ka,d.start&&A.enable]]),F.centerPlayBtnShow&&ti("div",{class:"uni-video-cover",onClick:Sa((()=>{}),["stop"])},[ti("div",{class:"uni-video-cover-play-button",onClick:Sa(p,["stop"])},null,8,["onClick"]),ti("p",{class:"uni-video-cover-duration"},[Av(Number(e.duration)||d.duration)])],8,["onClick"]),ti("div",{class:{"uni-video-toast":!0,"uni-video-toast-volume":"volume"===q.gestureType}},[ti("div",{class:"uni-video-toast-title"},[c("uni.video.volume")]),ti("svg",{class:"uni-video-toast-icon",width:"200px",height:"200px",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},[ti("path",{d:"M475.400704 201.19552l0 621.674496q0 14.856192-10.856448 25.71264t-25.71264 10.856448-25.71264-10.856448l-190.273536-190.273536-149.704704 0q-14.856192 0-25.71264-10.856448t-10.856448-25.71264l0-219.414528q0-14.856192 10.856448-25.71264t25.71264-10.856448l149.704704 0 190.273536-190.273536q10.856448-10.856448 25.71264-10.856448t25.71264 10.856448 10.856448 25.71264zm219.414528 310.837248q0 43.425792-24.28416 80.851968t-64.2816 53.425152q-5.71392 2.85696-14.2848 2.85696-14.856192 0-25.71264-10.570752t-10.856448-25.998336q0-11.999232 6.856704-20.284416t16.570368-14.2848 19.427328-13.142016 16.570368-20.284416 6.856704-32.569344-6.856704-32.569344-16.570368-20.284416-19.427328-13.142016-16.570368-14.2848-6.856704-20.284416q0-15.427584 10.856448-25.998336t25.71264-10.570752q8.57088 0 14.2848 2.85696 39.99744 15.427584 64.2816 53.139456t24.28416 81.137664zm146.276352 0q0 87.422976-48.56832 161.41824t-128.5632 107.707392q-7.428096 2.85696-14.2848 2.85696-15.427584 0-26.284032-10.856448t-10.856448-25.71264q0-22.284288 22.284288-33.712128 31.997952-16.570368 43.425792-25.141248 42.283008-30.855168 65.995776-77.423616t23.712768-99.136512-23.712768-99.136512-65.995776-77.423616q-11.42784-8.57088-43.425792-25.141248-22.284288-11.42784-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 79.99488 33.712128 128.5632 107.707392t48.56832 161.41824zm146.276352 0q0 131.42016-72.566784 241.41312t-193.130496 161.989632q-7.428096 2.85696-14.856192 2.85696-14.856192 0-25.71264-10.856448t-10.856448-25.71264q0-20.570112 22.284288-33.712128 3.999744-2.285568 12.85632-5.999616t12.85632-5.999616q26.284032-14.2848 46.854144-29.140992 70.281216-51.996672 109.707264-129.705984t39.426048-165.132288-39.426048-165.132288-109.707264-129.705984q-20.570112-14.856192-46.854144-29.140992-3.999744-2.285568-12.85632-5.999616t-12.85632-5.999616q-22.284288-13.142016-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 120.563712 51.996672 193.130496 161.989632t72.566784 241.41312z"},null)]),ti("div",{class:"uni-video-toast-value"},[ti("div",{style:{width:100*q.volumeNew+"%"},class:"uni-video-toast-value-content"},[ti("div",{class:"uni-video-toast-volume-grids"},[Ko(10,(()=>ti("div",{class:"uni-video-toast-volume-grids-item"},null)))])],4)])],2),ti("div",{class:{"uni-video-toast":!0,"uni-video-toast-progress":"progress"===q.gestureType}},[ti("div",{class:"uni-video-toast-title"},[Av(q.currentTimeNew)," / ",Av(d.duration)])],2),ti("div",{class:"uni-video-slots"},[o.default&&o.default()])],40,["onTouchstart","onTouchend","onTouchmove","onFullscreenchange","onWebkitfullscreenchange"])],8,["id","onClick"])}});let Iv,Ov=0;function Lv(e,t,n,o){var r,i=document.createElement("script"),a=t.callback||"callback",s="__uni_jsonp_callback_"+Ov++,l=t.timeout||3e4;function c(){clearTimeout(r),delete window[s],i.remove()}window[s]=e=>{v(n)&&n(e),c()},i.onerror=()=>{v(o)&&o(),c()},r=setTimeout((function(){v(o)&&o(),c()}),l),i.src=e+(e.indexOf("?")>=0?"&":"?")+a+"="+s,document.body.appendChild(i)}function $v(e){function t(){const e=this.div;this.getPanes().floatPane.appendChild(e)}function n(){const e=this.div.parentNode;e&&e.removeChild(this.div)}function o(){const t=this.option;this.Text=new e.Text({text:t.content,anchor:"bottom-center",offset:new e.Pixel(0,t.offsetY-16),style:{padding:(t.padding||8)+"px","line-height":(t.fontSize||14)+"px","border-radius":(t.borderRadius||0)+"px","border-color":`${t.bgColor||"#fff"} transparent transparent`,"background-color":t.bgColor||"#fff","box-shadow":"0 2px 6px 0 rgba(114, 124, 245, .5)","text-align":"center","font-size":(t.fontSize||14)+"px",color:t.color||"#000"},position:t.position});(e.event||e.Event).addListener(this.Text,"click",(()=>{this.callback()})),this.Text.setMap(t.map)}function r(){}function i(){this.Text&&this.option.map.remove(this.Text)}function a(){this.Text&&this.option.map.remove(this.Text)}class s{constructor(e={},s){this.createAMapText=o,this.removeAMapText=i,this.createBMapText=r,this.removeBMapText=a,this.onAdd=t,this.construct=t,this.onRemove=n,this.destroy=n,this.option=e||{};const l=this.visible=this.alwaysVisible="ALWAYS"===e.display;if(Wv())this.callback=s,this.visible&&this.createAMapText();else if(Hv())this.visible&&this.createBMapText();else{const t=e.map;this.position=e.position,this.index=1;const n=this.div=document.createElement("div"),o=n.style;o.position="absolute",o.whiteSpace="nowrap",o.transform="translateX(-50%) translateY(-100%)",o.zIndex="1",o.boxShadow=e.boxShadow||"none",o.display=l?"block":"none";const r=this.triangle=document.createElement("div");r.setAttribute("style","position: absolute;white-space: nowrap;border-width: 4px;border-style: solid;border-color: #fff transparent transparent;border-image: initial;font-size: 12px;padding: 0px;background-color: transparent;width: 0px;height: 0px;transform: translate(-50%, 100%);left: 50%;bottom: 0;"),this.setStyle(e),n.appendChild(r),t&&this.setMap(t)}}set onclick(e){this.div.onclick=e}get onclick(){return this.div.onclick}setOption(e){this.option=e,"ALWAYS"===e.display?this.alwaysVisible=this.visible=!0:this.alwaysVisible=!1,Wv()?this.visible&&this.createAMapText():Hv()?this.visible&&this.createBMapText():(this.setPosition(e.position),this.setStyle(e))}setStyle(e){const t=this.div,n=t.style;t.innerText=e.content||"",n.lineHeight=(e.fontSize||14)+"px",n.fontSize=(e.fontSize||14)+"px",n.padding=(e.padding||8)+"px",n.color=e.color||"#000",n.borderRadius=(e.borderRadius||0)+"px",n.backgroundColor=e.bgColor||"#fff",n.marginTop="-"+((e.top||0)+5)+"px",this.triangle.style.borderColor=`${e.bgColor||"#fff"} transparent transparent`}setPosition(e){this.position=e,this.draw()}draw(){const e=this.getProjection();if(!this.position||!this.div||!e)return;const t=e.fromLatLngToDivPixel(this.position),n=this.div.style;n.left=t.x+"px",n.top=t.y+"px"}changed(){this.div.style.display=this.visible?"block":"none"}}if(!Wv()&&!Hv()){const t=new(e.OverlayView||e.Overlay);s.prototype.setMap=t.setMap,s.prototype.getMap=t.getMap,s.prototype.getPanes=t.getPanes,s.prototype.getProjection=t.getProjection,s.prototype.map_changed=t.map_changed,s.prototype.set=t.set,s.prototype.get=t.get,s.prototype.setOptions=t.setValues,s.prototype.bindTo=t.bindTo,s.prototype.bindsTo=t.bindsTo,s.prototype.notify=t.notify,s.prototype.setValues=t.setValues,s.prototype.unbind=t.unbind,s.prototype.unbindAll=t.unbindAll,s.prototype.addListener=t.addListener}return s}const Dv={};function Rv(e,t){const n=Vv();if(!n.key)return void console.error("Map key not configured.");const o=Dv[n.type]=Dv[n.type]||[];if(Iv)t(Iv);else if(window[n.type]&&window[n.type].maps)Iv=Wv()||Hv()?window[n.type]:window[n.type].maps,Iv.Callout=Iv.Callout||$v(Iv),t(Iv);else if(o.length)o.push(t);else{o.push(t);const r=window,i="__map_callback__"+n.type;r[i]=function(){delete r[i],Iv=Wv()||Hv()?window[n.type]:window[n.type].maps,Iv.Callout=$v(Iv),o.forEach((e=>e(Iv))),o.length=0},Wv()&&function(e){window._AMapSecurityConfig={securityJsCode:e.securityJsCode||"",serviceHost:e.serviceHost||""}}(n);const a=document.createElement("script");let s=Bv(n.type);n.type===zv.QQ&&e.push("geometry"),e.length&&(s+=`libraries=${e.join("%2C")}&`),n.type===zv.BMAP?a.src=`${s}ak=${n.key}&callback=${i}`:a.src=`${s}key=${n.key}&callback=${i}`,a.onerror=function(){console.error("Map load failed.")},document.body.appendChild(a)}}const Bv=e=>({qq:"https://map.qq.com/api/js?v=2.exp&",google:"https://maps.googleapis.com/maps/api/js?",AMap:"https://webapi.amap.com/maps?v=2.0&",BMapGL:"https://api.map.baidu.com/api?type=webgl&v=1.0&"}[e]);const Nv="M13.3334375 16 q0.033125 1.1334375 0.783125 1.8834375 q0.75 0.75 1.8834375 0.75 q1.1334375 0 1.8834375 -0.75 q0.75 -0.75 0.75 -1.8834375 q0 -1.1334375 -0.75 -1.8834375 q-0.75 -0.75 -1.8834375 -0.75 q-1.1334375 0 -1.8834375 0.75 q-0.75 0.75 -0.783125 1.8834375 ZM30.9334375 14.9334375 l-1.1334375 0 q-0.5 -5.2 -4.0165625 -8.716875 q-3.516875 -3.5165625 -8.716875 -4.0165625 l0 -1.1334375 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 l0 1.1334375 q-5.2 0.5 -8.716875 4.0165625 q-3.5165625 3.516875 -4.0165625 8.716875 l-1.1334375 0 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 l1.1334375 0 q0.5 5.2 4.0165625 8.716875 q3.516875 3.5165625 8.716875 4.0165625 l0 1.1334375 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 l0 -1.1334375 q5.2 -0.5 8.716875 -4.0165625 q3.5165625 -3.516875 4.0165625 -8.716875 l1.1334375 0 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 ZM17.0665625 27.6665625 l0 -2.0665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 l0 2.0665625 q-4.3 -0.4665625 -7.216875 -3.383125 q-2.916875 -2.916875 -3.3834375 -7.216875 l2.0665625 0 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 l-2.0665625 0 q0.4665625 -4.3 3.3834375 -7.216875 q2.9165625 -2.916875 7.216875 -3.3834375 l0 2.0665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 l0 -2.0665625 q4.3 0.4665625 7.216875 3.3834375 q2.9165625 2.9165625 3.383125 7.216875 l-2.0665625 0 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 l2.0665625 0 q-0.4665625 4.3 -3.383125 7.216875 q-2.916875 2.9165625 -7.216875 3.383125 Z",qv="data:image/png;base64,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",jv="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAACcCAMAAAC3Fl5oAAAB3VBMVEVMaXH/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/EhL/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/Dw//AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/AAD/GRn/NTX/Dw//Fhb/AAD/AAD/AAD/GRn/GRn/Y2P/AAD/AAD/ExP/Ghr/AAD/AAD/MzP/GRn/AAD/Hh7/AAD/RUX/AAD/AAD/AAD/AAD/AAD/AAD/Dg7/AAD/HR3/Dw//FRX/SUn/AAD/////kJD/DQ3/Zmb/+/v/wMD/mJj/6en/vb3/1NT//Pz/ODj/+fn/3Nz/nJz/j4//9/f/7e3/9vb/7Oz/2Nj/x8f/Ozv/+Pj/3d3/nZ3/2dn//f3/6Oj/2tr/v7//09P/vr7/mZn/l5cdSvP3AAAAe3RSTlMAAhLiZgTb/vztB/JMRhlp6lQW86g8mQ4KFPs3UCH5U8huwlesWtTYGI7RsdVeJGfTW5rxnutLsvXWF8vQNdo6qQbuz7D4hgVIx2xtw8GC1TtZaIw0i84P98tU0/fsj7PKaAgiZZxeVfo8Z52eg1P0nESrENnjXVPUgw/uuSmDAAADsUlEQVR42u3aZ3cTRxgF4GtbYleSLdnGcsENG2ODjbExEHrvhAQCIb1Bem+QdkeuuFMNBBJIfmuOckzZI8/srHYmH3Lm+QNXK632LTvQ03Tu/IWeU/tTGTKT2n+q58L5c00wpXJd47DHEt5w47pKxLbhdLdPKb/7dBYxVLxw1GcI/2h1BcpzKNFHLX2JQ4gumaiitqpEEhEdOMJI9h5AFC3feYzI+7IF2tpSLEOqDXpObPRYFm/jCWho/4Ble7MdoT7fzhhq9yHEz28wltU1UPrJZ0wd66HwicfYvEFIfePTAP8tSLTupBHvtGJFH9bSkNrNWEHzERrT34xSH9Ogr1CijkbVAUH1KRqVqkdQAw07iIAaGlcTqI+/0LjeJJ5J0IIEnkpXMdzs4sTtW9dnZq7fuj2xOMtwVWk88RHDjBYejYvnjD8qjOpfQsUqhvj7oSjxcJIhVj3pyKqpNjYvVjQ/RrXq5YABKi3MCYm5BSrtWO5v11DlmlC4RpU1WRS9SJU7QukOVbpQ9JLu549+Dd0AUOlTbkGEuk85vxLAK5QbuytC3R2j3HoAjZSbFxrmKTcCoJdSk0LLJKV6gSaPMqNTQsvUKGW8JrxKqUWhaZFSeWyh1LTQNE2pHF6mzOy40DQ+S5mLimJcENoKlOnBWsr8KbRNUGYt5LXgd6HtD3lNQIoyN4S2G5RJIUOZm0LbTcqsBqVmhLYZSlkPsP4VWf+Rrd+m1v9o9h8Vv5p42C1R5qL1x7WRglOgVN52yfwNOBu76P+lLPoYidu23KPciIHGa07ZeIW1jvcNtI7q5vexCPGYCmf+m/Y9a3sAwQ5bI9T7ukPgPcn9GToEao+xk1OixJT+GIsvNAbx6eAgPq0xiF+KtkpYKhRXCQ8eFFcJhSWGu3rZ8jJkCM8kz9K4TUnrC6mAgzTsB9tLwQ2W15qfosQ2GrQNpZr7aczbzVjBZsvLcaC1g0bsbIVEnU8DOr6H1KDH2LwtUBi0/JII6Dxm9zUXkH+XMWzfh1Dte1i2Pe3QkC77Zel7aehpO8wyHG6Dtt0NjKxhN6I4uSli/TqJiJJDUQ4NDCURXTrXRy1XcumyD24M+AzhD1RXIIZsl/LoyZmurJHDM7s8lvB2FQ/PmPJ6PseAXP5HGMYAAC7ABbgAF+ACXIALcAEuwAW4ABfgAlyAC3ABLsAFuID/d8Cx4NEt8/byOf0wLnis8zjMq9/Kp7bWw4JOj8u8TlhRl+G/Mp2wpOX48GffvvZ1CyL4B53LAS6zb08EAAAAAElFTkSuQmCC";var zv=(e=>(e.QQ="qq",e.GOOGLE="google",e.AMAP="AMap",e.BMAP="BMapGL",e.UNKNOWN="",e))(zv||{});function Vv(){return __uniConfig.bMapKey?{type:"BMapGL",key:__uniConfig.bMapKey}:__uniConfig.qqMapKey?{type:"qq",key:__uniConfig.qqMapKey}:__uniConfig.googleMapKey?{type:"google",key:__uniConfig.googleMapKey}:__uniConfig.aMapKey?{type:"AMap",key:__uniConfig.aMapKey,securityJsCode:__uniConfig.aMapSecurityJsCode,serviceHost:__uniConfig.aMapServiceHost}:{type:"",key:""}}let Fv=!1,Uv=!1;const Wv=()=>Uv?Fv:(Uv=!0,Fv="AMap"===Vv().type),Hv=()=>"BMapGL"===Vv().type;function Yv(e,t,n){const o=Vv();return e&&"WGS84"===e.toUpperCase()||["google"].includes(o.type)||n?Promise.resolve(t):"qq"===o.type?new Promise((e=>{Lv(`https://apis.map.qq.com/jsapi?qt=translate&type=1&points=${t.longitude},${t.latitude}&key=${o.key}&output=jsonp&pf=jsapi&ref=jsapi`,{callback:"cb"},(n=>{if("detail"in n&&"points"in n.detail&&n.detail.points.length){const{lng:o,lat:r}=n.detail.points[0];e({longitude:o,latitude:r,altitude:t.altitude,accuracy:t.accuracy,altitudeAccuracy:t.altitudeAccuracy,heading:t.heading,speed:t.speed})}else e(t)}),(()=>e(t)))})):"AMap"===o.type?new Promise((e=>{Rv([],(()=>{window.AMap.convertFrom([t.longitude,t.latitude],"gps",((n,o)=>{if("ok"===o.info&&o.locations.length){const{lat:n,lng:r}=o.locations[0];e({longitude:r,latitude:n,altitude:t.altitude,accuracy:t.accuracy,altitudeAccuracy:t.altitudeAccuracy,heading:t.heading,speed:t.speed})}else e(t)}))}))})):Promise.reject(new Error("translate coordinate system faild"))}const Xv=gu({name:"MapMarker",props:{id:{type:[Number,String],default:""},latitude:{type:[Number,String],require:!0},longitude:{type:[Number,String],require:!0},title:{type:String,default:""},iconPath:{type:String,require:!0},rotate:{type:[Number,String],default:0},alpha:{type:[Number,String],default:1},width:{type:[Number,String],default:""},height:{type:[Number,String],default:""},callout:{type:Object,default:null},label:{type:Object,default:null},anchor:{type:Object,default:null},clusterId:{type:[Number,String],default:""},customCallout:{type:Object,default:null},ariaLabel:{type:String,default:""}},setup(e){const t=String(isNaN(Number(e.id))?"":e.id),n=Qn("onMapReady"),o=function(e){const t="uni-map-marker-label-"+e,n=document.createElement("style");return n.id=t,document.head.appendChild(n),jo((()=>{n.remove()})),function(e){const o=Object.assign({},e,{position:"absolute",top:"70px",borderStyle:"solid"}),r=document.createElement("div");return Object.keys(o).forEach((e=>{r.style[e]=o[e]||""})),n.innerText=`.${t}{${r.getAttribute("style")}}`,t}}(t);let r;function i(e){Wv()?e.removeAMapText():e.setMap(null)}if(n(((n,a,s)=>{function l(e){const l=e.title;let c;c=Wv()?new a.LngLat(e.longitude,e.latitude):Hv()?new a.Point(e.longitude,e.latitude):new a.LatLng(e.latitude,e.longitude);const u=new Image;let d=0;u.onload=()=>{const p=e.anchor||{};let f,h,g,m,v="number"==typeof p.x?p.x:.5,y="number"==typeof p.y?p.y:1;e.iconPath&&(e.width||e.height)?(h=e.width||u.width/u.height*e.height,g=e.height||u.height/u.width*e.width):(h=u.width/2,g=u.height/2),d=g,m=g-(g-y*g),f="MarkerImage"in a?new a.MarkerImage(u.src,null,null,new a.Point(v*h,y*g),new a.Size(h,g)):"Icon"in a?new a.Icon({image:u.src,size:new a.Size(h,g),imageSize:new a.Size(h,g),imageOffset:new a.Pixel(v*h,y*g)}):{url:u.src,anchor:new a.Point(v,y),size:new a.Size(h,g)},Hv()?(r=new a.Marker(new a.Point(c.lng,c.lat)),n.addOverlay(r)):(r.setPosition(c),r.setIcon(f)),"setRotation"in r&&r.setRotation(e.rotate||0);const b=e.label||{};let _;if("label"in r&&(r.label.setMap(null),delete r.label),b.content){const e={borderColor:b.borderColor,borderWidth:(Number(b.borderWidth)||0)+"px",padding:(Number(b.padding)||0)+"px",borderRadius:(Number(b.borderRadius)||0)+"px",backgroundColor:b.bgColor,color:b.color,fontSize:(b.fontSize||14)+"px",lineHeight:(b.fontSize||14)+"px",marginLeft:(Number(b.anchorX||b.x)||0)+"px",marginTop:(Number(b.anchorY||b.y)||0)+"px"};if("Label"in a)_=new a.Label({position:c,map:n,clickable:!1,content:b.content,style:e}),r.label=_;else if("setLabel"in r)if(Wv()){const t=`<div style="\n                  margin-left:${e.marginLeft};\n                  margin-top:${e.marginTop};\n                  padding:${e.padding};\n                  background-color:${e.backgroundColor};\n                  border-radius:${e.borderRadius};\n                  line-height:${e.lineHeight};\n                  color:${e.color};\n                  font-size:${e.fontSize};\n\n                  ">\n                  ${b.content}\n                <div>`;r.setLabel({content:t,direction:"bottom-right"})}else{const t=o(e);r.setLabel({text:b.content,color:e.color,fontSize:e.fontSize,className:t})}}const w=e.callout||{};let x,T=r.callout;if(w.content||l){Wv()&&w.content&&(w.content=w.content.replaceAll("\n","<br/>"));const o="0px 0px 3px 1px rgba(0,0,0,0.5)";let i=-d/2;if((e.width||e.height)&&(i+=14-d/2),x=w.content?{position:c,map:n,top:m,offsetY:i,content:w.content,color:w.color,fontSize:w.fontSize,borderRadius:w.borderRadius,bgColor:w.bgColor,padding:w.padding,boxShadow:w.boxShadow||o,display:w.display}:{position:c,map:n,top:m,offsetY:i,content:l,boxShadow:o},T)T.setOption(x);else if(Wv()){const e=e=>{""!==e&&s("callouttap",{},{markerId:Number(e)})};T=r.callout=new a.Callout(x,e)}else T=r.callout=new a.Callout(x),T.div.onclick=function(e){""!==t&&s("callouttap",e,{markerId:Number(t)}),e.stopPropagation(),e.preventDefault()},Vv().type===zv.GOOGLE&&(T.div.ontouchstart=function(e){e.stopPropagation()},T.div.onpointerdown=function(e){e.stopPropagation()})}else T&&(i(T),delete r.callout)},e.iconPath?u.src=Mu(e.iconPath):console.error("Marker.iconPath is required.")}!function(e){Hv()||(r=new a.Marker({map:n,flat:!0,autoRotation:!1})),l(e);const o=a.event||a.Event;Hv()||o.addListener(r,"click",(()=>{const n=r.callout;if(n&&!n.alwaysVisible)if(Wv())n.visible=!n.visible,n.visible?r.callout.createAMapText():r.callout.removeAMapText();else if(n.set("visible",!n.visible),n.visible){const e=n.div,t=e.parentNode;t.removeChild(e),t.appendChild(e)}t&&s("markertap",{},{markerId:Number(t),latitude:e.latitude,longitude:e.longitude})}))}(e),eo(e,l)})),t){const e=Qn("addMapChidlContext"),o=Qn("removeMapChidlContext"),i={id:t,translate(e){n(((t,n,o)=>{const i=e.destination,a=e.duration,s=!!e.autoRotate;let l=Number(e.rotate)||0,c=0;"getRotation"in r&&(c=r.getRotation());const u=r.getPosition(),d=new n.LatLng(i.latitude,i.longitude),p=n.geometry.spherical.computeDistanceBetween(u,d)/1e3/(("number"==typeof a?a:1e3)/36e5),f=n.event||n.Event,h=f.addListener(r,"moving",(e=>{const t=e.latLng,n=r.label;n&&n.setPosition(t);const o=r.callout;o&&o.setPosition(t)})),g=f.addListener(r,"moveend",(()=>{g.remove(),h.remove(),r.lastPosition=u,r.setPosition(d);const t=r.label;t&&t.setPosition(d);const n=r.callout;n&&n.setPosition(d);const o=e.animationEnd;v(o)&&o()}));let m=0;s&&(r.lastPosition&&(m=n.geometry.spherical.computeHeading(r.lastPosition,u)),l=n.geometry.spherical.computeHeading(u,d)-m),"setRotation"in r&&r.setRotation(c+l),"moveTo"in r?r.moveTo(d,p):(r.setPosition(d),f.trigger(r,"moveend",{}))}))}};e(i),jo((()=>o(i)))}return jo((function(){r&&(r.label&&"setMap"in r.label&&r.label.setMap(null),r.callout&&i(r.callout),r.setMap(null))})),()=>null}});function Gv(e){if(!e)return{r:0,g:0,b:0,a:0};let t=e.slice(1);const n=t.length;if(![3,4,6,8].includes(n))return{r:0,g:0,b:0,a:0};3!==n&&4!==n||(t=t.replace(/(\w{1})/g,"$1$1"));let[o,r,i,a]=t.match(/(\w{2})/g);const s=parseInt(o,16),l=parseInt(r,16),c=parseInt(i,16);return a?{r:s,g:l,b:c,a:(`0x100${a}`-65536)/255}:{r:s,g:l,b:c,a:1}}const Jv={points:{type:Array,require:!0},color:{type:String,default:"#000000"},width:{type:[Number,String],default:""},dottedLine:{type:[Boolean,String],default:!1},arrowLine:{type:[Boolean,String],default:!1},arrowIconPath:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderWidth:{type:[Number,String],default:""},colorList:{type:Array,default:()=>[]},level:{type:String,default:""}},Qv=gu({name:"MapPolyline",props:Jv,setup(e){let t,n;function o(){t&&t.setMap(null),n&&n.setMap(null)}return Qn("onMapReady")(((r,i)=>{function a(e){const o=[];e.points.forEach((e=>{let t;t=Wv()?[e.longitude,e.latitude]:Hv()?new i.Point(e.longitude,e.latitude):new i.LatLng(e.latitude,e.longitude),o.push(t)}));const a=Number(e.width)||1,{r:s,g:l,b:c,a:u}=Gv(e.color),{r:d,g:p,b:f,a:h}=Gv(e.borderColor),g={map:r,clickable:!1,path:o,strokeWeight:a,strokeColor:e.color||void 0,strokeDashStyle:e.dottedLine?"dash":"solid"},m=Number(e.borderWidth)||0,v={map:r,clickable:!1,path:o,strokeWeight:a+2*m,strokeColor:e.borderColor||void 0,strokeDashStyle:e.dottedLine?"dash":"solid"};"Color"in i?(g.strokeColor=new i.Color(s,l,c,u),v.strokeColor=new i.Color(d,p,f,h)):(g.strokeColor=`rgb(${s}, ${l}, ${c})`,g.strokeOpacity=u,v.strokeColor=`rgb(${d}, ${p}, ${f})`,v.strokeOpacity=h),m&&(n=new i.Polyline(v)),Hv()?(t=new i.Polyline(g.path,g),r.addOverlay(t)):t=new i.Polyline(g)}a(e),eo(e,(function(e){o(),a(e)}))})),jo(o),()=>null}}),Kv=gu({name:"MapCircle",props:{latitude:{type:[Number,String],require:!0},longitude:{type:[Number,String],require:!0},color:{type:String,default:"#000000"},fillColor:{type:String,default:"#00000000"},radius:{type:[Number,String],require:!0},strokeWidth:{type:[Number,String],default:""},level:{type:String,default:""}},setup(e){let t;function n(){t&&t.setMap(null)}return Qn("onMapReady")(((o,r)=>{function i(e){const n=Wv()||Hv()?[e.longitude,e.latitude]:new r.LatLng(e.latitude,e.longitude),i={map:o,center:n,clickable:!1,radius:e.radius,strokeWeight:Number(e.strokeWidth)||1,strokeDashStyle:"solid"};if(Hv())i.strokeColor=e.color,i.fillColor=e.fillColor||"#000",i.fillOpacity=1;else{const{r:t,g:n,b:o,a:a}=Gv(e.fillColor),{r:s,g:l,b:c,a:u}=Gv(e.color);"Color"in r?(i.fillColor=new r.Color(t,n,o,a),i.strokeColor=new r.Color(s,l,c,u)):(i.fillColor=`rgb(${t}, ${n}, ${o})`,i.fillOpacity=a,i.strokeColor=`rgb(${s}, ${l}, ${c})`,i.strokeOpacity=u)}if(Hv()){let e=new r.Point(i.center[0],i.center[1]);t=new r.Circle(e,i.radius,i),o.addOverlay(t)}else t=new r.Circle(i),Wv()&&o.add(t)}i(e),eo(e,(function(e){n(),i(e)}))})),jo(n),()=>null}}),Zv={id:{type:[Number,String],default:""},position:{type:Object,required:!0},iconPath:{type:String,required:!0},clickable:{type:[Boolean,String],default:""},trigger:{type:Function,required:!0}},ey=gu({name:"MapControl",props:Zv,setup(e){const t=xi((()=>Mu(e.iconPath))),n=xi((()=>{let t=`top:${e.position.top||0}px;left:${e.position.left||0}px;`;return e.position.width&&(t+=`width:${e.position.width}px;`),e.position.height&&(t+=`height:${e.position.height}px;`),t})),o=t=>{e.clickable&&e.trigger("controltap",t,{controlId:e.id})};return()=>ti("div",{class:"uni-map-control"},[ti("img",{src:t.value,style:n.value,class:"uni-map-control-icon",onClick:o},null,12,["src","onClick"])])}}),ty=fe((()=>{Pp.forEach((e=>{ny.prototype[e]=function(t){v(t)&&this._events[e].push(t)}})),Ip.forEach((e=>{ny.prototype[e]=function(t){var n=this._events[e.replace("off","on")],o=n.indexOf(t);o>=0&&n.splice(o,1)}}))}));class ny{constructor(){this._src="";var e=this._audio=new Audio;this._stoping=!1;["src","autoplay","loop","duration","currentTime","paused","volume"].forEach((t=>{Object.defineProperty(this,t,{set:"src"===t?t=>(e.src=Mu(t),this._src=t,t):n=>(e[t]=n,n),get:"src"===t?()=>this._src:()=>e[t]})})),this.startTime=0,Object.defineProperty(this,"obeyMuteSwitch",{set:()=>!1,get:()=>!1}),Object.defineProperty(this,"buffered",{get(){var t=e.buffered;return t.length?t.end(t.length-1):0}}),this._events={},Pp.forEach((e=>{this._events[e]=[]})),e.addEventListener("loadedmetadata",(()=>{var t=Number(this.startTime)||0;t>0&&(e.currentTime=t)}));var t=["canplay","pause","seeking","seeked","timeUpdate"];t.concat(["play","ended","error","waiting"]).forEach((n=>{e.addEventListener(n.toLowerCase(),(()=>{if(this._stoping&&t.indexOf(n)>=0)return;const e=`on${n.slice(0,1).toUpperCase()}${n.slice(1)}`;this._events[e].forEach((e=>{e()}))}),!1)})),ty()}play(){this._stoping=!1,this._audio.play()}pause(){this._audio.pause()}stop(){this._stoping=!0,this._audio.pause(),this._audio.currentTime=0,this._events.onStop.forEach((e=>{e()}))}seek(e){this._stoping=!1,"number"!=typeof(e=Number(e))||isNaN(e)||(this._audio.currentTime=e)}destroy(){this.stop()}}const oy=Md(0,(()=>new ny)),ry=Pd("makePhoneCall",(({phoneNumber:e},{resolve:t})=>(window.location.href=`tel:${e}`,t()))),iy=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let ay;function sy(){if(ay=ay||iy.__DC_STAT_UUID,!ay){ay=Date.now()+""+Math.floor(1e7*Math.random());try{iy.__DC_STAT_UUID=ay}catch(e){}}return ay}function ly(){if(!0!==__uniConfig.darkmode)return y(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function cy(){let e,t="0",n="",o="phone";const r=navigator.language;if(Ou){e="iOS";const o=Pu.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=Pu.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(Iu){e="Android";const o=Pu.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=Pu.match(/\((.+?)\)/),i=r?r[1].split(";"):Pu.split(" "),a=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<a.length;e++)if(a[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(Ru)n="iPad",e="iOS",o="pad",t=v(window.BigInt)?"14.0":"13.0";else if(Lu||$u||Du){n="PC",e="PC",o="pc",t="0";let r=Pu.match(/\((.+?)\)/)[1];if(Lu){switch(e="Windows",Lu[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if($u){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(Du){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,a=e.toLocaleLowerCase();let s="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)s="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(Pu)&&(s=t[n],l=Pu.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:a,browserName:s.toLocaleLowerCase(),browserVersion:l,language:r,deviceType:o,ua:Pu,osname:e,osversion:t,theme:ly()}}const uy=Md(0,(()=>{const e=window.devicePixelRatio,t=Bu(),n=Nu(t),o=qu(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=ju(o);let a=window.innerHeight;const s=dc.top,l={left:dc.left,right:i-dc.right,top:dc.top,bottom:a-dc.bottom,width:i-dc.left-dc.right,height:a-dc.top-dc.bottom},{top:c,bottom:u}=mc();return a-=c,a-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:a,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:s,safeArea:l,safeAreaInsets:{top:dc.top,right:dc.right,bottom:dc.bottom,left:dc.left},screenTop:r-a}}));let dy,py=!0;function fy(){py&&(dy=cy())}const hy=Md(0,(()=>{fy();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:a,deviceType:s}=dy;return{brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:sy(),deviceOrientation:a,deviceType:s,model:o,platform:r,system:i}})),gy=Md(0,(()=>{fy();const{theme:e,language:t,browserName:n,browserVersion:o}=dy;return{appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:nf?nf():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:""}})),my=Md(0,(()=>{py=!0,fy(),py=!1;const e=uy(),t=hy(),n=gy();py=!0;const{ua:o,browserName:r,browserVersion:i,osname:a,osversion:s}=dy,l=c(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:a.toLocaleLowerCase(),osVersion:s,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return S(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)})),vy=Pd("getSystemInfo",((e,{resolve:t})=>t(my())));function yy(){xy().then((({networkType:e})=>{Dx.invokeOnCallback("onNetworkStatusChange",{isConnected:"none"!==e,networkType:e})}))}function by(){return navigator.connection||navigator.webkitConnection||navigator.mozConnection}const _y=Cd("onNetworkStatusChange",(()=>{const e=by();e?e.addEventListener("change",yy):(window.addEventListener("offline",yy),window.addEventListener("online",yy))})),wy=Ed("offNetworkStatusChange",(()=>{const e=by();e?e.removeEventListener("change",yy):(window.removeEventListener("offline",yy),window.removeEventListener("online",yy))})),xy=Pd("getNetworkType",((e,{resolve:t})=>{const n=by();let o="unknown";return n?(o=n.type,"cellular"===o&&n.effectiveType?o=n.effectiveType.replace("slow-",""):!o&&n.effectiveType?o=n.effectiveType:["none","wifi"].includes(o)||(o="unknown")):!1===navigator.onLine&&(o="none"),t({networkType:o})}));let Ty=null;const Sy=Cd("onAccelerometer",(()=>{Cy()})),ky=Ed("offAccelerometer",(()=>{Ey()})),Cy=Pd("startAccelerometer",((e,{resolve:t,reject:n})=>{if(window.DeviceMotionEvent){if(!Ty){if(DeviceMotionEvent.requestPermission)return void DeviceMotionEvent.requestPermission().then((e=>{"granted"===e?(o(),t()):n(`${e}`)})).catch((e=>{n(`${e}`)}));o()}t()}else n();function o(){Ty=function(e){const t=e.acceleration||e.accelerationIncludingGravity;Dx.invokeOnCallback("onAccelerometer",{x:t&&t.x||0,y:t&&t.y||0,z:t&&t.z||0})},window.addEventListener("devicemotion",Ty,!1)}})),Ey=Pd("stopAccelerometer",((e,{resolve:t})=>{Ty&&(window.removeEventListener("devicemotion",Ty,!1),Ty=null),t()}));let Ay=null;const My=Cd("onCompass",(()=>{Iy()})),Py=Ed("offCompass",(()=>{Oy()})),Iy=Pd("startCompass",((e,{resolve:t,reject:n})=>{if(window.DeviceOrientationEvent){if(!Ay){if(DeviceOrientationEvent.requestPermission)return void DeviceOrientationEvent.requestPermission().then((e=>{"granted"===e?(o(),t()):n(`${e}`)})).catch((e=>{n(`${e}`)}));o()}t()}else n();function o(){Ay=function(e){const t=360-(null!==e.alpha?e.alpha:360);Dx.invokeOnCallback("onCompass",{direction:t})},window.addEventListener("deviceorientation",Ay,!1)}})),Oy=Pd("stopCompass",((e,{resolve:t})=>{Ay&&(window.removeEventListener("deviceorientation",Ay,!1),Ay=null),t()})),Ly=!!window.navigator.vibrate,$y=Pd("vibrateShort",((e,{resolve:t,reject:n})=>{Ly&&window.navigator.vibrate(15)?t():n("vibrateLong:fail")})),Dy=Pd("vibrateLong",((e,{resolve:t,reject:n})=>{Ly&&window.navigator.vibrate(400)?t():n("vibrateLong:fail")}));var Ry=(e,t,n)=>new Promise(((o,r)=>{var i=e=>{try{s(n.next(e))}catch(EC){r(EC)}},a=e=>{try{s(n.throw(e))}catch(EC){r(EC)}},s=e=>e.done?o(e.value):Promise.resolve(e.value).then(i,a);s((n=n.apply(e,t)).next())}));const By=Pd("getClipboardData",((e,t)=>Ry(void 0,[e,t],(function*(e,{resolve:t,reject:n}){$l();const{t:o}=kl();try{t({data:yield navigator.clipboard.readText()})}catch(r){!function(e,t){const n=document.getElementById("#clipboard"),o=n?n.value:void 0;o?e({data:o}):t()}(t,(()=>{n(`${r} ${o("uni.getClipboardData.fail")}`)}))}})))),Ny=Pd("setClipboardData",((e,t)=>Ry(void 0,[e,t],(function*({data:e},{resolve:t,reject:n}){try{yield navigator.clipboard.writeText(e),t()}catch(o){!function(e,t,n){const o=document.getElementById("#clipboard");o&&o.remove();const r=document.createElement("textarea");r.id="#clipboard",r.style.position="fixed",r.style.top="-9999px",r.style.zIndex="-9999",document.body.appendChild(r),r.value=e,r.select(),r.setSelectionRange(0,r.value.length);const i=document.execCommand("Copy",!1);r.blur(),i?t():n()}(e,t,n)}}))),0,wf);const qy=e=>{Dx.invokeOnCallback("onThemeChange",e)},jy=Cd("onThemeChange",(()=>{Dx.on("onThemeChange",qy)})),zy=Ed("offThemeChange",(()=>{Dx.off("onThemeChange",qy)}));const Vy=Md(0,((e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)})),Fy=Pd("setStorage",(({key:e,data:t},{resolve:n,reject:o})=>{try{Vy(e,t),n()}catch(r){o(r.message)}}));function Uy(e){const t=localStorage&&localStorage.getItem(e);if(!y(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=y(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const Wy=Md(0,(e=>{try{return Uy(e)}catch(t){return""}})),Hy=Pd("getStorage",(({key:e},{resolve:t,reject:n})=>{try{t({data:Uy(e)})}catch(o){n(o.message)}})),Yy=Md(0,(e=>{localStorage&&localStorage.removeItem(e)})),Xy=Pd("removeStorage",(({key:e},{resolve:t})=>{Yy(e),t()})),Gy=Md(0,(()=>{localStorage&&localStorage.clear()})),Jy=Pd("clearStorage",((e,{resolve:t})=>{Gy(),t()})),Qy=Md(0,(()=>{const e=localStorage&&localStorage.length||0,t=[];let n=0;for(let o=0;o<e;o++){const e=localStorage.key(o),r=localStorage.getItem(e)||"";n+=e.length+r.length,"uni-storage-keys"!==e&&t.push(e)}return{keys:t,currentSize:Math.ceil(2*n/1024),limitSize:Number.MAX_VALUE}})),Ky=Pd("getStorageInfo",((e,{resolve:t})=>{t(Qy())})),Zy=Pd("getFileInfo",(({filePath:e},{resolve:t,reject:n})=>{Th(e).then((e=>{t({size:e.size})})).catch((e=>{n(String(e))}))}),0,xf),eb=Pd("openDocument",(({filePath:e},{resolve:t})=>(window.open(e),t())),0,Tf),tb=Pd("hideKeyboard",((e,{resolve:t,reject:n})=>{const o=document.activeElement;!o||"TEXTAREA"!==o.tagName&&"INPUT"!==o.tagName||(o.blur(),t())}));const nb=Pd("getImageInfo",(({src:e},{resolve:t,reject:n})=>{const o=new Image;o.onload=function(){t({width:o.naturalWidth,height:o.naturalHeight,path:0===e.indexOf("/")?window.location.protocol+"//"+window.location.host+e:e})},o.onerror=function(){n()},o.src=e}),0,Of),ob=Pd("getVideoInfo",(({src:e},{resolve:t,reject:n})=>{Th(e,!0).then((e=>e)).catch((()=>null)).then((o=>{const r=document.createElement("video");if(void 0!==r.onloadedmetadata){const i=setTimeout((()=>{r.onloadedmetadata=null,r.onerror=null,n()}),e.startsWith("data:")||e.startsWith("blob:")?300:3e3);r.onloadedmetadata=function(){clearTimeout(i),r.onerror=null,t({size:o?o.size:0,duration:r.duration||0,width:r.videoWidth||0,height:r.videoHeight||0})},r.onerror=function(){clearTimeout(i),r.onloadedmetadata=null,n()},r.src=e}else n()}))}),0,$f),rb={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};function ib({count:e,sourceType:t,type:n,extension:o}){const r=document.createElement("input");return r.type="file",function(e,t){for(const n in t)e.style[n]=t[n]}(r,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),r.accept=o.map((e=>{if("all"!==n){const t=e.replace(".","");return`${n}/${rb[n][t]||t}`}return function(){const e=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!(!e||"micromessenger"!==e[0])}()?".":0===e.indexOf(".")?e:`.${e}`})).join(","),e&&e>1&&(r.multiple=!0),"all"!==n&&t instanceof Array&&1===t.length&&"camera"===t[0]&&r.setAttribute("capture","camera"),r}eg();let ab=null;const sb=Pd("chooseFile",(({count:e,sourceType:t,type:n,extension:o},{resolve:r,reject:i})=>{Ol();const{t:a}=kl();ab&&(document.body.removeChild(ab),ab=null),ab=ib({count:e,sourceType:t,type:n,extension:o}),document.body.appendChild(ab),ab.addEventListener("change",(function(t){const n=t.target,o=[];if(n&&n.files){const t=n.files.length;for(let r=0;r<t;r++){const t=n.files[r];let i;Object.defineProperty(t,"path",{get:()=>(i=i||kh(t),i)}),r<e&&o.push(t)}}r({get tempFilePaths(){return o.map((({path:e})=>e))},tempFiles:o})})),ab.click(),tg()||console.warn(a("uni.chooseFile.notUserActivation"))}),0,If);let lb=null;const cb=Pd("chooseImage",(({count:e,sourceType:t,extension:n},{resolve:o,reject:r})=>{Ol();const{t:i}=kl();lb&&(document.body.removeChild(lb),lb=null),lb=ib({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(lb),lb.addEventListener("change",(function(t){const n=t.target,r=[];if(n&&n.files){const t=n.files.length;for(let o=0;o<t;o++){const t=n.files[o];let i;Object.defineProperty(t,"path",{get:()=>(i=i||kh(t),i)}),o<e&&r.push(t)}}o({get tempFilePaths(){return r.map((({path:e})=>e))},tempFiles:r})})),lb.click(),tg()||console.warn(i("uni.chooseFile.notUserActivation"))}),0,Af),ub={esc:["Esc","Escape"],enter:["Enter"]},db=Object.keys(ub);function pb(){const e=ln(""),t=ln(!1),n=n=>{if(t.value)return;const o=db.find((e=>-1!==ub[e].indexOf(n.key)));o&&(e.value=o),Mn((()=>e.value=""))};return Ro((()=>{document.addEventListener("keyup",n)})),qo((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}const fb=ti("div",{class:"uni-mask"},null,-1);function hb(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),Ma(go({setup:()=>()=>(Fr(),Xr(e,t,null,16))}))}function gb(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function mb(e,{onEsc:t,onEnter:n}){const o=ln(e.visible),{key:r,disable:i}=pb();return eo((()=>e.visible),(e=>o.value=e)),eo((()=>o.value),(e=>i.value=!e)),Kn((()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}let vb=0,yb="";function bb(e){let t=vb;vb+=e?1:-1,vb=Math.max(0,vb),vb>0?0===t&&(yb=document.body.style.overflow,document.body.style.overflow="hidden"):(document.body.style.overflow=yb,yb="")}function _b(){Ro((()=>bb(!0))),jo((()=>bb(!1)))}const wb=gu({name:"ImageView",props:{src:{type:String,default:""}},setup(e){const t=Yt({direction:"none"});let n=1,o=0,r=0,i=0,a=0;function s({detail:e}){n=e.scale}function l(e){const t=e.target.getBoundingClientRect();o=t.width,r=t.height}function c(e){const t=e.target.getBoundingClientRect();i=t.width,a=t.height,d(e)}function u(e){const s=n*o>i,l=n*r>a;t.direction=s&&l?"all":s?"horizontal":l?"vertical":"none",d(e)}function d(e){"all"!==t.direction&&"horizontal"!==t.direction||e.stopPropagation()}return()=>{const n={position:"absolute",left:"0",top:"0",width:"100%",height:"100%"};return ti(vg,{style:n,onTouchstart:mu(c),onTouchmove:mu(d),onTouchend:mu(u)},{default:()=>[ti(Pg,{style:n,direction:t.direction,inertia:!0,scale:!0,"scale-min":"1","scale-max":"4",onScale:s},{default:()=>[ti("img",{src:e.src,style:{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)",maxHeight:"100%",maxWidth:"100%"},onLoad:l},null,40,["src","onLoad"])]},8,["style","direction","inertia","scale","onScale"])]},8,["style","onTouchstart","onTouchmove","onTouchend"])}}});function xb(e){let t="number"==typeof e.current?e.current:e.urls.indexOf(e.current);return t=t<0?0:t,t}const Tb=gu({name:"ImagePreview",props:{urls:{type:Array,default:()=>[]},current:{type:[Number,String],default:0}},emits:["close"],setup(e,{emit:t}){_b();const n=ln(null),o=ln(xb(e));let r;function i(){r||Mn((()=>{t("close")}))}function a(e){o.value=e.detail.current}eo((()=>e.current),(()=>o.value=xb(e))),Ro((()=>{const e=n.value;let t=0,o=0;e.addEventListener("mousedown",(e=>{r=!1,t=e.clientX,o=e.clientY})),e.addEventListener("mouseup",(e=>{(Math.abs(e.clientX-t)>20||Math.abs(e.clientY-o)>20)&&(r=!0)}))}));const s={position:"absolute","box-sizing":"border-box",top:"0",right:"0",width:"60px",height:"44px",padding:"6px","line-height":"32px","font-size":"26px",color:"white","text-align":"center",cursor:"pointer"};return()=>{let t;return ti("div",{ref:n,style:{display:"block",position:"fixed",left:"0",top:"0",width:"100%",height:"100%",zIndex:999,background:"rgba(0,0,0,0.8)"},onClick:i},[ti(im,{navigation:"auto",current:o.value,onChange:a,"indicator-dots":!1,autoplay:!1,style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%"}},(r=t=e.urls.map((e=>ti(sm,null,{default:()=>[ti(wb,{src:e},null,8,["src"])]}))),"function"==typeof r||"[object Object]"===Object.prototype.toString.call(r)&&!Gr(r)?t:{default:()=>[t],_:1}),8,["current","onChange"]),ti("div",{style:s},[Ec(kc,"#ffffff",26)],4)],8,["onClick"]);var r}}});let Sb,kb=null;const Cb=()=>{kb=null,Mn((()=>{null==Sb||Sb.unmount(),Sb=null}))},Eb=Pd("previewImage",((e,{resolve:t})=>{kb?c(kb,e):(kb=Yt(e),Mn((()=>{Sb=hb(Tb,kb,Cb),Sb.mount(gb("u-a-p"))}))),t()}),0,Lf),Ab=Pd("closePreviewImage",((e,{resolve:t,reject:n})=>{Sb?(Cb(),t()):n()}));let Mb=null;const Pb=Pd("chooseVideo",(({sourceType:e,extension:t},{resolve:n,reject:o})=>{Ol();const{t:r}=kl();Mb&&(document.body.removeChild(Mb),Mb=null),Mb=ib({sourceType:e,extension:t,type:"video"}),document.body.appendChild(Mb),Mb.addEventListener("change",(function(e){const t=e.target.files[0];let o="";const r={tempFilePath:o,tempFile:t,size:t.size,duration:0,width:0,height:0,name:t.name};Object.defineProperty(r,"tempFilePath",{get(){return o=o||kh(this.tempFile),o}});const i=document.createElement("video");if(void 0!==i.onloadedmetadata){const e=kh(t);i.onloadedmetadata=function(){Ch(e),n(c(r,{duration:i.duration||0,width:i.videoWidth||0,height:i.videoHeight||0}))},setTimeout((()=>{i.onloadedmetadata=null,Ch(e),n(r)}),300),i.src=e}else n(r)})),Mb.click(),tg()||console.warn(r("uni.chooseFile.notUserActivation"))}),0,Mf),Ib=Ad("request",(({url:e,data:t,header:n={},method:o,dataType:r,responseType:i,withCredentials:a,timeout:s=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const d=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(y(t)||t instanceof ArrayBuffer)u=t;else if("json"===d)try{u=JSON.stringify(t)}catch(m){u=t.toString()}else if("urlencoded"===d){const e=[];for(const n in t)p(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));u=e.join("&")}else u=t.toString();const f=new XMLHttpRequest,h=new Ob(f);f.open(o,e);for(const v in n)p(n,v)&&f.setRequestHeader(v,n[v]);const g=setTimeout((function(){f.onload=f.onabort=f.onerror=null,h.abort(),c("timeout",{errCode:5})}),s);return f.responseType=i,f.onload=function(){clearTimeout(g);const e=f.status;let t="text"===i?f.responseText:f.response;if("text"===i&&"json"===r)try{t=JSON.parse(t)}catch(m){}l({data:t,statusCode:e,header:Lb(f.getAllResponseHeaders()),cookies:[]})},f.onabort=function(){clearTimeout(g),c("abort",{errCode:600003})},f.onerror=function(){clearTimeout(g),c(void 0,{errCode:5})},f.withCredentials=a,f.send(u),h}),0,Nf);class Ob{constructor(e){this._xhr=e}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}function Lb(e){const t={};return e.split("\n").forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}class $b{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){v(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const Db=Ad("downloadFile",(({url:e,header:t={},timeout:n=__uniConfig.networkTimeout.downloadFile},{resolve:o,reject:r})=>{var i,a=new XMLHttpRequest,s=new $b(a);return a.open("GET",e,!0),Object.keys(t).forEach((e=>{a.setRequestHeader(e,t[e])})),a.responseType="blob",a.onload=function(){clearTimeout(i);const t=a.status,n=this.response;let r;const s=a.getResponseHeader("content-disposition");if(s){const e=s.match(/filename="?(\S+)"?\b/);e&&(r=e[1])}n.name=r||function(e){const t=(e=e.split("#")[0].split("?")[0]).split("/");return t[t.length-1]}(e),o({statusCode:t,tempFilePath:kh(n)})},a.onabort=function(){clearTimeout(i),r("abort",{errCode:600003})},a.onerror=function(){clearTimeout(i),r("",{errCode:602001})},a.onprogress=function(e){s._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesWritten:n,totalBytesExpectedToWrite:o})}))},a.send(),i=setTimeout((function(){a.onprogress=a.onload=a.onabort=a.onerror=null,s.abort(),r("timeout",{errCode:5})}),n),s}),0,qf);class Rb{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){v(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const Bb=Ad("uploadFile",(({url:e,file:t,filePath:n,name:o,files:r,header:i={},formData:a={},timeout:s=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new Rb;return f(r)&&r.length||(r=[{name:o,file:t,uri:n}]),Promise.all(r.map((({file:e,uri:t})=>e instanceof Blob?Promise.resolve(Sh(e)):Th(t)))).then((function(t){var n,o=new XMLHttpRequest,d=new FormData;Object.keys(a).forEach((e=>{d.append(e,a[e])})),Object.values(r).forEach((({name:e},n)=>{const o=t[n];d.append(e||"file",o,o.name||`file-${Date.now()}`)})),o.open("POST",e),Object.keys(i).forEach((e=>{o.setRequestHeader(e,i[e])})),o.upload.onprogress=function(e){u._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesSent:n,totalBytesExpectedToSend:o})}))},o.onerror=function(){clearTimeout(n),c("",{errCode:602001})},o.onabort=function(){clearTimeout(n),c("abort",{errCode:600003})},o.onload=function(){clearTimeout(n);const e=o.status;l({statusCode:e,data:o.responseText||o.response})},u._isAbort?c("abort",{errCode:600003}):(n=setTimeout((function(){o.upload.onprogress=o.onload=o.onabort=o.onerror=null,u.abort(),c("timeout",{errCode:5})}),s),o.send(d),u._xhr=o)})).catch((()=>{setTimeout((()=>{c("file error")}),0)})),u}),0,jf),Nb=[],qb={open:"",close:"",error:"",message:""};class jb{constructor(e,t,n){let o;this._callbacks={open:[],close:[],error:[],message:[]};try{const n=this._webSocket=new WebSocket(e,t);n.binaryType="arraybuffer";["open","close","error","message"].forEach((e=>{this._callbacks[e]=[],n.addEventListener(e,(t=>{const{data:n,code:o,reason:r}=t,i="message"===e?{data:n}:"close"===e?{code:o,reason:r}:{};if(this._callbacks[e].forEach((t=>{try{t(i)}catch(EC){console.error(`thirdScriptError\n${EC};at socketTask.on${O(e)} callback function\n`,EC)}})),this===Nb[0]&&qb[e]&&Dx.invokeOnCallback(qb[e],i),"error"===e||"close"===e){const e=Nb.indexOf(this);e>=0&&Nb.splice(e,1)}}))}));["CLOSED","CLOSING","CONNECTING","OPEN","readyState"].forEach((e=>{Object.defineProperty(this,e,{get:()=>n[e]})}))}catch(EC){o=EC}n&&n(o,this)}send(e){const t=(e||{}).data,n=this._webSocket;try{if(n.readyState!==n.OPEN)throw me(e,{errMsg:"sendSocketMessage:fail SocketTask.readyState is not OPEN",errCode:10002}),new Error("SocketTask.readyState is not OPEN");n.send(t),me(e,"sendSocketMessage:ok")}catch(o){me(e,{errMsg:`sendSocketMessage:fail ${o}`,errCode:602001})}}close(e={}){const t=this._webSocket;try{const n=e.code||1e3,o=e.reason;y(o)?t.close(n,o):t.close(n),me(e,"closeSocket:ok")}catch(n){me(e,`closeSocket:fail ${n}`)}}onOpen(e){this._callbacks.open.push(e)}onMessage(e){this._callbacks.message.push(e)}onError(e){this._callbacks.error.push(e)}onClose(e){this._callbacks.close.push(e)}}const zb=Ad("connectSocket",(({url:e,protocols:t},{resolve:n,reject:o})=>new jb(e,t,((e,t)=>{e?o(e.toString(),{errCode:600009}):(Nb.push(t),n())}))),0,zf);function Vb(e,t,n,o,r){const i=e[t];v(i)&&i.call(e,c({},n,{success(){o()},fail({errMsg:e}){r(e.replace("sendSocketMessage:fail ",""))},complete:void 0}))}const Fb=Pd("sendSocketMessage",((e,{resolve:t,reject:n})=>{const o=Nb[0];o&&o.readyState===o.OPEN?Vb(o,"send",e,t,n):n("WebSocket is not connected")})),Ub=Pd("closeSocket",((e,{resolve:t,reject:n})=>{const o=Nb[0];o?Vb(o,"close",e,t,n):n("WebSocket is not connected")}));function Wb(e){const t=`onSocket${O(e)}`;return Cd(t,(()=>{qb[e]=t}))}const Hb=Wb("open"),Yb=Wb("error"),Xb=Wb("message"),Gb=Wb("close"),Jb=Pd("getLocation",(({type:e,altitude:t,highAccuracyExpireTime:n,isHighAccuracy:o},{resolve:r,reject:i})=>{const a=Vv();new Promise(((e,r)=>{navigator.geolocation?navigator.geolocation.getCurrentPosition((t=>e({coords:t.coords})),r,{enableHighAccuracy:o||t,timeout:n||1e5}):r(new Error("device nonsupport geolocation"))})).catch((e=>new Promise(((t,n)=>{a.type===zv.QQ?Lv(`https://apis.map.qq.com/ws/location/v1/ip?output=jsonp&key=${a.key}`,{callback:"callback"},(e=>{if("result"in e&&e.result.location){const n=e.result.location;t({coords:{latitude:n.lat,longitude:n.lng},skip:!0})}else n(new Error(e.message||JSON.stringify(e)))}),(()=>n(new Error("network error")))):a.type===zv.GOOGLE?Ib({method:"POST",url:`https://www.googleapis.com/geolocation/v1/geolocate?key=${a.key}`,success(e){const o=e.data;"location"in o?t({coords:{latitude:o.location.lat,longitude:o.location.lng,accuracy:o.accuracy},skip:!0}):n(new Error(o.error&&o.error.message||JSON.stringify(e)))},fail(){n(new Error("network error"))}}):a.type===zv.AMAP?Rv([],(()=>{window.AMap.plugin("AMap.Geolocation",(()=>{new window.AMap.Geolocation({enableHighAccuracy:!0,timeout:1e4}).getCurrentPosition(((e,o)=>{"complete"===e?t({coords:{latitude:o.position.lat,longitude:o.position.lng,accuracy:o.accuracy},skip:!0}):n(new Error(o.message))}))}))})):n(e)})))).then((({coords:t,skip:n})=>{Yv(e,t,n).then((e=>{r({latitude:e.latitude,longitude:e.longitude,accuracy:e.accuracy,speed:e.altitude||0,altitude:e.altitude||0,verticalAccuracy:e.altitudeAccuracy||0,horizontalAccuracy:e.accuracy||0})})).catch((e=>{i(e.message)}))})).catch((e=>{i(e.message||JSON.stringify(e))}))}),0,kf);const Qb=gu({name:"LocationView",props:{latitude:{type:Number},longitude:{type:Number},scale:{type:Number,default:18},name:{type:String,default:""},address:{type:String,default:""}},emits:["close"],setup(e,{emit:t}){const n=function(e){const t=Yt({center:{latitude:0,longitude:0},marker:{id:1,latitude:0,longitude:0,iconPath:jv,width:32,height:52},location:{id:2,latitude:0,longitude:0,iconPath:qv,width:44,height:44}});function n(){e.latitude&&e.longitude&&(t.center.latitude=e.latitude,t.center.longitude=e.longitude,t.marker.latitude=e.latitude,t.marker.longitude=e.longitude)}return eo([()=>e.latitude,()=>e.longitude],n),n(),t}(e);function o(e){const t=e.detail.centerLocation;t&&(n.center.latitude=t.latitude,n.center.longitude=t.longitude)}function r(){const t=Vv();let o="";if(t.type===zv.GOOGLE){o=`https://www.google.com/maps/dir/?api=1${n.location.latitude?`&origin=${n.location.latitude}%2C${n.location.longitude}`:""}&destination=${e.latitude}%2C${e.longitude}`}else if(t.type===zv.QQ){o=`https://apis.map.qq.com/uri/v1/routeplan?type=drive${n.location.latitude?`&fromcoord=${n.location.latitude}%2C${n.location.longitude}&from=${encodeURIComponent("我的位置")}`:""}&tocoord=${e.latitude}%2C${e.longitude}&to=${encodeURIComponent(e.name||"目的地")}&ref=${t.key}`}else if(t.type===zv.AMAP){o=`https://uri.amap.com/navigation?${n.location.latitude?`from=${n.location.longitude},${n.location.latitude},${encodeURIComponent("我的位置")}&`:""}to=${e.longitude},${e.latitude},${encodeURIComponent(e.name||"目的地")}`}window.open(o)}function i(){t("close")}function a({latitude:e,longitude:t}){n.center.latitude=e,n.center.longitude=t}return _b(),Jb({type:"gcj02",success:({latitude:e,longitude:t})=>{n.location.latitude=e,n.location.longitude=t}}),()=>ti("div",{class:"uni-system-open-location"},[ti(Tx,{latitude:n.center.latitude,longitude:n.center.longitude,class:"map",markers:[n.marker,n.location],onRegionchange:o},{default:()=>[ti("div",{class:"map-move",onClick:()=>a(n.location)},[Ec(Nv,"#000000",24)],8,["onClick"])]},8,["latitude","longitude","markers","onRegionchange"]),ti("div",{class:"info"},[ti("div",{class:"name",onClick:()=>a(n.marker)},[e.name],8,["onClick"]),ti("div",{class:"address",onClick:()=>a(n.marker)},[e.address],8,["onClick"]),ti("div",{class:"nav",onClick:r},[Ec("M28 17c-6.49396875 0-12.13721875 2.57040625-15 6.34840625V5.4105l6.29859375 6.29859375c0.387875 0.387875 1.02259375 0.387875 1.4105 0 0.387875-0.387875 0.387875-1.02259375 0-1.4105L12.77853125 2.36803125a0.9978125 0.9978125 0 0 0-0.0694375-0.077125c-0.1944375-0.1944375-0.45090625-0.291375-0.70721875-0.290875l-0.00184375-0.0000625-0.00184375 0.0000625c-0.2563125-0.0005-0.51278125 0.09640625-0.70721875 0.290875a0.9978125 0.9978125 0 0 0-0.0694375 0.077125l-7.930625 7.9305625c-0.387875 0.387875-0.387875 1.02259375 0 1.4105 0.387875 0.387875 1.02259375 0.387875 1.4105 0L11 5.4105V29c0 0.55 0.45 1 1 1s1-0.45 1-1c0-5.52284375 6.71571875-10 15-10 0.55228125 0 1-0.44771875 1-1 0-0.55228125-0.44771875-1-1-1z","#ffffff",26)],8,["onClick"])]),ti("div",{class:"nav-btn-back",onClick:i},[Ec(Sc,"#ffffff",26)],8,["onClick"])])}});let Kb=null;const Zb=Pd("openLocation",((e,{resolve:t})=>{Kb?c(Kb,e):(Kb=Yt(e),Mn((()=>{const e=hb(Qb,Kb,(()=>{Kb=null,Mn((()=>{e.unmount()}))}));e.mount(gb("u-a-o"))}))),t()}),0,Ef);const e_=gu({name:"LoctaionPicker",props:{latitude:{type:Number},longitude:{type:Number}},emits:["close"],setup(e,{emit:t}){_b(),Bl();const{t:n}=kl(),o=function(e){const t=Yt({latitude:0,longitude:0,keyword:"",searching:!1});function n(){e.latitude&&e.longitude&&(t.latitude=e.latitude,t.longitude=e.longitude)}return eo([()=>e.latitude,()=>e.longitude],n),n(),t}(e),{list:r,listState:i,loadMore:a,reset:s,getList:l}=function(e){const t=__uniConfig.qqMapKey,n=Yt([]),o=ln(-1),r=xi((()=>n[o.value])),i=Yt({loading:!0,pageSize:20,pageIndex:1,hasNextPage:!0,nextPage:null,selectedIndex:o,selected:r}),a=ln(""),s=xi((()=>a.value?`region(${a.value},1,${e.latitude},${e.longitude})`:`nearby(${e.latitude},${e.longitude},5000)`));function l(e){e.forEach((e=>{n.push({name:e.title||e.name,address:e.address,distance:e._distance||e.distance,latitude:e.location.lat,longitude:e.location.lng})}))}function c(){i.loading=!0;const o=Vv();if(o.type===zv.GOOGLE){if(i.pageIndex>1&&i.nextPage)return void i.nextPage();new google.maps.places.PlacesService(document.createElement("div"))[e.searching?"textSearch":"nearbySearch"]({location:{lat:e.latitude,lng:e.longitude},query:e.keyword,radius:5e3},((e,t,o)=>{i.loading=!1,e&&e.length&&e.forEach((e=>{n.push({name:e.name||"",address:e.vicinity||e.formatted_address||"",distance:0,latitude:e.geometry.location.lat(),longitude:e.geometry.location.lng()})})),o&&(o.hasNextPage?i.nextPage=()=>{o.nextPage()}:i.hasNextPage=!1)}))}else o.type===zv.QQ?Lv(e.searching?`https://apis.map.qq.com/ws/place/v1/search?output=jsonp&key=${t}&boundary=${s.value}&keyword=${e.keyword}&page_size=${i.pageSize}&page_index=${i.pageIndex}`:`https://apis.map.qq.com/ws/geocoder/v1/?output=jsonp&key=${t}&location=${e.latitude},${e.longitude}&get_poi=1&poi_options=page_size=${i.pageSize};page_index=${i.pageIndex}`,{callback:"callback"},(t=>{if(i.loading=!1,e.searching&&"data"in t&&t.data.length)l(t.data);else if("result"in t){const e=t.result;a.value=e.ad_info?e.ad_info.adcode:"",e.pois&&l(e.pois)}n.length===i.pageSize*i.pageIndex&&(i.hasNextPage=!1)}),(()=>{i.loading=!1})):o.type===zv.AMAP&&window.AMap.plugin("AMap.PlaceSearch",(function(){const t=new window.AMap.PlaceSearch({city:"全国",pageSize:10,pageIndex:i.pageIndex}),n=e.searching?e.keyword:"",o=e.searching?5e4:5e3;t.searchNearBy(n,[e.longitude,e.latitude],o,(function(e,t){"error"===e?console.error(t):"no_data"===e?i.hasNextPage=!1:l(t.poiList.pois)})),i.loading=!1}))}return{listState:i,list:n,loadMore:function(){!i.loading&&i.hasNextPage&&(i.pageIndex++,c())},reset:function(){i.selectedIndex=-1,i.pageIndex=1,i.hasNextPage=!0,i.nextPage=null,n.splice(0,n.length)},getList:c}}(o),u=Ee((()=>{s(),o.keyword&&l()}),1e3,{setTimeout:setTimeout,clearTimeout:clearTimeout});function d(e){o.keyword=e.detail.value,u()}function p(){t("close",c({},i.selected))}function f(){t("close")}function h(e){const t=e.detail.centerLocation;t&&m(t)}function g(){Jb({type:"gcj02",success:m,fail:()=>{}})}function m({latitude:e,longitude:t}){o.latitude=e,o.longitude=t,o.searching||(s(),l())}return eo((()=>o.searching),(e=>{s(),e||l()})),o.latitude&&o.longitude||g(),()=>{const e=r.map(((e,t)=>{return ti("div",{key:t,class:{"list-item":!0,selected:i.selectedIndex===t},onClick:()=>{i.selectedIndex=t,o.latitude=e.latitude,o.longitude=e.longitude}},[Ec(Cc,"#007aff",24),ti("div",{class:"list-item-title"},[e.name]),ti("div",{class:"list-item-detail"},[(n=e.distance,n>100?`${n>1e3?(n/1e3).toFixed(1)+"k":n.toFixed(0)}m | `:n>0?"<100m | ":""),e.address])],10,["onClick"]);var n}));return i.loading&&e.unshift(ti("div",{class:"list-loading"},[ti("i",{class:"uni-loading"},null)])),ti("div",{class:"uni-system-choose-location"},[ti(Tx,{latitude:o.latitude,longitude:o.longitude,class:"map","show-location":!0,libraries:["places"],onUpdated:l,onRegionchange:h},{default:()=>[ti("div",{class:"map-location",style:`background-image: url("${jv}")`},null),ti("div",{class:"map-move",onClick:g},[Ec(Nv,"#000000",24)],8,["onClick"])],_:1},8,["latitude","longitude","show-location","onUpdated","onRegionchange"]),ti("div",{class:"nav"},[ti("div",{class:"nav-btn back",onClick:f},[Ec(kc,"#ffffff",26)],8,["onClick"]),ti("div",{class:{"nav-btn":!0,confirm:!0,disable:!i.selected},onClick:p},[Ec(Cc,"#ffffff",26)],10,["onClick"])]),ti("div",{class:"menu"},[ti("div",{class:"search"},[ti(pg,{value:o.keyword,class:"search-input",placeholder:n("uni.chooseLocation.search"),onFocus:()=>o.searching=!0,onInput:d},null,8,["value","placeholder","onFocus","onInput"]),o.searching&&ti("div",{class:"search-btn",onClick:()=>{o.searching=!1,o.keyword=""}},[n("uni.chooseLocation.cancel")],8,["onClick"])]),ti(om,{"scroll-y":!0,class:"list",onScrolltolower:a},(t=e,"function"==typeof t||"[object Object]"===Object.prototype.toString.call(t)&&!Gr(t)?e:{default:()=>[e],_:2}),8,["scroll-y","onScrolltolower"])])]);var t}}});let t_=null;const n_=Pd("chooseLocation",((e,{resolve:t,reject:n})=>{t_?n("cancel"):(t_=Yt(e),Mn((()=>{const e=hb(e_,t_,(o=>{t_=null,Mn((()=>{e.unmount()})),o?t(o):n("cancel")}));e.mount(gb("u-a-c"))})))}));let o_=!1,r_=0;const i_=Pd("startLocationUpdate",((e,{resolve:t,reject:n})=>{navigator.geolocation?(r_=r_||navigator.geolocation.watchPosition((n=>{o_=!0,Yv(null==e?void 0:e.type,n.coords).then((e=>{Dx.invokeOnCallback("onLocationChange",e),t()})).catch((e=>{Dx.invokeOnCallback("onLocationChangeError",{errMsg:`onLocationChange:fail ${e.message}`})}))}),(e=>{o_||(n(e.message),o_=!0),Dx.invokeOnCallback("onLocationChangeError",{errMsg:`onLocationChange:fail ${e.message}`})})),setTimeout(t,100)):n()}),0,Ff),a_=Pd("stopLocationUpdate",((e,{resolve:t})=>{r_&&(navigator.geolocation.clearWatch(r_),o_=!1,r_=0),t()})),s_=Cd("onLocationChange",(()=>{})),l_=Ed("offLocationChange",(()=>{})),c_=Cd("onLocationChangeError",(()=>{})),u_=Ed("offLocationChangeError",(()=>{})),d_=Pd("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===Rc("onBackPress",{from:e.from||"navigateBack"})&&(o=!1),o?(bv().$router.go(-e.delta),t()):n("onBackPress")}),0,Gf);function p_({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:r},i){const a=bv().$router,{path:s,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:Ce(n||"")}}(t);return new Promise(((t,c)=>{const u=function(e,t){return{__id__:t||++Qm,__type__:e}}(e,i);a["navigateTo"===e?"push":"replace"]({path:s,query:l,state:u,force:!0}).then((i=>{if(bs(i))return c(i.message);if("switchTab"===e&&(a.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=a.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new Ae(u.__id__,o),t(r?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return r?t({__id__:u.__id__}):t()}))}))}const f_=Pd("navigateTo",(({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:r})=>p_({type:"navigateTo",url:e,events:t,isAutomatedTesting:n}).then(o).catch(r)),0,Wf);const h_=Pd("redirectTo",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>(function(){const e=Pc();if(!e)return;const t=e.$page;Jm(ev(t.path,t.id))}(),p_({type:"redirectTo",url:e,isAutomatedTesting:t}).then(n).catch(o))),0,Hf);const g_=Pd("reLaunch",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>(function(){const e=Xm().keys();for(const t of e)Jm(t)}(),p_({type:"reLaunch",url:e,isAutomatedTesting:t}).then(n).catch(o))),0,Yf);function m_(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}const v_=Pd("switchTab",(({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:o,reject:r})=>(function(){const e=Lc();if(!e)return;const t=Xm(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:Jm(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,Rc(e,"onHide"))}(),p_({type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},function(e){const t=Xm().values();for(const n of t){const t=n.$page;if(m_(e,t))return n.$.__isActive=!0,t.id}}(e)).then(o).catch(r))),0,Xf),y_=Pd("preloadPage",(({url:e},{resolve:t,reject:n})=>{const o=Vc(e.split("?")[0]);o?o.loader&&o.loader().then((()=>{t({url:e,errMsg:"preloadPage:ok"})})).catch((t=>{n(`${e} ${String(t)}`)})):n(`${e}}`)}));function b_(e){__uniConfig.darkmode&&Dx.on("onThemeChange",e)}function __(e){Dx.off("onThemeChange",e)}function w_(e){let t={};return __uniConfig.darkmode&&(t=Ne(e,__uniConfig.themeConfig,ly())),__uniConfig.darkmode?t:e}function x_(e,t){const n=Jt(e),o=n?Yt(w_(e)):w_(e);return __uniConfig.darkmode&&n&&eo(e,(e=>{const t=w_(e);for(const n in t)o[n]=t[n]})),t&&b_(t),o}const T_={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},S_=go({props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},setup(e,{emit:t}){const n=ln(""),o=()=>a.value=!1,r=()=>(o(),t("close","cancel")),i=()=>(o(),t("close","confirm",n.value)),a=mb(e,{onEsc:r,onEnter:()=>{!e.editable&&i()}}),s=function(e){const t=ln(e.cancelColor),n=({theme:e})=>{((e,t)=>{t.value=T_[e].cancelColor})(e,t)};return Kn((()=>{e.visible?(t.value=e.cancelColor,"#000"===e.cancelColor&&("dark"===ly()&&n({theme:"dark"}),b_(n))):__(n)})),t}(e);return()=>{const{title:t,content:o,showCancel:l,confirmText:c,confirmColor:u,editable:d,placeholderText:p}=e;return n.value=o,ti(Qi,{name:"uni-fade"},{default:()=>[Wo(ti("uni-modal",{onTouchmove:pc},[fb,ti("div",{class:"uni-modal"},[t&&ti("div",{class:"uni-modal__hd"},[ti("strong",{class:"uni-modal__title",textContent:t},null,8,["textContent"])]),d?ti("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:p,value:o,onInput:e=>n.value=e.target.value},null,40,["placeholder","value","onInput"]):ti("div",{class:"uni-modal__bd",onTouchmovePassive:fc,textContent:o},null,40,["onTouchmovePassive","textContent"]),ti("div",{class:"uni-modal__ft"},[l&&ti("div",{style:{color:s.value},class:"uni-modal__btn uni-modal__btn_default",onClick:r},[e.cancelText],12,["onClick"]),ti("div",{style:{color:u},class:"uni-modal__btn uni-modal__btn_primary",onClick:i},[c],12,["onClick"])])])],40,["onTouchmove"]),[[ka,a.value]])]})}}});let k_;const C_=fe((()=>{Dx.on("onHidePopup",(()=>k_.visible=!1))}));let E_;function A_(e,t){const n="confirm"===e,o={confirm:n,cancel:"cancel"===e};n&&k_.editable&&(o.content=t),E_&&E_(o)}const M_=Pd("showModal",((e,{resolve:t})=>{C_(),E_=t,k_?(c(k_,e),k_.visible=!0):(k_=Yt(e),Mn((()=>(hb(S_,k_,A_).mount(gb("u-a-m")),Mn((()=>k_.visible=!0))))))}),0,ih),P_={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==ah.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},I_={light:"#fff",dark:"rgba(255,255,255,0.9)"},O_=e=>I_[e],L_=go({name:"Toast",props:P_,setup(e){Ml(),Pl();const{Icon:t}=function(e){const t=ln(O_(ly())),n=({theme:e})=>t.value=O_(e);Kn((()=>{e.visible?b_(n):__(n)}));return{Icon:xi((()=>{switch(e.icon){case"success":return ti(Ec(xc,t.value,38),{class:"uni-toast__icon"});case"error":return ti(Ec(Tc,t.value,38),{class:"uni-toast__icon"});case"loading":return ti("i",{class:["uni-toast__icon","uni-loading"]},null,2);default:return null}}))}}(e),n=mb(e,{});return()=>{const{mask:o,duration:r,title:i,image:a}=e;return ti(Qi,{name:"uni-fade"},{default:()=>[Wo(ti("uni-toast",{"data-duration":r},[o?ti("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:pc},null,40,["onTouchmove"]):"",a||t.value?ti("div",{class:"uni-toast"},[a?ti("img",{src:a,class:"uni-toast__icon"},null,10,["src"]):t.value,ti("p",{class:"uni-toast__content"},[i])]):ti("div",{class:"uni-sample-toast"},[ti("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[ka,n.value]])]})}}});let $_,D_,R_="";const B_=ze();function N_(e){$_?c($_,e):($_=Yt(c(e,{visible:!1})),Mn((()=>{B_.run((()=>{eo([()=>$_.visible,()=>$_.duration],(([e,t])=>{if(e){if(D_&&clearTimeout(D_),"onShowLoading"===R_)return;D_=setTimeout((()=>{U_("onHideToast")}),t)}else D_&&clearTimeout(D_)}))})),Dx.on("onHidePopup",(()=>U_("onHidePopup"))),hb(L_,$_,(()=>{})).mount(gb("u-a-t"))}))),setTimeout((()=>{$_.visible=!0}),10)}const q_=Pd("showToast",((e,{resolve:t,reject:n})=>{N_(e),R_="onShowToast",t()}),0,sh),j_={icon:"loading",duration:1e8,image:""},z_=Pd("showLoading",((e,{resolve:t,reject:n})=>{c(e,j_),N_(e),R_="onShowLoading",t()}),0,rh),V_=Pd("hideToast",((e,{resolve:t,reject:n})=>{U_("onHideToast"),t()})),F_=Pd("hideLoading",((e,{resolve:t,reject:n})=>{U_("onHideLoading"),t()}));function U_(e){const{t:t}=kl();if(!R_)return;let n="";if("onHideToast"===e&&"onShowToast"!==R_?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==R_&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);R_="",setTimeout((()=>{$_.visible=!1}),10)}function W_(e){const t=ln(0),n=ln(0),o=xi((()=>t.value>=500&&n.value>=500)),r=xi((()=>{const t={content:{transform:"",left:"",top:"",bottom:""},triangle:{left:"",top:"",bottom:"","border-width":"","border-color":""}},r=t.content,i=t.triangle,a=e.popover;function s(e){return Number(e)||0}if(o.value&&a){c(i,{position:"absolute",width:"0",height:"0","margin-left":"-6px","border-style":"solid"});const e=s(a.left),t=s(a.width),o=s(a.top),l=s(a.height),u=e+t/2;r.transform="none !important";const d=Math.max(0,u-150);r.left=`${d}px`;let p=Math.max(12,u-d);p=Math.min(288,p),i.left=`${p}px`;const f=n.value/2;o+l-f>f-o?(r.top="auto",r.bottom=n.value-o+6+"px",i.bottom="-6px",i["border-width"]="6px 6px 0 6px",i["border-color"]="#fcfcfd transparent transparent transparent"):(r.top=`${o+l+6}px`,i.top="-6px",i["border-width"]="0 6px 6px 6px",i["border-color"]="transparent transparent #fcfcfd transparent")}return t}));return Ro((()=>{const e=()=>{const{windowWidth:e,windowHeight:o,windowTop:r}=uni.getSystemInfoSync();t.value=e,n.value=o+(r||0)};window.addEventListener("resize",e),e(),jo((()=>{window.removeEventListener("resize",e)}))})),{isDesktop:o,popupStyle:r}}const H_={light:{listItemColor:"#000000",cancelItemColor:"#000000"},dark:{listItemColor:"rgba(255, 255, 255, 0.8)",cancelItemColor:"rgba(255, 255, 255)"}};const Y_=go({name:"ActionSheet",props:{title:{type:String,default:""},itemList:{type:Array,default:()=>[]},itemColor:{type:String,default:"#000000"},popover:{type:Object,default:null},visible:{type:Boolean,default:!1}},emits:["close"],setup(e,{emit:t}){Al();const n=ln(260),o=ln(0),r=ln(0),i=ln(0),a=ln(0),s=ln(null),l=ln(null),{t:c}=kl(),{_close:u}=function(e,t){function n(e){t("close",e)}const{key:o,disable:r}=pb();return eo((()=>e.visible),(e=>r.value=!e)),Kn((()=>{const{value:e}=o;"esc"===e&&n&&n(-1)})),{_close:n}}(e,t),{popupStyle:d}=W_(e);let p;function f(e){const t=i.value+e.deltaY;Math.abs(t)>10?(a.value+=t/3,a.value=a.value>=o.value?o.value:a.value<=0?0:a.value,p.scrollTo(a.value)):i.value=t,e.preventDefault()}Ro((()=>{const{scroller:e,handleTouchStart:t,handleTouchMove:n,handleTouchEnd:o}=Xg(s.value,{enableY:!0,friction:new Vg(1e-4),spring:new Wg(2,90,20),onScroll:e=>{a.value=e.target.scrollTop}});p=e,xg(s.value,(r=>{if(e)switch(r.detail.state){case"start":t(r);break;case"move":n(r);break;case"end":case"cancel":o(r)}}),!0)})),eo((()=>e.visible),(()=>{Mn((()=>{e.title&&(r.value=document.querySelector(".uni-actionsheet__title").offsetHeight),p.update(),s.value&&(o.value=s.value.clientHeight-n.value),document.querySelectorAll(".uni-actionsheet__cell").forEach((e=>{!function(e){const t=20;let n=0,o=0;e.addEventListener("touchstart",(e=>{const t=e.changedTouches[0];n=t.clientX,o=t.clientY})),e.addEventListener("touchend",(e=>{const r=e.changedTouches[0];if(Math.abs(r.clientX-n)<t&&Math.abs(r.clientY-o)<t){const t=e.target,n=e.currentTarget,o=new CustomEvent("click",{bubbles:!0,cancelable:!0,target:t,currentTarget:n});["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{o[e]=r[e]})),e.target.dispatchEvent(o)}}))}(e)}))}))}));const h=function(e){const t=Yt({listItemColor:"#000",cancelItemColor:"#000"}),n=({theme:e})=>{!function(e,t){["listItemColor","cancelItemColor"].forEach((n=>{t[n]=H_[e][n]}))}(e,t)};return Kn((()=>{e.visible?(t.listItemColor=t.cancelItemColor=e.itemColor,"#000"===e.itemColor&&(n({theme:ly()}),b_(n))):__(n)})),t}(e);return()=>ti("uni-actionsheet",{onTouchmove:pc},[ti(Qi,{name:"uni-fade"},{default:()=>[Wo(ti("div",{class:"uni-mask uni-actionsheet__mask",onClick:()=>u(-1)},null,8,["onClick"]),[[ka,e.visible]])]}),ti("div",{class:["uni-actionsheet",{"uni-actionsheet_toggle":e.visible}],style:d.value.content},[ti("div",{ref:l,class:"uni-actionsheet__menu",onWheel:f},[e.title?ti(Br,null,[ti("div",{class:"uni-actionsheet__cell",style:{height:`${r.value}px`}},null),ti("div",{class:"uni-actionsheet__title"},[e.title])]):"",ti("div",{style:{maxHeight:`${n.value}px`,overflow:"hidden"}},[ti("div",{ref:s},[e.itemList.map(((e,t)=>ti("div",{key:t,style:{color:h.listItemColor},class:"uni-actionsheet__cell",onClick:()=>u(t)},[e],12,["onClick"])))],512)])],40,["onWheel"]),ti("div",{class:"uni-actionsheet__action"},[ti("div",{style:{color:h.cancelItemColor},class:"uni-actionsheet__cell",onClick:()=>u(-1)},[c("uni.showActionSheet.cancel")],12,["onClick"])]),ti("div",{style:d.value.triangle},null,4)],6)],40,["onTouchmove"])}});let X_,G_,J_;const Q_=fe((()=>{Dx.on("onHidePopup",(()=>J_.visible=!1))}));function K_(e){-1===e?G_&&G_("cancel"):X_&&X_({tapIndex:e})}const Z_=Pd("showActionSheet",((e,{resolve:t,reject:n})=>{Q_(),X_=t,G_=n,J_?(c(J_,e),J_.visible=!0):(J_=Yt(e),Mn((()=>(hb(Y_,J_,K_).mount(gb("u-s-a-s")),Mn((()=>J_.visible=!0))))))}),0,oh),ew=Pd("loadFontFace",(({family:e,source:t,desc:n},{resolve:o,reject:r})=>{(function(e,t,n){const o=document.fonts;if(o){const r=new FontFace(e,t,n);return r.load().then((()=>{o.add&&o.add(r)}))}return new Promise((o=>{const r=document.createElement("style"),i=[];if(n){const{style:e,weight:t,stretch:o,unicodeRange:r,variant:a,featureSettings:s}=n;e&&i.push(`font-style:${e}`),t&&i.push(`font-weight:${t}`),o&&i.push(`font-stretch:${o}`),r&&i.push(`unicode-range:${r}`),a&&i.push(`font-variant:${a}`),s&&i.push(`font-feature-settings:${s}`)}r.innerText=`@font-face{font-family:"${e}";src:${t};${i.join(";")}}`,document.head.appendChild(r),o()}))})(e,t=t.startsWith('url("')||t.startsWith("url('")?`url('${Mu(t.substring(5,t.length-2))}')`:t.startsWith("url(")?`url('${Mu(t.substring(4,t.length-1))}')`:Mu(t),n).then((()=>{o()})).catch((e=>{r(`loadFontFace:fail ${e}`)}))}));function tw(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,Dx.emit("onNavigationBarChange",{titleText:t})}Kn(t),So(t)}function nw(e,t,n,o,r){if(!e)return r("page not found");const{navigationBar:i}=e;switch(t){case"setNavigationBarColor":const{frontColor:e,backgroundColor:t,animation:o}=n,{duration:r,timingFunc:a}=o;e&&(i.titleColor="#000000"===e?"#000000":"#ffffff"),t&&(i.backgroundColor=t),i.duration=r+"ms",i.timingFunc=a;break;case"showNavigationBarLoading":i.loading=!0;break;case"hideNavigationBarLoading":i.loading=!1;break;case"setNavigationBarTitle":const{title:s}=n;i.titleText=s}o()}const ow=Pd("setNavigationBarColor",((e,{resolve:t,reject:n})=>{nw(Ic(),"setNavigationBarColor",e,t,n)}),0,th),rw=Pd("showNavigationBarLoading",((e,{resolve:t,reject:n})=>{nw(Ic(),"showNavigationBarLoading",e||{},t,n)})),iw=Pd("hideNavigationBarLoading",((e,{resolve:t,reject:n})=>{nw(Ic(),"hideNavigationBarLoading",e||{},t,n)})),aw=Pd("setNavigationBarTitle",((e,{resolve:t,reject:n})=>{nw(Ic(),"setNavigationBarTitle",e,t,n)})),sw=Pd("pageScrollTo",(({scrollTop:e,selector:t,duration:n},{resolve:o})=>{!function(e,t,n){if(y(e)){const t=document.querySelector(e);if(t){const{top:n}=t.getBoundingClientRect();e=n+window.pageYOffset;const o=document.querySelector("uni-page-head");o&&(e-=o.offsetHeight)}}e<0&&(e=0);const o=document.documentElement,{clientHeight:r,scrollHeight:i}=o;if(e=Math.min(e,i-r),0===t)return void(o.scrollTop=document.body.scrollTop=e);if(window.scrollY===e)return;const a=t=>{if(t<=0)return void window.scrollTo(0,e);const n=e-window.scrollY;requestAnimationFrame((function(){window.scrollTo(0,window.scrollY+n/t*10),a(t-10)}))};a(t)}(t||e||0,n),o()}),0,nh),lw=Pd("startPullDownRefresh",((e,{resolve:t})=>{Dx.invokeViewMethod("startPullDownRefresh",{},Oc()),t()})),cw=Pd("stopPullDownRefresh",((e,{resolve:t})=>{Dx.invokeViewMethod("stopPullDownRefresh",{},Oc()),t()})),uw=["text","iconPath","iconfont","selectedIconPath","visible"],dw=["color","selectedColor","backgroundColor","borderStyle","midButton"],pw=["badge","redDot"];function fw(e,t,n){t.forEach((function(t){p(n,t)&&(e[t]=n[t])}))}function hw(e,t,n){const o=Rm();switch(e){case"showTabBar":o.shown=!0;break;case"hideTabBar":o.shown=!1;break;case"setTabBarItem":const{index:e}=t,n=o.list[e],r=n.pagePath;fw(n,uw,t);const{pagePath:i}=t;if(i){const t=de(i);t!==r&&function(e,t,n){const o=Vc(de(t));if(o){const{meta:e}=o;delete e.tabBarIndex,e.isQuit=e.isTabBar=!1}const r=Vc(de(n));if(r){const{meta:t}=r;t.tabBarIndex=e,t.isQuit=t.isTabBar=!0;const o=__uniConfig.tabBar;o&&o.list&&o.list[e]&&(o.list[e].pagePath=pe(n))}}(e,r,t)}break;case"setTabBarStyle":fw(o,dw,t);break;case"showTabBarRedDot":fw(o.list[t.index],pw,{badge:"",redDot:!0});break;case"setTabBarBadge":fw(o.list[t.index],pw,{badge:t.text,redDot:!0});break;case"hideTabBarRedDot":case"removeTabBarBadge":fw(o.list[t.index],pw,{badge:"",redDot:!1})}n()}const gw=Pd("setTabBarItem",((e,{resolve:t})=>{hw("setTabBarItem",e,t)}),0,ch),mw=Pd("setTabBarStyle",((e,{resolve:t})=>{hw("setTabBarStyle",e,t)}),0,dh),vw=Pd("hideTabBar",((e,{resolve:t})=>{hw("hideTabBar",e||{},t)})),yw=Pd("showTabBar",((e,{resolve:t})=>{hw("showTabBar",e||{},t)})),bw=Pd("hideTabBarRedDot",((e,{resolve:t})=>{hw("hideTabBarRedDot",e,t)}),0,ph),_w=Pd("showTabBarRedDot",((e,{resolve:t})=>{hw("showTabBarRedDot",e,t)}),0,fh),ww=Pd("removeTabBarBadge",((e,{resolve:t})=>{hw("removeTabBarBadge",e,t)}),0,hh),xw=Pd("setTabBarBadge",((e,{resolve:t})=>{hw("setTabBarBadge",e,t)}),0,gh),Tw=gu({name:"TabBar",setup(){const e=ln([]),t=Rm(),n=x_(t,(()=>{const e=w_(t);n.backgroundColor=e.backgroundColor,n.borderStyle=e.borderStyle,n.color=e.color,n.selectedColor=e.selectedColor,n.blurEffect=e.blurEffect,e.list&&e.list.length&&e.list.forEach(((e,t)=>{n.list[t].iconPath=e.iconPath,n.list[t].selectedIconPath=e.selectedIconPath}))}));!function(e,t){function n(){let n=[];n=e.list.filter((e=>!1!==e.visible)),t.value=n}ln(c({type:"midButton"},e.midButton)),Kn(n)}(n,e),function(e){eo((()=>e.shown),(t=>{yc({"--window-bottom":Hm(t?parseInt(e.height):0)})}))}(n);const o=function(e,t,n){return Kn((()=>{const o=e.meta;if(o.isTabBar){const e=o.route,r=n.value.findIndex((t=>t.pagePath===e));t.selectedIndex=r}})),(t,n)=>()=>{const{pagePath:o,text:r}=t;let i=de(o);i===__uniRoutes[0].alias&&(i="/"),e.path!==i?uni.switchTab({from:"tabBar",url:i,tabBarText:r}):Rc("onTabItemTap",{index:n,text:r,pagePath:o})}}(bl(),n,e),{style:r,borderStyle:i,placeholderStyle:a}=function(e){const t=xi((()=>{let t=e.backgroundColor;const n=e.blurEffect;return t||Vm&&n&&"none"!==n&&(t=Sw[n]),{backgroundColor:t||"#f7f7fa",backdropFilter:"none"!==n?"blur(10px)":n}})),n=xi((()=>{const{borderStyle:t}=e;return{backgroundColor:kw[t]||t}})),o=xi((()=>({height:e.height})));return{style:t,borderStyle:n,placeholderStyle:o}}(n);return Ro((()=>{n.iconfontSrc&&ew({family:"UniTabbarIconFont",source:`url("${n.iconfontSrc}")`})})),()=>{const t=function(e,t,n){const{selectedIndex:o,selectedColor:r,color:i}=e;return n.value.map(((n,a)=>{const s=o===a;return function(e,t,n,o,r,i,a,s){return ti("div",{key:a,class:"uni-tabbar__item",onClick:s(r,a)},[Cw(e,t||"",n,o,r,i)],8,["onClick"])}(s?r:i,s&&n.selectedIconPath||n.iconPath||"",n.iconfont?s&&n.iconfont.selectedText||n.iconfont.text:void 0,n.iconfont?s&&n.iconfont.selectedColor||n.iconfont.color:void 0,n,e,a,t)}))}(n,o,e);return ti("uni-tabbar",{class:"uni-tabbar-"+n.position},[ti("div",{class:"uni-tabbar",style:r.value},[ti("div",{class:"uni-tabbar-border",style:i.value},null,4),t],4),ti("div",{class:"uni-placeholder",style:a.value},null,4)],2)}}});const Sw={dark:"rgb(0, 0, 0, 0.8)",light:"rgb(250, 250, 250, 0.8)",extralight:"rgb(250, 250, 250, 0.8)"},kw={white:"rgba(255, 255, 255, 0.33)",black:"rgba(0, 0, 0, 0.33)"};function Cw(e,t,n,o,r,i){const{height:a}=i;return ti("div",{class:"uni-tabbar__bd",style:{height:a}},[n?Aw(n,o||"rgb(0, 0, 0, 0.8)",r,i):t&&Ew(t,r,i),r.text&&Mw(e,r,i),r.redDot&&Pw(r.badge)],4)}function Ew(e,t,n){const{type:o,text:r}=t,{iconWidth:i}=n;return ti("div",{class:"uni-tabbar__icon"+(r?" uni-tabbar__icon__diff":""),style:{width:i,height:i}},["midButton"!==o&&ti("img",{src:Mu(e)},null,8,["src"])],6)}function Aw(e,t,n,o){var r;const{type:i,text:a}=n,{iconWidth:s}=o,l="uni-tabbar__icon"+(a?" uni-tabbar__icon__diff":""),c={width:s,height:s},u={fontSize:(null==(r=n.iconfont)?void 0:r.fontSize)||s,color:t};return ti("div",{class:l,style:c},["midButton"!==i&&ti("div",{class:"uni-tabbar__iconfont",style:u},[e],4)],6)}function Mw(e,t,n){const{iconPath:o,text:r}=t,{fontSize:i,spacing:a}=n;return ti("div",{class:"uni-tabbar__label",style:{color:e,fontSize:i,lineHeight:o?"normal":1.8,marginTop:o?a:"inherit"}},[r],4)}function Pw(e){return ti("div",{class:"uni-tabbar__reddot"+(e?" uni-tabbar__badge":"")},[e],2)}let Iw;function Ow(){return Iw}const Lw=gu({name:"Layout",setup(e,{emit:t}){const n=ln(null);vc({"--status-bar-height":"0px","--top-window-height":"0px","--window-left":"0px","--window-right":"0px","--window-margin":"0px","--tab-bar-height":"0px"});const o=function(){const e=bl();return{routeKey:xi((()=>ev("/"+e.meta.route,$m()))),isTabBar:xi((()=>e.meta.isTabBar)),routeCache:nv}}(),{layoutState:r,windowState:i}=function(){Lm();{const e=Yt({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return eo((()=>e.marginWidth),(e=>vc({"--window-margin":e+"px"}))),eo((()=>e.leftWindowWidth+e.marginWidth),(e=>{vc({"--window-left":e+"px"})})),eo((()=>e.rightWindowWidth+e.marginWidth),(e=>{vc({"--window-right":e+"px"})})),{layoutState:e,windowState:xi((()=>({})))}}}();!function(e,t){const n=Lm();function o(){const o=document.body.clientWidth,r=Gm();let i={};if(r.length>0){i=r[r.length-1].$page.meta}else{const e=Vc(n.path,!0);e&&(i=e.meta)}const a=parseInt(String((p(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let s=!1;s=o>a,s&&a?(e.marginWidth=(o-a)/2,Mn((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+a+"px;margin:0 auto;")}))):(e.marginWidth=0,Mn((()=>{const e=t.value;e&&e.removeAttribute("style")})))}eo([()=>n.path],o),Ro((()=>{o(),window.addEventListener("resize",o)}))}(r,n);const a=function(e){const t=Lm(),n=Rm(),o=xi((()=>t.meta.isTabBar&&n.shown));return vc({"--tab-bar-height":n.height}),o}(),s=function(e){const t=ln(!1);return xi((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(a);return Iw=r,()=>{const e=function(e,t,n,o,r,i){return function({routeKey:e,isTabBar:t,routeCache:n}){return ti(ml,null,{default:Un((({Component:o})=>[(Fr(),Xr(xo,{matchBy:"key",cache:n},[(Fr(),Xr(Go(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))])),_:1})}(e)}(o),t=function(e){return Wo(ti(Tw,null,null,512),[[ka,e.value]])}(a);return ti("uni-app",{ref:n,class:s.value},[e,t],2)}}});const $w=Pd("showTopWindow",((e,{resolve:t,reject:n})=>{const o=Ow();o?(o.apiShowTopWindow=!0,Mn(t)):n()})),Dw=Pd("hideTopWindow",((e,{resolve:t,reject:n})=>{const o=Ow();o?(o.apiShowTopWindow=!1,Mn(t)):n()})),Rw=Pd("showLeftWindow",((e,{resolve:t,reject:n})=>{const o=Ow();o?(o.apiShowLeftWindow=!0,Mn(t)):n()})),Bw=Pd("hideLeftWindow",((e,{resolve:t,reject:n})=>{const o=Ow();o?(o.apiShowLeftWindow=!1,Mn(t)):n()})),Nw=Pd("showRightWindow",((e,{resolve:t,reject:n})=>{const o=Ow();o?(o.apiShowRightWindow=!0,Mn(t)):n()})),qw=Pd("hideRightWindow",((e,{resolve:t,reject:n})=>{const o=Ow();o?(o.apiShowRightWindow=!1,Mn(t)):n()})),jw=Md(0,(()=>{const e=Ow();return c({},e&&e.topWindowStyle)})),zw=Md(0,(e=>{const t=Ow();t&&(t.topWindowStyle=e)})),Vw=Md(0,(()=>{const e=Ow();return c({},e&&e.leftWindowStyle)})),Fw=Md(0,(e=>{const t=Ow();t&&(t.leftWindowStyle=e)})),Uw=Md(0,(()=>{const e=Ow();return c({},e&&e.rightWindowStyle)})),Ww=Md(0,(e=>{const t=Ow();t&&(t.rightWindowStyle=e)})),Hw=Md(0,(e=>{const t=document.querySelector("uni-page-body");return t?t.querySelector(`#${e}`):null})),Yw=Pd("saveImageToPhotosAlbum",$d("saveImageToPhotosAlbum")),Xw=Md(0,Od("getRecorderManager")),Gw=Pd("saveVideoToPhotosAlbum",$d("saveVideoToPhotosAlbum")),Jw=Md(0,Od("createCameraContext")),Qw=Md(0,Od("createLivePlayerContext")),Kw=Pd("saveFile",$d("saveFile")),Zw=Pd("getSavedFileList",$d("getSavedFileList")),ex=Pd("getSavedFileInfo",$d("getSavedFileInfo")),tx=Pd("removeSavedFile",$d("removeSavedFile")),nx=Cd("onMemoryWarning",Ld("onMemoryWarning")),ox=Cd("onGyroscopeChange",Ld("onGyroscopeChange")),rx=Pd("startGyroscope",$d("startGyroscope")),ix=Pd("stopGyroscope",$d("stopGyroscope")),ax=Pd("scanCode",$d("scanCode")),sx=Pd("setScreenBrightness",$d("setScreenBrightness")),lx=Pd("getScreenBrightness",$d("getScreenBrightness")),cx=Pd("setKeepScreenOn",$d("setKeepScreenOn")),ux=Cd("onUserCaptureScreen",Ld("onUserCaptureScreen")),dx=Pd("addPhoneContact",$d("addPhoneContact")),px=Pd("login",$d("login")),fx=Pd("getProvider",$d("getProvider")),hx=Object.defineProperty({__proto__:null,$emit:ep,$off:Zd,$on:Qd,$once:Kd,addInterceptor:Xd,addPhoneContact:dx,arrayBufferToBase64:Rd,base64ToArrayBuffer:Dd,canIUse:Um,canvasGetImageData:Ep,canvasPutImageData:Ap,canvasToTempFilePath:Mp,chooseFile:sb,chooseImage:cb,chooseLocation:n_,chooseVideo:Pb,clearStorage:Jy,clearStorageSync:Gy,closePreviewImage:Ab,closeSocket:Ub,connectSocket:zb,createAnimation:Kp,createCameraContext:Jw,createCanvasContext:Cp,createInnerAudioContext:oy,createIntersectionObserver:Bp,createLivePlayerContext:Qw,createMapContext:ap,createMediaQueryObserver:jp,createSelectorQuery:Yp,createVideoContext:op,cssBackdropFilter:Vm,cssConstant:zm,cssEnv:jm,cssVar:qm,downloadFile:Db,getAppBaseInfo:gy,getClipboardData:By,getDeviceInfo:hy,getElementById:Hw,getEnterOptionsSync:df,getFileInfo:Zy,getImageInfo:nb,getLaunchOptionsSync:pf,getLeftWindowStyle:Vw,getLocale:nf,getLocation:Jb,getNetworkType:xy,getProvider:fx,getPushClientId:bf,getRecorderManager:Xw,getRightWindowStyle:Uw,getSavedFileInfo:ex,getSavedFileList:Zw,getScreenBrightness:lx,getSelectedTextRange:sf,getStorage:Hy,getStorageInfo:Ky,getStorageInfoSync:Qy,getStorageSync:Wy,getSystemInfo:vy,getSystemInfoSync:my,getTopWindowStyle:jw,getVideoInfo:ob,getWindowInfo:uy,hideActionSheet:()=>{J_&&(J_.visible=!1)},hideKeyboard:tb,hideLeftWindow:Bw,hideLoading:F_,hideModal:()=>{k_&&(k_.visible=!1)},hideNavigationBarLoading:iw,hideRightWindow:qw,hideTabBar:vw,hideTabBarRedDot:bw,hideToast:V_,hideTopWindow:Dw,interceptors:{},invokePushCallback:function(e){if("enabled"===e.type)gf=!0;else if("clientId"===e.type)ff=e.cid,hf=e.errMsg,yf(ff,e.errMsg);else if("pushMsg"===e.type){const t={type:"receive",data:mf(e.message)};for(let e=0;e<_f.length;e++){if((0,_f[e])(t),t.stopped)break}}else"click"===e.type&&_f.forEach((t=>{t({type:"click",data:mf(e.message)})}))},loadFontFace:ew,login:px,makePhoneCall:ry,navigateBack:d_,navigateTo:f_,offAccelerometerChange:ky,offAppHide:function(e){uf("onHide",e)},offAppShow:function(e){uf("onShow",e)},offCompassChange:Py,offError:function(e){uf("onError",e)},offLocationChange:l_,offLocationChangeError:u_,offNetworkStatusChange:wy,offPageNotFound:function(e){uf("onPageNotFound",e)},offPushMessage:e=>{if(e){const t=_f.indexOf(e);t>-1&&_f.splice(t,1)}else _f.length=0},offThemeChange:zy,offUnhandledRejection:function(e){uf("onUnhandledRejection",e)},offWindowResize:tf,onAccelerometerChange:Sy,onAppHide:function(e){cf("onHide",e)},onAppShow:function(e){cf("onShow",e)},onCompassChange:My,onCreateVueApp:function(e){if(Oe)return e(Oe);Le.push(e)},onError:function(e){cf("onError",e)},onGyroscopeChange:ox,onLocaleChange:of,onLocationChange:s_,onLocationChangeError:c_,onMemoryWarning:nx,onNetworkStatusChange:_y,onPageNotFound:function(e){cf("onPageNotFound",e)},onPushMessage:e=>{-1===_f.indexOf(e)&&_f.push(e)},onSocketClose:Gb,onSocketError:Yb,onSocketMessage:Xb,onSocketOpen:Hb,onTabBarMidButtonTap:Zp,onThemeChange:jy,onUnhandledRejection:function(e){cf("onUnhandledRejection",e)},onUserCaptureScreen:ux,onWindowResize:ef,openDocument:eb,openLocation:Zb,pageScrollTo:sw,preloadPage:y_,previewImage:Eb,reLaunch:g_,redirectTo:h_,removeInterceptor:Gd,removeSavedFile:tx,removeStorage:Xy,removeStorageSync:Yy,removeTabBarBadge:ww,request:Ib,saveFile:Kw,saveImageToPhotosAlbum:Yw,saveVideoToPhotosAlbum:Gw,scanCode:ax,sendSocketMessage:Fb,setClipboardData:Ny,setKeepScreenOn:cx,setLeftWindowStyle:Fw,setLocale:rf,setNavigationBarColor:ow,setNavigationBarTitle:aw,setPageMeta:af,setRightWindowStyle:Ww,setScreenBrightness:sx,setStorage:Fy,setStorageSync:Vy,setTabBarBadge:xw,setTabBarItem:gw,setTabBarStyle:mw,setTopWindowStyle:zw,showActionSheet:Z_,showLeftWindow:Rw,showLoading:z_,showModal:M_,showNavigationBarLoading:rw,showRightWindow:Nw,showTabBar:yw,showTabBarRedDot:_w,showToast:q_,showTopWindow:$w,startAccelerometer:Cy,startCompass:Iy,startGyroscope:rx,startLocationUpdate:i_,startPullDownRefresh:lw,stopAccelerometer:Ey,stopCompass:Oy,stopGyroscope:ix,stopLocationUpdate:a_,stopPullDownRefresh:cw,switchTab:v_,uploadFile:Bb,upx2px:Wd,vibrateLong:Dy,vibrateShort:$y},Symbol.toStringTag,{value:"Module"}),gx=gu({name:"MapLocation",setup(){const e=Yt({latitude:0,longitude:0,rotate:0});{let t=function(t){e.rotate=t.direction},n=function(){Jb({type:"gcj02",success:t=>{e.latitude=t.latitude,e.longitude=t.longitude},complete:()=>{i=setTimeout(n,3e4)}})},o=function(){i&&clearTimeout(i),Py(t)};const r=Qn("onMapReady");let i;My(t),r(n),jo(o);const a=Qn("addMapChidlContext"),s=Qn("removeMapChidlContext"),l={id:"MAP_LOCATION",state:e};a(l),jo((()=>s(l)))}return()=>e.latitude?ti(Xv,li({anchor:{x:.5,y:.5},width:"44",height:"44",iconPath:qv},e),null,16,["iconPath"]):null}}),mx=gu({name:"MapPolygon",props:{dashArray:{type:Array,default:()=>[0,0]},points:{type:Array,required:!0},strokeWidth:{type:Number,default:1},strokeColor:{type:String,default:"#000000"},fillColor:{type:String,default:"#00000000"},zIndex:{type:Number,default:0}},setup(e){let t;return Qn("onMapReady")(((n,o,r)=>{function i(){const{points:r,strokeWidth:i,strokeColor:a,dashArray:s,fillColor:l,zIndex:c}=e,u=r.map((e=>{const{latitude:t,longitude:n}=e;return Wv()?[n,t]:Hv()?new o.Point(n,t):new o.LatLng(t,n)})),{r:d,g:p,b:f,a:h}=Gv(l),{r:g,g:m,b:v,a:y}=Gv(a),b={clickable:!0,cursor:"crosshair",editable:!1,map:n,fillColor:"",path:u,strokeColor:"",strokeDashStyle:s.some((e=>e>0))?"dash":"solid",strokeWeight:i,visible:!0,zIndex:c};o.Color?(b.fillColor=new o.Color(d,p,f,h),b.strokeColor=new o.Color(g,m,v,y)):(b.fillColor=`rgb(${d}, ${p}, ${f})`,b.fillOpacity=h,b.strokeColor=`rgb(${g}, ${m}, ${v})`,b.strokeOpacity=y),t?t.setOptions(b):Hv()?(t=new o.Polygon(b.path,b),n.addOverlay(t)):t=new o.Polygon(b)}i(),eo(e,i)})),jo((()=>{t.setMap(null)})),()=>null}});function vx(e){const t=[];return f(e)&&e.forEach((e=>{e&&e.latitude&&e.longitude&&t.push({latitude:e.latitude,longitude:e.longitude})})),t}function yx(e,t,n){return Hv()?function(e,t,n){return new e.Point(n,t)}(e,t,n):Wv()?function(e,t,n){return new e.LngLat(n,t)}(e,t,n):function(e,t,n){return new e.LatLng(t,n)}(e,t,n)}function bx(e){return"getLat"in e?e.getLat():Hv()?e.lat:e.lat()}function _x(e){return"getLng"in e?e.getLng():Hv()?e.lng:e.lng()}function xx(e,t,n){const o=vu(t,n),r=ln(null);let i,a;const s=Yt({latitude:Number(e.latitude),longitude:Number(e.longitude),includePoints:vx(e.includePoints)}),l=[];let u,d;function p(e){u?e(a,i,o):l.push(e)}const f=[];function h(e){d?e():l.push(e)}const g={};function m(){const e=a.getCenter();return{scale:a.getZoom(),centerLocation:{latitude:bx(e),longitude:_x(e)}}}function v(){if(Wv()){const e=[];s.includePoints.forEach((t=>{e.push([t.longitude,t.latitude])}));const t=new i.Bounds(...e);a.setBounds(t)}else if(Hv());else{const e=new i.LatLngBounds;s.includePoints.forEach((({latitude:t,longitude:n})=>{const o=new i.LatLng(t,n);e.extend(o)})),a.fitBounds(e)}}function y(){const t=r.value,l=yx(i,s.latitude,s.longitude),u=i.event||i.Event,p=new i.Map(t,{center:l,zoom:Number(e.scale),disableDoubleClickZoom:!0,mapTypeControl:!1,zoomControl:!1,scaleControl:!1,panControl:!1,fullscreenControl:!1,streetViewControl:!1,keyboardShortcuts:!1,minZoom:5,maxZoom:18,draggable:!0});if(Hv()&&(p.centerAndZoom(l,Number(e.scale)),p.enableScrollWheelZoom(),p._printLog&&p._printLog("uniapp")),eo((()=>e.scale),(e=>{p.setZoom(Number(e)||16)})),h((()=>{s.includePoints.length&&(v(),function(){const e=yx(i,s.latitude,s.longitude);a.setCenter(e)}())})),Hv())p.addEventListener("click",(()=>{o("tap",{},{}),o("click",{},{})})),p.addEventListener("dragstart",(()=>{o("regionchange",{},{type:"begin",causedBy:"gesture"})})),p.addEventListener("dragend",(()=>{o("regionchange",{},c({type:"end",causedBy:"drag"},m()))}));else{const e=u.addListener(p,"bounds_changed",(()=>{e.remove(),d=!0,f.forEach((e=>e())),f.length=0}));u.addListener(p,"click",(()=>{o("tap",{},{}),o("click",{},{})})),u.addListener(p,"dragstart",(()=>{o("regionchange",{},{type:"begin",causedBy:"gesture"})})),u.addListener(p,"dragend",(()=>{o("regionchange",{},c({type:"end",causedBy:"drag"},m()))}));const t=()=>{n("update:scale",p.getZoom()),o("regionchange",{},c({type:"end",causedBy:"scale"},m()))};u.addListener(p,"zoom_changed",t),u.addListener(p,"zoomend",t),u.addListener(p,"center_changed",(()=>{const e=p.getCenter(),t=bx(e),o=_x(e);n("update:latitude",t),n("update:longitude",o)}))}return p}eo([()=>e.latitude,()=>e.longitude],(([e,t])=>{const n=Number(e),o=Number(t);if((n!==s.latitude||o!==s.longitude)&&(s.latitude=n,s.longitude=o,a)){const e=yx(i,s.latitude,s.longitude);a.setCenter(e)}})),eo((()=>e.includePoints),(e=>{s.includePoints=vx(e),d&&v()}),{deep:!0});try{bm(((e,t={})=>{switch(e){case"getCenterLocation":p((()=>{const n=a.getCenter();me(t,{latitude:bx(n),longitude:_x(n),errMsg:`${e}:ok`})}));break;case"moveToLocation":{let n=Number(t.latitude),o=Number(t.longitude);if(!n||!o){const e=g.MAP_LOCATION;e&&(n=e.state.latitude,o=e.state.longitude)}if(n&&o){if(s.latitude=n,s.longitude=o,a){const e=yx(i,n,o);a.setCenter(e)}p((()=>{me(t,`${e}:ok`)}))}else me(t,`${e}:fail`)}break;case"translateMarker":p((()=>{const n=g[t.markerId];if(n){try{n.translate(t)}catch(o){me(t,`${e}:fail ${o.message}`)}me(t,`${e}:ok`)}else me(t,`${e}:fail not found`)}));break;case"includePoints":s.includePoints=vx(t.includePoints),(d||Wv())&&v(),h((()=>{me(t,`${e}:ok`)}));break;case"getRegion":h((()=>{const n=a.getBounds(),o=n.getSouthWest(),r=n.getNorthEast();me(t,{southwest:{latitude:bx(o),longitude:_x(o)},northeast:{latitude:bx(r),longitude:_x(r)},errMsg:`${e}:ok`})}));break;case"getScale":p((()=>{me(t,{scale:a.getZoom(),errMsg:`${e}:ok`})}))}}),wm(),!0)}catch(b){}return Ro((()=>{Rv(e.libraries,(e=>{i=e,a=y(),u=!0,l.forEach((e=>e(a,i,o))),l.length=0,o("updated",{},{})}))})),Jn("onMapReady",p),Jn("addMapChidlContext",(function(e){g[e.id]=e})),Jn("removeMapChidlContext",(function(e){delete g[e.id]})),{state:s,mapRef:r,trigger:o}}const Tx=hu({name:"Map",props:{id:{type:String,default:""},latitude:{type:[String,Number],default:0},longitude:{type:[String,Number],default:0},scale:{type:[String,Number],default:16},markers:{type:Array,default:()=>[]},includePoints:{type:Array,default:()=>[]},polyline:{type:Array,default:()=>[]},circles:{type:Array,default:()=>[]},controls:{type:Array,default:()=>[]},showLocation:{type:[Boolean,String],default:!1},libraries:{type:Array,default:()=>[]},polygons:{type:Array,default:()=>[]}},emits:["markertap","labeltap","callouttap","controltap","regionchange","tap","click","updated","update:scale","update:latitude","update:longitude"],setup(e,{emit:t,slots:n}){const o=ln(null),{mapRef:r,trigger:i}=xx(e,o,t);return()=>ti("uni-map",{ref:o,id:e.id},[ti("div",{ref:r,style:"width: 100%; height: 100%; position: relative; overflow: hidden"},null,512),e.markers.map((e=>ti(Xv,li({key:e.id},e),null,16))),e.polyline.map((e=>ti(Qv,e,null,16))),e.circles.map((e=>ti(Kv,e,null,16))),e.controls.map((e=>ti(ey,li(e,{trigger:i}),null,16,["trigger"]))),e.showLocation&&ti(gx,null,null),e.polygons.map((e=>ti(mx,e,null,16))),ti("div",{style:"position: absolute;top: 0;width: 100%;height: 100%;overflow: hidden;pointer-events: none;"},[n.default&&n.default()])],8,["id"])}});function Sx(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!Gr(e)}function kx(e){if(e.mode===Ax.TIME)return"00:00";if(e.mode===Ax.DATE){const t=(new Date).getFullYear()-150;switch(e.fields){case Mx.YEAR:return t.toString();case Mx.MONTH:return t+"-01";default:return t+"-01-01"}}return""}function Cx(e){if(e.mode===Ax.TIME)return"23:59";if(e.mode===Ax.DATE){const t=(new Date).getFullYear()+150;switch(e.fields){case Mx.YEAR:return t.toString();case Mx.MONTH:return t+"-12";default:return t+"-12-31"}}return""}function Ex(e,t,n,o){const r=e.mode===Ax.DATE?"-":":",i=e.mode===Ax.DATE?t.dateArray:t.timeArray;let a;if(e.mode===Ax.TIME)a=2;else switch(e.fields){case Mx.YEAR:a=1;break;case Mx.MONTH:a=2;break;default:a=3}const s=String(n).split(r);let l=[];for(let c=0;c<a;c++){const e=s[c];l.push(i[c].indexOf(e))}return l.indexOf(-1)>=0&&(l=o?Ex(e,t,o):l.map((()=>0))),l}const Ax={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date"},Mx={YEAR:"year",MONTH:"month",DAY:"day"},Px={PICKER:"picker",SELECT:"select"},Ix=hu({name:"Picker",compatConfig:{MODE:3},props:{name:{type:String,default:""},range:{type:Array,default:()=>[]},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:Ax.SELECTOR,validator:e=>Object.values(Ax).includes(e)},fields:{type:String,default:""},start:{type:String,default:e=>kx(e)},end:{type:String,default:e=>Cx(e)},disabled:{type:[Boolean,String],default:!1},selectorType:{type:String,default:""}},emits:["change","cancel","columnchange"],setup(e,{emit:t,slots:n}){Dl();const{t:o}=kl(),r=ln(null),i=ln(null),a=ln(null),s=ln(null),l=ln(!1),{state:c,rangeArray:u}=function(e){const t=Yt({valueSync:void 0,visible:!1,contentVisible:!1,popover:null,valueChangeSource:"",timeArray:[],dateArray:[],valueArray:[],oldValueArray:[],isDesktop:!1,popupStyle:{content:{},triangle:{}}}),n=xi((()=>{let n=e.range;switch(e.mode){case Ax.SELECTOR:return[n];case Ax.MULTISELECTOR:return n;case Ax.TIME:return t.timeArray;case Ax.DATE:{const n=t.dateArray;switch(e.fields){case Mx.YEAR:return[n[0]];case Mx.MONTH:return[n[0],n[1]];default:return[n[0],n[1],n[2]]}}}return[]}));return{state:t,rangeArray:n}}(e),d=vu(r,t),{system:p,selectorTypeComputed:h,_show:g,_l10nColumn:m,_l10nItem:v,_input:y,_fixInputPosition:b,_pickerViewChange:_,_cancel:w,_change:x,_resetFormData:T,_getFormData:S,_createTime:k,_createDate:C,_setValueSync:E}=function(e,t,n,o,r,i,a){const s=function(){const e=ln(!1);return e.value=(()=>0===String(navigator.vendor).indexOf("Apple")&&navigator.maxTouchPoints>0)(),e}(),l=function(){const e=ln("");return e.value=(()=>{if(/win|mac/i.test(navigator.platform)){if("Google Inc."===navigator.vendor)return"chrome";if(/Firefox/.test(navigator.userAgent))return"firefox"}return""})(),e}(),c=xi((()=>{const t=e.selectorType;return Object.values(Px).includes(t)?t:s.value?Px.PICKER:Px.SELECT})),u=xi((()=>e.mode===Ax.DATE&&!Object.values(Mx).includes(e.fields)&&t.isDesktop?l.value:"")),d=xi((()=>Ex(e,t,e.start,kx(e)))),p=xi((()=>Ex(e,t,e.end,Cx(e))));function h(n){if(e.disabled)return;t.valueChangeSource="";let o=r.value,i=n.currentTarget;o.remove(),(document.querySelector("uni-app")||document.body).appendChild(o),o.style.display="block";const a=i.getBoundingClientRect();t.popover={top:a.top,left:a.left,width:a.width,height:a.height},setTimeout((()=>{t.visible=!0}),20)}function g(){return{value:t.valueSync,key:e.name}}function m(){switch(e.mode){case Ax.SELECTOR:t.valueSync=0;break;case Ax.MULTISELECTOR:t.valueSync=e.value.map((e=>0));break;case Ax.DATE:case Ax.TIME:t.valueSync=""}}function v(){let e=[],n=[];for(let t=0;t<24;t++)e.push((t<10?"0":"")+t);for(let t=0;t<60;t++)n.push((t<10?"0":"")+t);t.timeArray.push(e,n)}function y(){let t=(new Date).getFullYear(),n=t-150,o=t+150;if(e.start){const t=new Date(e.start).getFullYear();!isNaN(t)&&t<n&&(n=t)}if(e.end){const t=new Date(e.end).getFullYear();!isNaN(t)&&t>o&&(o=t)}return{start:n,end:o}}function b(){let e=[];const n=y();for(let t=n.start,i=n.end;t<=i;t++)e.push(String(t));let o=[];for(let t=1;t<=12;t++)o.push((t<10?"0":"")+t);let r=[];for(let t=1;t<=31;t++)r.push((t<10?"0":"")+t);t.dateArray.push(e,o,r)}function _(e){return 60*e[0]+e[1]}function w(e){const t=31;return e[0]*t*12+(e[1]||0)*t+(e[2]||0)}function x(e,t){for(let n=0;n<e.length&&n<t.length;n++)e[n]=t[n]}function T(){let n=e.value;switch(e.mode){case Ax.MULTISELECTOR:{f(n)||(n=t.valueArray),f(t.valueSync)||(t.valueSync=[]);const o=t.valueSync.length=Math.max(n.length,e.range.length);for(let r=0;r<o;r++){const o=Number(n[r]),i=Number(t.valueSync[r]),a=isNaN(o)?isNaN(i)?0:i:o,s=e.range[r]?e.range[r].length-1:0;t.valueSync.splice(r,1,a<0||a>s?0:a)}}break;case Ax.TIME:case Ax.DATE:t.valueSync=String(n);break;default:{const e=Number(n);t.valueSync=e<0?0:e;break}}}function S(){let n,o=t.valueSync;switch(e.mode){case Ax.MULTISELECTOR:n=[...o];break;case Ax.TIME:n=Ex(e,t,o,ge({mode:Ax.TIME}));break;case Ax.DATE:n=Ex(e,t,o,ge({mode:Ax.DATE}));break;default:n=[o]}t.oldValueArray=[...n],t.valueArray=[...n]}function k(){let n=t.valueArray;switch(e.mode){case Ax.SELECTOR:return n[0];case Ax.MULTISELECTOR:return n.map((e=>e));case Ax.TIME:return t.valueArray.map(((e,n)=>t.timeArray[n][e])).join(":");case Ax.DATE:return t.valueArray.map(((e,n)=>t.dateArray[n][e])).join("-")}}function C(){A(),t.valueChangeSource="click";const e=k();t.valueSync=f(e)?e.map((e=>e)):e,n("change",{},{value:e})}function E(e){if("firefox"===u.value&&e){const{top:n,left:o,width:r,height:i}=t.popover,{pageX:a,pageY:s}=e;if(a>o&&a<o+r&&s>n&&s<n+i)return}A(),n("cancel",{},{})}function A(){t.visible=!1,setTimeout((()=>{let e=r.value;e.remove(),o.value.prepend(e),e.style.display="none"}),260)}function M(){e.mode===Ax.SELECTOR&&c.value===Px.SELECT&&(i.value.scrollTop=34*t.valueArray[0])}function P(e){const n=e.target;t.valueSync=n.value,Mn((()=>{C()}))}function I(e){if("chrome"===u.value){const t=o.value.getBoundingClientRect(),n=32;a.value.style.left=e.clientX-t.left-1.5*n+"px",a.value.style.top=e.clientY-t.top-.5*n+"px"}}function O(e){t.valueArray=L(e.detail.value,!0)}function L(t,n){const{getLocale:o}=kl();if(e.mode===Ax.DATE){const r=o();if(!r.startsWith("zh"))switch(e.fields){case Mx.YEAR:return t;case Mx.MONTH:return[t[1],t[0]];default:switch(r){case"es":case"fr":return[t[2],t[1],t[0]];default:return n?[t[2],t[0],t[1]]:[t[1],t[2],t[0]]}}}return t}function $(t,n){const{getLocale:o}=kl();if(e.mode===Ax.DATE){const r=o();if(r.startsWith("zh")){return t+["年","月","日"][n]}if(e.fields!==Mx.YEAR&&n===(e.fields===Mx.MONTH||"es"!==r&&"fr"!==r?0:1)){let e;switch(r){case"es":e=["enero","febrero","marzo","abril","mayo","junio","​​julio","agosto","septiembre","octubre","noviembre","diciembre"];break;case"fr":e=["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"];break;default:e=["January","February","March","April","May","June","July","August","September","October","November","December"]}return e[Number(t)-1]}}return t}return eo((()=>t.visible),(e=>{e?(clearTimeout(Ox),t.contentVisible=e,M()):Ox=setTimeout((()=>{t.contentVisible=e}),300)})),eo([()=>e.mode,()=>e.value,()=>e.range],T,{deep:!0}),eo((()=>t.valueSync),S,{deep:!0}),eo((()=>t.valueArray),(o=>{if(e.mode===Ax.TIME||e.mode===Ax.DATE){const n=e.mode===Ax.TIME?_:w,o=t.valueArray,r=d.value,i=p.value;if(e.mode===Ax.DATE){const e=t.dateArray,n=e[2].length,r=Number(e[2][o[2]])||1,i=new Date(`${e[0][o[0]]}/${e[1][o[1]]}/${r}`).getDate();i<r&&(o[2]-=i+n-r)}n(o)<n(r)?x(o,r):n(o)>n(i)&&x(o,i)}o.forEach(((o,r)=>{o!==t.oldValueArray[r]&&(t.oldValueArray[r]=o,e.mode===Ax.MULTISELECTOR&&n("columnchange",{},{column:r,value:o}))}))})),{selectorTypeComputed:c,system:u,_show:h,_cancel:E,_change:C,_l10nColumn:L,_l10nItem:$,_input:P,_resetFormData:m,_getFormData:g,_createTime:v,_createDate:b,_setValueSync:T,_fixInputPosition:I,_pickerViewChange:O}}(e,c,d,r,i,a,s);!function(e,t,n){const{key:o,disable:r}=pb();Kn((()=>{r.value=!e.visible})),eo(o,(e=>{"esc"===e?t():"enter"===e&&n()}))}(c,w,x),function(e,t){const n=Qn(wu,!1);if(n){const o={reset:e,submit:()=>{const e=["",null],{key:n,value:o}=t();return""!==n&&(e[0]=n,e[1]=o),e}};n.addField(o),qo((()=>{n.removeField(o)}))}}(T,S),k(),C(),E();const A=W_(c);return Kn((()=>{c.isDesktop=A.isDesktop.value,c.popupStyle=A.popupStyle.value})),qo((()=>{i.value&&i.value.remove()})),Ro((()=>{l.value=!0})),()=>{let t;const{visible:d,contentVisible:f,valueArray:T,popupStyle:S,valueSync:k}=c,{rangeKey:C,mode:E,start:A,end:M}=e,P=_u(e,"disabled");return ti("uni-picker",li({ref:r},P,{onClick:mu(g)}),[l.value?ti("div",{ref:i,class:["uni-picker-container",`uni-${E}-${h.value}`],onWheel:pc,onTouchmove:pc},[ti(Qi,{name:"uni-fade"},{default:()=>[Wo(ti("div",{class:"uni-mask uni-picker-mask",onClick:mu(w),onMousemove:b},null,40,["onClick","onMousemove"]),[[ka,d]])]}),p.value?null:ti("div",{class:[{"uni-picker-toggle":d},"uni-picker-custom"],style:S.content},[ti("div",{class:"uni-picker-header",onClick:fc},[ti("div",{class:"uni-picker-action uni-picker-action-cancel",onClick:mu(w)},[o("uni.picker.cancel")],8,["onClick"]),ti("div",{class:"uni-picker-action uni-picker-action-confirm",onClick:x},[o("uni.picker.done")],8,["onClick"])],8,["onClick"]),f?ti(zg,{value:m(T),class:"uni-picker-content",onChange:_},Sx(t=Ko(m(u.value),((e,t)=>{let n;return ti(Jg,{key:t},Sx(n=Ko(e,((e,n)=>ti("div",{key:n,class:"uni-picker-item"},["object"==typeof e?e[C]||"":v(e,t)]))))?n:{default:()=>[n],_:1})})))?t:{default:()=>[t],_:1},8,["value","onChange"]):null,ti("div",{ref:a,class:"uni-picker-select",onWheel:fc,onTouchmove:fc},[Ko(u.value[0],((e,t)=>ti("div",{key:t,class:["uni-picker-item",{selected:T[0]===t}],onClick:()=>{T[0]=t,x()}},["object"==typeof e?e[C]||"":e],10,["onClick"])))],40,["onWheel","onTouchmove"]),ti("div",{style:S.triangle},null,4)],6)],40,["onWheel","onTouchmove"]):null,ti("div",null,[n.default&&n.default()]),p.value?ti("div",{class:"uni-picker-system",onMousemove:mu(b)},[ti("input",{class:["uni-picker-system_input",p.value],ref:s,value:k,type:E,tabindex:"-1",min:A,max:M,onChange:e=>{y(e),fc(e)}},null,42,["value","type","min","max","onChange"])],40,["onMousemove"]):null],16,["onClick"])}}});let Ox;const Lx=c(Ul,{publishHandler(e,t,n){Dx.subscribeHandler(e,t,n)}}),$x=hx,Dx=c(tu,{publishHandler(e,t,n){Lx.subscribeHandler(e,t,n)}}),Rx=gu({name:"PageHead",setup(){const e=ln(null),t=Im(),n=x_(t.navigationBar,(()=>{const e=w_(t.navigationBar);n.backgroundColor=e.backgroundColor,n.titleColor=e.titleColor})),{clazz:o,style:r}=function(e){const t=xi((()=>{const{type:t,titlePenetrate:n,shadowColorType:o}=e,r={"uni-page-head":!0,"uni-page-head-transparent":"transparent"===t,"uni-page-head-titlePenetrate":"YES"===n,"uni-page-head-shadow":!!o};return o&&(r[`uni-page-head-shadow-${o}`]=!0),r})),n=xi((()=>({backgroundColor:e.backgroundColor,color:e.titleColor,transitionDuration:e.duration,transitionTimingFunction:e.timingFunc})));return{clazz:t,style:n}}(n);return()=>{const i=function(e,t){if(!t)return ti("div",{class:"uni-page-head-btn",onClick:Nx},[Ec(Sc,"transparent"===e.type?"#fff":e.titleColor,26)],8,["onClick"])}(n,t.isQuit),a=n.type||"default",s="transparent"!==a&&"float"!==a&&ti("div",{class:{"uni-placeholder":!0,"uni-placeholder-titlePenetrate":n.titlePenetrate}},null,2);return ti("uni-page-head",{"uni-page-head-type":a},[ti("div",{ref:e,class:o.value,style:r.value},[ti("div",{class:"uni-page-head-hd"},[i]),Bx(n),ti("div",{class:"uni-page-head-ft"},[])],6),s],8,["uni-page-head-type"])}}});function Bx(e,t){return function({type:e,loading:t,titleSize:n,titleText:o,titleImage:r}){return ti("div",{class:"uni-page-head-bd"},[ti("div",{style:{fontSize:n,opacity:"transparent"===e?0:1},class:"uni-page-head__title"},[t?ti("i",{class:"uni-loading"},null):r?ti("img",{src:r,class:"uni-page-head__title_image"},null,8,["src"]):o],4)])}(e)}function Nx(){1===Gm().length?uni.reLaunch({url:"/"}):uni.navigateBack({from:"backbutton",success(){}})}const qx={name:"PageRefresh",setup(){const{pullToRefresh:e}=Im();return{offset:e.offset,color:e.color}}},jx=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},zx={class:"uni-page-refresh-inner"},Vx=["fill"],Fx=[ei("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null,-1),ei("path",{d:"M0 0h24v24H0z",fill:"none"},null,-1)],Ux={class:"uni-page-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},Wx=["stroke"];const Hx=jx(qx,[["render",function(e,t,n,o,r,i){return Fr(),Yr("uni-page-refresh",null,[ei("div",{style:le({"margin-top":o.offset+"px"}),class:"uni-page-refresh"},[ei("div",zx,[(Fr(),Yr("svg",{fill:o.color,class:"uni-page-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},Fx,8,Vx)),(Fr(),Yr("svg",Ux,[ei("circle",{stroke:o.color,class:"uni-page-refresh__path",cx:"50",cy:"50",r:"20",fill:"none","stroke-width":"4","stroke-miterlimit":"10"},null,8,Wx)]))])],4)])}]]);function Yx(e,t,n){const o=Array.prototype.slice.call(e.changedTouches).filter((e=>e.identifier===t))[0];return!!o&&(e.deltaY=o.pageY-n,!0)}const Xx="aborting",Gx="refreshing",Jx="restoring";function Qx(e){const{id:t,pullToRefresh:n}=Im(),{range:o,height:r}=n;let i,a,s,l,c,u,d,p;bm((()=>{p||(p=Gx,m(),setTimeout((()=>{w()}),50))}),"startPullDownRefresh",!1,t),bm((()=>{p===Gx&&(v(),p=Jx,m(),function(e){if(!a)return;s.transition="-webkit-transform 0.3s",s.transform+=" scale(0.01)";const t=function(){n&&clearTimeout(n),a.removeEventListener("webkitTransitionEnd",t),s.transition="",s.transform="translate3d(-50%, 0, 0)",e()};a.addEventListener("webkitTransitionEnd",t);const n=setTimeout(t,350)}((()=>{v(),p=f=h=null})))}),"stopPullDownRefresh",!1,t),Ro((()=>{i=e.value.$el,a=i.querySelector(".uni-page-refresh"),s=a.style,l=a.querySelector(".uni-page-refresh-inner").style}));let f=null,h=null;function g(e){p&&i&&i.classList[e]("uni-page-refresh--"+p)}function m(){g("add")}function v(){g("remove")}const y=mu((e=>{const t=e.changedTouches[0];c=t.identifier,u=t.pageY,d=!([Xx,Gx,Jx].indexOf(p)>=0)})),b=mu((e=>{if(!d)return;if(!Yx(e,c,u))return;let{deltaY:t}=e;if(0!==(document.documentElement.scrollTop||document.body.scrollTop))return void(c=null);if(t<0&&!p)return;e.preventDefault(),null===f&&(h=t,p="pulling",m()),t-=h,t<0&&(t=0),f=t;(t>=o&&"reached"!==p||t<o&&"pulling"!==p)&&(v(),p="reached"===p?"pulling":"reached",m()),function(e){if(!a)return;let t=e/o;t>1?t=1:t*=t*t;const n=Math.round(e/(o/r))||0;l.transform="rotate("+360*t+"deg)",s.clip="rect("+(45-n)+"px,45px,45px,-5px)",s.transform="translate3d(-50%, "+n+"px, 0)"}(t)})),_=mu((e=>{Yx(e,c,u)&&null!==p&&("pulling"===p?(v(),p=Xx,m(),function(e){if(!a)return;if(s.transform){s.transition="-webkit-transform 0.3s",s.transform="translate3d(-50%, 0, 0)";const t=function(){n&&clearTimeout(n),a.removeEventListener("webkitTransitionEnd",t),s.transition="",e()};a.addEventListener("webkitTransitionEnd",t);const n=setTimeout(t,350)}else e()}((()=>{v(),p=f=h=null}))):"reached"===p&&(v(),p=Gx,m(),w()))}));function w(){a&&(s.transition="-webkit-transform 0.2s",s.transform="translate3d(-50%, "+r+"px, 0)",Rc(t,"onPullDownRefresh"))}return{onTouchstartPassive:y,onTouchmove:b,onTouchend:_,onTouchcancel:_}}const Kx=gu({name:"PageBody",setup(e,t){const n=Im(),o=ln(null),r=n.enablePullDownRefresh?Qx(o):null;return()=>{const e=function(e,t){if(!t.enablePullDownRefresh)return null;return ti(Hx,{ref:e},null,512)}(o,n);return ti(Br,null,[e,ti("uni-page-wrapper",r,[ti("uni-page-body",null,[Zo(t.slots,"default")])],16)])}}});const Zx=gu({name:"Page",setup(e,t){const n=Om($m()),o=n.navigationBar;return tw(n),()=>ti("uni-page",{"data-page":n.route},"custom"!==o.style?[ti(Rx),eT(t)]:[eT(t)])}});function eT(e){return Fr(),Xr(Kx,{key:0},{default:Un((()=>[Zo(e.slots,"page")])),_:3})}const tT={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.getApp=bv,window.getCurrentPages=Gm,window.wx=$x,window.uni=$x,window.UniViewJSBridge=Lx,window.UniServiceJSBridge=Dx,window.rpx2px=Wd,window.__setupPage=e=>Tv(e);const nT=Object.assign({}),oT=Object.assign;window.__uniConfig=oT({tabBar:{position:"bottom",color:"#000000",selectedColor:"#fa2209",borderStyle:"black",blurEffect:"none",fontSize:"10px",iconWidth:"24px",spacing:"3px",height:"50px",backgroundColor:"#ffffff",list:[{pagePath:"pages/index/index",iconPath:"/static/tabbar/home.png",selectedIconPath:"/static/tabbar/home-active.png",text:"首页"},{pagePath:"pages/category/index",iconPath:"/static/tabbar/cate.png",selectedIconPath:"/static/tabbar/cate-active.png",text:"分类"},{pagePath:"pages/cart/index",iconPath:"/static/tabbar/cart.png",selectedIconPath:"/static/tabbar/cart-active.png",text:"购物车"},{pagePath:"pages/user/index",iconPath:"/static/tabbar/user.png",selectedIconPath:"/static/tabbar/user-active.png",text:"我的"}],selectedIndex:0,shown:!0},globalStyle:{maxWidth:750,rpxCalcMaxDeviceWidth:750,rpxCalcBaseDeviceWidth:560,rpxCalcIncludeWidth:9999,backgroundTextStyle:"dark",navigationBar:{backgroundColor:"#ffffff",titleText:"",type:"default",titleColor:"#000000"},isNVue:!1},easycom:{autoscan:!0,custom:{}},compilerVersion:"4.08"},{appId:"__UNI__EBB6AD2",appName:"萤火商城2.0",appVersion:"2.4.3",appVersionCode:243,async:tT,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{maps:{qqmap:{key:"ZWEBZ-R7N3U-BJSVH-4TCR3-66MDQ-S3FDJ"}}},qqMapKey:"ZWEBZ-R7N3U-BJSVH-4TCR3-66MDQ-S3FDJ",bMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"zh-Hans",fallbackLocale:"",locales:Object.keys(nT).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return oT(e[n]||(e[n]={}),nT[t].default),e}),{}),router:{mode:"hash",base:"./",assets:"assets",routerBase:"/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const rT={delay:tT.delay,timeout:tT.timeout,suspensible:tT.suspensible};tT.loading&&(rT.loadingComponent={name:"SystemAsyncLoading",render:()=>ti(Yo(tT.loading))}),tT.error&&(rT.errorComponent={name:"SystemAsyncError",render:()=>ti(Yo(tT.error))});const iT=()=>t((()=>import("./pages-index-index.14ad7184.js")),["./pages-index-index.14ad7184.js","./index.2367c2df.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./uni-app.es.1cc81ac8.js","./index.67a2889e.js","./mp-html.1fbdb00d.js","./mp-html-87831716.css","./myCoupon.b423606c.js","./index.a491b43c.js","./index-93a2b2a0.css","./index.d95be262.js","./index-d0d63531.css","./GoodsStatus.a102fff1.js","./u-tag.af176b7f.js","./u-tag-9d25ed65.css","./color.813a9497.js","./ActiveStatus.55fe1037.js","./index-9fa6be0d.css","./index.fdbaadcb.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./index-c9bc5906.css","./index-222106f5.css"],import.meta.url).then((e=>Tv(e.default||e))),aT=vo(oT({loader:iT},rT)),sT=()=>t((()=>import("./pages-category-index.cf9d5b79.js")),["./pages-category-index.cf9d5b79.js","./wxofficial.d88f1cc0.js","./index.0c0c395b.js","./_plugin-vue_export-helper.1b428a4d.js","./index-13ff918b.css","./index.fdbaadcb.js","./uni-app.es.1cc81ac8.js","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./index-c9bc5906.css","./index.31fdd6ce.js","./index-505570eb.css","./mescroll-mixins.29ba6d02.js","./mescroll-mixins-70c23850.css","./color.813a9497.js","./SpecType.95f7e710.js","./cart.039c75b6.js","./index.6c9377fa.js","./index.f9eedeca.js","./index-565734b8.css","./index-96554799.css"],import.meta.url).then((e=>Tv(e.default||e))),lT=vo(oT({loader:sT},rT)),cT=()=>t((()=>import("./pages-cart-index.39556eef.js")),["./pages-cart-index.39556eef.js","./u-icon.abb5b4fa.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon-7703594e.css","./uni-app.es.1cc81ac8.js","./index.31fdd6ce.js","./index-505570eb.css","./index.7ed10257.js","./index.6c9377fa.js","./index-0a868898.css","./index.fdbaadcb.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./index-c9bc5906.css","./cart.039c75b6.js","./index-49dabf23.css"],import.meta.url).then((e=>Tv(e.default||e))),uT=vo(oT({loader:cT},rT)),dT=()=>t((()=>import("./pages-user-index.93a6e1ec.js")),["./pages-user-index.93a6e1ec.js","./index.a491b43c.js","./_plugin-vue_export-helper.1b428a4d.js","./index-93a2b2a0.css","./index.7ed10257.js","./index.6c9377fa.js","./index-0a868898.css","./index.67a2889e.js","./index.fdbaadcb.js","./uni-app.es.1cc81ac8.js","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./index-c9bc5906.css","./user.9445f703.js","./order.0fece54d.js","./index-e8605cda.css"],import.meta.url).then((e=>Tv(e.default||e))),pT=vo(oT({loader:dT},rT)),fT=()=>t((()=>import("./pages-custom-index.4c6fd44b.js")),["./pages-custom-index.4c6fd44b.js","./index.2367c2df.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./uni-app.es.1cc81ac8.js","./index.67a2889e.js","./mp-html.1fbdb00d.js","./mp-html-87831716.css","./myCoupon.b423606c.js","./index.a491b43c.js","./index-93a2b2a0.css","./index.d95be262.js","./index-d0d63531.css","./GoodsStatus.a102fff1.js","./u-tag.af176b7f.js","./u-tag-9d25ed65.css","./color.813a9497.js","./ActiveStatus.55fe1037.js","./index-9fa6be0d.css","./index-698e7b18.css"],import.meta.url).then((e=>Tv(e.default||e))),hT=vo(oT({loader:fT},rT)),gT=()=>t((()=>import("./pages-search-index.e9d78287.js")),["./pages-search-index.e9d78287.js","./_plugin-vue_export-helper.1b428a4d.js","./index-2e68cdcc.css"],import.meta.url).then((e=>Tv(e.default||e))),mT=vo(oT({loader:gT},rT)),vT=()=>t((()=>import("./pages-login-index.5347436f.js")),["./pages-login-index.5347436f.js","./captcha.f8d84ab0.js","./verify.3975cb19.js","./_plugin-vue_export-helper.1b428a4d.js","./upload.8ab6f224.js","./index.a491b43c.js","./index-93a2b2a0.css","./index-e71571d2.css"],import.meta.url).then((e=>Tv(e.default||e))),yT=vo(oT({loader:vT},rT)),bT=()=>t((()=>import("./pages-user-bind-index.db4c190b.js")),["./pages-user-bind-index.db4c190b.js","./user.9445f703.js","./captcha.f8d84ab0.js","./verify.3975cb19.js","./_plugin-vue_export-helper.1b428a4d.js","./index-94cdd09e.css"],import.meta.url).then((e=>Tv(e.default||e))),_T=vo(oT({loader:bT},rT)),wT=()=>t((()=>import("./pages-user-personal-index.58f64901.js")),["./pages-user-personal-index.58f64901.js","./u-form.4d62b4b6.js","./u-icon.abb5b4fa.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon-7703594e.css","./uni-app.es.1cc81ac8.js","./emitter.1571a5d9.js","./u-form-79329cbc.css","./index.a491b43c.js","./index-93a2b2a0.css","./user.9445f703.js","./upload.8ab6f224.js","./index-a365a307.css"],import.meta.url).then((e=>Tv(e.default||e))),xT=vo(oT({loader:wT},rT)),TT=()=>t((()=>import("./pages-article-index.552649c7.js")),["./pages-article-index.552649c7.js","./u-tabs.45b9467e.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./u-tabs-3dd7029d.css","./mescroll-mixins.29ba6d02.js","./mescroll-mixins-70c23850.css","./wxofficial.d88f1cc0.js","./index.61295699.js","./index-baf49dbb.css"],import.meta.url).then((e=>Tv(e.default||e))),ST=vo(oT({loader:TT},rT)),kT=()=>t((()=>import("./pages-article-detail.6b8807f0.js")),["./pages-article-detail.6b8807f0.js","./mp-html.1fbdb00d.js","./_plugin-vue_export-helper.1b428a4d.js","./mp-html-87831716.css","./uni-app.es.1cc81ac8.js","./wxofficial.d88f1cc0.js","./index.61295699.js","./detail-45f6fa9d.css"],import.meta.url).then((e=>Tv(e.default||e))),CT=vo(oT({loader:kT},rT)),ET=()=>t((()=>import("./pages-help-index.d7ff07df.js")),["./pages-help-index.d7ff07df.js","./mescroll-mixins.29ba6d02.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./mescroll-mixins-70c23850.css","./index-327b0424.css"],import.meta.url).then((e=>Tv(e.default||e))),AT=vo(oT({loader:ET},rT)),MT=()=>t((()=>import("./pages-coupon-index.428842e9.js")),["./pages-coupon-index.428842e9.js","./myCoupon.b423606c.js","./CouponType.ec81c796.js","./index.31fdd6ce.js","./_plugin-vue_export-helper.1b428a4d.js","./index-505570eb.css","./index-0d5339bd.css"],import.meta.url).then((e=>Tv(e.default||e))),PT=vo(oT({loader:MT},rT)),IT=()=>t((()=>import("./pages-goods-list.d21d3494.js")),["./pages-goods-list.d21d3494.js","./mescroll-mixins.29ba6d02.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./mescroll-mixins-70c23850.css","./wxofficial.d88f1cc0.js","./index.6c9377fa.js","./index.0c0c395b.js","./index-13ff918b.css","./list-f9dd0bb6.css"],import.meta.url).then((e=>Tv(e.default||e))),OT=vo(oT({loader:IT},rT)),LT=()=>t((()=>import("./pages-goods-detail.c66eb259.js")),["./pages-goods-detail.c66eb259.js","./mp-html.1fbdb00d.js","./_plugin-vue_export-helper.1b428a4d.js","./mp-html-87831716.css","./uni-app.es.1cc81ac8.js","./wxofficial.d88f1cc0.js","./index.6c9377fa.js","./cart.039c75b6.js","./SpecType.95f7e710.js","./index.7ed10257.js","./index-0a868898.css","./Comment.23afee3b.js","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-popup-e05d76e9.css","./comment.d51edea7.js","./comment-81119d34.css","./index.a491b43c.js","./index-93a2b2a0.css","./Comment-7e9b1a19.css","./index.67a2889e.js","./color.813a9497.js","./index.f9eedeca.js","./index-565734b8.css","./detail-2d6bcb8c.css"],import.meta.url).then((e=>Tv(e.default||e))),$T=vo(oT({loader:LT},rT)),DT=()=>t((()=>import("./pages-comment-index.ecd4fef0.js")),["./pages-comment-index.ecd4fef0.js","./u-tabs.45b9467e.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./u-tabs-3dd7029d.css","./comment.d51edea7.js","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./comment-81119d34.css","./mescroll-mixins.29ba6d02.js","./mescroll-mixins-70c23850.css","./index.a491b43c.js","./index-93a2b2a0.css","./index-a7ad131c.css"],import.meta.url).then((e=>Tv(e.default||e))),RT=vo(oT({loader:DT},rT)),BT=()=>t((()=>import("./pages-my-coupon-index.c0008cf9.js")),["./pages-my-coupon-index.c0008cf9.js","./u-tabs.45b9467e.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./u-tabs-3dd7029d.css","./mescroll-mixins.29ba6d02.js","./mescroll-mixins-70c23850.css","./myCoupon.b423606c.js","./CouponType.ec81c796.js","./index-0186477e.css"],import.meta.url).then((e=>Tv(e.default||e))),NT=vo(oT({loader:BT},rT)),qT=()=>t((()=>import("./pages-address-index.a2c62a6a.js")),["./pages-address-index.a2c62a6a.js","./u-icon.abb5b4fa.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon-7703594e.css","./uni-app.es.1cc81ac8.js","./emitter.1571a5d9.js","./address.fee2943c.js","./index.31fdd6ce.js","./index-505570eb.css","./index-79f3c063.css"],import.meta.url).then((e=>Tv(e.default||e))),jT=vo(oT({loader:qT},rT)),zT=()=>t((()=>import("./pages-address-create.8de4a333.js")),["./pages-address-create.8de4a333.js","./u-form.4d62b4b6.js","./u-icon.abb5b4fa.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon-7703594e.css","./uni-app.es.1cc81ac8.js","./emitter.1571a5d9.js","./u-form-79329cbc.css","./select-region.aa33d0f2.js","./u-loading.d2db78f5.js","./u-loading-1c18ab8d.css","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./u-popup-e05d76e9.css","./select-region-d143df5a.css","./verify.3975cb19.js","./address.fee2943c.js","./create-1e135d86.css"],import.meta.url).then((e=>Tv(e.default||e))),VT=vo(oT({loader:zT},rT)),FT=()=>t((()=>import("./pages-address-update.192aafd0.js")),["./pages-address-update.192aafd0.js","./u-form.4d62b4b6.js","./u-icon.abb5b4fa.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon-7703594e.css","./uni-app.es.1cc81ac8.js","./emitter.1571a5d9.js","./u-form-79329cbc.css","./select-region.aa33d0f2.js","./u-loading.d2db78f5.js","./u-loading-1c18ab8d.css","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./u-popup-e05d76e9.css","./select-region-d143df5a.css","./verify.3975cb19.js","./address.fee2943c.js","./update-12377811.css"],import.meta.url).then((e=>Tv(e.default||e))),UT=vo(oT({loader:FT},rT)),WT=()=>t((()=>import("./pages-points-log.f982391e.js")),["./pages-points-log.f982391e.js","./mescroll-mixins.29ba6d02.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./mescroll-mixins-70c23850.css","./log-ddf66f5b.css"],import.meta.url).then((e=>Tv(e.default||e))),HT=vo(oT({loader:WT},rT)),YT=()=>t((()=>import("./pages-wallet-index.c43084e0.js")),["./pages-wallet-index.c43084e0.js","./user.9445f703.js","./_plugin-vue_export-helper.1b428a4d.js","./index-35275bf9.css"],import.meta.url).then((e=>Tv(e.default||e))),XT=vo(oT({loader:YT},rT)),GT=()=>t((()=>import("./pages-wallet-balance-log.6a109f4f.js")),["./pages-wallet-balance-log.6a109f4f.js","./mescroll-mixins.29ba6d02.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./mescroll-mixins-70c23850.css","./log-25177340.css"],import.meta.url).then((e=>Tv(e.default||e))),JT=vo(oT({loader:GT},rT)),QT=()=>t((()=>import("./pages-wallet-recharge-index.9b134c76.js")),["./pages-wallet-recharge-index.9b134c76.js","./u-modal.297b5974.js","./u-loading.d2db78f5.js","./_plugin-vue_export-helper.1b428a4d.js","./u-loading-1c18ab8d.css","./uni-app.es.1cc81ac8.js","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-popup-e05d76e9.css","./u-modal-d51465a3.css","./Method.b1c118f9.js","./wechat.b16166af.js","./index-958ed830.css"],import.meta.url).then((e=>Tv(e.default||e))),KT=vo(oT({loader:QT},rT)),ZT=()=>t((()=>import("./pages-wallet-recharge-order.cbbd0a84.js")),["./pages-wallet-recharge-order.cbbd0a84.js","./mescroll-mixins.29ba6d02.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./mescroll-mixins-70c23850.css","./order-e5fb6074.css"],import.meta.url).then((e=>Tv(e.default||e))),eS=vo(oT({loader:ZT},rT)),tS=()=>t((()=>import("./pages-checkout-index.e045904a.js")),["./pages-checkout-index.e045904a.js","./u-loading.d2db78f5.js","./_plugin-vue_export-helper.1b428a4d.js","./u-loading-1c18ab8d.css","./uni-app.es.1cc81ac8.js","./u-modal.297b5974.js","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-popup-e05d76e9.css","./u-modal-d51465a3.css","./verify.3975cb19.js","./CouponType.ec81c796.js","./OrderType.122295e0.js","./index-21f34205.css"],import.meta.url).then((e=>Tv(e.default||e))),nS=vo(oT({loader:tS},rT)),oS=()=>t((()=>import("./pages-checkout-cashier-index.bf36cec9.js")),["./pages-checkout-cashier-index.bf36cec9.js","./u-modal.297b5974.js","./u-loading.d2db78f5.js","./_plugin-vue_export-helper.1b428a4d.js","./u-loading-1c18ab8d.css","./uni-app.es.1cc81ac8.js","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-popup-e05d76e9.css","./u-modal-d51465a3.css","./wechat.b16166af.js","./Method.b1c118f9.js","./index.d95be262.js","./index-d0d63531.css","./OrderType.122295e0.js","./index-e5c04908.css"],import.meta.url).then((e=>Tv(e.default||e))),rS=vo(oT({loader:oS},rT)),iS=()=>t((()=>import("./pages-order-center.4488702d.js")),["./pages-order-center.4488702d.js","./user.9445f703.js","./_plugin-vue_export-helper.1b428a4d.js","./center-e004384a.css"],import.meta.url).then((e=>Tv(e.default||e))),aS=vo(oT({loader:iS},rT)),sS=()=>t((()=>import("./pages-order-index.43c5db45.js")),["./pages-order-index.43c5db45.js","./u-tabs.45b9467e.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./u-tabs-3dd7029d.css","./mescroll-mixins.29ba6d02.js","./mescroll-mixins-70c23850.css","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-popup-e05d76e9.css","./OrderType.122295e0.js","./Method.b1c118f9.js","./order.0fece54d.js","./index-e6df5344.css"],import.meta.url).then((e=>Tv(e.default||e))),lS=vo(oT({loader:sS},rT)),cS=()=>t((()=>import("./pages-order-detail.f065a950.js")),["./pages-order-detail.f065a950.js","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./_plugin-vue_export-helper.1b428a4d.js","./u-mask-2b838d8b.css","./uni-app.es.1cc81ac8.js","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-popup-e05d76e9.css","./OrderType.122295e0.js","./Method.b1c118f9.js","./order.0fece54d.js","./close.45fd8c32.js","./detail-f7309fa3.css"],import.meta.url).then((e=>Tv(e.default||e))),uS=vo(oT({loader:cS},rT)),dS=()=>t((()=>import("./pages-order-express-index.2e9201c0.js")),["./pages-order-express-index.2e9201c0.js","./u-tabs.45b9467e.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./u-tabs-3dd7029d.css","./order.0fece54d.js","./index-175cfbe6.css"],import.meta.url).then((e=>Tv(e.default||e))),pS=vo(oT({loader:dS},rT)),fS=()=>t((()=>import("./pages-order-extract-check.46c4bdb4.js")),["./pages-order-extract-check.46c4bdb4.js","./OrderType.122295e0.js","./close.45fd8c32.js","./_plugin-vue_export-helper.1b428a4d.js","./check-6939d0ca.css"],import.meta.url).then((e=>Tv(e.default||e))),hS=vo(oT({loader:fS},rT)),gS=()=>t((()=>import("./pages-order-comment-index.d77f4af6.js")),["./pages-order-comment-index.d77f4af6.js","./upload.8ab6f224.js","./_plugin-vue_export-helper.1b428a4d.js","./index-08ea5618.css"],import.meta.url).then((e=>Tv(e.default||e))),mS=vo(oT({loader:gS},rT)),vS=()=>t((()=>import("./pages-refund-index.8065f769.js")),["./pages-refund-index.8065f769.js","./u-tabs.45b9467e.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./u-tabs-3dd7029d.css","./mescroll-mixins.29ba6d02.js","./mescroll-mixins-70c23850.css","./refund.3e4b01a8.js","./index-463eeb10.css"],import.meta.url).then((e=>Tv(e.default||e))),yS=vo(oT({loader:vS},rT)),bS=()=>t((()=>import("./pages-refund-detail.996fc1e1.js")),["./pages-refund-detail.996fc1e1.js","./RefundType.86b0e4a8.js","./refund.3e4b01a8.js","./_plugin-vue_export-helper.1b428a4d.js","./detail-50b07617.css"],import.meta.url).then((e=>Tv(e.default||e))),_S=vo(oT({loader:bS},rT)),wS=()=>t((()=>import("./pages-refund-apply.169b08b0.js")),["./pages-refund-apply.169b08b0.js","./RefundType.86b0e4a8.js","./upload.8ab6f224.js","./refund.3e4b01a8.js","./_plugin-vue_export-helper.1b428a4d.js","./apply-ad28c4c1.css"],import.meta.url).then((e=>Tv(e.default||e))),xS=vo(oT({loader:wS},rT)),TS=()=>t((()=>import("./pages-shop-extract.3b89d35c.js")),["./pages-shop-extract.3b89d35c.js","./shop.a6a0481a.js","./index.31fdd6ce.js","./_plugin-vue_export-helper.1b428a4d.js","./index-505570eb.css","./extract-2e401507.css"],import.meta.url).then((e=>Tv(e.default||e))),SS=vo(oT({loader:TS},rT)),kS=()=>t((()=>import("./pages-shop-detail.4dcaf130.js")),["./pages-shop-detail.4dcaf130.js","./wxofficial.d88f1cc0.js","./shop.a6a0481a.js","./_plugin-vue_export-helper.1b428a4d.js","./detail-e19c586b.css"],import.meta.url).then((e=>Tv(e.default||e))),CS=vo(oT({loader:kS},rT)),ES=()=>t((()=>import("./pages-dealer-index.8a7f8898.js")),["./pages-dealer-index.8a7f8898.js","./index.a491b43c.js","./_plugin-vue_export-helper.1b428a4d.js","./index-93a2b2a0.css","./index.65c85c65.js","./index-7377769d.css"],import.meta.url).then((e=>Tv(e.default||e))),AS=vo(oT({loader:ES},rT)),MS=()=>t((()=>import("./pages-dealer-apply.c31bcec6.js")),["./pages-dealer-apply.c31bcec6.js","./u-modal.297b5974.js","./u-loading.d2db78f5.js","./_plugin-vue_export-helper.1b428a4d.js","./u-loading-1c18ab8d.css","./uni-app.es.1cc81ac8.js","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-popup-e05d76e9.css","./u-modal-d51465a3.css","./Setting.73704eae.js","./apply-0747ddf8.css"],import.meta.url).then((e=>Tv(e.default||e))),PS=vo(oT({loader:MS},rT)),IS=()=>t((()=>import("./pages-dealer-withdraw-apply.9239c4c2.js")),["./pages-dealer-withdraw-apply.9239c4c2.js","./index.65c85c65.js","./ApplyStatus.f48dd5d8.js","./Setting.73704eae.js","./_plugin-vue_export-helper.1b428a4d.js","./apply-01a52c76.css"],import.meta.url).then((e=>Tv(e.default||e))),OS=vo(oT({loader:IS},rT)),LS=()=>t((()=>import("./pages-dealer-withdraw-list.6088ba9a.js")),["./pages-dealer-withdraw-list.6088ba9a.js","./u-tabs.45b9467e.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./u-tabs-3dd7029d.css","./mescroll-mixins.29ba6d02.js","./mescroll-mixins-70c23850.css","./u-modal.297b5974.js","./u-loading.d2db78f5.js","./u-loading-1c18ab8d.css","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-popup-e05d76e9.css","./u-modal-d51465a3.css","./ApplyStatus.f48dd5d8.js","./Setting.73704eae.js","./list-255a2e69.css"],import.meta.url).then((e=>Tv(e.default||e))),$S=vo(oT({loader:LS},rT)),DS=()=>t((()=>import("./pages-dealer-poster.bee2bf8d.js")),["./pages-dealer-poster.bee2bf8d.js","./Setting.73704eae.js","./_plugin-vue_export-helper.1b428a4d.js","./poster-744d32bb.css"],import.meta.url).then((e=>Tv(e.default||e))),RS=vo(oT({loader:DS},rT)),BS=()=>t((()=>import("./pages-dealer-order.93a6428f.js")),["./pages-dealer-order.93a6428f.js","./u-tabs.45b9467e.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./u-tabs-3dd7029d.css","./mescroll-mixins.29ba6d02.js","./mescroll-mixins-70c23850.css","./index.a491b43c.js","./index-93a2b2a0.css","./Setting.73704eae.js","./order-84d43bb0.css"],import.meta.url).then((e=>Tv(e.default||e))),NS=vo(oT({loader:BS},rT)),qS=()=>t((()=>import("./pages-dealer-team.6340e9f4.js")),["./pages-dealer-team.6340e9f4.js","./u-tabs.45b9467e.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./u-tabs-3dd7029d.css","./mescroll-mixins.29ba6d02.js","./mescroll-mixins-70c23850.css","./index.a491b43c.js","./index-93a2b2a0.css","./index.65c85c65.js","./Setting.73704eae.js","./team-374dbcdf.css"],import.meta.url).then((e=>Tv(e.default||e))),jS=vo(oT({loader:qS},rT)),zS=()=>t((()=>import("./pages-bargain-index.c1f37b0c.js")),["./pages-bargain-index.c1f37b0c.js","./mescroll-mixins.29ba6d02.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./mescroll-mixins-70c23850.css","./wxofficial.d88f1cc0.js","./index.a491b43c.js","./index-93a2b2a0.css","./index.d95be262.js","./index-d0d63531.css","./task.ea1c2741.js","./index-c35b1129.css"],import.meta.url).then((e=>Tv(e.default||e))),VS=vo(oT({loader:zS},rT)),FS=()=>t((()=>import("./pages-bargain-goods-index.d9e2ec76.js")),["./pages-bargain-goods-index.d9e2ec76.js","./mp-html.1fbdb00d.js","./_plugin-vue_export-helper.1b428a4d.js","./mp-html-87831716.css","./uni-app.es.1cc81ac8.js","./u-modal.297b5974.js","./u-loading.d2db78f5.js","./u-loading-1c18ab8d.css","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-popup-e05d76e9.css","./u-modal-d51465a3.css","./wxofficial.d88f1cc0.js","./Comment.23afee3b.js","./comment.d51edea7.js","./comment-81119d34.css","./index.a491b43c.js","./index-93a2b2a0.css","./Comment-7e9b1a19.css","./index.67a2889e.js","./color.813a9497.js","./task.ea1c2741.js","./index.f9eedeca.js","./index-565734b8.css","./index.d95be262.js","./index-d0d63531.css","./index.6c9377fa.js","./cart.039c75b6.js","./index-c87a4400.css"],import.meta.url).then((e=>Tv(e.default||e))),US=vo(oT({loader:FS},rT)),WS=()=>t((()=>import("./pages-bargain-task.78847f51.js")),["./pages-bargain-task.78847f51.js","./u-modal.297b5974.js","./u-loading.d2db78f5.js","./_plugin-vue_export-helper.1b428a4d.js","./u-loading-1c18ab8d.css","./uni-app.es.1cc81ac8.js","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-popup-e05d76e9.css","./u-modal-d51465a3.css","./wxofficial.d88f1cc0.js","./index.a491b43c.js","./index-93a2b2a0.css","./index.d95be262.js","./index-d0d63531.css","./index.6c9377fa.js","./task.ea1c2741.js","./task-21526cf9.css"],import.meta.url).then((e=>Tv(e.default||e))),HS=vo(oT({loader:WS},rT)),YS=()=>t((()=>import("./pages-sharp-index.ad749fb7.js")),["./pages-sharp-index.ad749fb7.js","./mescroll-mixins.29ba6d02.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./mescroll-mixins-70c23850.css","./wxofficial.d88f1cc0.js","./index.d95be262.js","./index-d0d63531.css","./color.813a9497.js","./GoodsStatus.a102fff1.js","./goods.d5789f1b.js","./index-ee8c66cb.css"],import.meta.url).then((e=>Tv(e.default||e))),XS=vo(oT({loader:YS},rT)),GS=()=>t((()=>import("./pages-sharp-goods-index.3315fa03.js")),["./pages-sharp-goods-index.3315fa03.js","./mp-html.1fbdb00d.js","./_plugin-vue_export-helper.1b428a4d.js","./mp-html-87831716.css","./uni-app.es.1cc81ac8.js","./wxofficial.d88f1cc0.js","./Comment.23afee3b.js","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-popup-e05d76e9.css","./comment.d51edea7.js","./comment-81119d34.css","./index.a491b43c.js","./index-93a2b2a0.css","./Comment-7e9b1a19.css","./index.67a2889e.js","./color.813a9497.js","./index.f9eedeca.js","./index-565734b8.css","./index.d95be262.js","./index-d0d63531.css","./goods.d5789f1b.js","./cart.039c75b6.js","./GoodsStatus.a102fff1.js","./index-3fd0cdb3.css"],import.meta.url).then((e=>Tv(e.default||e))),JS=vo(oT({loader:GS},rT)),QS=()=>t((()=>import("./pages-groupon-index.2a909e5a.js")),["./pages-groupon-index.2a909e5a.js","./u-tag.af176b7f.js","./u-icon.abb5b4fa.js","./_plugin-vue_export-helper.1b428a4d.js","./u-icon-7703594e.css","./uni-app.es.1cc81ac8.js","./u-tag-9d25ed65.css","./mescroll-mixins.29ba6d02.js","./mescroll-mixins-70c23850.css","./wxofficial.d88f1cc0.js","./color.813a9497.js","./ActiveStatus.55fe1037.js","./task.e46a0490.js","./goods.0efae71a.js","./Setting.3a6951be.js","./index-4599e764.css"],import.meta.url).then((e=>Tv(e.default||e))),KS=vo(oT({loader:QS},rT)),ZS=()=>t((()=>import("./pages-groupon-goods-index.1f77855a.js")),["./pages-groupon-goods-index.1f77855a.js","./mp-html.1fbdb00d.js","./_plugin-vue_export-helper.1b428a4d.js","./mp-html-87831716.css","./uni-app.es.1cc81ac8.js","./u-modal.297b5974.js","./u-loading.d2db78f5.js","./u-loading-1c18ab8d.css","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-popup-e05d76e9.css","./u-modal-d51465a3.css","./wxofficial.d88f1cc0.js","./Comment.23afee3b.js","./comment.d51edea7.js","./comment-81119d34.css","./index.a491b43c.js","./index-93a2b2a0.css","./Comment-7e9b1a19.css","./index.67a2889e.js","./SkuPopup.effa8820.js","./color.813a9497.js","./ActiveStatus.55fe1037.js","./SkuPopup-c2d43cb5.css","./index.d95be262.js","./index-d0d63531.css","./task.e46a0490.js","./goods.0efae71a.js","./cart.039c75b6.js","./index-cb446797.css"],import.meta.url).then((e=>Tv(e.default||e))),ek=vo(oT({loader:ZS},rT)),tk=()=>t((()=>import("./pages-groupon-task-index.7bc02854.js")),["./pages-groupon-task-index.7bc02854.js","./u-modal.297b5974.js","./u-loading.d2db78f5.js","./_plugin-vue_export-helper.1b428a4d.js","./u-loading-1c18ab8d.css","./uni-app.es.1cc81ac8.js","./u-popup.6604e8b6.js","./u-mask.6931c55d.js","./u-mask-2b838d8b.css","./u-icon.abb5b4fa.js","./u-icon-7703594e.css","./u-popup-e05d76e9.css","./u-modal-d51465a3.css","./wxofficial.d88f1cc0.js","./index.a491b43c.js","./index-93a2b2a0.css","./index.d95be262.js","./index-d0d63531.css","./index.7ed10257.js","./index.6c9377fa.js","./index-0a868898.css","./SkuPopup.effa8820.js","./color.813a9497.js","./ActiveStatus.55fe1037.js","./SkuPopup-c2d43cb5.css","./task.e46a0490.js","./Setting.3a6951be.js","./index-50409dce.css"],import.meta.url).then((e=>Tv(e.default||e))),nk=vo(oT({loader:tk},rT)),ok=()=>t((()=>import("./pages-live-index.59743604.js")),["./pages-live-index.59743604.js","./mescroll-mixins.29ba6d02.js","./_plugin-vue_export-helper.1b428a4d.js","./uni-app.es.1cc81ac8.js","./mescroll-mixins-70c23850.css","./wxofficial.d88f1cc0.js","./index-0b0af47f.css"],import.meta.url).then((e=>Tv(e.default||e))),rk=vo(oT({loader:ok},rT));function ik(e,t){return Fr(),Xr(Zx,null,{page:Un((()=>[ti(e,oT({},t,{ref:"page"}),null,512)])),_:1})}function ak(){return"undefined"!=typeof navigator&&"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}}window.__uniRoutes=[{path:"/",alias:"/pages/index/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(aT,t)}},loader:iT,meta:{isQuit:!0,isEntry:!0,isTabBar:!0,tabBarIndex:0,enablePullDownRefresh:!0,navigationBar:{type:"default"},isNVue:!1}},{path:"/pages/category/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(lT,t)}},loader:sT,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:1,navigationBar:{titleText:"全部分类",type:"default"},isNVue:!1}},{path:"/pages/cart/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(uT,t)}},loader:cT,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:2,navigationBar:{titleText:"购物车",type:"default"},isNVue:!1}},{path:"/pages/user/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(pT,t)}},loader:dT,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:3,enablePullDownRefresh:!0,navigationBar:{titleText:"个人中心",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/custom/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(hT,t)}},loader:fT,meta:{enablePullDownRefresh:!0,navigationBar:{type:"default"},isNVue:!1}},{path:"/pages/search/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(mT,t)}},loader:gT,meta:{navigationBar:{titleText:"商品搜索",type:"default"},isNVue:!1}},{path:"/pages/login/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(yT,t)}},loader:vT,meta:{navigationBar:{titleText:"会员登录",type:"default"},isNVue:!1}},{path:"/pages/user/bind/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(_T,t)}},loader:bT,meta:{navigationBar:{titleText:"绑定手机",type:"default"},isNVue:!1}},{path:"/pages/user/personal/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(xT,t)}},loader:wT,meta:{navigationBar:{titleText:"个人信息",type:"default"},isNVue:!1}},{path:"/pages/article/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(ST,t)}},loader:TT,meta:{navigationBar:{titleText:"资讯列表",type:"default"},isNVue:!1}},{path:"/pages/article/detail",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(CT,t)}},loader:kT,meta:{navigationBar:{titleText:"资讯详情",type:"default"},isNVue:!1}},{path:"/pages/help/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(AT,t)}},loader:ET,meta:{navigationBar:{titleText:"帮助中心",type:"default"},isNVue:!1}},{path:"/pages/coupon/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(PT,t)}},loader:MT,meta:{navigationBar:{titleText:"领券中心",type:"default"},isNVue:!1}},{path:"/pages/goods/list",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(OT,t)}},loader:IT,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"商品列表",type:"default"},isNVue:!1}},{path:"/pages/goods/detail",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik($T,t)}},loader:LT,meta:{navigationBar:{titleText:"商品详情页",type:"default"},isNVue:!1}},{path:"/pages/comment/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(RT,t)}},loader:DT,meta:{navigationBar:{titleText:"商品评价页",type:"default"},isNVue:!1}},{path:"/pages/my-coupon/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(NT,t)}},loader:BT,meta:{navigationBar:{titleText:"我的优惠券",type:"default"},isNVue:!1}},{path:"/pages/address/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(jT,t)}},loader:qT,meta:{navigationBar:{titleText:"收货地址",type:"default"},isNVue:!1}},{path:"/pages/address/create",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(VT,t)}},loader:zT,meta:{navigationBar:{titleText:"新增收货地址",type:"default"},isNVue:!1}},{path:"/pages/address/update",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(UT,t)}},loader:FT,meta:{navigationBar:{titleText:"编辑收货地址",type:"default"},isNVue:!1}},{path:"/pages/points/log",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(HT,t)}},loader:WT,meta:{navigationBar:{titleText:"账单明细",type:"default"},isNVue:!1}},{path:"/pages/wallet/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(XT,t)}},loader:YT,meta:{navigationBar:{titleText:"我的钱包",type:"default"},isNVue:!1}},{path:"/pages/wallet/balance/log",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(JT,t)}},loader:GT,meta:{navigationBar:{titleText:"账单详情",type:"default"},isNVue:!1}},{path:"/pages/wallet/recharge/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(KT,t)}},loader:QT,meta:{navigationBar:{titleText:"充值中心",type:"default"},isNVue:!1}},{path:"/pages/wallet/recharge/order",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(eS,t)}},loader:ZT,meta:{navigationBar:{titleText:"充值记录",type:"default"},isNVue:!1}},{path:"/pages/checkout/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(nS,t)}},loader:tS,meta:{navigationBar:{titleText:"订单结算台",type:"default"},isNVue:!1}},{path:"/pages/checkout/cashier/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(rS,t)}},loader:oS,meta:{navigationBar:{titleText:"支付订单",type:"default"},isNVue:!1}},{path:"/pages/order/center",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(aS,t)}},loader:iS,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"订单中心",type:"default"},isNVue:!1}},{path:"/pages/order/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(lS,t)}},loader:sS,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"我的订单",type:"default"},isNVue:!1}},{path:"/pages/order/detail",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(uS,t)}},loader:cS,meta:{navigationBar:{backgroundColor:"#e8c269",titleText:"订单详情",type:"default",titleColor:"#ffffff"},isNVue:!1}},{path:"/pages/order/express/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(pS,t)}},loader:dS,meta:{navigationBar:{titleText:"物流跟踪",type:"default"},isNVue:!1}},{path:"/pages/order/extract/check",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(hS,t)}},loader:fS,meta:{navigationBar:{titleText:"订单自提核销",type:"default"},isNVue:!1}},{path:"/pages/order/comment/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(mS,t)}},loader:gS,meta:{navigationBar:{titleText:"订单评价",type:"default"},isNVue:!1}},{path:"/pages/refund/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(yS,t)}},loader:vS,meta:{navigationBar:{titleText:"退换/售后",type:"default"},isNVue:!1}},{path:"/pages/refund/detail",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(_S,t)}},loader:bS,meta:{navigationBar:{titleText:"售后详情",type:"default"},isNVue:!1}},{path:"/pages/refund/apply",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(xS,t)}},loader:wS,meta:{navigationBar:{titleText:"申请售后",type:"default"},isNVue:!1}},{path:"/pages/shop/extract",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(SS,t)}},loader:TS,meta:{navigationBar:{titleText:"选择自提门店",type:"default"},isNVue:!1}},{path:"/pages/shop/detail",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(CS,t)}},loader:kS,meta:{navigationBar:{titleText:"门店详情",type:"default"},isNVue:!1}},{path:"/pages/dealer/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(AS,t)}},loader:ES,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/apply",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(PS,t)}},loader:MS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/withdraw/apply",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(OS,t)}},loader:IS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/withdraw/list",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik($S,t)}},loader:LS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/poster",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(RS,t)}},loader:DS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/order",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(NS,t)}},loader:BS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/dealer/team",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(jS,t)}},loader:qS,meta:{navigationBar:{titleText:"",type:"default"},isNVue:!1}},{path:"/pages/bargain/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(VS,t)}},loader:zS,meta:{navigationBar:{titleText:"砍价会场",type:"default"},isNVue:!1}},{path:"/pages/bargain/goods/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(US,t)}},loader:FS,meta:{navigationBar:{titleText:"砍价商品",type:"default"},isNVue:!1}},{path:"/pages/bargain/task",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(HS,t)}},loader:WS,meta:{navigationBar:{titleText:"砍价任务",type:"default"},isNVue:!1}},{path:"/pages/sharp/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(XS,t)}},loader:YS,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"整点秒杀会场",type:"default"},isNVue:!1}},{path:"/pages/sharp/goods/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(JS,t)}},loader:GS,meta:{navigationBar:{titleText:"秒杀商品详情",type:"default"},isNVue:!1}},{path:"/pages/groupon/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(KS,t)}},loader:QS,meta:{navigationBar:{titleText:"拼团活动",type:"default"},isNVue:!1}},{path:"/pages/groupon/goods/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(ek,t)}},loader:ZS,meta:{navigationBar:{titleText:"拼团商品",type:"default"},isNVue:!1}},{path:"/pages/groupon/task/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(nk,t)}},loader:tk,meta:{navigationBar:{backgroundColor:"#FF5644",titleText:"拼团详情",type:"default",titleColor:"#ffffff"},isNVue:!1}},{path:"/pages/live/index",component:{setup(){const e=bv(),t=e&&e.$route&&e.$route.query||{};return()=>ik(rk,t)}},loader:ok,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"直播列表",type:"default"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const sk="function"==typeof Proxy;class lk{constructor(e,t){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=e,this.hook=t;const n={};if(e.settings)for(const i in e.settings){const t=e.settings[i];n[i]=t.defaultValue}const o=`__vue-devtools-plugin-settings__${e.id}`;let r={...n};try{const e=localStorage.getItem(o),t=JSON.parse(e);Object.assign(r,t)}catch(EC){}this.fallbacks={getSettings:()=>r,setSettings(e){try{localStorage.setItem(o,JSON.stringify(e))}catch(EC){}r=e}},t.on("plugin:settings:set",((e,t)=>{e===this.plugin.id&&this.fallbacks.setSettings(t)})),this.proxiedOn=new Proxy({},{get:(e,t)=>this.target?this.target.on[t]:(...e)=>{this.onQueue.push({method:t,args:e})}}),this.proxiedTarget=new Proxy({},{get:(e,t)=>this.target?this.target[t]:"on"===t?this.proxiedOn:Object.keys(this.fallbacks).includes(t)?(...e)=>(this.targetQueue.push({method:t,args:e,resolve:()=>{}}),this.fallbacks[t](...e)):(...e)=>new Promise((n=>{this.targetQueue.push({method:t,args:e,resolve:n})}))})}async setRealTarget(e){this.target=e;for(const t of this.onQueue)this.target.on[t.method](...t.args);for(const t of this.targetQueue)t.resolve(await this.target[t.method](...t.args))}}function ck(e,t){const n=ak(),o=ak().__VUE_DEVTOOLS_GLOBAL_HOOK__,r=sk&&e.enableEarlyProxy;if(!o||!n.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__&&r){const i=r?new lk(e,o):null;(n.__VUE_DEVTOOLS_PLUGINS__=n.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:e,setupFn:t,proxy:i}),i&&t(i.proxiedTarget)}else o.emit("devtools-plugin:setup",e,t)}
/*!
 * vuex v4.1.0
 * (c) 2022 Evan You
 * @license MIT
 */function uk(e,t){Object.keys(e).forEach((function(n){return t(e[n],n)}))}function dk(e,t,n){return t.indexOf(e)<0&&(n&&n.prepend?t.unshift(e):t.push(e)),function(){var n=t.indexOf(e);n>-1&&t.splice(n,1)}}function pk(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var n=e.state;hk(e,n,[],e._modules.root,!0),fk(e,n,t)}function fk(e,t,n){var o=e._state,r=e._scope;e.getters={},e._makeLocalGettersCache=Object.create(null);var i=e._wrappedGetters,a={},s={},l=ze(!0);l.run((function(){uk(i,(function(t,n){a[n]=function(e,t){return function(){return e(t)}}(t,e),s[n]=xi((function(){return a[n]()})),Object.defineProperty(e.getters,n,{get:function(){return s[n].value},enumerable:!0})}))})),e._state=Yt({data:t}),e._scope=l,e.strict&&function(e){eo((function(){return e._state.data}),(function(){}),{deep:!0,flush:"sync"})}(e),o&&n&&e._withCommit((function(){o.data=null})),r&&r.stop()}function hk(e,t,n,o,r){var i=!n.length,a=e._modules.getNamespace(n);if(o.namespaced&&(e._modulesNamespaceMap[a],e._modulesNamespaceMap[a]=o),!i&&!r){var s=mk(t,n.slice(0,-1)),l=n[n.length-1];e._withCommit((function(){s[l]=o.state}))}var c=o.context=function(e,t,n){var o=""===t,r={dispatch:o?e.dispatch:function(n,o,r){var i=vk(n,o,r),a=i.payload,s=i.options,l=i.type;return s&&s.root||(l=t+l),e.dispatch(l,a)},commit:o?e.commit:function(n,o,r){var i=vk(n,o,r),a=i.payload,s=i.options,l=i.type;s&&s.root||(l=t+l),e.commit(l,a,s)}};return Object.defineProperties(r,{getters:{get:o?function(){return e.getters}:function(){return gk(e,t)}},state:{get:function(){return mk(e.state,n)}}}),r}(e,a,n);o.forEachMutation((function(t,n){!function(e,t,n,o){(e._mutations[t]||(e._mutations[t]=[])).push((function(t){n.call(e,o.state,t)}))}(e,a+n,t,c)})),o.forEachAction((function(t,n){var o=t.root?n:a+n,r=t.handler||t;!function(e,t,n,o){(e._actions[t]||(e._actions[t]=[])).push((function(t){var r,i=n.call(e,{dispatch:o.dispatch,commit:o.commit,getters:o.getters,state:o.state,rootGetters:e.getters,rootState:e.state},t);return(r=i)&&"function"==typeof r.then||(i=Promise.resolve(i)),e._devtoolHook?i.catch((function(t){throw e._devtoolHook.emit("vuex:error",t),t})):i}))}(e,o,r,c)})),o.forEachGetter((function(t,n){!function(e,t,n,o){if(e._wrappedGetters[t])return;e._wrappedGetters[t]=function(e){return n(o.state,o.getters,e.state,e.getters)}}(e,a+n,t,c)})),o.forEachChild((function(o,i){hk(e,t,n.concat(i),o,r)}))}function gk(e,t){if(!e._makeLocalGettersCache[t]){var n={},o=t.length;Object.keys(e.getters).forEach((function(r){if(r.slice(0,o)===t){var i=r.slice(o);Object.defineProperty(n,i,{get:function(){return e.getters[r]},enumerable:!0})}})),e._makeLocalGettersCache[t]=n}return e._makeLocalGettersCache[t]}function mk(e,t){return t.reduce((function(e,t){return e[t]}),e)}function vk(e,t,n){var o;return null!==(o=e)&&"object"==typeof o&&e.type&&(n=t,t=e,e=e.type),{type:e,payload:t,options:n}}var yk=0;function bk(e,t){ck({id:"org.vuejs.vuex",app:e,label:"Vuex",homepage:"https://next.vuex.vuejs.org/",logo:"https://vuejs.org/images/icons/favicon-96x96.png",packageName:"vuex",componentStateTypes:["vuex bindings"]},(function(n){n.addTimelineLayer({id:"vuex:mutations",label:"Vuex Mutations",color:_k}),n.addTimelineLayer({id:"vuex:actions",label:"Vuex Actions",color:_k}),n.addInspector({id:"vuex",label:"Vuex",icon:"storage",treeFilterPlaceholder:"Filter stores..."}),n.on.getInspectorTree((function(n){if(n.app===e&&"vuex"===n.inspectorId)if(n.filter){var o=[];Sk(o,t._modules.root,n.filter,""),n.rootNodes=o}else n.rootNodes=[Tk(t._modules.root,"")]})),n.on.getInspectorState((function(n){if(n.app===e&&"vuex"===n.inspectorId){var o=n.nodeId;gk(t,o),n.state=function(e,t,n){t="root"===n?t:t[n];var o=Object.keys(t),r={state:Object.keys(e.state).map((function(t){return{key:t,editable:!0,value:e.state[t]}}))};if(o.length){var i=function(e){var t={};return Object.keys(e).forEach((function(n){var o=n.split("/");if(o.length>1){var r=t,i=o.pop();o.forEach((function(e){r[e]||(r[e]={_custom:{value:{},display:e,tooltip:"Module",abstract:!0}}),r=r[e]._custom.value})),r[i]=kk((function(){return e[n]}))}else t[n]=kk((function(){return e[n]}))})),t}(t);r.getters=Object.keys(i).map((function(e){return{key:e.endsWith("/")?xk(e):e,editable:!1,value:kk((function(){return i[e]}))}}))}return r}((r=t._modules,(a=(i=o).split("/").filter((function(e){return e}))).reduce((function(e,t,n){var o=e[t];if(!o)throw new Error('Missing module "'+t+'" for path "'+i+'".');return n===a.length-1?o:o._children}),"root"===i?r:r.root._children)),"root"===o?t.getters:t._makeLocalGettersCache,o)}var r,i,a})),n.on.editInspectorState((function(n){if(n.app===e&&"vuex"===n.inspectorId){var o=n.nodeId,r=n.path;"root"!==o&&(r=o.split("/").filter(Boolean).concat(r)),t._withCommit((function(){n.set(t._state.data,r,n.state.value)}))}})),t.subscribe((function(e,t){var o={};e.payload&&(o.payload=e.payload),o.state=t,n.notifyComponentUpdate(),n.sendInspectorTree("vuex"),n.sendInspectorState("vuex"),n.addTimelineEvent({layerId:"vuex:mutations",event:{time:Date.now(),title:e.type,data:o}})})),t.subscribeAction({before:function(e,t){var o={};e.payload&&(o.payload=e.payload),e._id=yk++,e._time=Date.now(),o.state=t,n.addTimelineEvent({layerId:"vuex:actions",event:{time:e._time,title:e.type,groupId:e._id,subtitle:"start",data:o}})},after:function(e,t){var o={},r=Date.now()-e._time;o.duration={_custom:{type:"duration",display:r+"ms",tooltip:"Action duration",value:r}},e.payload&&(o.payload=e.payload),o.state=t,n.addTimelineEvent({layerId:"vuex:actions",event:{time:Date.now(),title:e.type,groupId:e._id,subtitle:"end",data:o}})}})}))}var _k=8702998,wk={label:"namespaced",textColor:16777215,backgroundColor:6710886};function xk(e){return e&&"root"!==e?e.split("/").slice(-2,-1)[0]:"Root"}function Tk(e,t){return{id:t||"root",label:xk(t),tags:e.namespaced?[wk]:[],children:Object.keys(e._children).map((function(n){return Tk(e._children[n],t+n+"/")}))}}function Sk(e,t,n,o){o.includes(n)&&e.push({id:o||"root",label:o.endsWith("/")?o.slice(0,o.length-1):o||"Root",tags:t.namespaced?[wk]:[]}),Object.keys(t._children).forEach((function(r){Sk(e,t._children[r],n,o+r+"/")}))}function kk(e){try{return e()}catch(EC){return EC}}var Ck=function(e,t){this.runtime=t,this._children=Object.create(null),this._rawModule=e;var n=e.state;this.state=("function"==typeof n?n():n)||{}},Ek={namespaced:{configurable:!0}};Ek.namespaced.get=function(){return!!this._rawModule.namespaced},Ck.prototype.addChild=function(e,t){this._children[e]=t},Ck.prototype.removeChild=function(e){delete this._children[e]},Ck.prototype.getChild=function(e){return this._children[e]},Ck.prototype.hasChild=function(e){return e in this._children},Ck.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},Ck.prototype.forEachChild=function(e){uk(this._children,e)},Ck.prototype.forEachGetter=function(e){this._rawModule.getters&&uk(this._rawModule.getters,e)},Ck.prototype.forEachAction=function(e){this._rawModule.actions&&uk(this._rawModule.actions,e)},Ck.prototype.forEachMutation=function(e){this._rawModule.mutations&&uk(this._rawModule.mutations,e)},Object.defineProperties(Ck.prototype,Ek);var Ak=function(e){this.register([],e,!1)};function Mk(e,t,n){if(t.update(n),n.modules)for(var o in n.modules){if(!t.getChild(o))return;Mk(e.concat(o),t.getChild(o),n.modules[o])}}Ak.prototype.get=function(e){return e.reduce((function(e,t){return e.getChild(t)}),this.root)},Ak.prototype.getNamespace=function(e){var t=this.root;return e.reduce((function(e,n){return e+((t=t.getChild(n)).namespaced?n+"/":"")}),"")},Ak.prototype.update=function(e){Mk([],this.root,e)},Ak.prototype.register=function(e,t,n){var o=this;void 0===n&&(n=!0);var r=new Ck(t,n);0===e.length?this.root=r:this.get(e.slice(0,-1)).addChild(e[e.length-1],r);t.modules&&uk(t.modules,(function(t,r){o.register(e.concat(r),t,n)}))},Ak.prototype.unregister=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1],o=t.getChild(n);o&&o.runtime&&t.removeChild(n)},Ak.prototype.isRegistered=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1];return!!t&&t.hasChild(n)};var Pk=function(e){var t=this;void 0===e&&(e={});var n=e.plugins;void 0===n&&(n=[]);var o=e.strict;void 0===o&&(o=!1);var r=e.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new Ak(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._scope=null,this._devtools=r;var i=this,a=this.dispatch,s=this.commit;this.dispatch=function(e,t){return a.call(i,e,t)},this.commit=function(e,t,n){return s.call(i,e,t,n)},this.strict=o;var l=this._modules.root.state;hk(this,l,[],this._modules.root),fk(this,l),n.forEach((function(e){return e(t)}))},Ik={state:{configurable:!0}};Pk.prototype.install=function(e,t){e.provide(t||"store",this),e.config.globalProperties.$store=this,void 0!==this._devtools&&this._devtools&&bk(e,this)},Ik.state.get=function(){return this._state.data},Ik.state.set=function(e){},Pk.prototype.commit=function(e,t,n){var o=this,r=vk(e,t,n),i=r.type,a=r.payload,s={type:i,payload:a},l=this._mutations[i];l&&(this._withCommit((function(){l.forEach((function(e){e(a)}))})),this._subscribers.slice().forEach((function(e){return e(s,o.state)})))},Pk.prototype.dispatch=function(e,t){var n=this,o=vk(e,t),r=o.type,i=o.payload,a={type:r,payload:i},s=this._actions[r];if(s){try{this._actionSubscribers.slice().filter((function(e){return e.before})).forEach((function(e){return e.before(a,n.state)}))}catch(EC){}var l=s.length>1?Promise.all(s.map((function(e){return e(i)}))):s[0](i);return new Promise((function(e,t){l.then((function(t){try{n._actionSubscribers.filter((function(e){return e.after})).forEach((function(e){return e.after(a,n.state)}))}catch(EC){}e(t)}),(function(e){try{n._actionSubscribers.filter((function(e){return e.error})).forEach((function(t){return t.error(a,n.state,e)}))}catch(EC){}t(e)}))}))}},Pk.prototype.subscribe=function(e,t){return dk(e,this._subscribers,t)},Pk.prototype.subscribeAction=function(e,t){return dk("function"==typeof e?{before:e}:e,this._actionSubscribers,t)},Pk.prototype.watch=function(e,t,n){var o=this;return eo((function(){return e(o.state,o.getters)}),t,Object.assign({},n))},Pk.prototype.replaceState=function(e){var t=this;this._withCommit((function(){t._state.data=e}))},Pk.prototype.registerModule=function(e,t,n){void 0===n&&(n={}),"string"==typeof e&&(e=[e]),this._modules.register(e,t),hk(this,this.state,e,this._modules.get(e),n.preserveState),fk(this,this.state)},Pk.prototype.unregisterModule=function(e){var t=this;"string"==typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit((function(){delete mk(t.state,e.slice(0,-1))[e[e.length-1]]})),pk(this)},Pk.prototype.hasModule=function(e){return"string"==typeof e&&(e=[e]),this._modules.isRegistered(e)},Pk.prototype.hotUpdate=function(e){this._modules.update(e),pk(this,!0)},Pk.prototype._withCommit=function(e){var t=this._committing;this._committing=!0,e(),this._committing=t},Object.defineProperties(Pk.prototype,Ik);const Ok={set(e,t,n){uni.setStorageSync(e,t);const o=parseInt(n);if(o>0){let t=Date.parse(new Date);t=t/1e3+o,uni.setStorageSync(e+"_expiry",t+"")}else uni.removeStorageSync(e+"_expiry")},get(e,t){const n=parseInt(uni.getStorageSync(e+"_expiry"));if(n&&parseInt(n)<Date.parse(new Date)/1e3)return t||!1;const o=uni.getStorageSync(e);return o||(null!=t&&""!=t||(t=!1),t)},remove(e){uni.removeStorageSync(e),uni.removeStorageSync(e+"_expiry")},clear(){uni.clearStorageSync()}},Lk={state:{storeId:null,platform:"",refereeId:null,modules:[]},mutations:{SET_STORE_ID:(e,t)=>{e.storeId=t},SET_PLATFORM:(e,t)=>{e.platform=t},SET_REFEREE_ID:(e,t)=>{e.refereeId=t},SET_MODULES:(e,t)=>{e.modules=t}},actions:{setRefereeId({commit:e},t){const n=this,o=parseInt(t);return new Promise(((t,r)=>{o>0&&n.getters.userId!=o&&(Ok.set("refereeId",o),e("SET_REFEREE_ID",o),t())}))},SetModules:({commit:e},t)=>new Promise(((n,o)=>{Ok.set("modules",t),e("SET_MODULES",t),n()}))}},$k=(e,t)=>{let n=/^(http|https):\/\//.test(t.url),o=Object.assign({timeout:e.timeout},e.config,t);return"FILE"==t.method?o.url=n?t.url:e.fileUrl+t.url:o.url=n?t.url:e.baseUrl+t.url,t.header?o.header=Object.assign({},e.header,t.header):o.header=Object.assign({},e.header),o};var Dk={qiniuRegion:"",qiniuImageURLPrefix:"",qiniuUploadToken:"",qiniuUploadTokenURL:"",qiniuUploadTokenFunction:null,qiniuShouldUseQiniuFileName:!1};function Rk(e,t,n,o,r,i){var a;if(null!=e)if(o&&function(e){e.region?Dk.qiniuRegion=e.region:console.error("qiniu uploader need your bucket region"),e.uptoken?Dk.qiniuUploadToken=e.uptoken:e.uptokenURL?Dk.qiniuUploadTokenURL=e.uptokenURL:e.uptokenFunc&&(Dk.qiniuUploadTokenFunction=e.uptokenFunc),e.domain&&(Dk.qiniuImageURLPrefix=e.domain),Dk.qiniuShouldUseQiniuFileName=e.shouldUseQiniuFileName}(o),Dk.qiniuUploadToken)Bk(e,t,n,o,r,i);else if(Dk.qiniuUploadTokenURL)a=function(){Bk(e,t,n,o,r,i)},wx.request({url:Dk.qiniuUploadTokenURL,success:function(e){var t=e.data.uptoken;t&&t.length>0?(Dk.qiniuUploadToken=t,a&&a()):console.error("qiniuUploader cannot get your token, please check the uptokenURL or server")},fail:function(e){console.error("qiniu UploadToken is null, please check the init config or networking: "+e)}});else{if(!Dk.qiniuUploadTokenFunction)return void console.error("qiniu uploader need one of [uptoken, uptokenURL, uptokenFunc]");if(Dk.qiniuUploadToken=Dk.qiniuUploadTokenFunction(),null==Dk.qiniuUploadToken&&Dk.qiniuUploadToken.length>0)return void console.error("qiniu UploadTokenFunction result is null, please check the return value");Bk(e,t,n,o,r,i)}else console.error("qiniu uploader need filePath to upload")}function Bk(e,t,n,o,r,i){if(null==Dk.qiniuUploadToken&&Dk.qiniuUploadToken.length>0)console.error("qiniu UploadToken is null, please check the init config or networking");else{var a=function(e){var t=null;switch(e){case"ECN":t="https://up.qbox.me";break;case"NCN":t="https://up-z1.qbox.me";break;case"SCN":t="https://up-z2.qbox.me";break;case"NA":t="https://up-na0.qbox.me";break;case"ASG":t="https://up-as0.qbox.me";break;default:console.error("please make the region is with one of [ECN, SCN, NCN, NA, ASG]")}return t}(Dk.qiniuRegion),s=e.split("//")[1];o&&o.key&&(s=o.key);var l={token:Dk.qiniuUploadToken};Dk.qiniuShouldUseQiniuFileName||(l.key=s);var c=wx.uploadFile({url:a,filePath:e,name:"file",formData:l,success:function(e){var o=e.data;e.data.hasOwnProperty("type")&&"Buffer"===e.data.type&&(o=String.fromCharCode.apply(null,e.data.data));try{var r=JSON.parse(o),i=Dk.qiniuImageURLPrefix+"/"+r.key;r.imageURL=i,t&&t(r)}catch(EC){console.log("parse JSON failed, origin String is: "+o),n&&n(EC)}},fail:function(e){console.error(e),n&&n(e)}});c.onProgressUpdate((e=>{r&&r(e)})),i&&i((()=>{c.abort()}))}}const Nk=function(e){return new Promise(((t,n)=>{uni.chooseImage({count:e.count||9,sizeType:e.sizeType||["original","compressed"],sourceType:e.sourceType||["album","camera"],success:function(e){t(e.tempFiles)},fail:e=>{n({errMsg:e.errMsg,errCode:e.errCode,statusCode:0})}})}))},qk=function(e){return new Promise(((t,n)=>{uni.chooseVideo({sourceType:e.sourceType||["album","camera"],compressed:e.compressed||!1,maxDuration:e.maxDuration||60,camera:e.camera||"back",success:function(e){let n=[{path:e.tempFilePath}];n[0].duration=e.duration,n[0].size=e.size,n[0].height=e.height,n[0].width=e.width,n[0].name=e.name,t(n)},fail:e=>{n({errMsg:e.errMsg,errCode:e.errCode,statusCode:0})}})}))},jk=function(e,t){return new Promise(((n,o)=>{if(Array.isArray(e.files)){let r=e.files.length,i=new Array;t?t((t=>{let a=t.visitPrefix.length;"/"==t.visitPrefix.charAt(a-1)&&(t.visitPrefix=t.visitPrefix.substring(0,a-1)),function a(s){let l=e.files[s],c=function(e,t=""){const n="0123456789qwertyuioplkjhgfdsazxcvbnm";let o="",r=new Date;for(let i=0;i<e;i++)o+=n.charAt(Math.ceil(1e8*Math.random())%n.length);return"file/"+t+r.getTime()+o}(10,t.folderPath),u={fileIndex:s,files:e.files,...l};if(l.name){u.name=l.name;let e=l.name.split(".");c+="."+e[e.length-1]}Rk(l.path||l,(t=>{u.url=t.imageURL,e.onEachUpdate&&e.onEachUpdate({url:t.imageURL,...u}),i.push(t.imageURL),r-1>s?a(s+1):n(i)}),(e=>{o(e)}),{region:t.region||"SCN",domain:t.visitPrefix,key:c,uptoken:t.token,uptokenURL:"UpTokenURL.com/uptoken"},(t=>{console.log(e),e.onProgressUpdate&&e.onProgressUpdate(Object.assign({},u,t))}))}(0)})):o({errMsg:"请添加七牛云回调方法：getQnToken",statusCode:0})}else o({errMsg:"files 必须是数组类型",statusCode:0})}))};const zk=Object.assign({},{name:"萤火商城2.0",apiUrl:"./index.php?s=/api/",storeId:10001,enabledSettingCache:!0,enabledAppShareWeixin:!1,enabledH5Multi:!1,domainIdRegex:/shop[\-]?(\d+)\./},{name:"萤火商城2.0",apiUrl:"./index.php?s=/api/",storeId:10001,enabledSettingCache:!0,enabledH5Multi:!0}),Vk={all:()=>zk,get:(e,t)=>zk.hasOwnProperty(e)?zk[e]:(console.error(`检测到不存在的配置项: ${e}`),t),getStoreId(){if(this.get("enabledH5Multi")){const e=window.location.hostname,t=this.get("domainIdRegex");if(e.match(t)){return t.exec(e)[1].trim()}}return this.get("storeId")}},Fk=Vk.get("apiUrl"),Uk=new class extends class{constructor(e){this.baseUrl=e.baseUrl||"",this.fileUrl=e.fileUrl||"",this.timeout=e.timeout||6e3,this.defaultUploadUrl=e.defaultUploadUrl||"",this.header=e.header||{},this.config=e.config||{isPrompt:!0,load:!0,isFactory:!0,resend:0}}post(e="",t={},n={}){return this.request({method:"POST",data:t,url:e,...n})}get(e="",t={},n={}){return this.request({method:"GET",data:t,url:e,...n})}put(e="",t={},n={}){return this.request({method:"PUT",data:t,url:e,...n})}delete(e="",t={},n={}){return this.request({method:"DELETE",data:t,url:e,...n})}jsonp(e="",t={},n={}){return this.request({method:"JSONP",data:t,url:e,...n})}async request(e){let t,n=!1;try{if(!e.url)throw{errMsg:"【request】缺失数据url",statusCode:0};if(t=$k(this,e),n=!0,this.requestStart){let e=this.requestStart(t);if("object"!=typeof e)throw{errMsg:"【request】请求开始拦截器未通过",statusCode:0,data:t.data,method:t.method,header:t.header,url:t.url};["data","header","isPrompt","load","isFactory"].forEach((n=>{t[n]=e[n]}))}let o={};if(o="JSONP"==t.method?await(e=>new Promise(((t,n)=>{let o="";Object.keys(e.data).forEach((t=>{o+=t+"="+e.data[t]+"&"})),""!==o&&(o=o.substr(0,o.lastIndexOf("&"))),e.url=e.url+"?"+o;let r="callback"+Math.ceil(1e6*Math.random());window[r]=e=>{t(e)};let i=document.createElement("script");i.src=e.url+"&callback="+r,document.head.appendChild(i),document.head.removeChild(i)})))(t):await(e=>new Promise(((t,n)=>{let o=!0,r={url:e.url,header:e.header,success:e=>{o=!1,t(e)},fail:e=>{o=!1,"request:fail abort"==e.errMsg?n({errMsg:"请求超时，请重新尝试",statusCode:0}):n(e)}};e.method&&(r.method=e.method),e.data&&(r.data=e.data),e.dataType&&(r.dataType=e.dataType),e.responseType&&(r.responseType=e.responseType),e.withCredentials&&(r.withCredentials=e.withCredentials);let i=uni.request(r);setTimeout((()=>{o&&i.abort()}),e.timeout)})))(t),t.isFactory&&this.dataFactory){let e=await this.dataFactory({...t,response:o});return Promise.resolve(e)}return Promise.resolve(o)}catch(o){return this.requestError&&this.requestError(o),Promise.reject(o)}finally{n&&this.requestEnd&&this.requestEnd(t)}}}{constructor(e){super(e)}async qnImgUpload(e={}){let t;try{t=await Nk(e),e.onSelectComplete&&e.onSelectComplete(t)}catch(n){return this.requestError&&this.requestError(n),Promise.reject(n)}if(t)return this.qnFileUpload({...e,files:t})}async qnVideoUpload(e={}){let t;try{t=await qk(e),e.onSelectComplete&&e.onSelectComplete(t)}catch(n){return this.requestError&&this.requestError(n),Promise.reject(n)}if(t)return this.qnFileUpload({...e,files:t})}async qnFileUpload(e={}){let t;try{if(t={...this.config,...e,header:{},method:"FILE"},this.requestStart){let e=this.requestStart(t);if("object"!=typeof e)throw{errMsg:"【request】请求开始拦截器未通过",statusCode:0,data:t.data,method:t.method,header:t.header,url:t.url};["load","files"].forEach((n=>{t[n]=e[n]}))}let n=await jk(t,this.getQnToken);return Promise.resolve(n)}catch(n){return this.requestError&&this.requestError(n),Promise.reject(n)}finally{this.requestEnd&&this.requestEnd(t)}}async urlImgUpload(){let e={};arguments[0]&&("string"==typeof arguments[0]?e.url=arguments[0]:"object"==typeof arguments[0]&&(e=Object.assign(e,arguments[0]))),arguments[1]&&"object"==typeof arguments[1]&&(e=Object.assign(e,arguments[1]));try{e.files=await Nk(e),e.onSelectComplete&&e.onSelectComplete(e.files)}catch(t){return this.requestError&&this.requestError(t),Promise.reject(t)}if(e.files)return this.urlFileUpload(e)}async urlVideoUpload(){let e={};arguments[0]&&("string"==typeof arguments[0]?e.url=arguments[0]:"object"==typeof arguments[0]&&(e=Object.assign(e,arguments[0]))),arguments[1]&&"object"==typeof arguments[1]&&(e=Object.assign(e,arguments[1]));try{e.files=await qk(e),e.onSelectComplete&&e.onSelectComplete(e.files)}catch(t){return this.requestError&&this.requestError(t),Promise.reject(t)}if(e.files)return this.urlFileUpload(e)}async urlFileUpload(){let e={method:"FILE"};arguments[0]&&("string"==typeof arguments[0]?e.url=arguments[0]:"object"==typeof arguments[0]&&(e=Object.assign(e,arguments[0]))),arguments[1]&&"object"==typeof arguments[1]&&(e=Object.assign(e,arguments[1])),!e.url&&this.defaultUploadUrl&&(e.url=this.defaultUploadUrl);let t=!1;try{if(!e.url)throw{errMsg:"【request】文件上传缺失数据url",statusCode:0,data:e.data,method:e.method,header:e.header,url:e.url};if(e=$k(this,e),t=!0,this.requestStart){let t=this.requestStart(e);if("object"!=typeof t)throw{errMsg:"【request】请求开始拦截器未通过",statusCode:0,data:e.data,method:e.method,header:e.header,url:e.url};["data","header","isPrompt","load","isFactory","files"].forEach((n=>{e[n]=t[n]}))}let n=await function(e,t){return new Promise(((n,o)=>{if(e.header["Content-Type"]&&delete e.header["Content-Type"],e.header["content-type"]&&delete e.header["content-type"],Array.isArray(e.files)){let r=function(s){let l=e.files[s],c={fileIndex:s,files:e.files,...l},u={url:e.url,filePath:l.path,header:e.header,name:e.name||"file",success:l=>{e.isFactory&&t?t({...e,response:l}).then((t=>{a.push(t),e.onEachUpdate&&e.onEachUpdate({data:t,...c}),i<=s?n(a):r(s+1)}),(e=>{o(e)})):(e.onEachUpdate&&e.onEachUpdate({data:l,...c}),a.push(l),i<=s?n(a):r(s+1))},fail:e=>{o(e)}};e.data&&(u.formData=e.data),uni.uploadFile(u).onProgressUpdate((t=>{e.onProgressUpdate&&e.onProgressUpdate(Object.assign({},c,t))}))};const i=e.files.length-1;let a=new Array;r(0)}else o({errMsg:"files 必须是数组类型",statusCode:0})}))}(e,this.dataFactory);return Promise.resolve(n)}catch(n){return this.requestError&&this.requestError(n),Promise.reject(n)}finally{t&&this.requestEnd&&this.requestEnd(e)}}}({baseUrl:Fk,fileUrl:Fk,defaultUploadUrl:"upload/image",header:{"content-type":"application/json;charset=utf-8"},timeout:15e3,config:{isPrompt:!0,load:!0,isFactory:!0}});let Wk=0;Uk.requestStart=e=>{if(e.load&&(Wk<=0&&uni.showLoading({title:"加载中",mask:!0}),Wk+=1),"FILE"==e.method&&e.maxSize){const t=e.maxSize;for(let n of e.files)if(n.size>t)return setTimeout((()=>{uni.showToast({title:"图片过大，请重新上传",icon:"none"})}),10),!1}return e.header.storeId=hC.getters.storeId,e.header.platform=hC.getters.platform,e.header["Access-Token"]=hC.getters.token,e},Uk.requestEnd=e=>{e.load&&(Wk-=1,Wk<=0&&uni.hideLoading())};let Hk=!1;Uk.dataFactory=async e=>{if(!e.response.statusCode||200!=e.response.statusCode)return Promise.reject({statusCode:e.response.statusCode,errMsg:"http状态码错误"});let t=e.response.data;if("string"==typeof t)try{t=JSON.parse(t)}catch(n){t=!1}return!1===t||"object"!=typeof t?Promise.reject({statusCode:e.response.statusCode,errMsg:"请检查api地址能否访问正常"}):200==t.status?Promise.resolve(t):401==t.status?(hC.dispatch("Logout"),Hk||(Hk=!0,uni.showModal({title:"温馨提示",content:"此时此刻需要您登录喔~",confirmText:"去登录",cancelText:"再逛会",success:e=>{e.confirm&&uni.navigateTo({url:"/pages/login/index"}),e.cancel&&Gm().length>1&&uni.navigateBack(),Hk=!1}})),Promise.reject({statusCode:0,errMsg:t.message,result:t})):500==t.status?(e.isPrompt&&setTimeout((()=>{uni.showToast({title:t.message,icon:"none",duration:2500})}),10),Promise.reject({statusCode:0,errMsg:t.message,result:t})):void 0},Uk.requestError=e=>{if(0===e.statusCode)throw e;setTimeout((()=>Yk(e)),10)};const Yk=e=>{let t=`网络请求出错：${e.errMsg}`;"request:fail"===e.errMsg&&(t="网络请求错误：请检查api地址能否访问正常"),uni.showToast({title:t,icon:"none",duration:3500})},Xk="passport/login",Gk="passport/loginMpWx",Jk="passport/loginWxOfficial",Qk="passport/loginMpWxMobile",Kk="passport/loginMpAlipay",Zk="passport/isPersonalMpweixin";function eC(e,t){return Uk.post(Zk,e,t)}const tC=(e,{token:t,userId:n})=>{const o=2592e3;Ok.set("userId",n,o),Ok.set("AccessToken",t,o),e("SET_TOKEN",t),e("SET_USER_ID",n)},nC=e=>e.replace(/\-/g,"/"),oC=(e={})=>{const t=[];for(const n in e){const o=e[n];o&&(lC(o)?o.forEach((e=>{t.push(n+"="+e)})):t.push(n+"="+o))}return t.join("&")},rC=(e="")=>{var t=new Object;if(e)for(var n=e.split("&"),o=0;o<n.length;o++)t[n[o].split("=")[0]]=n[o].split("=")[1]||"";return t},iC=(e,t)=>{for(var n in t)if(t[n]==e)return!0;return!1},aC=e=>0===Object.keys(e).length,sC=e=>"[object Object]"===Object.prototype.toString.call(e),lC=e=>"[object Array]"===Object.prototype.toString.call(e),cC=e=>lC(e)?0===e.length:sC(e)?aC(e):!e,uC=e=>{let t=lC(e)?[]:{};if("object"==typeof e){for(let n in e)t[n]="object"==typeof e[n]?uC(e[n]):e[n];return t}};function dC(e,t=100){let n;return function(){const o=this,r=arguments;n&&clearTimeout(n),n=setTimeout((function(){e.apply(o,r)}),t)}}const pC=(e,t)=>e.filter((e=>t.indexOf(e)>-1)),fC=e=>e*(()=>{const{windowWidth:e}=uni.getSystemInfoSync();return(e>750?560:e)/750})();const hC=new Pk({modules:{app:Lk,user:{state:{token:"",userId:null},mutations:{SET_TOKEN:(e,t)=>{e.token=t},SET_USER_ID:(e,t)=>{e.userId=t}},actions:{Login:({commit:e},t)=>new Promise(((n,o)=>{(function(e){return Uk.post(Xk,e)})({form:t}).then((t=>{const o=t.data;tC(e,o),n(t)})).catch(o)})),LoginMpWx:({commit:e},t)=>new Promise(((n,o)=>{(function(e,t){return Uk.post(Gk,e,t)})({form:t},{isPrompt:!1}).then((t=>{const o=t.data;tC(e,o),n(t)})).catch(o)})),LoginWxOfficial:({commit:e},t)=>new Promise(((n,o)=>{(function(e,t){return Uk.post(Jk,e,t)})({form:t},{isPrompt:!1}).then((t=>{const o=t.data;tC(e,o),n(t)})).catch(o)})),LoginMpWxMobile:({commit:e},t)=>new Promise(((n,o)=>{(function(e,t){return Uk.post(Qk,e,t)})({form:t},{isPrompt:!1}).then((t=>{const o=t.data;tC(e,o),n(t)})).catch(o)})),LoginMpAlipay:({commit:e},t)=>new Promise(((n,o)=>{(function(e,t){return Uk.post(Kk,e,t)})({form:t},{isPrompt:!1}).then((t=>{const o=t.data;tC(e,o),n(t)})).catch(o)})),Logout({commit:e},t){const n=this;return new Promise(((t,o)=>{n.getters.userId>0&&(Ok.remove("userId"),Ok.remove("AccessToken"),e("SET_TOKEN",""),e("SET_USER_ID",null),t())}))}}},theme:{state:{appTheme:{mainBg:"#fa2209",mainBg2:"#ff6335",mainText:"#ffffff",viceBg:"#ffb100",viceBg2:"#ffb900",viceText:"#ffffff"}},mutations:{SET_APP_THEME:(e,t)=>{cC(t)||(e.appTheme=t)}},actions:{SetAppTheme:({commit:e},t)=>new Promise(((n,o)=>{Ok.set("appTheme",t),e("SET_APP_THEME",t),n()}))}}},state:{},mutations:{},actions:{},getters:{storeId:e=>e.app.storeId,platform:e=>e.app.platform,token:e=>e.user.token,userId:e=>e.user.userId,refereeId:e=>e.app.refereeId,appTheme:e=>e.theme.appTheme,modules:e=>e.app.modules}}),gC="store/data",mC=()=>Uk.get(gC),vC=(()=>{const e=window.navigator.userAgent.toLowerCase();return"micromessenger"===String(e.match(/MicroMessenger/i))})()?"WXOFFICIAL":"H5",yC="WXOFFICIAL"===vC,bC="setting/data";class _C{constructor(e){const t=[],n=[];if(!Array.isArray(e))throw new Error("param is not an array!");e.map((e=>{e.key&&e.name&&(t.push(e.key),n.push(e.value),this[e.key]=e,e.key!==e.value&&(this[e.value]=e))})),this.data=e,this.keyArr=t,this.valueArr=n}keyOf(e){return this.data[this.keyArr.indexOf(e)]}valueOf(e){return this.data[this.valueArr.indexOf(e)]}getNameByKey(e){const t=this.keyOf(e);if(!t)throw new Error("No enum constant"+e);return t.name}getNameByValue(e){const t=this.valueOf(e);if(!t)throw new Error("No enum constant"+e);return t.name}getValueByKey(e){const t=this.keyOf(e);if(!t)throw new Error("No enum constant"+e);return t.key}getData(){return this.data}}const wC=new _C([{key:"REGISTER",name:"账户注册设置",value:"register"},{key:"APP_THEME",name:"店铺页面风格",value:"app_theme"},{key:"PAGE_CATEGORY_TEMPLATE",name:"分类页模板",value:"page_category_template"},{key:"POINTS",name:"积分设置",value:"points"},{key:"RECHARGE",name:"充值设置",value:"recharge"},{key:"RECOMMENDED",name:"商品推荐设置",value:"recommended"},{key:"CUSTOMER",name:"商城客服设置",value:"customer"}]),xC=e=>{Ok.set("Setting",e,600)},TC=()=>new Promise(((e,t)=>{Uk.get(bC).then((t=>{e(t.data.setting)}))})),SC=e=>(null==e&&(e=Vk.get("enabledSettingCache")),new Promise(((t,n)=>{const o=Ok.get("Setting");e&&o?t(o):TC().then((e=>{xC(e),t(e)}))}))),kC=(e,t)=>new Promise(((n,o)=>{SC(t).then((t=>n(t[e])))})),CC={setStorage:xC,data:SC,item:kC,setAppTheme:()=>new Promise(((e,t)=>{kC(wC.APP_THEME.value).then((t=>{hC.dispatch("SetAppTheme",t),e()}))})),isShowCustomerBtn:async()=>{const e=await kC(wC.CUSTOMER.value,!0);return!!e.enabled&&("wxqykf"===e.provider||"mpwxkf"===e.provider&&"MP-WEIXIN"===vC)}};var EC,AC={exports:{}};EC=window,AC.exports=function(e,t){if(!e.jWeixin){var n,o={config:"preVerifyJSAPI",onMenuShareTimeline:"menu:share:timeline",onMenuShareAppMessage:"menu:share:appmessage",onMenuShareQQ:"menu:share:qq",onMenuShareWeibo:"menu:share:weiboApp",onMenuShareQZone:"menu:share:QZone",previewImage:"imagePreview",getLocation:"geoLocation",openProductSpecificView:"openProductViewWithPid",addCard:"batchAddCard",openCard:"batchViewCard",chooseWXPay:"getBrandWCPayRequest",openEnterpriseRedPacket:"getRecevieBizHongBaoRequest",startSearchBeacons:"startMonitoringBeacons",stopSearchBeacons:"stopMonitoringBeacons",onSearchBeacons:"onBeaconsInRange",consumeAndShareCard:"consumedShareCard",openAddress:"editAddress"},r=function(){var e={};for(var t in o)e[o[t]]=t;return e}(),i=e.document,a=i.title,s=navigator.userAgent.toLowerCase(),l=navigator.platform.toLowerCase(),c=!(!l.match("mac")&&!l.match("win")),u=-1!=s.indexOf("wxdebugger"),d=-1!=s.indexOf("micromessenger"),p=-1!=s.indexOf("android"),f=-1!=s.indexOf("iphone")||-1!=s.indexOf("ipad"),h=(n=s.match(/micromessenger\/(\d+\.\d+\.\d+)/)||s.match(/micromessenger\/(\d+\.\d+)/))?n[1]:"",g={initStartTime:O(),initEndTime:0,preVerifyStartTime:0,preVerifyEndTime:0},m={version:1,appId:"",initTime:0,preVerifyTime:0,networkType:"",isPreVerifyOk:1,systemType:f?1:p?2:-1,clientVersion:h,url:encodeURIComponent(location.href)},v={},y={_completes:[]},b={state:0,data:{}};L((function(){g.initEndTime=O()}));var _=!1,w=[],x={config:function(t){I("config",v=t);var n=!1!==v.check;L((function(){if(n)k(o.config,{verifyJsApiList:P(v.jsApiList),verifyOpenTagList:P(v.openTagList)},function(){y._complete=function(e){g.preVerifyEndTime=O(),b.state=1,b.data=e},y.success=function(e){m.isPreVerifyOk=0},y.fail=function(e){y._fail?y._fail(e):b.state=-1};var e=y._completes;return e.push((function(){!function(){if(!(c||u||v.debug||h<"6.0.2"||m.systemType<0)){var e=new Image;m.appId=v.appId,m.initTime=g.initEndTime-g.initStartTime,m.preVerifyTime=g.preVerifyEndTime-g.preVerifyStartTime,x.getNetworkType({isInnerInvoke:!0,success:function(t){m.networkType=t.networkType;var n="https://open.weixin.qq.com/sdk/report?v="+m.version+"&o="+m.isPreVerifyOk+"&s="+m.systemType+"&c="+m.clientVersion+"&a="+m.appId+"&n="+m.networkType+"&i="+m.initTime+"&p="+m.preVerifyTime+"&u="+m.url;e.src=n}})}}()})),y.complete=function(t){for(var n=0,o=e.length;n<o;++n)e[n]();y._completes=[]},y}()),g.preVerifyStartTime=O();else{b.state=1;for(var e=y._completes,t=0,r=e.length;t<r;++t)e[t]();y._completes=[]}})),x.invoke||(x.invoke=function(t,n,o){e.WeixinJSBridge&&WeixinJSBridge.invoke(t,E(n),o)},x.on=function(t,n){e.WeixinJSBridge&&WeixinJSBridge.on(t,n)})},ready:function(e){0!=b.state?e():(y._completes.push(e),!d&&v.debug&&e())},error:function(e){h<"6.0.2"||(-1==b.state?e(b.data):y._fail=e)},checkJsApi:function(e){k("checkJsApi",{jsApiList:P(e.jsApiList)},(e._complete=function(e){if(p){var t=e.checkResult;t&&(e.checkResult=JSON.parse(t))}e=function(e){var t=e.checkResult;for(var n in t){var o=r[n];o&&(t[o]=t[n],delete t[n])}return e}(e)},e))},onMenuShareTimeline:function(e){C(o.onMenuShareTimeline,{complete:function(){k("shareTimeline",{title:e.title||a,desc:e.title||a,img_url:e.imgUrl||"",link:e.link||location.href,type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareAppMessage:function(e){C(o.onMenuShareAppMessage,{complete:function(t){"favorite"===t.scene?k("sendAppMessage",{title:e.title||a,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""}):k("sendAppMessage",{title:e.title||a,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareQQ:function(e){C(o.onMenuShareQQ,{complete:function(){k("shareQQ",{title:e.title||a,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareWeibo:function(e){C(o.onMenuShareWeibo,{complete:function(){k("shareWeiboApp",{title:e.title||a,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareQZone:function(e){C(o.onMenuShareQZone,{complete:function(){k("shareQZone",{title:e.title||a,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},updateTimelineShareData:function(e){k("updateTimelineShareData",{title:e.title,link:e.link,imgUrl:e.imgUrl},e)},updateAppMessageShareData:function(e){k("updateAppMessageShareData",{title:e.title,desc:e.desc,link:e.link,imgUrl:e.imgUrl},e)},startRecord:function(e){k("startRecord",{},e)},stopRecord:function(e){k("stopRecord",{},e)},onVoiceRecordEnd:function(e){C("onVoiceRecordEnd",e)},playVoice:function(e){k("playVoice",{localId:e.localId},e)},pauseVoice:function(e){k("pauseVoice",{localId:e.localId},e)},stopVoice:function(e){k("stopVoice",{localId:e.localId},e)},onVoicePlayEnd:function(e){C("onVoicePlayEnd",e)},uploadVoice:function(e){k("uploadVoice",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},downloadVoice:function(e){k("downloadVoice",{serverId:e.serverId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},translateVoice:function(e){k("translateVoice",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},chooseImage:function(e){k("chooseImage",{scene:"1|2",count:e.count||9,sizeType:e.sizeType||["original","compressed"],sourceType:e.sourceType||["album","camera"]},(e._complete=function(e){if(p){var t=e.localIds;try{t&&(e.localIds=JSON.parse(t))}catch(n){}}},e))},getLocation:function(e){},previewImage:function(e){k(o.previewImage,{current:e.current,urls:e.urls},e)},uploadImage:function(e){k("uploadImage",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},downloadImage:function(e){k("downloadImage",{serverId:e.serverId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},getLocalImgData:function(e){!1===_?(_=!0,k("getLocalImgData",{localId:e.localId},(e._complete=function(e){if(_=!1,0<w.length){var t=w.shift();wx.getLocalImgData(t)}},e))):w.push(e)},getNetworkType:function(e){k("getNetworkType",{},(e._complete=function(e){e=function(e){var t=e.errMsg;e.errMsg="getNetworkType:ok";var n=e.subtype;if(delete e.subtype,n)e.networkType=n;else{var o=t.indexOf(":"),r=t.substring(o+1);switch(r){case"wifi":case"edge":case"wwan":e.networkType=r;break;default:e.errMsg="getNetworkType:fail"}}return e}(e)},e))},openLocation:function(e){k("openLocation",{latitude:e.latitude,longitude:e.longitude,name:e.name||"",address:e.address||"",scale:e.scale||28,infoUrl:e.infoUrl||""},e)},getLocation:function(e){k(o.getLocation,{type:(e=e||{}).type||"wgs84"},(e._complete=function(e){delete e.type},e))},hideOptionMenu:function(e){k("hideOptionMenu",{},e)},showOptionMenu:function(e){k("showOptionMenu",{},e)},closeWindow:function(e){k("closeWindow",{},e=e||{})},hideMenuItems:function(e){k("hideMenuItems",{menuList:e.menuList},e)},showMenuItems:function(e){k("showMenuItems",{menuList:e.menuList},e)},hideAllNonBaseMenuItem:function(e){k("hideAllNonBaseMenuItem",{},e)},showAllNonBaseMenuItem:function(e){k("showAllNonBaseMenuItem",{},e)},scanQRCode:function(e){k("scanQRCode",{needResult:(e=e||{}).needResult||0,scanType:e.scanType||["qrCode","barCode"]},(e._complete=function(e){if(f){var t=e.resultStr;if(t){var n=JSON.parse(t);e.resultStr=n&&n.scan_code&&n.scan_code.scan_result}}},e))},openAddress:function(e){k(o.openAddress,{},(e._complete=function(e){var t;(t=e).postalCode=t.addressPostalCode,delete t.addressPostalCode,t.provinceName=t.proviceFirstStageName,delete t.proviceFirstStageName,t.cityName=t.addressCitySecondStageName,delete t.addressCitySecondStageName,t.countryName=t.addressCountiesThirdStageName,delete t.addressCountiesThirdStageName,t.detailInfo=t.addressDetailInfo,delete t.addressDetailInfo,e=t},e))},openProductSpecificView:function(e){k(o.openProductSpecificView,{pid:e.productId,view_type:e.viewType||0,ext_info:e.extInfo},e)},addCard:function(e){for(var t=e.cardList,n=[],r=0,i=t.length;r<i;++r){var a=t[r],s={card_id:a.cardId,card_ext:a.cardExt};n.push(s)}k(o.addCard,{card_list:n},(e._complete=function(e){var t=e.card_list;if(t){for(var n=0,o=(t=JSON.parse(t)).length;n<o;++n){var r=t[n];r.cardId=r.card_id,r.cardExt=r.card_ext,r.isSuccess=!!r.is_succ,delete r.card_id,delete r.card_ext,delete r.is_succ}e.cardList=t,delete e.card_list}},e))},chooseCard:function(e){k("chooseCard",{app_id:v.appId,location_id:e.shopId||"",sign_type:e.signType||"SHA1",card_id:e.cardId||"",card_type:e.cardType||"",card_sign:e.cardSign,time_stamp:e.timestamp+"",nonce_str:e.nonceStr},(e._complete=function(e){e.cardList=e.choose_card_info,delete e.choose_card_info},e))},openCard:function(e){for(var t=e.cardList,n=[],r=0,i=t.length;r<i;++r){var a=t[r],s={card_id:a.cardId,code:a.code};n.push(s)}k(o.openCard,{card_list:n},e)},consumeAndShareCard:function(e){k(o.consumeAndShareCard,{consumedCardId:e.cardId,consumedCode:e.code},e)},chooseWXPay:function(e){k(o.chooseWXPay,A(e),e)},openEnterpriseRedPacket:function(e){k(o.openEnterpriseRedPacket,A(e),e)},startSearchBeacons:function(e){k(o.startSearchBeacons,{ticket:e.ticket},e)},stopSearchBeacons:function(e){k(o.stopSearchBeacons,{},e)},onSearchBeacons:function(e){C(o.onSearchBeacons,e)},openEnterpriseChat:function(e){k("openEnterpriseChat",{useridlist:e.userIds,chatname:e.groupName},e)},launchMiniProgram:function(e){k("launchMiniProgram",{targetAppId:e.targetAppId,path:function(e){if("string"==typeof e&&0<e.length){var t=e.split("?")[0],n=e.split("?")[1];return t+=".html",void 0!==n?t+"?"+n:t}}(e.path),envVersion:e.envVersion},e)},openBusinessView:function(e){k("openBusinessView",{businessType:e.businessType,queryString:e.queryString||"",envVersion:e.envVersion},(e._complete=function(e){if(p){var t=e.extraData;if(t)try{e.extraData=JSON.parse(t)}catch(n){e.extraData={}}}},e))},miniProgram:{navigateBack:function(e){e=e||{},L((function(){k("invokeMiniProgramAPI",{name:"navigateBack",arg:{delta:e.delta||1}},e)}))},navigateTo:function(e){L((function(){k("invokeMiniProgramAPI",{name:"navigateTo",arg:{url:e.url}},e)}))},redirectTo:function(e){L((function(){k("invokeMiniProgramAPI",{name:"redirectTo",arg:{url:e.url}},e)}))},switchTab:function(e){L((function(){k("invokeMiniProgramAPI",{name:"switchTab",arg:{url:e.url}},e)}))},reLaunch:function(e){L((function(){k("invokeMiniProgramAPI",{name:"reLaunch",arg:{url:e.url}},e)}))},postMessage:function(e){L((function(){k("invokeMiniProgramAPI",{name:"postMessage",arg:e.data||{}},e)}))},getEnv:function(t){L((function(){t({miniprogram:"miniprogram"===e.__wxjs_environment})}))}}},T=1,S={};return i.addEventListener("error",(function(e){if(!p){var t=e.target,n=t.tagName,o=t.src;if(("IMG"==n||"VIDEO"==n||"AUDIO"==n||"SOURCE"==n)&&-1!=o.indexOf("wxlocalresource://")){e.preventDefault(),e.stopPropagation();var r=t["wx-id"];if(r||(r=T++,t["wx-id"]=r),S[r])return;S[r]=!0,wx.ready((function(){wx.getLocalImgData({localId:o,success:function(e){t.src=e.localData}})}))}}}),!0),i.addEventListener("load",(function(e){if(!p){var t=e.target,n=t.tagName;if(t.src,"IMG"==n||"VIDEO"==n||"AUDIO"==n||"SOURCE"==n){var o=t["wx-id"];o&&(S[o]=!1)}}}),!0),t&&(e.wx=e.jWeixin=x),x}function k(t,n,o){e.WeixinJSBridge?WeixinJSBridge.invoke(t,E(n),(function(e){M(t,e,o)})):I(t,o)}function C(t,n,o){e.WeixinJSBridge?WeixinJSBridge.on(t,(function(e){o&&o.trigger&&o.trigger(e),M(t,e,n)})):I(t,o||n)}function E(e){return(e=e||{}).appId=v.appId,e.verifyAppId=v.appId,e.verifySignType="sha1",e.verifyTimestamp=v.timestamp+"",e.verifyNonceStr=v.nonceStr,e.verifySignature=v.signature,e}function A(e){return{timeStamp:e.timestamp+"",nonceStr:e.nonceStr,package:e.package,paySign:e.paySign,signType:e.signType||"SHA1"}}function M(e,t,n){"openEnterpriseChat"!=e&&"openBusinessView"!==e||(t.errCode=t.err_code),delete t.err_code,delete t.err_desc,delete t.err_detail;var o=t.errMsg;o||(o=t.err_msg,delete t.err_msg,o=function(e,t){var n=e,o=r[n];o&&(n=o);var i="ok";if(t){var a=t.indexOf(":");"confirm"==(i=t.substring(a+1))&&(i="ok"),"failed"==i&&(i="fail"),-1!=i.indexOf("failed_")&&(i=i.substring(7)),-1!=i.indexOf("fail_")&&(i=i.substring(5)),"access denied"!=(i=(i=i.replace(/_/g," ")).toLowerCase())&&"no permission to execute"!=i||(i="permission denied"),"config"==n&&"function not exist"==i&&(i="ok"),""==i&&(i="fail")}return n+":"+i}(e,o),t.errMsg=o),(n=n||{})._complete&&(n._complete(t),delete n._complete),o=t.errMsg||"",v.debug&&!n.isInnerInvoke&&alert(JSON.stringify(t));var i=o.indexOf(":");switch(o.substring(i+1)){case"ok":n.success&&n.success(t);break;case"cancel":n.cancel&&n.cancel(t);break;default:n.fail&&n.fail(t)}n.complete&&n.complete(t)}function P(e){if(e){for(var t=0,n=e.length;t<n;++t){var r=e[t],i=o[r];i&&(e[t]=i)}return e}}function I(e,t){if(!(!v.debug||t&&t.isInnerInvoke)){var n=r[e];n&&(e=n),t&&t._complete&&delete t._complete,console.log('"'+e+'",',t||"")}}function O(){return(new Date).getTime()}function L(t){d&&(e.WeixinJSBridge?t():i.addEventListener&&i.addEventListener("WeixinJSBridgeReady",t,!1))}}(EC);var MC=AC.exports;const PC={data:[],current_page:1,last_page:1,per_page:15,total:0},IC=(e,t)=>{uni.showToast({title:e,icon:"success",mask:!0,duration:1500,success(){t&&t()}})},OC=(e,t)=>{uni.showModal({title:"友情提示",content:e,showCancel:!1,success(e){t&&t()}})},LC=(e,t=1500,n=!0)=>{uni.showToast({title:e,icon:"none",mask:n,duration:t})},$C=()=>["pages/index/index","pages/category/index","pages/cart/index","pages/user/index"],DC=(e,t,n)=>{let o=e;if(!cC(t)){o+="#/"+t;const e=RC(n);cC(e)||(o+="?"+e)}return o},RC=e=>oC(BC(e)),BC=e=>({refereeId:hC.getters.userId,...e}),NC=(e,t={},n="navigateTo")=>{if(!e||0==e.length)return!1;if(iC(e,["pages/index/index","pages/category/index","pages/cart/index","pages/user/index"]))return uni.switchTab({url:`/${e}`}),!0;const o=cC(t)?"":"?"+oC(t);return"navigateTo"===n&&uni.navigateTo({url:`/${e}${o}`}),"redirectTo"===n&&uni.redirectTo({url:`/${e}${o}`}),!0},qC=()=>{const e=Gm(),t=e[e.length-1].$page.fullPath.split("?");return{path:t[0].slice(1),query:rC(t[1])}},jC=e=>{uni.setStorageSync("cartTotalNum",Number(e))},zC=()=>{const e=(e=>{const t=uni.getStorageSync("cartTotalNum")||0;return VC()?t:0})();e>0?uni.setTabBarBadge({index:2,text:`${e}`}):uni.removeTabBarBadge({index:2})},VC=()=>!!hC.getters.userId,FC=()=>uC(PC),UC=(e,t,n)=>(1==n&&(t.data=[]),t.data.concat(e.data)),WC=e=>{return n="scene",sC(t=e)&&void 0!==t[n]?(e=>{if(void 0===e)return{};const t={},n=decodeURIComponent(e).split(",");for(const o in n){const e=n[o].split(":");e.length>0&&e[0]&&(t[e[0]]=e[1]||null)}return t})(e.scene):{};var t,n},HC=e=>iC(e,hC.getters.modules),YC=e=>e.filter((e=>HC(e))).length>0,XC=e=>e.filter((e=>!e.moduleKey||HC(e.moduleKey))),GC=e=>!!e&&("PAGE"===e.type&&NC(e.param.path,e.param.query),"CUSTOM"===e.type&&NC(e.param.path,rC(e.param.queryStr)),"URL"===e.type&&window.open(e.param.url),!0),JC="wxofficial/jssdkConfig",QC="wxofficial/oauthUrl",KC="wxofficial/oauthUserInfo",ZC={jssdkConfig:e=>Uk.get(JC,{url:e}),oauthUrl:e=>Uk.get(QC,{callbackUrl:e}),oauthUserInfo:e=>Uk.get(KC,{code:e})},eE=async()=>{const{path:e,query:t}=qC();return new Promise(((n,o)=>{uE.h5Url().then((o=>{const r=DC(o,e,t);n(r)}))}))},tE=async()=>{const e=window.location.href.split("#")[0],t=await ZC.jssdkConfig(e);MC.config(t.data.config),MC.error((e=>{console.error("jWeixin.error",e),OC(`微信链接分享配置错误：${e.errMsg}`)}))},nE=async e=>{const t=await sE.isWxofficialLinkShareCard();yC&&t&&(e.link=await eE(),MC.ready((()=>{console.log("jWeixin.ready",e),MC.updateAppMessageShareData(e),MC.updateTimelineShareData(e)})))};let oE=!1;const rE=()=>new Promise(((e,t)=>{uE.data().then((t=>{e(t.clientData.wxofficial.setting)}))})),iE=e=>new Promise(((t,n)=>{rE().then((n=>t(n[e])))})),aE=async()=>await iE("share"),sE={data:rE,item:iE,isWxofficialLinkShareCard:()=>new Promise(((e,t)=>{iE("share").then((t=>e(Boolean(t.enabled))))})),updateShareData:async e=>{oE=!0;const t=await aE(),n=Object.assign({},t,e);console.log("options",n),nE(n)},setGlobalShareCard:async(e=!1)=>{const t=await aE();yC&&t.enabled&&(window.location.href.split("#")[0],await tE(),oE&&!e||nE(t))}},lE=e=>{(e=>{Ok.set("Store",e,600)})(e),CC.setStorage(e.setting),CC.setAppTheme(),yC&&sE.setGlobalShareCard(!1),hC.dispatch("SetModules",e.modules)},cE=(e=!0)=>new Promise(((t,n)=>{const o=Ok.get("Store");e&&o?t(o):new Promise(((e,t)=>{mC().then((t=>{lE(t.data),e(t.data)}))})).then((e=>{t(e)}))})),uE={data:cE,storeInfo:()=>new Promise(((e,t)=>{cE().then((t=>e(t.storeInfo)))})),h5Url:()=>new Promise(((e,t)=>{cE().then((t=>{const n=t.clientData.h5.setting.baseUrl;e(n)}))})),isEnabledDealer:()=>new Promise(((e,t)=>{cE().then((t=>{const n=Boolean(t.dealer.setting.is_open);e(n)}))}))},dE={globalData:{},onLaunch({path:e,query:t,scene:n}){this.onStartupQuery(sC(t)?t:{}),this.getStoreInfo()},methods:{onStartupQuery(e){const t=WC(e),n=e.refereeId?e.refereeId:t.uid;n>0&&this.setRefereeId(n)},setRefereeId(e){hC.dispatch("setRefereeId",e)},getStoreInfo(){uE.data(!1)},updateManager(){const e=uni.getUpdateManager();e.onCheckForUpdate((e=>{})),e.onUpdateReady((()=>{uni.showModal({title:"更新提示",content:"新版本已经准备好，即将重启应用",showCancel:!1,success(t){t.confirm&&e.applyUpdate()}})})),e.onUpdateFailed((()=>{uni.showModal({title:"更新提示",content:"新版本下载失败",showCancel:!1})}))}}};function pE(){hC.commit("SET_STORE_ID",Vk.getStoreId()),hC.commit("SET_PLATFORM",vC),hC.commit("SET_TOKEN",Ok.get("AccessToken")),hC.commit("SET_USER_ID",Ok.get("userId")),hC.commit("SET_REFEREE_ID",Ok.get("refereeId")),hC.commit("SET_APP_THEME",Ok.get("appTheme")),hC.commit("SET_MODULES",Ok.get("modules"))}xv(dE,{init:_v,setup(e){const t=Lm(),n=()=>{var n;n=e,Object.keys(lf).forEach((e=>{lf[e].forEach((t=>{Lo(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i,onError:a}=e,s=function({path:e,query:t}){return c(Eh,{path:e,query:t}),c(Ah,Eh),c({},Eh)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:Se(t.query)});if(o&&D(o,s),r&&D(r,s),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};i&&D(i,e)}a&&(e.appContext.config.errorHandler=e=>{D(a,e)})};return Qn(rl).isReady().then(n),Ro((()=>{window.addEventListener("resize",Ee(Sv,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",kv),document.addEventListener("visibilitychange",Cv),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{Dx.emit("onThemeChange",{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(Fr(),Xr(Lw));e.setup=(e,o)=>{const r=t&&t(e,o);return v(r)?n:r},e.render=n}});const fE={data:()=>({platform:vC}),computed:{appTheme:()=>hC.getters.appTheme,appThemeStyle:()=>(e=>{let t="";for(const n in e)t+=`--${n.replace(/([A-Z])/g,"-$1").toLowerCase()}:${e[n]};`;return t})(hC.getters.appTheme)},mounted(){"WXOFFICIAL"===this.platform&&this.hideNavigationBar()},methods:{hideNavigationBar(){this.$nextTick((()=>{const e=document.getElementsByTagName("uni-page-head");e.length&&(e[0].style.display="none",document.body.style.setProperty("--window-top","0px"))}))}}},hE={data:()=>({}),onLoad(){this.$u.getRect=this.$uGetRect},methods:{$uGetRect(e,t){return new Promise((n=>{uni.createSelectorQuery().in(this)[t?"selectAll":"select"](e).boundingClientRect((e=>{t&&Array.isArray(e)&&e.length&&n(e),!t&&e&&n(e)})).exec()}))},getParentData(e=""){this.parent||(this.parent=!1),this.parent=this.$u.$parent.call(this,e),this.parent&&(Object.keys(this.parentData).map((e=>{this.parentData[e]=this.parent[e]})),this.parentData.value=this.parent.modelValue)},preventEvent(e){e&&e.stopPropagation&&e.stopPropagation()}},onReachBottom(){uni.$emit("uOnReachBottom")},beforeUnmount(){if(this.parent&&uni.$u.test.array(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}}};function gE(e){if([null,void 0,NaN,!1].includes(e))return e;if("object"!=typeof e&&"function"!=typeof e)return e;var t,n=(t=e,"[object Array]"===Object.prototype.toString.call(t)?[]:{});for(let o in e)e.hasOwnProperty(o)&&(n[o]="object"==typeof e[o]?gE(e[o]):e[o]);return n}function mE(e={},t={}){if("object"!=typeof(e=gE(e))||"object"!=typeof t)return!1;for(var n in t)t.hasOwnProperty(n)&&(n in e?"object"!=typeof e[n]||"object"!=typeof t[n]?e[n]=t[n]:e[n].concat&&t[n].concat?e[n]=e[n].concat(t[n]):e[n]=mE(e[n],t[n]):e[n]=t[n]);return e}function vE(e){switch(typeof e){case"undefined":return!0;case"string":if(0==e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(0===e||isNaN(e))return!0;break;case"object":if(null===e||0===e.length)return!0;for(var t in e)return!1;return!0}return!1}const yE={email:function(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)},mobile:function(e){return/^1[23456789]\d{9}$/.test(e)},url:function(e){return/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w-.\/?%&=]*)?/.test(e)},date:function(e){return!/Invalid|NaN/.test(new Date(e).toString())},dateISO:function(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},number:function(e){return/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(e)},digits:function(e){return/^\d+$/.test(e)},idCard:function(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)},carNo:function(e){const t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===e.length?n.test(e):8===e.length&&t.test(e)},amount:function(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)},chinese:function(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)},letter:function(e){return/^[a-zA-Z]*$/.test(e)},enOrNum:function(e){return/^[0-9a-zA-Z]*$/g.test(e)},contains:function(e,t){return e.indexOf(t)>=0},range:function(e,t){return e>=t[0]&&e<=t[1]},rangeLength:function(e,t){return e.length>=t[0]&&e.length<=t[1]},empty:vE,isEmpty:vE,jsonString:function(e){if("string"==typeof e)try{var t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(EC){return!1}return!1},landline:function(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)},object:function(e){return"[object Object]"===Object.prototype.toString.call(e)},array:function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)},code:function(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)}};const bE=new class{setConfig(e){this.config=mE(this.config,e)}request(e={}){if(this.interceptor.request&&"function"==typeof this.interceptor.request){let t=this.interceptor.request(e);if(!1===t)return new Promise((()=>{}));this.options=t}return e.dataType=e.dataType||this.config.dataType,e.responseType=e.responseType||this.config.responseType,e.url=e.url||"",e.params=e.params||{},e.header=Object.assign({},this.config.header,e.header),e.method=e.method||this.config.method,new Promise(((t,n)=>{e.complete=e=>{if(uni.hideLoading(),clearTimeout(this.config.timer),this.config.timer=null,this.config.originalData)if(this.interceptor.response&&"function"==typeof this.interceptor.response){let o=this.interceptor.response(e);!1!==o?t(o):n(e)}else t(e);else if(200==e.statusCode)if(this.interceptor.response&&"function"==typeof this.interceptor.response){let o=this.interceptor.response(e.data);!1!==o?t(o):n(e.data)}else t(e.data);else n(e)},e.url=yE.url(e.url)?e.url:this.config.baseUrl+(0==e.url.indexOf("/")?e.url:"/"+e.url),this.config.showLoading&&!this.config.timer&&(this.config.timer=setTimeout((()=>{uni.showLoading({title:this.config.loadingText,mask:this.config.loadingMask}),this.config.timer=null}),this.config.loadingTime)),uni.request(e)}))}constructor(){this.config={baseUrl:"",header:{},method:"POST",dataType:"json",responseType:"text",showLoading:!0,loadingText:"请求中...",loadingTime:800,timer:null,originalData:!1,loadingMask:!0},this.interceptor={request:null,response:null},this.get=(e,t={},n={})=>this.request({method:"GET",url:e,header:n,data:t}),this.post=(e,t={},n={})=>this.request({url:e,method:"POST",header:n,data:t}),this.put=(e,t={},n={})=>this.request({url:e,method:"PUT",header:n,data:t}),this.delete=(e,t={},n={})=>this.request({url:e,method:"DELETE",header:n,data:t})}};const _E=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,t){e=e&&this.addRootPath(e);let n="";return/.*\/.*\?.*=.*/.test(e)?(n=uni.$u.queryParams(t,!1),e+"&"+n):(n=uni.$u.queryParams(t),e+n)}async route(e={},t={}){let n={};if("string"==typeof e?(n.url=this.mixinParam(e,t),n.type="navigateTo"):(n=uni.$u.deepClone(e,this.config),n.url=this.mixinParam(e.url,e.params)),t.intercept&&(this.config.intercept=t.intercept),n.params=t,n=uni.$u.deepMerge(this.config,n),"function"==typeof uni.$u.routeIntercept){await new Promise(((e,t)=>{uni.$u.routeIntercept(n,e)}))&&this.openPage(n)}else this.openPage(n)}openPage(e){const{url:t,type:n,delta:o,animationType:r,animationDuration:i}=e;"navigateTo"!=e.type&&"to"!=e.type||uni.navigateTo({url:t,animationType:r,animationDuration:i}),"redirectTo"!=e.type&&"redirect"!=e.type||uni.redirectTo({url:t}),"switchTab"!=e.type&&"tab"!=e.type||uni.switchTab({url:t}),"reLaunch"!=e.type&&"launch"!=e.type||uni.reLaunch({url:t}),"navigateBack"!=e.type&&"back"!=e.type||uni.navigateBack({delta:o})}}).route;function wE(e=null,t="yyyy-mm-dd"){e||(e=Number(new Date)),10==e.toString().length&&(e*=1e3);let n,o=new Date(e),r={"y+":o.getFullYear().toString(),"m+":(o.getMonth()+1).toString(),"d+":o.getDate().toString(),"h+":o.getHours().toString(),"M+":o.getMinutes().toString(),"s+":o.getSeconds().toString()};for(let i in r)n=new RegExp("("+i+")").exec(t),n&&(t=t.replace(n[1],1==n[1].length?r[i]:r[i].padStart(n[1].length,"0")));return t}function xE(e,t=!0){if((e=e.toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e)){if(4===e.length){let t="#";for(let n=1;n<4;n+=1)t+=e.slice(n,n+1).concat(e.slice(n,n+1));e=t}let n=[];for(let t=1;t<7;t+=2)n.push(parseInt("0x"+e.slice(t,t+2)));return t?`rgb(${n[0]},${n[1]},${n[2]})`:n}if(/^(rgb|RGB)/.test(e)){return e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map((e=>Number(e)))}return e}function TE(e){let t=e;if(/^(rgb|RGB)/.test(t)){let e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(","),n="#";for(let t=0;t<e.length;t++){let o=Number(e[t]).toString(16);o=1==String(o).length?"0"+o:o,"0"===o&&(o+=o),n+=o}return 7!==n.length&&(n=t),n}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t))return t;{let e=t.replace(/#/,"").split("");if(6===e.length)return t;if(3===e.length){let t="#";for(let n=0;n<e.length;n+=1)t+=e[n]+e[n];return t}}}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if("[object String]"!==Object.prototype.toString.call(t))throw new TypeError("fillString must be String");let n=this;if(n.length>=e)return String(n);let o=e-n.length,r=Math.ceil(o/t.length);for(;r>>=1;)t+=t,1===r&&(t+=t);return t.slice(0,o)+n});const SE={colorGradient:function(e="rgb(0, 0, 0)",t="rgb(255, 255, 255)",n=10){let o=xE(e,!1),r=o[0],i=o[1],a=o[2],s=xE(t,!1),l=(s[0]-r)/n,c=(s[1]-i)/n,u=(s[2]-a)/n,d=[];for(let p=0;p<n;p++){let e=TE("rgb("+Math.round(l*p+r)+","+Math.round(c*p+i)+","+Math.round(u*p+a)+")");d.push(e)}return d},hexToRgb:xE,rgbToHex:TE,colorToRgba:function(e,t=.3){let n=(e=TE(e)).toLowerCase();if(n&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(n)){if(4===n.length){var o="#";for(let e=1;e<4;e+=1)o+=n.slice(e,e+1).concat(n.slice(e,e+1));n=o}var r=[];for(let e=1;e<7;e+=2)r.push(parseInt("0x"+n.slice(e,e+2)));return"rgba("+r.join(",")+","+t+")"}return n}};let kE=null;let CE=[],EE=[];const AE={v:"1.10.1",version:"1.10.1",type:["primary","success","info","error","warning"]};const ME={queryParams:function(e={},t=!0,n="brackets"){let o=t?"?":"",r=[];-1==["indices","brackets","repeat","comma"].indexOf(n)&&(n="brackets");for(let i in e){let t=e[i];if(!(["",void 0,null].indexOf(t)>=0))if(t.constructor===Array)switch(n){case"indices":for(let n=0;n<t.length;n++)r.push(i+"["+n+"]="+t[n]);break;case"brackets":default:t.forEach((e=>{r.push(i+"[]="+e)}));break;case"repeat":t.forEach((e=>{r.push(i+"="+e)}));break;case"comma":let e="";t.forEach((t=>{e+=(e?",":"")+t})),r.push(i+"="+e)}else r.push(i+"="+t)}return r.length?o+r.join("&"):""},route:_E,timeFormat:wE,date:wE,timeFrom:function(e=null,t="yyyy-mm-dd"){e||(e=Number(new Date)),10==e.toString().length&&(e*=1e3);let n=+new Date(Number(e)),o=(Number(new Date)-n)/1e3,r="";switch(!0){case o<300:r="刚刚";break;case o>=300&&o<3600:r=parseInt(o/60)+"分钟前";break;case o>=3600&&o<86400:r=parseInt(o/3600)+"小时前";break;case o>=86400&&o<2592e3:r=parseInt(o/86400)+"天前";break;default:r=!1===t?o>=2592e3&&o<31536e3?parseInt(o/2592e3)+"个月前":parseInt(o/31536e3)+"年前":wE(n,t)}return r},colorGradient:SE.colorGradient,colorToRgba:SE.colorToRgba,guid:function(e=32,t=!0,n=null){let o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),r=[];if(n=n||o.length,e)for(let i=0;i<e;i++)r[i]=o[0|Math.random()*n];else{let e;r[8]=r[13]=r[18]=r[23]="-",r[14]="4";for(let t=0;t<36;t++)r[t]||(e=0|16*Math.random(),r[t]=o[19==t?3&e|8:e])}return t?(r.shift(),"u"+r.join("")):r.join("")},color:{primary:"#2979ff",primaryDark:"#2b85e4",primaryDisabled:"#a0cfff",primaryLight:"#ecf5ff",bgColor:"#f3f4f6",info:"#909399",infoDark:"#82848a",infoDisabled:"#c8c9cc",infoLight:"#f4f4f5",warning:"#ff9900",warningDark:"#f29100",warningDisabled:"#fcbd71",warningLight:"#fdf6ec",error:"#fa3534",errorDark:"#dd6161",errorDisabled:"#fab6b6",errorLight:"#fef0f0",success:"#19be6b",successDark:"#18b566",successDisabled:"#71d5a1",successLight:"#dbf1e1",mainColor:"#303133",contentColor:"#606266",tipsColor:"#909399",lightColor:"#c0c4cc",borderColor:"#e4e7ed"},sys:function(){return uni.getSystemInfoSync()},os:function(){return uni.getSystemInfoSync().platform},type2icon:function(e="success",t=!1){-1==["primary","info","error","warning","success"].indexOf(e)&&(e="success");let n="";switch(e){case"primary":case"info":n="info-circle";break;case"error":n="close-circle";break;case"warning":n="error-circle";break;default:n="checkmark-circle"}return t&&(n+="-fill"),n},randomArray:function(e=[]){return e.sort((()=>Math.random()-.5))},wranning:function(e){},get:bE.get,post:bE.post,put:bE.put,delete:bE.delete,hexToRgb:SE.hexToRgb,rgbToHex:SE.rgbToHex,test:yE,random:function(e,t){if(e>=0&&t>0&&t>=e){let n=t-e+1;return Math.floor(Math.random()*n+e)}return 0},deepClone:gE,deepMerge:mE,getParent:function(e,t){let n=this.$parent;for(;n;){if(n.$options.name===e){let e={};if(Array.isArray(t))t.map((t=>{e[t]=n[t]?n[t]:""}));else for(let o in t)Array.isArray(t[o])?t[o].length?e[o]=t[o]:e[o]=n[o]:t[o].constructor===Object?Object.keys(t[o]).length?e[o]=t[o]:e[o]=n[o]:e[o]=t[o]||!1===t[o]?t[o]:n[o];return e}n=n.$parent}return{}},$parent:function(e){let t=this.$parent;for(;t;){if(!t.$options||t.$options.name===e)return t;t=t.$parent}return!1},addUnit:function(e="auto",t="rpx"){return e=String(e),yE.number(e)?`${e}${t}`:e},trim:function(e,t="both"){return"both"==t?e.replace(/^\s+|\s+$/g,""):"left"==t?e.replace(/^\s*/,""):"right"==t?e.replace(/(\s*$)/g,""):"all"==t?e.replace(/\s+/g,""):e},type:["primary","success","error","warning","info"],http:bE,toast:function(e,t=1500){uni.showToast({title:e,icon:"none",duration:t})},config:AE,zIndex:{toast:10090,noNetwork:10080,popup:10075,mask:10070,navbar:980,topTips:975,sticky:970,indexListSticky:965},debounce:function(e,t=500,n=!1){if(null!==kE&&clearTimeout(kE),n){var o=!kE;kE=setTimeout((function(){kE=null}),t),o&&"function"==typeof e&&e()}else kE=setTimeout((function(){"function"==typeof e&&e()}),t)},throttle:function(e,t=500,n=!0,o="default"){CE[o]||(CE[o]=null),n?EE[o]||(EE[o]=!0,"function"==typeof e&&e(),CE[o]=setTimeout((()=>{EE[o]=!1}),t)):EE[o]||(EE[o]=!0,CE[o]=setTimeout((()=>{EE[o]=!1,"function"==typeof e&&e()}),t))}};uni.$u=ME;const PE={install:e=>{e.mixin(hE),e.config.globalProperties.$u=ME}};(function(){const e=Ma({...dE,store:hC,created:pE});return e.config.globalProperties.$toast=LC,e.config.globalProperties.$success=IC,e.config.globalProperties.$error=OC,e.config.globalProperties.$navTo=NC,e.config.globalProperties.$getShareUrlParams=RC,e.config.globalProperties.$checkModule=HC,e.config.globalProperties.$checkModules=YC,e.use(PE),e.mixin(fE),{app:e}})().app.use(dv).mount("#app");export{Uk as $,wC as A,Zo as B,iC as C,VC as D,_C as E,Br as F,pC as G,dC as H,pg as I,XC as J,hC as K,sm as L,im as M,Ok as N,qC as O,GC as P,Pv as Q,fC as R,uE as S,ei as T,uC as U,cC as V,Gm as W,Cu as X,eC as Y,oC as Z,ZC as _,ti as a,$C as a0,WC as a1,Jg as a2,zg as a3,hm as a4,xu as a5,lC as a6,sC as a7,aC as a8,tm as a9,nC as aa,yC as ab,sE as ac,Vk as ad,DC as ae,Ix as af,BC as ag,ri as b,Xr as c,Yr as d,Ko as e,oi as f,Yh as g,Sa as h,gm as i,om as j,ce as k,um as l,jC as m,le as n,Fr as o,vC as p,FC as q,Yo as r,zC as s,G as t,UC as u,Wo as v,Un as w,ka as x,Go as y,CC as z};
