<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\service\goods;

use app\common\service\Goods as GoodsService;
use app\store\model\sharp\Goods as SharpGoodsModel;
use app\store\model\groupon\Goods as GrouponGoodsModel;
use app\store\model\bargain\Active as BargainGoodsModel;

/**
 * 服务层：商品关联验证
 * Class Apply
 * @package app\store\service\goods
 */
class Apply extends GoodsService
{
    /**
     * 验证商品规格属性是否锁定
     * @param int $goodsId
     * @return bool
     */
    public static function checkSpecLocked(int $goodsId): bool
    {
        $service = new static;
        if ($service->checkSharpGoods($goodsId)) return true;
        if ($service->checkGrouponGoods($goodsId)) return true;
        return false;
    }

    /**
     * 验证商品是否允许删除
     * @param int $goodsId
     * @return bool
     */
    public static function checkIsAllowDelete(int $goodsId): bool
    {
        $service = new static;
        if ($service->checkSharpGoods($goodsId)) return false;
        if ($service->checkBargainGoods($goodsId)) return false;
        if ($service->checkGrouponGoods($goodsId)) return false;
        return true;
    }

    /**
     * 验证商品是否参与了秒杀商品
     * @param int $goodsId
     * @return bool
     */
    private function checkSharpGoods(int $goodsId): bool
    {
        return SharpGoodsModel::isExistGoodsId($goodsId);
    }

    /**
     * 验证商品是否参与了砍价商品
     * @param int $goodsId
     * @return bool
     */
    private function checkBargainGoods(int $goodsId): bool
    {
        return BargainGoodsModel::isExistGoodsId($goodsId);
    }

    /**
     * 验证商品是否参与了多人拼团
     * @param int $goodsId
     * @return bool
     */
    private function checkGrouponGoods(int $goodsId): bool
    {
        return GrouponGoodsModel::isExistGoodsId($goodsId);
    }
}
