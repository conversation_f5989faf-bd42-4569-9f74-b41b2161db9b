<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\groupon;

use app\common\library\helper;
use app\common\model\groupon\Setting as SettingModel;

/**
 * 拼团活动设置模型
 * Class Setting
 * @package app\api\model\groupon
 */
class Setting extends SettingModel
{
    /**
     * 获取基本配置
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getBasic(): array
    {
        $config = static::getItem('basic');
        return helper::pick($config, ['backdrop', 'ruleBrief', 'ruleDetail']);
    }
}