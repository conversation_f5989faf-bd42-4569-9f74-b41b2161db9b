import{o as t,c as e,w as i,a as r,n as o,d as n,F as s,e as a,k as l,h as u,f as c,t as d,b as m,g as p,i as h,L as f,M as g,$ as y,E as v,N as C,O as w,C as k,P as b,v as _,l as P}from"./index-Dk2OK-Q2.js";import{_ as S}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{r as L}from"./uni-app.es.Bfy34Oxr.js";import{_ as x}from"./u-icon.NnBtpP9Z.js";import{_ as A}from"./u-mask.C7x5h6JE.js";const E=S({name:"u-swiper",emits:["click","change"],props:{list:{type:Array,default:()=>[]},title:{type:Boolean,default:!1},indicator:{type:Object,default:()=>({})},borderRadius:{type:[Number,String],default:8},interval:{type:[String,Number],default:3e3},mode:{type:String,default:"round"},height:{type:[Number,String],default:250},indicatorPos:{type:String,default:"bottomCenter"},effect3d:{type:Boolean,default:!1},effect3dPreviousMargin:{type:[Number,String],default:50},autoplay:{type:Boolean,default:!0},duration:{type:[Number,String],default:500},circular:{type:Boolean,default:!0},imgMode:{type:String,default:"aspectFill"},name:{type:String,default:"image"},bgColor:{type:String,default:"#f3f4f6"},current:{type:[Number,String],default:0},titleStyle:{type:Object,default:()=>({})}},watch:{list(t,e){t.length!==e.length&&(this.uCurrent=0)},current(t){this.uCurrent=t}},data(){return{uCurrent:this.current}},computed:{justifyContent(){return"topLeft"==this.indicatorPos||"bottomLeft"==this.indicatorPos?"flex-start":"topCenter"==this.indicatorPos||"bottomCenter"==this.indicatorPos?"center":"topRight"==this.indicatorPos||"bottomRight"==this.indicatorPos?"flex-end":void 0},titlePaddingBottom(){let t=0;return"none"==this.mode?"12rpx":(t=["bottomLeft","bottomCenter","bottomRight"].indexOf(this.indicatorPos)>=0&&"number"==this.mode?"60rpx":["bottomLeft","bottomCenter","bottomRight"].indexOf(this.indicatorPos)>=0&&"number"!=this.mode?"40rpx":"12rpx",t)},elCurrent(){return Number(this.current)}},methods:{listClick(t){this.$emit("click",t)},change(t){let e=t.detail.current;this.uCurrent=e,this.$emit("change",e)},animationfinish(t){}}},[["render",function(y,v,C,w,k,b){const _=p,P=h,S=f,L=g;return t(),e(P,{class:"u-swiper-wrap",style:o({borderRadius:`${C.borderRadius}rpx`})},{default:i((()=>[r(L,{current:b.elCurrent,onChange:b.change,onAnimationfinish:b.animationfinish,interval:C.interval,circular:C.circular,duration:C.duration,autoplay:C.autoplay,"previous-margin":C.effect3d?C.effect3dPreviousMargin+"rpx":"0","next-margin":C.effect3d?C.effect3dPreviousMargin+"rpx":"0",style:o({height:C.height+"rpx",backgroundColor:C.bgColor})},{default:i((()=>[(t(!0),n(s,null,a(C.list,((n,s)=>(t(),e(S,{class:"u-swiper-item",key:s},{default:i((()=>[r(P,{class:l(["u-list-image-wrap",[k.uCurrent!=s?"u-list-scale":""]]),onClick:u((t=>b.listClick(s)),["stop","prevent"]),style:o({borderRadius:`${C.borderRadius}rpx`,transform:C.effect3d&&k.uCurrent!=s?"scaleY(0.9)":"scaleY(1)",margin:C.effect3d&&k.uCurrent!=s?"0 20rpx":0})},{default:i((()=>[r(_,{class:"u-swiper-image",src:n[C.name]||n,mode:C.imgMode},null,8,["src","mode"]),C.title&&n.title?(t(),e(P,{key:0,class:"u-swiper-title u-line-1",style:o([{"padding-bottom":b.titlePaddingBottom},C.titleStyle])},{default:i((()=>[c(d(n.title),1)])),_:2},1032,["style"])):m("",!0)])),_:2},1032,["onClick","class","style"])])),_:2},1024)))),128))])),_:1},8,["current","onChange","onAnimationfinish","interval","circular","duration","autoplay","previous-margin","next-margin","style"]),r(P,{class:"u-swiper-indicator",style:o({top:"topLeft"==C.indicatorPos||"topCenter"==C.indicatorPos||"topRight"==C.indicatorPos?"12rpx":"auto",bottom:"bottomLeft"==C.indicatorPos||"bottomCenter"==C.indicatorPos||"bottomRight"==C.indicatorPos?"12rpx":"auto",justifyContent:b.justifyContent,padding:"0 "+(C.effect3d?"74rpx":"24rpx")})},{default:i((()=>["rect"==C.mode?(t(!0),n(s,{key:0},a(C.list,((i,r)=>(t(),e(P,{class:l(["u-indicator-item-rect",{"u-indicator-item-rect-active":r==k.uCurrent}]),key:r},null,8,["class"])))),128)):m("",!0),"dot"==C.mode?(t(!0),n(s,{key:1},a(C.list,((i,r)=>(t(),e(P,{class:l(["u-indicator-item-dot",{"u-indicator-item-dot-active":r==k.uCurrent}]),key:r},null,8,["class"])))),128)):m("",!0),"round"==C.mode?(t(!0),n(s,{key:2},a(C.list,((i,r)=>(t(),e(P,{class:l(["u-indicator-item-round",{"u-indicator-item-round-active":r==k.uCurrent}]),key:r},null,8,["class"])))),128)):m("",!0),"number"==C.mode?(t(),e(P,{key:3,class:"u-indicator-item-number"},{default:i((()=>[c(d(k.uCurrent+1)+"/"+d(C.list.length),1)])),_:1})):m("",!0)])),_:1},8,["style"])])),_:1},8,["style"])}],["__scopeId","data-v-8e0842f7"]]),N="promote/activeList",I="promote/updateViewsNum",T="promote/updateClickNum",R=new v([{key:"SINGLE",name:"单图广告",value:10},{key:"SWIPER",name:"多图轮播",value:20}]),V=new v([{key:"MANUAL",name:"仅用户手动关闭",value:10},{key:"AUTO",name:"自动关闭",value:20}]),j=new v([{key:"ONCE",name:"只推荐一次",value:10},{key:"EVERY_DAY",name:"每天推荐一次",value:20}]);const M=S({props:{},data:()=>({ContentTypeEnum:R,CloseModeEnum:V,FrequencyEnum:j,show:!1,times:0,interHandle:void 0,current:0,activeList:[]}),computed:{currentActive(){return this.activeList.length?this.activeList[this.current]:null}},created(){setTimeout((()=>{this.$checkModule("market-promote")&&this.getActiveList()}),1e3)},methods:{async getActiveList(){const t=this,e=await y.get(N,i,{load:!1,isPrompt:!1});var i;t.activeList=t.getUsableList(e.data.list),t.activeList.length>0&&t.showPromote(0)},showPromote(t=0){const e=this;e.show=!0,e.current=t,e.currentActive.close_mode==V.AUTO.value&&e.startTimer(e.currentActive.seconds),e.setViewsState()},getUsableList(t){const e=C.get("PromoteViews"),i=new Date((new Date).toLocaleDateString()).getTime();return t.filter((t=>{const r=w();if(!k(r.path,t.pages))return!1;const o=e[t.promote_id];return!o||t.frequency!=j.ONCE.value&&(t.frequency==j.EVERY_DAY.value&&i>o.showTime)}))},handleClose(){this.show=!1,this.endTimer();const t=this.current+1;this.activeList.length>t&&setTimeout((()=>this.showPromote(t)),1e3)},startTimer(t){const e=this;e.times=t,e.interHandle=setInterval((()=>{e.times--,e.times<=0&&e.handleClose()}),1e3)},endTimer(){this.times=0,clearInterval(this.interHandle)},setViewsState(){const{currentActive:t}=this,e=C.get("PromoteViews")||{};C.set("PromoteViews",{...e,[t.promote_id]:{showTime:(new Date).getTime()}}),this.updateViewsNum(t.promote_id)},handleSingleItem(){const{currentActive:t}=this;this.updateClickNum(t.promote_id),b(t.content_config[R.SINGLE.value].link),this.handleClose()},handleSwiperItem(t){const{currentActive:e}=this;this.updateClickNum(e.promote_id),b(e.content_config[R.SWIPER.value].adList[t].link)},updateViewsNum(t){(t=>{y.post(I,{promoteId:t},{load:!1,isPrompt:!1})})(t)},updateClickNum(t){(t=>{y.post(T,{promoteId:t},{load:!1,isPrompt:!1})})(t)}}},[["render",function(o,n,s,a,l,u){const f=p,g=h,y=L(_("u-swiper"),E),v=L(_("u-icon"),x),C=P,w=L(_("u-mask"),A);return t(),e(w,{show:l.show,duration:100,zoom:!1},{default:i((()=>[l.show?(t(),e(g,{key:0,class:"promote-content"},{default:i((()=>[u.currentActive.content_type==l.ContentTypeEnum.SINGLE.value?(t(),e(g,{key:0,class:"ad-picture",onClick:n[0]||(n[0]=t=>u.handleSingleItem())},{default:i((()=>[r(f,{class:"image",src:u.currentActive.content_config[l.ContentTypeEnum.SINGLE.value].imageUrl,mode:"widthFix"},null,8,["src"])])),_:1})):m("",!0),u.currentActive.content_type==l.ContentTypeEnum.SWIPER.value?(t(),e(g,{key:1,class:"ad-swiper"},{default:i((()=>[r(y,{"bg-color":"unset",name:"imageUrl",list:u.currentActive.content_config[l.ContentTypeEnum.SWIPER.value].adList,interval:3e3,effect3d:!0,height:640,onClick:u.handleSwiperItem},null,8,["list","onClick"])])),_:1})):m("",!0),r(g,{class:"action-close",onClick:n[1]||(n[1]=t=>u.handleClose())},{default:i((()=>[r(v,{name:"close-circle",effect3d:!0})])),_:1}),u.currentActive.close_mode==l.CloseModeEnum.AUTO.value?(t(),e(g,{key:2,class:"close-countdown"},{default:i((()=>[r(C,null,{default:i((()=>[c(d(l.times),1)])),_:1}),r(C,null,{default:i((()=>[c("秒后自动关闭")])),_:1})])),_:1})):m("",!0)])),_:1})):m("",!0)])),_:1},8,["show"])}],["__scopeId","data-v-904a4373"]]);export{M as P};
