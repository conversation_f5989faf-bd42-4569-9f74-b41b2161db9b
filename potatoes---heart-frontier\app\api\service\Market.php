<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\service;

use app\api\model\Coupon as CouponModel;
use app\api\model\Setting as SettingModel;
use app\api\model\FullDiscount as FullDiscountModel;
use app\api\model\groupon\Setting as GrouponSettingModel;
use app\common\enum\DiscountType as DiscountTypeEnum;
use app\common\enum\Setting as SettingEnum;
use app\common\enum\goods\GoodsSource as GoodsSourceEnum;

/**
 * 服务类: 营销管理
 * Class Market
 * @package app\api\service
 */
class Market
{
    /**
     * 获取促销活动详情
     * @param int|null $goodsId 商品ID
     * @param int|null $source 商品来源
     * @return array
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getDetail(?int $goodsId = null, ?int $source = null): array
    {
        // 获取优惠券列表
        $couponList = $this->getCouponList($goodsId, $source);
        // 获取促销活动列表
        $marketList = $this->getMarketList($goodsId, $source);
        return compact('marketList', 'couponList');
    }

    /**
     * 获取优惠券列表
     * @param int|null $goodsId 商品ID
     * @param int|null $source 商品来源
     * @return array|\think\Collection
     * @throws \cores\exception\BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getCouponList(?int $goodsId, ?int $source)
    {
        // 判断来源: 砍价商品和秒杀商品不支持该活动
        if (\in_array($source, [GoodsSourceEnum::BARGAIN, GoodsSourceEnum::SHARP])) {
            return [];
        }
        // 判断拼团商品是否支持
        if ($source == GoodsSourceEnum::GROUPON
            && !GrouponSettingModel::existDiscount(DiscountTypeEnum::COUPON)) {
            return [];
        }
        // 获取优惠券列表
        $model = new CouponModel;
        return $model->getList(null, false, $goodsId);
    }

    /**
     * 获取促销活动列表
     * @param int|null $goodsId 商品ID
     * @param int|null $source 商品来源
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getMarketList(?int $goodsId, ?int $source): array
    {
        // 满额立减
        $data = $this->getFullDiscount($goodsId, $source);
        // 满额包邮
        $data[] = $this->getFullFree($goodsId, $source);
        return \array_values(\array_filter($data));
    }

    /**
     * 满额立减
     * @param int|null $goodsId 商品ID
     * @param int|null $source 商品来源
     * @return string[]|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getFullDiscount(?int $goodsId, ?int $source): ?array
    {
        // 判断来源: 砍价商品和秒杀商品不支持该活动
        if (\in_array($source, [GoodsSourceEnum::BARGAIN, GoodsSourceEnum::SHARP])) {
            return [];
        }
        // 判断拼团商品是否支持
        if ($source == GoodsSourceEnum::GROUPON
            && !GrouponSettingModel::existDiscount(DiscountTypeEnum::FULL_DISCOUNT)) {
            return [];
        }
        // 获取开启的满额立减活动列表
        $model = new FullDiscountModel;
        $list = $model->getActiveListByGoodsIds([$goodsId]);
        $data = [];
        foreach ($list as $item) {
            $data[] = [
                'tagName' => '满额立减',
                'title' => $item['title'],
                'describe' => $item['describe'],
            ];
        }
        !empty($data) && $data[0]['isFirst'] = true;
        return $data;
    }

    /**
     * 满额包邮
     * @param int|null $goodsId 商品ID
     * @param int|null $source 商品来源
     * @return string[]|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function getFullFree(?int $goodsId, ?int $source): ?array
    {
        // 判断来源: 砍价商品和秒杀商品不支持该活动
        if (\in_array($source, [GoodsSourceEnum::BARGAIN, GoodsSourceEnum::SHARP])) {
            return [];
        }
        // 判断拼团商品是否支持
        if ($source == GoodsSourceEnum::GROUPON
            && !GrouponSettingModel::existDiscount(DiscountTypeEnum::FULL_FREE)) {
            return [];
        }
        // 获取满额包邮设置
        $options = SettingModel::getItem(SettingEnum::FULL_FREE);
        if (!$options['is_open'] || ($goodsId > 0 && \in_array($goodsId, $options['excludedGoodsIds']))) {
            return null;
        }
        return [
            'tagName' => '满额包邮',
            'title' => $options['title'],
            'describe' => $options['describe'],
            'isFirst' => true
        ];
    }
}