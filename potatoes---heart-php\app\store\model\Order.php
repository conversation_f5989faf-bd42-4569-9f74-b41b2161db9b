<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types = 1);

namespace app\store\model;

use app\common\enum\order\DataType as DataTypeEnum;
use app\common\enum\order\DeliveryStatus as DeliveryStatusEnum;
use app\common\enum\order\OrderStatus as OrderStatusEnum;
use app\common\enum\order\PayStatus as PayStatusEnum;
use app\common\enum\order\ReceiptStatus as ReceiptStatusEnum;use app\common\library\helper;use app\common\model\Order as OrderModel;use app\common\service\Order as OrderService;use app\common\service\order\PaySuccess as OrderPaySuccesService;
use app\common\service\order\Printer as PrinterService;
use app\common\service\order\Refund as RefundService;
use cores\exception\BaseException;
use think\facade\Db;

/**
 * 订单管理
 * Class Order
 * @package app\store\model
 */
class Order extends OrderModel
{
    /**
     * 订单详情页数据
     * @param int $orderId
     * @return Order|array|null
     */
    public function getDetail(int $orderId)
    {
        $order = static::detail($orderId, [
            'goods.image',
            'delivery' => ['goods', 'express'],
            'extract_shop.logoImage',
            'extract_clerk',
            'trade',
        ]);
        // 附加数据
        static::related($order, ['user', 'address', 'express', 'extract']);
        return $order;
    }
    public function addOrder(array $param = [])
    {
        $dd = [];
        foreach ($param as $key => $value) {
            //查询用户
            $user = Db::name('user')->where('mobile', $value['mobile'])->order('user_id desc')->find();

            $param[$key]['orderId'] = '';
            if ($user && $user['grade_id'] > 0) {
                $gradeName  = Db::name('user_grade')->where('grade_id', $user['grade_id'])->value('name');
                $categoryId = Db::name('category')->where('name', $gradeName)->value('category_id');
                $orderNo    = $this->orderNo();

                // 订单数据
                $data = [
                    'user_id'             => $user['user_id'],
                    'order_no'            => $orderNo,
                    'order_type'          => 10,
                    'total_price'         => 0,
                    'order_price'         => 0,
                    'coupon_id'           => 0,
                    'coupon_money'        => 0,
                    'points_money'        => 0,
                    'points_num'          => 0,
                    'full_discount_money' => 0,
                    'pay_price'           => 0,
                    'delivery_type'       => 10,
                    'buyer_remark'        => '会员大礼包派送',
                    'type'                => 1, //会员兑换
                    'pay_status'          => 20,
                    'pay_time'            => time(),
                    'order_source'        => 10,
                    'order_source_id'     => 0,
                    'order_source_data'   => [],
                    'points_bonus'        => 0,
                    'order_status'        => 10,

                    'platform'            => 'admin',
                    'store_id'            => 10001,
                ];

                // 保存订单记录
                $this->save($data);

                $dd[$this->order_id]['orderId']        = $this->order_id;
                $dd[$this->order_id]['deliveryMethod'] = $value['deliveryMethod'];
                $dd[$this->order_id]['mobile']         = $value['mobile'];
                $dd[$this->order_id]['expressId']      = $value['expressId'];
                $dd[$this->order_id]['expressNo']      = $value['expressNo'];

                $goodsId = Db::name('goods_category_rel')->where('category_id', $categoryId)->value('goods_id');

                // 保存订单商品信息
                $this->saveOrderGoods($user['user_id'], $goodsId);

                $this->saveOrderAddress($user);
                //更新会员礼包次数
                Db::name('xj_vip_order')->where('user_id', $user['user_id'])
                    ->where('invalid', 0)->where('pay_status',20)->dec('num', 1)->update();
            }

        }

        return $dd;
    }
    /**
     * 保存订单商品信息
     * @param $order
     * @return void
     */
    private function saveOrderGoods($user_id, $goodsId): void
    {

        $goods    = Db::name('goods')->where('goods_id', $goodsId)->find();
        $image_id = Db::name('goods_image')->where('goods_id', $goodsId)->value('image_id');
        // 订单商品列表
        $goodsList = [];
        /* @var GoodsModel $goods */
        $item = [
            'user_id'             => $user_id,
            'store_id'            => 10001,
            'goods_id'            => $goods['goods_id'],
            'goods_type'          => $goods['goods_type'],
            'goods_name'          => $goods['goods_name'],
            'goods_no'            => $goods['goods_no'] ?: '',
            'image_id'            => $image_id,
            'deduct_stock_type'   => $goods['deduct_stock_type'],
            'spec_type'           => 10,
            'goods_sku_id'        => 0,
            'goods_props'         => '',
            'content'             => '',
            'goods_sku_no'        => '',
            'goods_price'         => 0,
            'line_price'          => 0,
            'goods_weight'        => 0,
            'is_user_grade'       => 0,
            'grade_ratio'         => 0,
            'grade_goods_price'   => 0,
            'grade_total_money'   => 0,
            'coupon_money'        => 0,
            'points_money'        => 0,
            'points_num'          => 0,
            'points_bonus'        => 0,
            'full_discount_money' => 0,
            'total_num'           => 1,
            'total_price'         => 0,
            'total_pay_price'     => 0,
            'is_ind_dealer'       => 0,
            'dealer_money_type'   => 0,
            'first_money'         => 0,
            'second_money'        => 0,
            'third_money'         => 0,
        ];
        // 记录订单商品来源ID
        $item['goods_source_id'] = 0;
        $goodsList[]             = $item;
        $this->goods()->saveAll($goodsList) !== false;
    }
    /**
     * 获取订单商品的封面图（优先sku封面图）
     * @param $goods
     * @return int
     */
    private function getGoodsImageId($goods): int
    {
        return $goods['skuInfo']['image_id'] ?: (int) current($goods['goods_images'])['file_id'];
    }
    /**
     * 记录收货地址
     * @param $address
     * @return void
     */
    private function saveOrderAddress($address): void
    {

        $this->address()->save([
            'user_id'     => $address['user_id'],
            'store_id'    => 10001,
            'name'        => $address['name'],
            'phone'       => $address['phone'],
            'province_id' => $address['province_id'],
            'city_id'     => $address['city_id'],
            'region_id'   => $address['region_id'],
            'detail'      => $address['detail'],
        ]);
    }
    /**
     * 订单列表
     * @param array $param
     * @return mixed
     */
    public function getList(array $param = [])
    {
        // 检索查询条件
        $filter = $this->getQueryFilter($param);
        // 设置订单类型条件
        $dataTypeFilter = $this->getFilterDataType($param['dataType']);
        // 获取数据列表
        return $this->with(['goods.image', 'user.avatar', 'address'])
            ->alias('order')
            ->field('order.*')
            ->leftJoin('user', 'user.user_id = order.user_id')
            ->leftJoin('order_address address', 'address.order_id = order.order_id')
            ->where($dataTypeFilter)
            ->where($filter)
            ->where('order.is_delete', '=', 0)
            ->order(['order.create_time' => 'desc'])
            ->paginate(10);
    }

    /**
     * 订单列表(全部)
     * @param array $param
     * @return iterable|\think\model\Collection|\think\Paginator
     */
    public function getListAll(array $param = [])
    {
        // 检索查询条件
        $queryFilter = $this->getQueryFilter($param);
        // 设置订单类型条件
        $dataTypeFilter = $this->getFilterDataType($param['dataType']);
        // 获取数据列表
        $orderList = $this->alias('order')
            ->field('order.*')
            ->join('user', 'user.user_id = order.user_id')
            ->where($dataTypeFilter)
            ->where($queryFilter)
            ->where('order.is_delete', '=', 0)
            ->order(['order.create_time' => 'desc'])
            ->select();
        // 加载订单关联数据
        return static::preload($orderList, [
            'goods.image', 'address', 'user.avatar', 'express',
            'extract', 'extract_shop', 'trade',
        ]);
    }

    /**
     * 设置检索查询条件
     * @param array $param
     * @return array
     */
    private function getQueryFilter(array $param): array
    {
        // 默认参数
        $params = $this->setQueryDefaultValue($param, [
            'searchType'    => '', // 关键词类型 (10订单号 20会员昵称 30会员ID 40收货人姓名 50收货人电话)
            'searchValue'   => '', // 关键词内容
            'orderSource'   => -1, // 订单来源
            'payMethod'     => '', // 支付方式
            'deliveryType'  => -1, // 配送方式
            'extractShopId' => 0,  // 自提门店ID
            'betweenTime'   => [], // 起止时间
            'userId'        => 0,  // 会员ID
        ]);
        // 检索查询条件
        $filter = [];
        // 关键词
        if (! empty($params['searchValue'])) {
            $searchWhere = [
                10 => ['order.order_no', 'like', "%{$params['searchValue']}%"],
                20 => ['user.nick_name', 'like', "%{$params['searchValue']}%"],
                30 => ['order.user_id', '=', (int) $params['searchValue']],
                40 => ['address.name', 'like', "%{$params['searchValue']}%"],
                50 => ['address.phone', 'like', "%{$params['searchValue']}%"],
            ];
            array_key_exists($params['searchType'], $searchWhere) && $filter[] = $searchWhere[$params['searchType']];
        }
        // 起止时间
        if (! empty($params['betweenTime'])) {
            $times    = between_time($params['betweenTime']);
            $filter[] = ['order.create_time', '>=', $times['start_time']];
            $filter[] = ['order.create_time', '<', $times['end_time'] + 86400];
        }
        // 订单来源
        $params['orderSource'] > -1 && $filter[] = ['order_source', '=', (int) $params['orderSource']];
        // 支付方式
        ! empty($params['payMethod']) && $filter[] = ['pay_method', '=', $params['payMethod']];
        // 配送方式
        $params['deliveryType'] > -1 && $filter[] = ['delivery_type', '=', (int) $params['deliveryType']];
        // 自提门店id
        $params['extractShopId'] > 0 && $filter[] = ['extract_shop_id', '=', (int) $params['extractShopId']];
        // 会员ID
        $params['userId'] > 0 && $filter[] = ['order.user_id', '=', (int) $params['userId']];
        return $filter;
    }

    /**
     * 设置订单类型条件
     * @param string $dataType
     * @return array
     */
    private function getFilterDataType(string $dataType): array
    {
        // 数据类型
        $filter = [];
        switch ($dataType) {
            case DataTypeEnum::ALL:
                break;
            case DataTypeEnum::PAY:
                $filter[] = ['pay_status', '=', PayStatusEnum::PENDING];
                $filter[] = ['order_status', '=', OrderStatusEnum::NORMAL];
                break;
            case DataTypeEnum::DELIVERY:
                $filter = [
                    ['pay_status', '=', PayStatusEnum::SUCCESS],
                    ['delivery_status', '<>', DeliveryStatusEnum::DELIVERED],
                    ['order_status', 'in', [OrderStatusEnum::NORMAL, OrderStatusEnum::APPLY_CANCEL]],
                ];
                break;
            case DataTypeEnum::RECEIPT:
                $filter = [
                    ['pay_status', '=', PayStatusEnum::SUCCESS],
                    ['delivery_status', '=', DeliveryStatusEnum::DELIVERED],
                    ['receipt_status', '=', ReceiptStatusEnum::NOT_RECEIVED],
                ];
                break;
            case DataTypeEnum::COMPLETE:
                $filter[] = ['order_status', '=', OrderStatusEnum::COMPLETED];
                break;
            case DataTypeEnum::APPLY_CANCEL:
                $filter[] = ['order_status', '=', OrderStatusEnum::APPLY_CANCEL];
                break;
            case DataTypeEnum::CANCEL:
                $filter[] = ['order_status', '=', OrderStatusEnum::CANCELLED];
                break;
        }
        return $filter;
    }

    /**
     * 修改订单价格
     * @param array $data
     * @return bool
     */
    public function updatePrice(array $data): bool
    {
        if ($this['pay_status'] != PayStatusEnum::PENDING) {
            $this->error = '该订单不合法';
            return false;
        }
        // 实际付款金额
        $payPrice = helper::bcadd($data['order_price'], $data['express_price']);
        if ($payPrice <= 0) {
            $this->error = '订单实付款价格不能为0.00元';
            return false;
        }
        // 改价的金额差价
        $updatePrice = helper::bcsub($data['order_price'], $this['order_price']);
        // 更新订单记录
        return $this->save([
            'order_price'   => $data['order_price'],
            'pay_price'     => $payPrice,
            'update_price'  => $updatePrice,
            'express_price' => $data['express_price'],
        ]) !== false;
    }

    /**
     * 修改商家备注
     * @param array $data
     * @return bool
     */
    public function updateRemark(array $data): bool
    {
        return $this->save(['merchant_remark' => $data['content'] ?? '']);
    }

    /**
     * 小票打印
     * @param array $data
     * @return bool
     * @throws BaseException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function printer(array $data): bool
    {
        // 实例化打印机驱动
        $Printer = new PrinterService;
        // 手动打印小票
        $status = $Printer->printEvent($this, $data['printerId']);
        if ($status === false) {
            $this->error = $Printer->getError();
        }
        return $status;
    }

    /**
     * 审核：用户取消订单
     * @param array $data
     * @return bool|mixed
     */
    public function confirmCancel(array $data)
    {
        // 判断订单是否有效
        if (
            $this['pay_status'] != PayStatusEnum::SUCCESS
            || $this['order_status'] != OrderStatusEnum::APPLY_CANCEL
        ) {
            $this->error = '该订单不合法';
            return false;
        }
        // 订单取消事件
        return $this->transaction(function () use ($data) {
            if ($data['status']) {
                // 执行退款操作
                if (! (new RefundService)->handle($this)) {
                    throwError('执行订单退款失败');
                }
                // 订单取消事件
                OrderService::cancelEvent($this);
            }
            // 更新订单状态
            return $this->save([
                'order_status' => $data['status'] ? OrderStatusEnum::CANCELLED : OrderStatusEnum::NORMAL,
            ]);
        });
    }

    /**
     * 自提核销事件 (管理员)
     * @param array $param
     * @return bool|int
     */
    public function extractEvent(array $param)
    {
        // 设置默认参数
        $param = $this->setQueryDefaultValue($param, [
            'clerkId'              => null,
            'syncMpWeixinShipping' => 1,
        ]);
        return $this->confirmExtract((int) $param['clerkId'], (bool) $param['syncMpWeixinShipping']);
    }

    /**
     * 确认支付 (线下支付)
     * @return bool
     * @throws BaseException
     */
    public function paymentEvent(): bool
    {
        // 获取订单详情
        $service = new OrderPaySuccesService;
        // 订单支付成功业务处理
        $service->setOrderNo($this['order_no'])->setMethod(PaymentMethod::OFFLINE);
        if (! $service->handle()) {
            $this->error = $service->getError();
            return false;
        }
        return true;
    }

    /**
     * 将订单记录设置为已删除
     * @return bool
     */
    public function setDelete(): bool
    {
        return $this->save(['is_delete' => 1]);
    }

    /**
     * 获取已付款订单总数 (可指定某天)
     * @param null $startDate
     * @param null $endDate
     * @return int
     */
    public function getPayOrderTotal($startDate = null, $endDate = null): int
    {
        $filter = [
            ['pay_status', '=', PayStatusEnum::SUCCESS],
            ['order_status', '<>', OrderStatusEnum::CANCELLED],
        ];
        if (! is_null($startDate) && ! is_null($endDate)) {
            $filter[] = ['pay_time', '>=', strtotime($startDate)];
            $filter[] = ['pay_time', '<', strtotime($endDate) + 86400];
        }
        return $this->getOrderTotal($filter);
    }

    /**
     * 获取未发货订单数量
     * @return int
     */
    public function getNotDeliveredOrderTotal(): int
    {
        $filter = [
            ['pay_status', '=', PayStatusEnum::SUCCESS],
            ['delivery_status', '<>', DeliveryStatusEnum::DELIVERED],
            ['order_status', 'in', [OrderStatusEnum::NORMAL, OrderStatusEnum::APPLY_CANCEL]],
        ];
        return $this->getOrderTotal($filter);
    }

    /**
     * 获取未付款订单数量
     * @return int
     */
    public function getNotPayOrderTotal(): int
    {
        $filter = [
            ['pay_status', '=', PayStatusEnum::PENDING],
            ['order_status', '=', OrderStatusEnum::NORMAL],
        ];
        return $this->getOrderTotal($filter);
    }

    /**
     * 获取订单总数
     * @param array $filter
     * @return int
     */
    private function getOrderTotal(array $filter = []): int
    {
        // 获取订单总数量
        return $this->where($filter)
            ->where('is_delete', '=', 0)
            ->count();
    }

    /**
     * 获取某天的总销售额
     * @param null $startDate
     * @param null $endDate
     * @return float
     */
    public function getOrderTotalPrice($startDate = null, $endDate = null): float
    {
        // 查询对象
        $query = $this->getNewQuery();
        // 设置查询条件
        if (! is_null($startDate) && ! is_null($endDate)) {
            $query->where('pay_time', '>=', strtotime($startDate))
                ->where('pay_time', '<', strtotime($endDate) + 86400);
        }
        // 总销售额
        return $query->where('pay_status', '=', PayStatusEnum::SUCCESS)
            ->where('order_status', '<>', OrderStatusEnum::CANCELLED)
            ->where('is_delete', '=', 0)
            ->sum('pay_price');
    }

    /**
     * 获取某天的下单用户数
     * @param string $day
     * @return float|int
     */
    public function getPayOrderUserTotal(string $day)
    {
        $startTime = strtotime($day);
        return $this->field('user_id')
            ->where('pay_time', '>=', $startTime)
            ->where('pay_time', '<', $startTime + 86400)
            ->where('pay_status', '=', PayStatusEnum::SUCCESS)
            ->where('is_delete', '=', '0')
            ->group('user_id')
            ->count();
    }

    /**
     * 根据订单号获取ID集
     * @param array $orderNoArr
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getOrderIds(array $orderNoArr): array
    {
        $list = (new static )->where('order_no', 'in', $orderNoArr)->select();
        $data = [];
        foreach ($list as $item) {
            $data[$item['order_no']] = $item['order_id'];
        }
        return $data;
    }
}
