<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\store\controller\market;

use think\response\Json;
use app\store\controller\Controller;
use app\store\model\FullDiscount as FullDiscountModel;

/**
 * 满额立减活动管理
 * Class FullDiscount
 * @package app\store\controller\market
 */
class FullDiscount extends Controller
{
    /**
     * 活动列表
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function list(): Json
    {
        $model = new FullDiscountModel;
        $list = $model->getList($this->request->param());
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 详情记录
     * @param int $fullDiscountId
     * @return Json
     */
    public function detail(int $fullDiscountId): Json
    {
        $detail = FullDiscountModel::detail($fullDiscountId);
        return $this->renderSuccess(compact('detail'));
    }

    /**
     * 添加活动
     * @return Json
     */
    public function add(): Json
    {
        // 新增记录
        $model = new FullDiscountModel;
        if ($model->add($this->postForm())) {
            return $this->renderSuccess('添加成功');
        }
        return $this->renderError($model->getError() ?: '添加失败');
    }

    /**
     * 更新活动
     * @param int $fullDiscountId
     * @return Json
     */
    public function edit(int $fullDiscountId): Json
    {
        // 充值套餐详情
        $model = FullDiscountModel::detail($fullDiscountId);
        // 更新记录
        if ($model->edit($this->postForm())) {
            return $this->renderSuccess('更新成功');
        }
        return $this->renderError($model->getError() ?: '更新失败');
    }

    /**
     * 删除活动
     * @param int $fullDiscountId
     * @return Json
     */
    public function delete(int $fullDiscountId): Json
    {
        $model = FullDiscountModel::detail($fullDiscountId);
        if ($model->setDelete()) {
            return $this->renderSuccess('删除成功');
        }
        return $this->renderError($model->getError() ?: '删除失败');
    }
}
