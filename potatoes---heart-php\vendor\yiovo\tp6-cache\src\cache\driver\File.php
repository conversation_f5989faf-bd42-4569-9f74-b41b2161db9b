<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2025 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace yiovo\cache\cache\driver;

use DateTimeInterface;
use FilesystemIterator;
use think\App;
use yiovo\cache\cache\Driver;
use think\exception\InvalidCacheException;

/**
 * 文件缓存类
 */
class File extends Driver
{
    /**
     * 配置参数
     * @var array
     */
    protected $options = [
        'expire' => 0,
        'cache_subdir' => true,
        'prefix' => '',
        'path' => '',
        'hash_type' => 'md5',
        'data_compress' => false,
        'tag_prefix' => 'tag:',
        'serialize' => [],
        'fail_delete' => false,
    ];

    /**
     * 架构函数
     * @param App $app
     * @param array $options 参数
     */
    public function __construct(App $app, array $options = [])
    {
        if (!empty($options)) {
            $this->options = array_merge($this->options, $options);
        }

        if (empty($this->options['path'])) {
            $this->options['path'] = $app->getRuntimePath() . 'cache';
        }

        if (!str_ends_with($this->options['path'], DIRECTORY_SEPARATOR)) {
            $this->options['path'] .= DIRECTORY_SEPARATOR;
        }
    }

    /**
     * 取得变量的存储文件名
     * @access public
     * @param string $name 缓存变量名
     * @return string
     */
    public function getCacheKey(string $name): string
    {
        $name = hash($this->options['hash_type'], $name);

        if ($this->options['cache_subdir']) {
            // 使用子目录
            $name = substr($name, 0, 2) . DIRECTORY_SEPARATOR . substr($name, 2);
        }

        if ($this->options['prefix']) {
            $name = $this->options['prefix'] . DIRECTORY_SEPARATOR . $name;
        }

        return $this->options['path'] . $name . '.php';
    }

    /**
     * 获取缓存数据
     * @param string $name 缓存标识名
     * @return array|null
     */
    protected function getRaw(string $name)
    {
        $filename = $this->getCacheKey($name);

        if (!is_file($filename)) {
            return;
        }

        $content = @file_get_contents($filename);

        if (false !== $content) {
            $expire = (int)substr($content, 8, 12);
            if (0 != $expire && time() - $expire > filemtime($filename)) {
                //缓存过期删除缓存文件
                $this->unlink($filename);
                return;
            }

            $content = substr($content, 32);

            if ($this->options['data_compress'] && function_exists('gzcompress')) {
                //启用数据压缩
                $content = gzuncompress($content);
            }

            return is_string($content) ? [
                'content' => (string)$content,
                'expire' => $expire,
                'filemtime' => filemtime($filename)
            ] : null;
        }
    }

    /**
     * 判断缓存是否存在
     * @access public
     * @param string $name 缓存变量名
     * @return bool
     */
    public function has($name): bool
    {
        return $this->getRaw($name) !== null;
    }

    /**
     * 读取缓存
     * @access public
     * @param string $name 缓存变量名
     * @param mixed $default 默认值
     * @return mixed
     */
    public function get($name, $default = null): mixed
    {
        $raw = $this->getRaw($name);

        try {
            return is_null($raw) ? $this->getDefaultValue($name, $default) : $this->unserialize($raw['content']);
        } catch (InvalidCacheException $e) {
            return $this->getDefaultValue($name, $default, true);
        }
    }

    /**
     * 写入缓存
     * @access public
     * @param string $name 缓存变量名
     * @param mixed $value 存储数据
     * @param int|\DateInterval|DateTimeInterface|null $expire 有效时间 0为永久
     * @return bool
     */
    public function set($name, $value, $expire = null): bool
    {
        if (is_null($expire)) {
            $expire = $this->options['expire'];
        }

        $expire = $this->getExpireTime($expire);
        $filename = $this->getCacheKey($name);

        $dir = dirname($filename);

        if (!is_dir($dir)) {
            try {
                mkdir($dir, 0755, true);
            } catch (\Exception $e) {
                // 创建失败
            }
        }

        $data = $this->serialize($value);

        if ($this->options['data_compress'] && function_exists('gzcompress')) {
            //数据压缩
            $data = gzcompress($data, 3);
        }

        $data = "<?php\n//" . sprintf('%012d', $expire) . "\n exit();?>\n" . $data;

        if (str_contains($filename, '://') && !str_starts_with($filename, 'file://')) {
            //虚拟文件不加锁
            $result = file_put_contents($filename, $data);
        } else {
            $result = file_put_contents($filename, $data, LOCK_EX);
        }

        if ($result) {
            clearstatcache();
            return true;
        }

        return false;
    }

    /**
     * 自增缓存（针对数值缓存）
     * @access public
     * @param string $name 缓存变量名
     * @param int $step 步长
     * @return false|int
     */
    public function inc($name, $step = 1)
    {
        if ($raw = $this->getRaw($name)) {
            $value = $this->unserialize($raw['content']) + $step;
            $expire = $raw['expire'];
        } else {
            $value = $step;
            $expire = 0;
        }

        return $this->set($name, $value, $expire) ? $value : false;
    }

    /**
     * 自减缓存（针对数值缓存）
     * @access public
     * @param string $name 缓存变量名
     * @param int $step 步长
     * @return false|int
     */
    public function dec($name, $step = 1)
    {
        return $this->inc($name, -$step);
    }

    /**
     * 删除缓存
     * @access public
     * @param string $name 缓存变量名
     * @return bool
     */
    public function delete($name): bool
    {
        return $this->unlink($this->getCacheKey($name));
    }

    /**
     * 清除缓存
     * @access public
     * @return bool
     */
    public function clear(): bool
    {
        $dirname = $this->options['path'] . $this->options['prefix'];

        $this->rmdir($dirname);

        return true;
    }

    /**
     * 删除缓存标签
     * @access public
     * @param array $keys 缓存标识列表
     * @return void
     */
    public function clearTag($keys): void
    {
        foreach ($keys as $key) {
            $this->unlink($key);
        }
    }

    /**
     * 判断文件是否存在后，删除
     * @access private
     * @param string $path
     * @return bool
     */
    private function unlink(string $path): bool
    {
        try {
            return is_file($path) && unlink($path);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 删除文件夹
     * @param $dirname
     * @return bool
     */
    private function rmdir($dirname)
    {
        if (!is_dir($dirname)) {
            return false;
        }

        $items = new FilesystemIterator($dirname);

        foreach ($items as $item) {
            if ($item->isDir() && !$item->isLink()) {
                $this->rmdir($item->getPathname());
            } else {
                $this->unlink($item->getPathname());
            }
        }

        @rmdir($dirname);

        return true;
    }

    /**
     * 获取缓存剩余有效期(秒)
     * @param string $name 缓存标识名
     * @return int
     */
    public function ttl(string $name): int
    {
        $raw = $this->getRaw($name);
        if (empty($raw) || empty($raw['expire'])) {
            return 0;
        }
        return $raw['filemtime'] + $raw['expire'] - time();
    }

    /**
     * 更新缓存内容
     * 该方法区别于set方法，set方法不支持更新内容同时保留缓存剩余有效期
     * @param string $name 缓存标识名
     * @param mixed $newVal 新的内容
     * @param bool $isMerge 是否与更新前的数据合并(仅数组类型有效)
     * @return bool
     */
    public function update(string $name, $newVal, bool $isMerge = true): bool
    {
        $raw = $this->getRaw($name);
        if (empty($raw)) {
            throw new \InvalidArgumentException('cache name not found');
        }
        $value = $this->unserialize($raw['content']);
        if (is_array($value) && is_array($newVal) && $isMerge) {
            $newVal = array_merge($value, $newVal);
        }
        return $this->set($name, $newVal, $this->ttl($name));
    }
}
