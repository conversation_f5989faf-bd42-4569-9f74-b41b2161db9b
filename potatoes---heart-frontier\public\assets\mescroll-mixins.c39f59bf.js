import{o as t,c as o,w as e,a as n,e as i,f as s,t as l,k as r,n as p,g as c,i as a,y as h,r as u,B as d,v as w,x as g}from"./index-b996d08a.js";import{_ as m}from"./_plugin-vue_export-helper.1b428a4d.js";import{r as f}from"./uni-app.es.e82e6f02.js";const y={down:{offset:80,native:!1},up:{offset:150,toTop:{src:"https://www.mescroll.com/img/mescroll-totop.png",offset:1e3,right:20,bottom:120,width:72},empty:{use:!0,icon:"/static/empty.png"}},i18n:{zh:{down:{textInOffset:"下拉刷新",textOutOffset:"释放更新",textLoading:"加载中 ...",textSuccess:"加载成功",textErr:"加载失败"},up:{textLoading:"加载中 ...",textNoMore:"亲, 没有更多了",empty:{tip:"亲，暂无相关数据"}}},en:{down:{textInOffset:"drop down refresh",textOutOffset:"release updates",textLoading:"loading ...",textSuccess:"loaded successfully",textErr:"loading failed"},up:{textLoading:"loading ...",textNoMore:"-- END --",empty:{tip:"~ absolutely empty ~"}}}}},S={def:"zh",getType(){return uni.getStorageSync("mescroll-i18n")||this.def},setType(t){uni.setStorageSync("mescroll-i18n",t)}};const D=m({props:{option:{type:Object,default:()=>({})}},computed:{icon(){if(null!=this.option.icon)return this.option.icon;{let t=S.getType();return this.option.i18n?this.option.i18n[t].icon:y.i18n[t].up.empty.icon||y.up.empty.icon}},tip(){if(null!=this.option.tip)return this.option.tip;{let t=S.getType();return this.option.i18n?this.option.i18n[t].tip:y.i18n[t].up.empty.tip||y.up.empty.tip}},btnText(){if(this.option.i18n){let t=S.getType();return this.option.i18n[t].btnText}return this.option.btnText}},methods:{emptyClick(){this.$emit("emptyclick")}}},[["render",function(h,u,d,w,g,m){const f=c,y=a;return t(),o(y,{class:r(["mescroll-empty",{"empty-fixed":d.option.fixed}]),style:p({"z-index":d.option.zIndex,top:d.option.top})},{default:e((()=>[n(y,null,{default:e((()=>[m.icon?(t(),o(f,{key:0,class:"empty-icon",src:m.icon,mode:"widthFix"},null,8,["src"])):i("",!0)])),_:1}),m.tip?(t(),o(y,{key:0,class:"empty-tip"},{default:e((()=>[s(l(m.tip),1)])),_:1})):i("",!0),m.btnText?(t(),o(y,{key:1,class:"empty-btn",onClick:m.emptyClick},{default:e((()=>[s(l(m.btnText),1)])),_:1},8,["onClick"])):i("",!0)])),_:1},8,["class","style"])}],["__scopeId","data-v-58b94e32"]]);function T(t,o){let e=this;e.version="1.3.7",e.options=t||{},e.isScrollBody=o||!1,e.isDownScrolling=!1,e.isUpScrolling=!1;let n=e.options.down&&e.options.down.callback;e.initDownScroll(),e.initUpScroll(),setTimeout((function(){(e.optDown.use||e.optDown.native)&&e.optDown.auto&&n&&(e.optDown.autoShowLoading?e.triggerDownScroll():e.optDown.callback&&e.optDown.callback(e)),e.isUpAutoLoad||setTimeout((function(){e.optUp.use&&e.optUp.auto&&!e.isUpAutoLoad&&e.triggerUpScroll()}),100)}),30)}T.prototype.extendDownScroll=function(t){T.extend(t,{use:!0,auto:!0,native:!1,autoShowLoading:!1,isLock:!1,offset:80,startTop:100,inOffsetRate:1,outOffsetRate:.2,bottomOffset:20,minAngle:45,textInOffset:"下拉刷新",textOutOffset:"释放更新",textLoading:"加载中 ...",textSuccess:"加载成功",textErr:"加载失败",beforeEndDelay:0,bgColor:"transparent",textColor:"gray",inited:null,inOffset:null,outOffset:null,onMoving:null,beforeLoading:null,showLoading:null,afterLoading:null,beforeEndDownScroll:null,endDownScroll:null,afterEndDownScroll:null,callback:function(t){t.resetUpScroll()}})},T.prototype.extendUpScroll=function(t){T.extend(t,{use:!0,auto:!0,isLock:!1,isBoth:!0,callback:null,page:{num:0,size:10,time:null},noMoreSize:5,offset:150,textLoading:"加载中 ...",textNoMore:"-- END --",bgColor:"transparent",textColor:"gray",inited:null,showLoading:null,showNoMore:null,hideUpScroll:null,errDistance:60,toTop:{src:null,offset:1e3,duration:300,btnClick:null,onShow:null,zIndex:9990,left:null,right:20,bottom:120,safearea:!1,width:72,radius:"50%"},empty:{use:!0,icon:null,tip:"~ 暂无相关数据 ~",btnText:"",btnClick:null,onShow:null,fixed:!1,top:"100rpx",zIndex:99},onScroll:!1})},T.extend=function(t,o){if(!t)return o;for(let e in o)if(null==t[e]){let n=o[e];t[e]=null!=n&&"object"==typeof n?T.extend({},n):n}else"object"==typeof t[e]&&T.extend(t[e],o[e]);return t},T.prototype.hasColor=function(t){if(!t)return!1;let o=t.toLowerCase();return"#fff"!=o&&"#ffffff"!=o&&"transparent"!=o&&"white"!=o},T.prototype.initDownScroll=function(){let t=this;t.optDown=t.options.down||{},!t.optDown.textColor&&t.hasColor(t.optDown.bgColor)&&(t.optDown.textColor="#fff"),t.extendDownScroll(t.optDown),t.isScrollBody&&t.optDown.native?t.optDown.use=!1:t.optDown.native=!1,t.downHight=0,t.optDown.use&&t.optDown.inited&&setTimeout((function(){t.optDown.inited(t)}),0)},T.prototype.touchstartEvent=function(t){this.optDown.use&&(this.startPoint=this.getPoint(t),this.startTop=this.getScrollTop(),this.startAngle=0,this.lastPoint=this.startPoint,this.maxTouchmoveY=this.getBodyHeight()-this.optDown.bottomOffset,this.inTouchend=!1)},T.prototype.touchmoveEvent=function(t){if(!this.optDown.use)return;let o=this,e=o.getScrollTop(),n=o.getPoint(t);if(n.y-o.startPoint.y>0&&(o.isScrollBody&&e<=0||!o.isScrollBody&&(e<=0||e<=o.optDown.startTop&&e===o.startTop))&&!o.inTouchend&&!o.isDownScrolling&&!o.optDown.isLock&&(!o.isUpScrolling||o.isUpScrolling&&o.optUp.isBoth)){if(o.startAngle||(o.startAngle=o.getAngle(o.lastPoint,n)),o.startAngle<o.optDown.minAngle)return;if(o.maxTouchmoveY>0&&n.y>=o.maxTouchmoveY)return o.inTouchend=!0,void o.touchendEvent();o.preventDefault(t);let e=n.y-o.lastPoint.y;o.downHight<o.optDown.offset?(1!==o.movetype&&(o.movetype=1,o.isDownEndSuccess=null,o.optDown.inOffset&&o.optDown.inOffset(o),o.isMoveDown=!0),o.downHight+=e*o.optDown.inOffsetRate):(2!==o.movetype&&(o.movetype=2,o.optDown.outOffset&&o.optDown.outOffset(o),o.isMoveDown=!0),o.downHight+=e>0?e*o.optDown.outOffsetRate:e),o.downHight=Math.round(o.downHight);let i=o.downHight/o.optDown.offset;o.optDown.onMoving&&o.optDown.onMoving(o,i,o.downHight)}o.lastPoint=n},T.prototype.touchendEvent=function(t){if(this.optDown.use)if(this.isMoveDown)this.downHight>=this.optDown.offset?this.triggerDownScroll():(this.downHight=0,this.endDownScrollCall(this)),this.movetype=0,this.isMoveDown=!1;else if(!this.isScrollBody&&this.getScrollTop()===this.startTop){if(this.getPoint(t).y-this.startPoint.y<0){this.getAngle(this.getPoint(t),this.startPoint)>80&&this.triggerUpScroll(!0)}}},T.prototype.getPoint=function(t){return t?t.touches&&t.touches[0]?{x:t.touches[0].pageX,y:t.touches[0].pageY}:t.changedTouches&&t.changedTouches[0]?{x:t.changedTouches[0].pageX,y:t.changedTouches[0].pageY}:{x:t.clientX,y:t.clientY}:{x:0,y:0}},T.prototype.getAngle=function(t,o){let e=Math.abs(t.x-o.x),n=Math.abs(t.y-o.y),i=Math.sqrt(e*e+n*n),s=0;return 0!==i&&(s=Math.asin(n/i)/Math.PI*180),s},T.prototype.triggerDownScroll=function(){this.optDown.beforeLoading&&this.optDown.beforeLoading(this)||(this.showDownScroll(),!this.optDown.native&&this.optDown.callback&&this.optDown.callback(this))},T.prototype.showDownScroll=function(){this.isDownScrolling=!0,this.optDown.native?(uni.startPullDownRefresh(),this.showDownLoadingCall(0)):(this.downHight=this.optDown.offset,this.showDownLoadingCall(this.downHight))},T.prototype.showDownLoadingCall=function(t){this.optDown.showLoading&&this.optDown.showLoading(this,t),this.optDown.afterLoading&&this.optDown.afterLoading(this,t)},T.prototype.onPullDownRefresh=function(){this.isDownScrolling=!0,this.showDownLoadingCall(0),this.optDown.callback&&this.optDown.callback(this)},T.prototype.endDownScroll=function(){if(this.optDown.native)return this.isDownScrolling=!1,this.endDownScrollCall(this),void uni.stopPullDownRefresh();let t=this,o=function(){t.downHight=0,t.isDownScrolling=!1,t.endDownScrollCall(t),t.isScrollBody||(t.setScrollHeight(0),t.scrollTo(0,0))},e=0;t.optDown.beforeEndDownScroll&&(e=t.optDown.beforeEndDownScroll(t),null==t.isDownEndSuccess&&(e=0)),"number"==typeof e&&e>0?setTimeout(o,e):o()},T.prototype.endDownScrollCall=function(){this.optDown.endDownScroll&&this.optDown.endDownScroll(this),this.optDown.afterEndDownScroll&&this.optDown.afterEndDownScroll(this)},T.prototype.lockDownScroll=function(t){null==t&&(t=!0),this.optDown.isLock=t},T.prototype.lockUpScroll=function(t){null==t&&(t=!0),this.optUp.isLock=t},T.prototype.initUpScroll=function(){let t=this;t.optUp=t.options.up||{use:!1},!t.optUp.textColor&&t.hasColor(t.optUp.bgColor)&&(t.optUp.textColor="#fff"),t.extendUpScroll(t.optUp),!1!==t.optUp.use&&(t.optUp.hasNext=!0,t.startNum=t.optUp.page.num+1,t.optUp.inited&&setTimeout((function(){t.optUp.inited(t)}),0))},T.prototype.onReachBottom=function(){this.isScrollBody&&!this.isUpScrolling&&!this.optUp.isLock&&this.optUp.hasNext&&this.triggerUpScroll()},T.prototype.onPageScroll=function(t){this.isScrollBody&&(this.setScrollTop(t.scrollTop),t.scrollTop>=this.optUp.toTop.offset?this.showTopBtn():this.hideTopBtn())},T.prototype.scroll=function(t,o){this.setScrollTop(t.scrollTop),this.setScrollHeight(t.scrollHeight),null==this.preScrollY&&(this.preScrollY=0),this.isScrollUp=t.scrollTop-this.preScrollY>0,this.preScrollY=t.scrollTop,this.isScrollUp&&this.triggerUpScroll(!0),t.scrollTop>=this.optUp.toTop.offset?this.showTopBtn():this.hideTopBtn(),this.optUp.onScroll&&o&&o()},T.prototype.triggerUpScroll=function(t){if(!this.isUpScrolling&&this.optUp.use&&this.optUp.callback){if(!0===t){let t=!1;if(!this.optUp.hasNext||this.optUp.isLock||this.isDownScrolling||this.getScrollBottom()<=this.optUp.offset&&(t=!0),!1===t)return}this.showUpScroll(),this.optUp.page.num++,this.isUpAutoLoad=!0,this.num=this.optUp.page.num,this.size=this.optUp.page.size,this.time=this.optUp.page.time,this.optUp.callback(this)}},T.prototype.showUpScroll=function(){this.isUpScrolling=!0,this.optUp.showLoading&&this.optUp.showLoading(this)},T.prototype.showNoMore=function(){this.optUp.hasNext=!1,this.optUp.showNoMore&&this.optUp.showNoMore(this)},T.prototype.hideUpScroll=function(){this.optUp.hideUpScroll&&this.optUp.hideUpScroll(this)},T.prototype.endUpScroll=function(t){null!=t&&(t?this.showNoMore():this.hideUpScroll()),this.isUpScrolling=!1},T.prototype.resetUpScroll=function(t){if(this.optUp&&this.optUp.use){let o=this.optUp.page;this.prePageNum=o.num,this.prePageTime=o.time,o.num=this.startNum,o.time=null,this.isDownScrolling||!1===t||(null==t?(this.removeEmpty(),this.showUpScroll()):this.showDownScroll()),this.isUpAutoLoad=!0,this.num=o.num,this.size=o.size,this.time=o.time,this.optUp.callback&&this.optUp.callback(this)}},T.prototype.setPageNum=function(t){this.optUp.page.num=t-1},T.prototype.setPageSize=function(t){this.optUp.page.size=t},T.prototype.endByPage=function(t,o,e){let n;this.optUp.use&&null!=o&&(n=this.optUp.page.num<o),this.endSuccess(t,n,e)},T.prototype.endBySize=function(t,o,e){let n;if(this.optUp.use&&null!=o){n=(this.optUp.page.num-1)*this.optUp.page.size+t<o}this.endSuccess(t,n,e)},T.prototype.endSuccess=function(t,o,e){let n=this;if(n.isDownScrolling&&(n.isDownEndSuccess=!0,n.endDownScroll()),n.optUp.use){let i;if(null!=t){let s=n.optUp.page.num,l=n.optUp.page.size;if(1===s&&e&&(n.optUp.page.time=e),t<l||!1===o)if(n.optUp.hasNext=!1,0===t&&1===s)i=!1,n.showEmpty();else{i=!((s-1)*l+t<n.optUp.noMoreSize),n.removeEmpty()}else i=!1,n.optUp.hasNext=!0,n.removeEmpty()}n.endUpScroll(i)}},T.prototype.endErr=function(t){if(this.isDownScrolling){this.isDownEndSuccess=!1;let t=this.optUp.page;t&&this.prePageNum&&(t.num=this.prePageNum,t.time=this.prePageTime),this.endDownScroll()}this.isUpScrolling&&(this.optUp.page.num--,this.endUpScroll(!1),this.isScrollBody&&0!==t&&(t||(t=this.optUp.errDistance),this.scrollTo(this.getScrollTop()-t,0)))},T.prototype.showEmpty=function(){this.optUp.empty.use&&this.optUp.empty.onShow&&this.optUp.empty.onShow(!0)},T.prototype.removeEmpty=function(){this.optUp.empty.use&&this.optUp.empty.onShow&&this.optUp.empty.onShow(!1)},T.prototype.showTopBtn=function(){this.topBtnShow||(this.topBtnShow=!0,this.optUp.toTop.onShow&&this.optUp.toTop.onShow(!0))},T.prototype.hideTopBtn=function(){this.topBtnShow&&(this.topBtnShow=!1,this.optUp.toTop.onShow&&this.optUp.toTop.onShow(!1))},T.prototype.getScrollTop=function(){return this.scrollTop||0},T.prototype.setScrollTop=function(t){this.scrollTop=t},T.prototype.scrollTo=function(t,o){this.myScrollTo&&this.myScrollTo(t,o)},T.prototype.resetScrollTo=function(t){this.myScrollTo=t},T.prototype.getScrollBottom=function(){return this.getScrollHeight()-this.getClientHeight()-this.getScrollTop()},T.prototype.getStep=function(t,o,e,n,i){let s=o-t;if(0===n||0===s)return void(e&&e(o));let l=(n=n||300)/(i=i||30),r=s/l,p=0,c=setInterval((function(){p<l-1?(t+=r,e&&e(t,c),p++):(e&&e(o,c),clearInterval(c))}),i)},T.prototype.getClientHeight=function(t){let o=this.clientHeight||0;return 0===o&&!0!==t&&(o=this.getBodyHeight()),o},T.prototype.setClientHeight=function(t){this.clientHeight=t},T.prototype.getScrollHeight=function(){return this.scrollHeight||0},T.prototype.setScrollHeight=function(t){this.scrollHeight=t},T.prototype.getBodyHeight=function(){return this.bodyHeight||0},T.prototype.setBodyHeight=function(t){this.bodyHeight=t},T.prototype.preventDefault=function(t){t&&t.cancelable&&!t.defaultPrevented&&t.preventDefault()};const x=m({props:{option:{type:Object,default:()=>({})},value:!1,modelValue:!1},computed:{left(){return this.option.left?this.addUnit(this.option.left):"auto"},right(){return this.option.left?"auto":this.addUnit(this.option.right)},isShow(){return this.modelValue}},methods:{addUnit:t=>t?"number"==typeof t?t+"rpx":t:0,toTopClick(){this.$emit("update:modelValue",!1),this.$emit("click")}}},[["render",function(e,n,s,l,a,h){const u=c;return s.option.src?(t(),o(u,{key:0,class:r(["mescroll-totop",[h.isShow?"mescroll-totop-in":"mescroll-totop-out",{"mescroll-totop-safearea":s.option.safearea}]]),style:p({"z-index":s.option.zIndex,left:h.left,right:h.right,bottom:h.addUnit(s.option.bottom),width:h.addUnit(s.option.width),"border-radius":h.addUnit(s.option.radius)}),src:s.option.src,mode:"widthFix",onClick:h.toTopClick},null,8,["class","style","src","onClick"])):i("",!0)}],["__scopeId","data-v-3d9c8017"]]),U={data:()=>({wxsProp:{optDown:{},scrollTop:0,bodyHeight:0,isDownScrolling:!1,isUpScrolling:!1,isScrollBody:!0,isUpBoth:!0,t:0},callProp:{callType:"",t:0}}),methods:{wxsCall(t){"setWxsProp"===t.type?this.wxsProp={optDown:this.mescroll.optDown,scrollTop:this.mescroll.getScrollTop(),bodyHeight:this.mescroll.getBodyHeight(),isDownScrolling:this.mescroll.isDownScrolling,isUpScrolling:this.mescroll.isUpScrolling,isUpBoth:this.mescroll.optUp.isBoth,isScrollBody:this.mescroll.isScrollBody,t:Date.now()}:"setLoadType"===t.type?(this.downLoadType=t.downLoadType,this.$set(this.mescroll,"downLoadType",this.downLoadType),this.$set(this.mescroll,"isDownEndSuccess",null)):"triggerDownScroll"===t.type?this.mescroll.triggerDownScroll():"endDownScroll"===t.type?this.mescroll.endDownScroll():"triggerUpScroll"===t.type&&this.mescroll.triggerUpScroll(!0)}},mounted(){this.mescroll.optDown.afterLoading=()=>{this.callProp={callType:"showLoading",t:Date.now()}},this.mescroll.optDown.afterEndDownScroll=()=>{this.callProp={callType:"endDownScroll",t:Date.now()};let t=300+(this.mescroll.optDown.beforeEndDelay||0);setTimeout((()=>{4!==this.downLoadType&&0!==this.downLoadType||(this.callProp={callType:"clearTransform",t:Date.now()}),this.$set(this.mescroll,"downLoadType",this.downLoadType)}),t)},this.wxsCall({type:"setWxsProp"})}};var b={};function v(t,o){if(b.isMoveDown)b.downHight>=b.optDown.offset?(b.downHight=b.optDown.offset,b.callMethod(o,{type:"triggerDownScroll"})):(b.downHight=0,b.callMethod(o,{type:"endDownScroll"})),b.movetype=0,b.isMoveDown=!1;else if(!b.isScrollBody&&b.getScrollTop()===b.startTop){if(b.getPoint(t).y-b.startPoint.y<0)b.getAngle(b.getPoint(t),b.startPoint)>80&&b.callMethod(o,{type:"triggerUpScroll"})}b.callMethod(o,{type:"setWxsProp"})}b.onMoving=function(t,o,e){t.requestAnimationFrame((function(){t.selectComponent(".mescroll-wxs-content").setStyle({"will-change":"transform",transform:"translateY("+e+"px)",transition:""});var n=t.selectComponent(".mescroll-wxs-progress");n&&n.setStyle({transform:"rotate("+360*o+"deg)"})}))},b.showLoading=function(t){b.downHight=b.optDown.offset,t.requestAnimationFrame((function(){t.selectComponent(".mescroll-wxs-content").setStyle({"will-change":"auto",transform:"translateY("+b.downHight+"px)",transition:"transform 300ms"})}))},b.endDownScroll=function(t){b.downHight=0,b.isDownScrolling=!1,t.requestAnimationFrame((function(){t.selectComponent(".mescroll-wxs-content").setStyle({"will-change":"auto",transform:"translateY(0)",transition:"transform 300ms"})}))},b.clearTransform=function(t){t.requestAnimationFrame((function(){t.selectComponent(".mescroll-wxs-content").setStyle({"will-change":"",transform:"",transition:""})}))},b.disabled=function(){return!b.optDown||!b.optDown.use||b.optDown.native},b.getPoint=function(t){return t?t.touches&&t.touches[0]?{x:t.touches[0].pageX,y:t.touches[0].pageY}:t.changedTouches&&t.changedTouches[0]?{x:t.changedTouches[0].pageX,y:t.changedTouches[0].pageY}:{x:t.clientX,y:t.clientY}:{x:0,y:0}},b.getAngle=function(t,o){var e=Math.abs(t.x-o.x),n=Math.abs(t.y-o.y),i=Math.sqrt(e*e+n*n),s=0;return 0!==i&&(s=Math.asin(n/i)/Math.PI*180),s},b.getScrollTop=function(){return b.scrollTop||0},b.getBodyHeight=function(){return b.bodyHeight||0},b.callMethod=function(t,o){t&&t.callMethod("wxsCall",o)};const B={propObserver:function(t){t&&(b.optDown=t.optDown,b.scrollTop=t.scrollTop,b.bodyHeight=t.bodyHeight,b.isDownScrolling=t.isDownScrolling,b.isUpScrolling=t.isUpScrolling,b.isUpBoth=t.isUpBoth,b.isScrollBody=t.isScrollBody,b.startTop=t.scrollTop)},callObserver:function(t,o,e){b.disabled()||t.callType&&("showLoading"===t.callType?b.showLoading(e):"endDownScroll"===t.callType?b.endDownScroll(e):"clearTransform"===t.callType&&b.clearTransform(e))},touchstartEvent:function(t,o){b.downHight=0,b.startPoint=b.getPoint(t),b.startTop=b.getScrollTop(),b.startAngle=0,b.lastPoint=b.startPoint,b.maxTouchmoveY=b.getBodyHeight()-b.optDown.bottomOffset,b.inTouchend=!1,b.callMethod(o,{type:"setWxsProp"})},touchmoveEvent:function(t,o){var e=!0;if(b.disabled())return e;var n=b.getScrollTop(),i=b.getPoint(t);if(i.y-b.startPoint.y>0&&(b.isScrollBody&&n<=0||!b.isScrollBody&&(n<=0||n<=b.optDown.startTop&&n===b.startTop))&&!b.inTouchend&&!b.isDownScrolling&&!b.optDown.isLock&&(!b.isUpScrolling||b.isUpScrolling&&b.isUpBoth)){if(b.startAngle||(b.startAngle=b.getAngle(b.lastPoint,i)),b.startAngle<b.optDown.minAngle)return e;if(b.maxTouchmoveY>0&&i.y>=b.maxTouchmoveY)return b.inTouchend=!0,v(t,o),e;e=!1;var s=i.y-b.lastPoint.y;b.downHight<b.optDown.offset?(1!==b.movetype&&(b.movetype=1,b.callMethod(o,{type:"setLoadType",downLoadType:1}),b.isMoveDown=!0),b.downHight+=s*b.optDown.inOffsetRate):(2!==b.movetype&&(b.movetype=2,b.callMethod(o,{type:"setLoadType",downLoadType:2}),b.isMoveDown=!0),b.downHight+=s>0?s*b.optDown.outOffsetRate:s),b.downHight=Math.round(b.downHight);var l=b.downHight/b.optDown.offset;b.onMoving(o,l,b.downHight)}return b.lastPoint=i,e},touchendEvent:v},L=t=>{t.$wxs||(t.$wxs=[]),t.$wxs.push("wxsBiz"),t.mixins||(t.mixins=[]),t.mixins.push({beforeCreate(){this.wxsBiz=B}})};var H={};function P(t){H.optDown=t.optDown,H.scrollTop=t.scrollTop,H.isDownScrolling=t.isDownScrolling,H.isUpScrolling=t.isUpScrolling,H.isUpBoth=t.isUpBoth}window&&!window.$mescrollRenderInit&&(window.$mescrollRenderInit=!0,window.addEventListener("touchstart",(function(t){H.disabled()||(H.startPoint=H.getPoint(t))}),{passive:!0}),window.addEventListener("touchmove",(function(t){if(!H.disabled()&&(!(H.getScrollTop()>0)&&H.getPoint(t).y-H.startPoint.y>0&&!H.isDownScrolling&&!H.optDown.isLock&&(!H.isUpScrolling||H.isUpScrolling&&H.isUpBoth))){for(var o=t.target,e=!1;o&&o.tagName&&"UNI-PAGE-BODY"!==o.tagName&&"BODY"!=o.tagName;){var n=o.classList;if(n&&n.contains("mescroll-render-touch")){e=!0;break}o=o.parentNode}e&&t.cancelable&&!t.defaultPrevented&&t.preventDefault()}}),{passive:!1})),H.getScrollTop=function(){return H.scrollTop||document.documentElement.scrollTop||document.body.scrollTop||0},H.disabled=function(){return!H.optDown||!H.optDown.use||H.optDown.native},H.getPoint=function(t){return t?t.touches&&t.touches[0]?{x:t.touches[0].pageX,y:t.touches[0].pageY}:t.changedTouches&&t.changedTouches[0]?{x:t.changedTouches[0].pageX,y:t.changedTouches[0].pageY}:{x:t.clientX,y:t.clientY}:{x:0,y:0}};const k={mixins:[{data:()=>({propObserver:P})}]},C=t=>{t.$renderjs||(t.$renderjs=[]),t.$renderjs.push("renderBiz"),t.mixins||(t.mixins=[]),t.mixins.push({beforeCreate(){this.renderBiz=this},mounted(){this.$ownerInstance=this.$gcd(this,!0)}}),t.mixins.push(k)},O={name:"mescroll-body",mixins:[U],components:{MescrollTop:x},props:{down:Object,up:Object,i18n:Object,top:[String,Number],topbar:[Boolean,String],bottom:[String,Number],safearea:Boolean,height:[String,Number],bottombar:{type:Boolean,default:!0},sticky:Boolean},data:()=>({mescroll:{optDown:{},optUp:{}},downHight:0,downRate:0,downLoadType:0,upLoadType:0,isShowEmpty:!1,isShowToTop:!1,windowHeight:0,windowBottom:0,statusBarHeight:0}),computed:{minHeight(){return this.toPx(this.height||"100%")+"px"},numTop(){return this.toPx(this.top)},padTop(){return this.numTop+"px"},numBottom(){return this.toPx(this.bottom)},padBottom(){return this.numBottom+"px"},isDownReset(){return 3===this.downLoadType||4===this.downLoadType},transition(){return this.isDownReset?"transform 300ms":""},translateY(){return this.downHight>0?"translateY("+this.downHight+"px)":""},isDownLoading(){return 3===this.downLoadType},downRotate(){return"rotate("+360*this.downRate+"deg)"},downText(){if(!this.mescroll)return"";switch(this.downLoadType){case 1:default:return this.mescroll.optDown.textInOffset;case 2:return this.mescroll.optDown.textOutOffset;case 3:return this.mescroll.optDown.textLoading;case 4:return this.mescroll.isDownEndSuccess?this.mescroll.optDown.textSuccess:0==this.mescroll.isDownEndSuccess?this.mescroll.optDown.textErr:this.mescroll.optDown.textInOffset}}},methods:{toPx(t){if("string"==typeof t)if(-1!==t.indexOf("px"))if(-1!==t.indexOf("rpx"))t=t.replace("rpx","");else{if(-1===t.indexOf("upx"))return Number(t.replace("px",""));t=t.replace("upx","")}else if(-1!==t.indexOf("%")){let o=Number(t.replace("%",""))/100;return this.windowHeight*o}return t?uni.upx2px(Number(t)):0},emptyClick(){this.$emit("emptyclick",this.mescroll)},toTopClick(){this.mescroll.scrollTo(0,this.mescroll.optUp.toTop.duration),this.$emit("topclick",this.mescroll)}},created(){let t=this,o={down:{inOffset(){t.downLoadType=1},outOffset(){t.downLoadType=2},onMoving(o,e,n){t.downHight=n,t.downRate=e},showLoading(o,e){t.downLoadType=3,t.downHight=e},beforeEndDownScroll:o=>(t.downLoadType=4,o.optDown.beforeEndDelay),endDownScroll(){t.downLoadType=4,t.downHight=0,t.downResetTimer&&(clearTimeout(t.downResetTimer),t.downResetTimer=null),t.downResetTimer=setTimeout((()=>{4===t.downLoadType&&(t.downLoadType=0)}),300)},callback:function(o){t.$emit("down",o)}},up:{showLoading(){t.upLoadType=1},showNoMore(){t.upLoadType=2},hideUpScroll(o){t.upLoadType=o.optUp.hasNext?0:3},empty:{onShow(o){t.isShowEmpty=o}},toTop:{onShow(o){t.isShowToTop=o}},callback:function(o){t.$emit("up",o)}}},e=S.getType(),n={type:e};T.extend(n,t.i18n),T.extend(n,y.i18n),T.extend(o,n[e]),T.extend(o,{down:y.down,up:y.up});let i=JSON.parse(JSON.stringify({down:t.down,up:t.up}));T.extend(i,o),t.mescroll=new T(i,!0),t.mescroll.i18n=n,t.$emit("init",t.mescroll);const s=uni.getSystemInfoSync();s.windowHeight&&(t.windowHeight=s.windowHeight),s.windowBottom&&(t.windowBottom=s.windowBottom),s.statusBarHeight&&(t.statusBarHeight=s.statusBarHeight),t.mescroll.setBodyHeight(s.windowHeight),t.mescroll.resetScrollTo(((o,e)=>{"string"==typeof o?setTimeout((()=>{let n;-1==o.indexOf("#")&&-1==o.indexOf(".")?n="#"+o:(n=o,-1!=o.indexOf(">>>")&&(n=o.split(">>>")[1].trim())),uni.createSelectorQuery().select(n).boundingClientRect((function(o){if(o){let n=o.top;n+=t.mescroll.getScrollTop(),uni.pageScrollTo({scrollTop:n,duration:e})}else console.error(n+" does not exist")})).exec()}),30):uni.pageScrollTo({scrollTop:o,duration:e})})),t.up&&t.up.toTop&&null!=t.up.toTop.safearea||(t.mescroll.optUp.toTop.safearea=t.safearea),uni.$on("setMescrollGlobalOption",(o=>{if(!o)return;let e=o.i18n?o.i18n.type:null;if(e&&t.mescroll.i18n.type!=e&&(t.mescroll.i18n.type=e,S.setType(e),T.extend(o,t.mescroll.i18n[e])),o.down){let e=T.extend({},o.down);t.mescroll.optDown=T.extend(e,t.mescroll.optDown)}if(o.up){let e=T.extend({},o.up);t.mescroll.optUp=T.extend(e,t.mescroll.optUp)}}))},destroyed(){uni.$off("setMescrollGlobalOption")}};L(O),C(O);const E=m(O,[["render",function(c,m,y,S,T,x){const U=a,b=f(h("mescroll-empty"),D),v=u("mescroll-top");return t(),o(U,{class:r(["mescroll-body mescroll-render-touch",{"mescorll-sticky":y.sticky}]),style:p({minHeight:x.minHeight,"padding-top":x.padTop,"padding-bottom":x.padBottom}),onTouchstart:c.wxsBiz.touchstartEvent,onTouchmove:c.wxsBiz.touchmoveEvent,onTouchend:c.wxsBiz.touchendEvent,onTouchcancel:c.wxsBiz.touchendEvent,"change:prop":c.wxsBiz.propObserver,prop:c.wxsProp},{default:e((()=>[y.topbar&&T.statusBarHeight?(t(),o(U,{key:0,class:"mescroll-topbar",style:p({height:T.statusBarHeight+"px",background:y.topbar})},null,8,["style"])):i("",!0),n(U,{class:"mescroll-body-content mescroll-wxs-content",style:p({transform:x.translateY,transition:x.transition}),"change:prop":c.wxsBiz.callObserver,prop:c.callProp},{default:e((()=>[T.mescroll.optDown.use?(t(),o(U,{key:0,class:"mescroll-downwarp",style:p({background:T.mescroll.optDown.bgColor,color:T.mescroll.optDown.textColor})},{default:e((()=>[n(U,{class:"downwarp-content"},{default:e((()=>[n(U,{class:r(["downwarp-progress mescroll-wxs-progress",{"mescroll-rotate":x.isDownLoading}]),style:p({"border-color":T.mescroll.optDown.textColor,transform:x.downRotate})},null,8,["class","style"]),n(U,{class:"downwarp-tip"},{default:e((()=>[s(l(x.downText),1)])),_:1})])),_:1})])),_:1},8,["style"])):i("",!0),d(c.$slots,"default",{},void 0,!0),T.isShowEmpty?(t(),o(b,{key:1,option:T.mescroll.optUp.empty,onEmptyclick:x.emptyClick},null,8,["option","onEmptyclick"])):i("",!0),n(U,{class:"mescroll-upwarp--container"},{default:e((()=>[T.mescroll.optUp.use&&!x.isDownLoading&&3!==T.upLoadType?(t(),o(U,{key:0,class:"mescroll-upwarp",style:p({background:T.mescroll.optUp.bgColor,color:T.mescroll.optUp.textColor})},{default:e((()=>[w(n(U,null,{default:e((()=>[n(U,{class:"upwarp-progress mescroll-rotate",style:p({"border-color":T.mescroll.optUp.textColor})},null,8,["style"]),n(U,{class:"upwarp-tip"},{default:e((()=>[s(l(T.mescroll.optUp.textLoading),1)])),_:1})])),_:1},512),[[g,1===T.upLoadType]]),2===T.upLoadType?(t(),o(U,{key:0,class:"upwarp-nodata"},{default:e((()=>[s(l(T.mescroll.optUp.textNoMore),1)])),_:1})):i("",!0)])),_:1},8,["style"])):i("",!0)])),_:1})])),_:3},8,["style","change:prop","prop"]),y.bottombar&&T.windowBottom>0?(t(),o(U,{key:1,class:"mescroll-bottombar",style:p({height:T.windowBottom+"px"})},null,8,["style"])):i("",!0),y.safearea?(t(),o(U,{key:2,class:"mescroll-safearea"})):i("",!0),n(v,{modelValue:T.isShowToTop,"onUpdate:modelValue":m[0]||(m[0]=t=>T.isShowToTop=t),option:T.mescroll.optUp.toTop,onClick:x.toTopClick},null,8,["modelValue","option","onClick"]),n(U,{"change:prop":c.renderBiz.propObserver,prop:c.wxsProp},null,8,["change:prop","prop"])])),_:3},8,["class","style","onTouchstart","onTouchmove","onTouchend","onTouchcancel","change:prop","prop"])}],["__scopeId","data-v-18deff10"]]),M={data:()=>({mescroll:null}),onPullDownRefresh(){this.mescroll&&this.mescroll.onPullDownRefresh()},onPageScroll(t){this.mescroll&&this.mescroll.onPageScroll(t)},onReachBottom(){this.mescroll&&this.mescroll.onReachBottom()},methods:{mescrollInit(t){this.mescroll=t},downCallback(){this.mescroll.optUp.use?this.mescroll.resetUpScroll():setTimeout((()=>{this.mescroll.endSuccess()}),500)},upCallback(){setTimeout((()=>{this.mescroll.endErr()}),500)}}};export{M,E as _};
