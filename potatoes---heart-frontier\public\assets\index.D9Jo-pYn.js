import{$ as e,o as t,c as a,w as l,a as s,k as o,n as i,f as n,t as r,l as d,i as c,P as m,d as u,F as p,e as y,g,L as f,M as h,b as _,Q as S,u as k,B as b,s as x,r as C,v,h as w,x as I,j as L,y as A,R as $,T}from"./index-BrSKFjFf.js";import{_ as B}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{_ as j}from"./u-icon.Bawpp3Hr.js";import{r as z}from"./uni-app.es.BT6Htq7o.js";import{A as O}from"./index.Doi9vb8z.js";import{C as N}from"./index.Bb5p0uKO.js";import{_ as P}from"./mp-html.DhDdIpoD.js";import{r as G}from"./myCoupon.HoE3rHIH.js";import{A as M}from"./index.DuZJRo5X.js";import{C as R}from"./index.C8CPl3kp.js";import{A as F,G as D}from"./GoodsStatus.B-i_9Sb3.js";import{_ as W}from"./u-tag.D-I8vdkn.js";import{h as U}from"./color.D-c1b2x3.js";import{A as E}from"./ActiveStatus.SgCP_tut.js";const H="page/detail";function Y(t){return e.get(H,{pageId:t})}const Z=B({props:{itemIndex:String,itemStyle:Object,params:Object},methods:{onTargetSearch(){this.$navTo("pages/search/index")}}},[["render",function(e,m,u,p,y,g){const f=d,h=c;return t(),a(h,{class:o(["diy-search",{sticky:u.params.sticky}])},{default:l((()=>[s(h,{class:o(["inner",u.itemStyle.searchStyle]),onClick:g.onTargetSearch},{default:l((()=>[s(h,{class:"search-input",style:i({textAlign:u.itemStyle.textAlign})},{default:l((()=>[s(f,{class:"search-icon iconfont icon-search"}),s(f,null,{default:l((()=>[n(r(u.params.placeholder),1)])),_:1})])),_:1},8,["style"])])),_:1},8,["class","onClick"])])),_:1},8,["class"])}],["__scopeId","data-v-8c93fa7a"]]),V={data:()=>({}),methods:{onLink:m}};const X=B({name:"Images",props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},mixins:[V],methods:{}},[["render",function(e,o,n,r,d,m){const f=g,h=c;return t(),a(h,{class:"diy-imageSingle",style:i({paddingBottom:2*n.itemStyle.paddingTop+"rpx",background:n.itemStyle.background})},{default:l((()=>[(t(!0),u(p,null,y(n.dataList,((o,r)=>(t(),a(h,{class:"item-image",key:r,style:i({padding:`${2*n.itemStyle.paddingTop}rpx ${2*n.itemStyle.paddingLeft}rpx 0`})},{default:l((()=>[s(h,{class:"nav-to",onClick:t=>e.onLink(o.link)},{default:l((()=>[s(f,{class:"image",src:o.imgUrl,mode:"widthFix"},null,8,["src"])])),_:2},1032,["onClick"])])),_:2},1032,["style"])))),128))])),_:1},8,["style"])}],["__scopeId","data-v-6836e2d3"]]);const Q=B({name:"Banner",props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},mixins:[V],data:()=>({windowWidth:750,indicatorDots:!1,autoplay:!0,duration:800,imgHeights:[],imgCurrent:0}),created(){const e=this;uni.getSystemInfo({success({windowWidth:t}){e.windowWidth=t>750?750:t}})},methods:{_imagesHeight({detail:e}){const{width:t,height:a}=e,l=t/a,s=this.windowWidth/l;this.imgHeights.push(s)},_bindChange(e){this.imgCurrent=e.detail.current}}},[["render",function(e,n,r,d,m,_){const S=g,k=f,b=h,x=c;return t(),a(x,{class:"diy-banner",style:i({height:`${m.imgHeights[m.imgCurrent]}px`})},{default:l((()=>[s(b,{autoplay:m.autoplay,class:"swiper-box",duration:m.duration,circular:!0,interval:1e3*r.itemStyle.interval,onChange:_._bindChange},{default:l((()=>[(t(!0),u(p,null,y(r.dataList,((o,i)=>(t(),a(k,{key:i},{default:l((()=>[s(S,{mode:"widthFix",class:"slide-image",src:o.imgUrl,onClick:t=>e.onLink(o.link),onLoad:_._imagesHeight},null,8,["src","onClick","onLoad"])])),_:2},1024)))),128))])),_:1},8,["autoplay","duration","interval","onChange"]),s(x,{class:o(["indicator-dots",r.itemStyle.btnShape])},{default:l((()=>[(t(!0),u(p,null,y(r.dataList,((e,l)=>(t(),a(x,{class:o(["dots-item",{active:m.imgCurrent==l}]),style:i({backgroundColor:r.itemStyle.btnColor}),key:l},null,8,["class","style"])))),128))])),_:1},8,["class"])])),_:1},8,["style"])}],["__scopeId","data-v-55fd6fc7"]]);const q=B({name:"Window",props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},mixins:[V],methods:{}},[["render",function(e,n,r,d,m,f){const h=g,S=c;return t(),a(S,{class:"diy-window",style:i({background:r.itemStyle.background,padding:`${2*r.itemStyle.paddingTop}rpx ${2*r.itemStyle.paddingLeft}rpx`})},{default:l((()=>[r.itemStyle.layout>-1?(t(),a(S,{key:0,class:o(["data-list",[`avg-sm-${r.itemStyle.layout}`]])},{default:l((()=>[(t(!0),u(p,null,y(r.dataList,((o,n)=>(t(),a(S,{key:n,class:"data-item",style:i({padding:`${2*r.itemStyle.paddingTop}rpx ${2*r.itemStyle.paddingLeft}rpx`})},{default:l((()=>[s(S,{class:"item-image",onClick:t=>e.onLink(o.link)},{default:l((()=>[s(h,{class:"image",mode:"widthFix",src:o.imgUrl},null,8,["src"])])),_:2},1032,["onClick"])])),_:2},1032,["style"])))),128))])),_:1},8,["class"])):(t(),a(S,{key:1,class:"display"},{default:l((()=>[s(S,{class:"display-left",style:i({padding:`${2*r.itemStyle.paddingTop}rpx ${2*r.itemStyle.paddingLeft}rpx`})},{default:l((()=>[s(h,{class:"image",onClick:n[0]||(n[0]=t=>e.onLink(r.dataList[0].link)),src:r.dataList[0].imgUrl},null,8,["src"])])),_:1},8,["style"]),s(S,{class:"display-right"},{default:l((()=>[r.dataList.length>=2?(t(),a(S,{key:0,class:"display-right1",style:i({padding:`${2*r.itemStyle.paddingTop}rpx ${2*r.itemStyle.paddingLeft}rpx`})},{default:l((()=>[s(h,{class:"image",onClick:n[1]||(n[1]=t=>e.onLink(r.dataList[1].link)),src:r.dataList[1].imgUrl},null,8,["src"])])),_:1},8,["style"])):_("",!0),s(S,{class:"display-right2"},{default:l((()=>[r.dataList.length>=3?(t(),a(S,{key:0,class:"left",style:i({padding:`${2*r.itemStyle.paddingTop}rpx ${2*r.itemStyle.paddingLeft}rpx`})},{default:l((()=>[s(h,{class:"image",onClick:n[2]||(n[2]=t=>e.onLink(r.dataList[2].link)),src:r.dataList[2].imgUrl},null,8,["src"])])),_:1},8,["style"])):_("",!0),r.dataList.length>=4?(t(),a(S,{key:1,class:"right",style:i({padding:`${2*r.itemStyle.paddingTop}rpx ${2*r.itemStyle.paddingLeft}rpx`})},{default:l((()=>[s(h,{class:"image",onClick:n[3]||(n[3]=t=>e.onLink(r.dataList[3].link)),src:r.dataList[3].imgUrl},null,8,["src"])])),_:1},8,["style"])):_("",!0)])),_:1})])),_:1})])),_:1}))])),_:1},8,["style"])}],["__scopeId","data-v-99516265"]]);const J=B({props:{itemIndex:String,itemStyle:Object,params:Object,data:Object},mixins:[V],methods:{}},[["render",function(e,o,n,r,d,m){const f=g,h=c;return t(),a(h,{class:"diy-hotZone",style:i({paddingBottom:2*n.itemStyle.paddingTop+"rpx",background:n.itemStyle.background})},{default:l((()=>[s(h,{class:"bg-image",style:i({padding:`${2*n.itemStyle.paddingTop}rpx ${2*n.itemStyle.paddingLeft}rpx 0`})},{default:l((()=>[s(f,{class:"image",src:n.data.imgUrl,mode:"widthFix"},null,8,["src"])])),_:1},8,["style"]),(t(!0),u(p,null,y(n.data.maps,((l,s)=>(t(),a(h,{class:"zone-item",key:s,style:i({width:`${l.width}rpx`,height:`${l.height}rpx`,left:`${l.left}rpx`,top:`${l.top}rpx`}),onClick:t=>e.onLink(l.link)},null,8,["style","onClick"])))),128))])),_:1},8,["style"])}],["__scopeId","data-v-ab741baa"]]);const K=B({name:"Videos",props:{itemIndex:String,itemStyle:Object,params:Object},methods:{}},[["render",function(e,o,n,r,d,m){const u=S,p=c;return t(),a(p,{class:"diy-video",style:i({padding:2*n.itemStyle.paddingTop+"rpx 0"})},{default:l((()=>[s(u,{class:"video",style:i({height:2*n.itemStyle.height+"rpx"}),src:n.params.videoUrl,poster:n.params.poster,autoplay:1==n.params.autoplay,controls:""},null,8,["style","src","poster","autoplay"])])),_:1},8,["style"])}],["__scopeId","data-v-65bf0c27"]]);const ee=B({name:"Article",props:{itemIndex:String,params:Object,dataList:Array},methods:{onTargetDetail(e){uni.navigateTo({url:"/pages/article/detail?articleId="+e})}}},[["render",function(e,i,m,f,h,S){const k=d,b=c,x=g;return t(),a(b,{class:"diy-article"},{default:l((()=>[(t(!0),u(p,null,y(m.dataList,((e,i)=>(t(),a(b,{class:o(["article-item",[`show-type__${e.show_type}`]]),key:i,onClick:t=>S.onTargetDetail(e.article_id)},{default:l((()=>[10==e.show_type?(t(),u(p,{key:0},[s(b,{class:"article-item__left flex-box"},{default:l((()=>[s(b,{class:"article-item__title"},{default:l((()=>[s(k,{class:"twoline-hide"},{default:l((()=>[n(r(e.title),1)])),_:2},1024)])),_:2},1024),s(b,{class:"article-item__footer m-top10"},{default:l((()=>[s(k,{class:"article-views f-24 col-8"},{default:l((()=>[n(r(e.show_views)+"次浏览",1)])),_:2},1024)])),_:2},1024)])),_:2},1024),s(b,{class:"article-item__image"},{default:l((()=>[s(x,{class:"image",mode:"widthFix",src:e.image_url},null,8,["src"])])),_:2},1024)],64)):_("",!0),20==e.show_type?(t(),u(p,{key:1},[s(b,{class:"article-item__title"},{default:l((()=>[s(k,{class:"twoline-hide"},{default:l((()=>[n(r(e.title),1)])),_:2},1024)])),_:2},1024),s(b,{class:"article-item__image m-top20"},{default:l((()=>[s(x,{class:"image",mode:"widthFix",src:e.image_url},null,8,["src"])])),_:2},1024),s(b,{class:"article-item__footer m-top10"},{default:l((()=>[s(k,{class:"article-views f-24 col-8"},{default:l((()=>[n(r(e.show_views)+"次浏览",1)])),_:2},1024)])),_:2},1024)],64)):_("",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1})}],["__scopeId","data-v-ce7b26a5"]]);const te=B({emits:["close","getMore"],props:{list:{type:Array,default:()=>[]},type:{type:String,default:"warning"},volumeIcon:{type:Boolean,default:!0},moreIcon:{type:Boolean,default:!1},closeIcon:{type:Boolean,default:!1},autoplay:{type:Boolean,default:!0},color:{type:String,default:""},bgColor:{type:String,default:""},show:{type:Boolean,default:!0},fontSize:{type:[Number,String],default:26},volumeSize:{type:[Number,String],default:34},speed:{type:[Number,String],default:160},playState:{type:String,default:"play"},padding:{type:[Number,String],default:"18rpx 24rpx"}},data:()=>({textWidth:0,boxWidth:0,animationDuration:"10s",animationPlayState:"paused",showText:""}),watch:{list:{immediate:!0,handler(e){this.showText=e.join("，"),this.$nextTick((()=>{this.initSize()}))}},playState(e){this.animationPlayState="play"==e?"running":"paused"},speed(e){this.initSize()}},computed:{computeColor(){return this.color?this.color:"none"==this.type?"#606266":this.type},textStyle(){let e={};return this.color?e.color=this.color:"none"==this.type&&(e.color="#606266"),e.fontSize=this.fontSize+"rpx",e},computeBgColor(){return this.bgColor?this.bgColor:"none"==this.type?"transparent":void 0}},mounted(){this.$nextTick((()=>{this.initSize()}))},methods:{initSize(){let e=[],t=new Promise(((e,t)=>{uni.createSelectorQuery().in(this).select("#u-notice-content").boundingClientRect().exec((t=>{this.textWidth=t[0].width,e()}))}));e.push(t),Promise.all(e).then((()=>{this.animationDuration=this.textWidth/uni.upx2px(this.speed)+"s",this.animationPlayState="paused",setTimeout((()=>{"play"==this.playState&&this.autoplay&&(this.animationPlayState="running")}),10)}))},click(e){this.$emit("click")},close(){this.$emit("close")},getMore(){this.$emit("getMore")}}},[["render",function(e,m,u,p,y,g){const f=z(k("u-icon"),j),h=c,S=d;return u.show?(t(),a(h,{key:0,class:o(["u-notice-bar",[u.type?`u-type-${u.type}-light-bg`:""]]),style:i({background:g.computeBgColor,padding:u.padding})},{default:l((()=>[s(h,{class:"u-direction-row"},{default:l((()=>[s(h,{class:"u-icon-wrap"},{default:l((()=>[u.volumeIcon?(t(),a(f,{key:0,class:"u-left-icon",name:"volume-fill",size:u.volumeSize,color:g.computeColor},null,8,["size","color"])):_("",!0)])),_:1}),s(h,{class:"u-notice-box",id:"u-notice-box"},{default:l((()=>[s(h,{class:"u-notice-content",id:"u-notice-content",style:i({animationDuration:y.animationDuration,animationPlayState:y.animationPlayState})},{default:l((()=>[s(S,{class:o(["u-notice-text",["u-type-"+u.type]]),onClick:g.click,style:i([g.textStyle])},{default:l((()=>[n(r(y.showText),1)])),_:1},8,["onClick","style","class"])])),_:1},8,["style"])])),_:1}),s(h,{class:"u-icon-wrap"},{default:l((()=>[u.moreIcon?(t(),a(f,{key:0,onClick:g.getMore,class:"u-right-icon",name:"arrow-right",size:26,color:g.computeColor},null,8,["onClick","color"])):_("",!0),u.closeIcon?(t(),a(f,{key:1,onClick:g.close,class:"u-right-icon",name:"close",size:24,color:g.computeColor},null,8,["onClick","color"])):_("",!0)])),_:1})])),_:1})])),_:1},8,["style","class"])):_("",!0)}],["__scopeId","data-v-834500a9"]]);const ae=B({emits:["close","getMore","end"],props:{list:{type:Array,default:()=>[]},type:{type:String,default:"warning"},volumeIcon:{type:Boolean,default:!0},moreIcon:{type:Boolean,default:!1},closeIcon:{type:Boolean,default:!1},autoplay:{type:Boolean,default:!0},color:{type:String,default:""},bgColor:{type:String,default:""},direction:{type:String,default:"row"},show:{type:Boolean,default:!0},fontSize:{type:[Number,String],default:26},duration:{type:[Number,String],default:2e3},volumeSize:{type:[Number,String],default:34},speed:{type:Number,default:160},isCircular:{type:Boolean,default:!0},mode:{type:String,default:"horizontal"},playState:{type:String,default:"play"},disableTouch:{type:Boolean,default:!0},padding:{type:[Number,String],default:"18rpx 24rpx"}},computed:{computeColor(){return this.color?this.color:"none"==this.type?"#606266":this.type},textStyle(){let e={};return this.color?e.color=this.color:"none"==this.type&&(e.color="#606266"),e.fontSize=this.fontSize+"rpx",e},vertical(){return"horizontal"!=this.mode},computeBgColor(){return this.bgColor?this.bgColor:"none"==this.type?"transparent":void 0}},data:()=>({}),methods:{click(e){this.$emit("click",e)},close(){this.$emit("close")},getMore(){this.$emit("getMore")},change(e){e.detail.current==this.list.length-1&&this.$emit("end")}}},[["render",function(e,d,m,g,S,b){const x=z(k("u-icon"),j),C=c,v=f,w=h;return t(),a(C,{class:o(["u-notice-bar",[m.type?`u-type-${m.type}-light-bg`:""]]),style:i({background:b.computeBgColor,padding:m.padding})},{default:l((()=>[s(C,{class:"u-icon-wrap"},{default:l((()=>[m.volumeIcon?(t(),a(x,{key:0,class:"u-left-icon",name:"volume-fill",size:m.volumeSize,color:b.computeColor},null,8,["size","color"])):_("",!0)])),_:1}),s(w,{"disable-touch":m.disableTouch,onChange:b.change,autoplay:m.autoplay&&"play"==m.playState,vertical:b.vertical,circular:"",interval:m.duration,class:"u-swiper"},{default:l((()=>[(t(!0),u(p,null,y(m.list,((e,d)=>(t(),a(v,{key:d,class:"u-swiper-item"},{default:l((()=>[s(C,{class:o(["u-news-item u-line-1",["u-type-"+m.type]]),style:i([b.textStyle]),onClick:e=>b.click(d)},{default:l((()=>[n(r(e),1)])),_:2},1032,["style","onClick","class"])])),_:2},1024)))),128))])),_:1},8,["disable-touch","onChange","autoplay","vertical","interval"]),s(C,{class:"u-icon-wrap"},{default:l((()=>[m.moreIcon?(t(),a(x,{key:0,onClick:b.getMore,class:"u-right-icon",name:"arrow-right",size:26,color:b.computeColor},null,8,["onClick","color"])):_("",!0),m.closeIcon?(t(),a(x,{key:1,onClick:b.close,class:"u-right-icon",name:"close",size:24,color:b.computeColor},null,8,["onClick","color"])):_("",!0)])),_:1})])),_:1},8,["style","class"])}],["__scopeId","data-v-d897bbfb"]]);const le=B({name:"u-notice-bar",emits:["click","close","getMore","end"],props:{list:{type:Array,default:()=>[]},type:{type:String,default:"warning"},volumeIcon:{type:Boolean,default:!0},volumeSize:{type:[Number,String],default:34},moreIcon:{type:Boolean,default:!1},closeIcon:{type:Boolean,default:!1},autoplay:{type:Boolean,default:!0},color:{type:String,default:""},bgColor:{type:String,default:""},mode:{type:String,default:"horizontal"},show:{type:Boolean,default:!0},fontSize:{type:[Number,String],default:28},duration:{type:[Number,String],default:2e3},speed:{type:[Number,String],default:160},isCircular:{type:Boolean,default:!0},playState:{type:String,default:"play"},disableTouch:{type:Boolean,default:!0},borderRadius:{type:[Number,String],default:0},padding:{type:[Number,String],default:"18rpx 24rpx"},noListHidden:{type:Boolean,default:!0}},computed:{isShow(){return 0!=this.show&&(1!=this.noListHidden||0!=this.list.length)}},methods:{click(e){this.$emit("click",e)},close(){this.$emit("close")},getMore(){this.$emit("getMore")},end(){this.$emit("end")}}},[["render",function(e,s,o,n,r,d){const m=z(k("u-row-notice"),te),u=z(k("u-column-notice"),ae),p=c;return d.isShow?(t(),a(p,{key:0,class:"u-notice-bar-wrap",style:i({borderRadius:o.borderRadius+"rpx"})},{default:l((()=>["horizontal"==o.mode&&o.isCircular?(t(),a(m,{key:0,type:o.type,color:o.color,bgColor:o.bgColor,list:o.list,volumeIcon:o.volumeIcon,moreIcon:o.moreIcon,volumeSize:o.volumeSize,closeIcon:o.closeIcon,mode:o.mode,fontSize:o.fontSize,speed:o.speed,playState:o.playState,padding:o.padding,onGetMore:d.getMore,onClose:d.close,onClick:d.click},null,8,["type","color","bgColor","list","volumeIcon","moreIcon","volumeSize","closeIcon","mode","fontSize","speed","playState","padding","onGetMore","onClose","onClick"])):_("",!0),"vertical"==o.mode||"horizontal"==o.mode&&!o.isCircular?(t(),a(u,{key:1,type:o.type,color:o.color,bgColor:o.bgColor,list:o.list,volumeIcon:o.volumeIcon,moreIcon:o.moreIcon,closeIcon:o.closeIcon,mode:o.mode,volumeSize:o.volumeSize,"disable-touch":o.disableTouch,fontSize:o.fontSize,duration:o.duration,playState:o.playState,padding:o.padding,onGetMore:d.getMore,onClose:d.close,onClick:d.click,onEnd:d.end},null,8,["type","color","bgColor","list","volumeIcon","moreIcon","closeIcon","mode","volumeSize","disable-touch","fontSize","duration","playState","padding","onGetMore","onClose","onClick","onEnd"])):_("",!0)])),_:1},8,["style"])):_("",!0)}],["__scopeId","data-v-7d93799d"]]);const se=B({components:{Search:Z,Images:X,Banner:Q,Window:q,HotZone:J,Videos:K,Article:ee,Notice:B({props:{itemStyle:Object,params:Object},mixins:[V],methods:{}},[["render",function(e,o,n,r,d,m){const u=z(k("u-notice-bar"),le),p=c;return t(),a(p,{class:"diy-notice",style:i({paddingTop:2*n.itemStyle.paddingTop+"rpx",paddingBottom:2*n.itemStyle.paddingTop+"rpx"}),onClick:o[0]||(o[0]=t=>e.onLink(n.params.link))},{default:l((()=>[s(u,{padding:"10rpx 24rpx","volume-icon":n.params.showIcon,autoplay:n.params.scrollable,"bg-color":n.itemStyle.background,color:n.itemStyle.textColor,list:[n.params.text]},null,8,["volume-icon","autoplay","bg-color","color","list"])])),_:1},8,["style"])}]]),NavBar:B({name:"NavBar",props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},mixins:[V],methods:{}},[["render",function(e,d,m,f,h,_){const S=g,k=c;return t(),a(k,{class:"diy-navBar",style:i({background:m.itemStyle.background,color:m.itemStyle.textColor})},{default:l((()=>[s(k,{class:o(["data-list",[`avg-sm-${m.itemStyle.rowsNum}`]])},{default:l((()=>[(t(!0),u(p,null,y(m.dataList,((o,i)=>(t(),a(k,{class:"item-nav",key:i},{default:l((()=>[s(k,{class:"nav-to",onClick:t=>e.onLink(o.link)},{default:l((()=>[s(k,{class:"item-image"},{default:l((()=>[s(S,{class:"image",mode:"widthFix",src:o.imgUrl},null,8,["src"])])),_:2},1024),s(k,{class:"item-text oneline-hide"},{default:l((()=>[n(r(o.text),1)])),_:2},1024)])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1},8,["class"])])),_:1},8,["style"])}],["__scopeId","data-v-110b6f51"]]),Goods:B({components:{AddCartPopup:O},props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},data:()=>({inArray:b}),methods:{handleGoodsItem(e){this.$navTo("pages/goods/detail",{goodsId:e})},handleAddCart(e){this.$refs.AddCartPopup.handle(e)},onAddCart(e){x()}}},[["render",function(e,m,f,h,S,k){const b=g,x=c,A=d,$=C("AddCartPopup"),T=L;return t(),a(T,{class:"scroll-view","scroll-x":"slide"===f.itemStyle.display,style:i({background:f.itemStyle.background})},{default:l((()=>[s(x,{class:"diy-goods"},{default:l((()=>[s(x,{class:o(["goods-list",[`display-${f.itemStyle.display}`,`column-${f.itemStyle.column}`]]),style:i({padding:`${2*f.itemStyle.paddingY}rpx ${2*f.itemStyle.paddingX}rpx`})},{default:l((()=>[(t(!0),u(p,null,y(f.dataList,((e,d)=>(t(),a(x,{class:o(["goods-item",[`display-${f.itemStyle.cardType}`]]),key:d,style:i({marginBottom:2*f.itemStyle.itemMargin+"rpx",borderRadius:2*f.itemStyle.borderRadius+"rpx"}),onClick:t=>k.handleGoodsItem(e.goods_id)},{default:l((()=>[1===f.itemStyle.column?(t(),a(x,{key:0,class:"flex"},{default:l((()=>[s(x,{class:"goods-item-left"},{default:l((()=>[s(b,{class:"image",src:e.goods_image},null,8,["src"])])),_:2},1024),s(x,{class:"goods-item-right"},{default:l((()=>[s(x,{class:"goods-info"},{default:l((()=>[S.inArray("goodsName",f.itemStyle.show)?(t(),a(x,{key:0,class:"goods-name twoline-hide"},{default:l((()=>[n(r(e.goods_name),1)])),_:2},1024)):_("",!0),S.inArray("sellingPoint",f.itemStyle.show)?(t(),a(x,{key:1,class:"goods-selling"},{default:l((()=>[s(A,{class:"selling oneline-hide",style:i({color:f.itemStyle.sellingColor})},{default:l((()=>[n(r(e.selling_point),1)])),_:2},1032,["style"])])),_:2},1024)):_("",!0),S.inArray("goodsSales",f.itemStyle.show)?(t(),a(x,{key:2,class:"goods-sales oneline-hide"},{default:l((()=>[s(A,{class:"sales"},{default:l((()=>[n("已售"+r(e.goods_sales),1)])),_:2},1024)])),_:2},1024)):_("",!0),s(x,{class:"footer"},{default:l((()=>[s(x,{class:"goods-price oneline-hide",style:i({color:f.itemStyle.priceColor})},{default:l((()=>[S.inArray("goodsPrice",f.itemStyle.show)?(t(),u(p,{key:0},[s(A,{class:"unit"},{default:l((()=>[n("￥")])),_:1}),s(A,{class:"value"},{default:l((()=>[n(r(e.goods_price_min),1)])),_:2},1024)],64)):_("",!0),S.inArray("linePrice",f.itemStyle.show)?(t(),a(A,{key:1,class:"line-price"},{default:l((()=>[s(A,{class:"unit"},{default:l((()=>[n("￥")])),_:1}),s(A,{class:"value"},{default:l((()=>[n(r(e.line_price_min),1)])),_:2},1024)])),_:2},1024)):_("",!0)])),_:2},1032,["style"]),v(s(x,{class:"action"},{default:l((()=>[s(x,{class:"btn-cart",style:i({background:f.itemStyle.btnCartColor,color:f.itemStyle.btnFontColor}),onClick:w((t=>k.handleAddCart(e)),["stop"])},{default:l((()=>[s(A,{class:"cart-icon iconfont icon-plus"})])),_:2},1032,["style","onClick"])])),_:2},1536),[[I,S.inArray("cartBtn",f.itemStyle.show)&&f.itemStyle.column<3]])])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)):(t(),u(p,{key:1},[s(x,{class:"goods-image"},{default:l((()=>[s(b,{class:"image",mode:"aspectFill",src:e.goods_image},null,8,["src"])])),_:2},1024),s(x,{class:"goods-info"},{default:l((()=>[S.inArray("goodsName",f.itemStyle.show)?(t(),a(x,{key:0,class:"goods-name twoline-hide"},{default:l((()=>[n(r(e.goods_name),1)])),_:2},1024)):_("",!0),S.inArray("sellingPoint",f.itemStyle.show)?(t(),a(x,{key:1,class:"goods-selling"},{default:l((()=>[s(A,{class:"selling oneline-hide",style:i({color:f.itemStyle.sellingColor})},{default:l((()=>[n(r(e.selling_point),1)])),_:2},1032,["style"])])),_:2},1024)):_("",!0),S.inArray("goodsSales",f.itemStyle.show)?(t(),a(x,{key:2,class:"goods-sales oneline-hide"},{default:l((()=>[s(A,{class:"sales"},{default:l((()=>[n("已售"+r(e.goods_sales),1)])),_:2},1024)])),_:2},1024)):_("",!0),s(x,{class:"footer"},{default:l((()=>[S.inArray("goodsPrice",f.itemStyle.show)?(t(),a(x,{key:0,class:"goods-price oneline-hide",style:i({color:f.itemStyle.priceColor})},{default:l((()=>[s(A,{class:"unit"},{default:l((()=>[n("￥")])),_:1}),s(A,{class:"value"},{default:l((()=>[n(r(e.goods_price_min),1)])),_:2},1024),S.inArray("linePrice",f.itemStyle.show)?(t(),a(A,{key:0,class:"line-price"},{default:l((()=>[s(A,{class:"unit"},{default:l((()=>[n("￥")])),_:1}),s(A,{class:"value"},{default:l((()=>[n(r(e.line_price_min),1)])),_:2},1024)])),_:2},1024)):_("",!0)])),_:2},1032,["style"])):_("",!0),v(s(x,{class:"action"},{default:l((()=>[s(x,{class:"btn-cart",style:i({background:f.itemStyle.btnCartColor,color:f.itemStyle.btnFontColor}),onClick:w((t=>k.handleAddCart(e)),["stop"])},{default:l((()=>[s(A,{class:"cart-icon iconfont icon-plus"})])),_:2},1032,["style","onClick"])])),_:2},1536),[[I,S.inArray("cartBtn",f.itemStyle.show)&&f.itemStyle.column<3]])])),_:2},1024)])),_:2},1024)],64))])),_:2},1032,["class","style","onClick"])))),128))])),_:1},8,["class","style"])])),_:1}),s($,{ref:"AddCartPopup",onAddCart:k.onAddCart},null,8,["onAddCart"])])),_:1},8,["scroll-x","style"])}],["__scopeId","data-v-8b6c15c6"]]),Service:B({components:{CustomerBtn:N},props:{itemStyle:Object,params:Object},data:()=>({isShow:!1}),async created(){"phone"===this.params.type&&(this.isShow=!0),"chat"===this.params.type&&(this.isShow=await A.isShowCustomerBtn())},methods:{onMakePhoneCall(e){uni.makePhoneCall({phoneNumber:this.params.tel})}}},[["render",function(e,o,n,r,d,m){const u=g,p=c,y=C("customer-btn");return d.isShow?(t(),a(p,{key:0,class:"diy-service",style:i({"--right":2*n.itemStyle.right+"rpx","--bottom":2*n.itemStyle.bottom+"rpx"})},{default:l((()=>["phone"===n.params.type?(t(),a(p,{key:0,class:"service-icon",onClick:m.onMakePhoneCall},{default:l((()=>[s(u,{class:"image",src:n.params.image},null,8,["src"])])),_:1},8,["onClick"])):"chat"===n.params.type?(t(),a(y,{key:1},{default:l((()=>[s(p,{class:"service-icon"},{default:l((()=>[s(u,{class:"image",src:n.params.image},null,8,["src"])])),_:1})])),_:1})):_("",!0)])),_:1},8,["style"])):_("",!0)}],["__scopeId","data-v-ae14c3bb"]]),Blank:B({props:{itemStyle:Object},methods:{}},[["render",function(e,l,s,o,n,r){const d=c;return t(),a(d,{class:"diy-blank",style:i({height:2*s.itemStyle.height+"rpx",background:s.itemStyle.background})},null,8,["style"])}]]),Guide:B({props:{itemStyle:Object},methods:{}},[["render",function(e,o,n,r,d,m){const u=c;return t(),a(u,{class:"diy-guide",style:i({padding:2*n.itemStyle.paddingTop+"rpx 0",background:n.itemStyle.background})},{default:l((()=>[s(u,{class:"line",style:i({borderTop:`${2*n.itemStyle.lineHeight}rpx ${n.itemStyle.lineStyle} ${n.itemStyle.lineColor}`})},null,8,["style"])])),_:1},8,["style"])}],["__scopeId","data-v-dc3cca30"]]),RichText:B({props:{itemStyle:Object,params:Object},methods:{}},[["render",function(e,o,n,r,d,m){const u=z(k("mp-html"),P),p=c;return t(),a(p,{class:"diy-richText",style:i({padding:`${2*n.itemStyle.paddingTop}rpx ${2*n.itemStyle.paddingLeft}rpx`,background:n.itemStyle.background})},{default:l((()=>[s(u,{content:n.params.content},null,8,["content"])])),_:1},8,["style"])}],["__scopeId","data-v-a71b9a86"]]),Special:B({props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},mixins:[V],methods:{handleNavDetail(e){this.$navTo("pages/article/detail",{articleId:e})},handleNavMore(){this.$navTo("pages/article/index")}}},[["render",function(e,m,_,S,k,b){const x=g,C=c,v=d,w=f,I=h;return t(),a(C,{class:"diy-special",style:i({padding:2*_.itemStyle.paddingTop+"rpx 0",background:_.itemStyle.background})},{default:l((()=>[s(C,{class:"special-left",onClick:m[0]||(m[0]=e=>b.handleNavMore())},{default:l((()=>[s(x,{class:"image",mode:"widthFix",src:_.params.image},null,8,["src"])])),_:1}),$("div",{class:o(["special-content",[`display_${_.params.display}`]])},[s(I,{autoplay:!0,interval:1500,duration:800,circular:!0,vertical:!0,"display-multiple-items":_.itemStyle.display},{default:l((()=>[(t(!0),u(p,null,y(_.dataList,((e,o)=>(t(),a(w,{key:o},{default:l((()=>[s(C,{class:"content-item oneline-hide",onClick:t=>b.handleNavDetail(e.article_id)},{default:l((()=>[s(v,{style:i({color:_.itemStyle.textColor})},{default:l((()=>[n(r(e.title),1)])),_:2},1032,["style"])])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1},8,["display-multiple-items"])],2),$("div",{class:"special-more",onClick:m[1]||(m[1]=e=>b.handleNavMore())},[s(v,{class:"iconfont icon-arrow-right"})])])),_:1},8,["style"])}],["__scopeId","data-v-32110c36"]]),DiyOfficialAccount:B({props:{itemIndex:String},mixins:[V],methods:{}},[["render",function(e,t,a,l,s,o){return null}]]),Shop:B({props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},mixins:[V],methods:{handleNavDetail(e){this.$navTo("pages/shop/detail",{shopId:e})}}},[["render",function(e,o,i,m,f,h){const _=g,S=c,k=d;return t(),a(S,{class:"diy-shop"},{default:l((()=>[(t(!0),u(p,null,y(i.dataList,((e,o)=>(t(),a(S,{class:"shop-item dis-flex flex-y-center",key:o,onClick:t=>h.handleNavDetail(e.shop_id)},{default:l((()=>[s(S,{class:"shop-item__logo"},{default:l((()=>[s(_,{class:"image",src:e.logo_url},null,8,["src"])])),_:2},1024),s(S,{class:"shop-item__content"},{default:l((()=>[s(S,{class:"shop-item__title"},{default:l((()=>[s(k,null,{default:l((()=>[n(r(e.shop_name),1)])),_:2},1024)])),_:2},1024),s(S,{class:"shop-item__address oneline-hide"},{default:l((()=>[s(k,null,{default:l((()=>[n("门店地址："+r(e.region.province)+r(e.region.city)+r(e.region.region)+r(e.address),1)])),_:2},1024)])),_:2},1024),s(S,{class:"shop-item__phone"},{default:l((()=>[s(k,null,{default:l((()=>[n("联系电话："+r(e.phone),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})}],["__scopeId","data-v-5ddee39c"]]),Coupon:B({props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},data:()=>({couponList:[],disable:!1}),watch:{dataList:{handler(e){this.couponList=T(e)},immediate:!0,deep:!0}},mixins:[V],methods:{handleReceive(e,t){const a=this;!a.disable&&t.state.value&&(a.disable=!0,G(t.coupon_id,{},{load:!1}).then((l=>{a.$success(l.message),a.setReceived(e,t)})).finally((()=>a.disable=!1)))},setReceived(e,t){this.couponList[e]={...t,state:{value:0,text:"已领取"}}}}},[["render",function(e,m,g,f,h,S){const k=d,b=c,x=L;return h.couponList.length?(t(),a(b,{key:0,class:"diy-coupon",style:i({padding:2*g.itemStyle.paddingTop+"rpx 0",background:g.itemStyle.background})},{default:l((()=>[s(x,{"scroll-x":!0},{default:l((()=>[s(b,{class:"coupon-wrapper"},{default:l((()=>[(t(!0),u(p,null,y(h.couponList,((e,d)=>(t(),a(b,{class:o(["coupon-item",{disable:!e.state.value}]),key:d,style:i({marginRight:2*g.itemStyle.marginRight+"rpx"})},{default:l((()=>[s(k,{class:"before",style:i({background:g.itemStyle.background})},null,8,["style"]),s(b,{class:"left-content",style:i({background:g.itemStyle.couponBgColor,color:g.itemStyle.couponTextColor})},{default:l((()=>[s(b,{class:"content-top"},{default:l((()=>[10==e.coupon_type?(t(),u(p,{key:0},[s(k,{class:"unit"},{default:l((()=>[n("￥")])),_:1}),s(k,{class:"price"},{default:l((()=>[n(r(e.reduce_price),1)])),_:2},1024)],64)):_("",!0),20==e.coupon_type?(t(),a(k,{key:1,class:"price"},{default:l((()=>[n(r(e.discount)+"折",1)])),_:2},1024)):_("",!0)])),_:2},1024),s(b,{class:"content-bottom"},{default:l((()=>[s(k,{class:"f-22"},{default:l((()=>[n("满"+r(e.min_price)+"元可用",1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["style"]),s(b,{class:"right-receive",style:i({background:g.itemStyle.receiveBgColor,color:g.itemStyle.receiveTextColor}),onClick:t=>S.handleReceive(d,e)},{default:l((()=>[e.state.value?(t(),u(p,{key:0},[s(k,null,{default:l((()=>[n("立即")])),_:1}),s(k,null,{default:l((()=>[n("领取")])),_:1})],64)):(t(),a(k,{key:1},{default:l((()=>[n(r(e.state.text),1)])),_:2},1024))])),_:2},1032,["style","onClick"])])),_:2},1032,["class","style"])))),128))])),_:1})])),_:1})])),_:1},8,["style"])):_("",!0)}],["__scopeId","data-v-7747d7a2"]]),Bargain:B({components:{AvatarImage:M},props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},data:()=>({inArray:b}),mixins:[V],methods:{handleNavDetail(e){this.$navTo("pages/bargain/goods/index",{activeId:e.active_id,goodsId:e.goods_id})}}},[["render",function(e,o,m,f,h,S){const k=g,b=c,x=d,v=C("avatar-image");return t(),a(b,{class:"diy-bargain",style:i({background:m.itemStyle.background})},{default:l((()=>[(t(!0),u(p,null,y(m.dataList,((e,o)=>(t(),a(b,{class:"goods-item",key:o,onClick:t=>S.handleNavDetail(e)},{default:l((()=>[s(b,{class:"goods-image"},{default:l((()=>[s(k,{class:"image",src:e.goods_image},null,8,["src"])])),_:2},1024),s(b,{class:"goods-info"},{default:l((()=>[h.inArray("goodsName",m.itemStyle.show)?(t(),a(b,{key:0,class:"goods-name"},{default:l((()=>[s(x,{class:"twoline-hide"},{default:l((()=>[n(r(e.goods_name),1)])),_:2},1024)])),_:2},1024)):_("",!0),h.inArray("peoples",m.itemStyle.show)&&e.helpsCount?(t(),a(b,{key:1,class:"peoples"},{default:l((()=>[s(b,{class:"user-list"},{default:l((()=>[(t(!0),u(p,null,y(e.helpList,((e,o)=>(t(),a(b,{key:o,class:"user-item-avatar"},{default:l((()=>[s(v,{url:e.user.avatar_url,width:32},null,8,["url"])])),_:2},1024)))),128))])),_:2},1024),s(b,{class:"people__text"},{default:l((()=>[s(x,null,{default:l((()=>[n(r(e.helpsCount)+"人正在砍价",1)])),_:2},1024)])),_:2},1024)])),_:2},1024)):_("",!0),h.inArray("originalPrice",m.itemStyle.show)?(t(),a(b,{key:2,class:"goods-price"},{default:l((()=>[s(x,null,{default:l((()=>[n("￥"+r(e.original_price),1)])),_:2},1024)])),_:2},1024)):_("",!0),h.inArray("floorPrice",m.itemStyle.show)?(t(),a(b,{key:3,class:"floor-price"},{default:l((()=>[s(x,{class:"small"},{default:l((()=>[n("最低￥")])),_:1}),s(x,{class:"big"},{default:l((()=>[n(r(e.floor_price),1)])),_:2},1024)])),_:2},1024)):_("",!0),s(b,{class:"opt-touch"},{default:l((()=>[s(b,{class:"touch-btn"},{default:l((()=>[s(x,null,{default:l((()=>[n("立即参加")])),_:1})])),_:1})])),_:1})])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1},8,["style"])}],["__scopeId","data-v-f9341955"]]),Sharp:B({components:{CountDown:R},props:{itemIndex:String,itemStyle:Object,params:Object,data:Object},data:()=>({ActiveStatusEnum:F,GoodsStatusEnum:D,inArray:b}),mixins:[V],methods:{handleNavMore(){this.$navTo("pages/sharp/index")},handleNavDetail(e){const{data:t}=this;this.$navTo("pages/sharp/goods/index",{activeTimeId:t.active.active_time_id,sharpGoodsId:e})}}},[["render",function(e,m,f,h,S,k){const b=d,x=c,v=C("count-down"),w=g;return f.data.goodsList.data&&f.data.goodsList.data.length?(t(),a(x,{key:0,class:"diy-sharp",style:i({background:f.itemStyle.background})},{default:l((()=>[s(x,{class:"sharp-top",onClick:m[0]||(m[0]=e=>k.handleNavMore())},{default:l((()=>[s(x,{class:"sharp-top--left"},{default:l((()=>[s(x,{class:"sharp-modular"},{default:l((()=>[s(b,{class:"iconfont icon-miaosha-b"}),s(b,{class:"modular-name"},{default:l((()=>[n("限时秒杀")])),_:1})])),_:1}),s(x,{class:"sharp-active-status"},{default:l((()=>[s(b,null,{default:l((()=>[n(r(f.data.active.sharp_modular_text),1)])),_:1})])),_:1}),f.data.active.status==S.GoodsStatusEnum.STATE_BEGIN.value?(t(),a(x,{key:0,class:"active-count-down"},{default:l((()=>[s(v,{date:f.data.active.count_down_time,separator:"colon",theme:"custom"},null,8,["date"])])),_:1})):_("",!0)])),_:1}),s(x,{class:"sharp-top--right"},{default:l((()=>[s(x,{class:"sharp-more"},{default:l((()=>[s(b,{class:"sharp-more-text"},{default:l((()=>[n("更多")])),_:1}),s(b,{class:"sharp-more-arrow iconfont icon-arrow-right"})])),_:1})])),_:1})])),_:1}),s(x,{class:o(["goods-list display__list clearfix",[`column__${f.itemStyle.column}`]])},{default:l((()=>[(t(!0),u(p,null,y(f.data.goodsList.data,((e,o)=>(t(),a(x,{class:"goods-item",key:o,onClick:t=>k.handleNavDetail(e.sharp_goods_id)},{default:l((()=>[1==f.itemStyle.column?(t(),u(p,{key:0},[],64)):(t(),u(p,{key:1},[s(x,{class:"goods-image"},{default:l((()=>[s(w,{class:"image",mode:"aspectFill",src:e.goods_image},null,8,["src"])])),_:2},1024),s(x,{class:"detail"},{default:l((()=>[S.inArray("goodsName",f.itemStyle.show)?(t(),a(x,{key:0,class:"goods-name"},{default:l((()=>[s(b,{class:"twoline-hide"},{default:l((()=>[n(r(e.goods_name),1)])),_:2},1024)])),_:2},1024)):_("",!0),s(x,{class:"detail-price"},{default:l((()=>[S.inArray("seckillPrice",f.itemStyle.show)?(t(),a(b,{key:0,class:"goods-price c-red"},{default:l((()=>[s(b,{class:"small-unit"},{default:l((()=>[n("¥")])),_:1}),s(b,null,{default:l((()=>[n(r(e.seckill_price_min),1)])),_:2},1024)])),_:2},1024)):_("",!0),S.inArray("originalPrice",f.itemStyle.show)&&e.original_price>0?(t(),a(b,{key:1,class:"line-price"},{default:l((()=>[n("￥"+r(e.original_price),1)])),_:2},1024)):_("",!0)])),_:2},1024)])),_:2},1024)],64))])),_:2},1032,["onClick"])))),128))])),_:1},8,["class"])])),_:1},8,["style"])):_("",!0)}],["__scopeId","data-v-3badc385"]]),Groupon:B({components:{},mixins:[V],props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},data:()=>({inArray:b,ActiveTypeEnum:E}),computed:{tagBackgroundColor(){return U(this.appTheme.mainBg,.1)},tagBorderColor(){return U(this.appTheme.mainBg,.6)}},methods:{onTargetGoods(e){this.$navTo("pages/groupon/goods/index",{grouponGoodsId:e.groupon_goods_id})}}},[["render",function(e,m,f,h,S,b){const x=d,C=c,v=g,w=z(k("u-tag"),W);return t(),a(C,{class:"diy-groupon",style:i({background:f.itemStyle.background,padding:`${2*f.itemStyle.paddingY}rpx ${2*f.itemStyle.paddingX}rpx`})},{default:l((()=>[(t(!0),u(p,null,y(f.dataList,((d,c)=>(t(),a(C,{class:"goods-item--container",key:c,style:i({marginBottom:2*f.itemStyle.itemMargin+"rpx"})},{default:l((()=>[s(C,{class:o(["goods-item",[`display-${f.itemStyle.display}`,`border-${f.itemStyle.itemBorderRadius}`]]),onClick:e=>b.onTargetGoods(d)},{default:l((()=>[s(C,{class:"goods-item-left"},{default:l((()=>[d.active_type!=S.ActiveTypeEnum.NORMAL.value?(t(),a(C,{key:0,class:"label"},{default:l((()=>[s(x,null,{default:l((()=>[n(r(S.ActiveTypeEnum[d.active_type].name2),1)])),_:2},1024)])),_:2},1024)):_("",!0),s(v,{class:"image",src:d.goods_image},null,8,["src"])])),_:2},1024),s(C,{class:"goods-item-right"},{default:l((()=>[S.inArray("goodsName",f.itemStyle.show)?(t(),a(C,{key:0,class:"goods-name"},{default:l((()=>[s(x,{class:"twoline-hide"},{default:l((()=>[n(r(d.goods_name),1)])),_:2},1024)])),_:2},1024)):_("",!0),s(C,{class:"goods-item-desc"},{default:l((()=>[s(C,{class:"desc_situation"},{default:l((()=>[s(C,{class:"state-tag"},{default:l((()=>[S.inArray("peoples",f.itemStyle.show)?(t(),a(w,{key:0,color:e.appTheme.mainBg,"border-color":e.appTheme.mainBg,text:`${d.show_people}人团`,type:"error",size:"mini",mode:"plain"},null,8,["color","border-color","text"])):_("",!0)])),_:2},1024),s(C,{class:"state-tag"},{default:l((()=>[S.inArray("activeSales",f.itemStyle.show)&&d.active_sales?(t(),a(w,{key:0,color:e.appTheme.mainBg,"border-color":b.tagBorderColor,"bg-color":b.tagBackgroundColor,text:`已团${d.active_sales}件`,type:"error",size:"mini"},null,8,["color","border-color","bg-color","text"])):_("",!0)])),_:2},1024)])),_:2},1024),s(C,{class:"desc-footer"},{default:l((()=>[s(C,{class:"item-prices oneline-hide"},{default:l((()=>[S.inArray("grouponPrice",f.itemStyle.show)?(t(),a(x,{key:0,class:"price-x"},{default:l((()=>[n("¥"+r(d.groupon_price),1)])),_:2},1024)):_("",!0),S.inArray("grouponPrice",f.itemStyle.show)?(t(),a(x,{key:1,class:"price-y cl-9"},{default:l((()=>[n("¥"+r(d.original_price),1)])),_:2},1024)):_("",!0)])),_:2},1024),S.inArray("button",f.itemStyle.show)?(t(),a(C,{key:0,class:"settlement"},{default:l((()=>[n("去拼团")])),_:1})):_("",!0)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick","class"])])),_:2},1032,["style"])))),128))])),_:1},8,["style"])}],["__scopeId","data-v-94dcee0e"]]),ICPLicense:B({props:{itemStyle:Object,params:Object},mixins:[V],methods:{}},[["render",function(e,o,m,u,p,y){const g=d,f=c;return t(),a(f,{class:"diy-ICPLicense",style:i({padding:`${2*m.itemStyle.paddingTop}rpx ${2*m.itemStyle.paddingLeft}rpx`,background:m.itemStyle.background})},{default:l((()=>[s(f,{class:"line",style:i({textAlign:m.itemStyle.textAlign})},{default:l((()=>[s(g,{style:i({fontSize:2*m.itemStyle.fontSize+"rpx",color:m.itemStyle.textColor}),target:"_blank",onClick:o[0]||(o[0]=t=>e.onLink({type:"URL",param:{url:m.params.link}}))},{default:l((()=>[n(r(m.params.text),1)])),_:1},8,["style"])])),_:1},8,["style"])])),_:1},8,["style"])}]]),Title:B({props:{itemStyle:Object,params:Object},mixins:[V],methods:{}},[["render",function(e,o,m,p,y,g){const f=d,h=c;return t(),a(h,{class:"diy-title",style:i({padding:2*m.itemStyle.paddingY+"rpx 30rpx",background:m.itemStyle.background})},{default:l((()=>[$("div",{class:"title-content"},[$("div",{class:"title"},[s(f,{style:i({color:m.itemStyle.titleTextColor,fontSize:2*m.params.titleFontSize+"rpx",fontWeight:m.params.titleFontWeight})},{default:l((()=>[n(r(m.params.title),1)])),_:1},8,["style"])]),m.params.more.enable?(t(),u("div",{key:0,class:"more-content",style:i({color:m.itemStyle.moreTextColor}),onClick:o[0]||(o[0]=t=>e.onLink(m.params.more.link))},[s(f,{class:"more-text"},{default:l((()=>[n(r(m.params.more.text),1)])),_:1}),m.params.more.enableIcon?(t(),a(f,{key:0,class:"more-icon"},{default:l((()=>[s(f,{class:"iconfont icon-arrow-right"})])),_:1})):_("",!0)],4)):_("",!0)]),$("div",{class:"desc-content"},[s(f,{style:i({color:m.itemStyle.descTextColor,fontSize:2*m.params.descFontSize+"rpx",fontWeight:m.params.descFontWeight})},{default:l((()=>[n(r(m.params.desc),1)])),_:1},8,["style"])])])),_:1},8,["style"])}],["__scopeId","data-v-5967764b"]]),GoodsGroup:B({components:{AddCartPopup:O},props:{itemIndex:String,itemStyle:Object,params:Object,dataList:Array},data:()=>({inArray:b,tabIndex:0}),methods:{handleTabItem(e){this.tabIndex=e},handleGoodsItem(e){this.$navTo("pages/goods/detail",{goodsId:e})},handleAddCart(e){this.$refs.AddCartPopup.handle(e)},onAddCart(e){x()}}},[["render",function(e,m,f,h,S,k){const b=c,x=g,L=d,A=C("AddCartPopup");return t(),a(b,{class:"diy-goodsGroup",style:i({background:f.itemStyle.background,padding:`${f.itemStyle.paddingY}px ${f.itemStyle.paddingX}px`})},{default:l((()=>[s(b,{class:"tabs",style:i({"--tab-active-font-color":f.itemStyle.tabActiveFontColor,"--tab-active-bg-color":f.itemStyle.tabActiveBgColor,background:f.itemStyle.background})},{default:l((()=>[(t(!0),u(p,null,y(f.params.tabs,((e,d)=>(t(),a(b,{class:o(["tab-item",{active:d==S.tabIndex}]),key:d,onClick:e=>k.handleTabItem(d)},{default:l((()=>[s(b,{class:"tab-name",style:i({color:f.itemStyle.tabTextColor})},{default:l((()=>[n(r(e.name),1)])),_:2},1032,["style"]),s(b,{class:"sub-name"},{default:l((()=>[n(r(e.subName),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1},8,["style"]),s(b,{class:"goods-list"},{default:l((()=>[(t(!0),u(p,null,y(f.dataList[S.tabIndex],((e,d)=>(t(),a(b,{class:o(["goods-item",[`display-${f.itemStyle.cardType}`]]),key:d,style:i({marginBottom:`${f.itemStyle.itemMargin}px`,borderRadius:`${f.itemStyle.borderRadius}px`}),onClick:t=>k.handleGoodsItem(e.goods_id)},{default:l((()=>[s(b,{class:"goods-image"},{default:l((()=>[s(x,{class:"image",src:e.goods_image},null,8,["src"])])),_:2},1024),s(b,{class:"goods-info"},{default:l((()=>[S.inArray("goodsName",f.itemStyle.show)?(t(),a(b,{key:0,class:"goods-name twoline-hide"},{default:l((()=>[n(r(e.goods_name),1)])),_:2},1024)):_("",!0),S.inArray("sellingPoint",f.itemStyle.show)?(t(),a(b,{key:1,class:"goods-selling"},{default:l((()=>[s(L,{class:"selling oneline-hide",style:i({color:f.itemStyle.sellingColor})},{default:l((()=>[n(r(e.selling_point),1)])),_:2},1032,["style"])])),_:2},1024)):_("",!0),S.inArray("goodsSales",f.itemStyle.show)?(t(),a(b,{key:2,class:"goods-sales oneline-hide"},{default:l((()=>[s(L,{class:"sales"},{default:l((()=>[n("已售"+r(e.goods_sales),1)])),_:2},1024)])),_:2},1024)):_("",!0),s(b,{class:"footer"},{default:l((()=>[s(b,{class:"goods-price oneline-hide",style:i({color:f.itemStyle.priceColor})},{default:l((()=>[S.inArray("goodsPrice",f.itemStyle.show)?(t(),u(p,{key:0},[s(L,{class:"unit"},{default:l((()=>[n("￥")])),_:1}),s(L,{class:"value"},{default:l((()=>[n(r(e.goods_price_min),1)])),_:2},1024)],64)):_("",!0),S.inArray("linePrice",f.itemStyle.show)?(t(),a(L,{key:1,class:"line-price"},{default:l((()=>[s(L,{class:"unit"},{default:l((()=>[n("￥")])),_:1}),s(L,{class:"value"},{default:l((()=>[n(r(e.line_price_min),1)])),_:2},1024)])),_:2},1024)):_("",!0)])),_:2},1032,["style"]),v(s(b,{class:"action"},{default:l((()=>[s(b,{class:"btn-cart",style:i({background:f.itemStyle.btnCartColor,color:f.itemStyle.btnFontColor}),onClick:w((t=>k.handleAddCart(e)),["stop"])},{default:l((()=>[s(L,{class:"cart-icon iconfont icon-plus"})])),_:2},1032,["style","onClick"])])),_:2},1536),[[I,S.inArray("cartBtn",f.itemStyle.show)]])])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","style","onClick"])))),128))])),_:1}),s(A,{ref:"AddCartPopup",onAddCart:k.onAddCart},null,8,["onAddCart"])])),_:1},8,["style"])}],["__scopeId","data-v-34f127c2"]])},props:{items:{type:Array,default:()=>[]}}},[["render",function(e,s,o,i,n,r){const d=C("Search"),m=C("Images"),g=C("Banner"),f=C("Window"),h=C("Videos"),S=C("Article"),k=C("Notice"),b=C("NavBar"),x=C("Goods"),v=C("Service"),w=C("Blank"),I=C("Guide"),L=C("RichText"),A=C("Special"),$=C("DiyOfficialAccount"),T=C("Shop"),B=C("Coupon"),j=C("Bargain"),z=C("Sharp"),O=C("Groupon"),N=C("HotZone"),P=C("ICPLicense"),G=C("Title"),M=C("GoodsGroup"),R=c;return t(),a(R,{class:"page-items"},{default:l((()=>[(t(!0),u(p,null,y(o.items,((e,l)=>(t(),u(p,{key:l},["search"===e.type?(t(),a(d,{key:0,itemStyle:e.style,params:e.params},null,8,["itemStyle","params"])):_("",!0),"image"===e.type?(t(),a(m,{key:1,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"banner"===e.type?(t(),a(g,{key:2,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"window"===e.type?(t(),a(f,{key:3,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"video"===e.type?(t(),a(h,{key:4,itemStyle:e.style,params:e.params},null,8,["itemStyle","params"])):_("",!0),"article"===e.type?(t(),a(S,{key:5,params:e.params,dataList:e.data},null,8,["params","dataList"])):_("",!0),"notice"===e.type?(t(),a(k,{key:6,itemStyle:e.style,params:e.params},null,8,["itemStyle","params"])):_("",!0),"navBar"===e.type?(t(),a(b,{key:7,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"goods"===e.type?(t(),a(x,{key:8,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"service"===e.type?(t(),a(v,{key:9,itemStyle:e.style,params:e.params},null,8,["itemStyle","params"])):_("",!0),"blank"===e.type?(t(),a(w,{key:10,itemStyle:e.style},null,8,["itemStyle"])):_("",!0),"guide"===e.type?(t(),a(I,{key:11,itemStyle:e.style},null,8,["itemStyle"])):_("",!0),"richText"===e.type?(t(),a(L,{key:12,itemStyle:e.style,params:e.params},null,8,["itemStyle","params"])):_("",!0),"special"===e.type?(t(),a(A,{key:13,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"officialAccount"===e.type?(t(),a($,{key:14})):_("",!0),"shop"===e.type?(t(),a(T,{key:15,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"coupon"===e.type?(t(),a(B,{key:16,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"bargain"===e.type?(t(),a(j,{key:17,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"sharp"===e.type?(t(),a(z,{key:18,itemStyle:e.style,params:e.params,data:e.data},null,8,["itemStyle","params","data"])):_("",!0),"groupon"===e.type?(t(),a(O,{key:19,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0),"hotZone"===e.type?(t(),a(N,{key:20,itemStyle:e.style,params:e.params,data:e.data},null,8,["itemStyle","params","data"])):_("",!0),"ICPLicense"===e.type?(t(),a(P,{key:21,itemStyle:e.style,params:e.params},null,8,["itemStyle","params"])):_("",!0),"title"===e.type?(t(),a(G,{key:22,itemStyle:e.style,params:e.params},null,8,["itemStyle","params"])):_("",!0),"goodsGroup"===e.type?(t(),a(M,{key:23,itemStyle:e.style,params:e.params,dataList:e.data},null,8,["itemStyle","params","dataList"])):_("",!0)],64)))),128))])),_:1})}],["__scopeId","data-v-a73c91f4"]]);export{se as P,Y as d};
