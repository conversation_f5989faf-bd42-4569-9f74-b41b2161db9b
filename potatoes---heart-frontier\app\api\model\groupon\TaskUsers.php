<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\api\model\groupon;

use app\common\model\groupon\TaskUsers as TaskUsersModel;

/**
 * 拼团拼单成员模型
 * Class TaskUsers
 * @package app\api\model\groupon
 */
class TaskUsers extends TaskUsersModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'order_id',
        'robot_id',
        'store_id',
        'create_time',
    ];

    /**
     * 获取指定拼单的成员列表
     * @param int $taskId 拼单ID
     * @return TaskUsers[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getUserList(int $taskId)
    {
        $list = $this->where('task_id', '=', $taskId)->select();
        if ($list->isEmpty()) {
            return $list;
        }
        $list = static::preload($list, ['user.avatar', 'robot.avatar'], true);
        $data = [];
        foreach ($list as $item) {
            $data[] = [
                'id' => $item['id'],
                'task_id' => $item['task_id'],
                'is_leader' => $item['is_leader'],
                'userInfo' => $item['is_robot']
                    ? ['nick_name' => $item['robot']['nick_name'], 'avatar_url' => $item['robot']['avatar']['preview_url']]
                    : ['nick_name' => $item['user']['nick_name'], 'avatar_url' => $item['user']['avatar_url']]
            ];
        }
        return $data;
    }
}