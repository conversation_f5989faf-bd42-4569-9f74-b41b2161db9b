<?php
// +----------------------------------------------------------------------
// | 萤火商城系统 [ 致力于通过产品和服务，帮助商家高效化开拓市场 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2017~2024 https://www.yiovo.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed 这不是一个自由软件，不允许对程序代码以任何形式任何目的的再发行
// +----------------------------------------------------------------------
// | Author: 萤火科技 <<EMAIL>>
// +----------------------------------------------------------------------
declare (strict_types=1);

namespace app\admin\controller;

use think\response\Json;
use app\admin\model\Store as StoreModel;
use app\admin\service\store\User as StoreUserService;

/**
 * 商城管理
 * Class Store
 * @package app\admin\controller
 */
class Store extends Controller
{
    /**
     * 强制验证当前访问的控制器方法method
     * @var array
     */
    protected array $methodRules = [
        'index' => 'GET',
        'recycle' => 'GET',
        'add' => 'POST',
        'move' => 'POST',
        'delete' => 'POST',
    ];

    /**
     * 商城列表
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function index(): Json
    {
        // 商城列表
        $model = new StoreModel;
        $list = $model->getList();
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 获取商城登录token
     * @param int $storeId
     * @return Json
     */
    public function superLogin(int $storeId): Json
    {
        // 获取指定商城的管理员用户信息
        $userInfo = StoreUserService::getUserInfoByStoreId($storeId);
        if (empty($userInfo)) {
            return $this->renderError('未找到该商城管理员用户');
        }
        // 登录商户后台
        $token = StoreUserService::login($userInfo->toArray());
        return $this->renderSuccess([
            'userId' => $userInfo['store_user_id'],
            'token' => $token
        ]);
    }

    /**
     * 回收站列表
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function recycle(): Json
    {
        // 商城列表
        $model = new StoreModel;
        $list = $model->getList(true);
        return $this->renderSuccess(compact('list'));
    }

    /**
     * 新增商城
     * @return Json
     */
    public function add(): Json
    {
        // 新增记录
        $model = new StoreModel;
        if ($model->add($this->postForm())) {
            return $this->renderSuccess('添加成功');
        }
        return $this->renderError($model->getError() ?: '添加失败');
    }

    /**
     * 移入回收站
     * @param int $storeId
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function recovery(int $storeId): Json
    {
        // 商城详情
        $model = StoreModel::detail($storeId);
        if (!$model->recycle()) {
            return $this->renderError($model->getError() ?: '操作失败');
        }
        return $this->renderSuccess('操作成功');
    }

    /**
     * 移出回收站
     * @param int $storeId
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function move(int $storeId): Json
    {
        // 商城详情
        $model = StoreModel::detail($storeId);
        if (!$model->recycle(false)) {
            return $this->renderError($model->getError() ?: '操作失败');
        }
        return $this->renderSuccess('操作成功');
    }

    /**
     * 删除商城
     * @param int $storeId
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function delete(int $storeId): Json
    {
        // 商城详情
        $model = StoreModel::detail($storeId);
        if (!$model->setDelete()) {
            return $this->renderError($model->getError() ?: '操作失败');
        }
        return $this->renderSuccess('操作成功');
    }
}
