import{v as e,r as a,c as s,w as t,n as d,i as l,o,a as n,d as i,e as r,F as c,b as u,f,t as m,l as h}from"./index-DAm19nhc.js";import{_ as p,a as _}from"./u-radio-group.CdrHq9WN.js";import{r as g}from"./uni-app.es.CS65jdCG.js";import{l as v,d as I,r as k,s as y}from"./address.DhwSxn3R.js";import{E as C}from"./index.CqmmmBEk.js";import{_ as L}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./u-icon.BJNYmTLf.js";import"./emitter.DrjJCwnj.js";const j=L({components:{Empty:C},data:()=>({options:{},isLoading:!0,list:[],defaultId:null}),onLoad(e){this.options=e},onShow(){this.getPageData()},methods:{getPageData(){const e=this;e.isLoading=!0,Promise.all([e.getDefaultId(),e.getAddressList()]).then((()=>{e.onReorder()})).finally((()=>e.isLoading=!1))},getAddressList(){const e=this;return new Promise(((a,s)=>{v().then((s=>{e.list=s.data.list,a(s)})).catch(s)}))},getDefaultId(){return new Promise(((e,a)=>{const s=this;I().then((a=>{s.defaultId=a.data.defaultId,e(a)})).catch(a)}))},onReorder(){const e=this;e.list.sort((a=>a.address_id==e.defaultId?-1:1))},handleCreate(){this.$navTo("pages/address/create")},handleUpdate(e){this.$navTo("pages/address/update",{addressId:e})},handleRemove(e){const a=this;uni.showModal({title:"提示",content:"您确定要删除当前收货地址吗?",success({confirm:s}){s&&a.onRemove(e)}})},onRemove(e){const a=this;k(e).then((e=>{a.getPageData()}))},handleSetDefault(e){const a=this;y(e).then((e=>{"checkout"===a.options.from&&uni.navigateBack()}))}}},[["render",function(v,I,k,y,C,L){const j=h,w=l,D=g(e("u-radio"),p),x=g(e("u-radio-group"),_),P=a("empty");return o(),s(w,{class:"container",style:d(v.appThemeStyle)},{default:t((()=>[n(w,{class:"addres-list"},{default:t((()=>[(o(!0),i(c,null,r(C.list,((e,a)=>(o(),s(w,{class:"address-item",key:a},{default:t((()=>[n(w,{class:"contacts"},{default:t((()=>[n(j,{class:"name"},{default:t((()=>[f(m(e.name),1)])),_:2},1024),n(j,{class:"phone"},{default:t((()=>[f(m(e.phone),1)])),_:2},1024)])),_:2},1024),n(w,{class:"address"},{default:t((()=>[(o(!0),i(c,null,r(e.region,((e,a)=>(o(),s(j,{class:"region",key:a},{default:t((()=>[f(m(e),1)])),_:2},1024)))),128)),n(j,{class:"detail"},{default:t((()=>[f(m(e.detail),1)])),_:2},1024)])),_:2},1024),n(w,{class:"line"}),n(w,{class:"item-option"},{default:t((()=>[n(w,{class:"_left"},{default:t((()=>[n(w,{class:"item-radio"},{default:t((()=>[n(x,{modelValue:C.defaultId,"onUpdate:modelValue":I[0]||(I[0]=e=>C.defaultId=e),onChange:a=>L.handleSetDefault(e.address_id)},{default:t((()=>[n(D,{name:e.address_id,"active-color":v.appTheme.mainBg},{default:t((()=>[f(m(e.address_id==C.defaultId?"默认":"选择"),1)])),_:2},1032,["name","active-color"])])),_:2},1032,["modelValue","onChange"])])),_:2},1024)])),_:2},1024),n(w,{class:"_right"},{default:t((()=>[n(w,{class:"events"},{default:t((()=>[n(w,{class:"event-item",onClick:a=>L.handleUpdate(e.address_id)},{default:t((()=>[n(j,{class:"iconfont icon-edit"}),n(j,{class:"title"},{default:t((()=>[f("编辑")])),_:1})])),_:2},1032,["onClick"]),n(w,{class:"event-item",onClick:a=>L.handleRemove(e.address_id)},{default:t((()=>[n(j,{class:"iconfont icon-delete"}),n(j,{class:"title"},{default:t((()=>[f("删除")])),_:1})])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1}),C.list.length?u("",!0):(o(),s(P,{key:0,isLoading:C.isLoading,tips:"亲，暂无收货地址"},null,8,["isLoading"])),n(w,{class:"footer-fixed"},{default:t((()=>[n(w,{class:"btn-wrapper"},{default:t((()=>[n(w,{class:"btn-item btn-item-main",onClick:I[1]||(I[1]=e=>L.handleCreate())},{default:t((()=>[f("添加新地址")])),_:1})])),_:1})])),_:1})])),_:1},8,["style"])}],["__scopeId","data-v-eca7affb"]]);export{j as default};
